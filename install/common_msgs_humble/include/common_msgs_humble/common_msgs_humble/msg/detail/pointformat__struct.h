// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Pointformat.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__POINTFORMAT__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__POINTFORMAT__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'path'
#include "rosidl_runtime_c/string.h"

/// Struct defined in msg/Pointformat in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Pointformat
{
  double lon;
  double lat;
  float heading;
  uint8_t index;
  uint8_t backup1;
  uint8_t backup2;
  uint8_t backup3;
  float backup4;
  float backup5;
  float backup6;
  double backup7;
  double backup8;
  double backup9;
  rosidl_runtime_c__String path;
} common_msgs_humble__msg__Pointformat;

// Struct for a sequence of common_msgs_humble__msg__Pointformat.
typedef struct common_msgs_humble__msg__Pointformat__Sequence
{
  common_msgs_humble__msg__Pointformat * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Pointformat__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__POINTFORMAT__STRUCT_H_
