// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from common_msgs_humble:msg/Sensorobject.idl
// generated code does not contain a copyright notice
#include "common_msgs_humble/msg/detail/sensorobject__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


// Include directives for member types
// Member `points`
#include "common_msgs_humble/msg/detail/point3d__functions.h"
// Member `object_history`
#include "common_msgs_humble/msg/detail/objecthistory__functions.h"
// Member `object_prediction`
#include "common_msgs_humble/msg/detail/objectprediction__functions.h"

bool
common_msgs_humble__msg__Sensorobject__init(common_msgs_humble__msg__Sensorobject * msg)
{
  if (!msg) {
    return false;
  }
  // id
  // x
  // y
  // z
  // longtitude
  // latitude
  // altitude
  // relspeedy
  // relspeedx
  // rollrad
  // pitchrad
  // azimuth
  // pitchrate
  // rollrate
  // yawrate
  // width
  // length
  // height
  // classification
  // value
  // confidence
  // points
  if (!common_msgs_humble__msg__Point3d__Sequence__init(&msg->points, 0)) {
    common_msgs_humble__msg__Sensorobject__fini(msg);
    return false;
  }
  // driving_intent
  // behavior_state
  // radarindex
  // radarobjectid
  // s
  // l
  // speeds
  // speedl
  // object_decision
  // object_history
  if (!common_msgs_humble__msg__Objecthistory__Sequence__init(&msg->object_history, 0)) {
    common_msgs_humble__msg__Sensorobject__fini(msg);
    return false;
  }
  // object_prediction
  if (!common_msgs_humble__msg__Objectprediction__Sequence__init(&msg->object_prediction, 0)) {
    common_msgs_humble__msg__Sensorobject__fini(msg);
    return false;
  }
  return true;
}

void
common_msgs_humble__msg__Sensorobject__fini(common_msgs_humble__msg__Sensorobject * msg)
{
  if (!msg) {
    return;
  }
  // id
  // x
  // y
  // z
  // longtitude
  // latitude
  // altitude
  // relspeedy
  // relspeedx
  // rollrad
  // pitchrad
  // azimuth
  // pitchrate
  // rollrate
  // yawrate
  // width
  // length
  // height
  // classification
  // value
  // confidence
  // points
  common_msgs_humble__msg__Point3d__Sequence__fini(&msg->points);
  // driving_intent
  // behavior_state
  // radarindex
  // radarobjectid
  // s
  // l
  // speeds
  // speedl
  // object_decision
  // object_history
  common_msgs_humble__msg__Objecthistory__Sequence__fini(&msg->object_history);
  // object_prediction
  common_msgs_humble__msg__Objectprediction__Sequence__fini(&msg->object_prediction);
}

bool
common_msgs_humble__msg__Sensorobject__are_equal(const common_msgs_humble__msg__Sensorobject * lhs, const common_msgs_humble__msg__Sensorobject * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // id
  if (lhs->id != rhs->id) {
    return false;
  }
  // x
  if (lhs->x != rhs->x) {
    return false;
  }
  // y
  if (lhs->y != rhs->y) {
    return false;
  }
  // z
  if (lhs->z != rhs->z) {
    return false;
  }
  // longtitude
  if (lhs->longtitude != rhs->longtitude) {
    return false;
  }
  // latitude
  if (lhs->latitude != rhs->latitude) {
    return false;
  }
  // altitude
  if (lhs->altitude != rhs->altitude) {
    return false;
  }
  // relspeedy
  if (lhs->relspeedy != rhs->relspeedy) {
    return false;
  }
  // relspeedx
  if (lhs->relspeedx != rhs->relspeedx) {
    return false;
  }
  // rollrad
  if (lhs->rollrad != rhs->rollrad) {
    return false;
  }
  // pitchrad
  if (lhs->pitchrad != rhs->pitchrad) {
    return false;
  }
  // azimuth
  if (lhs->azimuth != rhs->azimuth) {
    return false;
  }
  // pitchrate
  if (lhs->pitchrate != rhs->pitchrate) {
    return false;
  }
  // rollrate
  if (lhs->rollrate != rhs->rollrate) {
    return false;
  }
  // yawrate
  if (lhs->yawrate != rhs->yawrate) {
    return false;
  }
  // width
  if (lhs->width != rhs->width) {
    return false;
  }
  // length
  if (lhs->length != rhs->length) {
    return false;
  }
  // height
  if (lhs->height != rhs->height) {
    return false;
  }
  // classification
  if (lhs->classification != rhs->classification) {
    return false;
  }
  // value
  if (lhs->value != rhs->value) {
    return false;
  }
  // confidence
  if (lhs->confidence != rhs->confidence) {
    return false;
  }
  // points
  if (!common_msgs_humble__msg__Point3d__Sequence__are_equal(
      &(lhs->points), &(rhs->points)))
  {
    return false;
  }
  // driving_intent
  if (lhs->driving_intent != rhs->driving_intent) {
    return false;
  }
  // behavior_state
  if (lhs->behavior_state != rhs->behavior_state) {
    return false;
  }
  // radarindex
  if (lhs->radarindex != rhs->radarindex) {
    return false;
  }
  // radarobjectid
  if (lhs->radarobjectid != rhs->radarobjectid) {
    return false;
  }
  // s
  if (lhs->s != rhs->s) {
    return false;
  }
  // l
  if (lhs->l != rhs->l) {
    return false;
  }
  // speeds
  if (lhs->speeds != rhs->speeds) {
    return false;
  }
  // speedl
  if (lhs->speedl != rhs->speedl) {
    return false;
  }
  // object_decision
  if (lhs->object_decision != rhs->object_decision) {
    return false;
  }
  // object_history
  if (!common_msgs_humble__msg__Objecthistory__Sequence__are_equal(
      &(lhs->object_history), &(rhs->object_history)))
  {
    return false;
  }
  // object_prediction
  if (!common_msgs_humble__msg__Objectprediction__Sequence__are_equal(
      &(lhs->object_prediction), &(rhs->object_prediction)))
  {
    return false;
  }
  return true;
}

bool
common_msgs_humble__msg__Sensorobject__copy(
  const common_msgs_humble__msg__Sensorobject * input,
  common_msgs_humble__msg__Sensorobject * output)
{
  if (!input || !output) {
    return false;
  }
  // id
  output->id = input->id;
  // x
  output->x = input->x;
  // y
  output->y = input->y;
  // z
  output->z = input->z;
  // longtitude
  output->longtitude = input->longtitude;
  // latitude
  output->latitude = input->latitude;
  // altitude
  output->altitude = input->altitude;
  // relspeedy
  output->relspeedy = input->relspeedy;
  // relspeedx
  output->relspeedx = input->relspeedx;
  // rollrad
  output->rollrad = input->rollrad;
  // pitchrad
  output->pitchrad = input->pitchrad;
  // azimuth
  output->azimuth = input->azimuth;
  // pitchrate
  output->pitchrate = input->pitchrate;
  // rollrate
  output->rollrate = input->rollrate;
  // yawrate
  output->yawrate = input->yawrate;
  // width
  output->width = input->width;
  // length
  output->length = input->length;
  // height
  output->height = input->height;
  // classification
  output->classification = input->classification;
  // value
  output->value = input->value;
  // confidence
  output->confidence = input->confidence;
  // points
  if (!common_msgs_humble__msg__Point3d__Sequence__copy(
      &(input->points), &(output->points)))
  {
    return false;
  }
  // driving_intent
  output->driving_intent = input->driving_intent;
  // behavior_state
  output->behavior_state = input->behavior_state;
  // radarindex
  output->radarindex = input->radarindex;
  // radarobjectid
  output->radarobjectid = input->radarobjectid;
  // s
  output->s = input->s;
  // l
  output->l = input->l;
  // speeds
  output->speeds = input->speeds;
  // speedl
  output->speedl = input->speedl;
  // object_decision
  output->object_decision = input->object_decision;
  // object_history
  if (!common_msgs_humble__msg__Objecthistory__Sequence__copy(
      &(input->object_history), &(output->object_history)))
  {
    return false;
  }
  // object_prediction
  if (!common_msgs_humble__msg__Objectprediction__Sequence__copy(
      &(input->object_prediction), &(output->object_prediction)))
  {
    return false;
  }
  return true;
}

common_msgs_humble__msg__Sensorobject *
common_msgs_humble__msg__Sensorobject__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Sensorobject * msg = (common_msgs_humble__msg__Sensorobject *)allocator.allocate(sizeof(common_msgs_humble__msg__Sensorobject), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(common_msgs_humble__msg__Sensorobject));
  bool success = common_msgs_humble__msg__Sensorobject__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
common_msgs_humble__msg__Sensorobject__destroy(common_msgs_humble__msg__Sensorobject * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    common_msgs_humble__msg__Sensorobject__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
common_msgs_humble__msg__Sensorobject__Sequence__init(common_msgs_humble__msg__Sensorobject__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Sensorobject * data = NULL;

  if (size) {
    data = (common_msgs_humble__msg__Sensorobject *)allocator.zero_allocate(size, sizeof(common_msgs_humble__msg__Sensorobject), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = common_msgs_humble__msg__Sensorobject__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        common_msgs_humble__msg__Sensorobject__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
common_msgs_humble__msg__Sensorobject__Sequence__fini(common_msgs_humble__msg__Sensorobject__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      common_msgs_humble__msg__Sensorobject__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

common_msgs_humble__msg__Sensorobject__Sequence *
common_msgs_humble__msg__Sensorobject__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Sensorobject__Sequence * array = (common_msgs_humble__msg__Sensorobject__Sequence *)allocator.allocate(sizeof(common_msgs_humble__msg__Sensorobject__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = common_msgs_humble__msg__Sensorobject__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
common_msgs_humble__msg__Sensorobject__Sequence__destroy(common_msgs_humble__msg__Sensorobject__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    common_msgs_humble__msg__Sensorobject__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
common_msgs_humble__msg__Sensorobject__Sequence__are_equal(const common_msgs_humble__msg__Sensorobject__Sequence * lhs, const common_msgs_humble__msg__Sensorobject__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!common_msgs_humble__msg__Sensorobject__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
common_msgs_humble__msg__Sensorobject__Sequence__copy(
  const common_msgs_humble__msg__Sensorobject__Sequence * input,
  common_msgs_humble__msg__Sensorobject__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(common_msgs_humble__msg__Sensorobject);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    common_msgs_humble__msg__Sensorobject * data =
      (common_msgs_humble__msg__Sensorobject *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!common_msgs_humble__msg__Sensorobject__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          common_msgs_humble__msg__Sensorobject__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!common_msgs_humble__msg__Sensorobject__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
