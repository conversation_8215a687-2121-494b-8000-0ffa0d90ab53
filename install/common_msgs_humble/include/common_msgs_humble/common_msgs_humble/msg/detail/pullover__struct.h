﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Pullover.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__PULLOVER__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__PULLOVER__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Struct defined in msg/Pullover in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Pullover
{
  /// 当前时间戳 ms 级
  int64_t timestamp;
  /// 0:暂未入站    1:入站成功   2：接收成功（弹出按钮）
  uint8_t po_decision;
  /// 0:暂不启动    1:可以出站（按钮消失，车辆起步）
  uint8_t po_button;
  /// 车门状态   0：关门    1：开门
  uint8_t doorstatus;
  /// 预留
  int32_t reserve;
} common_msgs_humble__msg__Pullover;

// Struct for a sequence of common_msgs_humble__msg__Pullover.
typedef struct common_msgs_humble__msg__Pullover__Sequence
{
  common_msgs_humble__msg__Pullover * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Pullover__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__PULLOVER__STRUCT_H_
