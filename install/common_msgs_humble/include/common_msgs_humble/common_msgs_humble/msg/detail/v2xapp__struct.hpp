// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/V2xapp.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__V2XAPP__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__V2XAPP__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__V2xapp __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__V2xapp __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct V2xapp_
{
  using Type = V2xapp_<ContainerAllocator>;

  explicit V2xapp_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->v2xtype = 0l;
      this->speedlimit = 0l;
      this->color = 0l;
      this->isvalid = 0;
      this->timestamp = 0ll;
    }
  }

  explicit V2xapp_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->v2xtype = 0l;
      this->speedlimit = 0l;
      this->color = 0l;
      this->isvalid = 0;
      this->timestamp = 0ll;
    }
  }

  // field types and members
  using _v2xtype_type =
    int32_t;
  _v2xtype_type v2xtype;
  using _speedlimit_type =
    int32_t;
  _speedlimit_type speedlimit;
  using _color_type =
    int32_t;
  _color_type color;
  using _isvalid_type =
    uint8_t;
  _isvalid_type isvalid;
  using _timestamp_type =
    int64_t;
  _timestamp_type timestamp;

  // setters for named parameter idiom
  Type & set__v2xtype(
    const int32_t & _arg)
  {
    this->v2xtype = _arg;
    return *this;
  }
  Type & set__speedlimit(
    const int32_t & _arg)
  {
    this->speedlimit = _arg;
    return *this;
  }
  Type & set__color(
    const int32_t & _arg)
  {
    this->color = _arg;
    return *this;
  }
  Type & set__isvalid(
    const uint8_t & _arg)
  {
    this->isvalid = _arg;
    return *this;
  }
  Type & set__timestamp(
    const int64_t & _arg)
  {
    this->timestamp = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::V2xapp_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::V2xapp_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::V2xapp_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::V2xapp_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::V2xapp_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::V2xapp_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::V2xapp_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::V2xapp_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::V2xapp_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::V2xapp_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__V2xapp
    std::shared_ptr<common_msgs_humble::msg::V2xapp_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__V2xapp
    std::shared_ptr<common_msgs_humble::msg::V2xapp_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const V2xapp_ & other) const
  {
    if (this->v2xtype != other.v2xtype) {
      return false;
    }
    if (this->speedlimit != other.speedlimit) {
      return false;
    }
    if (this->color != other.color) {
      return false;
    }
    if (this->isvalid != other.isvalid) {
      return false;
    }
    if (this->timestamp != other.timestamp) {
      return false;
    }
    return true;
  }
  bool operator!=(const V2xapp_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct V2xapp_

// alias to use template instance with default allocator
using V2xapp =
  common_msgs_humble::msg::V2xapp_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__V2XAPP__STRUCT_HPP_
