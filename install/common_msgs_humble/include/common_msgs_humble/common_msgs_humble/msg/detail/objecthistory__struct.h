﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Objecthistory.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBJECTHISTORY__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBJECTHISTORY__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'trajectorypoint'
#include "common_msgs_humble/msg/detail/point3d__struct.h"

/// Struct defined in msg/Objecthistory in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Objecthistory
{
  /// 时间戳-毫秒 发布跟踪预测轨迹信息 20220913
  int64_t timestamp;
  /// 轨迹点坐标XYZ 20220913
  common_msgs_humble__msg__Point3d trajectorypoint;
  /// 纬度    #20220914
  double lon;
  /// 经度
  double lat;
  /// 高度
  double alt;
  /// N 20220920
  float roll;
  /// E
  float pitch;
  /// D
  float heading;
  float relavx;
  float relavy;
  /// m/s
  float absvx;
  /// m/s
  float absvy;
  /// m
  float s;
  /// m
  float l;
  /// m/s
  float speeds;
  /// m/s
  float speedl;
} common_msgs_humble__msg__Objecthistory;

// Struct for a sequence of common_msgs_humble__msg__Objecthistory.
typedef struct common_msgs_humble__msg__Objecthistory__Sequence
{
  common_msgs_humble__msg__Objecthistory * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Objecthistory__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBJECTHISTORY__STRUCT_H_
