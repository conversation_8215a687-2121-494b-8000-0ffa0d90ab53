// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Sensorgps.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORGPS__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORGPS__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Sensorgps __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Sensorgps __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Sensorgps_
{
  using Type = Sensorgps_<ContainerAllocator>;

  explicit Sensorgps_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->lon = 0.0;
      this->lat = 0.0;
      this->alt = 0.0;
      this->roadtype = 0;
      this->lanetype = 0;
      this->heading = 0.0;
      this->pitch = 0.0;
      this->roll = 0.0;
      this->pitchrate = 0.0;
      this->rollrate = 0.0;
      this->yawrate = 0.0;
      this->accx = 0.0;
      this->accy = 0.0;
      this->accz = 0.0;
      this->mile = 0.0;
      this->velocity = 0.0;
      this->status = 0;
      this->rawstatus = 0;
      this->satenum = 0;
      this->gpstime = 0ll;
      this->isvalid = 0;
      this->timestamp = 0ll;
      this->speed_n = 0.0;
      this->speed_e = 0.0;
      this->speed_d = 0.0;
    }
  }

  explicit Sensorgps_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->lon = 0.0;
      this->lat = 0.0;
      this->alt = 0.0;
      this->roadtype = 0;
      this->lanetype = 0;
      this->heading = 0.0;
      this->pitch = 0.0;
      this->roll = 0.0;
      this->pitchrate = 0.0;
      this->rollrate = 0.0;
      this->yawrate = 0.0;
      this->accx = 0.0;
      this->accy = 0.0;
      this->accz = 0.0;
      this->mile = 0.0;
      this->velocity = 0.0;
      this->status = 0;
      this->rawstatus = 0;
      this->satenum = 0;
      this->gpstime = 0ll;
      this->isvalid = 0;
      this->timestamp = 0ll;
      this->speed_n = 0.0;
      this->speed_e = 0.0;
      this->speed_d = 0.0;
    }
  }

  // field types and members
  using _lon_type =
    double;
  _lon_type lon;
  using _lat_type =
    double;
  _lat_type lat;
  using _alt_type =
    double;
  _alt_type alt;
  using _roadtype_type =
    uint8_t;
  _roadtype_type roadtype;
  using _lanetype_type =
    uint8_t;
  _lanetype_type lanetype;
  using _heading_type =
    double;
  _heading_type heading;
  using _pitch_type =
    double;
  _pitch_type pitch;
  using _roll_type =
    double;
  _roll_type roll;
  using _pitchrate_type =
    double;
  _pitchrate_type pitchrate;
  using _rollrate_type =
    double;
  _rollrate_type rollrate;
  using _yawrate_type =
    double;
  _yawrate_type yawrate;
  using _accx_type =
    double;
  _accx_type accx;
  using _accy_type =
    double;
  _accy_type accy;
  using _accz_type =
    double;
  _accz_type accz;
  using _mile_type =
    double;
  _mile_type mile;
  using _velocity_type =
    double;
  _velocity_type velocity;
  using _status_type =
    uint8_t;
  _status_type status;
  using _rawstatus_type =
    uint8_t;
  _rawstatus_type rawstatus;
  using _satenum_type =
    uint8_t;
  _satenum_type satenum;
  using _gpstime_type =
    int64_t;
  _gpstime_type gpstime;
  using _isvalid_type =
    uint8_t;
  _isvalid_type isvalid;
  using _timestamp_type =
    int64_t;
  _timestamp_type timestamp;
  using _speed_n_type =
    double;
  _speed_n_type speed_n;
  using _speed_e_type =
    double;
  _speed_e_type speed_e;
  using _speed_d_type =
    double;
  _speed_d_type speed_d;

  // setters for named parameter idiom
  Type & set__lon(
    const double & _arg)
  {
    this->lon = _arg;
    return *this;
  }
  Type & set__lat(
    const double & _arg)
  {
    this->lat = _arg;
    return *this;
  }
  Type & set__alt(
    const double & _arg)
  {
    this->alt = _arg;
    return *this;
  }
  Type & set__roadtype(
    const uint8_t & _arg)
  {
    this->roadtype = _arg;
    return *this;
  }
  Type & set__lanetype(
    const uint8_t & _arg)
  {
    this->lanetype = _arg;
    return *this;
  }
  Type & set__heading(
    const double & _arg)
  {
    this->heading = _arg;
    return *this;
  }
  Type & set__pitch(
    const double & _arg)
  {
    this->pitch = _arg;
    return *this;
  }
  Type & set__roll(
    const double & _arg)
  {
    this->roll = _arg;
    return *this;
  }
  Type & set__pitchrate(
    const double & _arg)
  {
    this->pitchrate = _arg;
    return *this;
  }
  Type & set__rollrate(
    const double & _arg)
  {
    this->rollrate = _arg;
    return *this;
  }
  Type & set__yawrate(
    const double & _arg)
  {
    this->yawrate = _arg;
    return *this;
  }
  Type & set__accx(
    const double & _arg)
  {
    this->accx = _arg;
    return *this;
  }
  Type & set__accy(
    const double & _arg)
  {
    this->accy = _arg;
    return *this;
  }
  Type & set__accz(
    const double & _arg)
  {
    this->accz = _arg;
    return *this;
  }
  Type & set__mile(
    const double & _arg)
  {
    this->mile = _arg;
    return *this;
  }
  Type & set__velocity(
    const double & _arg)
  {
    this->velocity = _arg;
    return *this;
  }
  Type & set__status(
    const uint8_t & _arg)
  {
    this->status = _arg;
    return *this;
  }
  Type & set__rawstatus(
    const uint8_t & _arg)
  {
    this->rawstatus = _arg;
    return *this;
  }
  Type & set__satenum(
    const uint8_t & _arg)
  {
    this->satenum = _arg;
    return *this;
  }
  Type & set__gpstime(
    const int64_t & _arg)
  {
    this->gpstime = _arg;
    return *this;
  }
  Type & set__isvalid(
    const uint8_t & _arg)
  {
    this->isvalid = _arg;
    return *this;
  }
  Type & set__timestamp(
    const int64_t & _arg)
  {
    this->timestamp = _arg;
    return *this;
  }
  Type & set__speed_n(
    const double & _arg)
  {
    this->speed_n = _arg;
    return *this;
  }
  Type & set__speed_e(
    const double & _arg)
  {
    this->speed_e = _arg;
    return *this;
  }
  Type & set__speed_d(
    const double & _arg)
  {
    this->speed_d = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Sensorgps_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Sensorgps_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Sensorgps_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Sensorgps_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Sensorgps_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Sensorgps_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Sensorgps_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Sensorgps_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Sensorgps_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Sensorgps_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Sensorgps
    std::shared_ptr<common_msgs_humble::msg::Sensorgps_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Sensorgps
    std::shared_ptr<common_msgs_humble::msg::Sensorgps_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Sensorgps_ & other) const
  {
    if (this->lon != other.lon) {
      return false;
    }
    if (this->lat != other.lat) {
      return false;
    }
    if (this->alt != other.alt) {
      return false;
    }
    if (this->roadtype != other.roadtype) {
      return false;
    }
    if (this->lanetype != other.lanetype) {
      return false;
    }
    if (this->heading != other.heading) {
      return false;
    }
    if (this->pitch != other.pitch) {
      return false;
    }
    if (this->roll != other.roll) {
      return false;
    }
    if (this->pitchrate != other.pitchrate) {
      return false;
    }
    if (this->rollrate != other.rollrate) {
      return false;
    }
    if (this->yawrate != other.yawrate) {
      return false;
    }
    if (this->accx != other.accx) {
      return false;
    }
    if (this->accy != other.accy) {
      return false;
    }
    if (this->accz != other.accz) {
      return false;
    }
    if (this->mile != other.mile) {
      return false;
    }
    if (this->velocity != other.velocity) {
      return false;
    }
    if (this->status != other.status) {
      return false;
    }
    if (this->rawstatus != other.rawstatus) {
      return false;
    }
    if (this->satenum != other.satenum) {
      return false;
    }
    if (this->gpstime != other.gpstime) {
      return false;
    }
    if (this->isvalid != other.isvalid) {
      return false;
    }
    if (this->timestamp != other.timestamp) {
      return false;
    }
    if (this->speed_n != other.speed_n) {
      return false;
    }
    if (this->speed_e != other.speed_e) {
      return false;
    }
    if (this->speed_d != other.speed_d) {
      return false;
    }
    return true;
  }
  bool operator!=(const Sensorgps_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Sensorgps_

// alias to use template instance with default allocator
using Sensorgps =
  common_msgs_humble::msg::Sensorgps_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORGPS__STRUCT_HPP_
