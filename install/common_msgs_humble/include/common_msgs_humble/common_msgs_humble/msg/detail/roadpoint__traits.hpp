// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/Roadpoint.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__ROADPOINT__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__ROADPOINT__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/roadpoint__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const Roadpoint & msg,
  std::ostream & out)
{
  out << "{";
  // member: x
  {
    out << "x: ";
    rosidl_generator_traits::value_to_yaml(msg.x, out);
    out << ", ";
  }

  // member: y
  {
    out << "y: ";
    rosidl_generator_traits::value_to_yaml(msg.y, out);
    out << ", ";
  }

  // member: gx
  {
    out << "gx: ";
    rosidl_generator_traits::value_to_yaml(msg.gx, out);
    out << ", ";
  }

  // member: gy
  {
    out << "gy: ";
    rosidl_generator_traits::value_to_yaml(msg.gy, out);
    out << ", ";
  }

  // member: roadtype
  {
    out << "roadtype: ";
    rosidl_generator_traits::value_to_yaml(msg.roadtype, out);
    out << ", ";
  }

  // member: speed
  {
    out << "speed: ";
    rosidl_generator_traits::value_to_yaml(msg.speed, out);
    out << ", ";
  }

  // member: a
  {
    out << "a: ";
    rosidl_generator_traits::value_to_yaml(msg.a, out);
    out << ", ";
  }

  // member: jerk
  {
    out << "jerk: ";
    rosidl_generator_traits::value_to_yaml(msg.jerk, out);
    out << ", ";
  }

  // member: lanetype
  {
    out << "lanetype: ";
    rosidl_generator_traits::value_to_yaml(msg.lanetype, out);
    out << ", ";
  }

  // member: turnlight
  {
    out << "turnlight: ";
    rosidl_generator_traits::value_to_yaml(msg.turnlight, out);
    out << ", ";
  }

  // member: mergelanetype
  {
    out << "mergelanetype: ";
    rosidl_generator_traits::value_to_yaml(msg.mergelanetype, out);
    out << ", ";
  }

  // member: sensorlanetype
  {
    out << "sensorlanetype: ";
    rosidl_generator_traits::value_to_yaml(msg.sensorlanetype, out);
    out << ", ";
  }

  // member: heading
  {
    out << "heading: ";
    rosidl_generator_traits::value_to_yaml(msg.heading, out);
    out << ", ";
  }

  // member: curvature
  {
    out << "curvature: ";
    rosidl_generator_traits::value_to_yaml(msg.curvature, out);
    out << ", ";
  }

  // member: dkappa
  {
    out << "dkappa: ";
    rosidl_generator_traits::value_to_yaml(msg.dkappa, out);
    out << ", ";
  }

  // member: ddkappa
  {
    out << "ddkappa: ";
    rosidl_generator_traits::value_to_yaml(msg.ddkappa, out);
    out << ", ";
  }

  // member: leftsearchdis
  {
    out << "leftsearchdis: ";
    rosidl_generator_traits::value_to_yaml(msg.leftsearchdis, out);
    out << ", ";
  }

  // member: rightsearchdis
  {
    out << "rightsearchdis: ";
    rosidl_generator_traits::value_to_yaml(msg.rightsearchdis, out);
    out << ", ";
  }

  // member: s
  {
    out << "s: ";
    rosidl_generator_traits::value_to_yaml(msg.s, out);
    out << ", ";
  }

  // member: sideroadwidth
  {
    out << "sideroadwidth: ";
    rosidl_generator_traits::value_to_yaml(msg.sideroadwidth, out);
    out << ", ";
  }

  // member: lanewidth
  {
    out << "lanewidth: ";
    rosidl_generator_traits::value_to_yaml(msg.lanewidth, out);
    out << ", ";
  }

  // member: leftlanewidth
  {
    out << "leftlanewidth: ";
    rosidl_generator_traits::value_to_yaml(msg.leftlanewidth, out);
    out << ", ";
  }

  // member: rightlanewidth
  {
    out << "rightlanewidth: ";
    rosidl_generator_traits::value_to_yaml(msg.rightlanewidth, out);
    out << ", ";
  }

  // member: relativetime
  {
    out << "relativetime: ";
    rosidl_generator_traits::value_to_yaml(msg.relativetime, out);
    out << ", ";
  }

  // member: laneswitch
  {
    out << "laneswitch: ";
    rosidl_generator_traits::value_to_yaml(msg.laneswitch, out);
    out << ", ";
  }

  // member: laneborrow
  {
    out << "laneborrow: ";
    rosidl_generator_traits::value_to_yaml(msg.laneborrow, out);
    out << ", ";
  }

  // member: lanenum
  {
    out << "lanenum: ";
    rosidl_generator_traits::value_to_yaml(msg.lanenum, out);
    out << ", ";
  }

  // member: lanesite
  {
    out << "lanesite: ";
    rosidl_generator_traits::value_to_yaml(msg.lanesite, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Roadpoint & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: x
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "x: ";
    rosidl_generator_traits::value_to_yaml(msg.x, out);
    out << "\n";
  }

  // member: y
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "y: ";
    rosidl_generator_traits::value_to_yaml(msg.y, out);
    out << "\n";
  }

  // member: gx
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "gx: ";
    rosidl_generator_traits::value_to_yaml(msg.gx, out);
    out << "\n";
  }

  // member: gy
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "gy: ";
    rosidl_generator_traits::value_to_yaml(msg.gy, out);
    out << "\n";
  }

  // member: roadtype
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "roadtype: ";
    rosidl_generator_traits::value_to_yaml(msg.roadtype, out);
    out << "\n";
  }

  // member: speed
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "speed: ";
    rosidl_generator_traits::value_to_yaml(msg.speed, out);
    out << "\n";
  }

  // member: a
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "a: ";
    rosidl_generator_traits::value_to_yaml(msg.a, out);
    out << "\n";
  }

  // member: jerk
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "jerk: ";
    rosidl_generator_traits::value_to_yaml(msg.jerk, out);
    out << "\n";
  }

  // member: lanetype
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lanetype: ";
    rosidl_generator_traits::value_to_yaml(msg.lanetype, out);
    out << "\n";
  }

  // member: turnlight
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "turnlight: ";
    rosidl_generator_traits::value_to_yaml(msg.turnlight, out);
    out << "\n";
  }

  // member: mergelanetype
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "mergelanetype: ";
    rosidl_generator_traits::value_to_yaml(msg.mergelanetype, out);
    out << "\n";
  }

  // member: sensorlanetype
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "sensorlanetype: ";
    rosidl_generator_traits::value_to_yaml(msg.sensorlanetype, out);
    out << "\n";
  }

  // member: heading
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "heading: ";
    rosidl_generator_traits::value_to_yaml(msg.heading, out);
    out << "\n";
  }

  // member: curvature
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "curvature: ";
    rosidl_generator_traits::value_to_yaml(msg.curvature, out);
    out << "\n";
  }

  // member: dkappa
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "dkappa: ";
    rosidl_generator_traits::value_to_yaml(msg.dkappa, out);
    out << "\n";
  }

  // member: ddkappa
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "ddkappa: ";
    rosidl_generator_traits::value_to_yaml(msg.ddkappa, out);
    out << "\n";
  }

  // member: leftsearchdis
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "leftsearchdis: ";
    rosidl_generator_traits::value_to_yaml(msg.leftsearchdis, out);
    out << "\n";
  }

  // member: rightsearchdis
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "rightsearchdis: ";
    rosidl_generator_traits::value_to_yaml(msg.rightsearchdis, out);
    out << "\n";
  }

  // member: s
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "s: ";
    rosidl_generator_traits::value_to_yaml(msg.s, out);
    out << "\n";
  }

  // member: sideroadwidth
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "sideroadwidth: ";
    rosidl_generator_traits::value_to_yaml(msg.sideroadwidth, out);
    out << "\n";
  }

  // member: lanewidth
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lanewidth: ";
    rosidl_generator_traits::value_to_yaml(msg.lanewidth, out);
    out << "\n";
  }

  // member: leftlanewidth
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "leftlanewidth: ";
    rosidl_generator_traits::value_to_yaml(msg.leftlanewidth, out);
    out << "\n";
  }

  // member: rightlanewidth
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "rightlanewidth: ";
    rosidl_generator_traits::value_to_yaml(msg.rightlanewidth, out);
    out << "\n";
  }

  // member: relativetime
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "relativetime: ";
    rosidl_generator_traits::value_to_yaml(msg.relativetime, out);
    out << "\n";
  }

  // member: laneswitch
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "laneswitch: ";
    rosidl_generator_traits::value_to_yaml(msg.laneswitch, out);
    out << "\n";
  }

  // member: laneborrow
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "laneborrow: ";
    rosidl_generator_traits::value_to_yaml(msg.laneborrow, out);
    out << "\n";
  }

  // member: lanenum
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lanenum: ";
    rosidl_generator_traits::value_to_yaml(msg.lanenum, out);
    out << "\n";
  }

  // member: lanesite
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lanesite: ";
    rosidl_generator_traits::value_to_yaml(msg.lanesite, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Roadpoint & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::Roadpoint & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::Roadpoint & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::Roadpoint>()
{
  return "common_msgs_humble::msg::Roadpoint";
}

template<>
inline const char * name<common_msgs_humble::msg::Roadpoint>()
{
  return "common_msgs_humble/msg/Roadpoint";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::Roadpoint>
  : std::integral_constant<bool, true> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::Roadpoint>
  : std::integral_constant<bool, true> {};

template<>
struct is_message<common_msgs_humble::msg::Roadpoint>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__ROADPOINT__TRAITS_HPP_
