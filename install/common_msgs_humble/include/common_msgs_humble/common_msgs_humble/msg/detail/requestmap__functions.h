// generated from rosidl_generator_c/resource/idl__functions.h.em
// with input from common_msgs_humble:msg/Requestmap.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__REQUESTMAP__FUNCTIONS_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__REQUESTMAP__FUNCTIONS_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stdlib.h>

#include "rosidl_runtime_c/visibility_control.h"
#include "common_msgs_humble/msg/rosidl_generator_c__visibility_control.h"

#include "common_msgs_humble/msg/detail/requestmap__struct.h"

/// Initialize msg/Requestmap message.
/**
 * If the init function is called twice for the same message without
 * calling fini inbetween previously allocated memory will be leaked.
 * \param[in,out] msg The previously allocated message pointer.
 * Fields without a default value will not be initialized by this function.
 * You might want to call memset(msg, 0, sizeof(
 * common_msgs_humble__msg__Requestmap
 * )) before or use
 * common_msgs_humble__msg__Requestmap__create()
 * to allocate and initialize the message.
 * \return true if initialization was successful, otherwise false
 */
ROSIDL_GENERATOR_C_PUBLIC_common_msgs_humble
bool
common_msgs_humble__msg__Requestmap__init(common_msgs_humble__msg__Requestmap * msg);

/// Finalize msg/Requestmap message.
/**
 * \param[in,out] msg The allocated message pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_common_msgs_humble
void
common_msgs_humble__msg__Requestmap__fini(common_msgs_humble__msg__Requestmap * msg);

/// Create msg/Requestmap message.
/**
 * It allocates the memory for the message, sets the memory to zero, and
 * calls
 * common_msgs_humble__msg__Requestmap__init().
 * \return The pointer to the initialized message if successful,
 * otherwise NULL
 */
ROSIDL_GENERATOR_C_PUBLIC_common_msgs_humble
common_msgs_humble__msg__Requestmap *
common_msgs_humble__msg__Requestmap__create();

/// Destroy msg/Requestmap message.
/**
 * It calls
 * common_msgs_humble__msg__Requestmap__fini()
 * and frees the memory of the message.
 * \param[in,out] msg The allocated message pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_common_msgs_humble
void
common_msgs_humble__msg__Requestmap__destroy(common_msgs_humble__msg__Requestmap * msg);

/// Check for msg/Requestmap message equality.
/**
 * \param[in] lhs The message on the left hand size of the equality operator.
 * \param[in] rhs The message on the right hand size of the equality operator.
 * \return true if messages are equal, otherwise false.
 */
ROSIDL_GENERATOR_C_PUBLIC_common_msgs_humble
bool
common_msgs_humble__msg__Requestmap__are_equal(const common_msgs_humble__msg__Requestmap * lhs, const common_msgs_humble__msg__Requestmap * rhs);

/// Copy a msg/Requestmap message.
/**
 * This functions performs a deep copy, as opposed to the shallow copy that
 * plain assignment yields.
 *
 * \param[in] input The source message pointer.
 * \param[out] output The target message pointer, which must
 *   have been initialized before calling this function.
 * \return true if successful, or false if either pointer is null
 *   or memory allocation fails.
 */
ROSIDL_GENERATOR_C_PUBLIC_common_msgs_humble
bool
common_msgs_humble__msg__Requestmap__copy(
  const common_msgs_humble__msg__Requestmap * input,
  common_msgs_humble__msg__Requestmap * output);

/// Initialize array of msg/Requestmap messages.
/**
 * It allocates the memory for the number of elements and calls
 * common_msgs_humble__msg__Requestmap__init()
 * for each element of the array.
 * \param[in,out] array The allocated array pointer.
 * \param[in] size The size / capacity of the array.
 * \return true if initialization was successful, otherwise false
 * If the array pointer is valid and the size is zero it is guaranteed
 # to return true.
 */
ROSIDL_GENERATOR_C_PUBLIC_common_msgs_humble
bool
common_msgs_humble__msg__Requestmap__Sequence__init(common_msgs_humble__msg__Requestmap__Sequence * array, size_t size);

/// Finalize array of msg/Requestmap messages.
/**
 * It calls
 * common_msgs_humble__msg__Requestmap__fini()
 * for each element of the array and frees the memory for the number of
 * elements.
 * \param[in,out] array The initialized array pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_common_msgs_humble
void
common_msgs_humble__msg__Requestmap__Sequence__fini(common_msgs_humble__msg__Requestmap__Sequence * array);

/// Create array of msg/Requestmap messages.
/**
 * It allocates the memory for the array and calls
 * common_msgs_humble__msg__Requestmap__Sequence__init().
 * \param[in] size The size / capacity of the array.
 * \return The pointer to the initialized array if successful, otherwise NULL
 */
ROSIDL_GENERATOR_C_PUBLIC_common_msgs_humble
common_msgs_humble__msg__Requestmap__Sequence *
common_msgs_humble__msg__Requestmap__Sequence__create(size_t size);

/// Destroy array of msg/Requestmap messages.
/**
 * It calls
 * common_msgs_humble__msg__Requestmap__Sequence__fini()
 * on the array,
 * and frees the memory of the array.
 * \param[in,out] array The initialized array pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_common_msgs_humble
void
common_msgs_humble__msg__Requestmap__Sequence__destroy(common_msgs_humble__msg__Requestmap__Sequence * array);

/// Check for msg/Requestmap message array equality.
/**
 * \param[in] lhs The message array on the left hand size of the equality operator.
 * \param[in] rhs The message array on the right hand size of the equality operator.
 * \return true if message arrays are equal in size and content, otherwise false.
 */
ROSIDL_GENERATOR_C_PUBLIC_common_msgs_humble
bool
common_msgs_humble__msg__Requestmap__Sequence__are_equal(const common_msgs_humble__msg__Requestmap__Sequence * lhs, const common_msgs_humble__msg__Requestmap__Sequence * rhs);

/// Copy an array of msg/Requestmap messages.
/**
 * This functions performs a deep copy, as opposed to the shallow copy that
 * plain assignment yields.
 *
 * \param[in] input The source array pointer.
 * \param[out] output The target array pointer, which must
 *   have been initialized before calling this function.
 * \return true if successful, or false if either pointer
 *   is null or memory allocation fails.
 */
ROSIDL_GENERATOR_C_PUBLIC_common_msgs_humble
bool
common_msgs_humble__msg__Requestmap__Sequence__copy(
  const common_msgs_humble__msg__Requestmap__Sequence * input,
  common_msgs_humble__msg__Requestmap__Sequence * output);

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__REQUESTMAP__FUNCTIONS_H_
