﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Trajectorypoints.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__TRAJECTORYPOINTS__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__TRAJECTORYPOINTS__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'points'
#include "common_msgs_humble/msg/detail/roadpoint__struct.h"

/// Struct defined in msg/Trajectorypoints in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Trajectorypoints
{
  /// 轨迹点
  common_msgs_humble__msg__Roadpoint__Sequence points;
  /// source from where
  uint8_t source;
  /// 有效位
  uint8_t isvalid;
  uint8_t backpark;
  int64_t gpstime;
  /// 时间戳
  int64_t timestamp;
} common_msgs_humble__msg__Trajectorypoints;

// Struct for a sequence of common_msgs_humble__msg__Trajectorypoints.
typedef struct common_msgs_humble__msg__Trajectorypoints__Sequence
{
  common_msgs_humble__msg__Trajectorypoints * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Trajectorypoints__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__TRAJECTORYPOINTS__STRUCT_H_
