// generated from rosidl_typesupport_introspection_c/resource/idl__type_support.c.em
// with input from common_msgs_humble:msg/Sensorstatus.idl
// generated code does not contain a copyright notice

#include <stddef.h>
#include "common_msgs_humble/msg/detail/sensorstatus__rosidl_typesupport_introspection_c.h"
#include "common_msgs_humble/msg/rosidl_typesupport_introspection_c__visibility_control.h"
#include "rosidl_typesupport_introspection_c/field_types.h"
#include "rosidl_typesupport_introspection_c/identifier.h"
#include "rosidl_typesupport_introspection_c/message_introspection.h"
#include "common_msgs_humble/msg/detail/sensorstatus__functions.h"
#include "common_msgs_humble/msg/detail/sensorstatus__struct.h"


#ifdef __cplusplus
extern "C"
{
#endif

void common_msgs_humble__msg__Sensorstatus__rosidl_typesupport_introspection_c__Sensorstatus_init_function(
  void * message_memory, enum rosidl_runtime_c__message_initialization _init)
{
  // TODO(karsten1987): initializers are not yet implemented for typesupport c
  // see https://bgithub.xyz/ros2/ros2/issues/397
  (void) _init;
  common_msgs_humble__msg__Sensorstatus__init(message_memory);
}

void common_msgs_humble__msg__Sensorstatus__rosidl_typesupport_introspection_c__Sensorstatus_fini_function(void * message_memory)
{
  common_msgs_humble__msg__Sensorstatus__fini(message_memory);
}

static rosidl_typesupport_introspection_c__MessageMember common_msgs_humble__msg__Sensorstatus__rosidl_typesupport_introspection_c__Sensorstatus_message_member_array[2] = {
  {
    "state",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT8,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorstatus, state),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "timestamp",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_INT64,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorstatus, timestamp),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  }
};

static const rosidl_typesupport_introspection_c__MessageMembers common_msgs_humble__msg__Sensorstatus__rosidl_typesupport_introspection_c__Sensorstatus_message_members = {
  "common_msgs_humble__msg",  // message namespace
  "Sensorstatus",  // message name
  2,  // number of fields
  sizeof(common_msgs_humble__msg__Sensorstatus),
  common_msgs_humble__msg__Sensorstatus__rosidl_typesupport_introspection_c__Sensorstatus_message_member_array,  // message members
  common_msgs_humble__msg__Sensorstatus__rosidl_typesupport_introspection_c__Sensorstatus_init_function,  // function to initialize message memory (memory has to be allocated)
  common_msgs_humble__msg__Sensorstatus__rosidl_typesupport_introspection_c__Sensorstatus_fini_function  // function to terminate message instance (will not free memory)
};

// this is not const since it must be initialized on first access
// since C does not allow non-integral compile-time constants
static rosidl_message_type_support_t common_msgs_humble__msg__Sensorstatus__rosidl_typesupport_introspection_c__Sensorstatus_message_type_support_handle = {
  0,
  &common_msgs_humble__msg__Sensorstatus__rosidl_typesupport_introspection_c__Sensorstatus_message_members,
  get_message_typesupport_handle_function,
};

ROSIDL_TYPESUPPORT_INTROSPECTION_C_EXPORT_common_msgs_humble
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, common_msgs_humble, msg, Sensorstatus)() {
  if (!common_msgs_humble__msg__Sensorstatus__rosidl_typesupport_introspection_c__Sensorstatus_message_type_support_handle.typesupport_identifier) {
    common_msgs_humble__msg__Sensorstatus__rosidl_typesupport_introspection_c__Sensorstatus_message_type_support_handle.typesupport_identifier =
      rosidl_typesupport_introspection_c__identifier;
  }
  return &common_msgs_humble__msg__Sensorstatus__rosidl_typesupport_introspection_c__Sensorstatus_message_type_support_handle;
}
#ifdef __cplusplus
}
#endif
