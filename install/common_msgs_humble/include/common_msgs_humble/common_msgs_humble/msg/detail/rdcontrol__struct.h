﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Rdcontrol.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__RDCONTROL__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__RDCONTROL__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__struct.h"

/// Struct defined in msg/Rdcontrol in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Rdcontrol
{
  std_msgs__msg__Header header;
  /// 时间戳，单位ms
  int64_t timestamp;
  /// 驾驶模式，0-非远程驾驶，1-远程驾驶
  uint8_t drivemode;
  /// 座舱状态，0-正常，1-异常
  uint8_t drivestate;
  /// 方向盘转角，正负540，单位度
  float angle;
  /// 油门开度，0-100%，单位1%
  float gas;
  /// 刹车踏板开度，0-100%，单位1%
  float brake;
  /// 转向灯，0-无效（不亮），1-左转，2-右转，3-双闪
  uint8_t turnlignt;
  /// 档位，0-无效，1-P，2-R,3-N,4-D
  uint8_t gear;
  /// 手刹，0-松开，1-拉紧
  uint8_t epb;
} common_msgs_humble__msg__Rdcontrol;

// Struct for a sequence of common_msgs_humble__msg__Rdcontrol.
typedef struct common_msgs_humble__msg__Rdcontrol__Sequence
{
  common_msgs_humble__msg__Rdcontrol * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Rdcontrol__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__RDCONTROL__STRUCT_H_
