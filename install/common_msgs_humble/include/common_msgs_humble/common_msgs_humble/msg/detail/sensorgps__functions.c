// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from common_msgs_humble:msg/Sensorgps.idl
// generated code does not contain a copyright notice
#include "common_msgs_humble/msg/detail/sensorgps__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


bool
common_msgs_humble__msg__Sensorgps__init(common_msgs_humble__msg__Sensorgps * msg)
{
  if (!msg) {
    return false;
  }
  // lon
  // lat
  // alt
  // roadtype
  // lanetype
  // heading
  // pitch
  // roll
  // pitchrate
  // rollrate
  // yawrate
  // accx
  // accy
  // accz
  // mile
  // velocity
  // status
  // rawstatus
  // satenum
  // gpstime
  // isvalid
  // timestamp
  // speed_n
  // speed_e
  // speed_d
  return true;
}

void
common_msgs_humble__msg__Sensorgps__fini(common_msgs_humble__msg__Sensorgps * msg)
{
  if (!msg) {
    return;
  }
  // lon
  // lat
  // alt
  // roadtype
  // lanetype
  // heading
  // pitch
  // roll
  // pitchrate
  // rollrate
  // yawrate
  // accx
  // accy
  // accz
  // mile
  // velocity
  // status
  // rawstatus
  // satenum
  // gpstime
  // isvalid
  // timestamp
  // speed_n
  // speed_e
  // speed_d
}

bool
common_msgs_humble__msg__Sensorgps__are_equal(const common_msgs_humble__msg__Sensorgps * lhs, const common_msgs_humble__msg__Sensorgps * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // lon
  if (lhs->lon != rhs->lon) {
    return false;
  }
  // lat
  if (lhs->lat != rhs->lat) {
    return false;
  }
  // alt
  if (lhs->alt != rhs->alt) {
    return false;
  }
  // roadtype
  if (lhs->roadtype != rhs->roadtype) {
    return false;
  }
  // lanetype
  if (lhs->lanetype != rhs->lanetype) {
    return false;
  }
  // heading
  if (lhs->heading != rhs->heading) {
    return false;
  }
  // pitch
  if (lhs->pitch != rhs->pitch) {
    return false;
  }
  // roll
  if (lhs->roll != rhs->roll) {
    return false;
  }
  // pitchrate
  if (lhs->pitchrate != rhs->pitchrate) {
    return false;
  }
  // rollrate
  if (lhs->rollrate != rhs->rollrate) {
    return false;
  }
  // yawrate
  if (lhs->yawrate != rhs->yawrate) {
    return false;
  }
  // accx
  if (lhs->accx != rhs->accx) {
    return false;
  }
  // accy
  if (lhs->accy != rhs->accy) {
    return false;
  }
  // accz
  if (lhs->accz != rhs->accz) {
    return false;
  }
  // mile
  if (lhs->mile != rhs->mile) {
    return false;
  }
  // velocity
  if (lhs->velocity != rhs->velocity) {
    return false;
  }
  // status
  if (lhs->status != rhs->status) {
    return false;
  }
  // rawstatus
  if (lhs->rawstatus != rhs->rawstatus) {
    return false;
  }
  // satenum
  if (lhs->satenum != rhs->satenum) {
    return false;
  }
  // gpstime
  if (lhs->gpstime != rhs->gpstime) {
    return false;
  }
  // isvalid
  if (lhs->isvalid != rhs->isvalid) {
    return false;
  }
  // timestamp
  if (lhs->timestamp != rhs->timestamp) {
    return false;
  }
  // speed_n
  if (lhs->speed_n != rhs->speed_n) {
    return false;
  }
  // speed_e
  if (lhs->speed_e != rhs->speed_e) {
    return false;
  }
  // speed_d
  if (lhs->speed_d != rhs->speed_d) {
    return false;
  }
  return true;
}

bool
common_msgs_humble__msg__Sensorgps__copy(
  const common_msgs_humble__msg__Sensorgps * input,
  common_msgs_humble__msg__Sensorgps * output)
{
  if (!input || !output) {
    return false;
  }
  // lon
  output->lon = input->lon;
  // lat
  output->lat = input->lat;
  // alt
  output->alt = input->alt;
  // roadtype
  output->roadtype = input->roadtype;
  // lanetype
  output->lanetype = input->lanetype;
  // heading
  output->heading = input->heading;
  // pitch
  output->pitch = input->pitch;
  // roll
  output->roll = input->roll;
  // pitchrate
  output->pitchrate = input->pitchrate;
  // rollrate
  output->rollrate = input->rollrate;
  // yawrate
  output->yawrate = input->yawrate;
  // accx
  output->accx = input->accx;
  // accy
  output->accy = input->accy;
  // accz
  output->accz = input->accz;
  // mile
  output->mile = input->mile;
  // velocity
  output->velocity = input->velocity;
  // status
  output->status = input->status;
  // rawstatus
  output->rawstatus = input->rawstatus;
  // satenum
  output->satenum = input->satenum;
  // gpstime
  output->gpstime = input->gpstime;
  // isvalid
  output->isvalid = input->isvalid;
  // timestamp
  output->timestamp = input->timestamp;
  // speed_n
  output->speed_n = input->speed_n;
  // speed_e
  output->speed_e = input->speed_e;
  // speed_d
  output->speed_d = input->speed_d;
  return true;
}

common_msgs_humble__msg__Sensorgps *
common_msgs_humble__msg__Sensorgps__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Sensorgps * msg = (common_msgs_humble__msg__Sensorgps *)allocator.allocate(sizeof(common_msgs_humble__msg__Sensorgps), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(common_msgs_humble__msg__Sensorgps));
  bool success = common_msgs_humble__msg__Sensorgps__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
common_msgs_humble__msg__Sensorgps__destroy(common_msgs_humble__msg__Sensorgps * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    common_msgs_humble__msg__Sensorgps__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
common_msgs_humble__msg__Sensorgps__Sequence__init(common_msgs_humble__msg__Sensorgps__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Sensorgps * data = NULL;

  if (size) {
    data = (common_msgs_humble__msg__Sensorgps *)allocator.zero_allocate(size, sizeof(common_msgs_humble__msg__Sensorgps), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = common_msgs_humble__msg__Sensorgps__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        common_msgs_humble__msg__Sensorgps__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
common_msgs_humble__msg__Sensorgps__Sequence__fini(common_msgs_humble__msg__Sensorgps__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      common_msgs_humble__msg__Sensorgps__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

common_msgs_humble__msg__Sensorgps__Sequence *
common_msgs_humble__msg__Sensorgps__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Sensorgps__Sequence * array = (common_msgs_humble__msg__Sensorgps__Sequence *)allocator.allocate(sizeof(common_msgs_humble__msg__Sensorgps__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = common_msgs_humble__msg__Sensorgps__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
common_msgs_humble__msg__Sensorgps__Sequence__destroy(common_msgs_humble__msg__Sensorgps__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    common_msgs_humble__msg__Sensorgps__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
common_msgs_humble__msg__Sensorgps__Sequence__are_equal(const common_msgs_humble__msg__Sensorgps__Sequence * lhs, const common_msgs_humble__msg__Sensorgps__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!common_msgs_humble__msg__Sensorgps__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
common_msgs_humble__msg__Sensorgps__Sequence__copy(
  const common_msgs_humble__msg__Sensorgps__Sequence * input,
  common_msgs_humble__msg__Sensorgps__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(common_msgs_humble__msg__Sensorgps);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    common_msgs_humble__msg__Sensorgps * data =
      (common_msgs_humble__msg__Sensorgps *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!common_msgs_humble__msg__Sensorgps__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          common_msgs_humble__msg__Sensorgps__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!common_msgs_humble__msg__Sensorgps__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
