// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Requestmap.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__REQUESTMAP__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__REQUESTMAP__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/requestmap__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Requestmap_timestamp
{
public:
  explicit Init_Requestmap_timestamp(::common_msgs_humble::msg::Requestmap & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Requestmap timestamp(::common_msgs_humble::msg::Requestmap::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Requestmap msg_;
};

class Init_Requestmap_mapname
{
public:
  explicit Init_Requestmap_mapname(::common_msgs_humble::msg::Requestmap & msg)
  : msg_(msg)
  {}
  Init_Requestmap_timestamp mapname(::common_msgs_humble::msg::Requestmap::_mapname_type arg)
  {
    msg_.mapname = std::move(arg);
    return Init_Requestmap_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Requestmap msg_;
};

class Init_Requestmap_request
{
public:
  Init_Requestmap_request()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Requestmap_mapname request(::common_msgs_humble::msg::Requestmap::_request_type arg)
  {
    msg_.request = std::move(arg);
    return Init_Requestmap_mapname(msg_);
  }

private:
  ::common_msgs_humble::msg::Requestmap msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Requestmap>()
{
  return common_msgs_humble::msg::builder::Init_Requestmap_request();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__REQUESTMAP__BUILDER_HPP_
