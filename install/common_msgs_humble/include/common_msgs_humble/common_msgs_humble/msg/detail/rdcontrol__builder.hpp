// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Rdcontrol.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__RDCONTROL__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__RDCONTROL__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/rdcontrol__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Rdcontrol_epb
{
public:
  explicit Init_Rdcontrol_epb(::common_msgs_humble::msg::Rdcontrol & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Rdcontrol epb(::common_msgs_humble::msg::Rdcontrol::_epb_type arg)
  {
    msg_.epb = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Rdcontrol msg_;
};

class Init_Rdcontrol_gear
{
public:
  explicit Init_Rdcontrol_gear(::common_msgs_humble::msg::Rdcontrol & msg)
  : msg_(msg)
  {}
  Init_Rdcontrol_epb gear(::common_msgs_humble::msg::Rdcontrol::_gear_type arg)
  {
    msg_.gear = std::move(arg);
    return Init_Rdcontrol_epb(msg_);
  }

private:
  ::common_msgs_humble::msg::Rdcontrol msg_;
};

class Init_Rdcontrol_turnlignt
{
public:
  explicit Init_Rdcontrol_turnlignt(::common_msgs_humble::msg::Rdcontrol & msg)
  : msg_(msg)
  {}
  Init_Rdcontrol_gear turnlignt(::common_msgs_humble::msg::Rdcontrol::_turnlignt_type arg)
  {
    msg_.turnlignt = std::move(arg);
    return Init_Rdcontrol_gear(msg_);
  }

private:
  ::common_msgs_humble::msg::Rdcontrol msg_;
};

class Init_Rdcontrol_brake
{
public:
  explicit Init_Rdcontrol_brake(::common_msgs_humble::msg::Rdcontrol & msg)
  : msg_(msg)
  {}
  Init_Rdcontrol_turnlignt brake(::common_msgs_humble::msg::Rdcontrol::_brake_type arg)
  {
    msg_.brake = std::move(arg);
    return Init_Rdcontrol_turnlignt(msg_);
  }

private:
  ::common_msgs_humble::msg::Rdcontrol msg_;
};

class Init_Rdcontrol_gas
{
public:
  explicit Init_Rdcontrol_gas(::common_msgs_humble::msg::Rdcontrol & msg)
  : msg_(msg)
  {}
  Init_Rdcontrol_brake gas(::common_msgs_humble::msg::Rdcontrol::_gas_type arg)
  {
    msg_.gas = std::move(arg);
    return Init_Rdcontrol_brake(msg_);
  }

private:
  ::common_msgs_humble::msg::Rdcontrol msg_;
};

class Init_Rdcontrol_angle
{
public:
  explicit Init_Rdcontrol_angle(::common_msgs_humble::msg::Rdcontrol & msg)
  : msg_(msg)
  {}
  Init_Rdcontrol_gas angle(::common_msgs_humble::msg::Rdcontrol::_angle_type arg)
  {
    msg_.angle = std::move(arg);
    return Init_Rdcontrol_gas(msg_);
  }

private:
  ::common_msgs_humble::msg::Rdcontrol msg_;
};

class Init_Rdcontrol_drivestate
{
public:
  explicit Init_Rdcontrol_drivestate(::common_msgs_humble::msg::Rdcontrol & msg)
  : msg_(msg)
  {}
  Init_Rdcontrol_angle drivestate(::common_msgs_humble::msg::Rdcontrol::_drivestate_type arg)
  {
    msg_.drivestate = std::move(arg);
    return Init_Rdcontrol_angle(msg_);
  }

private:
  ::common_msgs_humble::msg::Rdcontrol msg_;
};

class Init_Rdcontrol_drivemode
{
public:
  explicit Init_Rdcontrol_drivemode(::common_msgs_humble::msg::Rdcontrol & msg)
  : msg_(msg)
  {}
  Init_Rdcontrol_drivestate drivemode(::common_msgs_humble::msg::Rdcontrol::_drivemode_type arg)
  {
    msg_.drivemode = std::move(arg);
    return Init_Rdcontrol_drivestate(msg_);
  }

private:
  ::common_msgs_humble::msg::Rdcontrol msg_;
};

class Init_Rdcontrol_timestamp
{
public:
  explicit Init_Rdcontrol_timestamp(::common_msgs_humble::msg::Rdcontrol & msg)
  : msg_(msg)
  {}
  Init_Rdcontrol_drivemode timestamp(::common_msgs_humble::msg::Rdcontrol::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return Init_Rdcontrol_drivemode(msg_);
  }

private:
  ::common_msgs_humble::msg::Rdcontrol msg_;
};

class Init_Rdcontrol_header
{
public:
  Init_Rdcontrol_header()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Rdcontrol_timestamp header(::common_msgs_humble::msg::Rdcontrol::_header_type arg)
  {
    msg_.header = std::move(arg);
    return Init_Rdcontrol_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Rdcontrol msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Rdcontrol>()
{
  return common_msgs_humble::msg::builder::Init_Rdcontrol_header();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__RDCONTROL__BUILDER_HPP_
