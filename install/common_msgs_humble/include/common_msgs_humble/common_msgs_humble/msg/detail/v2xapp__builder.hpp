// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/V2xapp.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__V2XAPP__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__V2XAPP__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/v2xapp__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_V2xapp_timestamp
{
public:
  explicit Init_V2xapp_timestamp(::common_msgs_humble::msg::V2xapp & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::V2xapp timestamp(::common_msgs_humble::msg::V2xapp::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::V2xapp msg_;
};

class Init_V2xapp_isvalid
{
public:
  explicit Init_V2xapp_isvalid(::common_msgs_humble::msg::V2xapp & msg)
  : msg_(msg)
  {}
  Init_V2xapp_timestamp isvalid(::common_msgs_humble::msg::V2xapp::_isvalid_type arg)
  {
    msg_.isvalid = std::move(arg);
    return Init_V2xapp_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::V2xapp msg_;
};

class Init_V2xapp_color
{
public:
  explicit Init_V2xapp_color(::common_msgs_humble::msg::V2xapp & msg)
  : msg_(msg)
  {}
  Init_V2xapp_isvalid color(::common_msgs_humble::msg::V2xapp::_color_type arg)
  {
    msg_.color = std::move(arg);
    return Init_V2xapp_isvalid(msg_);
  }

private:
  ::common_msgs_humble::msg::V2xapp msg_;
};

class Init_V2xapp_speedlimit
{
public:
  explicit Init_V2xapp_speedlimit(::common_msgs_humble::msg::V2xapp & msg)
  : msg_(msg)
  {}
  Init_V2xapp_color speedlimit(::common_msgs_humble::msg::V2xapp::_speedlimit_type arg)
  {
    msg_.speedlimit = std::move(arg);
    return Init_V2xapp_color(msg_);
  }

private:
  ::common_msgs_humble::msg::V2xapp msg_;
};

class Init_V2xapp_v2xtype
{
public:
  Init_V2xapp_v2xtype()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_V2xapp_speedlimit v2xtype(::common_msgs_humble::msg::V2xapp::_v2xtype_type arg)
  {
    msg_.v2xtype = std::move(arg);
    return Init_V2xapp_speedlimit(msg_);
  }

private:
  ::common_msgs_humble::msg::V2xapp msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::V2xapp>()
{
  return common_msgs_humble::msg::builder::Init_V2xapp_v2xtype();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__V2XAPP__BUILDER_HPP_
