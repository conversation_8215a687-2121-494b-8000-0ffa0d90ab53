// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Trajectorypoints.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__TRAJECTORYPOINTS__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__TRAJECTORYPOINTS__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/trajectorypoints__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Trajectorypoints_timestamp
{
public:
  explicit Init_Trajectorypoints_timestamp(::common_msgs_humble::msg::Trajectorypoints & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Trajectorypoints timestamp(::common_msgs_humble::msg::Trajectorypoints::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Trajectorypoints msg_;
};

class Init_Trajectorypoints_gpstime
{
public:
  explicit Init_Trajectorypoints_gpstime(::common_msgs_humble::msg::Trajectorypoints & msg)
  : msg_(msg)
  {}
  Init_Trajectorypoints_timestamp gpstime(::common_msgs_humble::msg::Trajectorypoints::_gpstime_type arg)
  {
    msg_.gpstime = std::move(arg);
    return Init_Trajectorypoints_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Trajectorypoints msg_;
};

class Init_Trajectorypoints_backpark
{
public:
  explicit Init_Trajectorypoints_backpark(::common_msgs_humble::msg::Trajectorypoints & msg)
  : msg_(msg)
  {}
  Init_Trajectorypoints_gpstime backpark(::common_msgs_humble::msg::Trajectorypoints::_backpark_type arg)
  {
    msg_.backpark = std::move(arg);
    return Init_Trajectorypoints_gpstime(msg_);
  }

private:
  ::common_msgs_humble::msg::Trajectorypoints msg_;
};

class Init_Trajectorypoints_isvalid
{
public:
  explicit Init_Trajectorypoints_isvalid(::common_msgs_humble::msg::Trajectorypoints & msg)
  : msg_(msg)
  {}
  Init_Trajectorypoints_backpark isvalid(::common_msgs_humble::msg::Trajectorypoints::_isvalid_type arg)
  {
    msg_.isvalid = std::move(arg);
    return Init_Trajectorypoints_backpark(msg_);
  }

private:
  ::common_msgs_humble::msg::Trajectorypoints msg_;
};

class Init_Trajectorypoints_source
{
public:
  explicit Init_Trajectorypoints_source(::common_msgs_humble::msg::Trajectorypoints & msg)
  : msg_(msg)
  {}
  Init_Trajectorypoints_isvalid source(::common_msgs_humble::msg::Trajectorypoints::_source_type arg)
  {
    msg_.source = std::move(arg);
    return Init_Trajectorypoints_isvalid(msg_);
  }

private:
  ::common_msgs_humble::msg::Trajectorypoints msg_;
};

class Init_Trajectorypoints_points
{
public:
  Init_Trajectorypoints_points()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Trajectorypoints_source points(::common_msgs_humble::msg::Trajectorypoints::_points_type arg)
  {
    msg_.points = std::move(arg);
    return Init_Trajectorypoints_source(msg_);
  }

private:
  ::common_msgs_humble::msg::Trajectorypoints msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Trajectorypoints>()
{
  return common_msgs_humble::msg::builder::Init_Trajectorypoints_points();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__TRAJECTORYPOINTS__BUILDER_HPP_
