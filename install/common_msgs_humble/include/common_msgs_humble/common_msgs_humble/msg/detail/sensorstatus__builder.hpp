// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Sensorstatus.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORSTATUS__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORSTATUS__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/sensorstatus__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Sensorstatus_timestamp
{
public:
  explicit Init_Sensorstatus_timestamp(::common_msgs_humble::msg::Sensorstatus & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Sensorstatus timestamp(::common_msgs_humble::msg::Sensorstatus::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorstatus msg_;
};

class Init_Sensorstatus_state
{
public:
  Init_Sensorstatus_state()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Sensorstatus_timestamp state(::common_msgs_humble::msg::Sensorstatus::_state_type arg)
  {
    msg_.state = std::move(arg);
    return Init_Sensorstatus_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorstatus msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Sensorstatus>()
{
  return common_msgs_humble::msg::builder::Init_Sensorstatus_state();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORSTATUS__BUILDER_HPP_
