// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Sl.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__SL__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__SL__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/sl__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Sl_l
{
public:
  explicit Init_Sl_l(::common_msgs_humble::msg::Sl & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Sl l(::common_msgs_humble::msg::Sl::_l_type arg)
  {
    msg_.l = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Sl msg_;
};

class Init_Sl_s
{
public:
  Init_Sl_s()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Sl_l s(::common_msgs_humble::msg::Sl::_s_type arg)
  {
    msg_.s = std::move(arg);
    return Init_Sl_l(msg_);
  }

private:
  ::common_msgs_humble::msg::Sl msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Sl>()
{
  return common_msgs_humble::msg::builder::Init_Sl_s();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__SL__BUILDER_HPP_
