// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Sensorobject.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSOROBJECT__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSOROBJECT__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'points'
#include "common_msgs_humble/msg/detail/point3d__struct.hpp"
// Member 'object_history'
#include "common_msgs_humble/msg/detail/objecthistory__struct.hpp"
// Member 'object_prediction'
#include "common_msgs_humble/msg/detail/objectprediction__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Sensorobject __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Sensorobject __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Sensorobject_
{
  using Type = Sensorobject_<ContainerAllocator>;

  explicit Sensorobject_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->id = 0ul;
      this->x = 0.0f;
      this->y = 0.0f;
      this->z = 0.0f;
      this->longtitude = 0.0;
      this->latitude = 0.0;
      this->altitude = 0.0;
      this->relspeedy = 0.0f;
      this->relspeedx = 0.0f;
      this->rollrad = 0.0f;
      this->pitchrad = 0.0f;
      this->azimuth = 0.0f;
      this->pitchrate = 0.0;
      this->rollrate = 0.0;
      this->yawrate = 0.0;
      this->width = 0.0f;
      this->length = 0.0f;
      this->height = 0.0f;
      this->classification = 0;
      this->value = 0;
      this->confidence = 0.0f;
      this->driving_intent = 0;
      this->behavior_state = 0;
      this->radarindex = 0;
      this->radarobjectid = 0;
      this->s = 0.0f;
      this->l = 0.0f;
      this->speeds = 0.0f;
      this->speedl = 0.0f;
      this->object_decision = 0;
    }
  }

  explicit Sensorobject_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->id = 0ul;
      this->x = 0.0f;
      this->y = 0.0f;
      this->z = 0.0f;
      this->longtitude = 0.0;
      this->latitude = 0.0;
      this->altitude = 0.0;
      this->relspeedy = 0.0f;
      this->relspeedx = 0.0f;
      this->rollrad = 0.0f;
      this->pitchrad = 0.0f;
      this->azimuth = 0.0f;
      this->pitchrate = 0.0;
      this->rollrate = 0.0;
      this->yawrate = 0.0;
      this->width = 0.0f;
      this->length = 0.0f;
      this->height = 0.0f;
      this->classification = 0;
      this->value = 0;
      this->confidence = 0.0f;
      this->driving_intent = 0;
      this->behavior_state = 0;
      this->radarindex = 0;
      this->radarobjectid = 0;
      this->s = 0.0f;
      this->l = 0.0f;
      this->speeds = 0.0f;
      this->speedl = 0.0f;
      this->object_decision = 0;
    }
  }

  // field types and members
  using _id_type =
    uint32_t;
  _id_type id;
  using _x_type =
    float;
  _x_type x;
  using _y_type =
    float;
  _y_type y;
  using _z_type =
    float;
  _z_type z;
  using _longtitude_type =
    double;
  _longtitude_type longtitude;
  using _latitude_type =
    double;
  _latitude_type latitude;
  using _altitude_type =
    double;
  _altitude_type altitude;
  using _relspeedy_type =
    float;
  _relspeedy_type relspeedy;
  using _relspeedx_type =
    float;
  _relspeedx_type relspeedx;
  using _rollrad_type =
    float;
  _rollrad_type rollrad;
  using _pitchrad_type =
    float;
  _pitchrad_type pitchrad;
  using _azimuth_type =
    float;
  _azimuth_type azimuth;
  using _pitchrate_type =
    double;
  _pitchrate_type pitchrate;
  using _rollrate_type =
    double;
  _rollrate_type rollrate;
  using _yawrate_type =
    double;
  _yawrate_type yawrate;
  using _width_type =
    float;
  _width_type width;
  using _length_type =
    float;
  _length_type length;
  using _height_type =
    float;
  _height_type height;
  using _classification_type =
    uint8_t;
  _classification_type classification;
  using _value_type =
    uint8_t;
  _value_type value;
  using _confidence_type =
    float;
  _confidence_type confidence;
  using _points_type =
    std::vector<common_msgs_humble::msg::Point3d_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Point3d_<ContainerAllocator>>>;
  _points_type points;
  using _driving_intent_type =
    uint8_t;
  _driving_intent_type driving_intent;
  using _behavior_state_type =
    uint8_t;
  _behavior_state_type behavior_state;
  using _radarindex_type =
    uint8_t;
  _radarindex_type radarindex;
  using _radarobjectid_type =
    uint8_t;
  _radarobjectid_type radarobjectid;
  using _s_type =
    float;
  _s_type s;
  using _l_type =
    float;
  _l_type l;
  using _speeds_type =
    float;
  _speeds_type speeds;
  using _speedl_type =
    float;
  _speedl_type speedl;
  using _object_decision_type =
    uint8_t;
  _object_decision_type object_decision;
  using _object_history_type =
    std::vector<common_msgs_humble::msg::Objecthistory_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Objecthistory_<ContainerAllocator>>>;
  _object_history_type object_history;
  using _object_prediction_type =
    std::vector<common_msgs_humble::msg::Objectprediction_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Objectprediction_<ContainerAllocator>>>;
  _object_prediction_type object_prediction;

  // setters for named parameter idiom
  Type & set__id(
    const uint32_t & _arg)
  {
    this->id = _arg;
    return *this;
  }
  Type & set__x(
    const float & _arg)
  {
    this->x = _arg;
    return *this;
  }
  Type & set__y(
    const float & _arg)
  {
    this->y = _arg;
    return *this;
  }
  Type & set__z(
    const float & _arg)
  {
    this->z = _arg;
    return *this;
  }
  Type & set__longtitude(
    const double & _arg)
  {
    this->longtitude = _arg;
    return *this;
  }
  Type & set__latitude(
    const double & _arg)
  {
    this->latitude = _arg;
    return *this;
  }
  Type & set__altitude(
    const double & _arg)
  {
    this->altitude = _arg;
    return *this;
  }
  Type & set__relspeedy(
    const float & _arg)
  {
    this->relspeedy = _arg;
    return *this;
  }
  Type & set__relspeedx(
    const float & _arg)
  {
    this->relspeedx = _arg;
    return *this;
  }
  Type & set__rollrad(
    const float & _arg)
  {
    this->rollrad = _arg;
    return *this;
  }
  Type & set__pitchrad(
    const float & _arg)
  {
    this->pitchrad = _arg;
    return *this;
  }
  Type & set__azimuth(
    const float & _arg)
  {
    this->azimuth = _arg;
    return *this;
  }
  Type & set__pitchrate(
    const double & _arg)
  {
    this->pitchrate = _arg;
    return *this;
  }
  Type & set__rollrate(
    const double & _arg)
  {
    this->rollrate = _arg;
    return *this;
  }
  Type & set__yawrate(
    const double & _arg)
  {
    this->yawrate = _arg;
    return *this;
  }
  Type & set__width(
    const float & _arg)
  {
    this->width = _arg;
    return *this;
  }
  Type & set__length(
    const float & _arg)
  {
    this->length = _arg;
    return *this;
  }
  Type & set__height(
    const float & _arg)
  {
    this->height = _arg;
    return *this;
  }
  Type & set__classification(
    const uint8_t & _arg)
  {
    this->classification = _arg;
    return *this;
  }
  Type & set__value(
    const uint8_t & _arg)
  {
    this->value = _arg;
    return *this;
  }
  Type & set__confidence(
    const float & _arg)
  {
    this->confidence = _arg;
    return *this;
  }
  Type & set__points(
    const std::vector<common_msgs_humble::msg::Point3d_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Point3d_<ContainerAllocator>>> & _arg)
  {
    this->points = _arg;
    return *this;
  }
  Type & set__driving_intent(
    const uint8_t & _arg)
  {
    this->driving_intent = _arg;
    return *this;
  }
  Type & set__behavior_state(
    const uint8_t & _arg)
  {
    this->behavior_state = _arg;
    return *this;
  }
  Type & set__radarindex(
    const uint8_t & _arg)
  {
    this->radarindex = _arg;
    return *this;
  }
  Type & set__radarobjectid(
    const uint8_t & _arg)
  {
    this->radarobjectid = _arg;
    return *this;
  }
  Type & set__s(
    const float & _arg)
  {
    this->s = _arg;
    return *this;
  }
  Type & set__l(
    const float & _arg)
  {
    this->l = _arg;
    return *this;
  }
  Type & set__speeds(
    const float & _arg)
  {
    this->speeds = _arg;
    return *this;
  }
  Type & set__speedl(
    const float & _arg)
  {
    this->speedl = _arg;
    return *this;
  }
  Type & set__object_decision(
    const uint8_t & _arg)
  {
    this->object_decision = _arg;
    return *this;
  }
  Type & set__object_history(
    const std::vector<common_msgs_humble::msg::Objecthistory_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Objecthistory_<ContainerAllocator>>> & _arg)
  {
    this->object_history = _arg;
    return *this;
  }
  Type & set__object_prediction(
    const std::vector<common_msgs_humble::msg::Objectprediction_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Objectprediction_<ContainerAllocator>>> & _arg)
  {
    this->object_prediction = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Sensorobject_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Sensorobject_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Sensorobject_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Sensorobject_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Sensorobject_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Sensorobject_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Sensorobject_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Sensorobject_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Sensorobject_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Sensorobject_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Sensorobject
    std::shared_ptr<common_msgs_humble::msg::Sensorobject_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Sensorobject
    std::shared_ptr<common_msgs_humble::msg::Sensorobject_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Sensorobject_ & other) const
  {
    if (this->id != other.id) {
      return false;
    }
    if (this->x != other.x) {
      return false;
    }
    if (this->y != other.y) {
      return false;
    }
    if (this->z != other.z) {
      return false;
    }
    if (this->longtitude != other.longtitude) {
      return false;
    }
    if (this->latitude != other.latitude) {
      return false;
    }
    if (this->altitude != other.altitude) {
      return false;
    }
    if (this->relspeedy != other.relspeedy) {
      return false;
    }
    if (this->relspeedx != other.relspeedx) {
      return false;
    }
    if (this->rollrad != other.rollrad) {
      return false;
    }
    if (this->pitchrad != other.pitchrad) {
      return false;
    }
    if (this->azimuth != other.azimuth) {
      return false;
    }
    if (this->pitchrate != other.pitchrate) {
      return false;
    }
    if (this->rollrate != other.rollrate) {
      return false;
    }
    if (this->yawrate != other.yawrate) {
      return false;
    }
    if (this->width != other.width) {
      return false;
    }
    if (this->length != other.length) {
      return false;
    }
    if (this->height != other.height) {
      return false;
    }
    if (this->classification != other.classification) {
      return false;
    }
    if (this->value != other.value) {
      return false;
    }
    if (this->confidence != other.confidence) {
      return false;
    }
    if (this->points != other.points) {
      return false;
    }
    if (this->driving_intent != other.driving_intent) {
      return false;
    }
    if (this->behavior_state != other.behavior_state) {
      return false;
    }
    if (this->radarindex != other.radarindex) {
      return false;
    }
    if (this->radarobjectid != other.radarobjectid) {
      return false;
    }
    if (this->s != other.s) {
      return false;
    }
    if (this->l != other.l) {
      return false;
    }
    if (this->speeds != other.speeds) {
      return false;
    }
    if (this->speedl != other.speedl) {
      return false;
    }
    if (this->object_decision != other.object_decision) {
      return false;
    }
    if (this->object_history != other.object_history) {
      return false;
    }
    if (this->object_prediction != other.object_prediction) {
      return false;
    }
    return true;
  }
  bool operator!=(const Sensorobject_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Sensorobject_

// alias to use template instance with default allocator
using Sensorobject =
  common_msgs_humble::msg::Sensorobject_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSOROBJECT__STRUCT_HPP_
