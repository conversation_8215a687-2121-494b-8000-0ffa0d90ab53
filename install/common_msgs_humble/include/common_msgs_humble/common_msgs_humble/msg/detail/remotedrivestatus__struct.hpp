// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Remotedrivestatus.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__REMOTEDRIVESTATUS__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__REMOTEDRIVESTATUS__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Remotedrivestatus __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Remotedrivestatus __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Remotedrivestatus_
{
  using Type = Remotedrivestatus_<ContainerAllocator>;

  explicit Remotedrivestatus_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->timestamp = 0ll;
      this->drivemode = 0;
      this->remotedrivestatus = 0;
      this->systemstatus = 0;
    }
  }

  explicit Remotedrivestatus_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->timestamp = 0ll;
      this->drivemode = 0;
      this->remotedrivestatus = 0;
      this->systemstatus = 0;
    }
  }

  // field types and members
  using _timestamp_type =
    int64_t;
  _timestamp_type timestamp;
  using _drivemode_type =
    uint8_t;
  _drivemode_type drivemode;
  using _remotedrivestatus_type =
    uint8_t;
  _remotedrivestatus_type remotedrivestatus;
  using _systemstatus_type =
    uint8_t;
  _systemstatus_type systemstatus;

  // setters for named parameter idiom
  Type & set__timestamp(
    const int64_t & _arg)
  {
    this->timestamp = _arg;
    return *this;
  }
  Type & set__drivemode(
    const uint8_t & _arg)
  {
    this->drivemode = _arg;
    return *this;
  }
  Type & set__remotedrivestatus(
    const uint8_t & _arg)
  {
    this->remotedrivestatus = _arg;
    return *this;
  }
  Type & set__systemstatus(
    const uint8_t & _arg)
  {
    this->systemstatus = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Remotedrivestatus_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Remotedrivestatus_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Remotedrivestatus_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Remotedrivestatus_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Remotedrivestatus_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Remotedrivestatus_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Remotedrivestatus_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Remotedrivestatus_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Remotedrivestatus_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Remotedrivestatus_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Remotedrivestatus
    std::shared_ptr<common_msgs_humble::msg::Remotedrivestatus_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Remotedrivestatus
    std::shared_ptr<common_msgs_humble::msg::Remotedrivestatus_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Remotedrivestatus_ & other) const
  {
    if (this->timestamp != other.timestamp) {
      return false;
    }
    if (this->drivemode != other.drivemode) {
      return false;
    }
    if (this->remotedrivestatus != other.remotedrivestatus) {
      return false;
    }
    if (this->systemstatus != other.systemstatus) {
      return false;
    }
    return true;
  }
  bool operator!=(const Remotedrivestatus_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Remotedrivestatus_

// alias to use template instance with default allocator
using Remotedrivestatus =
  common_msgs_humble::msg::Remotedrivestatus_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__REMOTEDRIVESTATUS__STRUCT_HPP_
