// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Sensorcameralight.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORCAMERALIGHT__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORCAMERALIGHT__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/sensorcameralight__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Sensorcameralight_timestamp
{
public:
  explicit Init_Sensorcameralight_timestamp(::common_msgs_humble::msg::Sensorcameralight & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Sensorcameralight timestamp(::common_msgs_humble::msg::Sensorcameralight::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorcameralight msg_;
};

class Init_Sensorcameralight_isvalid
{
public:
  explicit Init_Sensorcameralight_isvalid(::common_msgs_humble::msg::Sensorcameralight & msg)
  : msg_(msg)
  {}
  Init_Sensorcameralight_timestamp isvalid(::common_msgs_humble::msg::Sensorcameralight::_isvalid_type arg)
  {
    msg_.isvalid = std::move(arg);
    return Init_Sensorcameralight_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorcameralight msg_;
};

class Init_Sensorcameralight_start
{
public:
  Init_Sensorcameralight_start()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Sensorcameralight_isvalid start(::common_msgs_humble::msg::Sensorcameralight::_start_type arg)
  {
    msg_.start = std::move(arg);
    return Init_Sensorcameralight_isvalid(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorcameralight msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Sensorcameralight>()
{
  return common_msgs_humble::msg::builder::Init_Sensorcameralight_start();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORCAMERALIGHT__BUILDER_HPP_
