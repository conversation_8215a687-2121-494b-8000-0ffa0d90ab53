// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Requestmap.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__REQUESTMAP__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__REQUESTMAP__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Requestmap __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Requestmap __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Requestmap_
{
  using Type = Requestmap_<ContainerAllocator>;

  explicit Requestmap_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->request = 0;
      this->mapname = 0;
      this->timestamp = 0ll;
    }
  }

  explicit Requestmap_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->request = 0;
      this->mapname = 0;
      this->timestamp = 0ll;
    }
  }

  // field types and members
  using _request_type =
    uint8_t;
  _request_type request;
  using _mapname_type =
    uint8_t;
  _mapname_type mapname;
  using _timestamp_type =
    int64_t;
  _timestamp_type timestamp;

  // setters for named parameter idiom
  Type & set__request(
    const uint8_t & _arg)
  {
    this->request = _arg;
    return *this;
  }
  Type & set__mapname(
    const uint8_t & _arg)
  {
    this->mapname = _arg;
    return *this;
  }
  Type & set__timestamp(
    const int64_t & _arg)
  {
    this->timestamp = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Requestmap_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Requestmap_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Requestmap_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Requestmap_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Requestmap_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Requestmap_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Requestmap_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Requestmap_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Requestmap_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Requestmap_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Requestmap
    std::shared_ptr<common_msgs_humble::msg::Requestmap_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Requestmap
    std::shared_ptr<common_msgs_humble::msg::Requestmap_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Requestmap_ & other) const
  {
    if (this->request != other.request) {
      return false;
    }
    if (this->mapname != other.mapname) {
      return false;
    }
    if (this->timestamp != other.timestamp) {
      return false;
    }
    return true;
  }
  bool operator!=(const Requestmap_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Requestmap_

// alias to use template instance with default allocator
using Requestmap =
  common_msgs_humble::msg::Requestmap_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__REQUESTMAP__STRUCT_HPP_
