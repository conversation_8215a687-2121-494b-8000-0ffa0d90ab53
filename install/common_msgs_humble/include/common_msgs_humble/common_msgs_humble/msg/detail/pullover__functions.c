// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from common_msgs_humble:msg/Pullover.idl
// generated code does not contain a copyright notice
#include "common_msgs_humble/msg/detail/pullover__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


bool
common_msgs_humble__msg__Pullover__init(common_msgs_humble__msg__Pullover * msg)
{
  if (!msg) {
    return false;
  }
  // timestamp
  // po_decision
  // po_button
  // doorstatus
  // reserve
  return true;
}

void
common_msgs_humble__msg__Pullover__fini(common_msgs_humble__msg__Pullover * msg)
{
  if (!msg) {
    return;
  }
  // timestamp
  // po_decision
  // po_button
  // doorstatus
  // reserve
}

bool
common_msgs_humble__msg__Pullover__are_equal(const common_msgs_humble__msg__Pullover * lhs, const common_msgs_humble__msg__Pullover * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // timestamp
  if (lhs->timestamp != rhs->timestamp) {
    return false;
  }
  // po_decision
  if (lhs->po_decision != rhs->po_decision) {
    return false;
  }
  // po_button
  if (lhs->po_button != rhs->po_button) {
    return false;
  }
  // doorstatus
  if (lhs->doorstatus != rhs->doorstatus) {
    return false;
  }
  // reserve
  if (lhs->reserve != rhs->reserve) {
    return false;
  }
  return true;
}

bool
common_msgs_humble__msg__Pullover__copy(
  const common_msgs_humble__msg__Pullover * input,
  common_msgs_humble__msg__Pullover * output)
{
  if (!input || !output) {
    return false;
  }
  // timestamp
  output->timestamp = input->timestamp;
  // po_decision
  output->po_decision = input->po_decision;
  // po_button
  output->po_button = input->po_button;
  // doorstatus
  output->doorstatus = input->doorstatus;
  // reserve
  output->reserve = input->reserve;
  return true;
}

common_msgs_humble__msg__Pullover *
common_msgs_humble__msg__Pullover__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Pullover * msg = (common_msgs_humble__msg__Pullover *)allocator.allocate(sizeof(common_msgs_humble__msg__Pullover), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(common_msgs_humble__msg__Pullover));
  bool success = common_msgs_humble__msg__Pullover__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
common_msgs_humble__msg__Pullover__destroy(common_msgs_humble__msg__Pullover * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    common_msgs_humble__msg__Pullover__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
common_msgs_humble__msg__Pullover__Sequence__init(common_msgs_humble__msg__Pullover__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Pullover * data = NULL;

  if (size) {
    data = (common_msgs_humble__msg__Pullover *)allocator.zero_allocate(size, sizeof(common_msgs_humble__msg__Pullover), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = common_msgs_humble__msg__Pullover__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        common_msgs_humble__msg__Pullover__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
common_msgs_humble__msg__Pullover__Sequence__fini(common_msgs_humble__msg__Pullover__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      common_msgs_humble__msg__Pullover__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

common_msgs_humble__msg__Pullover__Sequence *
common_msgs_humble__msg__Pullover__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Pullover__Sequence * array = (common_msgs_humble__msg__Pullover__Sequence *)allocator.allocate(sizeof(common_msgs_humble__msg__Pullover__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = common_msgs_humble__msg__Pullover__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
common_msgs_humble__msg__Pullover__Sequence__destroy(common_msgs_humble__msg__Pullover__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    common_msgs_humble__msg__Pullover__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
common_msgs_humble__msg__Pullover__Sequence__are_equal(const common_msgs_humble__msg__Pullover__Sequence * lhs, const common_msgs_humble__msg__Pullover__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!common_msgs_humble__msg__Pullover__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
common_msgs_humble__msg__Pullover__Sequence__copy(
  const common_msgs_humble__msg__Pullover__Sequence * input,
  common_msgs_humble__msg__Pullover__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(common_msgs_humble__msg__Pullover);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    common_msgs_humble__msg__Pullover * data =
      (common_msgs_humble__msg__Pullover *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!common_msgs_humble__msg__Pullover__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          common_msgs_humble__msg__Pullover__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!common_msgs_humble__msg__Pullover__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
