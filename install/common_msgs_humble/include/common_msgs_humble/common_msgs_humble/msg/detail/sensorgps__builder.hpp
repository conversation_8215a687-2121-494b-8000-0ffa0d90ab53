// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Sensorgps.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORGPS__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORGPS__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/sensorgps__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Sensorgps_speed_d
{
public:
  explicit Init_Sensorgps_speed_d(::common_msgs_humble::msg::Sensorgps & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Sensorgps speed_d(::common_msgs_humble::msg::Sensorgps::_speed_d_type arg)
  {
    msg_.speed_d = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorgps msg_;
};

class Init_Sensorgps_speed_e
{
public:
  explicit Init_Sensorgps_speed_e(::common_msgs_humble::msg::Sensorgps & msg)
  : msg_(msg)
  {}
  Init_Sensorgps_speed_d speed_e(::common_msgs_humble::msg::Sensorgps::_speed_e_type arg)
  {
    msg_.speed_e = std::move(arg);
    return Init_Sensorgps_speed_d(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorgps msg_;
};

class Init_Sensorgps_speed_n
{
public:
  explicit Init_Sensorgps_speed_n(::common_msgs_humble::msg::Sensorgps & msg)
  : msg_(msg)
  {}
  Init_Sensorgps_speed_e speed_n(::common_msgs_humble::msg::Sensorgps::_speed_n_type arg)
  {
    msg_.speed_n = std::move(arg);
    return Init_Sensorgps_speed_e(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorgps msg_;
};

class Init_Sensorgps_timestamp
{
public:
  explicit Init_Sensorgps_timestamp(::common_msgs_humble::msg::Sensorgps & msg)
  : msg_(msg)
  {}
  Init_Sensorgps_speed_n timestamp(::common_msgs_humble::msg::Sensorgps::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return Init_Sensorgps_speed_n(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorgps msg_;
};

class Init_Sensorgps_isvalid
{
public:
  explicit Init_Sensorgps_isvalid(::common_msgs_humble::msg::Sensorgps & msg)
  : msg_(msg)
  {}
  Init_Sensorgps_timestamp isvalid(::common_msgs_humble::msg::Sensorgps::_isvalid_type arg)
  {
    msg_.isvalid = std::move(arg);
    return Init_Sensorgps_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorgps msg_;
};

class Init_Sensorgps_gpstime
{
public:
  explicit Init_Sensorgps_gpstime(::common_msgs_humble::msg::Sensorgps & msg)
  : msg_(msg)
  {}
  Init_Sensorgps_isvalid gpstime(::common_msgs_humble::msg::Sensorgps::_gpstime_type arg)
  {
    msg_.gpstime = std::move(arg);
    return Init_Sensorgps_isvalid(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorgps msg_;
};

class Init_Sensorgps_satenum
{
public:
  explicit Init_Sensorgps_satenum(::common_msgs_humble::msg::Sensorgps & msg)
  : msg_(msg)
  {}
  Init_Sensorgps_gpstime satenum(::common_msgs_humble::msg::Sensorgps::_satenum_type arg)
  {
    msg_.satenum = std::move(arg);
    return Init_Sensorgps_gpstime(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorgps msg_;
};

class Init_Sensorgps_rawstatus
{
public:
  explicit Init_Sensorgps_rawstatus(::common_msgs_humble::msg::Sensorgps & msg)
  : msg_(msg)
  {}
  Init_Sensorgps_satenum rawstatus(::common_msgs_humble::msg::Sensorgps::_rawstatus_type arg)
  {
    msg_.rawstatus = std::move(arg);
    return Init_Sensorgps_satenum(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorgps msg_;
};

class Init_Sensorgps_status
{
public:
  explicit Init_Sensorgps_status(::common_msgs_humble::msg::Sensorgps & msg)
  : msg_(msg)
  {}
  Init_Sensorgps_rawstatus status(::common_msgs_humble::msg::Sensorgps::_status_type arg)
  {
    msg_.status = std::move(arg);
    return Init_Sensorgps_rawstatus(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorgps msg_;
};

class Init_Sensorgps_velocity
{
public:
  explicit Init_Sensorgps_velocity(::common_msgs_humble::msg::Sensorgps & msg)
  : msg_(msg)
  {}
  Init_Sensorgps_status velocity(::common_msgs_humble::msg::Sensorgps::_velocity_type arg)
  {
    msg_.velocity = std::move(arg);
    return Init_Sensorgps_status(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorgps msg_;
};

class Init_Sensorgps_mile
{
public:
  explicit Init_Sensorgps_mile(::common_msgs_humble::msg::Sensorgps & msg)
  : msg_(msg)
  {}
  Init_Sensorgps_velocity mile(::common_msgs_humble::msg::Sensorgps::_mile_type arg)
  {
    msg_.mile = std::move(arg);
    return Init_Sensorgps_velocity(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorgps msg_;
};

class Init_Sensorgps_accz
{
public:
  explicit Init_Sensorgps_accz(::common_msgs_humble::msg::Sensorgps & msg)
  : msg_(msg)
  {}
  Init_Sensorgps_mile accz(::common_msgs_humble::msg::Sensorgps::_accz_type arg)
  {
    msg_.accz = std::move(arg);
    return Init_Sensorgps_mile(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorgps msg_;
};

class Init_Sensorgps_accy
{
public:
  explicit Init_Sensorgps_accy(::common_msgs_humble::msg::Sensorgps & msg)
  : msg_(msg)
  {}
  Init_Sensorgps_accz accy(::common_msgs_humble::msg::Sensorgps::_accy_type arg)
  {
    msg_.accy = std::move(arg);
    return Init_Sensorgps_accz(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorgps msg_;
};

class Init_Sensorgps_accx
{
public:
  explicit Init_Sensorgps_accx(::common_msgs_humble::msg::Sensorgps & msg)
  : msg_(msg)
  {}
  Init_Sensorgps_accy accx(::common_msgs_humble::msg::Sensorgps::_accx_type arg)
  {
    msg_.accx = std::move(arg);
    return Init_Sensorgps_accy(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorgps msg_;
};

class Init_Sensorgps_yawrate
{
public:
  explicit Init_Sensorgps_yawrate(::common_msgs_humble::msg::Sensorgps & msg)
  : msg_(msg)
  {}
  Init_Sensorgps_accx yawrate(::common_msgs_humble::msg::Sensorgps::_yawrate_type arg)
  {
    msg_.yawrate = std::move(arg);
    return Init_Sensorgps_accx(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorgps msg_;
};

class Init_Sensorgps_rollrate
{
public:
  explicit Init_Sensorgps_rollrate(::common_msgs_humble::msg::Sensorgps & msg)
  : msg_(msg)
  {}
  Init_Sensorgps_yawrate rollrate(::common_msgs_humble::msg::Sensorgps::_rollrate_type arg)
  {
    msg_.rollrate = std::move(arg);
    return Init_Sensorgps_yawrate(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorgps msg_;
};

class Init_Sensorgps_pitchrate
{
public:
  explicit Init_Sensorgps_pitchrate(::common_msgs_humble::msg::Sensorgps & msg)
  : msg_(msg)
  {}
  Init_Sensorgps_rollrate pitchrate(::common_msgs_humble::msg::Sensorgps::_pitchrate_type arg)
  {
    msg_.pitchrate = std::move(arg);
    return Init_Sensorgps_rollrate(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorgps msg_;
};

class Init_Sensorgps_roll
{
public:
  explicit Init_Sensorgps_roll(::common_msgs_humble::msg::Sensorgps & msg)
  : msg_(msg)
  {}
  Init_Sensorgps_pitchrate roll(::common_msgs_humble::msg::Sensorgps::_roll_type arg)
  {
    msg_.roll = std::move(arg);
    return Init_Sensorgps_pitchrate(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorgps msg_;
};

class Init_Sensorgps_pitch
{
public:
  explicit Init_Sensorgps_pitch(::common_msgs_humble::msg::Sensorgps & msg)
  : msg_(msg)
  {}
  Init_Sensorgps_roll pitch(::common_msgs_humble::msg::Sensorgps::_pitch_type arg)
  {
    msg_.pitch = std::move(arg);
    return Init_Sensorgps_roll(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorgps msg_;
};

class Init_Sensorgps_heading
{
public:
  explicit Init_Sensorgps_heading(::common_msgs_humble::msg::Sensorgps & msg)
  : msg_(msg)
  {}
  Init_Sensorgps_pitch heading(::common_msgs_humble::msg::Sensorgps::_heading_type arg)
  {
    msg_.heading = std::move(arg);
    return Init_Sensorgps_pitch(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorgps msg_;
};

class Init_Sensorgps_lanetype
{
public:
  explicit Init_Sensorgps_lanetype(::common_msgs_humble::msg::Sensorgps & msg)
  : msg_(msg)
  {}
  Init_Sensorgps_heading lanetype(::common_msgs_humble::msg::Sensorgps::_lanetype_type arg)
  {
    msg_.lanetype = std::move(arg);
    return Init_Sensorgps_heading(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorgps msg_;
};

class Init_Sensorgps_roadtype
{
public:
  explicit Init_Sensorgps_roadtype(::common_msgs_humble::msg::Sensorgps & msg)
  : msg_(msg)
  {}
  Init_Sensorgps_lanetype roadtype(::common_msgs_humble::msg::Sensorgps::_roadtype_type arg)
  {
    msg_.roadtype = std::move(arg);
    return Init_Sensorgps_lanetype(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorgps msg_;
};

class Init_Sensorgps_alt
{
public:
  explicit Init_Sensorgps_alt(::common_msgs_humble::msg::Sensorgps & msg)
  : msg_(msg)
  {}
  Init_Sensorgps_roadtype alt(::common_msgs_humble::msg::Sensorgps::_alt_type arg)
  {
    msg_.alt = std::move(arg);
    return Init_Sensorgps_roadtype(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorgps msg_;
};

class Init_Sensorgps_lat
{
public:
  explicit Init_Sensorgps_lat(::common_msgs_humble::msg::Sensorgps & msg)
  : msg_(msg)
  {}
  Init_Sensorgps_alt lat(::common_msgs_humble::msg::Sensorgps::_lat_type arg)
  {
    msg_.lat = std::move(arg);
    return Init_Sensorgps_alt(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorgps msg_;
};

class Init_Sensorgps_lon
{
public:
  Init_Sensorgps_lon()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Sensorgps_lat lon(::common_msgs_humble::msg::Sensorgps::_lon_type arg)
  {
    msg_.lon = std::move(arg);
    return Init_Sensorgps_lat(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorgps msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Sensorgps>()
{
  return common_msgs_humble::msg::builder::Init_Sensorgps_lon();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORGPS__BUILDER_HPP_
