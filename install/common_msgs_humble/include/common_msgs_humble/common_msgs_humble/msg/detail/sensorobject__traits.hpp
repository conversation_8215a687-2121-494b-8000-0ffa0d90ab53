// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/Sensorobject.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSOROBJECT__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSOROBJECT__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/sensorobject__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'points'
#include "common_msgs_humble/msg/detail/point3d__traits.hpp"
// Member 'object_history'
#include "common_msgs_humble/msg/detail/objecthistory__traits.hpp"
// Member 'object_prediction'
#include "common_msgs_humble/msg/detail/objectprediction__traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const Sensorobject & msg,
  std::ostream & out)
{
  out << "{";
  // member: id
  {
    out << "id: ";
    rosidl_generator_traits::value_to_yaml(msg.id, out);
    out << ", ";
  }

  // member: x
  {
    out << "x: ";
    rosidl_generator_traits::value_to_yaml(msg.x, out);
    out << ", ";
  }

  // member: y
  {
    out << "y: ";
    rosidl_generator_traits::value_to_yaml(msg.y, out);
    out << ", ";
  }

  // member: z
  {
    out << "z: ";
    rosidl_generator_traits::value_to_yaml(msg.z, out);
    out << ", ";
  }

  // member: longtitude
  {
    out << "longtitude: ";
    rosidl_generator_traits::value_to_yaml(msg.longtitude, out);
    out << ", ";
  }

  // member: latitude
  {
    out << "latitude: ";
    rosidl_generator_traits::value_to_yaml(msg.latitude, out);
    out << ", ";
  }

  // member: altitude
  {
    out << "altitude: ";
    rosidl_generator_traits::value_to_yaml(msg.altitude, out);
    out << ", ";
  }

  // member: relspeedy
  {
    out << "relspeedy: ";
    rosidl_generator_traits::value_to_yaml(msg.relspeedy, out);
    out << ", ";
  }

  // member: relspeedx
  {
    out << "relspeedx: ";
    rosidl_generator_traits::value_to_yaml(msg.relspeedx, out);
    out << ", ";
  }

  // member: rollrad
  {
    out << "rollrad: ";
    rosidl_generator_traits::value_to_yaml(msg.rollrad, out);
    out << ", ";
  }

  // member: pitchrad
  {
    out << "pitchrad: ";
    rosidl_generator_traits::value_to_yaml(msg.pitchrad, out);
    out << ", ";
  }

  // member: azimuth
  {
    out << "azimuth: ";
    rosidl_generator_traits::value_to_yaml(msg.azimuth, out);
    out << ", ";
  }

  // member: pitchrate
  {
    out << "pitchrate: ";
    rosidl_generator_traits::value_to_yaml(msg.pitchrate, out);
    out << ", ";
  }

  // member: rollrate
  {
    out << "rollrate: ";
    rosidl_generator_traits::value_to_yaml(msg.rollrate, out);
    out << ", ";
  }

  // member: yawrate
  {
    out << "yawrate: ";
    rosidl_generator_traits::value_to_yaml(msg.yawrate, out);
    out << ", ";
  }

  // member: width
  {
    out << "width: ";
    rosidl_generator_traits::value_to_yaml(msg.width, out);
    out << ", ";
  }

  // member: length
  {
    out << "length: ";
    rosidl_generator_traits::value_to_yaml(msg.length, out);
    out << ", ";
  }

  // member: height
  {
    out << "height: ";
    rosidl_generator_traits::value_to_yaml(msg.height, out);
    out << ", ";
  }

  // member: classification
  {
    out << "classification: ";
    rosidl_generator_traits::value_to_yaml(msg.classification, out);
    out << ", ";
  }

  // member: value
  {
    out << "value: ";
    rosidl_generator_traits::value_to_yaml(msg.value, out);
    out << ", ";
  }

  // member: confidence
  {
    out << "confidence: ";
    rosidl_generator_traits::value_to_yaml(msg.confidence, out);
    out << ", ";
  }

  // member: points
  {
    if (msg.points.size() == 0) {
      out << "points: []";
    } else {
      out << "points: [";
      size_t pending_items = msg.points.size();
      for (auto item : msg.points) {
        to_flow_style_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
    out << ", ";
  }

  // member: driving_intent
  {
    out << "driving_intent: ";
    rosidl_generator_traits::value_to_yaml(msg.driving_intent, out);
    out << ", ";
  }

  // member: behavior_state
  {
    out << "behavior_state: ";
    rosidl_generator_traits::value_to_yaml(msg.behavior_state, out);
    out << ", ";
  }

  // member: radarindex
  {
    out << "radarindex: ";
    rosidl_generator_traits::value_to_yaml(msg.radarindex, out);
    out << ", ";
  }

  // member: radarobjectid
  {
    out << "radarobjectid: ";
    rosidl_generator_traits::value_to_yaml(msg.radarobjectid, out);
    out << ", ";
  }

  // member: s
  {
    out << "s: ";
    rosidl_generator_traits::value_to_yaml(msg.s, out);
    out << ", ";
  }

  // member: l
  {
    out << "l: ";
    rosidl_generator_traits::value_to_yaml(msg.l, out);
    out << ", ";
  }

  // member: speeds
  {
    out << "speeds: ";
    rosidl_generator_traits::value_to_yaml(msg.speeds, out);
    out << ", ";
  }

  // member: speedl
  {
    out << "speedl: ";
    rosidl_generator_traits::value_to_yaml(msg.speedl, out);
    out << ", ";
  }

  // member: object_decision
  {
    out << "object_decision: ";
    rosidl_generator_traits::value_to_yaml(msg.object_decision, out);
    out << ", ";
  }

  // member: object_history
  {
    if (msg.object_history.size() == 0) {
      out << "object_history: []";
    } else {
      out << "object_history: [";
      size_t pending_items = msg.object_history.size();
      for (auto item : msg.object_history) {
        to_flow_style_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
    out << ", ";
  }

  // member: object_prediction
  {
    if (msg.object_prediction.size() == 0) {
      out << "object_prediction: []";
    } else {
      out << "object_prediction: [";
      size_t pending_items = msg.object_prediction.size();
      for (auto item : msg.object_prediction) {
        to_flow_style_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Sensorobject & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: id
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "id: ";
    rosidl_generator_traits::value_to_yaml(msg.id, out);
    out << "\n";
  }

  // member: x
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "x: ";
    rosidl_generator_traits::value_to_yaml(msg.x, out);
    out << "\n";
  }

  // member: y
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "y: ";
    rosidl_generator_traits::value_to_yaml(msg.y, out);
    out << "\n";
  }

  // member: z
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "z: ";
    rosidl_generator_traits::value_to_yaml(msg.z, out);
    out << "\n";
  }

  // member: longtitude
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "longtitude: ";
    rosidl_generator_traits::value_to_yaml(msg.longtitude, out);
    out << "\n";
  }

  // member: latitude
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "latitude: ";
    rosidl_generator_traits::value_to_yaml(msg.latitude, out);
    out << "\n";
  }

  // member: altitude
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "altitude: ";
    rosidl_generator_traits::value_to_yaml(msg.altitude, out);
    out << "\n";
  }

  // member: relspeedy
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "relspeedy: ";
    rosidl_generator_traits::value_to_yaml(msg.relspeedy, out);
    out << "\n";
  }

  // member: relspeedx
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "relspeedx: ";
    rosidl_generator_traits::value_to_yaml(msg.relspeedx, out);
    out << "\n";
  }

  // member: rollrad
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "rollrad: ";
    rosidl_generator_traits::value_to_yaml(msg.rollrad, out);
    out << "\n";
  }

  // member: pitchrad
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "pitchrad: ";
    rosidl_generator_traits::value_to_yaml(msg.pitchrad, out);
    out << "\n";
  }

  // member: azimuth
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "azimuth: ";
    rosidl_generator_traits::value_to_yaml(msg.azimuth, out);
    out << "\n";
  }

  // member: pitchrate
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "pitchrate: ";
    rosidl_generator_traits::value_to_yaml(msg.pitchrate, out);
    out << "\n";
  }

  // member: rollrate
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "rollrate: ";
    rosidl_generator_traits::value_to_yaml(msg.rollrate, out);
    out << "\n";
  }

  // member: yawrate
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "yawrate: ";
    rosidl_generator_traits::value_to_yaml(msg.yawrate, out);
    out << "\n";
  }

  // member: width
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "width: ";
    rosidl_generator_traits::value_to_yaml(msg.width, out);
    out << "\n";
  }

  // member: length
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "length: ";
    rosidl_generator_traits::value_to_yaml(msg.length, out);
    out << "\n";
  }

  // member: height
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "height: ";
    rosidl_generator_traits::value_to_yaml(msg.height, out);
    out << "\n";
  }

  // member: classification
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "classification: ";
    rosidl_generator_traits::value_to_yaml(msg.classification, out);
    out << "\n";
  }

  // member: value
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "value: ";
    rosidl_generator_traits::value_to_yaml(msg.value, out);
    out << "\n";
  }

  // member: confidence
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "confidence: ";
    rosidl_generator_traits::value_to_yaml(msg.confidence, out);
    out << "\n";
  }

  // member: points
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.points.size() == 0) {
      out << "points: []\n";
    } else {
      out << "points:\n";
      for (auto item : msg.points) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "-\n";
        to_block_style_yaml(item, out, indentation + 2);
      }
    }
  }

  // member: driving_intent
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "driving_intent: ";
    rosidl_generator_traits::value_to_yaml(msg.driving_intent, out);
    out << "\n";
  }

  // member: behavior_state
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "behavior_state: ";
    rosidl_generator_traits::value_to_yaml(msg.behavior_state, out);
    out << "\n";
  }

  // member: radarindex
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "radarindex: ";
    rosidl_generator_traits::value_to_yaml(msg.radarindex, out);
    out << "\n";
  }

  // member: radarobjectid
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "radarobjectid: ";
    rosidl_generator_traits::value_to_yaml(msg.radarobjectid, out);
    out << "\n";
  }

  // member: s
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "s: ";
    rosidl_generator_traits::value_to_yaml(msg.s, out);
    out << "\n";
  }

  // member: l
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "l: ";
    rosidl_generator_traits::value_to_yaml(msg.l, out);
    out << "\n";
  }

  // member: speeds
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "speeds: ";
    rosidl_generator_traits::value_to_yaml(msg.speeds, out);
    out << "\n";
  }

  // member: speedl
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "speedl: ";
    rosidl_generator_traits::value_to_yaml(msg.speedl, out);
    out << "\n";
  }

  // member: object_decision
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "object_decision: ";
    rosidl_generator_traits::value_to_yaml(msg.object_decision, out);
    out << "\n";
  }

  // member: object_history
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.object_history.size() == 0) {
      out << "object_history: []\n";
    } else {
      out << "object_history:\n";
      for (auto item : msg.object_history) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "-\n";
        to_block_style_yaml(item, out, indentation + 2);
      }
    }
  }

  // member: object_prediction
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.object_prediction.size() == 0) {
      out << "object_prediction: []\n";
    } else {
      out << "object_prediction:\n";
      for (auto item : msg.object_prediction) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "-\n";
        to_block_style_yaml(item, out, indentation + 2);
      }
    }
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Sensorobject & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::Sensorobject & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::Sensorobject & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::Sensorobject>()
{
  return "common_msgs_humble::msg::Sensorobject";
}

template<>
inline const char * name<common_msgs_humble::msg::Sensorobject>()
{
  return "common_msgs_humble/msg/Sensorobject";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::Sensorobject>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::Sensorobject>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<common_msgs_humble::msg::Sensorobject>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSOROBJECT__TRAITS_HPP_
