// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Rdcontrol.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__RDCONTROL__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__RDCONTROL__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Rdcontrol __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Rdcontrol __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Rdcontrol_
{
  using Type = Rdcontrol_<ContainerAllocator>;

  explicit Rdcontrol_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->timestamp = 0ll;
      this->drivemode = 0;
      this->drivestate = 0;
      this->angle = 0.0f;
      this->gas = 0.0f;
      this->brake = 0.0f;
      this->turnlignt = 0;
      this->gear = 0;
      this->epb = 0;
    }
  }

  explicit Rdcontrol_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_alloc, _init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->timestamp = 0ll;
      this->drivemode = 0;
      this->drivestate = 0;
      this->angle = 0.0f;
      this->gas = 0.0f;
      this->brake = 0.0f;
      this->turnlignt = 0;
      this->gear = 0;
      this->epb = 0;
    }
  }

  // field types and members
  using _header_type =
    std_msgs::msg::Header_<ContainerAllocator>;
  _header_type header;
  using _timestamp_type =
    int64_t;
  _timestamp_type timestamp;
  using _drivemode_type =
    uint8_t;
  _drivemode_type drivemode;
  using _drivestate_type =
    uint8_t;
  _drivestate_type drivestate;
  using _angle_type =
    float;
  _angle_type angle;
  using _gas_type =
    float;
  _gas_type gas;
  using _brake_type =
    float;
  _brake_type brake;
  using _turnlignt_type =
    uint8_t;
  _turnlignt_type turnlignt;
  using _gear_type =
    uint8_t;
  _gear_type gear;
  using _epb_type =
    uint8_t;
  _epb_type epb;

  // setters for named parameter idiom
  Type & set__header(
    const std_msgs::msg::Header_<ContainerAllocator> & _arg)
  {
    this->header = _arg;
    return *this;
  }
  Type & set__timestamp(
    const int64_t & _arg)
  {
    this->timestamp = _arg;
    return *this;
  }
  Type & set__drivemode(
    const uint8_t & _arg)
  {
    this->drivemode = _arg;
    return *this;
  }
  Type & set__drivestate(
    const uint8_t & _arg)
  {
    this->drivestate = _arg;
    return *this;
  }
  Type & set__angle(
    const float & _arg)
  {
    this->angle = _arg;
    return *this;
  }
  Type & set__gas(
    const float & _arg)
  {
    this->gas = _arg;
    return *this;
  }
  Type & set__brake(
    const float & _arg)
  {
    this->brake = _arg;
    return *this;
  }
  Type & set__turnlignt(
    const uint8_t & _arg)
  {
    this->turnlignt = _arg;
    return *this;
  }
  Type & set__gear(
    const uint8_t & _arg)
  {
    this->gear = _arg;
    return *this;
  }
  Type & set__epb(
    const uint8_t & _arg)
  {
    this->epb = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Rdcontrol_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Rdcontrol_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Rdcontrol_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Rdcontrol_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Rdcontrol_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Rdcontrol_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Rdcontrol_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Rdcontrol_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Rdcontrol_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Rdcontrol_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Rdcontrol
    std::shared_ptr<common_msgs_humble::msg::Rdcontrol_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Rdcontrol
    std::shared_ptr<common_msgs_humble::msg::Rdcontrol_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Rdcontrol_ & other) const
  {
    if (this->header != other.header) {
      return false;
    }
    if (this->timestamp != other.timestamp) {
      return false;
    }
    if (this->drivemode != other.drivemode) {
      return false;
    }
    if (this->drivestate != other.drivestate) {
      return false;
    }
    if (this->angle != other.angle) {
      return false;
    }
    if (this->gas != other.gas) {
      return false;
    }
    if (this->brake != other.brake) {
      return false;
    }
    if (this->turnlignt != other.turnlignt) {
      return false;
    }
    if (this->gear != other.gear) {
      return false;
    }
    if (this->epb != other.epb) {
      return false;
    }
    return true;
  }
  bool operator!=(const Rdcontrol_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Rdcontrol_

// alias to use template instance with default allocator
using Rdcontrol =
  common_msgs_humble::msg::Rdcontrol_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__RDCONTROL__STRUCT_HPP_
