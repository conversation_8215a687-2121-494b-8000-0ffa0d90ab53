// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from common_msgs_humble:msg/Pointformat.idl
// generated code does not contain a copyright notice
#include "common_msgs_humble/msg/detail/pointformat__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


// Include directives for member types
// Member `path`
#include "rosidl_runtime_c/string_functions.h"

bool
common_msgs_humble__msg__Pointformat__init(common_msgs_humble__msg__Pointformat * msg)
{
  if (!msg) {
    return false;
  }
  // lon
  // lat
  // heading
  // index
  // backup1
  // backup2
  // backup3
  // backup4
  // backup5
  // backup6
  // backup7
  // backup8
  // backup9
  // path
  if (!rosidl_runtime_c__String__init(&msg->path)) {
    common_msgs_humble__msg__Pointformat__fini(msg);
    return false;
  }
  return true;
}

void
common_msgs_humble__msg__Pointformat__fini(common_msgs_humble__msg__Pointformat * msg)
{
  if (!msg) {
    return;
  }
  // lon
  // lat
  // heading
  // index
  // backup1
  // backup2
  // backup3
  // backup4
  // backup5
  // backup6
  // backup7
  // backup8
  // backup9
  // path
  rosidl_runtime_c__String__fini(&msg->path);
}

bool
common_msgs_humble__msg__Pointformat__are_equal(const common_msgs_humble__msg__Pointformat * lhs, const common_msgs_humble__msg__Pointformat * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // lon
  if (lhs->lon != rhs->lon) {
    return false;
  }
  // lat
  if (lhs->lat != rhs->lat) {
    return false;
  }
  // heading
  if (lhs->heading != rhs->heading) {
    return false;
  }
  // index
  if (lhs->index != rhs->index) {
    return false;
  }
  // backup1
  if (lhs->backup1 != rhs->backup1) {
    return false;
  }
  // backup2
  if (lhs->backup2 != rhs->backup2) {
    return false;
  }
  // backup3
  if (lhs->backup3 != rhs->backup3) {
    return false;
  }
  // backup4
  if (lhs->backup4 != rhs->backup4) {
    return false;
  }
  // backup5
  if (lhs->backup5 != rhs->backup5) {
    return false;
  }
  // backup6
  if (lhs->backup6 != rhs->backup6) {
    return false;
  }
  // backup7
  if (lhs->backup7 != rhs->backup7) {
    return false;
  }
  // backup8
  if (lhs->backup8 != rhs->backup8) {
    return false;
  }
  // backup9
  if (lhs->backup9 != rhs->backup9) {
    return false;
  }
  // path
  if (!rosidl_runtime_c__String__are_equal(
      &(lhs->path), &(rhs->path)))
  {
    return false;
  }
  return true;
}

bool
common_msgs_humble__msg__Pointformat__copy(
  const common_msgs_humble__msg__Pointformat * input,
  common_msgs_humble__msg__Pointformat * output)
{
  if (!input || !output) {
    return false;
  }
  // lon
  output->lon = input->lon;
  // lat
  output->lat = input->lat;
  // heading
  output->heading = input->heading;
  // index
  output->index = input->index;
  // backup1
  output->backup1 = input->backup1;
  // backup2
  output->backup2 = input->backup2;
  // backup3
  output->backup3 = input->backup3;
  // backup4
  output->backup4 = input->backup4;
  // backup5
  output->backup5 = input->backup5;
  // backup6
  output->backup6 = input->backup6;
  // backup7
  output->backup7 = input->backup7;
  // backup8
  output->backup8 = input->backup8;
  // backup9
  output->backup9 = input->backup9;
  // path
  if (!rosidl_runtime_c__String__copy(
      &(input->path), &(output->path)))
  {
    return false;
  }
  return true;
}

common_msgs_humble__msg__Pointformat *
common_msgs_humble__msg__Pointformat__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Pointformat * msg = (common_msgs_humble__msg__Pointformat *)allocator.allocate(sizeof(common_msgs_humble__msg__Pointformat), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(common_msgs_humble__msg__Pointformat));
  bool success = common_msgs_humble__msg__Pointformat__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
common_msgs_humble__msg__Pointformat__destroy(common_msgs_humble__msg__Pointformat * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    common_msgs_humble__msg__Pointformat__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
common_msgs_humble__msg__Pointformat__Sequence__init(common_msgs_humble__msg__Pointformat__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Pointformat * data = NULL;

  if (size) {
    data = (common_msgs_humble__msg__Pointformat *)allocator.zero_allocate(size, sizeof(common_msgs_humble__msg__Pointformat), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = common_msgs_humble__msg__Pointformat__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        common_msgs_humble__msg__Pointformat__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
common_msgs_humble__msg__Pointformat__Sequence__fini(common_msgs_humble__msg__Pointformat__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      common_msgs_humble__msg__Pointformat__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

common_msgs_humble__msg__Pointformat__Sequence *
common_msgs_humble__msg__Pointformat__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Pointformat__Sequence * array = (common_msgs_humble__msg__Pointformat__Sequence *)allocator.allocate(sizeof(common_msgs_humble__msg__Pointformat__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = common_msgs_humble__msg__Pointformat__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
common_msgs_humble__msg__Pointformat__Sequence__destroy(common_msgs_humble__msg__Pointformat__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    common_msgs_humble__msg__Pointformat__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
common_msgs_humble__msg__Pointformat__Sequence__are_equal(const common_msgs_humble__msg__Pointformat__Sequence * lhs, const common_msgs_humble__msg__Pointformat__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!common_msgs_humble__msg__Pointformat__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
common_msgs_humble__msg__Pointformat__Sequence__copy(
  const common_msgs_humble__msg__Pointformat__Sequence * input,
  common_msgs_humble__msg__Pointformat__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(common_msgs_humble__msg__Pointformat);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    common_msgs_humble__msg__Pointformat * data =
      (common_msgs_humble__msg__Pointformat *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!common_msgs_humble__msg__Pointformat__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          common_msgs_humble__msg__Pointformat__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!common_msgs_humble__msg__Pointformat__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
