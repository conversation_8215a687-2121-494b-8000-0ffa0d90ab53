// generated from rosidl_typesupport_introspection_c/resource/idl__type_support.c.em
// with input from common_msgs_humble:msg/Trajectorypoints.idl
// generated code does not contain a copyright notice

#include <stddef.h>
#include "common_msgs_humble/msg/detail/trajectorypoints__rosidl_typesupport_introspection_c.h"
#include "common_msgs_humble/msg/rosidl_typesupport_introspection_c__visibility_control.h"
#include "rosidl_typesupport_introspection_c/field_types.h"
#include "rosidl_typesupport_introspection_c/identifier.h"
#include "rosidl_typesupport_introspection_c/message_introspection.h"
#include "common_msgs_humble/msg/detail/trajectorypoints__functions.h"
#include "common_msgs_humble/msg/detail/trajectorypoints__struct.h"


// Include directives for member types
// Member `points`
#include "common_msgs_humble/msg/roadpoint.h"
// Member `points`
#include "common_msgs_humble/msg/detail/roadpoint__rosidl_typesupport_introspection_c.h"

#ifdef __cplusplus
extern "C"
{
#endif

void common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__Trajectorypoints_init_function(
  void * message_memory, enum rosidl_runtime_c__message_initialization _init)
{
  // TODO(karsten1987): initializers are not yet implemented for typesupport c
  // see https://bgithub.xyz/ros2/ros2/issues/397
  (void) _init;
  common_msgs_humble__msg__Trajectorypoints__init(message_memory);
}

void common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__Trajectorypoints_fini_function(void * message_memory)
{
  common_msgs_humble__msg__Trajectorypoints__fini(message_memory);
}

size_t common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__size_function__Trajectorypoints__points(
  const void * untyped_member)
{
  const common_msgs_humble__msg__Roadpoint__Sequence * member =
    (const common_msgs_humble__msg__Roadpoint__Sequence *)(untyped_member);
  return member->size;
}

const void * common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__get_const_function__Trajectorypoints__points(
  const void * untyped_member, size_t index)
{
  const common_msgs_humble__msg__Roadpoint__Sequence * member =
    (const common_msgs_humble__msg__Roadpoint__Sequence *)(untyped_member);
  return &member->data[index];
}

void * common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__get_function__Trajectorypoints__points(
  void * untyped_member, size_t index)
{
  common_msgs_humble__msg__Roadpoint__Sequence * member =
    (common_msgs_humble__msg__Roadpoint__Sequence *)(untyped_member);
  return &member->data[index];
}

void common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__fetch_function__Trajectorypoints__points(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const common_msgs_humble__msg__Roadpoint * item =
    ((const common_msgs_humble__msg__Roadpoint *)
    common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__get_const_function__Trajectorypoints__points(untyped_member, index));
  common_msgs_humble__msg__Roadpoint * value =
    (common_msgs_humble__msg__Roadpoint *)(untyped_value);
  *value = *item;
}

void common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__assign_function__Trajectorypoints__points(
  void * untyped_member, size_t index, const void * untyped_value)
{
  common_msgs_humble__msg__Roadpoint * item =
    ((common_msgs_humble__msg__Roadpoint *)
    common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__get_function__Trajectorypoints__points(untyped_member, index));
  const common_msgs_humble__msg__Roadpoint * value =
    (const common_msgs_humble__msg__Roadpoint *)(untyped_value);
  *item = *value;
}

bool common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__resize_function__Trajectorypoints__points(
  void * untyped_member, size_t size)
{
  common_msgs_humble__msg__Roadpoint__Sequence * member =
    (common_msgs_humble__msg__Roadpoint__Sequence *)(untyped_member);
  common_msgs_humble__msg__Roadpoint__Sequence__fini(member);
  return common_msgs_humble__msg__Roadpoint__Sequence__init(member, size);
}

static rosidl_typesupport_introspection_c__MessageMember common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__Trajectorypoints_message_member_array[6] = {
  {
    "points",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message (initialized later)
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Trajectorypoints, points),  // bytes offset in struct
    NULL,  // default value
    common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__size_function__Trajectorypoints__points,  // size() function pointer
    common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__get_const_function__Trajectorypoints__points,  // get_const(index) function pointer
    common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__get_function__Trajectorypoints__points,  // get(index) function pointer
    common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__fetch_function__Trajectorypoints__points,  // fetch(index, &value) function pointer
    common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__assign_function__Trajectorypoints__points,  // assign(index, value) function pointer
    common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__resize_function__Trajectorypoints__points  // resize(index) function pointer
  },
  {
    "source",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT8,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Trajectorypoints, source),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "isvalid",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT8,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Trajectorypoints, isvalid),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "backpark",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT8,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Trajectorypoints, backpark),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "gpstime",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_INT64,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Trajectorypoints, gpstime),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "timestamp",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_INT64,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Trajectorypoints, timestamp),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  }
};

static const rosidl_typesupport_introspection_c__MessageMembers common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__Trajectorypoints_message_members = {
  "common_msgs_humble__msg",  // message namespace
  "Trajectorypoints",  // message name
  6,  // number of fields
  sizeof(common_msgs_humble__msg__Trajectorypoints),
  common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__Trajectorypoints_message_member_array,  // message members
  common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__Trajectorypoints_init_function,  // function to initialize message memory (memory has to be allocated)
  common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__Trajectorypoints_fini_function  // function to terminate message instance (will not free memory)
};

// this is not const since it must be initialized on first access
// since C does not allow non-integral compile-time constants
static rosidl_message_type_support_t common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__Trajectorypoints_message_type_support_handle = {
  0,
  &common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__Trajectorypoints_message_members,
  get_message_typesupport_handle_function,
};

ROSIDL_TYPESUPPORT_INTROSPECTION_C_EXPORT_common_msgs_humble
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, common_msgs_humble, msg, Trajectorypoints)() {
  common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__Trajectorypoints_message_member_array[0].members_ =
    ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, common_msgs_humble, msg, Roadpoint)();
  if (!common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__Trajectorypoints_message_type_support_handle.typesupport_identifier) {
    common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__Trajectorypoints_message_type_support_handle.typesupport_identifier =
      rosidl_typesupport_introspection_c__identifier;
  }
  return &common_msgs_humble__msg__Trajectorypoints__rosidl_typesupport_introspection_c__Trajectorypoints_message_type_support_handle;
}
#ifdef __cplusplus
}
#endif
