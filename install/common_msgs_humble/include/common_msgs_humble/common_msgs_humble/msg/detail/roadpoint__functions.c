// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from common_msgs_humble:msg/Roadpoint.idl
// generated code does not contain a copyright notice
#include "common_msgs_humble/msg/detail/roadpoint__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


bool
common_msgs_humble__msg__Roadpoint__init(common_msgs_humble__msg__Roadpoint * msg)
{
  if (!msg) {
    return false;
  }
  // x
  // y
  // gx
  // gy
  // roadtype
  // speed
  // a
  // jerk
  // lanetype
  // turnlight
  // mergelanetype
  // sensorlanetype
  // heading
  // curvature
  // dkappa
  // ddkappa
  // leftsearchdis
  // rightsearchdis
  // s
  // sideroadwidth
  // lanewidth
  // leftlanewidth
  // rightlanewidth
  // relativetime
  // laneswitch
  // laneborrow
  // lanenum
  // lanesite
  return true;
}

void
common_msgs_humble__msg__Roadpoint__fini(common_msgs_humble__msg__Roadpoint * msg)
{
  if (!msg) {
    return;
  }
  // x
  // y
  // gx
  // gy
  // roadtype
  // speed
  // a
  // jerk
  // lanetype
  // turnlight
  // mergelanetype
  // sensorlanetype
  // heading
  // curvature
  // dkappa
  // ddkappa
  // leftsearchdis
  // rightsearchdis
  // s
  // sideroadwidth
  // lanewidth
  // leftlanewidth
  // rightlanewidth
  // relativetime
  // laneswitch
  // laneborrow
  // lanenum
  // lanesite
}

bool
common_msgs_humble__msg__Roadpoint__are_equal(const common_msgs_humble__msg__Roadpoint * lhs, const common_msgs_humble__msg__Roadpoint * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // x
  if (lhs->x != rhs->x) {
    return false;
  }
  // y
  if (lhs->y != rhs->y) {
    return false;
  }
  // gx
  if (lhs->gx != rhs->gx) {
    return false;
  }
  // gy
  if (lhs->gy != rhs->gy) {
    return false;
  }
  // roadtype
  if (lhs->roadtype != rhs->roadtype) {
    return false;
  }
  // speed
  if (lhs->speed != rhs->speed) {
    return false;
  }
  // a
  if (lhs->a != rhs->a) {
    return false;
  }
  // jerk
  if (lhs->jerk != rhs->jerk) {
    return false;
  }
  // lanetype
  if (lhs->lanetype != rhs->lanetype) {
    return false;
  }
  // turnlight
  if (lhs->turnlight != rhs->turnlight) {
    return false;
  }
  // mergelanetype
  if (lhs->mergelanetype != rhs->mergelanetype) {
    return false;
  }
  // sensorlanetype
  if (lhs->sensorlanetype != rhs->sensorlanetype) {
    return false;
  }
  // heading
  if (lhs->heading != rhs->heading) {
    return false;
  }
  // curvature
  if (lhs->curvature != rhs->curvature) {
    return false;
  }
  // dkappa
  if (lhs->dkappa != rhs->dkappa) {
    return false;
  }
  // ddkappa
  if (lhs->ddkappa != rhs->ddkappa) {
    return false;
  }
  // leftsearchdis
  if (lhs->leftsearchdis != rhs->leftsearchdis) {
    return false;
  }
  // rightsearchdis
  if (lhs->rightsearchdis != rhs->rightsearchdis) {
    return false;
  }
  // s
  if (lhs->s != rhs->s) {
    return false;
  }
  // sideroadwidth
  if (lhs->sideroadwidth != rhs->sideroadwidth) {
    return false;
  }
  // lanewidth
  if (lhs->lanewidth != rhs->lanewidth) {
    return false;
  }
  // leftlanewidth
  if (lhs->leftlanewidth != rhs->leftlanewidth) {
    return false;
  }
  // rightlanewidth
  if (lhs->rightlanewidth != rhs->rightlanewidth) {
    return false;
  }
  // relativetime
  if (lhs->relativetime != rhs->relativetime) {
    return false;
  }
  // laneswitch
  if (lhs->laneswitch != rhs->laneswitch) {
    return false;
  }
  // laneborrow
  if (lhs->laneborrow != rhs->laneborrow) {
    return false;
  }
  // lanenum
  if (lhs->lanenum != rhs->lanenum) {
    return false;
  }
  // lanesite
  if (lhs->lanesite != rhs->lanesite) {
    return false;
  }
  return true;
}

bool
common_msgs_humble__msg__Roadpoint__copy(
  const common_msgs_humble__msg__Roadpoint * input,
  common_msgs_humble__msg__Roadpoint * output)
{
  if (!input || !output) {
    return false;
  }
  // x
  output->x = input->x;
  // y
  output->y = input->y;
  // gx
  output->gx = input->gx;
  // gy
  output->gy = input->gy;
  // roadtype
  output->roadtype = input->roadtype;
  // speed
  output->speed = input->speed;
  // a
  output->a = input->a;
  // jerk
  output->jerk = input->jerk;
  // lanetype
  output->lanetype = input->lanetype;
  // turnlight
  output->turnlight = input->turnlight;
  // mergelanetype
  output->mergelanetype = input->mergelanetype;
  // sensorlanetype
  output->sensorlanetype = input->sensorlanetype;
  // heading
  output->heading = input->heading;
  // curvature
  output->curvature = input->curvature;
  // dkappa
  output->dkappa = input->dkappa;
  // ddkappa
  output->ddkappa = input->ddkappa;
  // leftsearchdis
  output->leftsearchdis = input->leftsearchdis;
  // rightsearchdis
  output->rightsearchdis = input->rightsearchdis;
  // s
  output->s = input->s;
  // sideroadwidth
  output->sideroadwidth = input->sideroadwidth;
  // lanewidth
  output->lanewidth = input->lanewidth;
  // leftlanewidth
  output->leftlanewidth = input->leftlanewidth;
  // rightlanewidth
  output->rightlanewidth = input->rightlanewidth;
  // relativetime
  output->relativetime = input->relativetime;
  // laneswitch
  output->laneswitch = input->laneswitch;
  // laneborrow
  output->laneborrow = input->laneborrow;
  // lanenum
  output->lanenum = input->lanenum;
  // lanesite
  output->lanesite = input->lanesite;
  return true;
}

common_msgs_humble__msg__Roadpoint *
common_msgs_humble__msg__Roadpoint__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Roadpoint * msg = (common_msgs_humble__msg__Roadpoint *)allocator.allocate(sizeof(common_msgs_humble__msg__Roadpoint), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(common_msgs_humble__msg__Roadpoint));
  bool success = common_msgs_humble__msg__Roadpoint__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
common_msgs_humble__msg__Roadpoint__destroy(common_msgs_humble__msg__Roadpoint * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    common_msgs_humble__msg__Roadpoint__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
common_msgs_humble__msg__Roadpoint__Sequence__init(common_msgs_humble__msg__Roadpoint__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Roadpoint * data = NULL;

  if (size) {
    data = (common_msgs_humble__msg__Roadpoint *)allocator.zero_allocate(size, sizeof(common_msgs_humble__msg__Roadpoint), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = common_msgs_humble__msg__Roadpoint__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        common_msgs_humble__msg__Roadpoint__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
common_msgs_humble__msg__Roadpoint__Sequence__fini(common_msgs_humble__msg__Roadpoint__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      common_msgs_humble__msg__Roadpoint__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

common_msgs_humble__msg__Roadpoint__Sequence *
common_msgs_humble__msg__Roadpoint__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Roadpoint__Sequence * array = (common_msgs_humble__msg__Roadpoint__Sequence *)allocator.allocate(sizeof(common_msgs_humble__msg__Roadpoint__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = common_msgs_humble__msg__Roadpoint__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
common_msgs_humble__msg__Roadpoint__Sequence__destroy(common_msgs_humble__msg__Roadpoint__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    common_msgs_humble__msg__Roadpoint__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
common_msgs_humble__msg__Roadpoint__Sequence__are_equal(const common_msgs_humble__msg__Roadpoint__Sequence * lhs, const common_msgs_humble__msg__Roadpoint__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!common_msgs_humble__msg__Roadpoint__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
common_msgs_humble__msg__Roadpoint__Sequence__copy(
  const common_msgs_humble__msg__Roadpoint__Sequence * input,
  common_msgs_humble__msg__Roadpoint__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(common_msgs_humble__msg__Roadpoint);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    common_msgs_humble__msg__Roadpoint * data =
      (common_msgs_humble__msg__Roadpoint *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!common_msgs_humble__msg__Roadpoint__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          common_msgs_humble__msg__Roadpoint__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!common_msgs_humble__msg__Roadpoint__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
