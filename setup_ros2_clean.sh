#!/bin/bash

# Ubuntu 20.04 + ROS1 Noetic + ROS2 Humble 混合环境解决方案
# 问题：ROS2 setup.bash 会检测到 ROS1 并设置 ROS_ROOT=/opt/ros/noetic/share/ros

echo "=== ROS1/ROS2 混合环境问题解决脚本 ==="
echo "问题：source ~/ros2_humble/install/setup.bash 后出现 ROS_ROOT=/opt/ros/noetic/share/ros"
echo ""

# 方法1：临时隐藏 ROS1 安装目录
echo "=== 方法1：临时隐藏 ROS1 安装 ==="
if [ -d "/opt/ros/noetic" ]; then
    echo "检测到 ROS1 Noetic 安装在 /opt/ros/noetic"
    echo "临时重命名以避免 ROS2 检测..."
    sudo mv /opt/ros/noetic /opt/ros/noetic.backup 2>/dev/null || echo "需要 sudo 权限来重命名 ROS1 目录"
fi

# 清理所有 ROS 环境变量
echo ""
echo "=== 清理环境变量 ==="
unset ROS_ROOT ROS_DISTRO LD_LIBRARY_PATH ROS_PACKAGE_PATH ROSLISP_PACKAGE_DIRECTORIES
unset ROS_ETC_DIR ROS_MASTER_URI ROS_VERSION ROS_PYTHON_VERSION ROS_LOCALHOST_ONLY
unset PYTHONPATH CMAKE_PREFIX_PATH AMENT_PREFIX_PATH PKG_CONFIG_PATH ROS_IP ROS_HOSTNAME

# 设置干净的系统路径
export PATH="/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"

echo "环境变量清理完成"
env | grep -i ros || echo "✓ 确认没有 ROS 相关环境变量"

echo ""
echo "=== 设置 ROS2 环境 ==="
source ~/ros2_humble/install/setup.bash

echo "ROS2 环境设置结果："
echo "  ROS_DISTRO: $ROS_DISTRO"
echo "  ROS_VERSION: $ROS_VERSION"
echo "  ROS_ROOT: ${ROS_ROOT:-未设置}"

# 恢复 ROS1 目录
if [ -d "/opt/ros/noetic.backup" ]; then
    echo ""
    echo "=== 恢复 ROS1 安装 ==="
    sudo mv /opt/ros/noetic.backup /opt/ros/noetic 2>/dev/null || echo "恢复 ROS1 目录需要 sudo 权限"
fi

echo ""
echo "=== 设置工作空间 ==="
cd ~/Code/autodrivingVersionTest
source install/setup.bash

echo "工作空间设置完成"
echo "当前目录: $(pwd)"

echo ""
echo "=== 运行 autoalign ==="
if [ -f "install/autoalign/lib/autoalign/autoalign" ]; then
    echo "✓ 找到 autoalign 可执行文件"
    echo "正在启动..."
    ./install/autoalign/lib/autoalign/autoalign
else
    echo "✗ 未找到 autoalign 可执行文件"
    echo "请先编译 autoalign 包：colcon build --packages-select autoalign"
fi
