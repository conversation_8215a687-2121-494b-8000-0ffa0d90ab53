[0.036s] Invoking command in '/home/<USER>/Code/autodrivingVersionTest/src/build/commonlibrary': /usr/bin/cmake --build /home/<USER>/Code/autodrivingVersionTest/src/build/commonlibrary -- -j12 -l12
[0.095s] -- Found rclcpp: 16.0.13 (/home/<USER>/ros2_humble/install/rclcpp/share/rclcpp/cmake)
[2.323s] -- Found rosidl_generator_c: 3.1.6 (/home/<USER>/ros2_humble/install/rosidl_generator_c/share/rosidl_generator_c/cmake)
[2.327s] -- Found rosidl_adapter: 3.1.6 (/home/<USER>/ros2_humble/install/rosidl_adapter/share/rosidl_adapter/cmake)
[2.336s] -- Found rosidl_generator_cpp: 3.1.6 (/home/<USER>/ros2_humble/install/rosidl_generator_cpp/share/rosidl_generator_cpp/cmake)
[2.396s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_introspection_c;rosidl_typesupport_fastrtps_c
[2.461s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_introspection_cpp;rosidl_typesupport_fastrtps_cpp
[2.562s] -- Found rmw_implementation_cmake: 6.1.2 (/home/<USER>/ros2_humble/install/rmw_implementation_cmake/share/rmw_implementation_cmake/cmake)
[2.607s] -- Found rmw_fastrtps_cpp: 6.2.7 (/home/<USER>/ros2_humble/install/rmw_fastrtps_cpp/share/rmw_fastrtps_cpp/cmake)
[2.930s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[3.031s] =============================================================
[3.031s] -- ROS2 Found. ROS2 Support is turned On.
[3.031s] =============================================================
[3.060s] CMake Error at CMakeLists.txt:18 (find_package):
[3.061s]   By not providing "Findcommon_msgs_humble.cmake" in CMAKE_MODULE_PATH this
[3.061s]   project has asked CMake to find a package configuration file provided by
[3.061s]   "common_msgs_humble", but CMake did not find one.
[3.061s] 
[3.061s]   Could not find a package configuration file provided by
[3.061s]   "common_msgs_humble" with any of the following names:
[3.061s] 
[3.061s]     common_msgs_humbleConfig.cmake
[3.061s]     common_msgs_humble-config.cmake
[3.061s] 
[3.061s]   Add the installation prefix of "common_msgs_humble" to CMAKE_PREFIX_PATH or
[3.061s]   set "common_msgs_humble_DIR" to a directory containing one of the above
[3.061s]   files.  If "common_msgs_humble" provides a separate development package or
[3.061s]   SDK, be sure it has been installed.
[3.062s] 
[3.062s] 
[3.063s] -- Configuring incomplete, errors occurred!
[3.063s] See also "/home/<USER>/Code/autodrivingVersionTest/src/build/commonlibrary/CMakeFiles/CMakeOutput.log".
[3.063s] See also "/home/<USER>/Code/autodrivingVersionTest/src/build/commonlibrary/CMakeFiles/CMakeError.log".
[3.068s] make: *** [Makefile:716：cmake_check_build_system] 错误 1
[3.071s] Invoked command in '/home/<USER>/Code/autodrivingVersionTest/src/build/commonlibrary' returned '2': /usr/bin/cmake --build /home/<USER>/Code/autodrivingVersionTest/src/build/commonlibrary -- -j12 -l12
