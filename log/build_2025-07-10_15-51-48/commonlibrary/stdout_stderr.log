-- Found rosidl_generator_c: 3.1.6 (/home/<USER>/ros2_humble/install/rosidl_generator_c/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/home/<USER>/ros2_humble/install/rosidl_adapter/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/home/<USER>/ros2_humble/install/rosidl_generator_cpp/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_introspection_c;rosidl_typesupport_fastrtps_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_introspection_cpp;rosidl_typesupport_fastrtps_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/home/<USER>/ros2_humble/install/rmw_implementation_cmake/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/home/<USER>/ros2_humble/install/rmw_fastrtps_cpp/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
=============================================================
-- ROS2 Found. ROS2 Support is turned On.
=============================================================
CMake Error at CMakeLists.txt:18 (find_package):
  By not providing "Findcommon_msgs_humble.cmake" in CMAKE_MODULE_PATH this
  project has asked CMake to find a package configuration file provided by
  "common_msgs_humble", but CMake did not find one.

  Could not find a package configuration file provided by
  "common_msgs_humble" with any of the following names:

    common_msgs_humbleConfig.cmake
    common_msgs_humble-config.cmake

  Add the installation prefix of "common_msgs_humble" to CMAKE_PREFIX_PATH or
  set "common_msgs_humble_DIR" to a directory containing one of the above
  files.  If "common_msgs_humble" provides a separate development package or
  SDK, be sure it has been installed.


-- Configuring incomplete, errors occurred!
See also "/home/<USER>/Code/autodrivingVersionTest/src/build/commonlibrary/CMakeFiles/CMakeOutput.log".
See also "/home/<USER>/Code/autodrivingVersionTest/src/build/commonlibrary/CMakeFiles/CMakeError.log".
make: *** [Makefile:716：cmake_check_build_system] 错误 1
