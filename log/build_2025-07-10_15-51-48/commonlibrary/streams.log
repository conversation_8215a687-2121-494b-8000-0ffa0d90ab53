[0.035s] Invoking command in '/home/<USER>/Code/autodrivingVersionTest/src/build/commonlibrary': /usr/bin/cmake --build /home/<USER>/Code/autodrivingVersionTest/src/build/commonlibrary -- -j12 -l12
[2.496s] -- Found rosidl_generator_c: 3.1.6 (/home/<USER>/ros2_humble/install/rosidl_generator_c/share/rosidl_generator_c/cmake)
[2.501s] -- Found rosidl_adapter: 3.1.6 (/home/<USER>/ros2_humble/install/rosidl_adapter/share/rosidl_adapter/cmake)
[2.513s] -- Found rosidl_generator_cpp: 3.1.6 (/home/<USER>/ros2_humble/install/rosidl_generator_cpp/share/rosidl_generator_cpp/cmake)
[2.578s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_introspection_c;rosidl_typesupport_fastrtps_c
[2.650s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_introspection_cpp;rosidl_typesupport_fastrtps_cpp
[2.761s] -- Found rmw_implementation_cmake: 6.1.2 (/home/<USER>/ros2_humble/install/rmw_implementation_cmake/share/rmw_implementation_cmake/cmake)
[2.809s] -- Found rmw_fastrtps_cpp: 6.2.7 (/home/<USER>/ros2_humble/install/rmw_fastrtps_cpp/share/rmw_fastrtps_cpp/cmake)
[3.095s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[3.191s] =============================================================
[3.192s] -- ROS2 Found. ROS2 Support is turned On.
[3.192s] =============================================================
[3.223s] CMake Error at CMakeLists.txt:18 (find_package):
[3.223s]   By not providing "Findcommon_msgs_humble.cmake" in CMAKE_MODULE_PATH this
[3.223s]   project has asked CMake to find a package configuration file provided by
[3.223s]   "common_msgs_humble", but CMake did not find one.
[3.223s] 
[3.223s]   Could not find a package configuration file provided by
[3.223s]   "common_msgs_humble" with any of the following names:
[3.224s] 
[3.224s]     common_msgs_humbleConfig.cmake
[3.224s]     common_msgs_humble-config.cmake
[3.224s] 
[3.224s]   Add the installation prefix of "common_msgs_humble" to CMAKE_PREFIX_PATH or
[3.224s]   set "common_msgs_humble_DIR" to a directory containing one of the above
[3.224s]   files.  If "common_msgs_humble" provides a separate development package or
[3.224s]   SDK, be sure it has been installed.
[3.224s] 
[3.224s] 
[3.226s] -- Configuring incomplete, errors occurred!
[3.226s] See also "/home/<USER>/Code/autodrivingVersionTest/src/build/commonlibrary/CMakeFiles/CMakeOutput.log".
[3.226s] See also "/home/<USER>/Code/autodrivingVersionTest/src/build/commonlibrary/CMakeFiles/CMakeError.log".
[3.234s] make: *** [Makefile:716：cmake_check_build_system] 错误 1
[3.236s] Invoked command in '/home/<USER>/Code/autodrivingVersionTest/src/build/commonlibrary' returned '2': /usr/bin/cmake --build /home/<USER>/Code/autodrivingVersionTest/src/build/commonlibrary -- -j12 -l12
