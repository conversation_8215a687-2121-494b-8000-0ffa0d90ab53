[0.420s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'commonlibrary']
[0.420s] DEBUG:colcon:Parsed command line arguments: Namespace(allow_overriding=[], ament_cmake_args=None, base_paths=['.'], build_base='build', catkin_cmake_args=None, catkin_skip_building_tests=False, cmake_args=None, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, cmake_target=None, cmake_target_skip_unavailable=False, continue_on_error=False, event_handlers=None, executor='parallel', ignore_user_meta=False, install_base='install', log_base=None, log_level=None, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7f1c77a64310>>, merge_install=False, metas=['./colcon.meta'], mixin=None, mixin_files=None, mixin_verb=('build',), packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_end=None, packages_ignore=None, packages_ignore_regex=None, packages_select=['commonlibrary'], packages_select_build_failed=False, packages_select_by_dep=None, packages_select_regex=None, packages_select_test_failures=False, packages_skip=None, packages_skip_build_finished=False, packages_skip_by_dep=None, packages_skip_regex=None, packages_skip_test_passed=False, packages_skip_up_to=None, packages_start=None, packages_up_to=None, packages_up_to_regex=None, parallel_workers=12, paths=None, symlink_install=False, test_result_base=None, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7f1c77a64310>, verb_name='build', verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x7f1c77a64bb0>)
[0.742s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.743s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.743s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.743s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.743s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.743s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.743s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/Code/autodrivingVersionTest/src'
[0.743s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.743s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.743s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.743s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.743s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.743s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.743s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.743s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.743s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.756s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.756s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.862s] WARNING:colcon.colcon_cmake.package_identification.cmake:Ignoring 'CMakeLists.txt' since it seems to be a toplevel CMake file generated by 'catkin_make'
[0.863s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.863s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.863s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.863s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.863s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.863s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.863s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.863s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.863s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.863s] Level 1:colcon.colcon_core.package_identification:_identify(interaction) by extensions ['ignore', 'ignore_ament_install']
[0.864s] Level 1:colcon.colcon_core.package_identification:_identify(interaction) by extension 'ignore'
[0.864s] Level 1:colcon.colcon_core.package_identification:_identify(interaction) by extension 'ignore_ament_install'
[0.864s] Level 1:colcon.colcon_core.package_identification:_identify(interaction) by extensions ['colcon_pkg']
[0.864s] Level 1:colcon.colcon_core.package_identification:_identify(interaction) by extension 'colcon_pkg'
[0.864s] Level 1:colcon.colcon_core.package_identification:_identify(interaction) by extensions ['colcon_meta']
[0.864s] Level 1:colcon.colcon_core.package_identification:_identify(interaction) by extension 'colcon_meta'
[0.864s] Level 1:colcon.colcon_core.package_identification:_identify(interaction) by extensions ['ros']
[0.864s] Level 1:colcon.colcon_core.package_identification:_identify(interaction) by extension 'ros'
[0.864s] Level 1:colcon.colcon_core.package_identification:_identify(interaction) by extensions ['cmake', 'python']
[0.864s] Level 1:colcon.colcon_core.package_identification:_identify(interaction) by extension 'cmake'
[0.864s] Level 1:colcon.colcon_core.package_identification:_identify(interaction) by extension 'python'
[0.864s] Level 1:colcon.colcon_core.package_identification:_identify(interaction) by extensions ['python_setup_py']
[0.864s] Level 1:colcon.colcon_core.package_identification:_identify(interaction) by extension 'python_setup_py'
[0.864s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver) by extensions ['ignore', 'ignore_ament_install']
[0.864s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver) by extension 'ignore'
[0.864s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver) by extension 'ignore_ament_install'
[0.864s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver) by extensions ['colcon_pkg']
[0.864s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver) by extension 'colcon_pkg'
[0.864s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver) by extensions ['colcon_meta']
[0.865s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver) by extension 'colcon_meta'
[0.865s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver) by extensions ['ros']
[0.865s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver) by extension 'ros'
[0.865s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver) by extensions ['cmake', 'python']
[0.865s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver) by extension 'cmake'
[0.865s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver) by extension 'python'
[0.865s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver) by extensions ['python_setup_py']
[0.865s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver) by extension 'python_setup_py'
[0.865s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS) by extensions ['ignore', 'ignore_ament_install']
[0.865s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS) by extension 'ignore'
[0.865s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS) by extension 'ignore_ament_install'
[0.865s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS) by extensions ['colcon_pkg']
[0.865s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS) by extension 'colcon_pkg'
[0.865s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS) by extensions ['colcon_meta']
[0.865s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS) by extension 'colcon_meta'
[0.865s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS) by extensions ['ros']
[0.865s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS) by extension 'ros'
[0.865s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS) by extensions ['cmake', 'python']
[0.865s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS) by extension 'cmake'
[0.865s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS) by extension 'python'
[0.865s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS) by extensions ['python_setup_py']
[0.865s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS) by extension 'python_setup_py'
[0.866s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/ars40x_srr308_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.866s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/ars40x_srr308_msgs) by extension 'ignore'
[0.866s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/ars40x_srr308_msgs) by extension 'ignore_ament_install'
[0.866s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/ars40x_srr308_msgs) by extensions ['colcon_pkg']
[0.866s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/ars40x_srr308_msgs) by extension 'colcon_pkg'
[0.866s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/ars40x_srr308_msgs) by extensions ['colcon_meta']
[0.866s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/ars40x_srr308_msgs) by extension 'colcon_meta'
[0.866s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/ars40x_srr308_msgs) by extensions ['ros']
[0.866s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/ars40x_srr308_msgs) by extension 'ros'
[0.868s] DEBUG:colcon.colcon_core.package_identification:Package 'interaction/driver/CanBoard_ROS/ars40x_srr308_msgs' with type 'ros.catkin' and name 'ars40x_srr308_msgs'
[0.868s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/can_board_driver) by extensions ['ignore', 'ignore_ament_install']
[0.868s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/can_board_driver) by extension 'ignore'
[0.868s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/can_board_driver) by extension 'ignore_ament_install'
[0.868s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/can_board_driver) by extensions ['colcon_pkg']
[0.868s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/can_board_driver) by extension 'colcon_pkg'
[0.868s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/can_board_driver) by extensions ['colcon_meta']
[0.868s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/can_board_driver) by extension 'colcon_meta'
[0.869s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/can_board_driver) by extensions ['ros']
[0.869s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/can_board_driver) by extension 'ros'
[0.869s] DEBUG:colcon.colcon_core.package_identification:Package 'interaction/driver/CanBoard_ROS/can_board_driver' with type 'ros.catkin' and name 'can_board_driver'
[0.869s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/control_cmd_sender) by extensions ['ignore', 'ignore_ament_install']
[0.869s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/control_cmd_sender) by extension 'ignore'
[0.870s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/control_cmd_sender) by extension 'ignore_ament_install'
[0.870s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/control_cmd_sender) by extensions ['colcon_pkg']
[0.870s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/control_cmd_sender) by extension 'colcon_pkg'
[0.870s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/control_cmd_sender) by extensions ['colcon_meta']
[0.870s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/control_cmd_sender) by extension 'colcon_meta'
[0.870s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/control_cmd_sender) by extensions ['ros']
[0.870s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/control_cmd_sender) by extension 'ros'
[0.870s] DEBUG:colcon.colcon_core.package_identification:Package 'interaction/driver/CanBoard_ROS/control_cmd_sender' with type 'ros.catkin' and name 'control_cmd_sender'
[0.871s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/h100_info_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.871s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/h100_info_msgs) by extension 'ignore'
[0.871s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/h100_info_msgs) by extension 'ignore_ament_install'
[0.871s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/h100_info_msgs) by extensions ['colcon_pkg']
[0.871s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/h100_info_msgs) by extension 'colcon_pkg'
[0.871s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/h100_info_msgs) by extensions ['colcon_meta']
[0.871s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/h100_info_msgs) by extension 'colcon_meta'
[0.871s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/h100_info_msgs) by extensions ['ros']
[0.871s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/h100_info_msgs) by extension 'ros'
[0.872s] DEBUG:colcon.colcon_core.package_identification:Package 'interaction/driver/CanBoard_ROS/h100_info_msgs' with type 'ros.catkin' and name 'h100_info_msgs'
[0.872s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/stp31x_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.872s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/stp31x_msgs) by extension 'ignore'
[0.872s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/stp31x_msgs) by extension 'ignore_ament_install'
[0.872s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/stp31x_msgs) by extensions ['colcon_pkg']
[0.872s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/stp31x_msgs) by extension 'colcon_pkg'
[0.872s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/stp31x_msgs) by extensions ['colcon_meta']
[0.872s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/stp31x_msgs) by extension 'colcon_meta'
[0.872s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/stp31x_msgs) by extensions ['ros']
[0.872s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/CanBoard_ROS/stp31x_msgs) by extension 'ros'
[0.873s] DEBUG:colcon.colcon_core.package_identification:Package 'interaction/driver/CanBoard_ROS/stp31x_msgs' with type 'ros.catkin' and name 'stp31x_msgs'
[0.873s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste) by extensions ['ignore', 'ignore_ament_install']
[0.873s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste) by extension 'ignore'
[0.873s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste) by extension 'ignore_ament_install'
[0.873s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste) by extensions ['colcon_pkg']
[0.873s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste) by extension 'colcon_pkg'
[0.873s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste) by extensions ['colcon_meta']
[0.873s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste) by extension 'colcon_meta'
[0.873s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste) by extensions ['ros']
[0.873s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste) by extension 'ros'
[0.873s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste) by extensions ['cmake', 'python']
[0.873s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste) by extension 'cmake'
[0.873s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste) by extension 'python'
[0.873s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste) by extensions ['python_setup_py']
[0.873s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste) by extension 'python_setup_py'
[0.873s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste/images) by extensions ['ignore', 'ignore_ament_install']
[0.873s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste/images) by extension 'ignore'
[0.874s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste/images) by extension 'ignore_ament_install'
[0.874s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste/images) by extensions ['colcon_pkg']
[0.874s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste/images) by extension 'colcon_pkg'
[0.874s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste/images) by extensions ['colcon_meta']
[0.874s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste/images) by extension 'colcon_meta'
[0.874s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste/images) by extensions ['ros']
[0.874s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste/images) by extension 'ros'
[0.874s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste/images) by extensions ['cmake', 'python']
[0.874s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste/images) by extension 'cmake'
[0.874s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste/images) by extension 'python'
[0.874s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste/images) by extensions ['python_setup_py']
[0.874s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste/images) by extension 'python_setup_py'
[0.874s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste/livox_ros_driver) by extensions ['ignore', 'ignore_ament_install']
[0.874s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste/livox_ros_driver) by extension 'ignore'
[0.874s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste/livox_ros_driver) by extension 'ignore_ament_install'
[0.874s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste/livox_ros_driver) by extensions ['colcon_pkg']
[0.874s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste/livox_ros_driver) by extension 'colcon_pkg'
[0.874s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste/livox_ros_driver) by extensions ['colcon_meta']
[0.874s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste/livox_ros_driver) by extension 'colcon_meta'
[0.874s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste/livox_ros_driver) by extensions ['ros']
[0.874s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/livox_ros_driver-maste/livox_ros_driver) by extension 'ros'
[0.876s] DEBUG:colcon.colcon_core.package_identification:Package 'interaction/driver/livox_ros_driver-maste/livox_ros_driver' with type 'ros.catkin' and name 'livox_ros_driver'
[0.876s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/obu) by extensions ['ignore', 'ignore_ament_install']
[0.876s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/obu) by extension 'ignore'
[0.876s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/obu) by extension 'ignore_ament_install'
[0.876s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/obu) by extensions ['colcon_pkg']
[0.876s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/obu) by extension 'colcon_pkg'
[0.876s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/obu) by extensions ['colcon_meta']
[0.876s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/obu) by extension 'colcon_meta'
[0.876s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/obu) by extensions ['ros']
[0.876s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/driver/obu) by extension 'ros'
[0.877s] DEBUG:colcon.colcon_core.package_identification:Package 'interaction/driver/obu' with type 'ros.catkin' and name 'obu'
[0.877s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction) by extensions ['ignore', 'ignore_ament_install']
[0.877s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction) by extension 'ignore'
[0.877s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction) by extension 'ignore_ament_install'
[0.877s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction) by extensions ['colcon_pkg']
[0.877s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction) by extension 'colcon_pkg'
[0.877s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction) by extensions ['colcon_meta']
[0.877s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction) by extension 'colcon_meta'
[0.877s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction) by extensions ['ros']
[0.877s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction) by extension 'ros'
[0.877s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction) by extensions ['cmake', 'python']
[0.877s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction) by extension 'cmake'
[0.877s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction) by extension 'python'
[0.877s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction) by extensions ['python_setup_py']
[0.877s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction) by extension 'python_setup_py'
[0.877s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/collectmap) by extensions ['ignore', 'ignore_ament_install']
[0.878s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/collectmap) by extension 'ignore'
[0.878s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/collectmap) by extension 'ignore_ament_install'
[0.878s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/collectmap) by extensions ['colcon_pkg']
[0.878s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/collectmap) by extension 'colcon_pkg'
[0.878s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/collectmap) by extensions ['colcon_meta']
[0.878s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/collectmap) by extension 'colcon_meta'
[0.878s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/collectmap) by extensions ['ros']
[0.878s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/collectmap) by extension 'ros'
[0.879s] DEBUG:colcon.colcon_core.package_identification:Package 'interaction/interaction/collectmap' with type 'ros.catkin' and name 'collectmap'
[0.879s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/diagnosis) by extensions ['ignore', 'ignore_ament_install']
[0.879s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/diagnosis) by extension 'ignore'
[0.879s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/diagnosis) by extension 'ignore_ament_install'
[0.879s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/diagnosis) by extensions ['colcon_pkg']
[0.879s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/diagnosis) by extension 'colcon_pkg'
[0.879s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/diagnosis) by extensions ['colcon_meta']
[0.879s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/diagnosis) by extension 'colcon_meta'
[0.879s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/diagnosis) by extensions ['ros']
[0.879s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/diagnosis) by extension 'ros'
[0.880s] DEBUG:colcon.colcon_core.package_identification:Package 'interaction/interaction/diagnosis' with type 'ros.catkin' and name 'diagnosis'
[0.880s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/monitor) by extensions ['ignore', 'ignore_ament_install']
[0.880s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/monitor) by extension 'ignore'
[0.880s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/monitor) by extension 'ignore_ament_install'
[0.880s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/monitor) by extensions ['colcon_pkg']
[0.880s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/monitor) by extension 'colcon_pkg'
[0.880s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/monitor) by extensions ['colcon_meta']
[0.880s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/monitor) by extension 'colcon_meta'
[0.880s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/monitor) by extensions ['ros']
[0.880s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/monitor) by extension 'ros'
[0.882s] DEBUG:colcon.colcon_core.package_identification:Package 'interaction/interaction/monitor' with type 'ros.catkin' and name 'monitor'
[0.882s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs) by extensions ['ignore', 'ignore_ament_install']
[0.882s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs) by extension 'ignore'
[0.882s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs) by extension 'ignore_ament_install'
[0.882s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs) by extensions ['colcon_pkg']
[0.882s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs) by extension 'colcon_pkg'
[0.882s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs) by extensions ['colcon_meta']
[0.882s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs) by extension 'colcon_meta'
[0.882s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs) by extensions ['ros']
[0.882s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs) by extension 'ros'
[0.882s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs) by extensions ['cmake', 'python']
[0.882s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs) by extension 'cmake'
[0.882s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs) by extension 'python'
[0.882s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs) by extensions ['python_setup_py']
[0.882s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs) by extension 'python_setup_py'
[0.882s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/common_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.882s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/common_msgs) by extension 'ignore'
[0.882s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/common_msgs) by extension 'ignore_ament_install'
[0.883s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/common_msgs) by extensions ['colcon_pkg']
[0.883s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/common_msgs) by extension 'colcon_pkg'
[0.883s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/common_msgs) by extensions ['colcon_meta']
[0.883s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/common_msgs) by extension 'colcon_meta'
[0.883s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/common_msgs) by extensions ['ros']
[0.883s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/common_msgs) by extension 'ros'
[0.884s] DEBUG:colcon.colcon_core.package_identification:Package 'interaction/interaction/msgs/common_msgs' with type 'ros.catkin' and name 'common_msgs'
[0.884s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/common_msgs_humble) by extensions ['ignore', 'ignore_ament_install']
[0.884s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/common_msgs_humble) by extension 'ignore'
[0.884s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/common_msgs_humble) by extension 'ignore_ament_install'
[0.884s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/common_msgs_humble) by extensions ['colcon_pkg']
[0.884s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/common_msgs_humble) by extension 'colcon_pkg'
[0.884s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/common_msgs_humble) by extensions ['colcon_meta']
[0.884s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/common_msgs_humble) by extension 'colcon_meta'
[0.884s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/common_msgs_humble) by extensions ['ros']
[0.884s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/common_msgs_humble) by extension 'ros'
[0.885s] DEBUG:colcon.colcon_core.package_identification:Package 'interaction/interaction/msgs/common_msgs_humble' with type 'ros.catkin' and name 'common_msgs_humble'
[0.885s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/control_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.885s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/control_msgs) by extension 'ignore'
[0.885s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/control_msgs) by extension 'ignore_ament_install'
[0.885s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/control_msgs) by extensions ['colcon_pkg']
[0.885s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/control_msgs) by extension 'colcon_pkg'
[0.885s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/control_msgs) by extensions ['colcon_meta']
[0.885s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/control_msgs) by extension 'colcon_meta'
[0.885s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/control_msgs) by extensions ['ros']
[0.885s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/control_msgs) by extension 'ros'
[0.886s] DEBUG:colcon.colcon_core.package_identification:Package 'interaction/interaction/msgs/control_msgs' with type 'ros.catkin' and name 'control_msgs'
[0.886s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/perception_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.886s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/perception_msgs) by extension 'ignore'
[0.886s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/perception_msgs) by extension 'ignore_ament_install'
[0.886s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/perception_msgs) by extensions ['colcon_pkg']
[0.886s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/perception_msgs) by extension 'colcon_pkg'
[0.886s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/perception_msgs) by extensions ['colcon_meta']
[0.887s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/perception_msgs) by extension 'colcon_meta'
[0.887s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/perception_msgs) by extensions ['ros']
[0.887s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/msgs/perception_msgs) by extension 'ros'
[0.887s] DEBUG:colcon.colcon_core.package_identification:Package 'interaction/interaction/msgs/perception_msgs' with type 'ros.catkin' and name 'perception_msgs'
[0.887s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/myrviz) by extensions ['ignore', 'ignore_ament_install']
[0.887s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/myrviz) by extension 'ignore'
[0.887s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/myrviz) by extension 'ignore_ament_install'
[0.887s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/myrviz) by extensions ['colcon_pkg']
[0.887s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/myrviz) by extension 'colcon_pkg'
[0.888s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/myrviz) by extensions ['colcon_meta']
[0.888s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/myrviz) by extension 'colcon_meta'
[0.888s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/myrviz) by extensions ['ros']
[0.888s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/myrviz) by extension 'ros'
[0.888s] DEBUG:colcon.colcon_core.package_identification:Package 'interaction/interaction/myrviz' with type 'ros.catkin' and name 'myrviz'
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/myrvizplugin) by extensions ['ignore', 'ignore_ament_install']
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/myrvizplugin) by extension 'ignore'
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/myrvizplugin) by extension 'ignore_ament_install'
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/myrvizplugin) by extensions ['colcon_pkg']
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/myrvizplugin) by extension 'colcon_pkg'
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/myrvizplugin) by extensions ['colcon_meta']
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/myrvizplugin) by extension 'colcon_meta'
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/myrvizplugin) by extensions ['ros']
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/myrvizplugin) by extension 'ros'
[0.890s] DEBUG:colcon.colcon_core.package_identification:Package 'interaction/interaction/myrvizplugin' with type 'ros.catkin' and name 'myrvizplugin'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/padServer) by extensions ['ignore', 'ignore_ament_install']
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/padServer) by extension 'ignore'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/padServer) by extension 'ignore_ament_install'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/padServer) by extensions ['colcon_pkg']
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/padServer) by extension 'colcon_pkg'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/padServer) by extensions ['colcon_meta']
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/padServer) by extension 'colcon_meta'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/padServer) by extensions ['ros']
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/padServer) by extension 'ros'
[0.891s] DEBUG:colcon.colcon_core.package_identification:Package 'interaction/interaction/padServer' with type 'ros.catkin' and name 'padServer'
[0.891s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/playstation) by extensions ['ignore', 'ignore_ament_install']
[0.891s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/playstation) by extension 'ignore'
[0.891s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/playstation) by extension 'ignore_ament_install'
[0.891s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/playstation) by extensions ['colcon_pkg']
[0.891s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/playstation) by extension 'colcon_pkg'
[0.891s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/playstation) by extensions ['colcon_meta']
[0.891s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/playstation) by extension 'colcon_meta'
[0.891s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/playstation) by extensions ['ros']
[0.891s] Level 1:colcon.colcon_core.package_identification:_identify(interaction/interaction/playstation) by extension 'ros'
[0.892s] DEBUG:colcon.colcon_core.package_identification:Package 'interaction/interaction/playstation' with type 'ros.catkin' and name 'playstation'
[0.892s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.892s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.892s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.892s] Level 1:colcon.colcon_core.package_identification:_identify(perception) by extensions ['ignore', 'ignore_ament_install']
[0.892s] Level 1:colcon.colcon_core.package_identification:_identify(perception) by extension 'ignore'
[0.892s] Level 1:colcon.colcon_core.package_identification:_identify(perception) by extension 'ignore_ament_install'
[0.892s] Level 1:colcon.colcon_core.package_identification:_identify(perception) by extensions ['colcon_pkg']
[0.892s] Level 1:colcon.colcon_core.package_identification:_identify(perception) by extension 'colcon_pkg'
[0.892s] Level 1:colcon.colcon_core.package_identification:_identify(perception) by extensions ['colcon_meta']
[0.892s] Level 1:colcon.colcon_core.package_identification:_identify(perception) by extension 'colcon_meta'
[0.892s] Level 1:colcon.colcon_core.package_identification:_identify(perception) by extensions ['ros']
[0.892s] Level 1:colcon.colcon_core.package_identification:_identify(perception) by extension 'ros'
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(perception) by extensions ['cmake', 'python']
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(perception) by extension 'cmake'
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(perception) by extension 'python'
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(perception) by extensions ['python_setup_py']
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(perception) by extension 'python_setup_py'
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam) by extensions ['ignore', 'ignore_ament_install']
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam) by extension 'ignore'
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam) by extension 'ignore_ament_install'
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam) by extensions ['colcon_pkg']
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam) by extension 'colcon_pkg'
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam) by extensions ['colcon_meta']
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam) by extension 'colcon_meta'
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam) by extensions ['ros']
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam) by extension 'ros'
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam) by extensions ['cmake', 'python']
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam) by extension 'cmake'
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam) by extension 'python'
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam) by extensions ['python_setup_py']
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam) by extension 'python_setup_py'
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src) by extensions ['ignore', 'ignore_ament_install']
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src) by extension 'ignore'
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src) by extension 'ignore_ament_install'
[0.894s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src) by extensions ['colcon_pkg']
[0.894s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src) by extension 'colcon_pkg'
[0.894s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src) by extensions ['colcon_meta']
[0.894s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src) by extension 'colcon_meta'
[0.894s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src) by extensions ['ros']
[0.894s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src) by extension 'ros'
[0.894s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src) by extensions ['cmake', 'python']
[0.894s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src) by extension 'cmake'
[0.900s] WARNING:colcon.colcon_cmake.package_identification.cmake:Ignoring 'perception/bjslam/src/CMakeLists.txt' since it seems to be a toplevel CMake file generated by 'catkin_make'
[0.900s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src) by extension 'python'
[0.900s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src) by extensions ['python_setup_py']
[0.900s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src) by extension 'python_setup_py'
[0.900s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/comm_tcp) by extensions ['ignore', 'ignore_ament_install']
[0.900s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/comm_tcp) by extension 'ignore'
[0.900s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/comm_tcp) by extension 'ignore_ament_install'
[0.900s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/comm_tcp) by extensions ['colcon_pkg']
[0.900s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/comm_tcp) by extension 'colcon_pkg'
[0.900s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/comm_tcp) by extensions ['colcon_meta']
[0.900s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/comm_tcp) by extension 'colcon_meta'
[0.900s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/comm_tcp) by extensions ['ros']
[0.901s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/comm_tcp) by extension 'ros'
[0.901s] DEBUG:colcon.colcon_core.package_identification:Package 'perception/bjslam/src/comm_tcp' with type 'ros.catkin' and name 'comm_tcp'
[0.901s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_od) by extensions ['ignore', 'ignore_ament_install']
[0.901s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_od) by extension 'ignore'
[0.902s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_od) by extension 'ignore_ament_install'
[0.902s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_od) by extensions ['colcon_pkg']
[0.902s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_od) by extension 'colcon_pkg'
[0.902s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_od) by extensions ['colcon_meta']
[0.902s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_od) by extension 'colcon_meta'
[0.902s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_od) by extensions ['ros']
[0.902s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_od) by extension 'ros'
[0.902s] DEBUG:colcon.colcon_core.package_identification:Package 'perception/bjslam/src/wanji_od' with type 'ros.catkin' and name 'wanji_od'
[0.903s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_slam) by extensions ['ignore', 'ignore_ament_install']
[0.903s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_slam) by extension 'ignore'
[0.903s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_slam) by extension 'ignore_ament_install'
[0.903s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_slam) by extensions ['colcon_pkg']
[0.903s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_slam) by extension 'colcon_pkg'
[0.903s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_slam) by extensions ['colcon_meta']
[0.903s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_slam) by extension 'colcon_meta'
[0.903s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_slam) by extensions ['ros']
[0.903s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_slam) by extension 'ros'
[0.904s] DEBUG:colcon.colcon_core.package_identification:Package 'perception/bjslam/src/wanji_slam' with type 'ros.catkin' and name 'wj_slam'
[0.904s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web) by extensions ['ignore', 'ignore_ament_install']
[0.904s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web) by extension 'ignore'
[0.904s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web) by extension 'ignore_ament_install'
[0.904s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web) by extensions ['colcon_pkg']
[0.904s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web) by extension 'colcon_pkg'
[0.904s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web) by extensions ['colcon_meta']
[0.904s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web) by extension 'colcon_meta'
[0.904s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web) by extensions ['ros']
[0.904s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web) by extension 'ros'
[0.904s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web) by extensions ['cmake', 'python']
[0.904s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web) by extension 'cmake'
[0.904s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web) by extension 'python'
[0.904s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web) by extensions ['python_setup_py']
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web) by extension 'python_setup_py'
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/img) by extensions ['ignore', 'ignore_ament_install']
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/img) by extension 'ignore'
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/img) by extension 'ignore_ament_install'
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/img) by extensions ['colcon_pkg']
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/img) by extension 'colcon_pkg'
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/img) by extensions ['colcon_meta']
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/img) by extension 'colcon_meta'
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/img) by extensions ['ros']
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/img) by extension 'ros'
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/img) by extensions ['cmake', 'python']
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/img) by extension 'cmake'
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/img) by extension 'python'
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/img) by extensions ['python_setup_py']
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/img) by extension 'python_setup_py'
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources) by extensions ['ignore', 'ignore_ament_install']
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources) by extension 'ignore'
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources) by extension 'ignore_ament_install'
[0.906s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources) by extensions ['colcon_pkg']
[0.906s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources) by extension 'colcon_pkg'
[0.906s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources) by extensions ['colcon_meta']
[0.906s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources) by extension 'colcon_meta'
[0.906s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources) by extensions ['ros']
[0.906s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources) by extension 'ros'
[0.906s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources) by extensions ['cmake', 'python']
[0.906s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources) by extension 'cmake'
[0.906s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources) by extension 'python'
[0.906s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources) by extensions ['python_setup_py']
[0.906s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources) by extension 'python_setup_py'
[0.906s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui) by extensions ['ignore', 'ignore_ament_install']
[0.906s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui) by extension 'ignore'
[0.906s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui) by extension 'ignore_ament_install'
[0.906s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui) by extensions ['colcon_pkg']
[0.906s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui) by extension 'colcon_pkg'
[0.906s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui) by extensions ['colcon_meta']
[0.906s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui) by extension 'colcon_meta'
[0.906s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui) by extensions ['ros']
[0.906s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui) by extension 'ros'
[0.907s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui) by extensions ['cmake', 'python']
[0.907s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui) by extension 'cmake'
[0.907s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui) by extension 'python'
[0.907s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui) by extensions ['python_setup_py']
[0.907s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui) by extension 'python_setup_py'
[0.907s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes) by extensions ['ignore', 'ignore_ament_install']
[0.907s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes) by extension 'ignore'
[0.907s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes) by extension 'ignore_ament_install'
[0.907s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes) by extensions ['colcon_pkg']
[0.907s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes) by extension 'colcon_pkg'
[0.907s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes) by extensions ['colcon_meta']
[0.907s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes) by extension 'colcon_meta'
[0.907s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes) by extensions ['ros']
[0.907s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes) by extension 'ros'
[0.907s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes) by extensions ['cmake', 'python']
[0.907s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes) by extension 'cmake'
[0.907s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes) by extension 'python'
[0.907s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes) by extensions ['python_setup_py']
[0.907s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes) by extension 'python_setup_py'
[0.907s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap) by extensions ['ignore', 'ignore_ament_install']
[0.908s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap) by extension 'ignore'
[0.908s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap) by extension 'ignore_ament_install'
[0.908s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap) by extensions ['colcon_pkg']
[0.908s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap) by extension 'colcon_pkg'
[0.908s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap) by extensions ['colcon_meta']
[0.908s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap) by extension 'colcon_meta'
[0.908s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap) by extensions ['ros']
[0.908s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap) by extension 'ros'
[0.908s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap) by extensions ['cmake', 'python']
[0.908s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap) by extension 'cmake'
[0.908s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap) by extension 'python'
[0.908s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap) by extensions ['python_setup_py']
[0.908s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap) by extension 'python_setup_py'
[0.908s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap/images) by extensions ['ignore', 'ignore_ament_install']
[0.908s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap/images) by extension 'ignore'
[0.908s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap/images) by extension 'ignore_ament_install'
[0.908s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap/images) by extensions ['colcon_pkg']
[0.908s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap/images) by extension 'colcon_pkg'
[0.908s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap/images) by extensions ['colcon_meta']
[0.908s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap/images) by extension 'colcon_meta'
[0.909s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap/images) by extensions ['ros']
[0.909s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap/images) by extension 'ros'
[0.909s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap/images) by extensions ['cmake', 'python']
[0.909s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap/images) by extension 'cmake'
[0.909s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap/images) by extension 'python'
[0.909s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap/images) by extensions ['python_setup_py']
[0.909s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/bootstrap/images) by extension 'python_setup_py'
[0.909s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/icons) by extensions ['ignore', 'ignore_ament_install']
[0.909s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/icons) by extension 'ignore'
[0.909s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/icons) by extension 'ignore_ament_install'
[0.909s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/icons) by extensions ['colcon_pkg']
[0.909s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/icons) by extension 'colcon_pkg'
[0.909s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/icons) by extensions ['colcon_meta']
[0.909s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/icons) by extension 'colcon_meta'
[0.909s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/icons) by extensions ['ros']
[0.909s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/icons) by extension 'ros'
[0.909s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/icons) by extensions ['cmake', 'python']
[0.909s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/icons) by extension 'cmake'
[0.909s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/icons) by extension 'python'
[0.909s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/icons) by extensions ['python_setup_py']
[0.910s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/easyui/themes/icons) by extension 'python_setup_py'
[0.910s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/loading) by extensions ['ignore', 'ignore_ament_install']
[0.910s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/loading) by extension 'ignore'
[0.910s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/loading) by extension 'ignore_ament_install'
[0.910s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/loading) by extensions ['colcon_pkg']
[0.910s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/loading) by extension 'colcon_pkg'
[0.910s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/loading) by extensions ['colcon_meta']
[0.910s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/loading) by extension 'colcon_meta'
[0.910s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/loading) by extensions ['ros']
[0.910s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/loading) by extension 'ros'
[0.910s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/loading) by extensions ['cmake', 'python']
[0.910s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/loading) by extension 'cmake'
[0.910s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/loading) by extension 'python'
[0.910s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/loading) by extensions ['python_setup_py']
[0.910s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/loading) by extension 'python_setup_py'
[0.910s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/qrcode) by extensions ['ignore', 'ignore_ament_install']
[0.910s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/qrcode) by extension 'ignore'
[0.910s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/qrcode) by extension 'ignore_ament_install'
[0.910s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/qrcode) by extensions ['colcon_pkg']
[0.910s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/qrcode) by extension 'colcon_pkg'
[0.911s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/qrcode) by extensions ['colcon_meta']
[0.911s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/qrcode) by extension 'colcon_meta'
[0.911s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/qrcode) by extensions ['ros']
[0.911s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/qrcode) by extension 'ros'
[0.911s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/qrcode) by extensions ['cmake', 'python']
[0.911s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/qrcode) by extension 'cmake'
[0.911s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/qrcode) by extension 'python'
[0.911s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/qrcode) by extensions ['python_setup_py']
[0.911s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/qrcode) by extension 'python_setup_py'
[0.911s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/ros) by extensions ['ignore', 'ignore_ament_install']
[0.911s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/ros) by extension 'ignore'
[0.911s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/ros) by extension 'ignore_ament_install'
[0.911s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/ros) by extensions ['colcon_pkg']
[0.911s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/ros) by extension 'colcon_pkg'
[0.911s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/ros) by extensions ['colcon_meta']
[0.911s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/ros) by extension 'colcon_meta'
[0.911s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/ros) by extensions ['ros']
[0.911s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/ros) by extension 'ros'
[0.912s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/ros) by extensions ['cmake', 'python']
[0.912s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/ros) by extension 'cmake'
[0.912s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/ros) by extension 'python'
[0.912s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/ros) by extensions ['python_setup_py']
[0.912s] Level 1:colcon.colcon_core.package_identification:_identify(perception/bjslam/src/wanji_web/resources/ros) by extension 'python_setup_py'
[0.912s] Level 1:colcon.colcon_core.package_identification:_identify(perception/commonlibrary) by extensions ['ignore', 'ignore_ament_install']
[0.912s] Level 1:colcon.colcon_core.package_identification:_identify(perception/commonlibrary) by extension 'ignore'
[0.912s] Level 1:colcon.colcon_core.package_identification:_identify(perception/commonlibrary) by extension 'ignore_ament_install'
[0.912s] Level 1:colcon.colcon_core.package_identification:_identify(perception/commonlibrary) by extensions ['colcon_pkg']
[0.912s] Level 1:colcon.colcon_core.package_identification:_identify(perception/commonlibrary) by extension 'colcon_pkg'
[0.912s] Level 1:colcon.colcon_core.package_identification:_identify(perception/commonlibrary) by extensions ['colcon_meta']
[0.912s] Level 1:colcon.colcon_core.package_identification:_identify(perception/commonlibrary) by extension 'colcon_meta'
[0.912s] Level 1:colcon.colcon_core.package_identification:_identify(perception/commonlibrary) by extensions ['ros']
[0.912s] Level 1:colcon.colcon_core.package_identification:_identify(perception/commonlibrary) by extension 'ros'
[0.913s] DEBUG:colcon.colcon_core.package_identification:Package 'perception/commonlibrary' with type 'ros.catkin' and name 'commonlibrary'
[0.913s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers) by extensions ['ignore', 'ignore_ament_install']
[0.913s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers) by extension 'ignore'
[0.913s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers) by extension 'ignore_ament_install'
[0.913s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers) by extensions ['colcon_pkg']
[0.913s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers) by extension 'colcon_pkg'
[0.913s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers) by extensions ['colcon_meta']
[0.913s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers) by extension 'colcon_meta'
[0.913s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers) by extensions ['ros']
[0.914s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers) by extension 'ros'
[0.914s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers) by extensions ['cmake', 'python']
[0.914s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers) by extension 'cmake'
[0.914s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers) by extension 'python'
[0.914s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers) by extensions ['python_setup_py']
[0.914s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers) by extension 'python_setup_py'
[0.914s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/autoalign) by extensions ['ignore', 'ignore_ament_install']
[0.914s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/autoalign) by extension 'ignore'
[0.914s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/autoalign) by extension 'ignore_ament_install'
[0.914s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/autoalign) by extensions ['colcon_pkg']
[0.914s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/autoalign) by extension 'colcon_pkg'
[0.914s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/autoalign) by extensions ['colcon_meta']
[0.914s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/autoalign) by extension 'colcon_meta'
[0.914s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/autoalign) by extensions ['ros']
[0.914s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/autoalign) by extension 'ros'
[0.915s] DEBUG:colcon.colcon_core.package_identification:Package 'perception/drivers/autoalign' with type 'ros.catkin' and name 'autoalign'
[0.916s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar) by extensions ['ignore', 'ignore_ament_install']
[0.916s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar) by extension 'ignore'
[0.916s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar) by extension 'ignore_ament_install'
[0.916s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar) by extensions ['colcon_pkg']
[0.916s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar) by extension 'colcon_pkg'
[0.916s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar) by extensions ['colcon_meta']
[0.916s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar) by extension 'colcon_meta'
[0.916s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar) by extensions ['ros']
[0.916s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar) by extension 'ros'
[0.916s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar) by extensions ['cmake', 'python']
[0.916s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar) by extension 'cmake'
[0.916s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar) by extension 'python'
[0.916s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar) by extensions ['python_setup_py']
[0.917s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar) by extension 'python_setup_py'
[0.917s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar) by extensions ['ignore', 'ignore_ament_install']
[0.917s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar) by extension 'ignore'
[0.917s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar) by extension 'ignore_ament_install'
[0.917s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar) by extensions ['colcon_pkg']
[0.917s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar) by extension 'colcon_pkg'
[0.917s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar) by extensions ['colcon_meta']
[0.917s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar) by extension 'colcon_meta'
[0.917s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar) by extensions ['ros']
[0.917s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar) by extension 'ros'
[0.919s] DEBUG:colcon.colcon_core.package_identification:Package 'perception/drivers/radar/radar' with type 'ros.catkin' and name 'radar'
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_deal) by extensions ['ignore', 'ignore_ament_install']
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_deal) by extension 'ignore'
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_deal) by extension 'ignore_ament_install'
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_deal) by extensions ['colcon_pkg']
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_deal) by extension 'colcon_pkg'
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_deal) by extensions ['colcon_meta']
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_deal) by extension 'colcon_meta'
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_deal) by extensions ['ros']
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_deal) by extension 'ros'
[0.920s] DEBUG:colcon.colcon_core.package_identification:Package 'perception/drivers/radar/radar_deal' with type 'ros.catkin' and name 'radar_deal'
[0.920s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_driver) by extensions ['ignore', 'ignore_ament_install']
[0.920s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_driver) by extension 'ignore'
[0.920s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_driver) by extension 'ignore_ament_install'
[0.920s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_driver) by extensions ['colcon_pkg']
[0.920s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_driver) by extension 'colcon_pkg'
[0.920s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_driver) by extensions ['colcon_meta']
[0.920s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_driver) by extension 'colcon_meta'
[0.920s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_driver) by extensions ['ros']
[0.920s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_driver) by extension 'ros'
[0.921s] DEBUG:colcon.colcon_core.package_identification:Package 'perception/drivers/radar/radar_driver' with type 'ros.catkin' and name 'radar_driver'
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_msgs) by extension 'ignore'
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_msgs) by extension 'ignore_ament_install'
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_msgs) by extensions ['colcon_pkg']
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_msgs) by extension 'colcon_pkg'
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_msgs) by extensions ['colcon_meta']
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_msgs) by extension 'colcon_meta'
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_msgs) by extensions ['ros']
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(perception/drivers/radar/radar_msgs) by extension 'ros'
[0.922s] DEBUG:colcon.colcon_core.package_identification:Package 'perception/drivers/radar/radar_msgs' with type 'ros.catkin' and name 'radar_msgs'
[0.922s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion) by extensions ['ignore', 'ignore_ament_install']
[0.922s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion) by extension 'ignore'
[0.922s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion) by extension 'ignore_ament_install'
[0.922s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion) by extensions ['colcon_pkg']
[0.922s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion) by extension 'colcon_pkg'
[0.922s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion) by extensions ['colcon_meta']
[0.922s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion) by extension 'colcon_meta'
[0.922s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion) by extensions ['ros']
[0.922s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion) by extension 'ros'
[0.923s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion) by extensions ['cmake', 'python']
[0.923s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion) by extension 'cmake'
[0.923s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion) by extension 'python'
[0.923s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion) by extensions ['python_setup_py']
[0.923s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion) by extension 'python_setup_py'
[0.923s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion/sensorgps) by extensions ['ignore', 'ignore_ament_install']
[0.923s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion/sensorgps) by extension 'ignore'
[0.923s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion/sensorgps) by extension 'ignore_ament_install'
[0.923s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion/sensorgps) by extensions ['colcon_pkg']
[0.923s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion/sensorgps) by extension 'colcon_pkg'
[0.923s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion/sensorgps) by extensions ['colcon_meta']
[0.923s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion/sensorgps) by extension 'colcon_meta'
[0.923s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion/sensorgps) by extensions ['ros']
[0.923s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion/sensorgps) by extension 'ros'
[0.924s] DEBUG:colcon.colcon_core.package_identification:Package 'perception/fusion/sensorgps' with type 'ros.catkin' and name 'sensorgps'
[0.924s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion/sensorradar) by extensions ['ignore', 'ignore_ament_install']
[0.924s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion/sensorradar) by extension 'ignore'
[0.924s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion/sensorradar) by extension 'ignore_ament_install'
[0.924s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion/sensorradar) by extensions ['colcon_pkg']
[0.924s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion/sensorradar) by extension 'colcon_pkg'
[0.925s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion/sensorradar) by extensions ['colcon_meta']
[0.925s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion/sensorradar) by extension 'colcon_meta'
[0.925s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion/sensorradar) by extensions ['ros']
[0.925s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusion/sensorradar) by extension 'ros'
[0.925s] DEBUG:colcon.colcon_core.package_identification:Package 'perception/fusion/sensorradar' with type 'ros.catkin' and name 'sensorradar'
[0.926s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusiontracking) by extensions ['ignore', 'ignore_ament_install']
[0.926s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusiontracking) by extension 'ignore'
[0.926s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusiontracking) by extension 'ignore_ament_install'
[0.926s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusiontracking) by extensions ['colcon_pkg']
[0.926s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusiontracking) by extension 'colcon_pkg'
[0.926s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusiontracking) by extensions ['colcon_meta']
[0.926s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusiontracking) by extension 'colcon_meta'
[0.926s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusiontracking) by extensions ['ros']
[0.926s] Level 1:colcon.colcon_core.package_identification:_identify(perception/fusiontracking) by extension 'ros'
[0.927s] DEBUG:colcon.colcon_core.package_identification:Package 'perception/fusiontracking' with type 'ros.catkin' and name 'fusiontracking'
[0.927s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping) by extensions ['ignore', 'ignore_ament_install']
[0.927s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping) by extension 'ignore'
[0.927s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping) by extension 'ignore_ament_install'
[0.927s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping) by extensions ['colcon_pkg']
[0.927s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping) by extension 'colcon_pkg'
[0.927s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping) by extensions ['colcon_meta']
[0.927s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping) by extension 'colcon_meta'
[0.927s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping) by extensions ['ros']
[0.927s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping) by extension 'ros'
[0.927s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping) by extensions ['cmake', 'python']
[0.927s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping) by extension 'cmake'
[0.927s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping) by extension 'python'
[0.927s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping) by extensions ['python_setup_py']
[0.927s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping) by extension 'python_setup_py'
[0.928s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/logFiles) by extensions ['ignore', 'ignore_ament_install']
[0.928s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/logFiles) by extension 'ignore'
[0.928s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/logFiles) by extension 'ignore_ament_install'
[0.928s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/logFiles) by extensions ['colcon_pkg']
[0.928s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/logFiles) by extension 'colcon_pkg'
[0.928s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/logFiles) by extensions ['colcon_meta']
[0.928s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/logFiles) by extension 'colcon_meta'
[0.928s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/logFiles) by extensions ['ros']
[0.928s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/logFiles) by extension 'ros'
[0.928s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/logFiles) by extensions ['cmake', 'python']
[0.928s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/logFiles) by extension 'cmake'
[0.928s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/logFiles) by extension 'python'
[0.928s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/logFiles) by extensions ['python_setup_py']
[0.928s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/logFiles) by extension 'python_setup_py'
[0.928s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/mapfiles) by extensions ['ignore', 'ignore_ament_install']
[0.928s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/mapfiles) by extension 'ignore'
[0.928s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/mapfiles) by extension 'ignore_ament_install'
[0.928s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/mapfiles) by extensions ['colcon_pkg']
[0.928s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/mapfiles) by extension 'colcon_pkg'
[0.929s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/mapfiles) by extensions ['colcon_meta']
[0.929s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/mapfiles) by extension 'colcon_meta'
[0.929s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/mapfiles) by extensions ['ros']
[0.929s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/mapfiles) by extension 'ros'
[0.929s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/mapfiles) by extensions ['cmake', 'python']
[0.929s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/mapfiles) by extension 'cmake'
[0.929s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/mapfiles) by extension 'python'
[0.929s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/mapfiles) by extensions ['python_setup_py']
[0.929s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/mapfiles) by extension 'python_setup_py'
[0.929s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/performance) by extensions ['ignore', 'ignore_ament_install']
[0.929s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/performance) by extension 'ignore'
[0.929s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/performance) by extension 'ignore_ament_install'
[0.929s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/performance) by extensions ['colcon_pkg']
[0.929s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/performance) by extension 'colcon_pkg'
[0.929s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/performance) by extensions ['colcon_meta']
[0.929s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/performance) by extension 'colcon_meta'
[0.930s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/performance) by extensions ['ros']
[0.930s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/performance) by extension 'ros'
[0.930s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/performance) by extensions ['cmake', 'python']
[0.930s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/performance) by extension 'cmake'
[0.930s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/performance) by extension 'python'
[0.930s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/performance) by extensions ['python_setup_py']
[0.930s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/performance) by extension 'python_setup_py'
[0.930s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/radarObjects) by extensions ['ignore', 'ignore_ament_install']
[0.930s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/radarObjects) by extension 'ignore'
[0.930s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/radarObjects) by extension 'ignore_ament_install'
[0.930s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/radarObjects) by extensions ['colcon_pkg']
[0.930s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/radarObjects) by extension 'colcon_pkg'
[0.930s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/radarObjects) by extensions ['colcon_meta']
[0.930s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/radarObjects) by extension 'colcon_meta'
[0.930s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/radarObjects) by extensions ['ros']
[0.931s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/radarObjects) by extension 'ros'
[0.931s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/radarObjects) by extensions ['cmake', 'python']
[0.931s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/radarObjects) by extension 'cmake'
[0.931s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/radarObjects) by extension 'python'
[0.931s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/radarObjects) by extensions ['python_setup_py']
[0.931s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/radarObjects) by extension 'python_setup_py'
[0.931s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/rawfiles) by extensions ['ignore', 'ignore_ament_install']
[0.931s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/rawfiles) by extension 'ignore'
[0.931s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/rawfiles) by extension 'ignore_ament_install'
[0.931s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/rawfiles) by extensions ['colcon_pkg']
[0.931s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/rawfiles) by extension 'colcon_pkg'
[0.931s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/rawfiles) by extensions ['colcon_meta']
[0.931s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/rawfiles) by extension 'colcon_meta'
[0.931s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/rawfiles) by extensions ['ros']
[0.931s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/rawfiles) by extension 'ros'
[0.931s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/rawfiles) by extensions ['cmake', 'python']
[0.931s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/rawfiles) by extension 'cmake'
[0.931s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/rawfiles) by extension 'python'
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/rawfiles) by extensions ['python_setup_py']
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/rawfiles) by extension 'python_setup_py'
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/selfCarInfo) by extensions ['ignore', 'ignore_ament_install']
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/selfCarInfo) by extension 'ignore'
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/selfCarInfo) by extension 'ignore_ament_install'
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/selfCarInfo) by extensions ['colcon_pkg']
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/selfCarInfo) by extension 'colcon_pkg'
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/selfCarInfo) by extensions ['colcon_meta']
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/selfCarInfo) by extension 'colcon_meta'
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/selfCarInfo) by extensions ['ros']
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/selfCarInfo) by extension 'ros'
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/selfCarInfo) by extensions ['cmake', 'python']
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/selfCarInfo) by extension 'cmake'
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/selfCarInfo) by extension 'python'
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/selfCarInfo) by extensions ['python_setup_py']
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/selfCarInfo) by extension 'python_setup_py'
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/timeUse) by extensions ['ignore', 'ignore_ament_install']
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/timeUse) by extension 'ignore'
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/timeUse) by extension 'ignore_ament_install'
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/timeUse) by extensions ['colcon_pkg']
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/timeUse) by extension 'colcon_pkg'
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/timeUse) by extensions ['colcon_meta']
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/timeUse) by extension 'colcon_meta'
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/timeUse) by extensions ['ros']
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/timeUse) by extension 'ros'
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/timeUse) by extensions ['cmake', 'python']
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/timeUse) by extension 'cmake'
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/timeUse) by extension 'python'
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/timeUse) by extensions ['python_setup_py']
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/timeUse) by extension 'python_setup_py'
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/trkobjects) by extensions ['ignore', 'ignore_ament_install']
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/trkobjects) by extension 'ignore'
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/trkobjects) by extension 'ignore_ament_install'
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/trkobjects) by extensions ['colcon_pkg']
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/trkobjects) by extension 'colcon_pkg'
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/trkobjects) by extensions ['colcon_meta']
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/trkobjects) by extension 'colcon_meta'
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/trkobjects) by extensions ['ros']
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/trkobjects) by extension 'ros'
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/trkobjects) by extensions ['cmake', 'python']
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/trkobjects) by extension 'cmake'
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/trkobjects) by extension 'python'
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/trkobjects) by extensions ['python_setup_py']
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(perception/outputMapping/trkobjects) by extension 'python_setup_py'
[0.934s] Level 1:colcon.colcon_core.package_identification:_identify(perception/trackedobjectfusion) by extensions ['ignore', 'ignore_ament_install']
[0.934s] Level 1:colcon.colcon_core.package_identification:_identify(perception/trackedobjectfusion) by extension 'ignore'
[0.934s] Level 1:colcon.colcon_core.package_identification:_identify(perception/trackedobjectfusion) by extension 'ignore_ament_install'
[0.934s] Level 1:colcon.colcon_core.package_identification:_identify(perception/trackedobjectfusion) by extensions ['colcon_pkg']
[0.934s] Level 1:colcon.colcon_core.package_identification:_identify(perception/trackedobjectfusion) by extension 'colcon_pkg'
[0.934s] Level 1:colcon.colcon_core.package_identification:_identify(perception/trackedobjectfusion) by extensions ['colcon_meta']
[0.934s] Level 1:colcon.colcon_core.package_identification:_identify(perception/trackedobjectfusion) by extension 'colcon_meta'
[0.934s] Level 1:colcon.colcon_core.package_identification:_identify(perception/trackedobjectfusion) by extensions ['ros']
[0.934s] Level 1:colcon.colcon_core.package_identification:_identify(perception/trackedobjectfusion) by extension 'ros'
[0.935s] DEBUG:colcon.colcon_core.package_identification:Package 'perception/trackedobjectfusion' with type 'ros.catkin' and name 'trackedobjectfusion'
[0.935s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.935s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.935s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.935s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.935s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.971s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ars40x_srr308_msgs' in 'interaction/driver/CanBoard_ROS/ars40x_srr308_msgs'
[0.971s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'autoalign' in 'perception/drivers/autoalign'
[0.971s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'collectmap' in 'interaction/interaction/collectmap'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'comm_tcp' in 'perception/bjslam/src/comm_tcp'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'common_msgs' in 'interaction/interaction/msgs/common_msgs'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'common_msgs_humble' in 'interaction/interaction/msgs/common_msgs_humble'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'fusiontracking' in 'perception/fusiontracking'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'h100_info_msgs' in 'interaction/driver/CanBoard_ROS/h100_info_msgs'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'livox_ros_driver' in 'interaction/driver/livox_ros_driver-maste/livox_ros_driver'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'padServer' in 'interaction/interaction/padServer'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'radar_msgs' in 'perception/drivers/radar/radar_msgs'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'sensorgps' in 'perception/fusion/sensorgps'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'stp31x_msgs' in 'interaction/driver/CanBoard_ROS/stp31x_msgs'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'trackedobjectfusion' in 'perception/trackedobjectfusion'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'wanji_od' in 'perception/bjslam/src/wanji_od'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'wj_slam' in 'perception/bjslam/src/wanji_slam'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'can_board_driver' in 'interaction/driver/CanBoard_ROS/can_board_driver'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'control_cmd_sender' in 'interaction/driver/CanBoard_ROS/control_cmd_sender'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'control_msgs' in 'interaction/interaction/msgs/control_msgs'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'diagnosis' in 'interaction/interaction/diagnosis'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'myrviz' in 'interaction/interaction/myrviz'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'myrvizplugin' in 'interaction/interaction/myrvizplugin'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'obu' in 'interaction/driver/obu'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'perception_msgs' in 'interaction/interaction/msgs/perception_msgs'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'playstation' in 'interaction/interaction/playstation'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'radar_deal' in 'perception/drivers/radar/radar_deal'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'radar_driver' in 'perception/drivers/radar/radar_driver'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'sensorradar' in 'perception/fusion/sensorradar'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'monitor' in 'interaction/interaction/monitor'
[0.972s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'radar' in 'perception/drivers/radar/radar'
[0.972s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.972s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.993s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 346 installed packages in /home/<USER>/ros2_humble/install
[0.996s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/ros2_humble/install/orocos_kdl_vendor
[0.996s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /opt/ros/noetic
[0.996s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[1.027s] Level 5:colcon.colcon_core.verb:set package 'commonlibrary' build argument 'cmake_args' from command line to 'None'
[1.027s] Level 5:colcon.colcon_core.verb:set package 'commonlibrary' build argument 'cmake_target' from command line to 'None'
[1.027s] Level 5:colcon.colcon_core.verb:set package 'commonlibrary' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[1.027s] Level 5:colcon.colcon_core.verb:set package 'commonlibrary' build argument 'cmake_clean_cache' from command line to 'False'
[1.027s] Level 5:colcon.colcon_core.verb:set package 'commonlibrary' build argument 'cmake_clean_first' from command line to 'False'
[1.027s] Level 5:colcon.colcon_core.verb:set package 'commonlibrary' build argument 'cmake_force_configure' from command line to 'False'
[1.027s] Level 5:colcon.colcon_core.verb:set package 'commonlibrary' build argument 'ament_cmake_args' from command line to 'None'
[1.027s] Level 5:colcon.colcon_core.verb:set package 'commonlibrary' build argument 'catkin_cmake_args' from command line to 'None'
[1.027s] Level 5:colcon.colcon_core.verb:set package 'commonlibrary' build argument 'catkin_skip_building_tests' from command line to 'False'
[1.027s] DEBUG:colcon.colcon_core.verb:Building package 'commonlibrary' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/autodrivingVersionTest/src/build/commonlibrary', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/autodrivingVersionTest/src/install/commonlibrary', 'merge_install': False, 'path': '/home/<USER>/Code/autodrivingVersionTest/src/perception/commonlibrary', 'symlink_install': False, 'test_result_base': None}
[1.028s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[1.028s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[1.029s] INFO:colcon.colcon_ros.task.catkin.build:Building ROS package in '/home/<USER>/Code/autodrivingVersionTest/src/perception/commonlibrary' with build type 'catkin'
[1.029s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/autodrivingVersionTest/src/perception/commonlibrary'
[1.035s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[1.035s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.035s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.066s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/autodrivingVersionTest/src/build/commonlibrary': /usr/bin/cmake --build /home/<USER>/Code/autodrivingVersionTest/src/build/commonlibrary -- -j12 -l12
[4.267s] Level 1:colcon.colcon_core.shell:create_environment_hook('commonlibrary', 'ros_package_path')
[4.268s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/autodrivingVersionTest/src/install/commonlibrary/share/commonlibrary/hook/ros_package_path.ps1'
[4.268s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/autodrivingVersionTest/src/build/commonlibrary' returned '2': /usr/bin/cmake --build /home/<USER>/Code/autodrivingVersionTest/src/build/commonlibrary -- -j12 -l12
[4.268s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/autodrivingVersionTest/src/install/commonlibrary/share/commonlibrary/hook/ros_package_path.dsv'
[4.269s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/autodrivingVersionTest/src/install/commonlibrary/share/commonlibrary/hook/ros_package_path.sh'
[4.270s] Level 1:colcon.colcon_core.shell:create_environment_hook('commonlibrary', 'pkg_config_path')
[4.270s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/autodrivingVersionTest/src/install/commonlibrary/share/commonlibrary/hook/pkg_config_path.ps1'
[4.270s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/autodrivingVersionTest/src/install/commonlibrary/share/commonlibrary/hook/pkg_config_path.dsv'
[4.270s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/autodrivingVersionTest/src/install/commonlibrary/share/commonlibrary/hook/pkg_config_path.sh'
[4.278s] Level 1:colcon.colcon_core.shell:create_environment_hook('commonlibrary', 'pkg_config_path_multiarch')
[4.279s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/autodrivingVersionTest/src/install/commonlibrary/share/commonlibrary/hook/pkg_config_path_multiarch.ps1'
[4.279s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/autodrivingVersionTest/src/install/commonlibrary/share/commonlibrary/hook/pkg_config_path_multiarch.dsv'
[4.279s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/autodrivingVersionTest/src/install/commonlibrary/share/commonlibrary/hook/pkg_config_path_multiarch.sh'
[4.280s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(commonlibrary)
[4.286s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/autodrivingVersionTest/src/install/commonlibrary' for CMake module files
[4.286s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/autodrivingVersionTest/src/install/commonlibrary' for CMake config files
[4.287s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/autodrivingVersionTest/src/install/commonlibrary/bin'
[4.287s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/autodrivingVersionTest/src/install/commonlibrary/lib/pkgconfig/commonlibrary.pc'
[4.287s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/autodrivingVersionTest/src/install/commonlibrary/lib/python3.8/site-packages'
[4.287s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/autodrivingVersionTest/src/install/commonlibrary/bin'
[4.287s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/autodrivingVersionTest/src/install/commonlibrary/share/commonlibrary/package.ps1'
[4.288s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/autodrivingVersionTest/src/install/commonlibrary/share/commonlibrary/package.dsv'
[4.288s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/autodrivingVersionTest/src/install/commonlibrary/share/commonlibrary/package.sh'
[4.289s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/autodrivingVersionTest/src/install/commonlibrary/share/commonlibrary/package.bash'
[4.290s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/autodrivingVersionTest/src/install/commonlibrary/share/commonlibrary/package.zsh'
[4.290s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/autodrivingVersionTest/src/install/commonlibrary/share/colcon-core/packages/commonlibrary)
[4.301s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[4.301s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[4.301s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '2'
[4.301s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[4.319s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[4.319s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[4.319s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[4.332s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[4.332s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/autodrivingVersionTest/src/install/local_setup.ps1'
[4.333s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Code/autodrivingVersionTest/src/install/_local_setup_util_ps1.py'
[4.335s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/autodrivingVersionTest/src/install/setup.ps1'
[4.348s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/autodrivingVersionTest/src/install/local_setup.sh'
[4.348s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Code/autodrivingVersionTest/src/install/_local_setup_util_sh.py'
[4.349s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/autodrivingVersionTest/src/install/setup.sh'
[4.361s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/autodrivingVersionTest/src/install/local_setup.bash'
[4.362s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/autodrivingVersionTest/src/install/setup.bash'
[4.376s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/autodrivingVersionTest/src/install/local_setup.zsh'
[4.376s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/autodrivingVersionTest/src/install/setup.zsh'
