#!/bin/bash
###
 # @Author: hanshuangquan <EMAIL>
 # @Date: 2025-07-10 14:38:30
 # @LastEditors: hanshuangquan <EMAIL>
 # @LastEditTime: 2025-07-10 16:23:14
 # @FilePath: /src/build_ros2.sh
 # @Description: 
 # 
 # Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
### 

# Clean environment script for ROS2 build
unset ROS_PACKAGE_PATH
unset ROSLISP_PACKAGE_DIRECTORIES  
unset ROS_ETC_DIR
unset ROS_MASTER_URI
unset ROS_ROOT
unset ROS_DISTRO
unset ROS_VERSION
unset ROS_PYTHON_VERSION
unset ROS_LOCALHOST_ONLY

# Source ROS2
source /home/<USER>/ros2_humble/install/setup.bash

# Build both packages in the correct order
echo "Building common_msgs_humble and commonlibrary..."
colcon build --packages-select common_msgs_humble commonlibrary
