#!/usr/bin/env python3

import os
import subprocess
import sys

def clean_environment():
    """清理所有 ROS 相关的环境变量"""
    ros_vars = [
        'ROS_ROOT', 'ROS_DISTRO', 'ROS_PACKAGE_PATH', 'ROSLISP_PACKAGE_DIRECTORIES',
        'ROS_ETC_DIR', 'ROS_MASTER_URI', 'ROS_VERSION', 'ROS_PYTHON_VERSION',
        'ROS_LOCALHOST_ONLY', 'PYTHONPATH', 'LD_LIBRARY_PATH', 'PATH',
        'CMAKE_PREFIX_PATH', 'AMENT_PREFIX_PATH'
    ]
    
    for var in ros_vars:
        if var in os.environ:
            del os.environ[var]

def setup_ros2_environment():
    """设置 ROS2 环境"""
    # 基本路径
    ros2_install = "/home/<USER>/ros2_humble/install"
    workspace_install = "/home/<USER>/Code/autodrivingVersionTest/install"
    
    # 设置环境变量
    os.environ['ROS_DISTRO'] = 'humble'
    os.environ['ROS_VERSION'] = '2'
    
    # 设置 PATH
    path_dirs = [
        f"{ros2_install}/bin",
        "/usr/local/sbin", "/usr/local/bin", "/usr/sbin", "/usr/bin", "/sbin", "/bin"
    ]
    os.environ['PATH'] = ":".join(path_dirs)
    
    # 设置 LD_LIBRARY_PATH - 包含所有 ROS2 包的 lib 目录
    lib_dirs = []

    # 添加工作空间库
    lib_dirs.append(f"{workspace_install}/lib")

    # 添加所有 ROS2 包的库目录
    import glob
    ros2_lib_dirs = glob.glob(f"{ros2_install}/*/lib")
    lib_dirs.extend(ros2_lib_dirs)

    # 添加主要的 lib 目录
    lib_dirs.append(f"{ros2_install}/lib")
    lib_dirs.append("/usr/lib/x86_64-linux-gnu")
    lib_dirs.append("/usr/lib")
    lib_dirs.append("/lib/x86_64-linux-gnu")
    lib_dirs.append("/lib")

    os.environ['LD_LIBRARY_PATH'] = ":".join(lib_dirs)
    
    # 设置 PYTHONPATH
    python_dirs = [
        f"{ros2_install}/lib/python3.10/site-packages",
        f"{workspace_install}/lib/python3.10/site-packages"
    ]
    os.environ['PYTHONPATH'] = ":".join(python_dirs)
    
    # 设置 CMAKE_PREFIX_PATH 和 AMENT_PREFIX_PATH
    prefix_dirs = [ros2_install, workspace_install]
    os.environ['CMAKE_PREFIX_PATH'] = ":".join(prefix_dirs)
    os.environ['AMENT_PREFIX_PATH'] = ":".join(prefix_dirs)

def main():
    print("正在清理环境变量...")
    clean_environment()
    
    print("正在设置 ROS2 环境...")
    setup_ros2_environment()
    
    print("正在运行 autoalign...")
    
    # 直接运行可执行文件
    autoalign_exe = "/home/<USER>/Code/autodrivingVersionTest/install/autoalign/lib/autoalign/autoalign"
    
    try:
        subprocess.run([autoalign_exe], check=True)
    except subprocess.CalledProcessError as e:
        print(f"运行失败: {e}")
        return 1
    except FileNotFoundError:
        print(f"找不到可执行文件: {autoalign_exe}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
