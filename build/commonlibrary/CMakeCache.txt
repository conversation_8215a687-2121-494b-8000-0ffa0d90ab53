# This is the CMakeCache file.
# For build in directory: /home/<USER>/Code/autodrivingVersionTest/src/build/commonlibrary
# It was generated by CMake: /usr/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Generate environment files in the CMAKE_INSTALL_PREFIX
AMENT_CMAKE_ENVIRONMENT_GENERATION:BOOL=OFF

//Generate environment files in the package share folder
AMENT_CMAKE_ENVIRONMENT_PACKAGE_GENERATION:BOOL=ON

//Generate marker file containing the parent prefix path
AMENT_CMAKE_ENVIRONMENT_PARENT_PREFIX_PATH_GENERATION:BOOL=ON

//Replace the CMake install command with a custom implementation
// using symlinks instead of copying resources
AMENT_CMAKE_SYMLINK_INSTALL:BOOL=OFF

//Generate an uninstall target to revert the effects of the install
// step
AMENT_CMAKE_UNINSTALL_TARGET:BOOL=ON

//The path where test results are generated
AMENT_TEST_RESULTS_DIR:PATH=/home/<USER>/Code/autodrivingVersionTest/src/build/commonlibrary/test_results

//Build the testing tree.
BUILD_TESTING:BOOL=ON

//Boost date_time library (debug)
Boost_DATE_TIME_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_date_time.so

//Boost date_time library (release)
Boost_DATE_TIME_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_date_time.so

//Boost filesystem library (debug)
Boost_FILESYSTEM_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_filesystem.so

//Boost filesystem library (release)
Boost_FILESYSTEM_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_filesystem.so

//Path to a file.
Boost_INCLUDE_DIR:PATH=/usr/include

//Boost iostreams library (debug)
Boost_IOSTREAMS_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_iostreams.so

//Boost iostreams library (release)
Boost_IOSTREAMS_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_iostreams.so

//Boost library directory DEBUG
Boost_LIBRARY_DIR_DEBUG:PATH=/usr/lib/x86_64-linux-gnu

//Boost library directory RELEASE
Boost_LIBRARY_DIR_RELEASE:PATH=/usr/lib/x86_64-linux-gnu

//Boost regex library (debug)
Boost_REGEX_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_regex.so

//Boost regex library (release)
Boost_REGEX_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_regex.so

//Boost system library (debug)
Boost_SYSTEM_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_system.so

//Boost system library (release)
Boost_SYSTEM_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_system.so

//No help, variable specified on the command line.
CATKIN_INSTALL_INTO_PREFIX_ROOT:UNINITIALIZED=0

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/usr/bin/addr2line

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/usr/bin/c++

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-9

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-9

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:FILEPATH=/usr/bin/cc

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-9

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-9

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=OFF

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/home/<USER>/Code/autodrivingVersionTest/src/install/commonlibrary

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/make

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/usr/bin/objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=commonlibrary

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=/usr/bin/readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Path to a file.
EIGEN_INCLUDE_DIR:PATH=/usr/include/eigen3

//Path to a file.
FLANN_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
FLANN_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libflann_cpp.so

//Path to a library.
FLANN_LIBRARY_DEBUG:FILEPATH=FLANN_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
FastCDR_LIBRARY_DEBUG:FILEPATH=FastCDR_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
FastCDR_LIBRARY_RELEASE:FILEPATH=/home/<USER>/ros2_humble/install/fastcdr/lib/libfastcdr.so

//Path to a file.
FastRTPS_INCLUDE_DIR:PATH=/home/<USER>/ros2_humble/install/fastrtps/include

//Path to a library.
FastRTPS_LIBRARY_DEBUG:FILEPATH=FastRTPS_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
FastRTPS_LIBRARY_RELEASE:FILEPATH=/home/<USER>/ros2_humble/install/fastrtps/lib/libfastrtps.so

//Path to a file.
LIBUSB_1_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
LIBUSB_1_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libusb-1.0.so

//Path to a file.
OPENNI2_INCLUDE_DIR:PATH=/usr/include/openni2

//Path to a library.
OPENNI2_LIBRARY:FILEPATH=/usr/lib/libOpenNI2.so

//Path to a file.
OPENNI_INCLUDE_DIR:PATH=/usr/include/ni

//Path to a library.
OPENNI_LIBRARY:FILEPATH=/usr/lib/libOpenNI.so

//Path to a library.
OPENSSL_CRYPTO_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libcrypto.so

//Path to a file.
OPENSSL_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
OPENSSL_SSL_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libssl.so

//The directory containing a CMake configuration file for OpenCV.
OpenCV_DIR:PATH=/usr/local/share/OpenCV

//path to 2d headers
PCL_2D_INCLUDE_DIR:PATH=/usr/include/pcl-1.10

//path to apps headers
PCL_APPS_INCLUDE_DIR:PATH=/usr/include/pcl-1.10

//path to pcl_apps library
PCL_APPS_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_apps.so

//path to pcl_apps library debug
PCL_APPS_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_apps.so

//path to common headers
PCL_COMMON_INCLUDE_DIR:PATH=/usr/include/pcl-1.10

//path to pcl_common library
PCL_COMMON_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_common.so

//path to pcl_common library debug
PCL_COMMON_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_common.so

//The directory containing a CMake configuration file for PCL.
PCL_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/pcl

//path to features headers
PCL_FEATURES_INCLUDE_DIR:PATH=/usr/include/pcl-1.10

//path to pcl_features library
PCL_FEATURES_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_features.so

//path to pcl_features library debug
PCL_FEATURES_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_features.so

//path to filters headers
PCL_FILTERS_INCLUDE_DIR:PATH=/usr/include/pcl-1.10

//path to pcl_filters library
PCL_FILTERS_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_filters.so

//path to pcl_filters library debug
PCL_FILTERS_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_filters.so

//path to geometry headers
PCL_GEOMETRY_INCLUDE_DIR:PATH=/usr/include/pcl-1.10

//path to in_hand_scanner headers
PCL_IN_HAND_SCANNER_INCLUDE_DIR:PATH=/usr/include/pcl-1.10

//path to io headers
PCL_IO_INCLUDE_DIR:PATH=/usr/include/pcl-1.10

//path to pcl_io library
PCL_IO_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_io.so

//path to pcl_io library debug
PCL_IO_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_io.so

//path to kdtree headers
PCL_KDTREE_INCLUDE_DIR:PATH=/usr/include/pcl-1.10

//path to pcl_kdtree library
PCL_KDTREE_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_kdtree.so

//path to pcl_kdtree library debug
PCL_KDTREE_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_kdtree.so

//path to keypoints headers
PCL_KEYPOINTS_INCLUDE_DIR:PATH=/usr/include/pcl-1.10

//path to pcl_keypoints library
PCL_KEYPOINTS_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_keypoints.so

//path to pcl_keypoints library debug
PCL_KEYPOINTS_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_keypoints.so

//path to ml headers
PCL_ML_INCLUDE_DIR:PATH=/usr/include/pcl-1.10

//path to pcl_ml library
PCL_ML_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_ml.so

//path to pcl_ml library debug
PCL_ML_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_ml.so

//path to octree headers
PCL_OCTREE_INCLUDE_DIR:PATH=/usr/include/pcl-1.10

//path to pcl_octree library
PCL_OCTREE_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_octree.so

//path to pcl_octree library debug
PCL_OCTREE_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_octree.so

//path to outofcore headers
PCL_OUTOFCORE_INCLUDE_DIR:PATH=/usr/include/pcl-1.10

//path to pcl_outofcore library
PCL_OUTOFCORE_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_outofcore.so

//path to pcl_outofcore library debug
PCL_OUTOFCORE_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_outofcore.so

//path to people headers
PCL_PEOPLE_INCLUDE_DIR:PATH=/usr/include/pcl-1.10

//path to pcl_people library
PCL_PEOPLE_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_people.so

//path to pcl_people library debug
PCL_PEOPLE_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_people.so

//path to point_cloud_editor headers
PCL_POINT_CLOUD_EDITOR_INCLUDE_DIR:PATH=/usr/include/pcl-1.10

//path to recognition headers
PCL_RECOGNITION_INCLUDE_DIR:PATH=/usr/include/pcl-1.10

//path to pcl_recognition library
PCL_RECOGNITION_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_recognition.so

//path to pcl_recognition library debug
PCL_RECOGNITION_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_recognition.so

//path to registration headers
PCL_REGISTRATION_INCLUDE_DIR:PATH=/usr/include/pcl-1.10

//path to pcl_registration library
PCL_REGISTRATION_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_registration.so

//path to pcl_registration library debug
PCL_REGISTRATION_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_registration.so

//path to sample_consensus headers
PCL_SAMPLE_CONSENSUS_INCLUDE_DIR:PATH=/usr/include/pcl-1.10

//path to pcl_sample_consensus library
PCL_SAMPLE_CONSENSUS_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so

//path to pcl_sample_consensus library debug
PCL_SAMPLE_CONSENSUS_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so

//path to search headers
PCL_SEARCH_INCLUDE_DIR:PATH=/usr/include/pcl-1.10

//path to pcl_search library
PCL_SEARCH_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_search.so

//path to pcl_search library debug
PCL_SEARCH_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_search.so

//path to segmentation headers
PCL_SEGMENTATION_INCLUDE_DIR:PATH=/usr/include/pcl-1.10

//path to pcl_segmentation library
PCL_SEGMENTATION_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_segmentation.so

//path to pcl_segmentation library debug
PCL_SEGMENTATION_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_segmentation.so

//path to stereo headers
PCL_STEREO_INCLUDE_DIR:PATH=/usr/include/pcl-1.10

//path to pcl_stereo library
PCL_STEREO_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_stereo.so

//path to pcl_stereo library debug
PCL_STEREO_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_stereo.so

//path to surface headers
PCL_SURFACE_INCLUDE_DIR:PATH=/usr/include/pcl-1.10

//path to pcl_surface library
PCL_SURFACE_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_surface.so

//path to pcl_surface library debug
PCL_SURFACE_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_surface.so

//path to tracking headers
PCL_TRACKING_INCLUDE_DIR:PATH=/usr/include/pcl-1.10

//path to pcl_tracking library
PCL_TRACKING_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_tracking.so

//path to pcl_tracking library debug
PCL_TRACKING_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_tracking.so

//path to visualization headers
PCL_VISUALIZATION_INCLUDE_DIR:PATH=/usr/include/pcl-1.10

//path to pcl_visualization library
PCL_VISUALIZATION_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_visualization.so

//path to pcl_visualization library debug
PCL_VISUALIZATION_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_visualization.so

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/usr/bin/pkg-config

//Path to a program.
Python3_EXECUTABLE:FILEPATH=/usr/bin/python3

//Path to a library.
QHULL_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libqhull.so

//Path to a library.
QHULL_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libqhull.so

//Name of the computer/site where compile is being run
SITE:STRING=wanji

//The directory containing a CMake configuration file for TinyXML2.
TinyXML2_DIR:PATH=TinyXML2_DIR-NOTFOUND

//Path to a file.
USB_10_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
USB_10_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libusb-1.0.so

//The directory containing VTKConfig.cmake
VTK_DIR:PATH=/usr/lib/cmake/vtk-7.1

//Path to a library.
_lib:FILEPATH=/home/<USER>/ros2_humble/install/rosgraph_msgs/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so

//The directory containing a CMake configuration file for ament_cmake.
ament_cmake_DIR:PATH=/home/<USER>/ros2_humble/install/ament_cmake/share/ament_cmake/cmake

//The directory containing a CMake configuration file for ament_cmake_core.
ament_cmake_core_DIR:PATH=/home/<USER>/ros2_humble/install/ament_cmake_core/share/ament_cmake_core/cmake

//The directory containing a CMake configuration file for ament_cmake_export_definitions.
ament_cmake_export_definitions_DIR:PATH=/home/<USER>/ros2_humble/install/ament_cmake_export_definitions/share/ament_cmake_export_definitions/cmake

//The directory containing a CMake configuration file for ament_cmake_export_dependencies.
ament_cmake_export_dependencies_DIR:PATH=/home/<USER>/ros2_humble/install/ament_cmake_export_dependencies/share/ament_cmake_export_dependencies/cmake

//The directory containing a CMake configuration file for ament_cmake_export_include_directories.
ament_cmake_export_include_directories_DIR:PATH=/home/<USER>/ros2_humble/install/ament_cmake_export_include_directories/share/ament_cmake_export_include_directories/cmake

//The directory containing a CMake configuration file for ament_cmake_export_interfaces.
ament_cmake_export_interfaces_DIR:PATH=/home/<USER>/ros2_humble/install/ament_cmake_export_interfaces/share/ament_cmake_export_interfaces/cmake

//The directory containing a CMake configuration file for ament_cmake_export_libraries.
ament_cmake_export_libraries_DIR:PATH=/home/<USER>/ros2_humble/install/ament_cmake_export_libraries/share/ament_cmake_export_libraries/cmake

//The directory containing a CMake configuration file for ament_cmake_export_link_flags.
ament_cmake_export_link_flags_DIR:PATH=/home/<USER>/ros2_humble/install/ament_cmake_export_link_flags/share/ament_cmake_export_link_flags/cmake

//The directory containing a CMake configuration file for ament_cmake_export_targets.
ament_cmake_export_targets_DIR:PATH=/home/<USER>/ros2_humble/install/ament_cmake_export_targets/share/ament_cmake_export_targets/cmake

//The directory containing a CMake configuration file for ament_cmake_gen_version_h.
ament_cmake_gen_version_h_DIR:PATH=/home/<USER>/ros2_humble/install/ament_cmake_gen_version_h/share/ament_cmake_gen_version_h/cmake

//The directory containing a CMake configuration file for ament_cmake_include_directories.
ament_cmake_include_directories_DIR:PATH=/home/<USER>/ros2_humble/install/ament_cmake_include_directories/share/ament_cmake_include_directories/cmake

//The directory containing a CMake configuration file for ament_cmake_libraries.
ament_cmake_libraries_DIR:PATH=/home/<USER>/ros2_humble/install/ament_cmake_libraries/share/ament_cmake_libraries/cmake

//The directory containing a CMake configuration file for ament_cmake_python.
ament_cmake_python_DIR:PATH=/home/<USER>/ros2_humble/install/ament_cmake_python/share/ament_cmake_python/cmake

//The directory containing a CMake configuration file for ament_cmake_target_dependencies.
ament_cmake_target_dependencies_DIR:PATH=/home/<USER>/ros2_humble/install/ament_cmake_target_dependencies/share/ament_cmake_target_dependencies/cmake

//The directory containing a CMake configuration file for ament_cmake_test.
ament_cmake_test_DIR:PATH=/home/<USER>/ros2_humble/install/ament_cmake_test/share/ament_cmake_test/cmake

//The directory containing a CMake configuration file for ament_cmake_version.
ament_cmake_version_DIR:PATH=/home/<USER>/ros2_humble/install/ament_cmake_version/share/ament_cmake_version/cmake

//The directory containing a CMake configuration file for ament_index_cpp.
ament_index_cpp_DIR:PATH=/home/<USER>/ros2_humble/install/ament_index_cpp/share/ament_index_cpp/cmake

//The directory containing a CMake configuration file for builtin_interfaces.
builtin_interfaces_DIR:PATH=/home/<USER>/ros2_humble/install/builtin_interfaces/share/builtin_interfaces/cmake

//The directory containing a CMake configuration file for common_msgs_humble.
common_msgs_humble_DIR:PATH=common_msgs_humble_DIR-NOTFOUND

//Value Computed by CMake
commonlibrary_BINARY_DIR:STATIC=/home/<USER>/Code/autodrivingVersionTest/src/build/commonlibrary

//Dependencies for the target
commonlibrary_LIB_DEPENDS:STATIC=general;opencv_calib3d;general;opencv_core;general;opencv_dnn;general;opencv_features2d;general;opencv_flann;general;opencv_highgui;general;opencv_imgcodecs;general;opencv_imgproc;general;opencv_ml;general;opencv_objdetect;general;opencv_photo;general;opencv_shape;general;opencv_stitching;general;opencv_superres;general;opencv_video;general;opencv_videoio;general;opencv_videostab;general;opencv_viz;general;yaml-cpp;general;pcl_common;general;pcl_kdtree;general;pcl_octree;general;pcl_search;general;pcl_sample_consensus;general;pcl_filters;general;pcl_io;general;pcl_features;general;pcl_ml;general;pcl_segmentation;general;pcl_visualization;general;pcl_surface;general;pcl_registration;general;pcl_keypoints;general;pcl_tracking;general;pcl_recognition;general;pcl_stereo;general;pcl_apps;general;pcl_outofcore;general;pcl_people;general;/usr/lib/x86_64-linux-gnu/libboost_system.so;general;/usr/lib/x86_64-linux-gnu/libboost_filesystem.so;general;/usr/lib/x86_64-linux-gnu/libboost_date_time.so;general;/usr/lib/x86_64-linux-gnu/libboost_iostreams.so;general;/usr/lib/x86_64-linux-gnu/libboost_regex.so;optimized;/usr/lib/x86_64-linux-gnu/libqhull.so;debug;/usr/lib/x86_64-linux-gnu/libqhull.so;general;/usr/lib/libOpenNI.so;general;/usr/lib/libOpenNI2.so;general;vtkChartsCore;general;vtkCommonColor;general;vtkCommonCore;general;vtksys;general;vtkCommonDataModel;general;vtkCommonMath;general;vtkCommonMisc;general;vtkCommonSystem;general;vtkCommonTransforms;general;vtkCommonExecutionModel;general;vtkFiltersGeneral;general;vtkCommonComputationalGeometry;general;vtkFiltersCore;general;vtkInfovisCore;general;vtkFiltersExtraction;general;vtkFiltersStatistics;general;vtkImagingFourier;general;vtkImagingCore;general;vtkalglib;general;vtkRenderingContext2D;general;vtkRenderingCore;general;vtkFiltersGeometry;general;vtkFiltersSources;general;vtkRenderingFreeType;general;/usr/lib/x86_64-linux-gnu/libfreetype.so;general;/usr/lib/x86_64-linux-gnu/libz.so;general;vtkFiltersModeling;general;vtkImagingSources;general;vtkInteractionStyle;general;vtkInteractionWidgets;general;vtkFiltersHybrid;general;vtkImagingColor;general;vtkImagingGeneral;general;vtkImagingHybrid;general;vtkIOImage;general;vtkDICOMParser;general;vtkmetaio;general;/usr/lib/x86_64-linux-gnu/libjpeg.so;general;/usr/lib/x86_64-linux-gnu/libpng.so;general;/usr/lib/x86_64-linux-gnu/libtiff.so;general;vtkRenderingAnnotation;general;vtkRenderingVolume;general;vtkIOXML;general;vtkIOCore;general;vtkIOXMLParser;general;/usr/lib/x86_64-linux-gnu/libexpat.so;general;vtkIOGeometry;general;vtkIOLegacy;general;vtkIOPLY;general;vtkRenderingLOD;general;vtkViewsContext2D;general;vtkViewsCore;general;vtkRenderingContextOpenGL2;general;vtkRenderingOpenGL2;general;FLANN::FLANN;general;spdlog;general;rclcpp::rclcpp;general;std_msgs::std_msgs__rosidl_generator_c;general;std_msgs::std_msgs__rosidl_typesupport_fastrtps_c;general;std_msgs::std_msgs__rosidl_typesupport_fastrtps_cpp;general;std_msgs::std_msgs__rosidl_typesupport_introspection_c;general;std_msgs::std_msgs__rosidl_typesupport_c;general;std_msgs::std_msgs__rosidl_typesupport_introspection_cpp;general;std_msgs::std_msgs__rosidl_typesupport_cpp;general;std_msgs::std_msgs__rosidl_generator_py;general;sensor_msgs::sensor_msgs__rosidl_generator_c;general;sensor_msgs::sensor_msgs__rosidl_typesupport_fastrtps_c;general;sensor_msgs::sensor_msgs__rosidl_typesupport_fastrtps_cpp;general;sensor_msgs::sensor_msgs__rosidl_typesupport_introspection_c;general;sensor_msgs::sensor_msgs__rosidl_typesupport_c;general;sensor_msgs::sensor_msgs__rosidl_typesupport_introspection_cpp;general;sensor_msgs::sensor_msgs__rosidl_typesupport_cpp;general;sensor_msgs::sensor_msgs__rosidl_generator_py;general;visualization_msgs::visualization_msgs__rosidl_generator_c;general;visualization_msgs::visualization_msgs__rosidl_typesupport_fastrtps_c;general;visualization_msgs::visualization_msgs__rosidl_typesupport_fastrtps_cpp;general;visualization_msgs::visualization_msgs__rosidl_typesupport_introspection_c;general;visualization_msgs::visualization_msgs__rosidl_typesupport_c;general;visualization_msgs::visualization_msgs__rosidl_typesupport_introspection_cpp;general;visualization_msgs::visualization_msgs__rosidl_typesupport_cpp;general;visualization_msgs::visualization_msgs__rosidl_generator_py;

//Value Computed by CMake
commonlibrary_SOURCE_DIR:STATIC=/home/<USER>/Code/autodrivingVersionTest/src/perception/commonlibrary

//The directory containing a CMake configuration file for fastcdr.
fastcdr_DIR:PATH=/home/<USER>/ros2_humble/install/fastcdr/lib/cmake/fastcdr

//The directory containing a CMake configuration file for fastrtps.
fastrtps_DIR:PATH=/home/<USER>/ros2_humble/install/fastrtps/share/fastrtps/cmake

//The directory containing a CMake configuration file for fastrtps_cmake_module.
fastrtps_cmake_module_DIR:PATH=/home/<USER>/ros2_humble/install/fastrtps_cmake_module/share/fastrtps_cmake_module/cmake

//The directory containing a CMake configuration file for flann.
flann_DIR:PATH=flann_DIR-NOTFOUND

//The directory containing a CMake configuration file for foonathan_memory.
foonathan_memory_DIR:PATH=/home/<USER>/ros2_humble/install/foonathan_memory_vendor/lib/foonathan_memory/cmake

//The directory containing a CMake configuration file for geometry_msgs.
geometry_msgs_DIR:PATH=/home/<USER>/ros2_humble/install/geometry_msgs/share/geometry_msgs/cmake

//The directory containing a CMake configuration file for libstatistics_collector.
libstatistics_collector_DIR:PATH=/home/<USER>/ros2_humble/install/libstatistics_collector/share/libstatistics_collector/cmake

//The directory containing a CMake configuration file for libyaml_vendor.
libyaml_vendor_DIR:PATH=/home/<USER>/ros2_humble/install/libyaml_vendor/share/libyaml_vendor/cmake

//Path to a library.
pkgcfg_lib_PC_FLANN_flann:FILEPATH=/usr/lib/x86_64-linux-gnu/libflann.so

//Path to a library.
pkgcfg_lib_PC_FLANN_flann_cpp:FILEPATH=/usr/lib/x86_64-linux-gnu/libflann_cpp.so

//Path to a library.
pkgcfg_lib_PC_FLANN_lz4:FILEPATH=/usr/lib/x86_64-linux-gnu/liblz4.so

//Path to a library.
pkgcfg_lib_PC_OPENNI2_OpenNI2:FILEPATH=/usr/lib/libOpenNI2.so

//Path to a library.
pkgcfg_lib_PC_OPENNI_OpenNI:FILEPATH=/usr/lib/libOpenNI.so

//Path to a library.
pkgcfg_lib_PC_USB_10_usb-1.0:FILEPATH=/usr/lib/x86_64-linux-gnu/libusb-1.0.so

//Path to a library.
pkgcfg_lib__OPENSSL_crypto:FILEPATH=/usr/lib/x86_64-linux-gnu/libcrypto.so

//Path to a library.
pkgcfg_lib__OPENSSL_ssl:FILEPATH=/usr/lib/x86_64-linux-gnu/libssl.so

//The directory containing a CMake configuration file for rcl.
rcl_DIR:PATH=/home/<USER>/ros2_humble/install/rcl/share/rcl/cmake

//The directory containing a CMake configuration file for rcl_interfaces.
rcl_interfaces_DIR:PATH=/home/<USER>/ros2_humble/install/rcl_interfaces/share/rcl_interfaces/cmake

//The directory containing a CMake configuration file for rcl_logging_interface.
rcl_logging_interface_DIR:PATH=/home/<USER>/ros2_humble/install/rcl_logging_interface/share/rcl_logging_interface/cmake

//The directory containing a CMake configuration file for rcl_logging_spdlog.
rcl_logging_spdlog_DIR:PATH=/home/<USER>/ros2_humble/install/rcl_logging_spdlog/share/rcl_logging_spdlog/cmake

//The directory containing a CMake configuration file for rcl_yaml_param_parser.
rcl_yaml_param_parser_DIR:PATH=/home/<USER>/ros2_humble/install/rcl_yaml_param_parser/share/rcl_yaml_param_parser/cmake

//The directory containing a CMake configuration file for rclcpp.
rclcpp_DIR:PATH=/home/<USER>/ros2_humble/install/rclcpp/share/rclcpp/cmake

//The directory containing a CMake configuration file for rcpputils.
rcpputils_DIR:PATH=/home/<USER>/ros2_humble/install/rcpputils/share/rcpputils/cmake

//The directory containing a CMake configuration file for rcutils.
rcutils_DIR:PATH=/home/<USER>/ros2_humble/install/rcutils/share/rcutils/cmake

//The directory containing a CMake configuration file for rmw.
rmw_DIR:PATH=/home/<USER>/ros2_humble/install/rmw/share/rmw/cmake

//The directory containing a CMake configuration file for rmw_dds_common.
rmw_dds_common_DIR:PATH=/home/<USER>/ros2_humble/install/rmw_dds_common/share/rmw_dds_common/cmake

//The directory containing a CMake configuration file for rmw_fastrtps_cpp.
rmw_fastrtps_cpp_DIR:PATH=/home/<USER>/ros2_humble/install/rmw_fastrtps_cpp/share/rmw_fastrtps_cpp/cmake

//The directory containing a CMake configuration file for rmw_fastrtps_shared_cpp.
rmw_fastrtps_shared_cpp_DIR:PATH=/home/<USER>/ros2_humble/install/rmw_fastrtps_shared_cpp/share/rmw_fastrtps_shared_cpp/cmake

//The directory containing a CMake configuration file for rmw_implementation.
rmw_implementation_DIR:PATH=/home/<USER>/ros2_humble/install/rmw_implementation/share/rmw_implementation/cmake

//The directory containing a CMake configuration file for rmw_implementation_cmake.
rmw_implementation_cmake_DIR:PATH=/home/<USER>/ros2_humble/install/rmw_implementation_cmake/share/rmw_implementation_cmake/cmake

//The directory containing a CMake configuration file for rosgraph_msgs.
rosgraph_msgs_DIR:PATH=/home/<USER>/ros2_humble/install/rosgraph_msgs/share/rosgraph_msgs/cmake

//The directory containing a CMake configuration file for rosidl_adapter.
rosidl_adapter_DIR:PATH=/home/<USER>/ros2_humble/install/rosidl_adapter/share/rosidl_adapter/cmake

//The directory containing a CMake configuration file for rosidl_cmake.
rosidl_cmake_DIR:PATH=/home/<USER>/ros2_humble/install/rosidl_cmake/share/rosidl_cmake/cmake

//The directory containing a CMake configuration file for rosidl_default_runtime.
rosidl_default_runtime_DIR:PATH=/home/<USER>/ros2_humble/install/rosidl_default_runtime/share/rosidl_default_runtime/cmake

//The directory containing a CMake configuration file for rosidl_generator_c.
rosidl_generator_c_DIR:PATH=/home/<USER>/ros2_humble/install/rosidl_generator_c/share/rosidl_generator_c/cmake

//The directory containing a CMake configuration file for rosidl_generator_cpp.
rosidl_generator_cpp_DIR:PATH=/home/<USER>/ros2_humble/install/rosidl_generator_cpp/share/rosidl_generator_cpp/cmake

//The directory containing a CMake configuration file for rosidl_runtime_c.
rosidl_runtime_c_DIR:PATH=/home/<USER>/ros2_humble/install/rosidl_runtime_c/share/rosidl_runtime_c/cmake

//The directory containing a CMake configuration file for rosidl_runtime_cpp.
rosidl_runtime_cpp_DIR:PATH=/home/<USER>/ros2_humble/install/rosidl_runtime_cpp/share/rosidl_runtime_cpp/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_c.
rosidl_typesupport_c_DIR:PATH=/home/<USER>/ros2_humble/install/rosidl_typesupport_c/share/rosidl_typesupport_c/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_cpp.
rosidl_typesupport_cpp_DIR:PATH=/home/<USER>/ros2_humble/install/rosidl_typesupport_cpp/share/rosidl_typesupport_cpp/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_fastrtps_c.
rosidl_typesupport_fastrtps_c_DIR:PATH=/home/<USER>/ros2_humble/install/rosidl_typesupport_fastrtps_c/share/rosidl_typesupport_fastrtps_c/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_fastrtps_cpp.
rosidl_typesupport_fastrtps_cpp_DIR:PATH=/home/<USER>/ros2_humble/install/rosidl_typesupport_fastrtps_cpp/share/rosidl_typesupport_fastrtps_cpp/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_interface.
rosidl_typesupport_interface_DIR:PATH=/home/<USER>/ros2_humble/install/rosidl_typesupport_interface/share/rosidl_typesupport_interface/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_introspection_c.
rosidl_typesupport_introspection_c_DIR:PATH=/home/<USER>/ros2_humble/install/rosidl_typesupport_introspection_c/share/rosidl_typesupport_introspection_c/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_introspection_cpp.
rosidl_typesupport_introspection_cpp_DIR:PATH=/home/<USER>/ros2_humble/install/rosidl_typesupport_introspection_cpp/share/rosidl_typesupport_introspection_cpp/cmake

//The directory containing a CMake configuration file for sensor_msgs.
sensor_msgs_DIR:PATH=/home/<USER>/ros2_humble/install/sensor_msgs/share/sensor_msgs/cmake

//The directory containing a CMake configuration file for spdlog.
spdlog_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/spdlog

//The directory containing a CMake configuration file for spdlog_vendor.
spdlog_vendor_DIR:PATH=/home/<USER>/ros2_humble/install/spdlog_vendor/share/spdlog_vendor/cmake

//The directory containing a CMake configuration file for statistics_msgs.
statistics_msgs_DIR:PATH=/home/<USER>/ros2_humble/install/statistics_msgs/share/statistics_msgs/cmake

//The directory containing a CMake configuration file for std_msgs.
std_msgs_DIR:PATH=/home/<USER>/ros2_humble/install/std_msgs/share/std_msgs/cmake

//The directory containing a CMake configuration file for tracetools.
tracetools_DIR:PATH=/home/<USER>/ros2_humble/install/tracetools/share/tracetools/cmake

//The directory containing a CMake configuration file for visualization_msgs.
visualization_msgs_DIR:PATH=/home/<USER>/ros2_humble/install/visualization_msgs/share/visualization_msgs/cmake

//The directory containing a CMake configuration file for yaml.
yaml_DIR:PATH=/home/<USER>/ros2_humble/install/libyaml_vendor/cmake


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: Boost_DATE_TIME_LIBRARY_DEBUG
Boost_DATE_TIME_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_DATE_TIME_LIBRARY_RELEASE
Boost_DATE_TIME_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_FILESYSTEM_LIBRARY_DEBUG
Boost_FILESYSTEM_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_FILESYSTEM_LIBRARY_RELEASE
Boost_FILESYSTEM_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_INCLUDE_DIR
Boost_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_IOSTREAMS_LIBRARY_DEBUG
Boost_IOSTREAMS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_IOSTREAMS_LIBRARY_RELEASE
Boost_IOSTREAMS_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_LIBRARY_DIR_DEBUG
Boost_LIBRARY_DIR_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_LIBRARY_DIR_RELEASE
Boost_LIBRARY_DIR_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_REGEX_LIBRARY_DEBUG
Boost_REGEX_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_REGEX_LIBRARY_RELEASE
Boost_REGEX_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_SYSTEM_LIBRARY_DEBUG
Boost_SYSTEM_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_SYSTEM_LIBRARY_RELEASE
Boost_SYSTEM_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/home/<USER>/Code/autodrivingVersionTest/src/build/commonlibrary
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=16
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=3
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/usr/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/usr/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/usr/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/usr/bin/cmake-gui
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=
//Have library pthreads
CMAKE_HAVE_PTHREADS_CREATE:INTERNAL=
//Have library pthread
CMAKE_HAVE_PTHREAD_CREATE:INTERNAL=1
//Have include pthread.h
CMAKE_HAVE_PTHREAD_H:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/home/<USER>/Code/autodrivingVersionTest/src/perception/commonlibrary
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/usr/share/cmake-3.16
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: EIGEN_INCLUDE_DIR
EIGEN_INCLUDE_DIR-ADVANCED:INTERNAL=1
//Details about finding Boost
FIND_PACKAGE_MESSAGE_DETAILS_Boost:INTERNAL=[/usr/include][cfound components: system filesystem date_time iostreams regex ][v1.71.0(1.55.0)]
//Details about finding Eigen
FIND_PACKAGE_MESSAGE_DETAILS_Eigen:INTERNAL=[/usr/include/eigen3][v(3.1)]
//Details about finding FLANN
FIND_PACKAGE_MESSAGE_DETAILS_FLANN:INTERNAL=[/usr/lib/x86_64-linux-gnu/libflann_cpp.so][/usr/include][v()]
//Details about finding FastRTPS
FIND_PACKAGE_MESSAGE_DETAILS_FastRTPS:INTERNAL=[/home/<USER>/ros2_humble/install/fastrtps/include][/home/<USER>/ros2_humble/install/fastrtps/lib/libfastrtps.so;/home/<USER>/ros2_humble/install/fastcdr/lib/libfastcdr.so][v()]
//Details about finding OpenCV
FIND_PACKAGE_MESSAGE_DETAILS_OpenCV:INTERNAL=[/usr/local][v3.4.1()]
//Details about finding OpenNI
FIND_PACKAGE_MESSAGE_DETAILS_OpenNI:INTERNAL=[/usr/lib/libOpenNI.so][/usr/include/ni][v()]
//Details about finding OpenNI2
FIND_PACKAGE_MESSAGE_DETAILS_OpenNI2:INTERNAL=[/usr/lib/libOpenNI2.so][/usr/include/openni2][v()]
//Details about finding OpenSSL
FIND_PACKAGE_MESSAGE_DETAILS_OpenSSL:INTERNAL=[/usr/lib/x86_64-linux-gnu/libcrypto.so][/usr/include][c ][v1.1.1f()]
//Details about finding PCL
FIND_PACKAGE_MESSAGE_DETAILS_PCL:INTERNAL=[pcl_common;pcl_kdtree;pcl_octree;pcl_search;pcl_sample_consensus;pcl_filters;pcl_io;pcl_features;pcl_ml;pcl_segmentation;pcl_visualization;pcl_surface;pcl_registration;pcl_keypoints;pcl_tracking;pcl_recognition;pcl_stereo;pcl_apps;pcl_outofcore;pcl_people;/usr/lib/x86_64-linux-gnu/libboost_system.so;/usr/lib/x86_64-linux-gnu/libboost_filesystem.so;/usr/lib/x86_64-linux-gnu/libboost_date_time.so;/usr/lib/x86_64-linux-gnu/libboost_iostreams.so;/usr/lib/x86_64-linux-gnu/libboost_regex.so;optimized;/usr/lib/x86_64-linux-gnu/libqhull.so;debug;/usr/lib/x86_64-linux-gnu/libqhull.so;/usr/lib/libOpenNI.so;/usr/lib/libOpenNI2.so;vtkChartsCore;vtkCommonColor;vtkCommonCore;vtksys;vtkCommonDataModel;vtkCommonMath;vtkCommonMisc;vtkCommonSystem;vtkCommonTransforms;vtkCommonExecutionModel;vtkFiltersGeneral;vtkCommonComputationalGeometry;vtkFiltersCore;vtkInfovisCore;vtkFiltersExtraction;vtkFiltersStatistics;vtkImagingFourier;vtkImagingCore;vtkalglib;vtkRenderingContext2D;vtkRenderingCore;vtkFiltersGeometry;vtkFiltersSources;vtkRenderingFreeType;/usr/lib/x86_64-linux-gnu/libfreetype.so;/usr/lib/x86_64-linux-gnu/libz.so;vtkFiltersModeling;vtkImagingSources;vtkInteractionStyle;vtkInteractionWidgets;vtkFiltersHybrid;vtkImagingColor;vtkImagingGeneral;vtkImagingHybrid;vtkIOImage;vtkDICOMParser;vtkmetaio;/usr/lib/x86_64-linux-gnu/libjpeg.so;/usr/lib/x86_64-linux-gnu/libpng.so;/usr/lib/x86_64-linux-gnu/libtiff.so;vtkRenderingAnnotation;vtkRenderingVolume;vtkIOXML;vtkIOCore;vtkIOXMLParser;/usr/lib/x86_64-linux-gnu/libexpat.so;vtkIOGeometry;vtkIOLegacy;vtkIOPLY;vtkRenderingLOD;vtkViewsContext2D;vtkViewsCore;vtkRenderingContextOpenGL2;vtkRenderingOpenGL2;FLANN::FLANN][/usr/include/pcl-1.10;/usr/include/eigen3;/usr/include;/usr/include/vtk-7.1;/usr/include/freetype2;/usr/include/x86_64-linux-gnu;/usr/include/ni;/usr/include/openni2][v()]
//Details about finding PCL_2D
FIND_PACKAGE_MESSAGE_DETAILS_PCL_2D:INTERNAL=[/usr/include/pcl-1.10][v()]
//Details about finding PCL_APPS
FIND_PACKAGE_MESSAGE_DETAILS_PCL_APPS:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_apps.so][/usr/include/pcl-1.10][v()]
//Details about finding PCL_COMMON
FIND_PACKAGE_MESSAGE_DETAILS_PCL_COMMON:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_common.so][/usr/include/pcl-1.10][v()]
//Details about finding PCL_FEATURES
FIND_PACKAGE_MESSAGE_DETAILS_PCL_FEATURES:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_features.so][/usr/include/pcl-1.10][v()]
//Details about finding PCL_FILTERS
FIND_PACKAGE_MESSAGE_DETAILS_PCL_FILTERS:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_filters.so][/usr/include/pcl-1.10][v()]
//Details about finding PCL_GEOMETRY
FIND_PACKAGE_MESSAGE_DETAILS_PCL_GEOMETRY:INTERNAL=[/usr/include/pcl-1.10][v()]
//Details about finding PCL_IN_HAND_SCANNER
FIND_PACKAGE_MESSAGE_DETAILS_PCL_IN_HAND_SCANNER:INTERNAL=[/usr/include/pcl-1.10][v()]
//Details about finding PCL_IO
FIND_PACKAGE_MESSAGE_DETAILS_PCL_IO:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_io.so][/usr/include/pcl-1.10][v()]
//Details about finding PCL_KDTREE
FIND_PACKAGE_MESSAGE_DETAILS_PCL_KDTREE:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_kdtree.so][/usr/include/pcl-1.10][v()]
//Details about finding PCL_KEYPOINTS
FIND_PACKAGE_MESSAGE_DETAILS_PCL_KEYPOINTS:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_keypoints.so][/usr/include/pcl-1.10][v()]
//Details about finding PCL_ML
FIND_PACKAGE_MESSAGE_DETAILS_PCL_ML:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_ml.so][/usr/include/pcl-1.10][v()]
//Details about finding PCL_OCTREE
FIND_PACKAGE_MESSAGE_DETAILS_PCL_OCTREE:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_octree.so][/usr/include/pcl-1.10][v()]
//Details about finding PCL_OUTOFCORE
FIND_PACKAGE_MESSAGE_DETAILS_PCL_OUTOFCORE:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_outofcore.so][/usr/include/pcl-1.10][v()]
//Details about finding PCL_PEOPLE
FIND_PACKAGE_MESSAGE_DETAILS_PCL_PEOPLE:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_people.so][/usr/include/pcl-1.10][v()]
//Details about finding PCL_POINT_CLOUD_EDITOR
FIND_PACKAGE_MESSAGE_DETAILS_PCL_POINT_CLOUD_EDITOR:INTERNAL=[/usr/include/pcl-1.10][v()]
//Details about finding PCL_RECOGNITION
FIND_PACKAGE_MESSAGE_DETAILS_PCL_RECOGNITION:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_recognition.so][/usr/include/pcl-1.10][v()]
//Details about finding PCL_REGISTRATION
FIND_PACKAGE_MESSAGE_DETAILS_PCL_REGISTRATION:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_registration.so][/usr/include/pcl-1.10][v()]
//Details about finding PCL_SAMPLE_CONSENSUS
FIND_PACKAGE_MESSAGE_DETAILS_PCL_SAMPLE_CONSENSUS:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so][/usr/include/pcl-1.10][v()]
//Details about finding PCL_SEARCH
FIND_PACKAGE_MESSAGE_DETAILS_PCL_SEARCH:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_search.so][/usr/include/pcl-1.10][v()]
//Details about finding PCL_SEGMENTATION
FIND_PACKAGE_MESSAGE_DETAILS_PCL_SEGMENTATION:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_segmentation.so][/usr/include/pcl-1.10][v()]
//Details about finding PCL_STEREO
FIND_PACKAGE_MESSAGE_DETAILS_PCL_STEREO:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_stereo.so][/usr/include/pcl-1.10][v()]
//Details about finding PCL_SURFACE
FIND_PACKAGE_MESSAGE_DETAILS_PCL_SURFACE:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_surface.so][/usr/include/pcl-1.10][v()]
//Details about finding PCL_TRACKING
FIND_PACKAGE_MESSAGE_DETAILS_PCL_TRACKING:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_tracking.so][/usr/include/pcl-1.10][v()]
//Details about finding PCL_VISUALIZATION
FIND_PACKAGE_MESSAGE_DETAILS_PCL_VISUALIZATION:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_visualization.so][/usr/include/pcl-1.10][v()]
//Details about finding Python3
FIND_PACKAGE_MESSAGE_DETAILS_Python3:INTERNAL=[/usr/bin/python3][cfound components: Interpreter ][v3.8.10()]
//Details about finding Qhull
FIND_PACKAGE_MESSAGE_DETAILS_Qhull:INTERNAL=[optimized;/usr/lib/x86_64-linux-gnu/libqhull.so;debug;/usr/lib/x86_64-linux-gnu/libqhull.so][/usr/include][v()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//Details about finding USB_10
FIND_PACKAGE_MESSAGE_DETAILS_USB_10:INTERNAL=[/usr/lib/x86_64-linux-gnu/libusb-1.0.so][/usr/include][v()]
//Details about finding libusb-1.0
FIND_PACKAGE_MESSAGE_DETAILS_libusb-1.0:INTERNAL=[/usr/include][v()]
//ADVANCED property for variable: OPENSSL_CRYPTO_LIBRARY
OPENSSL_CRYPTO_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENSSL_INCLUDE_DIR
OPENSSL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENSSL_SSL_LIBRARY
OPENSSL_SSL_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_2D_INCLUDE_DIR
PCL_2D_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_APPS_INCLUDE_DIR
PCL_APPS_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_APPS_LIBRARY
PCL_APPS_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_APPS_LIBRARY_DEBUG
PCL_APPS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_COMMON_INCLUDE_DIR
PCL_COMMON_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_COMMON_LIBRARY
PCL_COMMON_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_COMMON_LIBRARY_DEBUG
PCL_COMMON_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_FEATURES_INCLUDE_DIR
PCL_FEATURES_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_FEATURES_LIBRARY
PCL_FEATURES_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_FEATURES_LIBRARY_DEBUG
PCL_FEATURES_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_FILTERS_INCLUDE_DIR
PCL_FILTERS_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_FILTERS_LIBRARY
PCL_FILTERS_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_FILTERS_LIBRARY_DEBUG
PCL_FILTERS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_GEOMETRY_INCLUDE_DIR
PCL_GEOMETRY_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_IN_HAND_SCANNER_INCLUDE_DIR
PCL_IN_HAND_SCANNER_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_IO_INCLUDE_DIR
PCL_IO_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_IO_LIBRARY
PCL_IO_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_IO_LIBRARY_DEBUG
PCL_IO_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_KDTREE_INCLUDE_DIR
PCL_KDTREE_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_KDTREE_LIBRARY
PCL_KDTREE_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_KDTREE_LIBRARY_DEBUG
PCL_KDTREE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_KEYPOINTS_INCLUDE_DIR
PCL_KEYPOINTS_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_KEYPOINTS_LIBRARY
PCL_KEYPOINTS_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_KEYPOINTS_LIBRARY_DEBUG
PCL_KEYPOINTS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_ML_INCLUDE_DIR
PCL_ML_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_ML_LIBRARY
PCL_ML_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_ML_LIBRARY_DEBUG
PCL_ML_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_OCTREE_INCLUDE_DIR
PCL_OCTREE_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_OCTREE_LIBRARY
PCL_OCTREE_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_OCTREE_LIBRARY_DEBUG
PCL_OCTREE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_OUTOFCORE_INCLUDE_DIR
PCL_OUTOFCORE_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_OUTOFCORE_LIBRARY
PCL_OUTOFCORE_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_OUTOFCORE_LIBRARY_DEBUG
PCL_OUTOFCORE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_PEOPLE_INCLUDE_DIR
PCL_PEOPLE_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_PEOPLE_LIBRARY
PCL_PEOPLE_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_PEOPLE_LIBRARY_DEBUG
PCL_PEOPLE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_POINT_CLOUD_EDITOR_INCLUDE_DIR
PCL_POINT_CLOUD_EDITOR_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_RECOGNITION_INCLUDE_DIR
PCL_RECOGNITION_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_RECOGNITION_LIBRARY
PCL_RECOGNITION_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_RECOGNITION_LIBRARY_DEBUG
PCL_RECOGNITION_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_REGISTRATION_INCLUDE_DIR
PCL_REGISTRATION_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_REGISTRATION_LIBRARY
PCL_REGISTRATION_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_REGISTRATION_LIBRARY_DEBUG
PCL_REGISTRATION_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SAMPLE_CONSENSUS_INCLUDE_DIR
PCL_SAMPLE_CONSENSUS_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SAMPLE_CONSENSUS_LIBRARY
PCL_SAMPLE_CONSENSUS_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SAMPLE_CONSENSUS_LIBRARY_DEBUG
PCL_SAMPLE_CONSENSUS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SEARCH_INCLUDE_DIR
PCL_SEARCH_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SEARCH_LIBRARY
PCL_SEARCH_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SEARCH_LIBRARY_DEBUG
PCL_SEARCH_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SEGMENTATION_INCLUDE_DIR
PCL_SEGMENTATION_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SEGMENTATION_LIBRARY
PCL_SEGMENTATION_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SEGMENTATION_LIBRARY_DEBUG
PCL_SEGMENTATION_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_STEREO_INCLUDE_DIR
PCL_STEREO_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_STEREO_LIBRARY
PCL_STEREO_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_STEREO_LIBRARY_DEBUG
PCL_STEREO_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SURFACE_INCLUDE_DIR
PCL_SURFACE_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SURFACE_LIBRARY
PCL_SURFACE_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SURFACE_LIBRARY_DEBUG
PCL_SURFACE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_TRACKING_INCLUDE_DIR
PCL_TRACKING_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_TRACKING_LIBRARY
PCL_TRACKING_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_TRACKING_LIBRARY_DEBUG
PCL_TRACKING_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_VISUALIZATION_INCLUDE_DIR
PCL_VISUALIZATION_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_VISUALIZATION_LIBRARY
PCL_VISUALIZATION_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_VISUALIZATION_LIBRARY_DEBUG
PCL_VISUALIZATION_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
PC_EIGEN_CFLAGS:INTERNAL=-I/usr/include/eigen3
PC_EIGEN_CFLAGS_I:INTERNAL=
PC_EIGEN_CFLAGS_OTHER:INTERNAL=
PC_EIGEN_FOUND:INTERNAL=1
PC_EIGEN_INCLUDEDIR:INTERNAL=
PC_EIGEN_INCLUDE_DIRS:INTERNAL=/usr/include/eigen3
PC_EIGEN_LDFLAGS:INTERNAL=
PC_EIGEN_LDFLAGS_OTHER:INTERNAL=
PC_EIGEN_LIBDIR:INTERNAL=
PC_EIGEN_LIBRARIES:INTERNAL=
PC_EIGEN_LIBRARY_DIRS:INTERNAL=
PC_EIGEN_LIBS:INTERNAL=
PC_EIGEN_LIBS_L:INTERNAL=
PC_EIGEN_LIBS_OTHER:INTERNAL=
PC_EIGEN_LIBS_PATHS:INTERNAL=
PC_EIGEN_MODULE_NAME:INTERNAL=eigen3
PC_EIGEN_PREFIX:INTERNAL=/usr
PC_EIGEN_STATIC_CFLAGS:INTERNAL=-I/usr/include/eigen3
PC_EIGEN_STATIC_CFLAGS_I:INTERNAL=
PC_EIGEN_STATIC_CFLAGS_OTHER:INTERNAL=
PC_EIGEN_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/eigen3
PC_EIGEN_STATIC_LDFLAGS:INTERNAL=
PC_EIGEN_STATIC_LDFLAGS_OTHER:INTERNAL=
PC_EIGEN_STATIC_LIBDIR:INTERNAL=
PC_EIGEN_STATIC_LIBRARIES:INTERNAL=
PC_EIGEN_STATIC_LIBRARY_DIRS:INTERNAL=
PC_EIGEN_STATIC_LIBS:INTERNAL=
PC_EIGEN_STATIC_LIBS_L:INTERNAL=
PC_EIGEN_STATIC_LIBS_OTHER:INTERNAL=
PC_EIGEN_STATIC_LIBS_PATHS:INTERNAL=
PC_EIGEN_VERSION:INTERNAL=3.3.7
PC_EIGEN_eigen3_INCLUDEDIR:INTERNAL=
PC_EIGEN_eigen3_LIBDIR:INTERNAL=
PC_EIGEN_eigen3_PREFIX:INTERNAL=
PC_EIGEN_eigen3_VERSION:INTERNAL=
PC_FLANN_CFLAGS:INTERNAL=
PC_FLANN_CFLAGS_I:INTERNAL=
PC_FLANN_CFLAGS_OTHER:INTERNAL=
PC_FLANN_FOUND:INTERNAL=1
PC_FLANN_INCLUDEDIR:INTERNAL=/usr/include
PC_FLANN_INCLUDE_DIRS:INTERNAL=
PC_FLANN_LDFLAGS:INTERNAL=-llz4;-lflann;-lflann_cpp
PC_FLANN_LDFLAGS_OTHER:INTERNAL=
PC_FLANN_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
PC_FLANN_LIBRARIES:INTERNAL=lz4;flann;flann_cpp
PC_FLANN_LIBRARY_DIRS:INTERNAL=
PC_FLANN_LIBS:INTERNAL=
PC_FLANN_LIBS_L:INTERNAL=
PC_FLANN_LIBS_OTHER:INTERNAL=
PC_FLANN_LIBS_PATHS:INTERNAL=
PC_FLANN_MODULE_NAME:INTERNAL=flann
PC_FLANN_PREFIX:INTERNAL=/usr
PC_FLANN_STATIC_CFLAGS:INTERNAL=
PC_FLANN_STATIC_CFLAGS_I:INTERNAL=
PC_FLANN_STATIC_CFLAGS_OTHER:INTERNAL=
PC_FLANN_STATIC_INCLUDE_DIRS:INTERNAL=
PC_FLANN_STATIC_LDFLAGS:INTERNAL=-llz4;-lflann;-lflann_cpp
PC_FLANN_STATIC_LDFLAGS_OTHER:INTERNAL=
PC_FLANN_STATIC_LIBDIR:INTERNAL=
PC_FLANN_STATIC_LIBRARIES:INTERNAL=lz4;flann;flann_cpp
PC_FLANN_STATIC_LIBRARY_DIRS:INTERNAL=
PC_FLANN_STATIC_LIBS:INTERNAL=
PC_FLANN_STATIC_LIBS_L:INTERNAL=
PC_FLANN_STATIC_LIBS_OTHER:INTERNAL=
PC_FLANN_STATIC_LIBS_PATHS:INTERNAL=
PC_FLANN_VERSION:INTERNAL=1.9.1
PC_FLANN_flann_INCLUDEDIR:INTERNAL=
PC_FLANN_flann_LIBDIR:INTERNAL=
PC_FLANN_flann_PREFIX:INTERNAL=
PC_FLANN_flann_VERSION:INTERNAL=
PC_OPENNI2_CFLAGS:INTERNAL=-I/usr/include/openni2
PC_OPENNI2_CFLAGS_I:INTERNAL=
PC_OPENNI2_CFLAGS_OTHER:INTERNAL=
PC_OPENNI2_FOUND:INTERNAL=1
PC_OPENNI2_INCLUDEDIR:INTERNAL=/usr/include/openni2
PC_OPENNI2_INCLUDE_DIRS:INTERNAL=/usr/include/openni2
PC_OPENNI2_LDFLAGS:INTERNAL=-lOpenNI2
PC_OPENNI2_LDFLAGS_OTHER:INTERNAL=
PC_OPENNI2_LIBDIR:INTERNAL=/usr/lib
PC_OPENNI2_LIBRARIES:INTERNAL=OpenNI2
PC_OPENNI2_LIBRARY_DIRS:INTERNAL=
PC_OPENNI2_LIBS:INTERNAL=
PC_OPENNI2_LIBS_L:INTERNAL=
PC_OPENNI2_LIBS_OTHER:INTERNAL=
PC_OPENNI2_LIBS_PATHS:INTERNAL=
PC_OPENNI2_MODULE_NAME:INTERNAL=libopenni2
PC_OPENNI2_PREFIX:INTERNAL=/usr
PC_OPENNI2_STATIC_CFLAGS:INTERNAL=-I/usr/include/openni2
PC_OPENNI2_STATIC_CFLAGS_I:INTERNAL=
PC_OPENNI2_STATIC_CFLAGS_OTHER:INTERNAL=
PC_OPENNI2_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/openni2
PC_OPENNI2_STATIC_LDFLAGS:INTERNAL=-lOpenNI2
PC_OPENNI2_STATIC_LDFLAGS_OTHER:INTERNAL=
PC_OPENNI2_STATIC_LIBDIR:INTERNAL=
PC_OPENNI2_STATIC_LIBRARIES:INTERNAL=OpenNI2
PC_OPENNI2_STATIC_LIBRARY_DIRS:INTERNAL=
PC_OPENNI2_STATIC_LIBS:INTERNAL=
PC_OPENNI2_STATIC_LIBS_L:INTERNAL=
PC_OPENNI2_STATIC_LIBS_OTHER:INTERNAL=
PC_OPENNI2_STATIC_LIBS_PATHS:INTERNAL=
PC_OPENNI2_VERSION:INTERNAL=2.2.0.3
PC_OPENNI2_libopenni2_INCLUDEDIR:INTERNAL=
PC_OPENNI2_libopenni2_LIBDIR:INTERNAL=
PC_OPENNI2_libopenni2_PREFIX:INTERNAL=
PC_OPENNI2_libopenni2_VERSION:INTERNAL=
PC_OPENNI_CFLAGS:INTERNAL=-I/usr/include/ni
PC_OPENNI_CFLAGS_I:INTERNAL=
PC_OPENNI_CFLAGS_OTHER:INTERNAL=
PC_OPENNI_FOUND:INTERNAL=1
PC_OPENNI_INCLUDEDIR:INTERNAL=/usr/include/ni
PC_OPENNI_INCLUDE_DIRS:INTERNAL=/usr/include/ni
PC_OPENNI_LDFLAGS:INTERNAL=-lOpenNI
PC_OPENNI_LDFLAGS_OTHER:INTERNAL=
PC_OPENNI_LIBDIR:INTERNAL=/usr/lib
PC_OPENNI_LIBRARIES:INTERNAL=OpenNI
PC_OPENNI_LIBRARY_DIRS:INTERNAL=
PC_OPENNI_LIBS:INTERNAL=
PC_OPENNI_LIBS_L:INTERNAL=
PC_OPENNI_LIBS_OTHER:INTERNAL=
PC_OPENNI_LIBS_PATHS:INTERNAL=
PC_OPENNI_MODULE_NAME:INTERNAL=libopenni
PC_OPENNI_PREFIX:INTERNAL=/usr
PC_OPENNI_STATIC_CFLAGS:INTERNAL=-I/usr/include/ni
PC_OPENNI_STATIC_CFLAGS_I:INTERNAL=
PC_OPENNI_STATIC_CFLAGS_OTHER:INTERNAL=
PC_OPENNI_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/ni
PC_OPENNI_STATIC_LDFLAGS:INTERNAL=-lOpenNI
PC_OPENNI_STATIC_LDFLAGS_OTHER:INTERNAL=
PC_OPENNI_STATIC_LIBDIR:INTERNAL=
PC_OPENNI_STATIC_LIBRARIES:INTERNAL=OpenNI
PC_OPENNI_STATIC_LIBRARY_DIRS:INTERNAL=
PC_OPENNI_STATIC_LIBS:INTERNAL=
PC_OPENNI_STATIC_LIBS_L:INTERNAL=
PC_OPENNI_STATIC_LIBS_OTHER:INTERNAL=
PC_OPENNI_STATIC_LIBS_PATHS:INTERNAL=
PC_OPENNI_VERSION:INTERNAL=1.5.4.0
PC_OPENNI_libopenni_INCLUDEDIR:INTERNAL=
PC_OPENNI_libopenni_LIBDIR:INTERNAL=
PC_OPENNI_libopenni_PREFIX:INTERNAL=
PC_OPENNI_libopenni_VERSION:INTERNAL=
PC_USB_10_CFLAGS:INTERNAL=-I/usr/include/libusb-1.0
PC_USB_10_CFLAGS_I:INTERNAL=
PC_USB_10_CFLAGS_OTHER:INTERNAL=
PC_USB_10_FOUND:INTERNAL=1
PC_USB_10_INCLUDEDIR:INTERNAL=/usr/include
PC_USB_10_INCLUDE_DIRS:INTERNAL=/usr/include/libusb-1.0
PC_USB_10_LDFLAGS:INTERNAL=-lusb-1.0
PC_USB_10_LDFLAGS_OTHER:INTERNAL=
PC_USB_10_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
PC_USB_10_LIBRARIES:INTERNAL=usb-1.0
PC_USB_10_LIBRARY_DIRS:INTERNAL=
PC_USB_10_LIBS:INTERNAL=
PC_USB_10_LIBS_L:INTERNAL=
PC_USB_10_LIBS_OTHER:INTERNAL=
PC_USB_10_LIBS_PATHS:INTERNAL=
PC_USB_10_MODULE_NAME:INTERNAL=libusb-1.0
PC_USB_10_PREFIX:INTERNAL=/usr
PC_USB_10_STATIC_CFLAGS:INTERNAL=-I/usr/include/libusb-1.0
PC_USB_10_STATIC_CFLAGS_I:INTERNAL=
PC_USB_10_STATIC_CFLAGS_OTHER:INTERNAL=
PC_USB_10_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/libusb-1.0
PC_USB_10_STATIC_LDFLAGS:INTERNAL=-lusb-1.0;-ludev;-pthread
PC_USB_10_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread
PC_USB_10_STATIC_LIBDIR:INTERNAL=
PC_USB_10_STATIC_LIBRARIES:INTERNAL=usb-1.0;udev
PC_USB_10_STATIC_LIBRARY_DIRS:INTERNAL=
PC_USB_10_STATIC_LIBS:INTERNAL=
PC_USB_10_STATIC_LIBS_L:INTERNAL=
PC_USB_10_STATIC_LIBS_OTHER:INTERNAL=
PC_USB_10_STATIC_LIBS_PATHS:INTERNAL=
PC_USB_10_VERSION:INTERNAL=1.0.23
PC_USB_10_libusb-1.0_INCLUDEDIR:INTERNAL=
PC_USB_10_libusb-1.0_LIBDIR:INTERNAL=
PC_USB_10_libusb-1.0_PREFIX:INTERNAL=
PC_USB_10_libusb-1.0_VERSION:INTERNAL=
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
//QHull header
QHULL_HEADER:INTERNAL=/usr/include/libqhull/libqhull.h
//Last used BOOST_INCLUDEDIR value.
_BOOST_INCLUDEDIR_LAST:INTERNAL=/usr/include
//Last used Boost_ADDITIONAL_VERSIONS value.
_Boost_ADDITIONAL_VERSIONS_LAST:INTERNAL=1.71.0;1.71;1.71.0;1.71;1.70.0;1.70;1.69.0;1.69;1.68.0;1.68;1.67.0;1.67;1.66.0;1.66;1.65.1;1.65.0;1.65;1.64.0;1.64;1.63.0;1.63;1.62.0;1.62;1.61.0;1.61;1.60.0;1.60;1.59.0;1.59;1.58.0;1.58;1.57.0;1.57;1.56.0;1.56;1.55.0;1.55
//Components requested for this build tree.
_Boost_COMPONENTS_SEARCHED:INTERNAL=date_time;filesystem;iostreams;regex;system
//Last used Boost_INCLUDE_DIR value.
_Boost_INCLUDE_DIR_LAST:INTERNAL=/usr/include
//Last used Boost_LIBRARY_DIR_DEBUG value.
_Boost_LIBRARY_DIR_DEBUG_LAST:INTERNAL=/usr/lib/x86_64-linux-gnu
//Last used Boost_LIBRARY_DIR_RELEASE value.
_Boost_LIBRARY_DIR_RELEASE_LAST:INTERNAL=/usr/lib/x86_64-linux-gnu
//Last used Boost_NAMESPACE value.
_Boost_NAMESPACE_LAST:INTERNAL=boost
//Last used Boost_USE_MULTITHREADED value.
_Boost_USE_MULTITHREADED_LAST:INTERNAL=TRUE
_OPENSSL_CFLAGS:INTERNAL=
_OPENSSL_CFLAGS_I:INTERNAL=
_OPENSSL_CFLAGS_OTHER:INTERNAL=
_OPENSSL_FOUND:INTERNAL=1
_OPENSSL_INCLUDEDIR:INTERNAL=/usr/include
_OPENSSL_INCLUDE_DIRS:INTERNAL=
_OPENSSL_LDFLAGS:INTERNAL=-lssl;-lcrypto
_OPENSSL_LDFLAGS_OTHER:INTERNAL=
_OPENSSL_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
_OPENSSL_LIBRARIES:INTERNAL=ssl;crypto
_OPENSSL_LIBRARY_DIRS:INTERNAL=
_OPENSSL_LIBS:INTERNAL=
_OPENSSL_LIBS_L:INTERNAL=
_OPENSSL_LIBS_OTHER:INTERNAL=
_OPENSSL_LIBS_PATHS:INTERNAL=
_OPENSSL_MODULE_NAME:INTERNAL=openssl
_OPENSSL_PREFIX:INTERNAL=/usr
_OPENSSL_STATIC_CFLAGS:INTERNAL=
_OPENSSL_STATIC_CFLAGS_I:INTERNAL=
_OPENSSL_STATIC_CFLAGS_OTHER:INTERNAL=
_OPENSSL_STATIC_INCLUDE_DIRS:INTERNAL=
_OPENSSL_STATIC_LDFLAGS:INTERNAL=-lssl;-lcrypto;-ldl;-pthread
_OPENSSL_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread
_OPENSSL_STATIC_LIBDIR:INTERNAL=
_OPENSSL_STATIC_LIBRARIES:INTERNAL=ssl;crypto;dl
_OPENSSL_STATIC_LIBRARY_DIRS:INTERNAL=
_OPENSSL_STATIC_LIBS:INTERNAL=
_OPENSSL_STATIC_LIBS_L:INTERNAL=
_OPENSSL_STATIC_LIBS_OTHER:INTERNAL=
_OPENSSL_STATIC_LIBS_PATHS:INTERNAL=
_OPENSSL_VERSION:INTERNAL=1.1.1f
_OPENSSL_openssl_INCLUDEDIR:INTERNAL=
_OPENSSL_openssl_LIBDIR:INTERNAL=
_OPENSSL_openssl_PREFIX:INTERNAL=
_OPENSSL_openssl_VERSION:INTERNAL=
//ADVANCED property for variable: _Python3_EXECUTABLE
_Python3_EXECUTABLE-ADVANCED:INTERNAL=1
_Python3_EXECUTABLE:INTERNAL=/usr/bin/python3
//ADVANCED property for variable: _Python3_INTERPRETER_SIGNATURE
_Python3_INTERPRETER_SIGNATURE-ADVANCED:INTERNAL=1
_Python3_INTERPRETER_SIGNATURE:INTERNAL=bab8af3765305e7bd993d9eb6192b421
__pkg_config_arguments_PC_EIGEN:INTERNAL=eigen3
__pkg_config_arguments_PC_FLANN:INTERNAL=flann
__pkg_config_arguments_PC_OPENNI:INTERNAL=QUIET;libopenni
__pkg_config_arguments_PC_OPENNI2:INTERNAL=QUIET;libopenni2
__pkg_config_arguments_PC_USB_10:INTERNAL=libusb-1.0
__pkg_config_arguments__OPENSSL:INTERNAL=QUIET;openssl
__pkg_config_checked_PC_EIGEN:INTERNAL=1
__pkg_config_checked_PC_FLANN:INTERNAL=1
__pkg_config_checked_PC_OPENNI:INTERNAL=1
__pkg_config_checked_PC_OPENNI2:INTERNAL=1
__pkg_config_checked_PC_USB_10:INTERNAL=1
__pkg_config_checked__OPENSSL:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_FLANN_flann
pkgcfg_lib_PC_FLANN_flann-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_FLANN_flann_cpp
pkgcfg_lib_PC_FLANN_flann_cpp-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_FLANN_lz4
pkgcfg_lib_PC_FLANN_lz4-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_OPENNI2_OpenNI2
pkgcfg_lib_PC_OPENNI2_OpenNI2-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_OPENNI_OpenNI
pkgcfg_lib_PC_OPENNI_OpenNI-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_USB_10_usb-1.0
pkgcfg_lib_PC_USB_10_usb-1.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib__OPENSSL_crypto
pkgcfg_lib__OPENSSL_crypto-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib__OPENSSL_ssl
pkgcfg_lib__OPENSSL_ssl-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=/usr/lib

