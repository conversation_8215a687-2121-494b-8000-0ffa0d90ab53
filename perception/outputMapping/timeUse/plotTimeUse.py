'''
Author: hanshuangquan <EMAIL>
Date: 2024-10-18 13:31:01
LastEditors: hanshuangquan <EMAIL>
LastEditTime: 2024-10-30 16:17:47
FilePath: /src/perception/outputMapping/timeUse/plotTimeUse.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 读取第一个文件
filePath = "/mnt/data/autoDriving/HongQi2/autodriving0928/src/perception/outputMapping/timeUse"
fileName = '1730276168238.csv'
data1 = pd.read_csv(filePath + '/' + fileName, sep=None, engine='python')
print("Data from file1:")
print(data1.head())

head = data1.head()

# 读取第二个文件
# file2 = 'file2.txt'
# data2 = pd.read_csv(file2, sep='\t', header=None, names=['frameCount', 'trackedObjectSize', 'trackingTimeUse'])

# 创建图形
plt.figure(figsize=(12, 6))

# 绘制第一个文件的数据
plt.subplot(1, 1, 1)
plt.plot(np.array(data1['frameCount']), np.array(data1['trackedObjectSize']), marker='o', label='trackedObjectSize')
plt.plot(np.array(data1['frameCount']), np.array(data1['trackingTimeUse']), marker='x', label='trackingTimeUse')
plt.plot(np.array(data1['frameCount']), np.array(data1['allLidarOBUObjectSize']), marker='o', label='allLidarOBUObjectSize')
plt.plot(np.array(data1['frameCount']), np.array(data1['allLidarOBUTimeUse']), marker='x', label='allLidarOBUTimeUse')
plt.title('HMTracking')
plt.xlabel('Frame Count')
plt.ylabel('Value')
plt.legend()

# 绘制第二个文件的数据
# plt.subplot(1, 2, 2)
# plt.plot(data2['frameCount'], data2['trackedObjectSize'], marker='o', label='trackedObjectSize')
# plt.plot(data2['frameCount'], data2['trackingTimeUse'], marker='x', label='trackingTimeUse')
# plt.title('File 2')
# plt.xlabel('Frame Count')
# plt.ylabel('Value')
# plt.legend()

# 显示图形
plt.tight_layout()
plt.show()
