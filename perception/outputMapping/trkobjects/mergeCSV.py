import csv
import glob
import os
import sys

#csv_path = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))
#csv_path = os.path.abspath(os.path.join(os.getcwd(), "."))

# file_name = sys.argv[1]
# csv_list = glob.glob(file_name + '/*.csv')
#
# csv_list.sort()
#
# for file in csv_list:
#     datas = []
#     for fname in csv_list:
# 	if 'csv' in fname:
# 	    with open(fname, 'r') as f:
# 		reader = csv.reader(f)
# 		reader = list(reader)[0:]
# 		for line in reader:
# 		    datas.append(line)
# #new_name = file_name.strip('/')
# output_name = file_name + '.csv'

# NOTE: need to note csv files change
csv_head = [
    'id',
    'timestamp',
    'x',
    'y',
    'w',
    'l',
    'h',
    'yaw',
    'relavx',
    'relavy',
    'absvx',
    'absvy',
    'relavx_raw',
    'relavy_raw',
    'absvx_raw',
    'absvy_raw',
    'fusioninfo',
    'radarIndex',
    'radarObjectID',
    'selfcarVx',
    'selfcarVy',
    'trajectorySpeed(0)',
    'selfcarYawrate',
]

# with open(output_name, 'w') as fw:
#     writer = csv.writer(fw)
#     writer.writerow(csv_head)
#     writer.writerows(datas)

def mergecsv(file_name):
    csv_list = glob.glob(file_name + '/*.csv')

    csv_list.sort()

    for file in csv_list:
        datas = []
        for fname in csv_list:
            if 'csv' in fname:
                with open(fname, 'r') as f:
                    reader = csv.reader(f)
                    reader = list(reader)[0:]
                    for line in reader:
                        datas.append(line)
    #new_name = file_name.strip('/')
    output_name = file_name + '.csv'

    with open(output_name, 'w') as fw:
        writer = csv.writer(fw)
        writer.writerow(csv_head)
        writer.writerows(datas)