'''
Description: 
Version: 2.0
Autor: shuangquan han
LastEditors: shuangquan han
LastEditTime: 2023-01-16 09:12:08
'''
#!/usr/bin/python
#-*-coding:utf-8-*-
colors = ['red','blue']

data = np.array([x, y]).T
model = DBSCAN(eps = 0.5, 
               min_samples = 1, 
               leaf_size = 1).fit(data)
plt.figure()
plt.scatter(x, 
            y, 
            c = model.labels_, 
            cmap = matplotlib.colors.ListedColormap(colors))
plt.xlabel("X")
plt.ylabel("Y")
plt.title("Scatter Plot of Feature X and Y")
plt.show()

