#!/usr/bin/python
#####-*-coding:utf-8-*-

import csv
#import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.colors
import codecs
import sklearn #scikit-learn
from sklearn.cluster import DBSCAN


from numpy import polyfit, poly1d

from mergeCSV import mergecsv


#需要处理的csv文件名称（时间戳），目标id，
file_name = sys.argv[1]
obj_id = sys.argv[2]

mergecsv(file_name)

use_input_values = 0 #0表示使用固定阈值，1表示使用输入参数作为阈值

if use_input_values:
    ##期望横向速度，期望纵向速度，速度阈值
    vx_desired = sys.argv[3]
    vy_desired = sys.argv[4]
    threshold = sys.argv[5]#横纵向阈值设为同一值，后续可按照实际需求分开设计
    vx_desired_value = float(vx_desired)#输入参数值
    vy_desired_value = float(vy_desired)
    thre_value = float(threshold)
else:
    vx_desired_value = 0.0#定义为固定值，可修改为输入参数值
    vy_desired_value = 0.0
    thre_value = 1.0

output_name = file_name +'_' + obj_id + '_historyInfo.csv'

#提取特定id的全部信息生成新的csv文件并用于后续数据分析
with open(file_name + '.csv', 'r') as fr:
    with open(output_name, 'w', newline='') as fw:
        reader = csv.reader(fr)
        writer = csv.writer(fw)
        header = next(reader)#获取表头信息
        writer.writerow(header)
        for row_list in reader:
            id_list = str(row_list[0]).strip()#获取第一列数据
            if id_list == obj_id:
                writer.writerow(row_list)



#对特定id的数据进行分析
data = pd.read_csv(output_name)
row_num = data.shape[0]#行数（不含表头）
col_num = data.shape[1]#列数

#获取时间戳信息
timestamp = data['timestamp']
timestamp_max = data['timestamp'].max()
timestamp_min = data['timestamp'].min()

#获取横向相对速度信息
relavx = data['relavx']
relavx_max = data['relavx'].max()
relavx_min = data['relavx'].min()
relavx_mean = data['relavx'].mean()

#获取横向绝对速度信息
vx = data['absvx']
vx_max = data['absvx'].abs().max()
vx_min = data['absvx'].abs().min()
vx_mean = data['absvx'].mean()
vx_absmean = data['absvx'].abs().mean()
vx_variance = data['absvx'].var()


thre_vx_max = vx_desired_value+thre_value
thre_vx_min = vx_desired_value-thre_value
out_thre_vx = sum((i>(thre_vx_max) or i<(thre_vx_min)) for i in vx)
out_thre_vx_percent = '{:.3f}%'.format(float(out_thre_vx)/row_num*100)
in_thre_vx_percent = '{:.3f}%'.format(float(row_num-out_thre_vx)/row_num*100)

#获取纵向相对速度信息
relavy = data['relavy']
relavy_max = data['relavy'].max()
relavy_min = data['relavy'].min()
relavy_mean = data['relavy'].mean()

#获取纵向绝对速度信息
vy = data['absvy']
vy_max = data['absvy'].abs().max()
vy_min = data['absvy'].abs().min()
vy_mean = data['absvy'].mean()
vy_absmean = data['absvy'].abs().mean()
vy_variance = data['absvy'].var()

thre_vy_max = vy_desired_value+thre_value
thre_vy_min = vy_desired_value-thre_value
out_thre_vy = sum((i>(thre_vy_max) or i<(thre_vy_min)) for i in vy)

out_thre_vy_percent = '{:.3f}%'.format(float(out_thre_vy)/row_num*100)
in_thre_vy_percent = '{:.3f}%'.format(float(row_num-out_thre_vy)/row_num*100)


#将分析结果进行存储便于写入文件中
out_str = {}#以字典方式存储字符串
out_str[0] = '目标id：' + str(obj_id) + ' 共收集 ' + str(row_num) + ' 组数据'
out_str[1] = '时间戳范围：' + str(timestamp_min) + ' ~ ' + str(timestamp_max)
out_str[2] = 'relavx：最大值：' + str(relavx_max) + '  最小值：' + str(relavx_min) + '  平均值：' + str(relavx_mean)
out_str[3] = 'vx：abs最大值：' + str(vx_max) + '  abs最小值：' + str(vx_min) + '  平均值：' + str(vx_mean) + '  abs平均值：' + str(vx_absmean) + '  方差：' + str(vx_variance)
out_str[4] = 'vx：阈值范围：' + str(thre_vx_min) + '~' + str(thre_vx_max) + ' 阈值范围外数量：' + str(out_thre_vx) + '  阈值范围内数量占比：' + in_thre_vx_percent + '  阈值范围外数量占比：' + out_thre_vx_percent
out_str[5] = 'relavy：最大值：' + str(relavy_max) + '  最小值：' + str(relavy_min) + '  平均值：' + str(relavy_mean)
out_str[6] = 'vy：abs最大值：' + str(vy_max) + '  abs最小值：' + str(vy_min) + '  平均值：' + str(vy_mean) + ' abs平均值：' + str(vy_absmean) + '  方差：' + str(vy_variance)
out_str[7] = 'vy：阈值范围：' + str(thre_vy_min) + '~' + str(thre_vy_max) + ' 阈值范围外数量：' + str(out_thre_vy) + '  阈值范围内数量占比：' + in_thre_vy_percent + '  阈值范围外数量占比：' + out_thre_vy_percent

#在文件中添加分析结果
with open(output_name, 'a') as fa:
    fa.write(codecs.BOM_UTF8.decode())
    writer = csv.writer(fa)
    writer.writerow('')
    for item in out_str.values():
        ls = [item] #将字典类型转换为list类型，写入csv 后其内容位于一个单元格内
        writer.writerow(ls)

#
# #
# # #获取随时间变化的相对速度变化曲线
# plt.figure(1)
# plt.scatter(timestamp, vx, s=2, marker='x')#点图
# # plt.scatter(timestamp, vy, s=2, marker='x')#点图
# plt.plot(timestamp, vx, color='#FF0000', label='vx')#折线图, linewidth=1
# # plt.plot(timestamp, vy, color='#00FF00', label='vy')#, linewidth=1
# plt.hlines(0.0, timestamp_min, timestamp_max, color='#000000', linestyles='dashed')#添加水平线
# plt.grid()#添加x轴y轴网格线
# plt.title(u'obj_speed', fontsize=18, fontweight='heavy', color='blue')#标签
# plt.xlabel(u'time(s)', fontsize=13)
# plt.ylabel(u'speed(m/s)', fontsize=13)
# plt.legend(fontsize=13)#图例
# plt.savefig('./'+file_name+'_' + obj_id+'_vxvy.png')#可以将生成的图片保存在路径下
# plt.show()
# # #########################################################################################
# #
# # ## DBSCAN聚类
# colors = ['red','blue']
# y = vx
# # print(type(y))
# # print(type(y.shape))
# # print(y.shape)
#
# x = timestamp
# # numSize = vx.shape[0]
# # print(numSize)
# # x = list(range(numSize))
# # print(type(x))
#
# data = np.array([x, y]).T
# # model = sklearn.cluster.DBSCAN(eps = 0.5,
# #                min_samples = 1,
# #                leaf_size = 1).fit(data)
# model = DBSCAN(eps = 0.8,
#                                min_samples = 2,
#                                leaf_size = 1).fit(data)
#
# plt.figure()
# plt.scatter(x,
#             y,
#             c = model.labels_,
#             cmap = matplotlib.colors.ListedColormap(colors)
#             )
# plt.hlines(0.0, timestamp_min, timestamp_max, color='#000000', linestyles='dashed')#添加水平线
# plt.grid()#添加x轴y轴网格线
#
# plt.xlabel("timeStamp")
# plt.ylabel("Vx(abs)")
# plt.title("Scatter Plot of Feature timeStamp and Vx(abs)")
# plt.savefig('./'+file_name+'_' + obj_id +'_DBSCAN.png')#可以将生成的图片保存在路径下
# plt.show()
# #########################################################################################
#
# def hampel(X, k):
#     length = X.shape[0] - 1
#     nsigma = 3
#     iLo = np.array([i - k for i in range(0, length + 1)])#0~length的索引
#     iHi = np.array([i + k for i in range(0, length + 1)])#20~length最大值
#     iLo[iLo < 0] = 0    #小于0的部分置0
#     iHi[iHi > length] = length #大于length的部分设置为length
#     xmad = []
#     xmedian = []
#     for i in range(length + 1):
#         w = X[iLo[i]:iHi[i] + 1]    #取X中k + 1个数
#         medj = np.median(w)  #取X中k + 1个数的中位数
#         mad = np.median(np.abs(w - medj))    #取X中k + 1个数与中位数差的中位数
#         xmad.append(mad)
#         xmedian.append(medj)
#     xmad = np.array(xmad)
#     xmedian = np.array(xmedian) #样本中位数，返回一个和x大小一样的向量或矩阵
#     scale = 1.4826  # 缩放？？ 计算中位数差比较方便，而1.4826是正态分布下标准偏差和绝对中位差之间的倍数关系，以获得渐近正态一致性
#     xsigma = scale * xmad   #估计标准差，返回一个和x大小一样的向量或矩阵
#     xi = ~(np.abs(X - xmedian) <= nsigma * xsigma)  # 找出离群点（即超过nsigma个标准差） ~按位取反运算符：对数据的每个二进制位取反,即把1变为0,把0变为1
#
#     # 将离群点替换为中为数值
#     xf = X.copy()
#     xf[xi] = xmedian[xi]
#     return xf
#
# plt.figure()
# X = np.array(vx) #数据Vx
# res = hampel(X, 20)
# plt.xlabel("index")
# plt.ylabel("Vx(abs)")
# plt.title("Scatter Plot of Feature index-Vx(abs)")
# plt.scatter(timestamp, X, s=2, marker='x')#点图
# plt.scatter(timestamp, res, s=2, marker='x')#点图
# plt.plot(timestamp, X)
# plt.plot(timestamp, res, '--')#
# plt.hlines(0.0, timestamp_min, timestamp_max, color='#000000', linestyles='dashed')#添加水平线
# plt.grid()#添加x轴y轴网格线
# plt.savefig('./'+file_name+'_' + obj_id +'_hampelVx.png')#可以将生成的图片保存在路径下
# plt.show()
#
# plt.figure()
# Y = np.array(vy) #数据 Vy
# res = hampel(Y, 20)
# plt.xlabel("index")
# plt.ylabel("Vy(abs)")
# plt.title("Scatter Plot of Feature index-Vy(abs)")
# plt.scatter(timestamp, Y, s=2, marker='x')#点图
# plt.scatter(timestamp, res, s=2, marker='x')#点图
# plt.plot(timestamp, Y)
# plt.plot(timestamp, res, '--')#
# plt.hlines(0.0, timestamp_min, timestamp_max, color='#000000', linestyles='dashed')#添加水平线
# plt.grid()#添加x轴y轴网格线
# plt.savefig('./'+file_name+'_' + obj_id +'_hampelVy.png')#可以将生成的图片保存在路径下
# plt.show()
#
# #########################################################################################
# #拟合多项式
# y1 = poly1d(polyfit(timestamp,vx,1))
# y3 = poly1d(polyfit(timestamp,vx,3))
# y5 = poly1d(polyfit(timestamp,vx,5))
# y7 = poly1d(polyfit(timestamp,vx,7))
# y9 = poly1d(polyfit(timestamp,vx,9))
# plt.plot(timestamp,vx,color='m',linestyle='',marker='o',label=u'fitData')
# plt.plot(x,y3(x),color='b',linestyle='-',marker='.',label=u"fitCurve")
# plt.hlines(0.0, timestamp_min, timestamp_max, color='#000000', linestyles='dashed')#添加水平线
# plt.grid()#添加x轴y轴网格线
# # 把拟合的曲线在这里画出来
# plt.legend(loc='upper left')
# plt.show()
# plt.savefig('./'+file_name+'_' + obj_id +'_curvefitVx.png')#可以将生成的图片保存在路径下
