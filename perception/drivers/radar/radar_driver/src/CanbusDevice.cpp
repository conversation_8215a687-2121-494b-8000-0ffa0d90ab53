#include "CanbusDevice.h"
#include <iostream>
#include <linux/can.h>
#include <linux/can/raw.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <net/if.h>
#include <sys/ioctl.h>
#include <unistd.h>
#include <string.h>


CanbusDevice::CanbusDevice(const char* dev_name)
{
    m_can_dev_name = dev_name;
}

int CanbusDevice::Init()
{
    m_can_fd = socket(AF_CAN, SOCK_RAW, CAN_RAW);
    if (m_can_fd < 0)
    {
        printf("socket() error\n");
        return EXIT_FAILURE;
    }
    
    struct ifreq ifr;
    strncpy(ifr.ifr_name, m_can_dev_name.c_str(), IFNAMSIZ - 1);
    ifr.ifr_name[IFNAMSIZ - 1] = '\0';
    ifr.ifr_ifindex = if_nametoindex(ifr.ifr_name);
    if (!ifr.ifr_ifindex) {
        perror("if_nametoindex");
        return 1;
    }

    //can data filter
    struct can_filter cf[1];
    cf[0].can_id = 0;
    cf[0].can_mask = 0;

    setsockopt(m_can_fd, SOL_CAN_RAW, CAN_RAW_FILTER, &cf, sizeof(cf));
     
    struct sockaddr_can addr;
    memset(&addr, 0, sizeof(addr));
    addr.can_family = AF_CAN;
    addr.can_ifindex = ifr.ifr_ifindex;

    int ret = bind(m_can_fd, (struct sockaddr*)&addr, sizeof(addr));
    if (ret < 0)
    {
        printf("bind() error\n");
        return EXIT_FAILURE;
    }

    //创建接收线程
    m_start = true;
    auto l_tmp = std::thread(&CanbusDevice::recvDataTh, this);
	l_tmp.detach();
    return EXIT_SUCCESS;
}

CanbusDevice::~CanbusDevice()
{
    m_start = false;
    std::cout << "CanbusDevice::~CanbusDevice()" << std::endl;
    close(m_can_fd);
}



void CanbusDevice::recvDataTh()
{
    while (m_start)
    {
        int len = 0, msg_id = 0; 
        unsigned char* data= new unsigned char[8]; ;
        int ret = Read(data, len, msg_id);
        canData l_temp; 
        l_temp.data = data;
        l_temp.len = len;
        l_temp.msg_id = msg_id;
        m_count++;
        std::unique_lock<std::mutex> lck(m_SendDdata_mutex);
        m_CanQue.push(l_temp);
        lck.unlock();
    }
    
}


int CanbusDevice::Read(unsigned char *data, int& len, int& msg_id)
{
    struct can_frame can_data;
    int ret = read(m_can_fd, &can_data, sizeof(can_data));

    if (ret <= 0)
    {
        perror("read ");
        len = 0;
        msg_id = 0;
        return ret;
    }
    msg_id = can_data.can_id;
    len = can_data.can_dlc;
  
    memcpy(data, can_data.data, len);
    return ret;
}

int CanbusDevice::Write(const unsigned char *data, int len, int msg_id)
{
    if(len > CAN_MAX_DLEN || len < 0)
    {
        return 0;
    }
    struct canfd_frame frame;
    memset(&frame, 0, sizeof(frame));
    for(int i = 0; i < 8; i++)
    {
        frame.data[i] = data[i];
    }
    frame.len = len;
    frame.can_id = msg_id;
    int ret = write(m_can_fd, &frame, 16); 
    if(ret != 16)
    {
        perror("write()");
    }
    return ret;
}

std::queue<canData> CanbusDevice::CanDataGet()
{
    std::queue<canData> p_data;
    std::unique_lock<std::mutex> lck(m_SendDdata_mutex);
    p_data = m_CanQue;

   
    while (!m_CanQue.empty())
    {
        canData l_temp = m_CanQue.front();
 
        m_CanQue.pop();
    }
    lck.unlock();
  
    return p_data;
}


std::string CanbusDevice::GetDevName()
{
    return m_can_dev_name;
}
