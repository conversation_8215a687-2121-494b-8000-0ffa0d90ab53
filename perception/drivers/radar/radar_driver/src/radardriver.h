/**************************************************************************
Copyright: 	2021-2023 VANJEE Technology.
File name: 	radardriver.h
Description: 毫米波雷达数据驱动程序。	
Author: 	zhuxuekui
Version: 	V1.0
Date: 		2019.6
History: 	无
**************************************************************************/

#ifndef RADAR_DRIVER_H
#define RADAR_DRIVER_H

#include "radarparser.h"
#include "CanbusDevice.h"

/* FOREGROUND color control*/
#define DEBUGHEAD "[radar-->]"

#define RST  "\x1B[0m"
#define KRED  "\x1B[31m" 
#define KGRN  "\x1B[32m"
#define KYEL  "\x1B[33m"
#define KBLU  "\x1B[34m"
#define KMAG  "\x1B[35m"
#define KCYN  "\x1B[36m"
#define KWHT  "\x1B[37m"
#define FRED(x) KRED x RST
#define FGRN(x) KGRN x RST
#define FYEL(x) KYEL x RST
#define FBLU(x) KBLU x RST
#define FMAG(x) KMAG x RST
#define FCYN(x) KCYN x RST
#define FWHT(x) KWHT x RST
#define BOLD(x) "\x1B[1m" x RST
#define UNDL(x) "\x1B[4m" x RST

 using namespace std;


#if defined  __aarch64__
const static int RECVBUFFERSIZE = 8;
#elif defined __x86_64__
const static int RECVBUFFERSIZE = 1300;
#endif
 class RadarDriverNode
 {
   public:
     RadarDriverNode(ros::NodeHandle handle);
     ~RadarDriverNode(); 
     /*** Receive Buffer Data from Udp/Canet ***/
     void RecvCarInfoKernel();
      /***the Function of Parsing ARS Data from the Group of Buffer Data ***/
     void ParserData(unsigned char data[],int num);

     void ParserData(unsigned char data[],int p_len, int p_msg_id);


     /***the Function of Publishing ARS Data***/
     void PubMsg(ros::Publisher pub); //
     /*** Receive  Data from Buusensorgps node  ***/
     void Callback_gps(const common_msgs::sensorgps::ConstPtr msg);
     /*** main process function ***/
     void Run();

   public:
     int m_runningmode;
     bool bdebug;
     std::string updatelog;
     
     int m_sensortype;

     
     std::string m_canetip;
     int m_canetport;
     std::string m_pcip;
     std::string m_radarCanID;
     int m_pcport;
     float m_deltaAngle;  //雷达校正横摆角
     float m_deltaX;      //横坐标
     float m_deltaY;      //纵坐标
     int m_updown;
     int m_autoalign;   //是否校正

     ros::NodeHandle m_handle;
     //interface
     ros::Subscriber sub_actuator;
     ros::Publisher pub_radardriver;


     //data
     common_msgs::sensorgps msg_gps;

     //boost
     CanetUdp* boost_udp;
     unsigned char buffer[RECVBUFFERSIZE];

     //parse class
     SensorRadar_ARS m_radar_parser;
     
     float last_heading;
     CanbusDevice *m_canbus = nullptr;
 };



 #endif
