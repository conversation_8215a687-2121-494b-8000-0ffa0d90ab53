
/**************************************************************************
Copyright: 	2021-2023 VANJEE Technology.
File name: 	radardriver.cpp
Description: 毫米波雷达数据驱动程序。	
Author: 	zhuxuekui
Version: 	V1.0
Date: 		2019.6
History: 	无
**************************************************************************/

 #include"radardriver.h"

 RadarDriverNode::RadarDriverNode(ros::NodeHandle handle)
 {
   m_canetip = "*************";
   m_canetport = 4001;
   m_pcip = "*************";
   m_pcport = 4001;
   m_runningmode = 0;
   updatelog = "null";
   bdebug = true;


   m_deltaAngle = 0;//0.01°
   m_deltaX = 0;
   m_deltaY = 0;//cm
   m_autoalign = 0;
   m_updown = 0;

   std::string node_name = ros::this_node::getName(); //获取节点名称 

   handle.param(node_name+"/radardeltax",m_deltaX,m_deltaX);
   handle.param(node_name+"/radardeltay",m_deltaY,m_deltaY);
   handle.param(node_name+"/radardeltayaw",m_deltaAngle,m_deltaAngle);
   handle.param(node_name+"/radarcanetip",m_canetip,m_canetip);
   handle.param(node_name+"/radarcanetport",m_canetport,m_canetport);
   handle.param(node_name+"/radarpcip",m_pcip,m_pcip);
   handle.param(node_name+"/radarpcport",m_pcport,m_pcport);
   handle.param(node_name+"/radarcali",m_autoalign,m_autoalign);
   handle.param(node_name+"/radarupdown",m_updown,m_updown);
   //m_radarCanID
   handle.param(node_name+"/radarCanID",m_radarCanID,m_radarCanID);
   int sensortype = -1;  
    if(node_name == "/radarfront_driver")            sensortype = FRONTRADAR;
    else if(node_name == "/radarback_driver")        sensortype = BACKRADAR;
    else if(node_name == "/radarleftfront_driver")   sensortype = LEFTFRONTRADAR;
    else if(node_name == "/radarrightfront_driver")  sensortype = RIGHTFRONTRADAR;
    else if(node_name == "/radarleftback_driver")    sensortype = LEFTBACKRADAR;
    else if(node_name == "/radarrightback_driver")   sensortype = RIGHTBACKRADAR; 
    m_sensortype = sensortype;
    m_handle = handle;
   //paramters
   std::cout<<FRED("Copyright©2021-2023 VANJEE Technology. All rights reserved ")<<std::endl;
   std::cout<<FYEL("*****"+node_name+":parameters*******************")<<std::endl;
   std::cout<<FGRN("canet_ip: ")<<m_canetip<<std::endl;
   std::cout<<FGRN("canet_port: ")<<m_canetport<<std::endl;
   std::cout<<FGRN("pc_ip: ")<<m_pcip<<std::endl;
   std::cout<<FGRN("pc_port: ")<<m_pcport<<std::endl;
   std::cout<<FGRN("pc_runningmode: ")<<m_runningmode<<std::endl;
   std::cout<<FGRN("sensortype: ")<<m_sensortype<<std::endl;
   std::cout<<FGRN("output_debuginfo:")<<bdebug<<std::endl;
   std::cout<<FGRN("m_radarCanID:")<<m_radarCanID<<std::endl;
   std::cout<<FYEL("***------------------------**radardriver:parameters end***************")<<std::endl; 

   //数据标定
   m_radar_parser.SetCaliInfo(m_deltaAngle,m_deltaX,m_deltaY,sensortype,m_updown);
   pub_radardriver = m_handle.advertise<radar_msgs::sensorradar>(node_name+"/radardriver",1000);  
 }

 RadarDriverNode::~RadarDriverNode()
 {
#if defined  __aarch64__
    if(m_canbus != nullptr)
    {
    delete m_canbus;
    }
#endif
#if defined __x86_64__
   delete boost_udp;
#endif
 }

 /*
  *author:zhuxuekui
  *date:2016.07.21
  *detail:run program
  */
 void RadarDriverNode::Run()
 {
   	//bind socket and receive_data
#if defined  __aarch64__
     m_canbus = new CanbusDevice(m_radarCanID.c_str());
     int l_ret = m_canbus->Init();
#endif


#if defined __x86_64__
	 boost_udp = new CanetUdp(m_pcip,m_pcport,m_canetip,m_canetport);
     boost_udp->socketBuild();
#endif
     sub_actuator = m_handle.subscribe("sensorgps",1000,&RadarDriverNode::Callback_gps,this);

     ros::Rate loop_rate(500); //1000hz

     long cnt = ros::Time::now().toSec()*1000;
     while(ros::ok())
     {
        //动态数据获取 
        if(m_autoalign == 1)
        {
            //加载动态配置参数
            int sensortype = -1;
            bool front = false,back = false,leftfront = false,leftback = false,rightfront = false,rightback = false;
            ros::NodeHandle n;
            
            n.getParam("align_frontradar", front);
            n.getParam("align_backradar", back);
            n.getParam("align_leftfrontradar", leftfront);
            n.getParam("align_rightfrontradar",rightfront);
            n.getParam("align_leftbackradar", leftback);
            n.getParam("align_rightbackradar", rightback);

            if(front)            sensortype = FRONTRADAR;
		    else if(back)        sensortype = BACKRADAR;
            else if(leftfront)   sensortype = LEFTFRONTRADAR;
		    else if(rightfront)  sensortype = RIGHTFRONTRADAR;
  		    else if(leftback)    sensortype = LEFTBACKRADAR;
            else if(rightback)   sensortype = RIGHTBACKRADAR; 
            
            if(sensortype == FRONTRADAR)
            {
                n.getParam("delta_yaw", m_deltaAngle);
                n.getParam("delta_x", m_deltaX);
                n.getParam("delta_y", m_deltaY);
                m_deltaAngle = m_deltaAngle*100;
                m_deltaX = m_deltaX*100; //m-->cm
                m_deltaY = m_deltaY*100;                
            }
            else if(sensortype == BACKRADAR)
            {
                n.getParam("delta_yaw", m_deltaAngle);
                n.getParam("delta_x", m_deltaX);
                n.getParam("delta_y", m_deltaY);
                
                m_deltaAngle = m_deltaAngle*100;
                m_deltaX = m_deltaX*100;
                m_deltaY = m_deltaY*100;
            }
            else if(sensortype == LEFTFRONTRADAR)
            {
                n.getParam("delta_yaw", m_deltaAngle);
                n.getParam("delta_x", m_deltaX);
                n.getParam("delta_y", m_deltaY);
                m_deltaAngle = m_deltaAngle*100;
                m_deltaX = m_deltaX*100;
                m_deltaY = m_deltaY*100;                
            }
            else if(sensortype == RIGHTFRONTRADAR)
            {
                n.getParam("delta_yaw", m_deltaAngle);
                n.getParam("delta_x", m_deltaX);
                n.getParam("delta_y", m_deltaY);
                m_deltaAngle = m_deltaAngle*100;
                m_deltaX = m_deltaX*100;
                m_deltaY = m_deltaY*100;                
            }
            else if(sensortype == LEFTBACKRADAR)
            {
                n.getParam("delta_yaw", m_deltaAngle);
                n.getParam("delta_x", m_deltaX);
                n.getParam("delta_y", m_deltaY);
                m_deltaAngle = m_deltaAngle*100;
                m_deltaX = m_deltaX*100;
                m_deltaY = m_deltaY*100;                
            }
            else if(sensortype == RIGHTBACKRADAR)
            {
                n.getParam("delta_yaw", m_deltaAngle);
                n.getParam("delta_x", m_deltaX);
                n.getParam("delta_y", m_deltaY);
                m_deltaAngle = m_deltaAngle*100;
                m_deltaX = m_deltaX*100;
                m_deltaY = m_deltaY*100;                
            }
            // cout << "x: " << m_deltaX << ",y: " << m_deltaY << " ,yaw: " << m_deltaAngle << endl;
             m_radar_parser.SetCaliInfo(m_deltaAngle,m_deltaX,m_deltaY,m_sensortype,m_updown);
        }

        if((ros::Time::now().toSec()*1000-cnt) > 50)
        {
           if( m_sensortype == FRONTRADAR)
           {
             //SendCarInfoKernel(); // pc --> canet   //20ms 
           }
           cnt = ros::Time::now().toSec()*1000;
        }
        RecvCarInfoKernel();   // canet -->pc
        ros::spinOnce();
        loop_rate.sleep();
    }
 }


 /*
  *author:zhuxuekui
  *date:2016.07.21
  *detail:Read canet data, publish after parsing
  */
 void RadarDriverNode::RecvCarInfoKernel()
 {
 
#if defined  __aarch64__
    memset(buffer,0,sizeof(buffer));
    int len = 0, msg_id = 0;
    std::queue<canData> l_que = m_canbus->CanDataGet();

    int l_count = 0;
    while (!l_que.empty())
    {   l_count++;
        canData l_data = l_que.front();
        l_que.pop();
        ParserData(l_data.data, l_data.len, l_data.msg_id);
        delete[] l_data.data;
    }
#endif
    // std::cout<<l_ret<<"   len:"<<len<<"    msg_id:"<<msg_id<<std::endl;
    // printf("%#x\n",msg_id);
    

#if defined __x86_64__
   memset(buffer,0,sizeof(buffer));
   int ret = boost_udp->receive_data(buffer);
   if (ret > 0 && ret % 13 == 0) //13
   {
      ParserData(buffer,ret);//一次收到的有多帧数据,我们需要解析。  边收边解析
   }
   //std::cout << ret << std::endl;
 #endif
 }



 /*
  *author:chenyongjing
  *date:2023.10.31
  *detail:parse canet data
  */

void RadarDriverNode::ParserData(unsigned char data[],int p_len, int p_msg_id)
{
    VCI_CAN_OBJ frame;
    frame.ID = p_msg_id;
    for (int j = 0; j < 8; ++j) //获取data
    {
        frame.Data[j] = data[j];
    } 
    // cout << hex <<frame.ID << endl;
    bool ret = false;
    ret = m_radar_parser.ParserObjData(&frame);//解析雷达数据

    if(ret)//数据解析完毕
    {
        // std::cout<<"ret:"<<ret<<std::endl;
        PubMsg(pub_radardriver);//发送雷达数据

        for(int i = 0; i < MaxTarNum; ++i)
        {
            m_radar_parser.RadarRecData[i].is_update = 0;
        }
    }
}


 /*
  *author:zhuxuekui
  *date:2016.07.21
  *detail:parse canet data
  */
 void RadarDriverNode::ParserData(unsigned char data[],int num)
 {
    for(int i = 0; i < num/13; ++i)
    {
        VCI_CAN_OBJ frame;
        CHAR2UINT ii;
        for (int j = 0; j < 4; ++j)
        {
            ii.ch[3-j] = data[1+j+i*13];
        }
        unsigned int id = ii.i; //获取ID
        frame.ID = id;
        for (int j = 0; j < 8; ++j) //获取data
        {
            frame.Data[j] = data[5+j+i*13];
        } 
        //cout << hex <<frame.ID << endl;
        bool ret = false;
        ret = m_radar_parser.ParserObjData(&frame);//解析雷达数据
        if(ret)//数据解析完毕
        {
            PubMsg(pub_radardriver);//发送雷达数据

            for(int i = 0; i < MaxTarNum; ++i)
            {
                m_radar_parser.RadarRecData[i].is_update = 0;
            }
        }
    }
 }


 /*
  *author:zhuxuekui
  *date:2016.07.21
  *detail:Pub data
  */
 void RadarDriverNode::PubMsg(ros::Publisher pub)
 {
	    radar_msgs::sensorradar msg;
	    msg.isvalid = 1;
        for(int i = 0; i < MaxTarNum; ++i)
        {
            if(m_radar_parser.RadarRecData[i].is_update == 1)
            {
                radar_msgs::radarobject ob;
                float x =  m_radar_parser.RadarRecData[i].lateral_dist;       //m
                float y =  m_radar_parser.RadarRecData[i].longitude_dist;     //m
                if(abs(x) < 0.0001)
                {
                   x = 0;
                }
                ob.id = m_radar_parser.RadarRecData[i].obstacle_id;
                ob.x = x;
                ob.y = y; //m
                ob.relspeedy = m_radar_parser.RadarRecData[i].longitude_vel; //m/s
                ob.relspeedx = m_radar_parser.RadarRecData[i].lateral_vel;
                ob.length = m_radar_parser.RadarRecData[i].length;
                ob.width = m_radar_parser.RadarRecData[i].width;
                ob.classification = m_radar_parser.RadarRecData[i].obstacle_class;
                ob.flag = 0; //which radar
                ob.DistLat_rms = m_radar_parser.RadarRecData[i].disLat_rms;
                ob.DistLong_rms = m_radar_parser.RadarRecData[i].disLong_rms;
                ob.VrelLong_rms = m_radar_parser.RadarRecData[i].vreLong_rms;
                ob.VrelLat_rms = m_radar_parser.RadarRecData[i].vreLat_rms;
                ob.ArelLong_rms = m_radar_parser.RadarRecData[i].areLon_rms;
                ob.ArelLat_rms = m_radar_parser.RadarRecData[i].areLat_rms;
                ob.Orientation_rms = m_radar_parser.RadarRecData[i].orientation_angle_rms;
                ob.DynProp = m_radar_parser.RadarRecData[i].dynProp;
                ob.RCS = m_radar_parser.RadarRecData[i].rcs;
                ob.ProbOfExist = m_radar_parser.RadarRecData[i].probOfExist;
                ob.ArelLong = m_radar_parser.RadarRecData[i].areLong;
                ob.ArelLat = m_radar_parser.RadarRecData[i].areLat;
                ob.OrientationAngel = m_radar_parser.RadarRecData[i].orientation_angle;
                ob.CollDetRegionBitfield = m_radar_parser.RadarRecData[i].collDetRegionBitField;
                msg.obs.push_back(ob);

            }
        }
      
		// std::cout<<FYEL(DEBUGHEAD)<<"size: "<<msg.obs.size()<<std::endl;
	    pub.publish(msg);
 }




 /*
  *author:zhuxuekui
  *date:2016.07.21
  *detail:callback function
  */
 void RadarDriverNode::Callback_gps(const common_msgs::sensorgps::ConstPtr msg){
     msg_gps = *msg;
 }

