cmake_minimum_required(VERSION 2.8.3)
cmake_policy(SET CMP0038 NEW)
project(radar_msgs_humble)
set(CMAKE_BUILD_TYPE "Release")

if(rclcpp_FOUND)
  message(=============================================================)
  message("-- ROS2 Found. ROS2 Support is turned On.")
  message(=============================================================)
  add_definitions(-DROS2_FOUND)

  find_package(ament_cmake REQUIRED)
  find_package(rosidl_default_generators REQUIRED)
  find_package(std_msgs REQUIRED)

  rosidl_generate_interfaces(${PROJECT_NAME}
    sensorradar.msg
    radarobject.msg
    DEPENDENCIES std_msgs
  )

  install(DIRECTORY msg/
    DESTINATION share/${PROJECT_NAME}/msg
  )

  ament_export_include_directories(include)

  ament_export_dependencies(rosidl_default_runtime)

  ament_export_dependencies(rosidl_default_runtime)
  ament_export_dependencies(std_msgs)
  ament_package()

endif(ROS2_FOUND)

if(roscpp_FOUND)
  find_package(catkin REQUIRED COMPONENTS
    roscpp
    rospy
    std_msgs
    message_generation
  )

  add_message_files(
    DIRECTORY msg
    FILES
    sensorradar.msg
    radarobject.msg
  )

  generate_messages(DEPENDENCIES std_msgs)

  catkin_package(
    CATKIN_DEPENDS message_runtime std_msgs
  )
endif(roscpp_FOUND)


