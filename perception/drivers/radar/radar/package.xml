<package format="2">

  <name>radar</name>
  <version>1.0.0</version>
  <description>
    Basic ROS support for the Radar.
  </description>
  <maintainer email="<EMAIL>">wanji</maintainer>
  <author> wanji</author>
  <license>BSD</license>
  <!--
  <review status="API reviewed" notes="2017-04-17"/>
  -->
  <url type="website">http://www.ros.org/wiki/ivradar</url>
  <url type="repository">https://github.com/ros-drivers/radar</url>
  <url type="bugtracker">https://github.com/ros-drivers/radar</url>

  <!-- ROS1 dependencies -->
  <buildtool_depend>catkin</buildtool_depend>

  <run_depend>radar_driver</run_depend>
  <run_depend>radar_msgs</run_depend>
  <run_depend>radar_deal</run_depend>

  <!-- ROS2 dependencies -->
  <buildtool_depend>ament_cmake</buildtool_depend>
  <exec_depend>radar_driver</exec_depend>
  <exec_depend>radar_msgs</exec_depend>
  <exec_depend>radar_deal</exec_depend>

  <export>
    <metapackage/>
  </export>

</package>
