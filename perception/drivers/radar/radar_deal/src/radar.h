/**************************************************************************
Copyright: 	2021-2023 VANJEE Technology.
File name: 	radar.h
Description: 毫米波雷达数据处理程序。	
Author: 	zhuxuekui
Version: 	V1.0
Date: 		2019.6
History: 	无
**************************************************************************/
#pragma once

#include<iostream>
#include<vector>
#include <unistd.h>
#include<iostream>
#include<vector>
#include<ros/ros.h>
#include<string.h>
#include<std_msgs/Float32MultiArray.h>

 #include"radar_msgs/sensorradar.h"
 #include"radar_msgs/radarobject.h"
 #include"common_msgs/actuator.h"

/* FOREGROUND color control*/
#define DEBUGHEAD "[frontradar-->]"

#define RST  "\x1B[0m"
#define KRED  "\x1B[31m" 
#define KGRN  "\x1B[32m"
#define KYEL  "\x1B[33m"
#define KBLU  "\x1B[34m"
#define KMAG  "\x1B[35m"
#define KCYN  "\x1B[36m"
#define KWHT  "\x1B[37m"
#define FRED(x) KRED x RST
#define FGRN(x) KGRN x RST
#define FYEL(x) KYEL x RST
#define FBLU(x) KBLU x RST
#define FMAG(x) KMAG x RST
#define FCYN(x) KCYN x RST
#define FWHT(x) KWHT x RST
#define BOLD(x) "\x1B[1m" x RST
#define UNDL(x) "\x1B[4m" x RST

 using namespace std;

//限定ROI区域
const static float MAXLATDIS = 50;//横坐标为50m
const static float MAXLONGDIS = 200;//纵坐标为200m

//障碍物数据
typedef struct _ARSRecData
{
	unsigned char IsUpdatedFlag; //数据是否更新
	unsigned char DetectStatus; //检测状态
	double Distance; //纵坐标 m
	double LatPos;    //横坐标 m
	double RelSpd_Lat;  //横向速度 m/s
	double RelSpd_Long; //纵向速度 m/s
	short width;       //宽度 m
	short length;      //长度 m
	unsigned char classification;//种类
    unsigned char id;//障碍物ID

	float rcs;//反射强度
	float orientation_angle;//起始角
	unsigned char disLong_rms;  // 纵向距离标准差  c  --
	unsigned char disLat_rms; //   横向距离标准差  c  --
	unsigned char vreLong_rms; //纵向相对速度标准差  c  --
	unsigned char vreLat_rms; //横向相对速度标准差  c  --
	unsigned char areLat_rms;  //横向相对加速度标准差  c --
	unsigned char areLon_rms;   //纵向相对加速度标准差  c --
	unsigned char orientation_angle_rms;  //方向角标准差  c --
	float  areLong;  // 目标的纵向相对加速度  d   --
	float  areLat;   // 目标的横向相对加速度  d  --
	unsigned char probOfExist; //测试状态   c   --
	unsigned char dynProp;   //目标的动态 特性b    -- ok
	unsigned char collDetRegionBitField;// 区域的位字段

} ARSRecData;

//最多的障碍物数量
const int MaxTarNum = 255;

//障碍物
typedef struct _ARSData
{
	ARSRecData radarRxData[MaxTarNum];
}ARSData;

/************************************************************************/
/*                         雷达目标跟踪相关变量                            */
/************************************************************************/

#define DETECT_FRAME (20)//目标库中存储帧数
#define LAST(a) ((a > 0)?(a - 1):(DETECT_FRAME - 1))//
#define LOST_MAX_NUM (20)//目标最大丢失次数
//直角坐标数据
typedef struct _TargetInfo_Rec
{
    double Distance[DETECT_FRAME];// 纵坐标*m
    double  LatPos[DETECT_FRAME];//横坐标*m
    short  RelSpd_Lat;//横向速度 m/s
    short  RelSpd_Long;//纵向速度 m/s

	short width;       //宽度 m
	short length;      //长度 m
	unsigned char classification; //障碍物种类
	unsigned char  Total;   //障碍物连续出现的次数 
    unsigned char  MoveFlag;//跟踪状态
	unsigned char  RollCnt;//0~DETECT_FRAME-1之间循环
    unsigned char  TrackState;//0——空闲，未跟踪；1——跟踪起始；2——稳定跟踪状态；3——跟踪预测状态
    unsigned char  TrackID;//接收缓存中的ID，TrackState为1或2时有效；TrackSttate为3时，表示原来跟踪的ID
	unsigned char  LostCnt;//丢失的次数
	unsigned char  LostType;//0：丢失次数过多；1：增加新目标使其溢出；2：异常丢失；3：逻辑判断丢失（预留）； 4：不稳定目标; 5 超出范围丢失


	float rcs;//反射强度
	float orientation_angle;//起始角
	unsigned char disLong_rms;  // 纵向距离标准差  c  --
	unsigned char disLat_rms; //   横向距离标准差  c  --
	unsigned char vreLong_rms; //纵向相对速度标准差  c  --
	unsigned char vreLat_rms; //横向相对速度标准差  c  --
	unsigned char areLat_rms;  //横向相对加速度标准差  c --
	unsigned char areLon_rms;   //纵向相对加速度标准差  c --
	unsigned char orientation_angle_rms;  //方向角标准差  c --
	float  areLong;  // 目标的纵向相对加速度  d   --
	float  areLat;   // 目标的横向相对加速度  d  --
	unsigned char probOfExist; //测试状态   c   --
	unsigned char dynProp;   //目标的动态 特性b    -- ok
	unsigned char collDetRegionBitField;// 区域的位字段


} RadarTargetInfo_Rec;

#define DELTA_DIS (500)//纵向距离阈值cm 250
#define DELTA_LAT (150)//横向距离阈值cm 150
#define DELTA_REL (200)//相对速度阈值cm/s
#define DELTA_MINDIS (100*100)//cm
#define MAX_DISTANCE (25000) //max Distance

#define STRAIGHTROAD  20 //方向盘转角


 class radarNode
 {
   public:

	/*************************************************
	Function:       radarNode
	Description:    构造函数，主要用于数据初始化
	Input:          handle：ros句柄
	Output:         无
	Return:         无
	*************************************************/    
     radarNode(ros::NodeHandle handle);
	/*************************************************
	Function:       ~radarNode
	Description:    析构函数
	Input:          无
	Output:         无
	Return:         无
	*************************************************/	 
     ~radarNode(){};
    /*************************************************
	Function:       TargetSelect
	Description:    算法处理主程序
	Input:          ARSData &radar_data——接收解析到的雷达数据
	Output:         处理是否异常
	Return:         无
	*************************************************/
     int TargetSelect();

	 int TargetRawSelect();
	/*************************************************
	Function:       PubMsg
	Description:    发送数据
	Input:          pub
	Output:         无
	Return:         无
	*************************************************/
     void PubMsg(ros::Publisher pub); //
    /*************************************************
	Function:       SubCallback_radar
	Description:    接收得到毫米波雷达原始数据
	Input:          radar_msgs::sensorradar::ConstPtr msg
	Output:         无
	Return:         无
	*************************************************/
     void SubCallback_radar(const radar_msgs::sensorradar::ConstPtr msg);
    /*************************************************
	Function:       run
	Description:    main function
	Input:          无
	Output:         无
	Return:         无
	*************************************************/
     void run();

    /*************************************************
	Function:       SubCallback_actuator
	Description:    接收得到车辆信息
	Input:          radar_msgs::sensorradar::ConstPtr msg
	Output:         无
	Return:         无
	*************************************************/
	 void SubCallback_actuator(const common_msgs::actuator::ConstPtr &msg);

   public:
     int m_runningmode;
     bool bdebug;
     std::string updatelog;
     
     ros::NodeHandle m_handle;
     //interface
     ros::Subscriber sub_radardriver;
     ros::Publisher  pub_radar;
	 ros::Subscriber sub_actuator;
     //data
     ARSData m_arsdata; //雷达原始数据
     int validNum; //雷达障碍物数目

	 common_msgs::actuator m_actuatorData;//车辆数据

    /************************************************************************/
    /*                         处理相关的变量和算法                         */
    /************************************************************************/
    RadarTargetInfo_Rec Target[MaxTarNum];//Track targets library
    static const int FilterFactor = 3;//*1/4，一阶滞后滤波系数，表示相信原来量的程度
    static const int FREQ = 20;//frequency = 20Hz,雷达数据的接收频率
    int Index;//障碍物序号
    int NumOfTar; //有效障碍物个数
    int Err_Cnt;  //雷达错误代码
	std::string node_name;
 };



