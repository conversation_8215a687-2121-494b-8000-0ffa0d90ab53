
/**************************************************************************
Copyright: 	2021-2023 VANJEE Technology.
File name: 	radar.cpp
Description: 毫米波雷达数据处理程序。	
Author: 	zhuxuekui
Version: 	V1.0
Date: 		2019.6
History: 	无
**************************************************************************/
 #include"radar.h"

	/*************************************************
	Function:       radarNode
	Description:    构造函数，主要用于数据初始化
	Input:          handle：ros句柄
	Output:         无
	Return:         无
	*************************************************/  
 radarNode::radarNode(ros::NodeHandle handle)
 {

   m_runningmode = 0;
   updatelog = "null";
   bdebug = false;
   validNum = 256;
   
   handle.param("runningmode",m_runningmode,m_runningmode );
   handle.param("updatelog",updatelog,updatelog);
   handle.param("bdebug",bdebug,bdebug);


   m_handle = handle;

   //paramters
   std::cout<<FRED("Copyright©2021-2023 VANJEE Technology. All rights reserved ")<<std::endl;
   std::cout<<FYEL("*****radar:parameters*******************")<<std::endl;
   std::cout<<FGRN("pc_runningmode: ")<<m_runningmode<<std::endl;
   std::cout<<FGRN("update_log: ")<<updatelog<<std::endl;
   std::cout<<FGRN("output_debuginfo:")<<bdebug<<std::endl;
   std::cout<<FYEL("*****radar:parameters end***************")<<std::endl; 
 }


 /*
  *author:zhuxuekui
  *date:2016.07.21
  *detail:run program
  */
 void radarNode::run()
 {
     
     node_name = ros::this_node::getName(); //获取节点名称 
     std::string node_name_deal = "";
     if(node_name == "/radarfront_deal") node_name_deal = "/radarfront_driver";
     else if(node_name == "/radarback_deal") node_name_deal = "/radarback_driver";
     else if(node_name == "/radarleftback_deal") node_name_deal = "/radarleftback_driver";
     else if(node_name == "/radarrightback_deal") node_name_deal = "/radarrightback_driver";
     else if(node_name == "/radarleftfront_deal") node_name_deal = "/radarleftfront_driver";
     else if(node_name == "/radarrightfront_deal") node_name_deal = "/radarrightfront_driver";

    //  std::cout << "node name: " << node_name << std::endl;
     sub_radardriver = m_handle.subscribe(node_name_deal+"/radardriver",1,&radarNode::SubCallback_radar,this);
     
     pub_radar = m_handle.advertise<radar_msgs::sensorradar>(node_name+"/radar",1); 

     sub_actuator = m_handle.subscribe("actuator",1000, &radarNode::SubCallback_actuator, this);
 
     ros::spin();
 }


 /*
  *author:zhuxuekui
  *date:2016.07.21
  *detail: publish msg
  */
 void radarNode::PubMsg(ros::Publisher pub)
 {
    radar_msgs::sensorradar msg;
    memset(&msg,0,sizeof(msg));
    msg.isvalid = 1;
    if(NumOfTar > 0)
    {
        //std::cout << "---------------------------->" << std::endl;
        if(node_name == "/radarfront_deal")
        {
           // std::cout << "front radar " << std::endl;
            for(int i = 0; i < MaxTarNum; ++i)
            {
                if(Target[i].TrackState >= 1)
                {
                    radar_msgs::radarobject ob;
                    memset(&ob,0,sizeof(ob));
                    float x = (float)(Target[i].LatPos[Index])*1.0/100.0;       //m
                    float y = (float)(Target[i].Distance[Index])*1.0/100.0;     //m
                    if(fabs(x) < MAXLATDIS && fabs(y) < MAXLONGDIS)
                    {
                        ob.id =  i+1;//Target[i].TrackID;
                        ob.x = x;
                        ob.y = y; //no
                        ob.relspeedy = (float)(Target[i].RelSpd_Long)*1.0/100;    //cm/s-> m/s;
                        ob.relspeedx = (float)(Target[i].RelSpd_Lat)*1.0/100;     //cm/s-> m/s;
                        ob.width = (float)(Target[i].width)*1.0/100;          // m;
                        ob.length = (float)(Target[i].length)*1.0/100;        // m;
                        ob.classification = Target[i].classification;         // m;
                        ob.flag = Target[i].TrackState; //
                        msg.obs.push_back(ob);
                    }
                   //cout << "dis: " << ob.y << "  lat: " << ob.x << "  relspeed: " <<  ob.relspeed << endl;                
                }
            }
            //cout << "validNum: " << NumOfTar << endl;
        }
        else
        {
            for(int i = 0; i < validNum; ++i)
            {
                if(Target[i].TrackState >= 2)
                {
                    radar_msgs::radarobject ob;
                    memset(&ob,0,sizeof(ob));
                    float x = (float)(Target[i].LatPos[Index])*1.0/100.0;       //m
                    float y = (float)(Target[i].Distance[Index])*1.0/100.0;     //m

                   // if(fabs(x) < MAXLATDIS && fabs(y) < MAXLONGDIS)
                    {
                        ob.id =  Target[i].TrackID;
                        ob.x = x;
                        ob.y = y; //no
                        ob.relspeedy = (float)(Target[i].RelSpd_Long)*1.0/100;    //cm/s-> m/s;
                        ob.relspeedx = (float)(Target[i].RelSpd_Lat)*1.0/100;     //cm/s-> m/s;
                        ob.width = (float)(Target[i].width)*1.0/100;          // m;
                        ob.length = (float)(Target[i].length)*1.0/100;        // m;
                        ob.classification = Target[i].classification;         // m;
                        ob.flag = 1; //which radar
                        ob.DistLat_rms = Target[i].disLat_rms;
                        ob.DistLong_rms = Target[i].disLong_rms;
                        ob.VrelLong_rms = Target[i].vreLong_rms;
                        ob.VrelLat_rms = Target[i].vreLat_rms;
                        ob.Orientation_rms = Target[i].orientation_angle_rms;
                        ob.DynProp = Target[i].dynProp;
                        ob.RCS = Target[i].rcs;
                        ob.ProbOfExist = Target[i].probOfExist;
                        ob.ArelLong = Target[i].areLong;
                        ob.ArelLat = Target[i].areLat;
                        ob.OrientationAngel = Target[i].orientation_angle;
                        ob.CollDetRegionBitfield = Target[i].collDetRegionBitField;

                        msg.obs.push_back(ob);
                        // if(abs(x) < 1.5 && y < 30)
                        // {
                        //   cout << "id: " << (int)(ob.id) << "  dis: " << ob.y << "  lat: " << ob.x << "  relspeed: " <<  ob.relspeedy << endl;                
                            
                        //  //std::cout<<FYEL(DEBUGHEAD)<<"lon dis is "<<y<<std::endl;
                        //  //std::cout<<FYEL(DEBUGHEAD)<<"lat dis is "<<x<<std::endl;
                        // } 
                    }
                //cout << "dis: " << ob.y << "  lat: " << ob.x << "  relspeed: " <<  ob.relspeed << endl;                
                }
            }
            //cout << "validNum: " << NumOfTar << endl;
        }

    }
    else
    {
        radar_msgs::radarobject ob;
        memset(&ob,0,sizeof(ob));
        ob.x = 200;
        ob.y = 200;
        ob.relspeedy = 0;
        ob.relspeedx = 0;
        ob.flag = 1;
        msg.obs.push_back(ob);
    }
    //std::cout<<FYEL(DEBUGHEAD)<<"size: "<<msg.obs.size()<<std::endl;
    pub.publish(msg);
 }



void radarNode::SubCallback_actuator(const common_msgs::actuator::ConstPtr &msg)
{
    memset(&m_actuatorData,0,sizeof(m_actuatorData)); 
    m_actuatorData = *msg;
    //std::cout << ">>>>>>>>>>>>>>>>>>>>>>>>>>>" << m_actuatorData.epsangle << std::endl;
}

 /*
  *author:zhuxuekui
  *date:2016.07.21
  *detail:callback function
  */
 void radarNode::SubCallback_radar(const radar_msgs::sensorradar::ConstPtr msg)
 {
     
     //memset(&m_arsdata,0,sizeof(m_arsdata));
     for(int i = 0; i < 256; ++i)
     {
         m_arsdata.radarRxData[i].IsUpdatedFlag = 0;
     }
     validNum = msg->obs.size();
     for(int i = 0; i < validNum; ++i)
     {
         m_arsdata.radarRxData[i].Distance = msg->obs[i].y*100;
         m_arsdata.radarRxData[i].LatPos = msg->obs[i].x*100; //m -> cm
         m_arsdata.radarRxData[i].RelSpd_Lat = msg->obs[i].relspeedx*100; //m/s-->cm/s
         m_arsdata.radarRxData[i].RelSpd_Long = msg->obs[i].relspeedy*100; //m/s-->cm/s
         m_arsdata.radarRxData[i].width = msg->obs[i].width*100; //m/s-->cm/s
         m_arsdata.radarRxData[i].length = msg->obs[i].length*100; //m/s-->cm/s
         m_arsdata.radarRxData[i].classification = msg->obs[i].classification; //m/s-->cm/s
         m_arsdata.radarRxData[i].id = msg->obs[i].id;
         m_arsdata.radarRxData[i].IsUpdatedFlag = 1;

         m_arsdata.radarRxData[i].rcs = msg->obs[i].RCS;
         m_arsdata.radarRxData[i].orientation_angle = msg->obs[i].OrientationAngel;
         m_arsdata.radarRxData[i].disLong_rms = msg->obs[i].DistLong_rms;
         m_arsdata.radarRxData[i].disLat_rms = msg->obs[i].DistLat_rms;
         m_arsdata.radarRxData[i].vreLong_rms = msg->obs[i].VrelLong_rms;
         m_arsdata.radarRxData[i].vreLat_rms = msg->obs[i].VrelLat_rms;
         m_arsdata.radarRxData[i].areLat_rms = msg->obs[i].ArelLat_rms;
         m_arsdata.radarRxData[i].areLon_rms = msg->obs[i].ArelLong_rms;
         m_arsdata.radarRxData[i].orientation_angle_rms = msg->obs[i].Orientation_rms;
         m_arsdata.radarRxData[i].areLong = msg->obs[i].ArelLong;
         m_arsdata.radarRxData[i].areLat = msg->obs[i].ArelLat;
         m_arsdata.radarRxData[i].probOfExist = msg->obs[i].ProbOfExist;
         m_arsdata.radarRxData[i].dynProp = msg->obs[i].DynProp;
         m_arsdata.radarRxData[i].collDetRegionBitField = msg->obs[i].CollDetRegionBitfield;

         
         //cout << "dis: " << msg->obs[i].y << "  lat: " << msg->obs[i].x << "  relspeed: " <<  msg->obs[i].relspeed << endl;
     }



   //  if(node_name == "/radarfront_deal")
     {
         TargetSelect();
     } 
    //  else
    //   {
    //      TargetRawSelect();
    //   }

     PubMsg(pub_radar);
 }


int radarNode::TargetRawSelect()
{
    NumOfTar = 0;
    Index = ((Index + 1) < DETECT_FRAME)?(Index + 1):(0);
    for (int i = 0; i < validNum; i ++)//
    {
        if ( (fabs(m_arsdata.radarRxData[i].Distance) < 6000) && (fabs(m_arsdata.radarRxData[i].LatPos) < 2000) )//) //(fabs(m_arsdata.radarRxData[i].Distance) > 10) && 
        {
            memset(Target+NumOfTar, 0, sizeof(RadarTargetInfo_Rec));
            Target[NumOfTar].Distance[Index] = m_arsdata.radarRxData[i].Distance;
            //std::cout << "dis: "<< m_arsdata.radarRxData[i].Distance << endl;
            Target[NumOfTar].LatPos[Index] = m_arsdata.radarRxData[i].LatPos;
            Target[NumOfTar].Total ++;
            Target[NumOfTar].RelSpd_Long = m_arsdata.radarRxData[i].RelSpd_Long;
            Target[NumOfTar].RelSpd_Lat =  m_arsdata.radarRxData[i].RelSpd_Lat;
            Target[NumOfTar].width = m_arsdata.radarRxData[i].width;
            Target[NumOfTar].length = m_arsdata.radarRxData[i].length;
            Target[NumOfTar].classification = m_arsdata.radarRxData[i].classification;      
            Target[NumOfTar].TrackState = 2;
            Target[NumOfTar].TrackID = i;
            NumOfTar ++;

            Target[NumOfTar].rcs = m_arsdata.radarRxData[i].rcs;   
            Target[NumOfTar].orientation_angle = m_arsdata.radarRxData[i].orientation_angle;   
            Target[NumOfTar].disLong_rms = m_arsdata.radarRxData[i].disLong_rms;   
            Target[NumOfTar].disLat_rms = m_arsdata.radarRxData[i].disLat_rms;   
            Target[NumOfTar].vreLong_rms = m_arsdata.radarRxData[i].vreLong_rms;   
            Target[NumOfTar].vreLat_rms = m_arsdata.radarRxData[i].vreLat_rms;   
            Target[NumOfTar].areLat_rms = m_arsdata.radarRxData[i].areLat_rms;   
            Target[NumOfTar].areLon_rms = m_arsdata.radarRxData[i].areLon_rms;   
            Target[NumOfTar].orientation_angle_rms = m_arsdata.radarRxData[i].orientation_angle_rms;   
            Target[NumOfTar].areLong = m_arsdata.radarRxData[i].areLong;   
            Target[NumOfTar].areLat = m_arsdata.radarRxData[i].areLat;   
            Target[NumOfTar].probOfExist = m_arsdata.radarRxData[i].probOfExist;   
            Target[NumOfTar].dynProp = m_arsdata.radarRxData[i].dynProp;   
            Target[NumOfTar].collDetRegionBitField = m_arsdata.radarRxData[i].collDetRegionBitField;   
            
        }
    }
    /*End of creatint or updatint target library*/
    return 0;
}

/*************************************************
	Function:       TargetSelect
	Description:    算法处理主程序
	Input:          ARSData &radar_data——接收解析到的雷达数据
	Output:         处理是否异常
	Return:         无
	*************************************************/
	int radarNode::TargetSelect()
	{
		int i,j;
		unsigned char Cnt = 0;
		int deltaDis=0,deltaLatPos=0,deltaRelSpd=0;
        //去除相近目标
        std::vector<ARSRecData>  objects;
        int rx = 0, ry = 0,vy = 0, vx = 0;
        for (i = 0; i < validNum; i ++)
        {
            rx = (int)m_arsdata.radarRxData[i].LatPos;//cm
		    ry = (int)m_arsdata.radarRxData[i].Distance;
            vy = (int)m_arsdata.radarRxData[i].RelSpd_Long;
            vx = (int)m_arsdata.radarRxData[i].RelSpd_Lat;
            bool unmatched = true;
            int minindex = -1;
            for( j = 0; j < objects.size(); ++j)
            {
                 ARSRecData ob = objects[j];
                 deltaDis = abs(ry - ob.Distance);// 
                 deltaLatPos = abs(rx  - ob.LatPos);//cm
                 if(deltaDis < 80 && deltaLatPos < 50)//满足条件就更新
                 {
                     unmatched = false;
                     minindex = j;
                 }
            } 

            if(!unmatched)
            {
                //更新
                int ovy = objects[minindex].RelSpd_Long;
                int ovx = objects[minindex].RelSpd_Lat;
                if(fabs(ovy) > fabs(vy))
                {
                    objects[minindex].RelSpd_Long = ovy;
                    objects[minindex].RelSpd_Lat = ovx;
                }
                else
                {
                    objects[minindex].RelSpd_Long = vy;
                    objects[minindex].RelSpd_Lat =  vx;
                }
            }
            else
            {
               objects.push_back(m_arsdata.radarRxData[i]);
            }
        }

        for (int k = 0; k < objects.size(); k ++)
        {
            objects[k].IsUpdatedFlag = 1;
        }

        /*Creat or update target library*/
        Index = ((Index + 1) < DETECT_FRAME)?(Index + 1):(0);
        for (j = 0; j < MaxTarNum; j ++)//更新可更新的库中目标
        {
            if (0 != Target[j].TrackState)  //已跟踪目标且该目标未被更新
            {
                bool unmatched = true;
                int minindex = -1;
                long mindis = 10000*10000; //cm
                for (i = 0; i < objects.size(); i ++)
                {
                    if (objects[i].IsUpdatedFlag==1)
                    {
                        deltaDis = abs((int)objects[i].Distance  - Target[j].Distance[LAST(Index)]);// - Target[j].RelSpd_Long / 20 
                        deltaLatPos = abs((int)objects[i].LatPos   - Target[j].LatPos[LAST(Index)]);//- Target[j].RelSpd_Lat / 20
                        deltaRelSpd = abs((int)objects[i].RelSpd_Long - Target[j].RelSpd_Long);
                        long dis = deltaDis*deltaDis + deltaLatPos*deltaLatPos;
                        if ((deltaDis < DELTA_DIS) && (deltaLatPos < DELTA_LAT) && (dis<mindis) && (dis < DELTA_MINDIS)) // && (deltaRelSpd < DELTA_REL)
                        {
                           	minindex = i;
                            unmatched = false;
                            mindis = dis;
                        }
                    }
                }
                if (!unmatched)//找到可更新的目标
                {
                    objects[minindex].IsUpdatedFlag = 0;
                    Target[j].Distance[Index] = objects[minindex].Distance;
                    Target[j].LatPos[Index] = (Target[j].LatPos[LAST(Index)] + objects[minindex].LatPos)/2; //objects[i].LatPos;//
                    Target[j].Total = (Target[j].Total < DETECT_FRAME) ? (Target[j].Total + 1):(DETECT_FRAME);
                    Target[j].RelSpd_Long = objects[minindex].RelSpd_Long;
                    Target[j].RelSpd_Lat = objects[minindex].RelSpd_Lat;
                    Target[j].width = objects[minindex].width;
                    Target[j].length = objects[minindex].length;
                    Target[j].classification = objects[minindex].classification;                                
                    Target[j].TrackID = objects[minindex].id;
                    Target[j].LostCnt = 0;


                    Target[NumOfTar].rcs = m_arsdata.radarRxData[i].rcs;   
                    Target[NumOfTar].orientation_angle = m_arsdata.radarRxData[i].orientation_angle;   
                    Target[NumOfTar].disLong_rms = m_arsdata.radarRxData[i].disLong_rms;   
                    Target[NumOfTar].disLat_rms = m_arsdata.radarRxData[i].disLat_rms;   
                    Target[NumOfTar].vreLong_rms = m_arsdata.radarRxData[i].vreLong_rms;   
                    Target[NumOfTar].vreLat_rms = m_arsdata.radarRxData[i].vreLat_rms;   
                    Target[NumOfTar].areLat_rms = m_arsdata.radarRxData[i].areLat_rms;   
                    Target[NumOfTar].areLon_rms = m_arsdata.radarRxData[i].areLon_rms;   
                    Target[NumOfTar].orientation_angle_rms = m_arsdata.radarRxData[i].orientation_angle_rms;   
                    Target[NumOfTar].areLong = m_arsdata.radarRxData[i].areLong;   
                    Target[NumOfTar].areLat = m_arsdata.radarRxData[i].areLat;   
                    Target[NumOfTar].probOfExist = m_arsdata.radarRxData[i].probOfExist;   
                    Target[NumOfTar].dynProp = m_arsdata.radarRxData[i].dynProp;   
                    Target[NumOfTar].collDetRegionBitField = m_arsdata.radarRxData[i].collDetRegionBitField;   

                    if (Target[j].Total == DETECT_FRAME)
                    {
                        Target[j].TrackState = 2;
                    }
                    else
                    {
                        Target[j].TrackState = 1;
                    }
                }
                else //未找到可更新的一致目标，暂时赋给估计值----若没进break,则i=8,超出范围.
                {
                    if (Target[j].Total == DETECT_FRAME)
                    {
                        Target[j].TrackState = 3;
                        Target[j].LostCnt ++;

                        //std::cout << "---------------------" << m_actuatorData.epsangle <<std::endl;
                        if(fabs(m_actuatorData.epsangle) < STRAIGHTROAD)
                        {
                            if (Target[j].LostCnt > LOST_MAX_NUM)//跟踪终止
                            {
                                memset(Target+j, 0, sizeof(RadarTargetInfo_Rec));
                                NumOfTar --;
                                Target[j].LostType = 0;
                                Target[j].Total = 0;
                                Target[j].TrackState = 0;
                            }
                            else
                            {
                                Target[j].Distance[Index] = Target[j].Distance[LAST(Index)] + Target[j].RelSpd_Long / 10; //预测部分 需要考虑到时间延迟
                                //Target[j].LatPos[Index] = Target[j].LatPos[LAST(Index)] + Target[j].RelSpd_Lat / 10;
                            }
                        }
                        else
                        {
                            if (Target[j].LostCnt > 2)//跟踪终止
                            {
                                memset(Target+j, 0, sizeof(RadarTargetInfo_Rec));
                                NumOfTar --;
                                Target[j].LostType = 0;
                                Target[j].Total = 0;
                                Target[j].TrackState = 0;
                            }
                            else
                            {
                                Target[j].Distance[Index] = Target[j].Distance[LAST(Index)] + Target[j].RelSpd_Long / 20; //预测部分 需要考虑到时间延迟
                                //Target[j].LatPos[Index] = Target[j].LatPos[LAST(Index)] + Target[j].RelSpd_Lat / 10;
                            }
                        }

                    }
                    else
                    {
                        memset(Target+j, 0, sizeof(RadarTargetInfo_Rec));
                        NumOfTar --;
                        Target[j].LostType = 4;
                        Target[j].Total = 0;
                        Target[j].TrackState = 0;
                    }
                }
            }
        }//for (j = 0; j < MaxTarNum; j ++)////更新可更新的库中目标
       
        for (i = 0; i < validNum; i ++)//寻找接收缓存中未被利用的目标，新建该轨迹
        {
            if (objects[i].IsUpdatedFlag==1)//接收缓存中已被更新有效目标
            {
                for(j = 0; j < MaxTarNum; j ++)//寻找库中空闲位置
                {
                    if (0 == Target[j].TrackState) //未被跟踪，新建轨迹
                    {
                        objects[i].IsUpdatedFlag = 0; //已处理
                        memset(Target+j, 0, sizeof(RadarTargetInfo_Rec));
                        Target[j].Distance[Index] = objects[i].Distance;
                        Target[j].LatPos[Index] = objects[i].LatPos;

                        Target[j].Total = 1;
                        Target[j].RelSpd_Long = objects[i].RelSpd_Long;
                        Target[j].RelSpd_Lat = objects[i].RelSpd_Lat;
                        Target[j].width = objects[i].width;
                        Target[j].length = objects[i].length;
                        Target[j].classification = objects[i].classification;                        
                        Target[j].TrackState = 1;
                        Target[j].TrackID = objects[i].id;
                        NumOfTar ++;


                        Target[NumOfTar].rcs = m_arsdata.radarRxData[i].rcs;   
                        Target[NumOfTar].orientation_angle = m_arsdata.radarRxData[i].orientation_angle;   
                        Target[NumOfTar].disLong_rms = m_arsdata.radarRxData[i].disLong_rms;   
                        Target[NumOfTar].disLat_rms = m_arsdata.radarRxData[i].disLat_rms;   
                        Target[NumOfTar].vreLong_rms = m_arsdata.radarRxData[i].vreLong_rms;   
                        Target[NumOfTar].vreLat_rms = m_arsdata.radarRxData[i].vreLat_rms;   
                        Target[NumOfTar].areLat_rms = m_arsdata.radarRxData[i].areLat_rms;   
                        Target[NumOfTar].areLon_rms = m_arsdata.radarRxData[i].areLon_rms;   
                        Target[NumOfTar].orientation_angle_rms = m_arsdata.radarRxData[i].orientation_angle_rms;   
                        Target[NumOfTar].areLong = m_arsdata.radarRxData[i].areLong;   
                        Target[NumOfTar].areLat = m_arsdata.radarRxData[i].areLat;   
                        Target[NumOfTar].probOfExist = m_arsdata.radarRxData[i].probOfExist;   
                        Target[NumOfTar].dynProp = m_arsdata.radarRxData[i].dynProp;   
                        Target[NumOfTar].collDetRegionBitField = m_arsdata.radarRxData[i].collDetRegionBitField;   
                        break;
                    }
                }
                if (objects[i].IsUpdatedFlag==1)//未找到空闲位置,寻找丢失次数最大的目标
                {
                    int tempID = MaxTarNum;//目标库中临时ID
                    int MaxLostCnt = 0;
                    for(int k = 0; k < MaxTarNum; k ++)
                    {
                        if (3 == Target[k].TrackState)
                        {
                            if (MaxLostCnt < Target[k].LostCnt)//最少被执行1次
                            {
                                tempID = k;
                                MaxLostCnt = Target[k].LostCnt;
                            }
                        }
                    }

                    if (tempID != MaxTarNum)
                    {
                        objects[i].IsUpdatedFlag = 0;
                        memset(Target+tempID, 0, sizeof(RadarTargetInfo_Rec));
                        Target[tempID].LostType = 1;
                        Target[tempID].Distance[Index] = objects[i].Distance;
                        Target[tempID].LatPos[Index] = objects[i].LatPos;
                        Target[tempID].Total = 1;

                        Target[tempID].RelSpd_Long = objects[i].RelSpd_Long;
                        Target[tempID].RelSpd_Lat = objects[i].RelSpd_Lat;
                        Target[tempID].width = objects[i].width;
                        Target[tempID].length = objects[i].length;
                        Target[tempID].classification = objects[i].classification;                        
                        Target[tempID].TrackState = 1;
                        Target[tempID].TrackID = objects[i].id;

                        Target[NumOfTar].rcs = m_arsdata.radarRxData[i].rcs;   
                        Target[NumOfTar].orientation_angle = m_arsdata.radarRxData[i].orientation_angle;   
                        Target[NumOfTar].disLong_rms = m_arsdata.radarRxData[i].disLong_rms;   
                        Target[NumOfTar].disLat_rms = m_arsdata.radarRxData[i].disLat_rms;   
                        Target[NumOfTar].vreLong_rms = m_arsdata.radarRxData[i].vreLong_rms;   
                        Target[NumOfTar].vreLat_rms = m_arsdata.radarRxData[i].vreLat_rms;   
                        Target[NumOfTar].areLat_rms = m_arsdata.radarRxData[i].areLat_rms;   
                        Target[NumOfTar].areLon_rms = m_arsdata.radarRxData[i].areLon_rms;   
                        Target[NumOfTar].orientation_angle_rms = m_arsdata.radarRxData[i].orientation_angle_rms;   
                        Target[NumOfTar].areLong = m_arsdata.radarRxData[i].areLong;   
                        Target[NumOfTar].areLat = m_arsdata.radarRxData[i].areLat;   
                        Target[NumOfTar].probOfExist = m_arsdata.radarRxData[i].probOfExist;   
                        Target[NumOfTar].dynProp = m_arsdata.radarRxData[i].dynProp;   
                        Target[NumOfTar].collDetRegionBitField = m_arsdata.radarRxData[i].collDetRegionBitField;   
                        break;
                    }
                }
            }
        }//for (i = 0; i < Max_Target_Num; i ++)//寻找接收缓存中未被利用的目标，新建该轨迹
        /*End of creatint or updatint target library*/
		return 0;
	}
