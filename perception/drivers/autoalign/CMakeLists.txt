cmake_minimum_required(VERSION 2.8.3)
cmake_policy(SET CMP0057 NEW)
project(autoalign)

find_package(rclcpp REQUIRED COMPONENTS)
if(rclcpp_FOUND)
  message(=============================================================)
  message("-- ROS2 Found. ROS2 Support is turned On.")
  message(=============================================================)
  add_definitions(-DROS2_FOUND)
  include_directories(${rclcpp_INCLUDE_DIRS}
  src)

  find_package(ament_cmake REQUIRED)
  find_package(sensor_msgs REQUIRED)
  find_package(std_msgs REQUIRED)
  find_package(visualization_msgs)

else()
  find_package(roscpp QUIET)
  if(roscpp_FOUND)
    message(=============================================================)
    message("-- ROS1 Found. ROS1 Support is turned On.")
    message(=============================================================)
    add_definitions(-DROS1_FOUND)


    find_package(catkin REQUIRED COMPONENTS
      cv_bridge
      image_transport
      roscpp
      rospy
      sensor_msgs
      std_msgs
      genmsg
      actionlib_msgs
      actionlib
      message_generation
      dynamic_reconfigure
      roslib
    )
  endif(roscpp_FOUND)
endif(rclcpp_FOUND)

## Generate added messages and services with any dependencies listed here
#  generate_messages(
#    DEPENDENCIES
#       sensor_msgs
#       std_msgs
#       actionlib_msgs
#       std_msgs 
#  )

if(roscpp_FOUND)
  ## Generate dynamic reconfigure parameters in the 'cfg' folder
  generate_dynamic_reconfigure_options(
  #   cfg/DynReconf1.cfg
  #   cfg/DynReconf2.cfg
      cfg/autoalign.cfg
  #   nodeparams.cfg
  )
endif(roscpp_FOUND)

if(roscpp_FOUND)
  catkin_package(
  #  INCLUDE_DIRS include
  #  LIBRARIES usbcamera_pub
    CATKIN_DEPENDS cv_bridge image_transport roscpp rospy sensor_msgs std_msgs message_runtime
  #  DEPENDS system_lib
  )
  


  include_directories(
    ${catkin_INCLUDE_DIRS}
    ${dynamic_reconfigure_PACKAGE_PATH}/cmake/cfgbuild.cmake
  )
endif(roscpp_FOUND)

file(GLOB_RECURSE EXTRA_FILES */*)
add_custom_target(${PROJECT_NAME}_OTHER_FILES ALL WORKING_DIRECTORY ${PROJECT_SOURCE_DIR} SOURCES ${EXTRA_FILES})


add_executable(autoalign src/main.cpp src/mapcfg.cpp)
#add_dependencies(autoalign ${PROJECT_NAME}_gencfg)

if(roscpp_FOUND)
add_dependencies(autoalign ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
target_link_libraries(autoalign ${catkin_LIBRARIES})


# Mark libraries for installation
# See http://docs.ros.org/melodic/api/catkin/html/howto/format1/building_libraries.html
install(TARGETS ${PROJECT_NAME}
        ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
        LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
        RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
        )

# Mark cpp header files for installation


# Mark other files for installation (e.g. launch and bag files, etc.)
install(DIRECTORY launch cfg
        DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}
        )

endif(roscpp_FOUND)

if(rclcpp_FOUND)
  ament_target_dependencies(${PROJECT_NAME}
    rclcpp
    std_msgs
    sensor_msgs
    visualization_msgs
  )

  install(TARGETS
    ${PROJECT_NAME}
    DESTINATION lib/${PROJECT_NAME})

  install(DIRECTORY
    launch
    cfg
    DESTINATION share/${PROJECT_NAME})

  ament_package()
endif(rclcpp_FOUND)