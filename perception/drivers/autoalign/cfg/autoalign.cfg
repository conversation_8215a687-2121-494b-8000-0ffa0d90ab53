#! /usr/bin/env python



PACKAGE='autoalign'

import roslib; roslib.load_manifest(PACKAGE)
from math import pi
from dynamic_reconfigure.parameter_generator import *
from dynamic_reconfigure.parameter_generator_catkin import *

gen = ParameterGenerator()

gen.add("delta_yaw",   double_t,   0, "delta yaw value from car",  0, -180, 180)

gen.add("delta_pitch",   double_t,   0, "delta pitch value from car",  0, -180, 180)

gen.add("delta_roll",   double_t,   0, "delta roll value from car",  0, -180, 180)

gen.add("delta_x",   double_t,   0, "delta x value from car",  0, -10, 10)

gen.add("delta_y",   double_t,   0, "delta y value from car",  0, -10, 10)

gen.add("delta_z",   double_t,   0, "delta z value from car",  0, -10, 10)

gen.add("leftlidar",   bool_t,   0, "align left sensor ")

gen.add("rightlidar",   bool_t,   0, "align right sensor ")

gen.add("midlidar",   bool_t,   0, "align middle sensor ")

gen.add("frontlidar",   bool_t,   0, "align front sensor ")

gen.add("backlidar",   bool_t,   0, "align back sensor ")

gen.add("frontradar",   bool_t,   0, "align front radar sensor ")

gen.add("leftfrontradar",   bool_t,   0, "align left front radar sensor ")

gen.add("rightfrontradar",   bool_t,   0, "align right front radar sensor ")

gen.add("backradar",   bool_t,   0, "align back radar sensor ")

gen.add("leftbackradar",   bool_t,   0, "align left back radar sensor ")

gen.add("rightbackradar",   bool_t,   0, "align right back radar sensor ")

gen.add("luxlidar",   bool_t,   0, "align lux4 lidar sensor ")

gen.add("lidargps",   bool_t,   0, "align lidar gps sensor ")


exit(gen.generate(PACKAGE, "autoalign", "autoalign"))

