/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2025-07-14 11:18:20
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-07-14 11:21:56
 * @FilePath: /src/perception/drivers/autoalign/src/autoalignConfig.h
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
// autoalignConfig.h
#pragma once

#include <vector>
#include <string>

namespace autoalign {

struct autoalignConfig {
    double delta_yaw = 0.0;
    double delta_pitch = 0.0;
    double delta_roll = 0.0;
    double delta_x = 0.0;
    double delta_y = 0.0;
    double delta_z = 0.0;

    bool leftlidar = false;
    bool midlidar = false;
    bool frontlidar = false;
    bool backlidar = false;
    bool rightlidar = false;
    bool luxlidar = false;

    bool frontradar = false;
    bool leftfrontradar = false;
    bool rightfrontradar = false;

    bool backradar = false;
    bool leftbackradar = false;
    bool rightbackradar = false;
    bool lidargps = false;
};

} // namespace autoalign