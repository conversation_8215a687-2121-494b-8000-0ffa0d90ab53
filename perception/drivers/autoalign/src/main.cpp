/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2025-02-13 16:27:21
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-07-14 09:33:52
 * @FilePath: /src/perception/drivers/autoalign/src/main.cpp
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
/*************************************************************************
        > File Name: ivconfig.cpp
        > Author:
        > Mail:
        > Created Time: 2016年08月01日 星期一 17时22分40秒
 ************************************************************************/

#include "mapcfg.h"
#include <cmath>
#include <iostream>
using namespace std;

#ifdef ROS1_FOUND
int main(int argc, char *argv[]) {
  ros::init(argc, argv, "autoalign");
  ros::NodeHandle nh;
  mapcfg cfg(nh);
  return 0;
}

#endif
#ifdef ROS2_FOUND 
int main(int argc,char * argv[]){
  rclcpp::init(argc,argv);
  auto node = rclcpp::Node::make_shared("autoalign");
  auto cfgnode = new mapcfg(node);
  return 0;
}
#endif