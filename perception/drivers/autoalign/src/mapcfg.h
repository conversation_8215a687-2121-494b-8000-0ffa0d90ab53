
#ifndef _CONFIG_H
#define _CONFIG_H

#ifdef ROS1_FOUND
// c++ lib
#include <vector>
// ROS lib
#include "ros/ros.h"
#include "ros/time.h"
// other lib
#include <sstream>

#include "std_msgs/String.h"
#include <dynamic_reconfigure/server.h>
#include <autoalign/autoalignConfig.h>



#endif
#ifdef ROS2_FOUND
// c++ lib
#include <vector>
// ROS lib
#include "rclcpp/rclcpp.hpp"
#include "rclcpp/time.hpp"
// other lib
#include <sstream>

#include "std_msgs/msg/string.hpp"  
#include <rclcpp/parameter_client.hpp>
#include "autoalignConfig.h"
#endif

using namespace std;
// change you configure class here
typedef autoalign::autoalignConfig config_;
#ifdef ROS1_FOUND
class mapcfg {
    public:
        mapcfg(ros::NodeHandle nh);
        ~mapcfg();

	/*************************************************
	Function:       callback
	Description:    回调函数，并赋值
	Input:          配置参数，等级
	Output:         无
	Return:         无
	*************************************************/  
        void callback(config_ &config, uint32_t level);

    private:
        config_ m_config;
        ros::Subscriber sub_;
        ros::Publisher pub_;
};

#endif
#ifdef ROS2_FOUND
class mapcfg {
    public:
        mapcfg(rclcpp::Node::SharedPtr& node);
        ~mapcfg();

	/*************************************************
	Function:       callback
	Description:    回调函数，并赋值
	Input:          配置参数，等级
	Output:         无
	Return:         无
	*************************************************/  
        void callback(config_ &config, uint32_t level);

    private:
        config_ m_config;
        rclcpp::Node::SharedPtr node_;
        rclcpp::Subscription<std_msgs::msg::String>::SharedPtr sub_;
        rclcpp::Publisher<std_msgs::msg::String>::SharedPtr pub_;
};

#endif

#endif
