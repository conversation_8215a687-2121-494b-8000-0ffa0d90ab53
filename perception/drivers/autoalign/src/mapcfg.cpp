/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2025-02-13 16:27:21
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-07-14 13:11:50
 * @FilePath: /src/perception/drivers/autoalign/src/mapcfg.cpp
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */

#include "mapcfg.h"
#include <iostream>
using namespace std;

#ifdef ROS1_FOUND
mapcfg::mapcfg(ros::NodeHandle nh) {
    std::string s0 = "unknown";
    int s1 = 0;
    double s2 = 0.0;
    bool s3;
    ROS_INFO("Starting to spin...");
    // boost bind invoking object member function;
    dynamic_reconfigure::Server<config_> srv;
    dynamic_reconfigure::Server<config_>::CallbackType f;
    f = boost::bind(&mapcfg::callback, this, _1, _2);
    srv.setCallback(f);
    ros::Rate loop_rate(1);

    while (ros::ok()) 
    {
        // please pay attention to the useage of ROS_INFO and ROS_INFO_STREAM
        ros::spinOnce();
        loop_rate.sleep();
    }
}

mapcfg::~mapcfg() 
{
    
}



	/*************************************************
	Function:       callback
	Description:    回调函数，并赋值
	Input:          配置参数，等级
	Output:         无
	Return:         无
	*************************************************/  
void mapcfg::callback(config_ &config, uint32_t level) 
{

    ros::NodeHandle nh;
    m_config = config;

    nh.setParam("delta_yaw", m_config.delta_yaw);
    nh.setParam("delta_pitch", m_config.delta_pitch);
    nh.setParam("delta_roll", m_config.delta_roll);
    nh.setParam("delta_x", m_config.delta_x);
    nh.setParam("delta_y", m_config.delta_y);
    nh.setParam("delta_z", m_config.delta_z);
    nh.setParam("align_leftlidar", m_config.leftlidar);
    nh.setParam("align_midlidar", m_config.midlidar);
    nh.setParam("align_frontlidar", m_config.frontlidar);
    nh.setParam("align_backlidar", m_config.backlidar);  
    nh.setParam("align_rightlidar", m_config.rightlidar);
    nh.setParam("align_luxlidar", m_config.luxlidar);    

    nh.setParam("align_frontradar", m_config.frontradar);
    nh.setParam("align_leftfrontradar", m_config.leftfrontradar);
    nh.setParam("align_rightfrontradar", m_config.rightfrontradar);

    nh.setParam("align_backradar", m_config.backradar);
    nh.setParam("align_leftbackradar", m_config.leftbackradar);
    nh.setParam("align_rightbackradar", m_config.rightbackradar);
    nh.setParam("align_lidargps", m_config.lidargps);
    
}
#endif

#ifdef ROS2_FOUND
mapcfg::mapcfg(rclcpp::Node::SharedPtr& nh) : node_(nh) {
    RCLCPP_INFO(node_->get_logger(), "Starting to spin...");

    // 初始化动态重配置服务客户端
    auto param_client = std::make_shared<rclcpp::SyncParametersClient>(node_, "autoalign");
    if (!param_client->wait_for_service(std::chrono::seconds(5))) {
        RCLCPP_WARN(node_->get_logger(), "Parameter service not available.");
    }

    rclcpp::Rate loop_rate(1);

    while (rclcpp::ok()) {
        rclcpp::spin(node_);
        loop_rate.sleep();
    }
}

mapcfg::~mapcfg() {}

void mapcfg::callback(config_ &config, uint32_t level) {
    m_config = config;

    auto param_client = std::make_shared<rclcpp::SyncParametersClient>(
        node_, "autoalign");

    if (!param_client->wait_for_service(std::chrono::seconds(5))) {
        RCLCPP_WARN(node_->get_logger(), "Parameter service not available.");
        return;
    }

    std::vector<rclcpp::Parameter> parameters;

    // 添加所有参数
    parameters.emplace_back("delta_yaw", m_config.delta_yaw);
    parameters.emplace_back("delta_pitch", m_config.delta_pitch);
    parameters.emplace_back("delta_roll", m_config.delta_roll);
    parameters.emplace_back("delta_x", m_config.delta_x);
    parameters.emplace_back("delta_y", m_config.delta_y);
    parameters.emplace_back("delta_z", m_config.delta_z);

    parameters.emplace_back("align_leftlidar", m_config.leftlidar);
    parameters.emplace_back("align_midlidar", m_config.midlidar);
    parameters.emplace_back("align_frontlidar", m_config.frontlidar);
    parameters.emplace_back("align_backlidar", m_config.backlidar);
    parameters.emplace_back("align_rightlidar", m_config.rightlidar);
    parameters.emplace_back("align_luxlidar", m_config.luxlidar);

    parameters.emplace_back("align_frontradar", m_config.frontradar);
    parameters.emplace_back("align_leftfrontradar", m_config.leftfrontradar);
    parameters.emplace_back("align_rightfrontradar", m_config.rightfrontradar);

    parameters.emplace_back("align_backradar", m_config.backradar);
    parameters.emplace_back("align_leftbackradar", m_config.leftbackradar);
    parameters.emplace_back("align_rightbackradar", m_config.rightbackradar);
    parameters.emplace_back("align_lidargps", m_config.lidargps);

    // 原子性地设置所有参数
    auto result = param_client->set_parameters_atomically(parameters);

    if (result.successful) {
        RCLCPP_INFO(node_->get_logger(), "All parameters set successfully.");
    } else {
        RCLCPP_WARN(node_->get_logger(), "Failed to set some or all parameters.");
    }
}

#endif