/*
 * @Description  : 
 * @Version      : V1.0.0
 * <AUTHOR> hanshuangquan <EMAIL>
 * @Date         : 2024-04-11 17:01:06
 * @LastEditors  : hanshuangquan <EMAIL>
 * @LastEditTime : 2024-06-14 08:40:39
 * @FilePath     : fusionNode.cpp
 * Copyright 2024 van<PERSON>, All Rights Reserved. 
 * 2024-04-11 17:01:06
 */
#include "fusionNode.h"


FusionNode::FusionNode(ros::NodeHandle& nodeHandle):m_nh(nodeHandle)
{
    m_pLidarObjects = boost::make_shared<c_cLidarObjects>(m_nh, "/objectTrack/track_results", COMMON::SensorType::TRACKING);
    m_pCameraObjects = boost::make_shared<c_cCameraObjects>(m_nh, "image_detect_3dresults", COMMON::SensorType::CAMERA);
    m_pcSenObjectsViewer = boost::make_shared<c_cSenObjectsViewer>(m_nh, "fusionTrackedObjectsBBX", VISUALIZATION::ShowType::FusionTrackedObjects);

    m_pub_fusionTrackedObjects = m_nh.advertise<common_msgs::sensorobjects>("/fusion/fusionTrackedObjects",1, true);
}

FusionNode::~FusionNode()
{
    m_pLidarObjects = nullptr;
    m_pCameraObjects = nullptr;
    m_pcSenObjectsViewer = nullptr;

}

void FusionNode::run(){
    LOG(INFO) << "fusionNode start";
    ros::Rate rate(10);
    
    while(ros::ok()){
        ros::spinOnce();
        process();
        rate.sleep();
    }
}


void FusionNode::process(){
    // 以lidar跟踪数据初始化融合
    static bool l_isContianerInitialized = false;
    if(!l_isContianerInitialized){
        if(m_pLidarObjects->m_SensorDataDeque.empty()){
            // LOG(INFO) << "lidar tracked objects is empty";
            return;
        }
        else{
            LOG(INFO) << "get lidar tracked objects deque size = " << m_pLidarObjects->m_SensorDataDeque.size();
        }
        
        bool l_isGetLidarObjects = m_pLidarObjects->getCurrentData();
        if(!l_isGetLidarObjects || m_pLidarObjects->m_curSensorData.obs.empty()){
            LOG(INFO) << "get lidar tracked objects failed, l_isGetLidarObjects = " << l_isGetLidarObjects
                << ", lidar tracked objects, size = " << m_pLidarObjects->m_curSensorData.obs.size();
            return;
        }
        else{
            LOG(INFO) << "get lidar tracked objects, size = " << m_pLidarObjects->m_curSensorData.obs.size();
        }
        // 有lidar跟踪数据且跟踪数量不为空，进行数据转换
        common_msgs::sensorobjects l_lidarObjects = m_pLidarObjects->m_curSensorData;
        transLidarObjects2FusionObjects(l_lidarObjects);
        m_currentTimestamp = m_pLidarObjects->m_curSensorData.timestamp;
        m_sensorType = COMMON::SensorType::LIDAR;
        m_fusionState = FusionState::Init;
        l_isContianerInitialized = true;
        return;
    }

    // 1.对lidar跟踪和相机跟踪数据与当前数据进行时间同步，找到与当前时间最近的lidar跟踪和相机跟踪数据
    // 2.判断lidar跟踪和相机跟踪数据的时间是否超过300ms
    // 3.如果超过200ms，则不进行融合，（是否需要删除容器中的数据？）
    // 4.根据时间差按时间先后顺序进行融合
    LOG(INFO) << std::setprecision(12) <<"m_currentTimestamp = " << m_currentTimestamp / 1000.0;
    m_pLidarObjects->timeSynchroBeforeCurrent(m_currentTimestamp, COMMON::SensorType::FUSION);
    m_pCameraObjects->timeSynchroBeforeCurrent(m_currentTimestamp, COMMON::SensorType::FUSION);
    bool l_isGetLidarObjects = m_pLidarObjects->getCurrentData();
    bool l_isGetCameraData = m_pCameraObjects->getCurrentData();
    if(l_isGetLidarObjects){
        LOG(INFO) << std::setprecision(12) <<"lidar timestamp = " << m_pLidarObjects->m_curSensorData.timestamp / 1000.0;
    }
    if(l_isGetCameraData){
        LOG(INFO) << std::setprecision(12) <<"camera timestamp = " << m_pCameraObjects->m_curSensorData.timestamp / 1000.0;
    }

    if(l_isGetLidarObjects && !l_isGetCameraData && abs(m_currentTimestamp - m_pLidarObjects->m_curSensorData.timestamp) < 200){
        // lidar融合
        m_fusionState = FusionState::Lidar;
        LOG(INFO) << "2 only get lidar tracked objects, size = " << m_pLidarObjects->m_curSensorData.obs.size()
            <<", m_currentTimestamp = " << m_currentTimestamp / 1000.0;
        fuseLidarTrackedObjects(m_pLidarObjects->m_curSensorData, COMMON::SensorType::LIDAR);
        m_currentTimestamp = m_pLidarObjects->m_curSensorData.timestamp;
        m_sensorType = COMMON::SensorType::LIDAR;
        
        objectsVisualizePublish();
    }
    else if(l_isGetCameraData && !l_isGetLidarObjects && abs(m_currentTimestamp - m_pCameraObjects->m_curSensorData.timestamp) < 200){
        // camera融合
        m_fusionState = FusionState::Camera;
        LOG(INFO) << "1 only get camera tracked objects, size = " << m_pCameraObjects->m_curSensorData.obs.size()
            <<", m_currentTimestamp = " << m_currentTimestamp / 1000.0;
        m_pCameraObjects->preprocess();
        fuseLidarTrackedObjects(m_pCameraObjects->m_curSensorData, COMMON::SensorType::CAMERA);
        m_currentTimestamp = m_pCameraObjects->m_curSensorData.timestamp;
        m_sensorType = COMMON::SensorType::CAMERA;
        
        objectsVisualizePublish();
    }
    else if(l_isGetLidarObjects && l_isGetCameraData
        && abs(m_currentTimestamp - m_pLidarObjects->m_curSensorData.timestamp) < 200
        && abs(m_currentTimestamp - m_pCameraObjects->m_curSensorData.timestamp) < 200
        ){
        LOG(INFO) << "3 get both lidar and camera tracked objects";
        // 按时间先后融合
        if(m_pLidarObjects->m_curSensorData.timestamp <= m_pLidarObjects->m_curSensorData.timestamp){
            m_fusionState = FusionState::LidarCamera;
            LOG(INFO) << "fuse lidar tracked objects, size = " << m_pLidarObjects->m_curSensorData.obs.size()
                <<", m_currentTimestamp = " << m_currentTimestamp / 1000.0;
            fuseLidarTrackedObjects(m_pLidarObjects->m_curSensorData, COMMON::SensorType::LIDAR);
            LOG(INFO) << "fuse camera tracked objects, size = " << m_pCameraObjects->m_curSensorData.obs.size()
                <<", m_currentTimestamp = " << m_currentTimestamp / 1000.0;
            m_pCameraObjects->preprocess();
            fuseLidarTrackedObjects(m_pCameraObjects->m_curSensorData, COMMON::SensorType::CAMERA);
            m_currentTimestamp = m_pCameraObjects->m_curSensorData.timestamp;
            m_sensorType = COMMON::SensorType::CAMERA;

            objectsVisualizePublish();
        }
        else{
            m_fusionState = FusionState::CameraLidar;
            LOG(INFO) << "fuse camera tracked objects, size = " << m_pCameraObjects->m_curSensorData.obs.size()
                <<", m_currentTimestamp = " << m_currentTimestamp / 1000.0;
            m_pCameraObjects->preprocess();
            fuseLidarTrackedObjects(m_pCameraObjects->m_curSensorData, COMMON::SensorType::CAMERA);
            LOG(INFO) << "fuse lidar tracked objects, size = " << m_pLidarObjects->m_curSensorData.obs.size()
                <<", m_currentTimestamp = " << m_currentTimestamp / 1000.0;
            fuseLidarTrackedObjects(m_pLidarObjects->m_curSensorData, COMMON::SensorType::LIDAR);
            m_currentTimestamp = m_pLidarObjects->m_curSensorData.timestamp;
            m_sensorType = COMMON::SensorType::LIDAR;

            objectsVisualizePublish();
        }
        
    }
    else{
        if(m_pLidarObjects->m_curSensorData.timestamp <= m_pCameraObjects->m_curSensorData.timestamp){
            // m_currentTimestamp += 100;
            m_currentTimestamp = m_pCameraObjects->m_curSensorData.timestamp;
            LOG(WARNING) << "both dont have tracked objects, sync lidar timestamp, currentTimestamp = " << m_currentTimestamp / 1000.0
                <<", l_isGetLidarObjects = " << l_isGetLidarObjects<<", l_isGetCameraData = " << l_isGetCameraData;
            cout<<FRED("lidar deque is empty()");
        }
        else{
            m_currentTimestamp = m_pLidarObjects->m_curSensorData.timestamp;
            LOG(WARNING) << "both dont have tracked objects, sync Camera timestamp, currentTimestamp = " << m_currentTimestamp / 1000.0
                <<", l_isGetLidarObjects = " << l_isGetLidarObjects<<", l_isGetCameraData = " << l_isGetCameraData;
        }
    }

    // 航迹管理：未匹配lidar航迹，未匹配camera航迹，判断航迹是否消失，消失则删除航迹
    // 匹配上的目标，转为融合目标类型，更新融合目标信息，加入容器
    // getMatchedObjects();
    // transType();
    // 未匹配的lidar目标，转为融合目标类型，加入容器
    // 未匹配的camera目标，转为融合目标类型，加入容器

}


void FusionNode::transLidarObjects2FusionObjects(const common_msgs::sensorobjects& objects){
    for(const auto& object : objects.obs){
        boost::shared_ptr<FusionObject> l_pFusionObjects = boost::make_shared<FusionObject>();
        l_pFusionObjects->setFusionObject(object);
        l_pFusionObjects->m_matchedLidarID = object.id;
        l_pFusionObjects->m_lidarObject = object;
        m_vpFusionObjects.emplace_back(l_pFusionObjects);
        LOG(INFO) << "初始化融合id: " << l_pFusionObjects->m_id 
            << ", match lidar id: " << l_pFusionObjects->m_matchedLidarID << ", lidar id: " << object.id;
    }
    m_currentTimestamp = objects.timestamp;
    LOG(INFO) << "初始化 m_vpFusionObjects size =  " << m_vpFusionObjects.size() <<", objects size = " << objects.obs.size();
}


// lidar-fusion匹配（ID-距离）,匹配上，更新位置，未匹配lidar航迹，加入容器
void FusionNode::fuseLidarTrackedObjects(const common_msgs::sensorobjects& lidarObjects, const int& sensorType){
    if(lidarObjects.obs.empty()){
        // LOG(INFO) << "fuseLidarTrackedObjects:lidar tracked objects is empty";
        return;
    }
    
    std::vector<std::pair<boost::shared_ptr<FusionObject>, common_msgs::sensorobject>> m_vMatachedFusionSensorObjects;
    std::vector<boost::shared_ptr<FusionObject>> m_vpUnmatchedFusionObjects;
    std::vector<common_msgs::sensorobject> m_vUnmatchedDetections;
    
    // ID匹配
    IDMatch(lidarObjects, m_vMatachedFusionSensorObjects, m_vpUnmatchedFusionObjects, m_vUnmatchedDetections);
    // 距离匹配
    distanceMatch(lidarObjects, m_vMatachedFusionSensorObjects, m_vpUnmatchedFusionObjects, m_vUnmatchedDetections);

    UpdateAssignedTracks(m_vMatachedFusionSensorObjects, sensorType);

    UpdateUnassignedTracks(m_vpUnmatchedFusionObjects);

    // 未匹配目标创建新融合ID
    CreateNewTracks(m_vUnmatchedDetections, sensorType);

    // 删除未更新的航迹
    RemoveLostTrack();
    LOG(INFO) << "融合size: " << m_vpFusionObjects.size();
}

void FusionNode::IDMatch(const common_msgs::sensorobjects& sensorObjects,
    std::vector<std::pair<boost::shared_ptr<FusionObject>, common_msgs::sensorobject>>& vMatachedFusionSensorObjects,
    std::vector<boost::shared_ptr<FusionObject>>& vpUnmatchedFusionObjects,
    std::vector<common_msgs::sensorobject>& vUnmatchedDetections)
{
    int l_fusionObjectNum = m_vpFusionObjects.size();
    int l_sensorObjectNum = sensorObjects.obs.size();
    LOG(INFO) << "IDMatch: l_fusionObjectNum = " << l_fusionObjectNum << ", sensorObjects size is " << sensorObjects.obs.size();
    
    for (size_t i = 0; i < l_sensorObjectNum; i++){
        common_msgs::sensorobject l_sensorObject = sensorObjects.obs[i];
        auto it = std::find_if(m_vpFusionObjects.begin(), m_vpFusionObjects.end(), [&](boost::shared_ptr<FusionObject>& object){
            // LOG(INFO) << "跟踪id: " << l_sensorObject.id << ", object->m_matchedLidarID : " << object->m_matchedLidarID;
            bool l_isFoundMatchedID = false;
            if(m_sensorType = COMMON::SensorType::LIDAR){
                l_isFoundMatchedID = object->m_matchedLidarID == l_sensorObject.id;
            }
            else if(m_sensorType = COMMON::SensorType::CAMERA){
                l_isFoundMatchedID = object->m_matchedCameraID == l_sensorObject.id;
            }
            return l_isFoundMatchedID;
        });

        if(it != m_vpFusionObjects.end()){
            int fusionObjectIndex = std::distance(m_vpFusionObjects.begin(), it);
            // LOG(INFO) << "IDMatch: m_vpFusionObjects size 111111112222 " << m_vpFusionObjects.size()
            //     << ", fusionObjectIndex = " << fusionObjectIndex;
            boost::shared_ptr<FusionObject> l_pFusionObject = m_vpFusionObjects[fusionObjectIndex];
            // LOG(INFO) << "IDMatch: lidarObjects size aaaa " << sensorObjects.obs.size();
            vMatachedFusionSensorObjects.emplace_back(
                std::pair< boost::shared_ptr<FusionObject>, common_msgs::sensorobject>(l_pFusionObject, l_sensorObject));
            // LOG(INFO) <<"i = " << i << ", fusionObjectIndex = " << fusionObjectIndex << ", 匹配ID " << l_pFusionObject->m_id << " , 跟踪 " << l_sensorObject.id;
        }
        else{
            vUnmatchedDetections.emplace_back(std::move(l_sensorObject));
            LOG(INFO) << "not find match 跟踪id: " << l_sensorObject.id;
        }
    }
    
    
    for (size_t i = 0; i < l_fusionObjectNum; i++){
        boost::shared_ptr<FusionObject> l_pFusionObject = m_vpFusionObjects[i];
        // std::cout<<"fusion id = " << l_pFusionObject->m_id << std::endl;
        auto it = std::find_if(vMatachedFusionSensorObjects.begin(), vMatachedFusionSensorObjects.end(),
            [&]( std::pair<boost::shared_ptr<FusionObject>, common_msgs::sensorobject> matchPair){
                // std::cout<<"match id = " << std::get<0>(matchPair)->m_id << std::endl;
                return std::get<0>(matchPair)->m_id == l_pFusionObject->m_id;
        });

        if(it == vMatachedFusionSensorObjects.end()){
            vpUnmatchedFusionObjects.emplace_back(l_pFusionObject);
        }
    }
    
    LOG(INFO) << "fusion-ID匹配：fusion目标个数="<< l_fusionObjectNum <<", 匹配的目标个数= "<< vMatachedFusionSensorObjects.size()
        <<", 不匹配fusion个数= " << vpUnmatchedFusionObjects.size() <<", sensor目标个数= " << l_sensorObjectNum 
        <<", 不匹配检测个数= " << vUnmatchedDetections.size() <<endl;
}

void FusionNode::distanceMatch(const common_msgs::sensorobjects& sensorObjects,
        std::vector<std::pair<boost::shared_ptr<FusionObject>, common_msgs::sensorobject>>& vMatachedFusionSensorObjects,
        std::vector<boost::shared_ptr<FusionObject>>& vpUnmatchedFusionObjects,
        std::vector<common_msgs::sensorobject>& vUnmatchedDetections)
{
    int l_fusionObjectNum = vpUnmatchedFusionObjects.size();
    int l_sensorObjectNum = vUnmatchedDetections.size();
    LOG(INFO) << "distanceMatch: l_fusionObjectNum = " << l_fusionObjectNum << ", sensorObjects size is " << l_sensorObjectNum;

    if(l_fusionObjectNum == 0 || l_sensorObjectNum == 0){
        return;
    }
    
    m_cComputeAssignmentMatrix.setAssignmentMatrix(l_fusionObjectNum,l_sensorObjectNum);
    bool l_isUseIOUDistance = false;
    for (size_t i = 0; i < l_fusionObjectNum; i++)
    {
        boost::shared_ptr<FusionObject> l_pFusionObject = vpUnmatchedFusionObjects[i];
        std::vector<float> l_fusionObjectBox{l_pFusionObject->m_x, l_pFusionObject->m_y, 1,
        	                                  l_pFusionObject->m_length, l_pFusionObject->m_width, 1,
        	                                  l_pFusionObject->m_yawRad};
        m_cComputeAssignmentMatrix.setPredictedObject(l_fusionObjectBox);

        for(int j = 0; j < l_sensorObjectNum; ++j){
            common_msgs::sensorobject l_sensorObject = vUnmatchedDetections[j];
            std::vector<float> l_sensorObjectBox{l_sensorObject.x, l_sensorObject.y, 1,
                                                l_sensorObject.length, l_sensorObject.width, 1,
                                                l_sensorObject.azimuth};

            m_cComputeAssignmentMatrix.setDetectedObject(l_sensorObjectBox);
            if(l_isUseIOUDistance){
                m_cComputeAssignmentMatrix.m_assignmentMatrix[i][j] = m_cComputeAssignmentMatrix.getBoxIOUDistance();
            }
            else{
                m_cComputeAssignmentMatrix.m_assignmentMatrix[i][j] = m_cComputeAssignmentMatrix.getLocationDistance();
            }
        }
    }
    m_cHungarianAlgorithm.Solve(m_cComputeAssignmentMatrix.m_assignmentMatrix, m_cComputeAssignmentMatrix.m_assignmentPairIndex);
    m_matchedPairs.clear();

    float l_matchThreshold;
    if(l_isUseIOUDistance){
        l_matchThreshold = 0.1;
    }
    else{
        l_matchThreshold = 6;
    }

    std::vector<boost::shared_ptr<FusionObject>> l_vpUnmatchedFusionObjects;
    std::vector<common_msgs::sensorobject> l_vUnmatchedDetections;
    std::set<int> l_matchedFusionIDS;
	for (unsigned int i = 0; i < l_fusionObjectNum; ++i)
	{
		if (m_cComputeAssignmentMatrix.m_assignmentPairIndex[i] == -1){
            l_vpUnmatchedFusionObjects.emplace_back(vpUnmatchedFusionObjects[i]);
			continue;
		}
		if (1 - m_cComputeAssignmentMatrix.m_assignmentMatrix[i][m_cComputeAssignmentMatrix.m_assignmentPairIndex[i]] < l_matchThreshold)
		{
            l_vpUnmatchedFusionObjects.emplace_back(vpUnmatchedFusionObjects[i]);
            l_vUnmatchedDetections.emplace_back(vUnmatchedDetections[m_cComputeAssignmentMatrix.m_assignmentPairIndex[i]]);
		}
		else {
            boost::shared_ptr<FusionObject> l_pFusionObject = m_vpFusionObjects[i];
            l_matchedFusionIDS.insert(l_pFusionObject->m_id);
            vMatachedFusionSensorObjects.emplace_back(
                std::pair<boost::shared_ptr<FusionObject>, common_msgs::sensorobject>(l_pFusionObject, vUnmatchedDetections[m_cComputeAssignmentMatrix.m_assignmentPairIndex[i]]));
            m_matchedPairs.push_back(std::vector<int>{i, m_cComputeAssignmentMatrix.m_assignmentPairIndex[i]});
		}
	}

    // 在所有的融合目标中，找出不在融合目标及未匹配目标中的目标
    for (size_t i = 0; i < l_sensorObjectNum; i++)
    {
        common_msgs::sensorobject l_sensorObject = vUnmatchedDetections[i];
        auto it = std::find_if(vMatachedFusionSensorObjects.begin(), vMatachedFusionSensorObjects.end(),
            [&](const std::pair<boost::shared_ptr<FusionObject>, common_msgs::sensorobject>& matchPair){
                // std::cout<<"match id = " << std::get<0>(matchPair)->m_id << std::endl;
                return std::get<1>(matchPair).id == l_sensorObject.id;
        });

        if(it == vMatachedFusionSensorObjects.end()){
            auto it2 = std::find_if(l_vUnmatchedDetections.begin(), l_vUnmatchedDetections.end(),
                [&](const common_msgs::sensorobject& object){
                    // std::cout<<"match id = " << std::get<0>(matchPair)->m_id << std::endl;
                    return object.id == l_sensorObject.id;
            });
            if(it2 == l_vUnmatchedDetections.end()){
                l_vUnmatchedDetections.emplace_back(l_sensorObject);
            }
        }
    }
    
    {
        // debug
        cout << "distance matched ID: \n\t";
        for(auto it = l_matchedFusionIDS.begin(); it!=l_matchedFusionIDS.end(); it++){
            cout << *it <<", " ;
        }
        cout << endl;
    }
    // 从未匹配的容器中找到匹配对，存入匹配容器
    vpUnmatchedFusionObjects = l_vpUnmatchedFusionObjects;
    vUnmatchedDetections = l_vUnmatchedDetections;
    LOG(INFO) << "fusion距离匹配：fusion目标个数="<< l_fusionObjectNum <<", 匹配的目标个数= "<< m_matchedPairs.size()
        <<", 不匹配fusion个数= " << vpUnmatchedFusionObjects.size() <<", sensor目标个数= " << l_sensorObjectNum 
        <<", 不匹配检测个数= " << vUnmatchedDetections.size() <<endl;

    assert( l_fusionObjectNum == m_matchedPairs.size() + vpUnmatchedFusionObjects.size());
    assert( l_sensorObjectNum == m_matchedPairs.size() + vUnmatchedDetections.size());

	
}

void FusionNode::UpdateAssignedTracks(std::vector<std::pair<boost::shared_ptr<FusionObject>, common_msgs::sensorobject>> vMatachedFusionSensorObjects,
                                        const int& sensorType)
{
    // ID匹配的目标ID不动，distanceMatch的目标维持ID不变，信息(位置、长宽高、航向角)更新
    for(auto& pMatchPairObject: vMatachedFusionSensorObjects){
        std::get<0>(pMatchPairObject)->updateAssignedObjects(std::get<1>(pMatchPairObject));
        std::get<0>(pMatchPairObject)->m_sensorType = sensorType;
        
        if(sensorType == COMMON::SensorType::LIDAR){
            std::get<0>(pMatchPairObject)->m_matchedLidarID = std::get<1>(pMatchPairObject).id;
            std::get<0>(pMatchPairObject)->m_lidarObject = std::get<1>(pMatchPairObject);
        }
        else if(sensorType == COMMON::SensorType::CAMERA){
            std::get<0>(pMatchPairObject)->m_matchedCameraID = std::get<1>(pMatchPairObject).id;
            std::get<0>(pMatchPairObject)->m_cameraObject = std::get<1>(pMatchPairObject);
        }
        
    }
    
}


void FusionNode::UpdateUnassignedTracks(std::vector<boost::shared_ptr<FusionObject>>& vpUnmatchedFusionObjects){
    // 未更新的融合目标，更新未更新次数
    for(auto& pFusionObject: vpUnmatchedFusionObjects){
        pFusionObject->updateUnassignedObjects();
    }
    
}

void FusionNode::CreateNewTracks(const std::vector<common_msgs::sensorobject>& vUnmatchedDetections, const int& sensorType){
    for(const auto& object : vUnmatchedDetections){
        boost::shared_ptr<FusionObject> l_pFusionObjects = boost::make_shared<FusionObject>();
        l_pFusionObjects->setFusionObject(object);
        if(sensorType == COMMON::SensorType::LIDAR){
            l_pFusionObjects->m_matchedLidarID = object.id;
            l_pFusionObjects->m_lidarObject = object;
            LOG(INFO) << "初始化融合id: " << l_pFusionObjects->m_id 
                << ", match lidar id: " << l_pFusionObjects->m_matchedLidarID << ", lidar id: " << object.id;
        }
        else if(sensorType == COMMON::SensorType::CAMERA){
            l_pFusionObjects->m_matchedCameraID = object.id;
            l_pFusionObjects->m_cameraObject = object;
            LOG(INFO) << "初始化融合id: " << l_pFusionObjects->m_id 
                << ", match camera id: " << l_pFusionObjects->m_matchedLidarID << ", lidar id: " << object.id;
        }
        
        m_vpFusionObjects.emplace_back(l_pFusionObjects);
        
    }
}

void FusionNode::RemoveLostTrack(){
    // 未更新的融合目标，未更新次数超过阈值，删除
    // 使用范围基础的for循环遍历，避免在循环体内调用成员函数
    std::vector<boost::shared_ptr<FusionObject>> l_vpFusionObjectsToRemove;
    int l_unUpdateThreshold = 2;
    switch (m_fusionState){
        case FusionState::Lidar:
        case FusionState::Camera:{
            l_unUpdateThreshold = 1;
            break;
        }
        case FusionState::LidarCamera:
        case FusionState::CameraLidar:{
            l_unUpdateThreshold = 2;
            break;
        }
        default:
            break;
    }
    for (const auto& obj : m_vpFusionObjects) {
        if (obj->m_unUpdateCount > l_unUpdateThreshold) {
            std::cout << "Removed id: " << obj->m_id << ", threshold: " << l_unUpdateThreshold << std::endl;
            l_vpFusionObjectsToRemove.push_back(obj);
        }
    }

    // 一次性删除所有需要移除的元素
    for (const auto& obj : l_vpFusionObjectsToRemove) {
        m_vpFusionObjects.erase(std::find(m_vpFusionObjects.begin(), m_vpFusionObjects.end(), obj));
    }
        

    // check
    std::cout <<"reserved id: \n\t";
    for(auto& l_pSingleFusionObject: m_vpFusionObjects){
        std::cout << l_pSingleFusionObject->m_id <<", ";
    }
    std::cout << std::endl;
}

void FusionNode::transFusionObjects2LidarObjects(common_msgs::sensorobjects& objects, const int& sensorType){
    objects.isvalid = 1;
    objects.timestamp = m_currentTimestamp;
    objects.gpstime = m_currentTimestamp;
    for (const auto& obj : m_vpFusionObjects) {
        common_msgs::sensorobject l_sensorobject;
        if(sensorType == COMMON::SensorType::LIDAR) {
            l_sensorobject = obj->m_lidarObject;
        }
        else if(sensorType == COMMON::SensorType::CAMERA) {
            l_sensorobject = obj->m_cameraObject;
        }
        l_sensorobject.id = obj->m_id;
        objects.obs.emplace_back(std::move(l_sensorobject));
    }
    LOG(INFO) << "转换类别size: " << objects.obs.size();
    LOG(INFO) << "............................................";
}


void FusionNode::objectsVisualizePublish(){
     // 融合目标转成公共类型
    common_msgs::sensorobjects l_fusionObjects;
    transFusionObjects2LidarObjects(l_fusionObjects, m_sensorType);
    // 显示、发布
    m_pcSenObjectsViewer->visualize(l_fusionObjects);
    m_pub_fusionTrackedObjects.publish(l_fusionObjects);
}

// TODO camera-fusion匹配,匹配上，更新类别，未匹配lidar航迹，加入容器
void FusionNode::fuseCameraTrackedObjects(const common_msgs::sensorobjects& cameraObjects){
   

}



