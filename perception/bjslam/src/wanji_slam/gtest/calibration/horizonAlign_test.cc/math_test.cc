#include "../../../include/algorithm/calibration/horizonAlign/impl/continueAlign.hpp"
#include "../../../include/algorithm/calibration/horizonAlign/impl/horizontalAlign.hpp"
#include "../../type_gtest.h"
#include <random>

class HORIZON : public testing::Test {
  protected:
    boost::shared_ptr<wj_hA::GroundPlaneExtract> g_pGroudPlaneExtract;
    virtual void SetUp()
    {
        g_pGroudPlaneExtract.reset(new wj_hA::GroundPlaneExtract());
        // 在每个TestCase执行之前会执行
    }
    virtual void TearDown()
    {
        g_pGroudPlaneExtract = nullptr;
    }




    float getRandomData(const float p_fMin, const float p_fMax)
    {
        std::random_device rd;
        // 用 random_device产生一个真随机数，用作“伪随机数发生器”的种子
        std::default_random_engine gen(rd());
        // 一个正态“分布器”，高斯分布器是 std::normal_distribution
        std::uniform_real_distribution<float> dis(p_fMin, p_fMax);
        return dis(gen);
    }

    
};

// TEST_F(HORIZON, creatcloud)
// {
//     Eigen::Quaterniond l_qHorizon;

//     l_qHorizon.w() =0.998789;
//     l_qHorizon.x() =  0.0392541;
//     l_qHorizon.y() = -0.0296586;
//     l_qHorizon.z() = 0;

//     pcl::PointCloud<pcl::PointXYZ>::Ptr l_pPC_withThreePoints(new pcl::PointCloud<pcl::PointXYZ>());
//     l_pPC_withThreePoints->resize(200000);
//     for (auto& p : l_pPC_withThreePoints->points)
//     {
//         p.x = getRandomData(-25, 25);
//         p.y = getRandomData(-25, 25);
//         if (p.x > 20 && p.x < 25 && abs(p.y - 10) < 5)
//         {
//             p.z = getRandomData(0, 20);
//         }
//         else if (p.x > -10 && p.x < -5 && abs(p.y + 10) < 5)
//         {
//             p.z = getRandomData(0, 10);
//         }
//         else if (abs(p.x - 25) < 0.05 || abs(p.y - 25) < 0.05 ||abs(p.x + 25) < 0.05 ||abs(p.y + 25) < 0.05)
//         {
//             p.z = getRandomData(0, 5);
//         }
//         else
//             p.z = getRandomData(-0.01, 0.01);
//     }
   
//     Eigen::Vector3d l_po, l_point;
//     for (int i = 0; i < l_pPC_withThreePoints->size(); i++)
//     {
//         l_po[0] = l_pPC_withThreePoints->points[i].x;
//         l_po[1] = l_pPC_withThreePoints->points[i].y;
//         l_po[2] = l_pPC_withThreePoints->points[i].z;
//         // std::cout << "执行前" << l_po[0] << " " << l_po[1] << " " << l_po[2] << std::endl;
//         l_point = l_qHorizon * l_po;
//         // std::cout << "执行后" << l_point[0] << " " << l_point[1] << " " << l_point[2] <<
//         // std::endl;
//         l_pPC_withThreePoints->points[i].x = l_point[0];
//         l_pPC_withThreePoints->points[i].y = l_point[1];
//         l_pPC_withThreePoints->points[i].z = l_point[2];
//     }
//     l_pPC_withThreePoints->height = 1;
//     l_pPC_withThreePoints->width = l_pPC_withThreePoints->size();
//     pcl::io::savePCDFile<pcl::PointXYZ>("/home/<USER>/data/lrz_ws/水平校准测试/水平校准点云/X4.5Y3.4.pcd",
//                                         *l_pPC_withThreePoints);
// }

// caclPlaneFunctionRANSAC_逻辑测试:
/**
 * 1.成功时输出正确的平面方程
 * 2.失败时输出0,0,1
 */

// filter逻辑测试:
/**
 * 1.平面拟合成功时,输出四元数是否正确
 * 2.平面拟合失败时,输出的四元数是否为初始值
 * 3.若平面方程值未初始化,filter流程中的calcHorizontalQuat_会计算出什么结果
 */

//测试1:平面拟合成功时,输出四元数是否正确
// TEST_F(HORIZON, filter_TEST1)
// {
//     //准备一个绕y轴旋转 -30度的平面
//     pcl::PointCloud<pcl::PointXYZ>::Ptr l_pPC_withThreePoints(new
//     pcl::PointCloud<pcl::PointXYZ>()); pcl::io::loadPCDFile<pcl::PointXYZ>(
//         "/home/<USER>/data/lrz_ws/水平校准测试/水平校准点云/原图绕y旋转-30度后.pcd",
//         *l_pPC_withThreePoints);
//     g_pGroudPlaneExtract->setInputCloud(l_pPC_withThreePoints);

//     Eigen::VectorXf l_v4PlaneModel = Eigen::VectorXf::Zero(4);  //平面方程 - 未作初始化
//     // 理论上返回true
//     ASSERT_TRUE(g_pGroudPlaneExtract->calGroPara_(l_v4PlaneModel));

//     //打印l_v4PlaneModel 平面方程参数

//     for (int i = 0; i < l_v4PlaneModel.size(); ++i)
//     {
//         std::cout << l_v4PlaneModel[i];
//         if (i < l_v4PlaneModel.size() - 1)
//         {
//             std::cout << ", ";
//         }
//     }
//     std::cout << "]" << std::endl;
//     Eigen::Quaterniond l_qHorizon(1, 0, 0, 0);
//     g_pGroudPlaneExtract->calcHorizontalQuat_(l_v4PlaneModel, l_qHorizon);
//     //应该为0,0.258819,0,0.9659258
//     double tolerance = 1e-6;
//     ASSERT_NEAR(l_qHorizon.x(), 0.0, tolerance);
//     ASSERT_NEAR(l_qHorizon.y(), 0.258819, tolerance);
//     ASSERT_NEAR(l_qHorizon.z(), 0.0, tolerance);
//     ASSERT_NEAR(l_qHorizon.w(), 0.9659258, tolerance);
// }

// //测试2 平面拟合失败时,输出的四元数是否为初始值
// //结论 是的
// TEST_F(HORIZON, filter_TEST2)
// {
//     Eigen::VectorXf l_v4PlaneModel = Eigen::VectorXf::Zero(4);

//     l_v4PlaneModel = Eigen::Vector3f(0, 0, 1);
//     Eigen::Quaterniond l_qHorizon(1, 0, 0, 0);
//     g_pGroudPlaneExtract->calcHorizontalQuat_(l_v4PlaneModel, l_qHorizon);

//     //测试单元
//     double tolerance = 1e-6;
//     ASSERT_NEAR(l_qHorizon.x(), 0.0, tolerance);
//     ASSERT_NEAR(l_qHorizon.y(), 0.0, tolerance);
//     ASSERT_NEAR(l_qHorizon.z(), 0.0, tolerance);
//     ASSERT_NEAR(l_qHorizon.w(), 1, tolerance);
// }
// //测试3: 若平面方程值未初始化,filter流程中的calcHorizontalQuat_会计算出什么结果
// //不初始化会崩溃
// //初始化后为 0,0,0,0,计算结果四元数为初值
// TEST_F(HORIZON, filter_TEST3)
// {
//     Eigen::VectorXf l_v4PlaneModel = Eigen::VectorXf::Zero(4);  //[0,0,0,0]
//     // Eigen::VectorXf l_v4PlaneModel;//这里只声明,在进行下一步计算时崩溃

//     Eigen::Quaterniond l_qHorizon(1, 0, 0, 0);
//     g_pGroudPlaneExtract->calcHorizontalQuat_(l_v4PlaneModel, l_qHorizon);

//     // l_qHorizon = 0 0 0 0.707107  与0 0 0 1 一样
//     double tolerance = 1e-6;
//     ASSERT_NEAR(l_qHorizon.x(), 0.0, tolerance);
//     ASSERT_NEAR(l_qHorizon.y(), 0.0, tolerance);
//     ASSERT_NEAR(l_qHorizon.z(), 0.0, tolerance);
//     // w不一定为 1
// }

// TEST_F(HORIZON, PLANFUNCTEST)
// {
//     pcl::PointCloud<pcl::PointXYZ>::Ptr l_pPC_withThreePoints(new
//     pcl::PointCloud<pcl::PointXYZ>()); pcl::io::loadPCDFile<pcl::PointXYZ>(
//         "/home/<USER>/data/lrz_ws/水平校准测试/水平校准点云/原图绕y旋转-30度后.pcd",
//         *l_pPC_withThreePoints);
//     g_pGroudPlaneExtract->setInputCloud(l_pPC_withThreePoints);
//     Eigen::VectorXf l_v4PlaneModel = Eigen::Vector4f::Zero();  //平面方程
//     g_pGroudPlaneExtract->caclPlaneFunctionRANSAC_(l_pPC_withThreePoints, l_v4PlaneModel);

//     // std::cout << "平面方程为:"
//     //           << "(" << l_v4PlaneModel[0] << ")x +(" << l_v4PlaneModel[1] << ")y+("
//     //           << l_v4PlaneModel[2] << ")z+(" << l_v4PlaneModel[3] << ") = 0" << std::endl;
//     Eigen::Quaterniond l_qHorizon(1.0, 0.0, 0.0, 0.0);
//     g_pGroudPlaneExtract->calcHorizontalQuat_(l_v4PlaneModel, l_qHorizon);
//     //输出 x y z w
//     // 0, 0.258819, 0, 0.9659258

//     float l_fAngle = 0.0;
//     float l_fHeight = 0.0;

//     g_pGroudPlaneExtract->calcPlanAngle_(l_v4PlaneModel, l_fAngle);
//     std::cout << "角度:" << l_fAngle << std::endl;
//     // Eigen::VectorXf l_v4PlaneModel_null_test;
//     // // std::cout<<"函数内不初始化值:" << l_v4PlaneModel_null_test[0] <<"
//     // // "<<l_v4PlaneModel_null_test[1]<<" "<< l_v4PlaneModel_null_test[2]<<" "<<
//     // // l_v4PlaneModel_null_test[3] <<std::endl;
//     // g_pGroudPlaneExtract->calcPlanAngle_(l_v4PlaneModel_null_test, l_fAngle);
//     // std::cout << "不初始化输出的角度:" << l_fAngle << std::endl;

//     g_pGroudPlaneExtract->calcGroundHeight_(l_v4PlaneModel, l_fHeight);
//     std::cout << "高度:" << l_fHeight << std::endl;
//     Eigen::Vector3d l_po, l_point;
//     for (int i = 0; i < l_pPC_withThreePoints->size(); i++)
//     {
//         l_po[0] = l_pPC_withThreePoints->points[i].x;
//         l_po[1] = l_pPC_withThreePoints->points[i].y;
//         l_po[2] = l_pPC_withThreePoints->points[i].z;
//         l_point = l_qHorizon * l_po;
//         l_pPC_withThreePoints->points[i].x = l_point[0];
//         l_pPC_withThreePoints->points[i].y = l_point[1];
//         l_pPC_withThreePoints->points[i].z = l_point[2];
//     }
//     l_pPC_withThreePoints->height = 1;
//     l_pPC_withThreePoints->width = l_pPC_withThreePoints->size();
//     // pcl::io::savePCDFile<pcl::PointXYZ>(
//     // "/home/<USER>/data/lrz_ws/水平校准测试/水平校准点云/校准.pcd", *l_pPC_withThreePoints);
// }

// TEST_F(HORIZON, curvature_filter)
// {
//     pcl::PointCloud<pcl::PointXYZ>::Ptr l_pPC_withThreePoints(new pcl::PointCloud<pcl::PointXYZ>());
//     pcl::io::loadPCDFile<pcl::PointXYZ>("/home/<USER>/data/lrz_ws/水平校准测试/水平校准点云/X5Y5.pcd",
//                                         *l_pPC_withThreePoints);

//     g_pGroudPlaneExtract->setInputCloud(l_pPC_withThreePoints);
//     Eigen::VectorXf l_v4PlaneModel = Eigen::Vector4f::Zero();
//     g_pGroudPlaneExtract->calGroPara_(l_v4PlaneModel);
// }
// //原代码没错,学习了 unique用法
// TEST_F(HORIZON, SORT_AND_uniqueTest)
// {
//     std::vector<int> l_vTestvector;  //{-1,2,-1,4,-1,3,-1,1};
//     l_vTestvector.push_back(-1);
//     l_vTestvector.push_back(2);
//     l_vTestvector.push_back(-1);
//     l_vTestvector.push_back(4);
//     l_vTestvector.push_back(-1);
//     l_vTestvector.push_back(3);
//     l_vTestvector.push_back(-1);
//     l_vTestvector.push_back(1);

//     std::sort(l_vTestvector.begin(), l_vTestvector.end());
//     for (int i : l_vTestvector)
//     {
//         std::cout << i << " ";
//     }
//     std::cout << "size:"<< l_vTestvector.size() << std::endl;
//     std::cout << std::endl;
//     // std::unique(l_vTestvector.begin(), l_vTestvector.end());
//     // std::cout << "unique后:" << std::endl;
//     // for (int i : l_vTestvector)
//     // {
//     //     std::cout << i << " ";
//     // }
//     // std::cout << "size:"<< l_vTestvector.size() << std::endl;
//     // std::cout << std::endl;

//     l_vTestvector.erase(std::unique(l_vTestvector.begin(), l_vTestvector.end()),
//                         l_vTestvector.end());
//     std::cout << "erase后:" << std::endl;
//     for (int i : l_vTestvector)
//     {
//         std::cout << i << " ";
//     }
//     std::cout << "size:"<< l_vTestvector.size() << std::endl;
//     std::cout << std::endl;
// }
// TEST_F(HORIZON, queue_poptest)
// {
//     // queue 随意pop 和取front() 测试
//     //测试结果 :不会崩,但是队列空了以后还取元素,取的随机值
//     std::queue<int> l_qTestqueue;
//     l_qTestqueue.push(1);
//     l_qTestqueue.push(2);
//     std::cout << "此时队列应有2个元素-实际:" << l_qTestqueue.size();

//     int i = l_qTestqueue.front();

//     std::cout << "获取的元素" << i << std::endl;
//     l_qTestqueue.pop();
//     std::cout << "此时队列应有1个元素-实际:" << l_qTestqueue.size() << std::endl;
//     i = l_qTestqueue.front();

//     std::cout << "获取的元素" << i << std::endl;
//     l_qTestqueue.pop();
//     std::cout << "此时队列应有0个元素-实际:" << l_qTestqueue.size() << std::endl;

//     //此时再次pop,会崩吗?

//     l_qTestqueue.pop();
//     std::cout << " pop()不会崩" << std::endl;
//     std::cout << "此时队列应有-1个元素-实际:" << l_qTestqueue.size() << std::endl;
//     i = l_qTestqueue.front();
//     //此时在取头元素会崩吗
//     std::cout << " front()不会崩" << std::endl;
//     std::cout << "获取的元素" << i << std::endl;
//     i = l_qTestqueue.front();
//     std::cout << "获取的元素" << i << std::endl;

//     l_qTestqueue.pop();
//     i = l_qTestqueue.front();
//     std::cout << "获取的元素" << i << std::endl;

//     l_qTestqueue.pop();
//     i = l_qTestqueue.front();
//     std::cout << "获取的元素" << i << std::endl;

//     l_qTestqueue.pop();
//     i = l_qTestqueue.front();
//     std::cout << "获取的元素" << i << std::endl;

//     l_qTestqueue.pop();
//     i = l_qTestqueue.front();
//     std::cout << "获取的元素" << i << std::endl;

//     l_qTestqueue.pop();
//     i = l_qTestqueue.front();
//     std::cout << "获取的元素" << i << std::endl;

//     l_qTestqueue.pop();
//     i = l_qTestqueue.front();
//     std::cout << "获取的元素" << i << std::endl;

//     l_qTestqueue.pop();
//     //

//     // float 0/0 崩溃吗?
//     float a = 0;
//     float b = 0;
//     std::cout << a / b << std::endl;
//     float dev = a / b;
//     if (dev > 10)
//     {
//         std::cout << "没崩,大于10" << std::endl;
//     }
//     else
//     {
//         std::cout << "没崩,小于10" << std::endl;
//     }
// }


TEST_F(HORIZON, PLANFUNCTEST)
{
    pcl::PointCloud<pcl::PointXYZ>::Ptr l_pPC_withThreePoints(new
    pcl::PointCloud<pcl::PointXYZ>()); pcl::io::loadPCDFile<pcl::PointXYZ>(
        "/home/<USER>/data/lrz_ws/水平校准测试/水平校准点云/X5Y5/X5Y5.pcd",
        *l_pPC_withThreePoints);
    g_pGroudPlaneExtract->setInputCloud(l_pPC_withThreePoints);
    Eigen::VectorXf l_v4PlaneModel = Eigen::Vector4f::Zero();  //平面方程
    g_pGroudPlaneExtract->calGroPara_(l_v4PlaneModel);
    Eigen::Quaterniond l_qHorizon(1.0, 0.0, 0.0, 0.0);
    g_pGroudPlaneExtract->calcHorizontalQuat_(l_v4PlaneModel, l_qHorizon);
    float l_fAngle = 0.0;
    float l_fHeight = 0.0;
    g_pGroudPlaneExtract->calcPlanAngle_(l_v4PlaneModel, l_fAngle);
    std::cout << "角度:" << l_fAngle << std::endl;
    g_pGroudPlaneExtract->calcGroundHeight_(l_v4PlaneModel, l_fHeight);
    std::cout << "高度:" << l_fHeight << std::endl;



for (int i = 0; i < l_pPC_withThreePoints->size(); i++)
{
    // 将点云进行旋转
    Eigen::Vector3d l_point(l_pPC_withThreePoints->points[i].x, l_pPC_withThreePoints->points[i].y, l_pPC_withThreePoints->points[i].z);
    l_point = l_qHorizon * l_point;
    l_pPC_withThreePoints->points[i].x = static_cast<float>(l_point[0]);
    l_pPC_withThreePoints->points[i].y = static_cast<float>(l_point[1]);
    l_pPC_withThreePoints->points[i].z = static_cast<float>(l_point[2]);
}








    // pcl::io::savePCDFile<pcl::PointXYZ>(
    // "/home/<USER>/data/lrz_ws/水平校准测试/水平校准点云/校准.pcd", *l_pPC_withThreePoints);
    g_pGroudPlaneExtract->setInputCloud(l_pPC_withThreePoints);
    l_v4PlaneModel = Eigen::Vector4f::Zero();  //平面方程
    g_pGroudPlaneExtract->caclPlaneFunctionRANSAC_(l_pPC_withThreePoints, l_v4PlaneModel);
    g_pGroudPlaneExtract->calcPlanAngle_(l_v4PlaneModel, l_fAngle);
    std::cout << " 应该为0 -角度:" << l_fAngle << std::endl;
   

}