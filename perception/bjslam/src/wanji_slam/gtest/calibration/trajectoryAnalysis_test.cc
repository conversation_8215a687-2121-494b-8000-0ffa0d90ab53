/*
 * @Description:
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2022-04-12 16:08:22
 * @LastEditors: <PERSON><PERSON> Chen
 * @LastEditTime: 2022-04-29 15:21:01
 */
#include "algorithm/calibration/mLidarCalib/trajectoryAnalysis.h"
#include <gtest/gtest.h>
#include <random>

/*
 * StaticAnalysis 测试目标1: 测试静态分析能力 更新-计算的三种一般状态：
            1. 输入X小波动数据-->静止时间增长
            2. 输入X大波动数据（模拟位姿晃动)-->静止时间不增长-->静止时间清零
            3. 输入X小波动数据-->静止时间重新增长
 * StaticAnalysis 测试目标2: 测试静态分析能力 跳跃状态：
            1. 输入X小波动数据(位置A附近)
            2. 输入X小波动数据(位置B附近)(模拟设置位姿)-->静止时间不增长-->静止时间清零
            3. 输入X小波动数据(位置B附近)-->静止时间重新增长
 */
namespace wj_slam {
/**
 * @description: 要使用测试fixture，请从testing:: test派生一个类
 * @note: 每个TEST_F使用同一实例的副本，任何改变都不传递到其他测试用例
 */
class StaticAnalysisTest : public testing::Test {
    // 让成员受保护，因此它们可以从子类访问
  protected:
    /**
     * @description: SetUp()将在每个TEST_F测试运行前自动调用。
     * @note: 如果需要初始化变量，重载该函数。
     */
    void SetUp() override
    {
        // 使用系统默认参数初始
        c_pSa_.reset(new StaticAnalysis(
            m_iMaxTimeReInit, m_fInitVeloTHR, m_fInitStdDevTHR[0], m_fInitStdDevTHR[1]));
    }
    /**
     * @description: TearDown()将在每个TEST_F测试运行后自动调用。
     * @note: 如果需要清理工作，重载该函数。
     */
    void TearDown() override
    {
        c_pSa_ = nullptr;
    }
    void setNDPose(const double p_fMean, const double p_fSigma, const int p_iTime)
    {
        s_PoseWithTwist l_lastPose = test_random_;
        // 生成随机数
        std::random_device rd;
        // 用 random_device产生一个真随机数，用作“伪随机数发生器”的种子
        std::mt19937 gen(rd());
        // 一个正态“分布器”，高斯分布器是 std::normal_distribution
        std::normal_distribution<double> dis(p_fMean, p_fSigma);
        test_random_ = s_PoseWithTwist();
        double l_fDist = dis(gen);
        test_random_.m_Pose.m_trans << l_fDist, l_fDist, l_fDist;
        test_random_.m_iTimetamp = p_iTime;
        test_random_.m_Twist = l_lastPose.m_Pose.inverse() * test_random_.m_Pose;
        test_random_.m_Twist /= (test_random_.m_iTimetamp - l_lastPose.m_iTimetamp) / SCAN_TIME_MS;
    }
    // 模拟值变化的随机数值
    s_PoseWithTwist test_random_;
    // 模拟参数=系统默认参数
    // static s_MultiLidarCalibConfig c_param;
    int m_iMaxTimeReInit = 1000;
    float m_fInitVeloTHR = 0.05;
    float m_fInitStdDevTHR[2] = {0.05, 0.5};
    StaticAnalysis::Ptr c_pSa_;
};
// s_MultiLidarCalibConfig c_param = s_MultiLidarCalibConfig();

TEST_F(StaticAnalysisTest, normalStateFlow)
{
    // 起始时刻0,方便计算
    int l_iTimes = 0;
    int l_iStaticTime = 0;
    // case 0: 更新小范围波动的帧X次
    for (; l_iTimes < 15 * SCAN_TIME_MS; l_iTimes += SCAN_TIME_MS)
    {
        setNDPose(1.0, 0.03, l_iTimes);
        c_pSa_->renewPose(test_random_);
        l_iStaticTime = c_pSa_->hasStaticTime();
        // 每次静止时间：稳定增长
        EXPECT_EQ(l_iStaticTime, l_iTimes);
    }
    s_PoseWithTwist l_result;
    // 获取结果： 成功
    EXPECT_TRUE(c_pSa_->getLatestStaticMeanPose(l_result));
    // 结果范围： 设定均值附近
    EXPECT_NEAR(l_result.m_Pose.x(), 1.0, 0.03);
    // case 1: 更新大范围波动数据X次
    // 开始静止时间戳
    int l_iUnStabeStart = l_iTimes;
    // 应该刷新静止时长的时间戳
    int l_iClearTime = l_iUnStabeStart + m_iMaxTimeReInit;
    // 当前累计静止时间
    int l_iStaticLast = l_iStaticTime;
    for (; l_iTimes < 30 * SCAN_TIME_MS; l_iTimes += SCAN_TIME_MS)
    {
        setNDPose(1.0, 0.5, l_iTimes);
        c_pSa_->renewPose(test_random_);
        l_iStaticTime = c_pSa_->hasStaticTime();
        // 当累计不超过限制时,静止时间不变化
        if (l_iTimes <= l_iClearTime)
            EXPECT_EQ(l_iStaticTime, l_iStaticLast);
        // 当累计抖动超过限制时静止时间应为0
        else
            EXPECT_EQ(l_iStaticTime, 0);
    }
    // 当前累计静止时间
    l_iStaticLast = l_iStaticTime;
    // case 2:输入X小波动数据-->静止时间重新增长
    for (; l_iTimes < 45 * SCAN_TIME_MS; l_iTimes += SCAN_TIME_MS)
    {
        setNDPose(0.9, 0.03, l_iTimes);
        c_pSa_->renewPose(test_random_);
        l_iStaticTime = c_pSa_->hasStaticTime();
        // 首次,静止时间不变化
        if (l_iTimes == 30 * SCAN_TIME_MS)
            EXPECT_EQ(l_iStaticTime, l_iStaticLast);
        // 每次静止时间：稳定增长
        else
            EXPECT_GT(l_iStaticTime, l_iStaticLast);
        // 上次
        l_iStaticLast = l_iStaticTime;
    }
}

TEST_F(StaticAnalysisTest, jumpStateFlow)
{
    // 起始时刻0,方便计算
    int l_iTimes = 0;
    int l_iStaticTime = 0;
    // case 0: 更新A位置小范围波动的帧X次
    for (; l_iTimes < 15 * SCAN_TIME_MS; l_iTimes += SCAN_TIME_MS)
    {
        setNDPose(1.0, 0.03, l_iTimes);
        c_pSa_->renewPose(test_random_);
        l_iStaticTime = c_pSa_->hasStaticTime();
        // 每次静止时间：稳定增长
        EXPECT_EQ(l_iStaticTime, l_iTimes);
    }
    s_PoseWithTwist l_result;
    // 获取结果： 成功
    EXPECT_TRUE(c_pSa_->getLatestStaticMeanPose(l_result));
    // 结果范围： 设定均值附近
    EXPECT_NEAR(l_result.m_Pose.x(), 1.0, 0.03);
    // case 1: 输入B位置X小波动数据-->静止时间不变化
    // case 2:输入B位置X小波动数据-->静止时间重新增长
    // 开始静止时间戳
    int l_iUnStabeStart = l_iTimes;
    // 应该刷新静止时长的时间戳
    int l_iClearTime = l_iUnStabeStart + m_iMaxTimeReInit;
    // 当前累计静止时间
    int l_iStaticLast = l_iStaticTime;
    for (; l_iTimes < l_iClearTime + 15 * SCAN_TIME_MS; l_iTimes += SCAN_TIME_MS)
    {
        setNDPose(0.0, 0.03, l_iTimes);
        c_pSa_->renewPose(test_random_);
        l_iStaticTime = c_pSa_->hasStaticTime();
        // 当累计不超过限制时,静止时间不变化
        if (l_iTimes <= l_iClearTime)
            EXPECT_EQ(l_iStaticTime, l_iStaticLast);
        // 当累计抖动超过限制时静止时间应为0
        else
        {
            EXPECT_GT(l_iStaticTime, l_iStaticLast);
            l_iStaticLast = l_iStaticTime;
        }
    }
}

}  // namespace wj_slam