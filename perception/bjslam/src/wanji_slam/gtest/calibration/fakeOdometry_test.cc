/*
 * @Description:
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2022-04-12 16:08:12
 * @LastEditors: <PERSON><PERSON> Chen
 * @LastEditTime: 2022-04-29 13:52:36
 */
/*
 * 测试目标1:
        测试位姿流 更新-计算的三种状态：
            1. 输入前一帧位姿更新->新特征帧计算
            2. 缺失前一帧位姿更新->新特征帧计算
            3.
        renewPrecisionPose-->getPrecisionPose
 * 测试目标2:
        run线程的运行

 */

#include "algorithm/calibration/mLidarCalib/fakeOdometry.h"
#ifndef _FAKEODOMETRY_PRIVATE_TEST_
#    pragma message("_FAKEODOMETRY_PRIVATE_TEST_ DISABLE")
#else
#    pragma message("_FAKEODOMETRY_PRIVATE_TEST_ ENABLE")
#    include <boost/bind.hpp>
#    include <gtest/gtest.h>
#    include <random>

namespace wj_slam {
/**
 * @description: 要使用测试fixture，请从testing:: test派生一个类
 * @note: 每个TEST_F使用同一实例的副本，任何改变都不传递到其他测试用例
 */
class fakeOdometryTest : public testing::Test {
    // 让成员受保护，因此它们可以从子类访问
  protected:
    typedef pcl::PointXYZHSV P;
    /**
     * @description: SetUp()将在每个TEST_F测试运行前自动调用。
     * @note: 如果需要初始化变量，重载该函数。
     */
    void SetUp() override
    {
        c_pFakeOdometry_.reset(
            new FakeOdometry<P>(c_featureBuf_,
                                boost::bind(&fakeOdometryTest::keyframeOutputCallback_, this, _1),
                                offsetof(P, s)));
    }
    /**
     * @description: TearDown()将在每个TEST_F测试运行后自动调用。
     * @note: 如果需要清理工作，重载该函数。
     */
    void TearDown() override
    {
        c_pFakeOdometry_ = nullptr;
    }
    void keyframeOutputCallback_(KEYFRAME<P>::Ptr p_keyframe)
    {
        c_pKeyframe_ = p_keyframe;
    }
    void setNewFeature(int p_iTime, int p_iID)
    {
        c_feature_.reset(new FEATURE_PAIR<P>());
        c_feature_->m_dTimestamp = p_iTime;
        c_feature_->m_dTimespan = SCAN_TIME_MS;
        c_feature_->m_iSample2ndSize = 0;
        c_feature_->m_uiScanFrame = p_iID;
        std::random_device rd;
        std::mt19937 gen(rd());
        std::normal_distribution<double> dis(0, 50.0);
        for (int n = 0; n < 3; ++n)
        {
            c_feature_->first->push_back(P(dis(gen), dis(gen), dis(gen)));
            *c_feature_->second = *c_feature_->first;
        }
        c_featureBuf_.push(c_feature_);
    }
    // 类接口需求
    FEATURE_PAIR<P>::Ptr c_feature_;
    std::queue<FEATURE_PAIR<P>::Ptr> c_featureBuf_;
    bool c_bPush_;
    KEYFRAME<P>::Ptr c_pKeyframe_;
    //待测试类
    FakeOdometry<P>::Ptr c_pFakeOdometry_;
};

/**
 * @description: 测试位姿流 更新-计算的三种状态
 */
TEST_F(fakeOdometryTest, poseTwistFlow)
{
    s_PoseWithTwist l_pLocate, l_pOdom;
    // 更新位姿
    l_pLocate.m_iTimetamp = 160000;
    l_pLocate.m_Pose.setX(0.0);
    l_pLocate.m_Twist.setX(1.0);
    c_pFakeOdometry_->renewPrecisionPose(l_pLocate);
    l_pOdom = c_pFakeOdometry_->getPrecisionPose();
    // 位置直接刷新
    EXPECT_DOUBLE_EQ(l_pOdom.m_Pose.x(), l_pLocate.m_Pose.x());
    // 速度不受外部设置影响
    EXPECT_DOUBLE_EQ(l_pOdom.m_Twist.x(), 0.0);
    l_pLocate.m_iTimetamp += SCAN_TIME_MS;
    l_pLocate.m_Pose.setX(0.1);
    l_pLocate.m_Twist.setX(1.0);
    c_pFakeOdometry_->renewPrecisionPose(l_pLocate);
    l_pOdom = c_pFakeOdometry_->getPrecisionPose();
    // 位置直接刷新
    EXPECT_DOUBLE_EQ(l_pOdom.m_Pose.x(), l_pLocate.m_Pose.x());
    // 速度和上次平均 上次0,这次根据位置变化计算为0.05
    EXPECT_DOUBLE_EQ(l_pOdom.m_Twist.x(), 0.05);
    // case 0 一般情况: 假设有输入-100ms位姿，新点云到达，计算转移
    // case 1 上次没计算完: 假设没有输入新位姿，新点云到达，计算转移
    // case 2 上上次计算完: 假设有输入-200ms新位姿，新点云到达，计算转移
    for (int i = 0; i < 3; ++i)
    {
        // 模拟第2帧之前收到0帧的位姿
        if (i == 2)
        {
            l_pLocate.m_iTimetamp = l_pLocate.m_iTimetamp + SCAN_TIME_MS;
            l_pLocate.m_Pose.setX(0.16);
            l_pLocate.m_Twist.setX(0.5);
            c_pFakeOdometry_->renewPrecisionPose(l_pLocate);
            l_pOdom = c_pFakeOdometry_->getPrecisionPose();
        }
        else
        {
            // 模拟新到点云时间第0帧点云+100,第1帧点云+200，第3帧点云+300
            c_pFakeOdometry_->c_timeCurr_ = l_pOdom.m_iTimetamp + SCAN_TIME_MS * (i + 1);
        }
        // 获取时间差
        c_pFakeOdometry_->c_fJumpNum_ = c_pFakeOdometry_->getJumpNum_();
        // c0间隔1，c1间隔2，c2间隔1
        if (i == 1)
            EXPECT_FLOAT_EQ(c_pFakeOdometry_->c_fJumpNum_, 2.0);
        else
            EXPECT_FLOAT_EQ(c_pFakeOdometry_->c_fJumpNum_, 1.0);
        // 时间推进更新
        c_pFakeOdometry_->renewOdom_();
        l_pOdom = c_pFakeOdometry_->getPrecisionPose();
        // 位置=最新位姿(外设)+最新速度（计算）*时间（当前到外设时间）
        EXPECT_DOUBLE_EQ(l_pOdom.m_Pose.x(),
                         l_pLocate.m_Pose.x()
                             + l_pOdom.m_Twist.x() * c_pFakeOdometry_->c_fJumpNum_);
    }
}
/**
 * @description: 测试基本流程:运行-暂停-结束
 */
TEST_F(fakeOdometryTest, run)
{
    s_PoseWithTwist l_pLocate;
    int l_iTime = 160000;
    int l_iScanID = 0;

    // 启动分离线程
    std::thread l_OdomThread = std::thread(&FakeOdometry<P>::run, c_pFakeOdometry_);
    l_OdomThread.detach();
    // 启动前初始化: 失败
    EXPECT_EQ(c_pFakeOdometry_->c_bSysHasInit, false);
    // 启动前运行控制:失败
    EXPECT_EQ(c_pFakeOdometry_->c_bCalibRun_, false);
    // 启动
    c_pFakeOdometry_->start();
    EXPECT_EQ(c_pFakeOdometry_->c_bCalibRun_, true);
    // case 0：前3帧正常发，正常收
    for (; l_iScanID < 3; ++l_iScanID)
    {
        // 加入新帧
        setNewFeature(l_iTime, l_iScanID);
        usleep(5e4);
        // 初始化：成功
        EXPECT_EQ(c_pFakeOdometry_->c_bSysHasInit, true);
        // 传递引用：成功
        EXPECT_EQ(c_pFakeOdometry_->c_pCurrFeature_, c_feature_);
        // 运行结束：成功
        EXPECT_EQ(c_pFakeOdometry_->c_bCalibRunOver_, true);
        // 外部接收：成功
        EXPECT_EQ(c_pKeyframe_->m_pFeature->m_dTimestamp, l_iTime);
        // 队列清空：成功
        EXPECT_EQ(c_featureBuf_.size(), 0);
        // 更新位姿
        l_pLocate.m_iTimetamp = l_iTime;
        c_pFakeOdometry_->renewPrecisionPose(l_pLocate);
        // 下一帧
        l_iTime += SCAN_TIME_MS;
    }
    // case 1：第2帧发送完成后stop
    c_pFakeOdometry_->stop();
    usleep(5e3);
    // 暂停检查：成功
    EXPECT_TRUE(c_pFakeOdometry_->isStop());
    // case 2：stop后发送第3~5帧，检查是否收到
    // case 3：结束后发送第6~9帧，检查是否收到
    for (; l_iScanID < 10; ++l_iScanID)
    {
        // 加入新帧
        setNewFeature(l_iTime, l_iScanID);
        usleep(5e4);
        // 队列清空：成功
        if (l_iScanID < 6)
        {
            EXPECT_EQ(c_featureBuf_.size(), 0);
        }
        // 接收新帧：失败
        EXPECT_NE(c_pFakeOdometry_->c_pCurrFeature_, c_feature_);
        // 运行结束：成功
        EXPECT_EQ(c_pFakeOdometry_->c_bCalibRunOver_, true);
        // 外部接收：失败
        EXPECT_NE(c_pKeyframe_->m_pFeature->m_dTimestamp, l_iTime);
        // 下一帧
        l_iTime += SCAN_TIME_MS;
        // case 3：结束
        if (l_iScanID == 5)
        {
            c_pFakeOdometry_->shutDown();
            usleep(5e3);
        }
    }
}
/**
 * @description: 测试重启流程:运行-暂停-重启
 */
TEST_F(fakeOdometryTest, restart)
{
    s_PoseWithTwist l_pLocate;
    KEYFRAME<P>::Ptr& l_pKeyframe_ = c_pKeyframe_;
    int l_iTime = 160000;
    int l_iScanID = 0;
    // 启动分离线程
    std::thread l_OdomThread = std::thread(&FakeOdometry<P>::run, c_pFakeOdometry_);
    l_OdomThread.detach();
    // 启动
    c_pFakeOdometry_->start();
    // case 0：0~2帧正常发，正常收
    // case 1：2~3中间暂停
    // case 2：5~6中间重启
    // case 3：8~检查是否成功重启
    for (; l_iScanID < 12; ++l_iScanID)
    {
        // 加入新帧
        setNewFeature(l_iTime, l_iScanID);
        usleep(5e4);
        if (l_iScanID > 7)
        {
            // 初始化：成功
            EXPECT_EQ(c_pFakeOdometry_->c_bSysHasInit, true);
            // 传递引用：成功
            EXPECT_EQ(c_pFakeOdometry_->c_pCurrFeature_, c_feature_);
            // 时间连续
            EXPECT_FLOAT_EQ(c_pFakeOdometry_->c_fJumpNum_, 1.0);
            // 运行结束：成功
            EXPECT_EQ(c_pFakeOdometry_->c_bCalibRunOver_, true);
            // 外部接收：成功
            EXPECT_EQ(c_pKeyframe_->m_pFeature->m_dTimestamp, l_iTime);
            // 队列清空：成功
            EXPECT_EQ(c_featureBuf_.size(), 0);
        }
        // 除结束帧外都模拟收到位姿
        if (l_iScanID < 2 || l_iScanID > 5)
        {
            // 更新位姿
            l_pLocate.m_iTimetamp = l_iTime;
            l_pLocate.m_Pose.setX(0.0 + l_iScanID * SCAN_TIME);
            c_pFakeOdometry_->renewPrecisionPose(l_pLocate);
        }
        // 下一帧
        l_iTime += SCAN_TIME_MS;
        // case 1：第2号发送完成后stop
        if (l_iScanID == 2)
            c_pFakeOdometry_->stop();
        // case 2：第5号发送完成重启
        if (l_iScanID == 5)
            c_pFakeOdometry_->start();
    }

    // case 4：结束
    c_pFakeOdometry_->shutDown();
    usleep(1e6);
}
}  // namespace wj_slam

#endif
