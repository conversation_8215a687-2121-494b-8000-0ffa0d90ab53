/**
 * @file location_test.cpp
 * <AUTHOR>
 * @brief
 * @version 1.0
 * @date 2023-07-31
 */
#include "../../include/common/common_ex.h"
#include "../../test/location/location.h"
#include "../../test/param.hpp"
#include <gtest/gtest.h>
#include <visualization_msgs/Marker.h>
#include <visualization_msgs/MarkerArray.h>

WJLog* generalLog::wjlog = nullptr;
// 路径日志
WJLog* generalLog::pathlog = nullptr;
// 校验日志
WJLog* generalLog::checklog = nullptr;
// 速度日志
WJLog* generalLog::speedlog = nullptr;
// 对外日志
WJLog* generalLog::faelog = nullptr;

namespace wj_slam {
class location_test : public testing::Test {
  protected:
    typedef pcl::PointXYZI POSE;
    typedef pcl::PointXYZHSV PoinT;
    typedef boost::shared_ptr<Location<PoinT, PoinT>> Ptr;
    typedef KEYFRAME<PoinT> KeyFrame;
    typedef typename KeyFrame::Ptr KeyFramePtr;
    typedef boost::function<void(s_PoseWithTwist&)> LT_OUTPUT_CB;
    typedef boost::function<bool(s_PoseWithTwist&, int)> WHEELOdom_CB;

    SYSPARAM* c_stSysParam_;
    bool c_bHasSend_ = false;
    std::thread loca;
    Location<PoinT, PoinT>::Ptr c_location;
    Queue<KeyFramePtr> c_keyframe;
    std::mutex c_mutex;
    // LT_OUTPUT_CB c_highCb;
    WHEELOdom_CB c_wheelCb;
    generalLog* glog = new generalLog("/home/<USER>/slam_wsv2.0/src/wanji_slam/data/Log/Logfiles",
                                      "wj",
                                      FileMode::DAILY_ROTATE,
                                      LogMode::TRIAL_MODE,
                                      10);
    bool getSendInfoState(void)
    {
        if (!c_stSysParam_->m_bDebugModel
            && (c_stSysParam_->m_iWorkMode == WorkMode::InitMapMode
                || c_stSysParam_->m_iWorkMode == WorkMode::ContMapMode))
        {
            return true;
        }
        return false;
    }
    void highcallback(s_PoseWithTwist& p_pose)
    {
        p_pose.m_Pose.printf("highpose");
        p_pose.m_Twist.printf("hightwist");
    }
    void backfeature(int a, int b) {}
    void sendpose(std::vector<s_POSE6D> p_pose)
    {
        if (p_pose.size() >= 2)
        {
            p_pose[0].printf("web lidar pose");
            p_pose[1].printf("web agv pose");
        }
        if (getSendInfoState())
            return;
        if (p_pose.size() >= 1)
        {
            p_pose[0].printf("outputPose_");
            p_pose[0].printf("outputPath_");
        }
        if (p_pose.size() >= 2)
        {
            p_pose[1].printf("outputPoseOutEnd_");
            p_pose[1].printf("outputPathOutEnd_");
        }
        if (p_pose.size() >= 3)
        {
            p_pose[2].printf("outputPosePred_");
            p_pose[2].printf("outputPathPred_");
        }
        if (p_pose.size() >= 4)
        {
            p_pose[3].printf("outputPoseCurb_");
            p_pose[3].printf("outputPathCurb_");
        }
    }
    void transPcToDebugMode(boost::shared_ptr<FEATURE_PAIR<PoinT>> pc,
                            pcl::PointCloud<pcl::PointXYZHSV>::Ptr& pcout,
                            bool p_bCurrFrame = false)
    {
        if (!c_stSysParam_->m_bDebugModel)
            return;
        printf("display %ld - %ld\n", pc->surfSize(), pc->m_iSample2ndSize);
        int offset_corn = 0;
        int offset_surf = 0;
        int offset_UnsSurf = 0;
        int offset_curb = 0;
        pcout->clear();
        typename pcl::PointCloud<PoinT>::Ptr l_outPc(new pcl::PointCloud<PoinT>());
        *l_outPc = *pc->first;
        offset_corn = l_outPc->points.size();
        *l_outPc += *pc->second;
        // 当前帧标记采样点
        if (p_bCurrFrame)
        {
            offset_surf = offset_corn + pc->m_iSample2ndSize;
            offset_UnsSurf = l_outPc->points.size();
        }
        else
        {
            offset_surf = l_outPc->points.size();
            offset_UnsSurf = offset_surf;
        }
        *l_outPc += *pc->fourth;
        offset_curb = l_outPc->points.size();
        pcl::copyPointCloud(*l_outPc, *pcout);
        for (int i = 0; i < offset_corn; i++)
            pcout->points[i].h = 1;
        for (int i = offset_corn; i < offset_surf; i++)
            pcout->points[i].h = 2;
        for (int i = offset_surf; i < offset_UnsSurf; i++)
            pcout->points[i].h = 3;
        for (int i = offset_UnsSurf; i < offset_curb; i++)
            pcout->points[i].h = 4;
    }
    void showPointCloud(std::string pub, typename pcl::PointCloud<PoinT>::Ptr& pc)
    {
        std::cout << pub << std::endl;
        // sensor_msgs::PointCloud2 msgPC;
        // pcl::toROSMsg(*pc, msgPC);
        // msgPC.header.frame_id = "world";
        // msgPC.header.stamp = ros::Time::now();
        // pub.publish(msgPC);
    }
    void showPC(boost::shared_ptr<KEYFRAME<PoinT>> pc,
                boost::shared_ptr<KEYFRAME<PoinT>> map = nullptr)
    {
        // 如果是debug模式 或者 定位模式 则进入发送程序
        if (getSendInfoState())
            return;

        if (c_stSysParam_->m_bDebugModel)
        {
            pcl::PointCloud<pcl::PointXYZHSV>::Ptr l_pOutPC(
                new pcl::PointCloud<pcl::PointXYZHSV>());
            if (c_stSysParam_->m_bSendCurPC && pc)
            {
                transPcToDebugMode(pc->m_pFeature, l_pOutPC, true);
                showPointCloud("outputCurr_", l_pOutPC);
            }
            if (c_stSysParam_->m_bSendMap && map)
            {
                transPcToDebugMode(map->m_pFeature, l_pOutPC);
                showPointCloud("outputMap_", l_pOutPC);
                c_bHasSend_ = false;
            }
            showPointCloud("outputMarksMap", map->m_pFeature->third);
            showPointCloud("outputCurMarks", pc->m_pFeature->third);
        }
        else
        {
            typename pcl::PointCloud<PoinT>::Ptr outPC(new pcl::PointCloud<PoinT>());
            if (c_stSysParam_->m_bSendCurPC && pc)
            {
                s_POSE6D l_pose = pc->m_Pose;
                if (c_stSysParam_->m_iViewMode == 0)
                {
                    c_location->c_pMatcher_->transformCloudPoints(
                        l_pose.m_quat, l_pose.m_trans, pc->m_pFeature->allPC, outPC);
                    showPointCloud("outputCurr_", outPC);
                }
                else
                    showPointCloud("outputCurr_", pc->m_pFeature->fourth);
            }
            if (c_stSysParam_->m_bSendMap && map)
            {
                if (!c_bHasSend_)
                {
                    c_bHasSend_ = true;
                    if (c_stSysParam_->m_iViewMode == 0)
                        showPointCloud("outputMap_", map->m_pFeature->allPC);
                    else
                        showPointCloud("outputMap_", map->m_pFeature->fourth);
                }
            }
            else
            {
                c_bHasSend_ = false;
            }
        }
    }
    void sendpc(KeyFramePtr p_a, KeyFramePtr p_b)
    {
        boost::shared_ptr<KEYFRAME<PoinT>> l_pc(new KEYFRAME<PoinT>());
        boost::shared_ptr<KEYFRAME<PoinT>> l_map(new KEYFRAME<PoinT>());
        *l_pc = *p_a;
        *l_map = *p_b;
        std::thread showpc(&location_test::showPC, this, l_pc, l_map);
        showpc.detach();
    }
    void sendbox(pcl::PointCloud<pcl::PointXYZ>::Ptr p_stBox)
    {
        visualization_msgs::Marker lineList;
        lineList.header.frame_id = "world";
        lineList.lifetime = ros::Duration();
        lineList.ns = "lines";
        lineList.action = visualization_msgs::Marker::DELETEALL;
        lineList.action = visualization_msgs::Marker::ADD;
        lineList.pose.orientation.w = 1.0;
        lineList.type = visualization_msgs::Marker::LINE_LIST;
        lineList.scale.x = 0.05;
        lineList.id = 1;
        lineList.color.r = 1.0;
        lineList.color.g = 0.0;
        lineList.color.b = 0.0;
        lineList.color.a = 1.0;

        for (uint32_t i = 0; i < p_stBox->points.size(); i++)
        {
            geometry_msgs::Point p;
            p.x = p_stBox->points[i].x;
            p.y = p_stBox->points[i].y;
            p.z = p_stBox->points[i].z;
            lineList.points.push_back(p);

            if (i != 0)
                lineList.points.push_back(p);
            if (i == p_stBox->points.size() - 1)
            {
                p.x = p_stBox->points[0].x;
                p.y = p_stBox->points[0].y;
                p.z = p_stBox->points[0].z;
                lineList.points.push_back(p);
            }
        }
        lineList.header.stamp = ros::Time::now();
        // outPredBox_.publish(lineList);
        std::cout << "outPredBox_" << std::endl;
    }
    void fillPcdNameList_(std::vector<std::string>& l_vfiles, std::string l_sPath)
    {
        boost::filesystem::path p = boost::filesystem::path(l_sPath);
        if (!boost::filesystem::exists(p))
            return;
        for (auto i = boost::filesystem::directory_iterator(p);
             i != boost::filesystem::directory_iterator();
             i++)
        {
            // 非目录则添加
            if (!boost::filesystem::is_directory(i->path()))
            {
                l_vfiles.push_back(i->path().filename().string());
            }
        }
        // 从小到大排序
        if (!l_vfiles.empty())
            sort(l_vfiles.begin(), l_vfiles.end());
    }
    void SetUp() override
    {
        Param l_param(false);
        l_param.loadSysParam("/home/<USER>/slam_wsv2.0/src/wanji_slam");
        c_stSysParam_ = SYSPARAM::getIn();
        c_stSysParam_->m_sPkgPath = "/home/<USER>/slam_wsv2.0/src/wanji_slam";
        c_location.reset(
            new Location<PoinT, PoinT>(c_keyframe,
                                       boost::bind(&location_test::highcallback, this, _1),
                                       c_wheelCb,
                                       boost::bind(&location_test::backfeature, this, _1, _2),
                                       boost::bind(&location_test::sendpose, this, _1),
                                       boost::bind(&location_test::sendpc, this, _1, _2),
                                       boost::bind(&location_test::sendbox, this, _1)));
    }
    void TearDown() override
    {
        // c_location->~Location();
    }
};
/*所有测试之前请将location.h中gtest_test.h头文件包含引用打开*/
/*此测试需要加载一个正确格式地图，用来验证切换模式正常读去地图*/
// TEST_F(location_test, Location)
// {
//     EXPECT_EQ(0, c_location->c_stSysParam_->m_iWorkMode);
//     EXPECT_EQ(c_location->c_pMatcher_->c_b2dMatch_, true);
//     EXPECT_EQ(c_location->c_bLocationRunOver_, true);
//     EXPECT_EQ(c_location->c_bShutDown_, false);
//     EXPECT_EQ(c_location->c_bShutDownOver_, false);
//     EXPECT_EQ(c_location->c_bHasInit, false);
//     EXPECT_EQ(c_location->c_bIsKeyFrame_, false);
//     EXPECT_EQ(c_location->c_bLocationRun_, false);

//     c_stSysParam_->m_iWorkMode = 1;
//     c_location->c_bHasInit = true;
//     c_location->setWorkMode(c_stSysParam_->m_iWorkMode);
//     EXPECT_EQ(c_location->c_sConfig_.m_bIsEnLoop, false);
//     EXPECT_EQ(c_location->c_sConfig_.m_bOnlyLocatMode, false);
//     EXPECT_EQ(c_location->c_sConfig_.m_bCopyLatestKeyFrame, false);
//     EXPECT_EQ(c_location->c_sConfig_.m_bIsEnAmcl, false);
//     EXPECT_EQ(c_location->c_sConfig_.m_bIsEnRM, true);
//     EXPECT_EQ(c_location->c_sConfig_.m_bIsSendPC, true);
//     EXPECT_EQ(c_location->c_sConfig_.m_workMode, c_stSysParam_->m_iWorkMode);
//     EXPECT_EQ(c_location->c_pMatcher_->c_iWorkModel_, c_stSysParam_->m_iWorkMode);
//     EXPECT_EQ(c_location->c_bLocationRun_, true);

//     c_stSysParam_->m_iWorkMode = 2;
//     c_location->setWorkMode(c_stSysParam_->m_iWorkMode);
//     EXPECT_EQ(c_location->c_sConfig_.m_bIsEnLoop, false);
//     EXPECT_EQ(c_location->c_sConfig_.m_bOnlyLocatMode, false);
//     EXPECT_EQ(c_location->c_sConfig_.m_bCopyLatestKeyFrame, false);
//     EXPECT_EQ(c_location->c_sConfig_.m_bIsEnAmcl, false);
//     EXPECT_EQ(c_location->c_sConfig_.m_bIsEnRM, true);
//     EXPECT_EQ(c_location->c_sConfig_.m_bIsSendPC, true);
//     EXPECT_EQ(c_location->c_sConfig_.m_workMode, c_stSysParam_->m_iWorkMode);
//     EXPECT_EQ(c_location->c_pMatcher_->c_iWorkModel_, c_stSysParam_->m_iWorkMode);

//     c_stSysParam_->m_iWorkMode = 3;
//     c_location->setWorkMode(c_stSysParam_->m_iWorkMode);
//     EXPECT_EQ(c_location->c_sConfig_.m_bIsEnLoop, false);
//     EXPECT_EQ(c_location->c_sConfig_.m_bOnlyLocatMode, true);
//     EXPECT_EQ(c_location->c_sConfig_.m_bCopyLatestKeyFrame, true);
//     EXPECT_EQ(c_location->c_sConfig_.m_bIsEnAmcl, false);
//     EXPECT_EQ(c_location->c_sConfig_.m_bIsEnRM, false);
//     EXPECT_EQ(c_location->c_sConfig_.m_bIsSendPC, true);
//     EXPECT_EQ(c_location->c_sConfig_.m_workMode, c_stSysParam_->m_iWorkMode);
//     EXPECT_EQ(c_location->c_pMatcher_->c_iWorkModel_, c_stSysParam_->m_iWorkMode);

//     c_stSysParam_->m_iWorkMode = 4;
//     c_location->setWorkMode(c_stSysParam_->m_iWorkMode);
//     EXPECT_EQ(c_location->c_sConfig_.m_bIsEnLoop, false);
//     EXPECT_EQ(c_location->c_sConfig_.m_bOnlyLocatMode, true);
//     EXPECT_EQ(c_location->c_sConfig_.m_bCopyLatestKeyFrame, true);
//     EXPECT_EQ(c_location->c_sConfig_.m_bIsEnAmcl, false);
//     EXPECT_EQ(c_location->c_sConfig_.m_bIsEnRM, false);
//     EXPECT_EQ(c_location->c_sConfig_.m_bIsSendPC, true);
//     EXPECT_EQ(c_location->c_sConfig_.m_workMode, c_stSysParam_->m_iWorkMode);
// }

// TEST_F(location_test, checkNewKeyFrames_)
// {
//     EXPECT_EQ(c_location->checkNewKeyFrames_(), false);
//     EXPECT_EQ(c_location->c_qKeyFrameOptToBe_.size(), 0);
//     KeyFramePtr l_feature(new KeyFrame());
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf.pcd",
//     *l_feature->m_pFeature->second);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/corn.pcd", *l_feature->m_pFeature->first);
//     c_keyframe.push(l_feature);
//     EXPECT_EQ(c_location->c_qKeyFrameOptToBe_.size(), 1);
//     EXPECT_EQ(c_location->checkNewKeyFrames_(), false);
//     EXPECT_EQ(c_location->c_qKeyFrameOptToBe_.size(), 0);

//     c_stSysParam_->m_iWorkMode = 1;
//     c_location->setWorkMode(c_stSysParam_->m_iWorkMode);
//     c_keyframe.push(l_feature);
//     EXPECT_EQ(c_location->checkNewKeyFrames_(), true);
//     c_location->c_bShutDown_ = true;
//     EXPECT_EQ(c_location->checkNewKeyFrames_(), false);
// }

// TEST_F(location_test, getKeyFrame_)
// {
//     KeyFramePtr l_feature(new KeyFrame());
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf.pcd",
//     *l_feature->m_pFeature->second);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/corn.pcd", *l_feature->m_pFeature->first);
//     l_feature->m_pFeature->m_uiScanFrame = 1;
//     c_keyframe.push(l_feature);
//     KeyFramePtr l_getfeature = c_location->getKeyFrame_();
//     EXPECT_EQ(l_getfeature->m_pFeature->surfSize(), l_feature->m_pFeature->surfSize());
//     EXPECT_EQ(l_getfeature->m_pFeature->cornerSize(), l_feature->m_pFeature->cornerSize());

//     c_stSysParam_->m_iWorkMode = 1;
//     c_location->setWorkMode(c_stSysParam_->m_iWorkMode);
//     c_keyframe.push(l_feature);
//     l_getfeature = c_location->getKeyFrame_();
//     EXPECT_EQ(l_getfeature->m_pFeature->m_uiScanFrame, l_feature->m_pFeature->m_uiScanFrame);

//     c_stSysParam_->m_iWorkMode = 3;
//     c_location->setWorkMode(c_stSysParam_->m_iWorkMode);
//     for (size_t i = 0; i < 10; i++)
//     {
//         l_feature->m_pFeature->m_uiScanFrame = i;
//         c_keyframe.push(l_feature);
//     }
//     l_getfeature = c_location->getKeyFrame_();
//     EXPECT_EQ(l_getfeature->m_pFeature->m_uiScanFrame, 9);
// }

// TEST_F(location_test, location_)
// {
//     KeyFramePtr l_feature(new KeyFrame());
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf.pcd",
//     *l_feature->m_pFeature->second);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/corn.pcd", *l_feature->m_pFeature->first);
//     l_feature->m_pFeature->m_uiScanFrame = 1;

//     EXPECT_EQ(c_location->location_(l_feature, c_location->c_CurrLocStatus_), true);
//     EXPECT_EQ(l_feature->m_Pose.m_fPercent, -1);
//     c_location->c_bHasInit = true;
//     EXPECT_EQ(c_location->location_(l_feature, c_location->c_CurrLocStatus_), false);

//     KeyFramePtr l_map(new KeyFrame());
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/src/wanji_slam/data/Map/jh_/se.pcd",
//                          *l_map->m_pFeature->second);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/src/wanji_slam/data/Map/jh_/fi.pcd",
//                          *l_map->m_pFeature->first);

//     c_location->setTarget_(l_feature);

//     s_POSE6D l_pose;
//     l_pose.setXYZ(0.1, 0, 0);
//     l_pose.setRPY(0, 0, 0);
//     c_location->transPointCloud_(l_pose, l_feature);

//     c_location->c_bIsKeyFrame_ = true;
//     c_location->c_sConfig_.m_bOnlyLocatMode = true;
//     EXPECT_EQ(c_location->matchSlam_(l_feature), true);
//     c_location->c_sConfig_.m_bOnlyLocatMode = false;
//     EXPECT_EQ(c_location->matchSlam_(l_feature), true);
// }

// TEST_F(location_test, quickAnalyPoseStatus)
// {
//     s_PoseWithTwist l_curr, l_pred;
//     PoseCheckStatus l_state;
//     bool l_pre = false;
//     c_location->c_bManualPoseModel = true;
//     EXPECT_EQ(c_location->quickAnalyPoseStatus(l_curr, l_pred, l_state, l_pre), true);
//     EXPECT_EQ(l_state, PoseCheckStatus::Valid);

//     c_location->c_bManualPoseModel = false;
//     l_state = PoseCheckStatus::Novalid;
//     EXPECT_EQ(c_location->quickAnalyPoseStatus(l_curr, l_pred, l_state, l_pre), true);
//     EXPECT_EQ(l_state, PoseCheckStatus::Valid);

//     c_location->c_stSysParam_->m_posCheck.m_bOpenPoseCheck = true;
//     c_location->c_stSysParam_->m_posCheck.m_bOnlyWheelOdom = true;
//     l_state = PoseCheckStatus::Novalid;
//     EXPECT_EQ(c_location->quickAnalyPoseStatus(l_curr, l_pred, l_state, l_pre), true);
//     EXPECT_EQ(l_state, PoseCheckStatus::Valid);

//     c_location->c_stSysParam_->m_posCheck.m_bOnlyWheelOdom = false;
//     c_location->c_PoseCheckNoClear = PoseCheckStatus::Novalid;
//     l_state = PoseCheckStatus::Valid;
//     EXPECT_EQ(c_location->quickAnalyPoseStatus(l_curr, l_pred, l_state, l_pre), true);
//     EXPECT_EQ(l_state, PoseCheckStatus::Novalid);

//     c_location->c_bOccupyVerify_ = true;
//     l_state = PoseCheckStatus::Valid;
//     EXPECT_EQ(c_location->quickAnalyPoseStatus(l_curr, l_pred, l_state, l_pre), true);
//     EXPECT_EQ(l_state, PoseCheckStatus::Novalid);

//     c_location->c_stSysParam_->m_posCheck.m_iOccVerifyBypassThd = 0;
//     c_location->c_stSysParam_->m_posCheck.m_bUseOccupyVerify = true;
//     l_state = PoseCheckStatus::Novalid;
//     EXPECT_EQ(c_location->quickAnalyPoseStatus(l_curr, l_pred, l_state, l_pre), true);
//     EXPECT_EQ(l_state, PoseCheckStatus::Valid);

//     l_pre = true;
//     l_state = PoseCheckStatus::Novalid;
//     EXPECT_EQ(c_location->quickAnalyPoseStatus(l_curr, l_pred, l_state, l_pre), true);
//     EXPECT_EQ(l_state, PoseCheckStatus::Valid);

//     c_location->c_PoseCheckNoClear = PoseCheckStatus::Valid;
//     l_state = PoseCheckStatus::Valid;
//     EXPECT_EQ(c_location->quickAnalyPoseStatus(l_curr, l_pred, l_state, l_pre), true);
//     EXPECT_EQ(l_state, PoseCheckStatus::Novalid);

//     l_pre = false;
//     l_curr.setFlag(PoseStatus::ContinuePose);
//     l_curr.m_Pose.m_bFlag = PoseStatus::ContinuePose;
//     l_state = PoseCheckStatus::Novalid;
//     EXPECT_EQ(c_location->quickAnalyPoseStatus(l_curr, l_pred, l_state, l_pre), true);
//     EXPECT_EQ(l_state, PoseCheckStatus::Valid);
// }

// TEST_F(location_test, poseTwistCheck)
// {
//     s_PoseWithTwist l_curr, l_pred;
//     l_curr.m_Pose.setXYZ(0.1, 0.1, 0);
//     l_curr.m_Pose.setRPY(0, 0, 0.1);
//     l_curr.m_Twist.setXYZ(0.1, 0.1, 0);
//     l_curr.m_Twist.setRPY(0, 0, 0.1);

//     l_pred = l_curr;
//     l_pred.recvTimeAlign(100);
//     l_curr.m_Pose.setXYZ(0.25, 0.25, 0);
//     l_curr.m_Pose.setRPY(0, 0, 0.2);
//     l_curr.m_Twist.setXYZ(0.1, 0.1, 0);
//     l_curr.m_Twist.setRPY(0, 0, 0.1);
//     c_location->c_stSysParam_->m_posCheck.m_stPoseCheck.m_Twist.setXYZ(0.15, 0.15, 0);
//     c_location->c_stSysParam_->m_posCheck.m_stPoseCheck.m_Twist.setRPY(0, 0, 0.1);
//     c_location->poseTwistCheck(l_curr,
//                                l_pred,
//                                c_stSysParam_->m_posCheck.m_stPoseCheck,
//                                c_stSysParam_->m_posCheck.m_stPoseMoveStatus,
//                                "poseCheck");
// }

// TEST_F(location_test, updatePoseStatus_)
// {
//     KeyFramePtr l_feature(new KeyFrame());
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf.pcd",
//     *l_feature->m_pFeature->second);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/corn.pcd", *l_feature->m_pFeature->first);
//     c_location->c_pKeyFrameOptToBe_ = l_feature;
//     c_location->c_pKeyFrameOptToBe_->m_Pose.setXYZ(0.1, 0, 0);
//     c_location->c_pKeyFrameOptToBe_->m_Pose.setRPY(0, 0, 0.1);
//     c_location->c_pKeyFrameOptToBe_->m_Pose.setP(1.0);
//     c_location->c_pKeyFrameOptToBe_->m_Pose.m_bFlag = PoseStatus::ContinuePose;

//     s_PoseWithTwist l_curr, l_pred;
//     l_curr.m_Pose.setXYZ(0.1, 0.1, 0);
//     l_curr.m_Pose.setRPY(0, 0, 0.1);
//     l_curr.m_Twist.setXYZ(0.1, 0.1, 0);
//     l_curr.m_Twist.setRPY(0, 0, 0.1);
//     l_curr.m_bFlag = PoseStatus::ContinuePose;
//     l_curr.m_iRecvTimestamp = 100;
//     l_pred = l_curr;
//     l_pred.recvTimeAlign(100);

//     c_location->c_stSysParam_->m_posCheck.m_bOnlyWheelOdom = true;
//     c_location->c_stSysParam_->m_posCheck.m_bOpenPoseCheck = true;
//     c_location->c_stSysParam_->m_posCheck.m_bUseLoct = true;
//     c_location->c_stSysParam_->m_posCheck.m_bSafeModel = true;
//     c_location->c_poseTwistContainer_.setLastSlamPrecPoseTwist(l_pred);
//     c_location->updatePoseStatus_(l_curr);
//     printf("flag %d\n", c_location->c_PoseCheckStatus);
// }

// TEST_F(location_test, checkVirtualMoveOverFar_)
// {
//     s_PoseWithTwist l_curr, l_pred;
//     l_curr.m_Pose.setXYZ(0.1, 0.1, 0);
//     l_curr.m_Pose.setRPY(0, 0, 0.1);
//     l_curr.m_Twist.setXYZ(0.1, 0.1, 0);
//     l_curr.m_Twist.setRPY(0, 0, 0.1);
//     l_curr.m_iRecvTimestamp = 100;

//     l_pred.m_Pose.setXYZ(1, 1, 0);
//     l_pred.m_Pose.setRPY(0, 0, 1);
//     l_pred.m_Twist.setXYZ(0.1, 0.1, 0);
//     l_pred.m_Twist.setRPY(0, 0, 0.1);
//     l_pred.m_iRecvTimestamp = 10000;

//     EXPECT_EQ(c_location->checkVirtualMoveOverFar_(l_curr, l_pred), false);

//     c_location->c_stSysParam_->m_posCheck.m_bSafeModel = true;
//     c_location->c_stSysParam_->m_posCheck.m_fPredPosFarDist = 100;
//     c_location->c_stSysParam_->m_posCheck.m_fPredPosFarTime = 1;
//     EXPECT_EQ(c_location->checkVirtualMoveOverFar_(l_curr, l_pred), true);
// }

// TEST_F(location_test, updateAGVPose)
// {
//     s_PoseWithTwist l_curr, l_pred;
//     l_curr.m_Pose.setXYZ(0.1, 0.1, 0);
//     l_curr.m_Pose.setRPY(0, 0, 0.1);
//     l_curr.m_Twist.setXYZ(0.1, 0.1, 0);
//     l_curr.m_Twist.setRPY(0, 0, 0.1);
//     l_curr.m_iRecvTimestamp = 100;
//     c_location->c_poseTwistContainer_.setFusePoseTwist(l_curr);

//     l_pred.m_Pose.setXYZ(0.2, 0.2, 0);
//     l_pred.m_Pose.setRPY(0, 0, 0.2);
//     l_pred.m_Twist.setXYZ(0.1, 0.1, 0);
//     l_pred.m_Twist.setRPY(0, 0, 0.1);
//     l_pred.m_iRecvTimestamp = 200;
//     c_location->c_stSysParam_->m_agv.m_stTrans.setXYZ(100, 100, 0);
//     c_location->updateAGVPose(l_pred);
//     EXPECT_FLOAT_EQ(c_location->c_stSysParam_->m_pos.m_stCurrPoseWJ.m_Pose.x(),
//                     l_pred.m_Pose.x() + 100);
//     EXPECT_FLOAT_EQ(c_location->c_stSysParam_->m_pos.m_stCurrPoseWJ.m_Pose.y(),
//                     l_pred.m_Pose.y() + 100);
//     EXPECT_FLOAT_EQ(c_location->c_stSysParam_->m_pos.m_stCurrPoseWJ.m_Pose.z(),
//     l_pred.m_Pose.z());
// }

// TEST_F(location_test, renewPose_)
// {
//     KeyFramePtr l_feature(new KeyFrame());
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf.pcd",
//     *l_feature->m_pFeature->second);
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/corn.pcd", *l_feature->m_pFeature->first);
//     c_location->c_pKeyFrameOptToBe_ = l_feature;
//     c_location->c_pKeyFrameOptToBe_->m_Pose.setXYZ(0.1, 0, 0);
//     c_location->c_pKeyFrameOptToBe_->m_Pose.setRPY(0, 0, 0.1);
//     c_location->c_pKeyFrameOptToBe_->m_Pose.setP(1.0);
//     c_location->c_pKeyFrameOptToBe_->m_Pose.m_bFlag = PoseStatus::ContinuePose;

//     c_location->c_stIncreaseOpt_.setXYZ(0.1, 0, 0);
//     c_location->c_stIncreaseOpt_.setRPY(0, 0, 0);

//     c_location->renewPose_();
//     EXPECT_EQ(c_location->c_pKeyFrameOptToBe_->m_Pose.x(), 0.2);
//     EXPECT_EQ(c_location->c_pKeyFrameOptToBe_->m_Pose.y(), 0);
//     EXPECT_EQ(c_location->c_pKeyFrameOptToBe_->m_Pose.z(), 0);

//     EXPECT_FLOAT_EQ(c_location->c_poseTwistContainer_.getSlamOptPoseTwist().m_Pose.x(),
//                     c_location->c_pKeyFrameOptToBe_->m_Pose.x());
//     EXPECT_FLOAT_EQ(c_location->c_poseTwistContainer_.getSlamOptPoseTwist().m_Pose.y(),
//                     c_location->c_pKeyFrameOptToBe_->m_Pose.y());
//     EXPECT_FLOAT_EQ(c_location->c_poseTwistContainer_.getSlamOptPoseTwist().m_Pose.z(),
//                     c_location->c_pKeyFrameOptToBe_->m_Pose.z());

//     c_location->c_bHasInit = true;
//     c_location->c_fJumpNum_ = 1.0;
//     c_location->c_pKeyFrameOptToBe_->m_Pose.setXYZ(0.2, 0, 0);
//     c_location->c_pKeyFrameOptToBe_->m_Pose.setRPY(0, 0, 0.1);
//     c_location->c_pKeyFrameOptToBe_->m_Pose.setP(1.0);
//     c_location->c_pKeyFrameOptToBe_->m_Pose.m_bFlag = PoseStatus::ContinuePose;
//     c_location->c_stSysParam_->m_posCheck.m_bUseLoct = true;

//     c_location->renewPose_();
//     c_location->c_poseTwistContainer_.getSlamOptPoseTwist().m_Twist.printf("speed");

//     s_PoseWithTwist l_last;
//     s_PoseWithTwist l_curr;
//     s_PoseWithTwist l_pred;
//     PoseCheckStatus l_state;
//     l_last.m_Pose.setXYZ(0.1, 0.1, 0);
//     l_last.m_Pose.setRPY(0, 0, 0.1);
//     l_last.m_Pose.m_bFlag = PoseStatus::ContinuePose;
//     l_last.m_Twist.setXYZ(0.1, 0.1, 0);
//     l_last.m_Twist.setRPY(0, 0, 0.1);
//     l_last.m_iRecvTimestamp = 100;
//     l_last.m_bFlag = PoseStatus::ContinuePose;
//     c_location->c_poseTwistContainer_.setLastSlamPrecPoseTwist(l_last);

//     l_curr.m_Pose.setXYZ(0.2, 0.2, 0);
//     l_curr.m_Pose.setRPY(0, 0, 0.2);
//     l_curr.m_Pose.m_bFlag = PoseStatus::ContinuePose;
//     l_curr.m_Twist.setXYZ(0.1, 0.1, 0);
//     l_curr.m_Twist.setRPY(0, 0, 0.1);
//     l_curr.m_iRecvTimestamp = 200;
//     l_curr.m_bFlag = PoseStatus::ContinuePose;

//     c_location->c_stSysParam_->m_posCheck.m_bUseLoct = true;
//     c_location->c_bManualPoseModel = true;
//     c_location->getPoseCheckStatus_(l_curr, l_pred, l_state);
//     EXPECT_EQ(l_state, PoseCheckStatus::Valid);
//     l_pred.m_Pose.printf("pose");
//     l_pred.m_Twist.printf("twist");
// }

// TEST_F(location_test, initFrameLabel)
// {
//     KeyFramePtr l_feature(new KeyFrame());
//     pcl::io::loadPCDFile("/home/<USER>/slam_wsv2.0/output/surf.pcd",
//     *l_feature->m_pFeature->second); c_location->c_stSysParam_->m_map.m_fGroundHigh = 1.5;
//     c_location->c_stSysParam_->m_map.m_fRoofHigh = 2.0;
//     c_location->initFrameLabel(l_feature);
//     pcl::io::savePCDFileASCII("/home/<USER>/slam_wsv2.0/output/se.pcd",
//                               *l_feature->m_pFeature->second);
// }

// TEST_F(location_test, isUpdateMap_)
// {
//     s_PoseWithTwist l_curr, l_fuse, l_addMap;
//     l_curr.m_Pose.setXYZ(0.2, 0.2, 0);
//     l_curr.m_Pose.setRPY(0, 0, 0.2);
//     l_curr.m_Pose.m_bFlag = PoseStatus::ContinuePose;
//     l_curr.m_Twist.setXYZ(0.1, 0.1, 0);
//     l_curr.m_Twist.setRPY(0, 0, 0.1);
//     l_curr.m_iRecvTimestamp = 200;
//     l_curr.m_bFlag = PoseStatus::ContinuePose;

//     l_addMap.m_Pose.setXYZ(1, 1, 0);
//     l_addMap.m_Pose.setRPY(0, 0, 1);
//     l_addMap.m_Pose.m_bFlag = PoseStatus::ContinuePose;
//     l_addMap.m_Twist.setXYZ(0.1, 0.1, 0);
//     l_addMap.m_Twist.setRPY(0, 0, 0.1);
//     l_addMap.m_iRecvTimestamp = 100;
//     l_addMap.m_bFlag = PoseStatus::ContinuePose;
//     l_fuse = l_curr;

//     c_location->c_poseTwistContainer_.setSlamOptPoseTwist(l_curr);
//     c_location->c_poseTwistContainer_.setFusePoseTwist(l_fuse);
//     c_location->c_poseTwistContainer_.setLastAddMapPoseTwist(l_addMap);
//     EXPECT_EQ(c_location->isUpdateMap_(), false);

//     c_location->c_sConfig_.m_workMode = WorkMode::InitMapMode;
//     EXPECT_EQ(c_location->isUpdateMap_(), false);

//     c_location->c_bIsKeyFrame_ = true;
//     EXPECT_EQ(c_location->isUpdateMap_(), true);

//     c_location->c_sConfig_.m_workMode = WorkMode::ContMapMode;
//     EXPECT_EQ(c_location->isUpdateMap_(), true);

//     c_location->c_sConfig_.m_workMode = WorkMode::UpdateMapMode;
//     EXPECT_EQ(c_location->isUpdateMap_(), true);
// }

// TEST_F(location_test, isLidarDisCon_)
// {
//     int a = 1000, b = 100;

//     EXPECT_EQ(c_location->isLidarDisCon_(a, b), false);

//     c_location->c_bHasInit = true;
//     EXPECT_EQ(c_location->isLidarDisCon_(a, b), false);

//     c_location->c_stSysParam_->m_bIsOnlineMode = true;
//     EXPECT_EQ(c_location->isLidarDisCon_(a, b), false);

//     c_location->c_stSysParam_->m_iWorkMode = WorkMode::LocatMode;
//     EXPECT_EQ(c_location->isLidarDisCon_(a, b), true);
// }

// TEST_F(location_test, lidarDisconFunc)
// {
//     int num = 100;
//     c_location->lidarDisconFunc(num);
// }

// TEST_F(location_test, settingPose)
// {
//     POSE l_pose;
//     l_pose.x = 1.0;
//     l_pose.y = 1.0;
//     l_pose.z = 10.0;
//     c_location->c_pcKeyFramesPose_->points.push_back(l_pose);
//     c_location->c_pSubMaper_->setNearByPath(c_location->c_pcKeyFramesPose_);
//     c_location->stop();
//     EXPECT_EQ(c_location->c_bLocationRun_, false);

//     while (!c_location->isStop())
//     {
//         sleepMs(1);
//     }

//     c_stSysParam_->m_pos.m_stSetPose.m_bFlag = PoseStatus::SettingPose;
//     c_stSysParam_->m_pos.m_stSetPose.setXYZ(1.0, 1.0, 0);
//     c_stSysParam_->m_pos.m_stSetPose.setRPY(0, 0, 90.0);
//     c_location->start();

//     EXPECT_DOUBLE_EQ(c_location->c_stSysParam_->m_pos.m_stSetPose.x(), 1.0);
//     EXPECT_DOUBLE_EQ(c_location->c_stSysParam_->m_pos.m_stSetPose.y(), 1.0);
//     EXPECT_DOUBLE_EQ(c_location->c_stSysParam_->m_pos.m_stSetPose.z(), 10.0);
//     EXPECT_DOUBLE_EQ(c_location->c_stSysParam_->m_pos.m_stSetPose.yaw(), 90.0);
//     EXPECT_EQ(c_location->c_bLocationRun_, true);

//     c_location->c_pKeyFrameOptToBe_.reset(new KeyFrame());

//     c_location->c_pKeyFrameOptToBe_->m_Pose.m_bFlag = PoseStatus::Default;
//     c_location->updateLocalMap_(c_location->c_pKeyFrameOptToBe_->m_Pose);
//     EXPECT_EQ(c_location->c_stSysParam_->m_pos.m_stSetPose.m_bFlag, PoseStatus::SettingPose);

//     c_location->c_pKeyFrameOptToBe_->m_Pose.m_bFlag = PoseStatus::SettingPose;
//     c_location->updateLocalMap_(c_location->c_pKeyFrameOptToBe_->m_Pose);
//     EXPECT_EQ(c_location->c_stSysParam_->m_pos.m_stSetPose.m_bFlag, PoseStatus::Default);
// }

TEST_F(location_test, run)
{
    std::vector<std::string> l_vcorName;
    std::vector<std::string> l_vsurName;
    std::string l_corfile = "/home/<USER>/slam_wsv2.0/output/corn/";
    std::string l_surfile = "/home/<USER>/slam_wsv2.0/output/surf/";
    fillPcdNameList_(l_vcorName, l_corfile);
    fillPcdNameList_(l_vsurName, l_surfile);
    EXPECT_EQ(l_vcorName.size(), l_vsurName.size());

    // 当采样点数小于面点点数时
    for (size_t i = 0; i < l_vcorName.size(); i++)
    {
        KeyFramePtr l_frame(new KeyFrame());
        l_frame->m_pFeature->m_uiScanFrame = 17000 + i;
        l_frame->m_pFeature->m_dTimespan = 100;
        l_frame->m_pFeature->m_dTimestamp = 100 + 100 * i;
        l_frame->m_pFeature->m_iRecvTimestamp = 99 + 100 * i;
        l_frame->m_bIsKeyFrame = true;
        std::string l_cor = "/home/<USER>/slam_wsv2.0/output/corn/" + l_vcorName[i];
        std::string l_sur = "/home/<USER>/slam_wsv2.0/output/surf/" + l_vsurName[i];
        pcl::io::loadPCDFile(l_cor, *l_frame->m_pFeature->first);
        pcl::io::loadPCDFile(l_sur, *l_frame->m_pFeature->second);
        pcl::io::loadPCDFile(l_sur, *l_frame->m_pFeature->third);
        pcl::io::loadPCDFile(l_sur, *l_frame->m_pFeature->fourth);
        pcl::io::loadPCDFile(l_sur, *l_frame->m_pFeature->allPC);
        l_frame->m_pFeature->m_iSample2ndSize = l_frame->m_pFeature->surfSize() - 200;
        c_keyframe.push(l_frame);
    }
    // 当采样点数等于面点点数时
    for (size_t j = 0; j < l_vcorName.size(); j++)
    {
        KeyFramePtr l_frame(new KeyFrame());
        l_frame->m_pFeature->m_uiScanFrame = 17000 + (j + 11);
        l_frame->m_pFeature->m_dTimespan = 100;
        l_frame->m_pFeature->m_dTimestamp = 100 + 100 * (j + 11);
        l_frame->m_pFeature->m_iRecvTimestamp = 99 + 100 * (j + 11);
        l_frame->m_bIsKeyFrame = true;
        std::string l_cor = "/home/<USER>/slam_wsv2.0/output/corn/" + l_vcorName[j];
        std::string l_sur = "/home/<USER>/slam_wsv2.0/output/surf/" + l_vsurName[j];
        pcl::io::loadPCDFile(l_cor, *l_frame->m_pFeature->first);
        pcl::io::loadPCDFile(l_sur, *l_frame->m_pFeature->second);
        pcl::io::loadPCDFile(l_sur, *l_frame->m_pFeature->third);
        pcl::io::loadPCDFile(l_sur, *l_frame->m_pFeature->fourth);
        pcl::io::loadPCDFile(l_sur, *l_frame->m_pFeature->allPC);
        l_frame->m_pFeature->m_iSample2ndSize = l_frame->m_pFeature->surfSize();
        c_keyframe.push(l_frame);
    }
    c_stSysParam_->m_bDebugModel = true;
    c_stSysParam_->m_iWorkMode = WorkMode::InitMapMode;
    c_location->setWorkMode(c_stSysParam_->m_iWorkMode);
    loca = std::thread(&Location<PoinT, PoinT>::run, c_location);
    loca.detach();
    while (!c_keyframe.empty())
    {
        sleepMs(50);
    }
    c_location->shutDown();
}

}  // namespace wj_slam