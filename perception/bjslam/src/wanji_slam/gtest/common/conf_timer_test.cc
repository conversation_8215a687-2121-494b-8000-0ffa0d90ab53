#include "common/config/conf_timer.h"
#include "wj_log.h"
#include <gtest/gtest.h>
#include <thread>
using namespace wj_slam;

WJLog* generalLog::wjlog = nullptr;
// 路径日志
WJLog* generalLog::pathlog = nullptr;
// 校验日志
WJLog* generalLog::checklog = nullptr;
// 速度日志
WJLog* generalLog::speedlog = nullptr;
// 对外日志
WJLog* generalLog::faelog = nullptr;
TEST(CONFIG_TIMER, Instantiation)
{
    generalLog* glog = nullptr;
    glog =
        new generalLog("./", "wj", FileMode::DAILY_ROTATE, LogMode::TRIAL_MODE, 1024 * 1024 * 10);
    sTimeval time(0);
    int cnt = 0;
    std::cout << (cnt++) << std::endl;
    time.getDiffMs(time);
    std::cout << (cnt++) << std::endl;
    time.now();
    std::cout << (cnt++) << std::endl;
    s_TimeConfig con;
    con.setSensorBaseTime(time);
    std::cout << (cnt++) << std::endl;
    con.setSystemBaseTime();
    con.getTimeNowMs();
    std::cout << (cnt++) << std::endl;
    con.isSensorTimeSet();
    std::cout << (cnt++) << std::endl;
    con.timeSource();
    std::cout << (cnt++) << std::endl;
    time.printf("");
    double timMs = con.getTimeNowMs();
    for (int i = 0; i < 100; ++i)
    {
        sTimeval time(0);
        std::cout << WJLog::getWholeSysTime() << std::endl;
        LOGFAE(WERROR,
               "[{}] Get Lidar [{}] RawIMU Data Error, scanId: {}",
               WJLog::getWholeSysTime(),
               "core",
               1);
        LOGFAE(WERROR,
               "[{}] Get IMU Data Error, syncTime: {:.3f}ms, scanId: {}, syncPrecs: {}ms",
               WJLog::getWholeSysTime(),
               timMs,
               1,
               10);
        time.printf("");
    }
}
class s_TimeConfig_Test : public ::testing::Test {
  private:
    // wj_slam::sTimeval sensorTime_;
    double getRand(double edge)
    {
        double out = (double)(rand() - RAND_MAX / 2.0) / RAND_MAX * edge;
        return out;
    }

  public:
    s_TimeConfig c_systemTime_;
    sTimeval FirstTime;
    std::mutex loc;
    void SetUp() override
    {
        srand(time(0));
        c_systemTime_.setSystemBaseTime();
        // sensorTime_ = c_systemTime_.getSystemBaseTime();
        // sTimeval timeTmp;
        // timeTmp.set(100, 10000);
        // timeTmp.set(100, 10000);
        // sensorTime_.printf("sensor time");
    }
    void TearDown() override {}
    void setSensorBaseTime()
    {
        time_t randTime = 100 + getRand(1) * 10;
        time_t randTimeUs = 100000 + getRand(2) * 1000;
        int sleepTime = fabs(20 + getRand(80));
        std::cerr << "start set Sensor Base Time " << c_systemTime_.getTimeNowMs()
                  << " addr: " << randTime << " slepp : " << sleepTime << std::endl;
        usleep(sleepTime);
        sTimeval sensorTime_;
        sensorTime_.set(randTime, randTimeUs);

        std::cerr << "start set Sensor Base Time 000 : " << c_systemTime_.getTimeNowMs()
                  << "addr : " << randTime << std::endl;
        if (!c_systemTime_.isSensorTimeSet())
        {
            c_systemTime_.setSensorBaseTime(sensorTime_);
            FirstTime = sensorTime_;
        }
        sensorTime_.printf("sensor_" + std::to_string(randTime));
        c_systemTime_.getSensorBaseTime().printf("syssensor_" + std::to_string(randTime));
    }
};
TEST_F(s_TimeConfig_Test, timeSetTest)
{
    std::cerr << "start timeSetTest " << c_systemTime_.getTimeNowMs() << std::endl;
    std::thread setST(&s_TimeConfig_Test::setSensorBaseTime, this);
    std::cerr << "start timeSetTest1 " << c_systemTime_.getTimeNowMs() << std::endl;
    std::thread setST1(&s_TimeConfig_Test::setSensorBaseTime, this);
    setST.join();
    setST1.join();
    std::cerr << "test done\n";

    EXPECT_EQ(FirstTime.second() - 10, c_systemTime_.getSensorBaseTime().second());
    EXPECT_EQ(FirstTime.subsecond(), c_systemTime_.getSensorBaseTime().subsecond());
    // EXPECT_NE(FirstTime[1].second() - 10, c_systemTime_.getSensorBaseTime().second());
    // EXPECT_NE(FirstTime[1].subsecond(), c_systemTime_.getSensorBaseTime().subsecond());
}
int main(int argc, char** argv)
{
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}