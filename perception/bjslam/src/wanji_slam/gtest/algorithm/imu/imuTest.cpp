
#include "algorithm/imu/imuFilter.h"
#include <boost/shared_ptr.hpp>
#include <boost/thread.hpp>
#include <fstream>
#include <gtest/gtest.h>
#include <tf/transform_datatypes.h>
#include <unistd.h>
TEST(imuFilter, Instantiation)
{
    wj_slam::imuFilter imu;
}
double getRand(double edge)
{
    double out = (double)(rand() - RAND_MAX / 2.0) / RAND_MAX * edge;
    return out;
}
TEST(imuFilter, stateCheckTest)
{
    wj_slam::imuFilter c_imuFilter_;
    wj_slam::IMUData l_bias;
    l_bias.angularVelocity() << 0.5, -2, 3;
    l_bias.linearAcceleration() << 0, 0, 9.81;
    l_bias.printf("setBias");
    wj_slam::IMUPerScan imuPerData;
    for (int i = 0; i < 500; ++i)
    {
        wj_slam::IMUData imu(l_bias);
        imu.angularVelocity()[0] += getRand(0.005);
        imu.angularVelocity()[1] += getRand(0.007);
        imu.angularVelocity()[2] += getRand(0.002);
        imu.linearAcceleration()[0] += getRand(0.1);
        imuPerData.push_back(imu);
    }
    std::cout << "stateCheckTest imuPerData.size() " << imuPerData.size() << std::endl;
    for (int j = 0; j < imuPerData.size(); ++j)
    {
        c_imuFilter_.calculateBias_(imuPerData.at(j));
        wj_slam::IMUData imu_bias = c_imuFilter_.bias();
        if (j > 300)
        {
            if (imu_bias.isValid())
            {
                imu_bias.printf("stateCheckTest");
                std::cout << "get bias when " << j << std::endl;
                EXPECT_LE(j, 305);
                EXPECT_NEAR(imu_bias.wx(), l_bias.wx(), 0.001);
                EXPECT_NEAR(imu_bias.wy(), l_bias.wy(), 0.001);
                EXPECT_NEAR(imu_bias.wz(), l_bias.wz(), 0.001);
                break;
            }
        }
    }
}
TEST(imuFilter, stateCheckMoveTest)
{
    wj_slam::imuFilter c_imuFilter_;
    wj_slam::IMUData l_bias;
    l_bias.angularVelocity() << 0.5, -2, 3;
    l_bias.linearAcceleration() << 0, 0, 9.81;
    wj_slam::IMUPerScan imuPerData;
    for (int i = 0; i < 200; ++i)
    {
        wj_slam::IMUData imu(l_bias);
        imu.angularVelocity()[0] += getRand(0.005);
        imu.angularVelocity()[1] += getRand(0.007);
        imu.angularVelocity()[2] += getRand(0.002);
        imu.linearAcceleration()[0] += getRand(0.1);
        imuPerData.push_back(imu);
    }
    int moveCnt = 200;
    for (int i = 0; i < moveCnt; ++i)
    {
        wj_slam::IMUData imu(l_bias);
        imu.angularVelocity()[0] += (getRand(0.005) + 0.01);
        imu.angularVelocity()[1] += getRand(0.007);
        imu.angularVelocity()[2] += getRand(0.002);
        imu.linearAcceleration()[0] += getRand(0.1);
        imuPerData.push_back(imu);
    }
    for (int i = 0; i < 200; ++i)
    {
        wj_slam::IMUData imu(l_bias);
        imu.angularVelocity()[0] += getRand(0.005);
        imu.angularVelocity()[1] += getRand(0.007);
        imu.angularVelocity()[2] += getRand(0.002);
        imu.linearAcceleration()[0] += getRand(0.1);
        imuPerData.push_back(imu);
    }
    std::cout << "stateCheckMoveTest imuPerData.size() " << imuPerData.size() << std::endl;
    for (int j = 0; j < imuPerData.size(); ++j)
    {
        c_imuFilter_.calculateBias_(imuPerData.at(j));
        wj_slam::IMUData imu_bias = c_imuFilter_.bias();
        if (imu_bias.isValid())
        {
            imu_bias.printfData("stateCheckMoveTest");
            // std::cout << "get bias when " << j << std::endl;
            EXPECT_GE(j, 300);
            EXPECT_LE(j, 305 + moveCnt);
            EXPECT_NEAR(imu_bias.wx(), l_bias.wx(), 0.001);
            EXPECT_NEAR(imu_bias.wy(), l_bias.wy(), 0.001);
            EXPECT_NEAR(imu_bias.wz(), l_bias.wz(), 0.001);
            break;
        }
    }
}
// TEST(imuFilter, getDataTest)
// {
//     wj_slam::imuFilter c_imuFilter_;
//     wj_slam::IMUData l_bias;
//     l_bias.angularVelocity() << 0.5, -2, 3;
//     l_bias.linearAcceleration() << 0, 0, 9.81;
//     c_imuFilter_.setBias(l_bias);
//     wj_slam::IMUPerScan imuPerData;
//     for (int i = 0; i < 200; ++i)
//     {
//         wj_slam::IMUData imu(l_bias);
//         imu.angularVelocity()[0] += getRand(0.005);
//         imu.angularVelocity()[1] += getRand(0.007);
//         imu.angularVelocity()[2] += getRand(0.002);
//         imu.linearAcceleration()[0] += getRand(0.1);
//         imuPerData.push_back(imu);
//     }
//     int moveCnt = 50;

//     //匀加速
//     wj_slam::IMUData imuMoveJS;
//     for (int i = 0; i < moveCnt; ++i)
//     {
//         imuMoveJS = (l_bias);
//         imuMoveJS.angularVelocity()[0] += getRand(0.005);
//         imuMoveJS.angularVelocity()[1] += getRand(0.007);
//         imuMoveJS.angularVelocity()[2] += (getRand(0.002) + 0.1 * i);
//         imuPerData.push_back(imuMoveJS);
//     }
//     //匀速
//     wj_slam::IMUData imuMoveYS;
//     for (int i = 0; i < 200; ++i)
//     {
//         imuMoveYS = (imuMoveJS);
//         imuMoveYS.angularVelocity()[0] += getRand(0.005);
//         imuMoveYS.angularVelocity()[1] += getRand(0.007);
//         imuMoveYS.angularVelocity()[2] += getRand(0.002);
//         imuPerData.push_back(imuMoveYS);
//     }
//     //匀减速
//     wj_slam::IMUData imuMoveMiS;
//     for (int i = 0; i < moveCnt; ++i)
//     {
//         imuMoveMiS = imuMoveJS;
//         imuMoveMiS.angularVelocity()[0] += getRand(0.005);
//         imuMoveMiS.angularVelocity()[1] += getRand(0.007);
//         imuMoveMiS.angularVelocity()[2] += (getRand(0.002) - 0.1 * i);
//         imuPerData.push_back(imuMoveMiS);
//     }
//     for (int i = 0; i < 200; ++i)
//     {
//         wj_slam::IMUData imu(imuMoveMiS);
//         imu.angularVelocity()[0] += getRand(0.005);
//         imu.angularVelocity()[1] += getRand(0.007);
//         imu.angularVelocity()[2] += getRand(0.002);
//         imu.linearAcceleration()[0] += getRand(0.1);
//         imuPerData.push_back(imu);
//     }
//     std::cout << "stateCheckMoveTest imuPerData.size() " << imuPerData.size() << std::endl;
//     for (int j = 0; j < imuPerData.size(); ++j)
//     {
//         c_imuFilter_.calculateBias_(imuPerData.at(j));
//         wj_slam::IMUData imu_bias = c_imuFilter_.bias();
//         if (imu_bias.isValid())
//         {
//             imu_bias.printfData("stateCheckMoveTest");
//             // std::cout << "get bias when " << j << std::endl;
//             EXPECT_GE(j, 300);
//             EXPECT_LE(j, 305 + moveCnt);
//             EXPECT_NEAR(imu_bias.wx(), l_bias.wx(), 0.001);
//             EXPECT_NEAR(imu_bias.wy(), l_bias.wy(), 0.001);
//             EXPECT_NEAR(imu_bias.wz(), l_bias.wz(), 0.001);
//             break;
//         }
//     }
// }
TEST(IMUPerScan, Instantiation)
{
    wj_slam::IMUPerScan imuPerData;
    wj_slam::IMUData imu;
    imu.linearAcceleration() << 0.1, 0.2, 0.3;
    imu.angularVelocity() << 0.4, 0.5, 0.6;
    EXPECT_EQ(imuPerData.size(), 0);
    EXPECT_EQ(imuPerData.scanId(), -1);
    imuPerData.push_back(imu);
    EXPECT_EQ(imuPerData.size(), 1);
    wj_slam::IMUData imucopy(imuPerData.at(0));
    EXPECT_DOUBLE_EQ(imucopy.ax(), imu.ax());
    EXPECT_DOUBLE_EQ(imucopy.ay(), imu.ay());
    EXPECT_DOUBLE_EQ(imucopy.az(), imu.az());
    EXPECT_DOUBLE_EQ(imucopy.wx(), imu.wx());
    EXPECT_DOUBLE_EQ(imucopy.wy(), imu.wy());
    EXPECT_DOUBLE_EQ(imucopy.wz(), imu.wz());
}
TEST(IMUData, Instantiation)
{
    wj_slam::IMUData imu;
    EXPECT_DOUBLE_EQ(imu.ax(), 0);
    EXPECT_DOUBLE_EQ(imu.ay(), 0);
    EXPECT_DOUBLE_EQ(imu.az(), 0);
    EXPECT_DOUBLE_EQ(imu.wx(), 0);
    EXPECT_DOUBLE_EQ(imu.wy(), 0);
    EXPECT_DOUBLE_EQ(imu.wz(), 0);
    EXPECT_DOUBLE_EQ(imu.imuTime(), -1);
    EXPECT_DOUBLE_EQ(imu.syncTime(), -1);
    EXPECT_DOUBLE_EQ(imu.imuRecvTime(), -1);
    EXPECT_EQ(imu.scanId(), -1);
    EXPECT_FLOAT_EQ(imu.temperature(), -1000);
    EXPECT_EQ(imu.isValid(), false);
    EXPECT_EQ(imu.quat().isApprox(Eigen::Quaterniond::Identity()), true);
    imu.linearAcceleration() << 0.1, 0.2, 0.3;
    imu.angularVelocity() << 0.4, 0.5, 0.6;
    EXPECT_NEAR(imu.ax(), 0.1, 0.00001);
    EXPECT_NEAR(imu.ay(), 0.2, 0.00001);
    EXPECT_NEAR(imu.az(), 0.3, 0.00001);
    EXPECT_NEAR(imu.wx(), 0.4, 0.00001);
    EXPECT_NEAR(imu.wy(), 0.5, 0.00001);
    EXPECT_NEAR(imu.wz(), 0.6, 0.00001);
    imu.setValid();
    EXPECT_TRUE(imu.isValid());
    imu.setNoValid();
    EXPECT_FALSE(imu.isValid());
    imu.setValid();
    imu.scanId() = 100;
    imu.temperature() = 23.5;
    Eigen::Quaterniond quat(0.9, 0.1, 0.1, 0.1);
    quat.normalize();
    imu.quat() = quat;
    EXPECT_TRUE(imu.quat().isApprox(quat));
    imu.imuTime() = 100.1;
    imu.syncTime() = 1000.1;
    imu.imuRecvTime() = 2000.1;
    EXPECT_DOUBLE_EQ(imu.imuTime(), 100.1);
    EXPECT_DOUBLE_EQ(imu.syncTime(), 1000.1);
    EXPECT_DOUBLE_EQ(imu.imuRecvTime(), 2000.1);

    // IMUData(const IMUData& data)
    wj_slam::IMUData data(imu);
    EXPECT_DOUBLE_EQ(data.ax(), imu.ax());
    EXPECT_DOUBLE_EQ(data.ay(), imu.ay());
    EXPECT_DOUBLE_EQ(data.az(), imu.az());
    EXPECT_DOUBLE_EQ(data.wx(), imu.wx());
    EXPECT_DOUBLE_EQ(data.wy(), imu.wy());
    EXPECT_DOUBLE_EQ(data.wz(), imu.wz());
    EXPECT_DOUBLE_EQ(imu.imuTime(), data.imuTime());
    EXPECT_DOUBLE_EQ(imu.syncTime(), data.syncTime());
    EXPECT_DOUBLE_EQ(imu.imuRecvTime(), data.imuRecvTime());
    EXPECT_TRUE(data.isValid());
    EXPECT_EQ(data.scanId(), imu.scanId());
    EXPECT_FLOAT_EQ(data.temperature(), imu.temperature());
    EXPECT_TRUE(imu.quat().isApprox(data.quat()));

    // operator=
    wj_slam::IMUData dataOpera;
    dataOpera = imu;
    EXPECT_DOUBLE_EQ(dataOpera.ax(), imu.ax());
    EXPECT_DOUBLE_EQ(dataOpera.ay(), imu.ay());
    EXPECT_DOUBLE_EQ(dataOpera.az(), imu.az());
    EXPECT_DOUBLE_EQ(dataOpera.wx(), imu.wx());
    EXPECT_DOUBLE_EQ(dataOpera.wy(), imu.wy());
    EXPECT_DOUBLE_EQ(dataOpera.wz(), imu.wz());
    EXPECT_DOUBLE_EQ(imu.imuTime(), dataOpera.imuTime());
    EXPECT_DOUBLE_EQ(imu.syncTime(), dataOpera.syncTime());
    EXPECT_DOUBLE_EQ(imu.imuRecvTime(), dataOpera.imuRecvTime());
    EXPECT_TRUE(dataOpera.isValid());
    EXPECT_EQ(dataOpera.scanId(), imu.scanId());
    EXPECT_FLOAT_EQ(dataOpera.temperature(), imu.temperature());
    EXPECT_TRUE(imu.quat().isApprox(dataOpera.quat()));

    // operator -=
    wj_slam::IMUData dataMinusE(imu);
    dataMinusE -= imu;
    EXPECT_DOUBLE_EQ(dataMinusE.wx(), 0);
    EXPECT_DOUBLE_EQ(dataMinusE.wy(), 0);
    EXPECT_DOUBLE_EQ(dataMinusE.wz(), 0);
    EXPECT_DOUBLE_EQ(dataMinusE.ax(), imu.ax());
    EXPECT_DOUBLE_EQ(dataMinusE.ay(), imu.ay());
    EXPECT_DOUBLE_EQ(dataMinusE.az(), imu.az());
    EXPECT_DOUBLE_EQ(imu.imuTime(), dataMinusE.imuTime());
    EXPECT_DOUBLE_EQ(imu.syncTime(), dataMinusE.syncTime());
    EXPECT_DOUBLE_EQ(imu.imuRecvTime(), dataMinusE.imuRecvTime());
    EXPECT_TRUE(dataMinusE.isValid());
    EXPECT_EQ(dataMinusE.scanId(), imu.scanId());
    EXPECT_FLOAT_EQ(dataMinusE.temperature(), imu.temperature());
    EXPECT_TRUE(imu.quat().isApprox(dataMinusE.quat()));

    // operator-
    wj_slam::IMUData dataMinus;
    wj_slam::IMUData imu1(imu);
    dataMinus = imu - imu1;
    EXPECT_DOUBLE_EQ(dataMinus.wx(), 0);
    EXPECT_DOUBLE_EQ(dataMinus.wy(), 0);
    EXPECT_DOUBLE_EQ(dataMinus.wz(), 0);
    EXPECT_DOUBLE_EQ(dataMinus.ax(), imu.ax());
    EXPECT_DOUBLE_EQ(dataMinus.ay(), imu.ay());
    EXPECT_DOUBLE_EQ(dataMinus.az(), imu.az());
    EXPECT_DOUBLE_EQ(imu.imuTime(), dataMinus.imuTime());
    EXPECT_DOUBLE_EQ(imu.syncTime(), dataMinus.syncTime());
    EXPECT_DOUBLE_EQ(imu.imuRecvTime(), dataMinus.imuRecvTime());
    EXPECT_TRUE(dataMinus.isValid());
    EXPECT_EQ(dataMinus.scanId(), imu.scanId());
    EXPECT_FLOAT_EQ(dataMinus.temperature(), imu.temperature());
    EXPECT_TRUE(imu.quat().isApprox(dataMinus.quat()));
}

class imuTest : public testing::Test {
  public:
    boost::shared_ptr<wj_slam::imuFilter> c_imuFilter_;
    std::vector<wj_slam::IMUPerScan> c_vImuPerScan_;
    wj_slam::IMUPerScan c_generateData_;
    wj_slam::IMUPerScan c_getData_;
    wj_slam::IMUPerScan c_getDataRaw_;
    wj_slam::IMUData c_bias_;
    int c_loadDataLen = 0;

    void loadFile(const std::string& file)
    {
        std::ifstream l_file;
        l_file.open(file.c_str());
        if (!l_file)
        {
            std::cout << "file load error : " << file << "\n";
            return;
        }
        std::string str;
        unsigned int ie = 0;
        bool newLine = false;
        boost::shared_ptr<wj_slam::IMUPerScan> imuperScanPtr(new wj_slam::IMUPerScan());
        int scanIdLast = 0;
        while (true)
        {
            double data[11] = {0};
            getline(l_file, str);
            if (l_file.fail())
                break;
            char* p = strtok((char*)str.data(), ",");  // 逗号分隔依次取出
            // ie = 0;
            newLine = true;
            while (p != NULL)
            {
                sscanf(p, "%lf", data + ((ie) % 11));  // char ---> float
                ie++;
                p = strtok(NULL, ",");
            }

            if (ie % 11 != 0)
            {
                std::cout << "data error! " << ie << "\n";
                continue;
            }
            wj_slam::IMUData imudata;
            // std::cout << "input data \n";
            int index = -1;
            imudata.syncTime() = data[++index];
            imudata.imuTime() = data[++index];
            imudata.imuRecvTime() = data[++index];
            imudata.scanId() = data[++index];
            imudata.temperature() = data[++index];
            imudata.angularVelocity() << data[5], data[6], data[7];
            imudata.linearAcceleration() << data[8], data[9], data[10];
            c_loadDataLen++;
            // imudata.printfData();
            if (11 == ie)
            {
                scanIdLast = imudata.scanId();
            }
            if (scanIdLast != imudata.scanId())
            {
                // std::cout << "scanChange : " << scanIdLast << "-->" << imudata.scanId() << ","
                //           << imuperScanPtr->size() << std::endl;
                scanIdLast = imudata.scanId();
                c_vImuPerScan_.push_back(*imuperScanPtr);
                imuperScanPtr.reset(new wj_slam::IMUPerScan());
            }
            else
                imuperScanPtr->push_back(imudata);
        }
        l_file.close();
        std::cout << "load File done. Imu data size : " << c_vImuPerScan_.size() << ", " << ie
                  << std::endl;
    }

  public:
    void SetUp() override
    {
        srand(time(0));
        c_imuFilter_.reset(new wj_slam::imuFilter());
    }
    void TearDown() override
    {
        c_imuFilter_ = nullptr;
    }
    void getImuData()
    {
        usleep(100000);
        std::cout << "**************start get data\n";
        for (int i = 0; i < c_generateData_.size(); i = i + 10)
        {
            usleep(100000);
            wj_slam::IMUData& imuPer = c_generateData_.at(i);
            wj_slam::IMUData l_imuData;
            Eigen::Vector3d l_angle;
            c_imuFilter_->getIMUData(l_imuData, imuPer.syncTime());
            if (l_imuData.isValid())
            {
                std::cout << "\n";
                l_imuData.printf("find data");
                c_getDataRaw_.push_back(l_imuData);
                if (c_imuFilter_->getAngularTwist(l_angle, imuPer.syncTime() + 100, 10))
                {
                    l_imuData.angularVelocity() = l_angle;

                    l_imuData.printf("correct data");
                    c_getData_.push_back(l_imuData);
                }
            }
            else
            {
                std::cout << "get imudata wrong " << i << std::endl;
            }
        }
        std::cout << "*********start get end\n";
    }
    void generateData()
    {
        int dataCnt = 0;
        c_bias_.angularVelocity() << getRand(2), getRand(3), getRand(2);
        c_bias_.linearAcceleration() << 0, 0, 9.81;
        c_imuFilter_->setBias(c_bias_);
        for (int i = 0; i < 200; ++i)
        {
            wj_slam::IMUData imu(c_bias_);
            imu.angularVelocity()[0] += getRand(0.005);
            imu.angularVelocity()[1] += getRand(0.007);
            imu.angularVelocity()[2] += getRand(0.002);
            imu.linearAcceleration()[0] += getRand(0.1);
            imu.imuTime() = dataCnt * 10.0;
            imu.syncTime() = 10000 + dataCnt * 10.0;
            imu.imuRecvTime() = dataCnt * 10.0;
            dataCnt++;
            c_generateData_.push_back(imu);
        }
        int moveCnt = 200;

        //匀加速
        wj_slam::IMUData imuMoveJS;
        for (int i = 0; i < moveCnt; ++i)
        {
            imuMoveJS = (c_bias_);
            imuMoveJS.angularVelocity()[0] += (getRand(0.005) /*  - 0.005 * i */);
            imuMoveJS.angularVelocity()[1] += (getRand(0.007) /* - 0.001 * i */);
            imuMoveJS.angularVelocity()[2] += (getRand(0.002) - 0.01 * i);
            imuMoveJS.imuTime() = dataCnt * 10.0;
            imuMoveJS.syncTime() = 10000 + dataCnt * 10.0;
            imuMoveJS.imuRecvTime() = dataCnt * 10.0;
            dataCnt++;
            c_generateData_.push_back(imuMoveJS);
        }
        //匀速
        wj_slam::IMUData imuMoveYS;
        for (int i = 0; i < moveCnt; ++i)
        {
            imuMoveYS = (imuMoveJS);
            imuMoveYS.angularVelocity()[0] += getRand(0.005);
            imuMoveYS.angularVelocity()[1] += getRand(0.007);
            imuMoveYS.angularVelocity()[2] += getRand(0.002);
            imuMoveYS.imuTime() = dataCnt * 10.0;
            imuMoveYS.syncTime() = 10000 + dataCnt * 10.0;
            imuMoveYS.imuRecvTime() = dataCnt * 10.0;
            dataCnt++;
            c_generateData_.push_back(imuMoveYS);
        }
        //匀减速
        wj_slam::IMUData imuMoveMiS;
        for (int i = 0; i < moveCnt; ++i)
        {
            imuMoveMiS = imuMoveJS;
            imuMoveMiS.angularVelocity()[0] += (getRand(0.005) /* + 0.005 * i */);
            imuMoveMiS.angularVelocity()[1] += (getRand(0.007) /* + 0.001 * i */);
            imuMoveMiS.angularVelocity()[2] += (getRand(0.002) + 0.01 * i);
            imuMoveMiS.imuTime() = dataCnt * 10.0;
            imuMoveMiS.syncTime() = 10000 + dataCnt * 10.0;
            imuMoveMiS.imuRecvTime() = dataCnt * 10.0;
            dataCnt++;
            c_generateData_.push_back(imuMoveMiS);
        }
        for (int i = 0; i < 200; ++i)
        {
            wj_slam::IMUData imu(imuMoveMiS);
            imu.angularVelocity()[0] += getRand(0.005);
            imu.angularVelocity()[1] += getRand(0.007);
            imu.angularVelocity()[2] += getRand(0.002);
            imu.linearAcceleration()[0] += getRand(0.1);
            imu.imuTime() = dataCnt * 10.0;
            imu.syncTime() = 10000 + dataCnt * 10.0;
            imu.imuRecvTime() = dataCnt * 10.0;
            dataCnt++;
            c_generateData_.push_back(imu);
        }
    }
};

TEST_F(imuTest, inputRawDataTest)
{
    std::cout << "load File strat ...\n";
    char buff[FILENAME_MAX];
    getcwd(buff, FILENAME_MAX);
    std::string path(buff);
    path = path + "/src/wanji_slam/gtest/algorithm/imu/imu.csv";
    std::cout << "path : " << path << std::endl;
    loadFile(path);
    int imudataCnt = 0;
    for (int i = 0; i < c_vImuPerScan_.size(); ++i)
    {
        wj_slam::IMUPerScan& imuPer = c_vImuPerScan_[i];
        wj_slam::IMUData imu, imu1, imu2, imu3, imu4;
        for (int j = 0; j < imuPer.size(); ++j)
        {
            c_imuFilter_->inputRawData(imuPer.at(j));
            imudataCnt++;
            c_imuFilter_->getIMUData(imu, imuPer.at(j).syncTime(), 1, false);
            c_imuFilter_->getIMUData(imu1, imuPer.at(j).syncTime(), 1, false);
            c_imuFilter_->getIMUData(imu2, imuPer.at(j).syncTime(), 1, false);
            c_imuFilter_->getIMUData(imu3, imuPer.at(j).syncTime(), 1, false);
            c_imuFilter_->getIMUData(imu4, imuPer.at(j).syncTime(), 1, true);
            // imu.printf("filter " + std::to_string(imudataCnt));
            if (imudataCnt <= 300)
            {
                EXPECT_EQ(imu.isValid(), false) << " imu is initing ";
            }
            else
            {
                wj_slam::IMUData imu_bias = c_imuFilter_->bias();
                if (imu_bias.isValid())
                {
                    EXPECT_NEAR(imu_bias.wx(), 0.01292, 0.0001) << " imu is initing ";
                    EXPECT_NEAR(imu_bias.wy(), 0.00002283, 0.0001) << " imu is initing ";
                    EXPECT_NEAR(imu_bias.wz(), -0.01574, 0.0001) << " imu is initing ";
                    imu_bias.printf("inputRawDataTest");
                    std::cout << "get bias when " << imudataCnt << std::endl;
                    // break;
                }
                if (imu.isValid())
                {
                    EXPECT_NEAR(imu_bias.wx(), 0.01292, 0.0001) << " imu is initing ";
                    EXPECT_NEAR(imu_bias.wy(), 0.00002283, 0.0001) << " imu is initing ";
                    EXPECT_NEAR(imu_bias.wz(), -0.01574, 0.0001) << " imu is initing ";

                    EXPECT_EQ(imu.isValid(), true);
                    EXPECT_EQ(imu1.isValid(), true);
                    EXPECT_EQ(imu2.isValid(), true);
                    EXPECT_EQ(imu3.isValid(), true);
                    EXPECT_EQ(imu4.isValid(), true);
                    std::cout << "get imu when " << imudataCnt << std::endl;
                    return;
                }
            }
        }
    }
}
/**
 * @brief Construct a new test f object
 * @attention 该模块需要人工校验
 *
 */
TEST_F(imuTest, getDataTest)
{
    int imudataCnt = 0;
    int imuRawDataCnt = 0;
    generateData();

    boost::thread getThr(&imuTest::getImuData, this);
    std::cout << "*********************start input data\n";
    c_imuFilter_->bias().printf("bias");
    for (int i = 0; i < c_generateData_.size(); ++i)
    {
        usleep(10000);
        c_imuFilter_->inputRawData(c_generateData_.at(i));
        // c_generateData_.at(i).printf("ge");
        imuRawDataCnt++;
        if (c_imuFilter_->bias().isValid())
        {
            // std::cout << "get bias valid when " << imuRawDataCnt << std::endl;
            imudataCnt++;
            int dataLen = c_imuFilter_->size();
            if (imudataCnt > 500)
                EXPECT_EQ(dataLen, 500);
            else
                EXPECT_EQ(dataLen, imudataCnt);
        }
    }
    if (getThr.joinable())
        getThr.join();
    for (int i = 0; i < c_generateData_.size(); ++i)
    {
        c_generateData_.at(i).printfData("gen");
    }
    for (int i = 0; i < c_getData_.size(); ++i)
    {
        c_getData_.at(i).printfData("find");
    }
    for (int i = 0; i < c_getDataRaw_.size(); ++i)
    {
        c_getDataRaw_.at(i).printfData("rawfind");
    }

    std::cout << "******************input data end\n";
}
int main(int argc, char** argv)
{
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}