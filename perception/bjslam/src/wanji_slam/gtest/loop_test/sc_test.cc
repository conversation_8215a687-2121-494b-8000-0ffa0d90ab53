#include <Eigen/Dense>
#include <gtest/gtest.h>
#include <opencv2/highgui/highgui.hpp>
#include <opencv2/imgcodecs.hpp>
//测试调用函数
#include "../../include/algorithm/loop/placeRecongnize/impl/placeRecongnize.hpp"
#include "../../include/algorithm/loop/placeRecongnize/placeRecongnize.h"

// pcl库
#include <pcl/common/transforms.h>
#include <pcl/io/pcd_io.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
// icp
#include <iostream>
#include <pcl/io/pcd_io.h>
#include <pcl/point_types.h>
#include <pcl/registration/icp.h>

// LOOP 2 MAP _ ALIGN
#include "../../include/algorithm/loop/loopCheck/loop2map_align.hpp"

// log
#include "../../test/param.hpp"
generalLog* glog = nullptr;
WJLog* generalLog::wjlog = nullptr;
// 路径日志
WJLog* generalLog::pathlog = nullptr;
// 校验日志
WJLog* generalLog::checklog = nullptr;
// 速度日志
WJLog* generalLog::speedlog = nullptr;
// 对外日志
WJLog* generalLog::faelog = nullptr;

class ScanContex : public testing::Test {
  protected:
    using PointT = pcl::PointXYZHSV;
    using PointCloudT = pcl::PointCloud<PointT>;
    using FeaturePair = FEATURE_PAIR<PointT>;         /**< 输入特征组类型 */
    using FeaturePairPtr = typename FeaturePair::Ptr; /**< 输入特征组指针类型 */

    boost::shared_ptr<wj_slam::PlaceRecongnize> sc_caculer;
    boost::shared_ptr<wj_slam::L2M_Align<PointT, PointT>> c_pL2M_;

    SYSPARAM* c_stSysParam_;
    boost::shared_ptr<LaserRegistration<PointT, PointT>> c_pMatcher_;

    std::string l_sSc_MainFile = "/home/<USER>/data/lrz_ws/src/wanji_slam/data/Map/big_circle/KF/";
    std::string l_sSc_saveFile = "/home/<USER>/data/lrz_ws/src/wanji_slam/data/Map/loop_gtest/";
    std::string l_sName_sc = "/sc.tif";
    std::string l_sName_pose = "/pose.csv";
   
    virtual void SetUp()
    {
        c_stSysParam_ = SYSPARAM::getIn();
        sc_caculer.reset(new wj_slam::PlaceRecongnize);
        // std::cout<<"回环sc测试:"<<l_id_1.c_str() < "and" <<  l_id_2.c_str() << std::endl;
        c_pL2M_.reset(new wj_slam::L2M_Align<PointT, PointT>);
        c_pMatcher_.reset(new LaserRegistration<PointT, PointT>());

        wj_slam::s_masterCfg* g_masterParamPtr;
        g_masterParamPtr = wj_slam::s_masterCfg::getIn();
        wj_slam::Param l_param(false);
        std::string path = "/home/<USER>/data/lrz_ws/src/wanji_slam";
        l_param.loadSysParam(path);
        printf("pgk Path: %s\n", g_masterParamPtr->m_slam->m_sPkgPath.c_str());

        // 挂载日志模块
        glog = new generalLog(g_masterParamPtr->m_slam->m_fae.m_sLogPath,
                              "wj",
                              FileMode::DAILY_ROTATE,
                              LogMode::TRIAL_MODE,
                              g_masterParamPtr->m_slam->m_fae.getLogFileSizeByte());

        glog->changeMode(g_masterParamPtr->m_slam->m_fae.m_iLogLevel);
    }
    virtual void TearDown() {}

    //加载scan-contex保存的文件
    void readImageToMatrix_(std::string p_sFilePath, Eigen::MatrixXd& matrix)
    {
        cv::Mat image = cv::imread(p_sFilePath, cv::IMREAD_UNCHANGED);  //按照原格式读取

        if (!image.empty())
        {
            int rows = image.rows;
            int cols = image.cols;

            matrix.resize(rows, cols);

            for (int y = 0; y < rows; ++y)
            {
                for (int x = 0; x < cols; ++x)
                {
                    matrix(y, x) = static_cast<double>(image.at<float>(y, x));
                }
            }
        }
        else
        {
            // Handle error: Unable to read the image file
            // You might throw an exception or set an error flag here
        }
    }

    void writeOneScanContext_(std::string p_sFilePath, Eigen::MatrixXd& matrix)
    {
        int rows = matrix.rows();
        int cols = matrix.cols();
        cv::Mat image = cv::Mat::zeros(rows, cols, CV_32FC1);

        for (int y = 0; y < rows; ++y)
        {
            for (int x = 0; x < cols; ++x)
            {
                // get pixel
                image.at<float>(y, x) = matrix(y, x);
            }
        }
        if (!image.empty())
            cv::imwrite(p_sFilePath, image);
    }

    void rotatePointCloud(PointCloudT::Ptr cloud_in,
                          PointCloudT::Ptr cloud_out,
                          double angle_degrees,
                          double translation_x,
                          double translation_y)
    {
        // Convert angle from degrees to radians
        double angle_radians = angle_degrees * M_PI / 180.0;

        // Define rotation matrix
        Eigen::Affine3f transform_matrix = Eigen::Affine3f::Identity();
        transform_matrix.rotate(Eigen::AngleAxisf(angle_radians, Eigen::Vector3f::UnitZ()));
        transform_matrix.translation() << translation_x, translation_y, 0.0;

        // Apply rotation to the input point cloud

        pcl::transformPointCloud(*cloud_in, *cloud_out, transform_matrix);
    }

    float xy2theta(const float& _x, const float& _y)
    {
        if (_x >= 0 & _y >= 0)
            return (180 / M_PI) * atan(_y / _x);

        if (_x < 0 & _y >= 0)
            return 180 - ((180 / M_PI) * atan(_y / (-_x)));

        if (_x < 0 & _y < 0)
            return 180 + ((180 / M_PI) * atan(_y / _x));

        if (_x >= 0 & _y < 0)
            return 360 - ((180 / M_PI) * atan((-_y) / _x));
    }  // xy2theta

    int PC_NUM_RING =   c_stSysParam_->m_loop.m_iRing;
    int PC_NUM_SECTOR = c_stSysParam_->m_loop.m_iSector;
    double PC_MAX_RADIUS = c_stSysParam_->m_loop.m_dRadius;
    Eigen::MatrixXd makeScancontext(pcl::PointCloud<PointT>& _scan_down)
    {
        //这帧点云的点的数量
        int num_pts_scan_down = _scan_down.points.size();

        // Step 1. 创建bin的矩阵，并把每个bin内点云最大高度初始化为-1000
        // main
        const int NO_POINT =
            -1000;  // 标记格子中是否有点，如果没有点高度设置成-1000, 是一个肯定没有的值
        //; ring行、sector列的矩阵，和论文中一致
        Eigen::MatrixXd desc = NO_POINT * Eigen::MatrixXd::Ones(PC_NUM_RING, PC_NUM_SECTOR);

        PointT pt;
        float azim_angle, azim_range;  // wihtin 2d plane
        int ring_idx, sctor_idx;
        // Step 2. 遍历每个点，往bin中赋值点云最大高度
        for (int pt_idx = 0; pt_idx < num_pts_scan_down; pt_idx++)
        {
            pt.x = _scan_down.points[pt_idx].x;
            pt.y = _scan_down.points[pt_idx].y;
            //! 疑问：这里把所有的高度+2，让高度>0，为什么要这么做？
            //; 解答：目前感觉就是对于地面机器人这种场景，安装高度不变，然后把安装高度加上去之后，
            //;      让安装高度之下的点云的高度从负数也都变成正数
            pt.z =
                _scan_down.points[pt_idx].z + 2;  // naive adding is ok (all points should be > 0).

            // xyz to ring, sector
            azim_range = sqrt(pt.x * pt.x + pt.y * pt.y);  // 距离
            azim_angle = xy2theta(pt.x, pt.y);             // 角度

            // if range is out of roi, pass
            //; 距离超过50米的点不考虑
            if (azim_range > PC_MAX_RADIUS || pt.z < 0)
                continue;

            //;
            //计算这个点落到那个bin中，下标从1开始数。注意下面先min再max，其实就是把结果限制到1~PC_NUM之间
            ring_idx = std::max(
                std::min(PC_NUM_RING, int(ceil((azim_range / PC_MAX_RADIUS) * PC_NUM_RING))), 1);
            sctor_idx = std::max(
                std::min(PC_NUM_SECTOR, int(ceil((azim_angle / 360.0) * PC_NUM_SECTOR))), 1);

            // taking maximum z
            //; 用z值，也就是高度来更新这个格子，存最大的高度。
            //; 注意之这里为什么-1，以为数组的索引从0开始，上面的索引是[1,
            // PC_NUM]，而在编程中数组的索引应该是[0, PC_NUM-1]
            if (desc(ring_idx - 1, sctor_idx - 1) < pt.z)  // -1 means cpp starts from 0
                desc(ring_idx - 1, sctor_idx - 1) =
                    pt.z;  // update for taking maximum value at that bin
        }

        // Step 3. 把bin中没有点的那些，高度设置成0
        // reset no points to zero (for cosine dist later)

        for (int row_idx = 0; row_idx < desc.rows(); row_idx++)
            for (int col_idx = 0; col_idx < desc.cols(); col_idx++)
                if (desc(row_idx, col_idx) == NO_POINT)
                    desc(row_idx, col_idx) = 0;

        return desc;
    }  // SCManager::makeScancontext

    void readOneFrame(std::string p_sFilePath, FeaturePairPtr p_pPCs, s_POSE6D& p_pose)
    {
        static int i = 0;
        std::string c_sAl = "all.pcd";    /**< 全部点云文件名 */
        std::string c_sFi = "fi.pcd";     /**< 角点点云文件名 */
        std::string c_sSe = "se.pcd";     /**< 面点点云文件名 */
        std::string c_sMark = "mark.pcd"; /**< 面点点云文件名 */
        std::string c_sCu = "curb.pcd";   /**< 中线点云文件名 */
        std::string c_sPt = "p.pcd";      /**< 路径点云文件名 */
        std::string c_sPs = "pose.csv";   /**< 位姿及信息文件名 */
        std::string c_sSc = "sc.tif";     /**< 描述符图片文件名 */

        // std::cout << "read from " + p_sFilePath << std::endl;
        std::string l_sAl = p_sFilePath + "/" + c_sAl;
        std::string l_sFi = p_sFilePath + "/" + c_sFi;
        std::string l_sSe = p_sFilePath + "/" + c_sSe;
        std::string l_sCu = p_sFilePath + "/" + c_sCu;
        std::string l_sPs = p_sFilePath + "/" + c_sPs;
        std::string l_sSc = p_sFilePath + "/" + c_sSc;

        // pose.csv
        std::ifstream l_Psfile(l_sPs.c_str());
        if (!l_Psfile)
            std::cout << "Error File" << l_sPs.c_str() << std::endl;
        else
        {
            double p_dData[7];
            int p_iData[4];
            std::string str;
            // 第一行
            getline(l_Psfile, str);
            // 逗号分隔依次取出
            char* p = strtok((char*)str.data(), ",");
            //  顺序 "x,y,z,roll,pitch,yaw,m_fPercent,id,time,sampleSize,m_bHasContext"
            for (int ie = 0; ie < 7; ++ie)
            {
                p_dData[ie] = atof(p);
                // std::cout << p_dData[ie] << " ";
                p = strtok(NULL, ",");
            }
            for (int ie = 7; ie < 11; ++ie)
            {
                p_iData[ie - 7] = atoi(p);
                // std::cout << p_iData[ie - 7] << " ";
                p = strtok(NULL, ",");
            }
            l_Psfile.close();
            std::cout << std::endl;

            // 填写
            p_pose.m_trans << p_dData[0], p_dData[1], p_dData[2];

            p_pose.setRPY(p_dData[3], p_dData[4], p_dData[5]);

            p_pose.m_fPercent = p_dData[6];

            p_pPCs->m_uiScanFrame = (uint)p_iData[0];

            p_pPCs->m_dTimestamp = p_iData[1];

            p_pPCs->m_iSample2ndSize = p_iData[2];

            p_pPCs->m_bHasContext = p_iData[3];
        }
        // 4x .pcd

        pcl::io::loadPCDFile<PointT>(l_sAl, *p_pPCs->allPC);

        pcl::io::loadPCDFile<PointT>(l_sFi, *p_pPCs->first);

        pcl::io::loadPCDFile<PointT>(l_sSe, *p_pPCs->second);

        // pcl::io::loadPCDFile<PointT>(l_sCu, *p_pPCs->fourth);
        // if (p_pPCs->m_bHasContext)
        {
            readImageToMatrix_(l_sSc, *p_pPCs->sc);
        }
    }
};

// TEST_F(ScanContex, SC_SHIFT)
// {
//     Eigen::MatrixXd l_matrix_1;
//     Eigen::MatrixXd l_matrix_2;

//     readImageToMatrix_(l_sSc_MainFile + l_id_1 + l_sName_sc, l_matrix_1);
//     readImageToMatrix_(l_sSc_MainFile + l_id_2 + l_sName_sc, l_matrix_2);

//     auto dist_shift = sc_caculer->distanceBtnScanContext(l_matrix_1, l_matrix_2);
//     std::cout << dist_shift.first << std::endl;
//     std::cout << dist_shift.second << std::endl;

//     pcl::PointCloud<pcl::PointXYZHSV>::Ptr l_allpc(new pcl::PointCloud<pcl::PointXYZHSV>());
//     pcl::PointCloud<pcl::PointXYZHSV>::Ptr l_allpc_rot(new pcl::PointCloud<pcl::PointXYZHSV>());
//     pcl::io::loadPCDFile<pcl::PointXYZHSV>(l_sSc_MainFile + l_id_2 + "/all.pcd", *(l_allpc));

//     double l_dRotang = dist_shift.second / 60.0 * 360.0;

//     std::cout << l_dRotang << std::endl;
//     // rotatePointCloud(l_allpc, l_allpc_rot, -l_dRotang, 0, 0);

//     // pcl::io::savePCDFile<pcl::PointXYZHSV>(l_sSc_MainFile + l_id_2 + "/all_rot.pcd",
//     //                                        *(l_allpc_rot));
// }

TEST_F(ScanContex, _SC_MAKE_)
{
    int l_iMapId = c_stSysParam_->m_loop.mapid;
    int l_iCurId = c_stSysParam_->m_loop.curid;
    pcl::PointCloud<pcl::PointXYZHSV>::Ptr l_allpc(new pcl::PointCloud<pcl::PointXYZHSV>());
    pcl::io::loadPCDFile<pcl::PointXYZHSV>(l_sSc_MainFile + std::to_string(l_iCurId) + "/all.pcd", *(l_allpc));
    pcl::PointCloud<pcl::PointXYZHSV>::Ptr l_allpc2(new pcl::PointCloud<pcl::PointXYZHSV>());
    pcl::io::loadPCDFile<pcl::PointXYZHSV>(l_sSc_MainFile +  std::to_string(l_iMapId) + "/all.pcd", *(l_allpc2));

    Eigen::MatrixXd newsc = makeScancontext(*l_allpc);
    Eigen::MatrixXd newsc2 = makeScancontext(*l_allpc2);
    auto dist_shift = sc_caculer->distanceBtnScanContext(newsc, newsc2);
    std::cout << dist_shift.first << std::endl;
    std::cout << dist_shift.second << std::endl;
   
      std::cout << "SC得分:"<<1- dist_shift.first << std::endl;

    // double l_dRotang = dist_shift.second / float(c_stSysParam_->m_loop.m_iSector) * 360.0;
    // std::cout << l_dRotang << std::endl;


}
// //源码对两同一云与cjz提取出来的是一致的,只不过是反向
// TEST_F(ScanContex, _SC_contrast_)
// {
//     pcl::PointCloud<pcl::PointXYZHSV>::Ptr l_allpc(new pcl::PointCloud<pcl::PointXYZHSV>());
//     pcl::io::loadPCDFile<pcl::PointXYZHSV>(l_sSc_MainFile + l_id_1 + "/all.pcd", *(l_allpc));

//     //源码提取与cjz提取做对比
//     Eigen::MatrixXd newsc = makeScancontext(*l_allpc);
//     Eigen::MatrixXd newsc2;
//     readImageToMatrix_(l_sSc_MainFile + l_id_1 + l_sName_sc, newsc2);

//     auto dist_shift = sc_caculer->distanceBtnScanContext(newsc, newsc2);
//     std::cout << dist_shift.first << std::endl;
//     std::cout << dist_shift.second << std::endl;
//     double l_dRotang = dist_shift.second / 60.0 * 360.0;
//     std::cout << l_dRotang << std::endl;
//     // 180
// }

// TEST_F(ScanContex, _SC_contrast2_)
// {
//     pcl::PointCloud<pcl::PointXYZHSV>::Ptr l_allpc(new pcl::PointCloud<pcl::PointXYZHSV>());
//     pcl::io::loadPCDFile<pcl::PointXYZHSV>(l_sSc_MainFile + l_id_2 + "/all.pcd", *(l_allpc));

//     //源码提取与cjz提取做对比
//     Eigen::MatrixXd newsc = makeScancontext(*l_allpc);
//     Eigen::MatrixXd newsc2;
//     readImageToMatrix_(l_sSc_MainFile + l_id_2 + l_sName_sc, newsc2);

//     auto dist_shift = sc_caculer->distanceBtnScanContext(newsc, newsc2);
//     std::cout << dist_shift.first << std::endl;
//     std::cout << dist_shift.second << std::endl;
//     double l_dRotang = dist_shift.second / 60.0 * 360.0;
//     std::cout << l_dRotang << std::endl;
//     // 180
// }

TEST_F(ScanContex, _SC_contrast_align_world)
{
    int l_iMapID = atoi(l_id_1.c_str());
    std::vector<FeaturePairPtr> l_vFearture_map(1);
    int j = 0;
    s_POSE6D l_MapPose;
    for (size_t i = l_iMapID - 0; i < l_iMapID + 1; i++)
    {
        s_POSE6D l_pose;
        std::string path = l_sSc_MainFile + std::to_string(i);
        l_vFearture_map[j].reset(new FEATURE_PAIR<PointT>());
        readOneFrame(path, l_vFearture_map[j], l_pose);
        j++;
        if (l_iMapID == i)
            l_MapPose = l_pose;
    }
    // l_MapPose.printf();
    wj_slam::KEYFRAME<PointT>::Ptr l_KFMap(new wj_slam::KEYFRAME<PointT>());
    for (auto p : l_vFearture_map)
    {
        *(l_KFMap->m_pFeature->first) += *(p->first);
        *(l_KFMap->m_pFeature->second) += *(p->second);
    }

    s_POSE6D l_CurPose;
    wj_slam::KEYFRAME<PointT>::Ptr l_KFCur(new wj_slam::KEYFRAME<PointT>());
    readOneFrame(l_sSc_MainFile + l_id_2, l_KFCur->m_pFeature, l_CurPose);
    std::cout << "原始位姿:" << std::endl;
    l_CurPose.printf();

    wj_slam::KEYFRAME<PointT>::Ptr l_KFCur_tran(new wj_slam::KEYFRAME<PointT>());
    s_POSE6D trans_all;
    bool res = false;
    // for (size_t i = 0; i < 100; i++)
    {
        s_POSE6D pose;
        c_pL2M_->setInputSource(l_KFCur->m_pFeature, pose, true);
        c_pL2M_->setInputTarget(l_KFMap->m_pFeature);

        res = c_pL2M_->align();

        if (res)
        {
            s_POSE6D tran1 = c_pL2M_->getFinalTransformation();

            trans_all = tran1 * trans_all;
            c_pL2M_->c_trans.transformCloudPoints(tran1.m_quat,
                                                  tran1.m_trans,
                                                  l_KFCur->m_pFeature->first,
                                                  l_KFCur->m_pFeature->first);
            c_pL2M_->c_trans.transformCloudPoints(tran1.m_quat,
                                                  tran1.m_trans,
                                                  l_KFCur->m_pFeature->second,
                                                  l_KFCur->m_pFeature->second);
        }
        else
        {
            // std::cout << "匹配失败" << std::endl;
        }
    }
    std::cout << "偏转:" << std::endl;
    trans_all.printf();
    if (res)
    {
        s_POSE6D pose_new;
        pose_new = trans_all * l_CurPose;
        std::cout << "偏转后位姿:" << std::endl;
        pose_new.printf();
        //转到世界坐标系
        c_pL2M_->c_trans.transformCloudPoints(pose_new.m_quat,
                                              pose_new.m_trans,
                                              l_KFCur->m_pFeature->allPC,
                                              l_KFCur->m_pFeature->allPC);

        pcl::io::savePCDFile<pcl::PointXYZHSV>(l_sSc_MainFile + l_id_2
                                                   + "/all_world_new_quanjupipei.pcd",
                                               *(l_KFCur->m_pFeature->allPC));
    }
}

// TEST_F(ScanContex, _SC_contrast_align)
// {
//     int l_iMapID = atoi(l_id_1.c_str());
//     std::vector<FeaturePairPtr> l_vFearture_map(10);
//     int j = 0;
//     s_POSE6D l_MapPose;
//     for (size_t i = l_iMapID - 5; i < l_iMapID + 5; i++)
//     {
//         s_POSE6D l_pose;
//         std::string path = l_sSc_MainFile + std::to_string(i);
//         l_vFearture_map[j].reset(new FEATURE_PAIR<PointT>());
//         readOneFrame(path, l_vFearture_map[j], l_pose);
//         j++;
//         if (l_iMapID == i)
//             l_MapPose = l_pose;
//     }
//     // l_MapPose.printf();
//     wj_slam::KEYFRAME<PointT>::Ptr l_KFMap(new wj_slam::KEYFRAME<PointT>());
//     for (auto p : l_vFearture_map)
//     {
//         *(l_KFMap->m_pFeature->first) += *(p->first);
//         *(l_KFMap->m_pFeature->second) += *(p->second);
//     }

//     s_POSE6D l_CurPose;
//     wj_slam::KEYFRAME<PointT>::Ptr l_KFCur(new wj_slam::KEYFRAME<PointT>());
//     readOneFrame(l_sSc_MainFile + l_id_2, l_KFCur->m_pFeature, l_CurPose);
//     std::cout << "原始位姿:" << std::endl;
//     l_CurPose.printf();

//     //转到局部坐标
//     s_POSE6D l_EstTrans_wto0 = l_MapPose.inverse();

//     c_pL2M_->c_trans.transformCloudPoints(l_EstTrans_wto0.m_quat,
//                                           l_EstTrans_wto0.m_trans,
//                                           l_KFMap->m_pFeature->first,
//                                           l_KFMap->m_pFeature->first);
//     c_pL2M_->c_trans.transformCloudPoints(l_EstTrans_wto0.m_quat,
//                                           l_EstTrans_wto0.m_trans,
//                                           l_KFMap->m_pFeature->second,
//                                           l_KFMap->m_pFeature->second);

//     s_POSE6D l_EstTrans_wto01 = l_CurPose.inverse();

//     c_pL2M_->c_trans.transformCloudPoints(l_EstTrans_wto01.m_quat,
//                                           l_EstTrans_wto01.m_trans,
//                                           l_KFCur->m_pFeature->first,
//                                           l_KFCur->m_pFeature->first);
//     c_pL2M_->c_trans.transformCloudPoints(l_EstTrans_wto01.m_quat,
//                                           l_EstTrans_wto01.m_trans,
//                                           l_KFCur->m_pFeature->second,
//                                           l_KFCur->m_pFeature->second);

//     // //检查:这个转换对不对
//     // pcl::io::savePCDFile<pcl::PointXYZHSV>(l_sSc_MainFile + l_id_2 + "/second_to0.pcd",
//     //                                        *(l_KFCur->m_pFeature->second));

//     pcl::io::savePCDFile<pcl::PointXYZHSV>(l_sSc_saveFile + l_id_1 + "_scmap.pcd",
//                                            *(l_KFMap->m_pFeature->second));

//     // //已验证 正确

//     // //此时:所有点云已经移动至局部坐标

//     // /*step1:求sc偏转*/
//     Eigen::MatrixXd l_matrix_1;
//     Eigen::MatrixXd l_matrix_2;
//     readImageToMatrix_(l_sSc_MainFile + l_id_1 + l_sName_sc, l_matrix_1);
//     readImageToMatrix_(l_sSc_MainFile + l_id_2 + l_sName_sc, l_matrix_2);
//     auto dist_shift = sc_caculer->distanceBtnScanContext(l_matrix_1, l_matrix_2);
//     if (dist_shift.first < 0.3)
//     {
//         double l_dRotang = dist_shift.second / 60.0 * 360.0;
//         std::cout<< "SC修正旋转角度" << l_dRotang <<std::endl;
//         s_POSE6D tran0;
//         tran0.setRPY(0.0, 0.0, -l_dRotang);

//         c_pL2M_->c_trans.transformCloudPoints(
//             tran0.m_quat, tran0.m_trans, l_KFCur->m_pFeature->first, l_KFCur->m_pFeature->first);
//         c_pL2M_->c_trans.transformCloudPoints(
//             tran0.m_quat, tran0.m_trans, l_KFCur->m_pFeature->second,
//             l_KFCur->m_pFeature->second);

//         // // 检查:这个转换对不对
//         pcl::io::savePCDFile<pcl::PointXYZHSV>(l_sSc_saveFile + l_id_2 + "__toSCMap.pcd",
//                                                *(l_KFCur->m_pFeature->second));

//     s_POSE6D trans_all;
//     bool res = false;
//     // for (size_t i = 0; i < 100; i++)
//     {
//         /*step2 计算局部点云偏转*/
//         s_POSE6D pose;
//         pose.setXYZ(0.0, 0.0, 0.0);
//         // pose.setQuat(l_CurPose.m_quat);
//         c_pL2M_->setInputSource(l_KFCur->m_pFeature, pose, true);
//         // c_pL2M_->setInputTarget(l_KFMap->m_pFeature);

//         res = c_pL2M_->align();

//         if (res)
//         {
//             // std::cout << "匹配成功" << std::endl;
//             s_POSE6D tran1 = c_pL2M_->getFinalTransformation();
//             // tran1.printf();
//             // s_POSE6D trans_all;
//             trans_all = tran1 * trans_all;
//             c_pL2M_->c_trans.transformCloudPoints(tran1.m_quat,
//                                                   tran1.m_trans,
//                                                   l_KFCur->m_pFeature->first,
//                                                   l_KFCur->m_pFeature->first);
//             c_pL2M_->c_trans.transformCloudPoints(tran1.m_quat,
//                                                   tran1.m_trans,
//                                                   l_KFCur->m_pFeature->second,
//                                                   l_KFCur->m_pFeature->second);

//             pcl::io::savePCDFile<pcl::PointXYZHSV>(
//                 l_sSc_saveFile + l_id_2 + "_scan_toMAP.pcd", *(l_KFCur->m_pFeature->second));
//         }
//         else
//         {
//             std::cout << "匹配失败" << std::endl;
//         }
//     }

//     if (res)
//     {
//         std::cout << "偏转:" << std::endl;
//         trans_all.printf();

//         s_POSE6D pose_new;

//         pose_new = l_MapPose * trans_all * tran0;
//         std::cout << "偏移后位姿:" << std::endl;
//         pose_new.printf();
//         //转到世界坐标系
//         c_pL2M_->c_trans.transformCloudPoints(pose_new.m_quat,
//                                               pose_new.m_trans,
//                                               l_KFCur->m_pFeature->allPC,
//                                               l_KFCur->m_pFeature->allPC);

//         pcl::io::savePCDFile<pcl::PointXYZHSV>(l_sSc_saveFile + l_id_2 + "_all_world_new.pcd",
//                                                *(l_KFCur->m_pFeature->allPC));
//     }
// }
// else
// {
//     std::cout << "不是回环点" << std::endl;
// }
// }

// TEST_F(ScanContex, _SC_aligncheck_error)
// {
//     int l_iMapID = atoi(l_id_1.c_str());
//     std::vector<FeaturePairPtr> l_vFearture_map(20);
//     int j = 0;
//     s_POSE6D l_MapPose;
//     for (size_t i = l_iMapID - 10; i < l_iMapID + 10; i++)
//     {
//         s_POSE6D l_pose;
//         std::string path = l_sSc_MainFile + std::to_string(i);
//         l_vFearture_map[j].reset(new FEATURE_PAIR<PointT>());
//         readOneFrame(path, l_vFearture_map[j], l_pose);
//         j++;
//         if (l_iMapID == i)
//             l_MapPose = l_pose;
//     }
//     // l_MapPose.printf();
//     wj_slam::KEYFRAME<PointT>::Ptr l_KFMap(new wj_slam::KEYFRAME<PointT>());
//     for (auto p : l_vFearture_map)
//     {
//         *(l_KFMap->m_pFeature->first) += *(p->first);
//         *(l_KFMap->m_pFeature->second) += *(p->second);
//     }

//     s_POSE6D l_CurPose;
//     wj_slam::KEYFRAME<PointT>::Ptr l_KFCur(new wj_slam::KEYFRAME<PointT>());
//     readOneFrame(l_sSc_MainFile + l_id_2, l_KFCur->m_pFeature, l_CurPose);
//     std::cout << "原始位姿:" << std::endl;
//     l_CurPose.printf();

//     //转到局部坐标
//     s_POSE6D l_EstTrans_wto0 = l_MapPose.inverse();

//     c_pL2M_->c_trans.transformCloudPoints(l_EstTrans_wto0.m_quat,
//                                           l_EstTrans_wto0.m_trans,
//                                           l_KFMap->m_pFeature->first,
//                                           l_KFMap->m_pFeature->first);
//     c_pL2M_->c_trans.transformCloudPoints(l_EstTrans_wto0.m_quat,
//                                           l_EstTrans_wto0.m_trans,
//                                           l_KFMap->m_pFeature->second,
//                                           l_KFMap->m_pFeature->second);

//     pcl::io::savePCDFile<pcl::PointXYZHSV>(l_sSc_MainFile + l_id_2 + "/MAP.pcd",
//                                            *(l_KFMap->m_pFeature->second));

//     s_POSE6D l_EstTrans_wto01 = l_CurPose.inverse();

//     c_pL2M_->c_trans.transformCloudPoints(l_EstTrans_wto01.m_quat,
//                                           l_EstTrans_wto01.m_trans,
//                                           l_KFCur->m_pFeature->first,
//                                           l_KFCur->m_pFeature->first);
//     c_pL2M_->c_trans.transformCloudPoints(l_EstTrans_wto01.m_quat,
//                                           l_EstTrans_wto01.m_trans,
//                                           l_KFCur->m_pFeature->second,
//                                           l_KFCur->m_pFeature->second);

//     s_POSE6D trans_all;
//     bool res = false;

//     /*step2 计算局部点云偏转*/
//     s_POSE6D pose;
//     // pose.setXYZ(0.0, 0.0, 0.0);
//     // pose.setQuat(l_CurPose.m_quat);
//     c_pL2M_->setInputSource(l_KFCur->m_pFeature, pose, true);
//     c_pL2M_->setInputTarget(l_KFMap->m_pFeature);

//     res = c_pL2M_->align();

//     if (res)
//     {
//         // std::cout << "匹配成功" << std::endl;
//         s_POSE6D tran1 = c_pL2M_->getFinalTransformation();
//         // tran1.printf();
//         // s_POSE6D trans_all;
//         trans_all = tran1 * trans_all;
//         c_pL2M_->c_trans.transformCloudPoints(
//             tran1.m_quat, tran1.m_trans, l_KFCur->m_pFeature->allPC, l_KFCur->m_pFeature->allPC);
//         trans_all.printf();

//         pcl::io::savePCDFile<pcl::PointXYZHSV>(l_sSc_MainFile + l_id_2 +
//         "/all_world_CHECKERRO.pcd",
//                                                *(l_KFCur->m_pFeature->allPC));
//     }
//     else
//     {
//         std::cout << "匹配失败" << std::endl;
//     }
// }

// TEST_F(ScanContex, _SC_aligncheck_erroronly1)
// {
//     int l_iMapID = atoi(l_id_1.c_str());
//     std::vector<FeaturePairPtr> l_vFearture_map(1);
//     int j = 0;
//     s_POSE6D l_MapPose;
//     for (size_t i = l_iMapID - 0; i < l_iMapID + 1; i++)
//     {
//         s_POSE6D l_pose;
//         std::string path = l_sSc_MainFile + std::to_string(i);
//         l_vFearture_map[j].reset(new FEATURE_PAIR<PointT>());
//         readOneFrame(path, l_vFearture_map[j], l_pose);
//         j++;
//         if (l_iMapID == i)
//             l_MapPose = l_pose;
//     }
//     // l_MapPose.printf();
//     wj_slam::KEYFRAME<PointT>::Ptr l_KFMap(new wj_slam::KEYFRAME<PointT>());
//     for (auto p : l_vFearture_map)
//     {
//         *(l_KFMap->m_pFeature->first) += *(p->first);
//         *(l_KFMap->m_pFeature->second) += *(p->second);
//     }

//     s_POSE6D l_CurPose;
//     wj_slam::KEYFRAME<PointT>::Ptr l_KFCur(new wj_slam::KEYFRAME<PointT>());
//     readOneFrame(l_sSc_MainFile + l_id_2, l_KFCur->m_pFeature, l_CurPose);
//     std::cout << "原始位姿:" << std::endl;
//     l_CurPose.printf();

//     //转到局部坐标
//     s_POSE6D l_EstTrans_wto0 = l_MapPose.inverse();

//     c_pL2M_->c_trans.transformCloudPoints(l_EstTrans_wto0.m_quat,
//                                           l_EstTrans_wto0.m_trans,
//                                           l_KFMap->m_pFeature->first,
//                                           l_KFMap->m_pFeature->first);
//     c_pL2M_->c_trans.transformCloudPoints(l_EstTrans_wto0.m_quat,
//                                           l_EstTrans_wto0.m_trans,
//                                           l_KFMap->m_pFeature->second,
//                                           l_KFMap->m_pFeature->second);

//     s_POSE6D l_EstTrans_wto01 = l_CurPose.inverse();

//     c_pL2M_->c_trans.transformCloudPoints(l_EstTrans_wto01.m_quat,
//                                           l_EstTrans_wto01.m_trans,
//                                           l_KFCur->m_pFeature->first,
//                                           l_KFCur->m_pFeature->first);
//     c_pL2M_->c_trans.transformCloudPoints(l_EstTrans_wto01.m_quat,
//                                           l_EstTrans_wto01.m_trans,
//                                           l_KFCur->m_pFeature->second,
//                                           l_KFCur->m_pFeature->second);

//     s_POSE6D trans_all;
//     bool res = false;

//     /*step2 计算局部点云偏转*/
//     s_POSE6D pose;
//     // pose.setXYZ(0.0, 0.0, 0.0);
//     // pose.setQuat(l_CurPose.m_quat);
//     c_pL2M_->setInputSource(l_KFCur->m_pFeature, pose, true);
//     c_pL2M_->setInputTarget(l_KFMap->m_pFeature);

//     res = c_pL2M_->align();

//     if (res)
//     {
//         // std::cout << "匹配成功" << std::endl;
//         s_POSE6D tran1 = c_pL2M_->getFinalTransformation();
//         // tran1.printf();
//         // s_POSE6D trans_all;
//         trans_all = tran1 * trans_all;
//         c_pL2M_->c_trans.transformCloudPoints(
//             tran1.m_quat, tran1.m_trans, l_KFCur->m_pFeature->allPC, l_KFCur->m_pFeature->allPC);
//         pcl::io::savePCDFile<pcl::PointXYZHSV>(l_sSc_MainFile + l_id_2
//                                                    + "/all_world_CHECKERROONLY1.pcd",
//                                                *(l_KFCur->m_pFeature->allPC));

//         trans_all.printf();
//     }
//     else
//     {
//         std::cout << "匹配失败" << std::endl;
//     }
// }
