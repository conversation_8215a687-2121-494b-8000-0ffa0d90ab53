

/*
使用说明:
1.建图后查看LOG,若应该回环的帧未发生回环,使用本测试案例检测回环匹配过程是否正确;
2.需要修改的
(1)MAPFILEWITHKF : 需要测试的地图文件夹,保存时使用debug保存,检查地图文件夹中是否存在KF文件夹;
(2)SAVETESTRESULTEPATH: 过程文件保存路径,注意:未实现文件夹创建方法,需要手动创建文件夹
(3)MAP_ID 回环帧帧号,CUR_ID当前帧帧号
3.结果
(1)id + _MAP.pcd 保存基于回环帧的回环地图的全局点云
(2)id + _RAW.pcd 保存当前帧的全局点云
(3)id + _SC.pcd 保存局部点云SC预估后的位置,观察是否是SC预估错误
(4)id + _result.pcd 保存回环匹配成功后,当前帧修正后的全局点云
(5)id + _CHECKRAW.pcd 保存当前帧的前一帧的原始全局点云
(6)id + _CHECK.pcd 保存当前帧的前一帧作为回环校验帧经过修正后的全局点云

4.分析过程
(1)若存在回环错误(该回环的点没回环,不该回环的错误回环导致地图错误),LOG中查看进入回环产生回环计算的帧以及是否进入了AMCL;
(2)在KF中分析是否是回环帧,对产生异常的使用本测试案例进行分析;
(3)检查MAP和RAW是否确实是回环帧,若是且进入了AMCL,则查看SC预估是否基本正确(有正负10度左右偏差合理);
(4)若SC预估正确,匹配返回TRUE,有两种情况:
    1> result.pcd 与地图优化错误,则说明回环检测代码中判断匹配成功的方法有错误
    2> result.pcd 与地图优化正确,则说明回环校验未通过
    3> _CHECK.pcd 与地图优化正确,则说明回环检测代码中判断匹配成功的方法有问题(阈值设置问题)
(5)若SC预估正确,匹配返回FALSE,有两种情况
    1> 代码中额外增加保存amcl结果代码,若AMCL错误则是粒子滤波问题
    2> AMCL没问题,则查看匹配优化结果异常
todo:
  gtest增加参数输入,就不需要每次测试都重新编译了

*/

// log
#include "../../include/common/common_ex.h"
#include "../../include/common/common_master.h"
#include "../../include/wj_log.h"
#include "../../test/param.hpp"
// pcl库
#include <pcl/common/transforms.h>
#include <pcl/io/pcd_io.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
// gtest
#include <gtest/gtest.h>
// opencv
#include <opencv2/highgui/highgui.hpp>
#include <opencv2/imgcodecs.hpp>

#include "../../include/algorithm/loop/loopCheck/loopCheck.hpp"
#include "../../include/algorithm/map/sub_map/KeyFrameMap.h"
#include "../../test/location/impl/debugSave.hpp"
#define FILEPATH "/home/<USER>/data/lrz_ws/src/wanji_slam/data/Map/small"
#define pathCLoud "/home/<USER>/data/lrz_ws/src/wanji_slam/data/Map/small/p.pcd"
#define MAPFILEWITHKF "/home/<USER>/data/lrz_ws/src/wanji_slam/data/Map/small/KF/"
#define SAVETESTRESULTEPATH "/home/<USER>/data/lrz_ws/src/wanji_slam/data/Map/loop_gtest/gtest"

#define MAP_ID 283
#define CUR_ID 150

generalLog* glog = nullptr;
WJLog* generalLog::wjlog = nullptr;
// 路径日志
WJLog* generalLog::pathlog = nullptr;
// 校验日志
WJLog* generalLog::checklog = nullptr;
// 速度日志
WJLog* generalLog::speedlog = nullptr;
// 对外日志
WJLog* generalLog::faelog = nullptr;
using namespace wj_slam;
class LOOKCHECK : public testing::Test {
  protected:
    using PointT = pcl::PointXYZHSV;
    using PointCloudT = pcl::PointCloud<PointT>;
    using FeaturePair = FEATURE_PAIR<PointT>;         /**< 输入特征组类型 */
    using FeaturePairPtr = typename FeaturePair::Ptr; /**< 输入特征组指针类型 */
    using Frame = KEYFRAME<PointT>;                   /**< 输入帧-特征组指针类型*/
    using FramePtr = typename Frame::Ptr;

    boost::shared_ptr<LoopCheck<PointT>> c_pLoopCheck_;
    boost::shared_ptr<oprateFile<PointT>> c_pRWkfFile_;
    boost::shared_ptr<KfMapPair> c_pMapPair_;
    std::vector<std::pair<int, int>> failLoopPair;
    std::vector<std::pair<int, int>> succLoopPair;
    std::vector<std::pair<int, int>> needToCheckLoopPair;
    virtual void SetUp()
    {
        // log对象
        wj_slam::s_masterCfg* g_masterParamPtr;
        wj_slam::Param l_param(false);
        g_masterParamPtr = wj_slam::s_masterCfg::getIn();
        l_param.loadSysParam("/home/<USER>/data/lrz_ws/src/wanji_slam");
        glog = new generalLog(g_masterParamPtr->m_slam->m_fae.m_sLogPath,
                              "wj",
                              FileMode::DAILY_ROTATE,
                              LogMode::TRIAL_MODE,
                              g_masterParamPtr->m_slam->m_fae.getLogFileSizeByte());

        glog->changeMode(g_masterParamPtr->m_slam->m_fae.m_iLogLevel);

        c_pMapPair_.reset(new KfMapPair());
        c_pLoopCheck_.reset(new LoopCheck<PointT>(c_pMapPair_));
        c_pRWkfFile_.reset(new oprateFile<PointT>(FILEPATH));
    }
    virtual void TearDown() {}

    //获取地图帧
    void getMapFeature(int p_iMapid,
                       int p_iSizeMap,
                       wj_slam::KEYFRAME<PointT>::Ptr& p_pKFMap,
                       wj_slam::s_POSE6D& p_MapPose)
    {
        std::vector<FeaturePairPtr> l_vFearture_map(p_iSizeMap);
        int j = 0;
        wj_slam::s_POSE6D l_pose;
        for (int i = p_iMapid - p_iSizeMap / 2; i < p_iMapid + p_iSizeMap / 2; i++)
        {
            if (i < 0)
                continue;
            std::string path = MAPFILEWITHKF + std::to_string(i);
            l_vFearture_map[j].reset(new FEATURE_PAIR<PointT>());
            c_pRWkfFile_->readOneFrame_(path, l_vFearture_map[j], l_pose);
            j++;
            if (p_iMapid == i)
                p_MapPose = l_pose;
        }
        l_vFearture_map.resize(j);
        // printf("地图pose信息:\n");
        // p_MapPose.printf();
        for (auto p : l_vFearture_map)
        {
            *(p_pKFMap->m_pFeature->first) += *(p->first);
            *(p_pKFMap->m_pFeature->second) += *(p->second);
        }
    }
    //获取当前帧
    void getCurFeature(int p_iCurid,
                       wj_slam::KEYFRAME<PointT>::Ptr& p_pKFCur,
                       wj_slam::s_POSE6D& p_CurPose)
    {
        std::string cur_id = std::to_string(p_iCurid);
        c_pRWkfFile_->readOneFrame_(MAPFILEWITHKF + cur_id, p_pKFCur->m_pFeature, p_CurPose);
        // std::cout << "帧" << p_iCurid << "位姿信息:" << std::endl;
        // p_CurPose.printf();
    }

    void saveCloud_debug(std::string p_sName, int p_iID, PointCloudT& p_pCloud)
    {
        pcl::io::savePCDFile<pcl::PointXYZHSV>(
            SAVETESTRESULTEPATH + std::to_string(p_iID) + p_sName + ".pcd", p_pCloud);
    }

    void transPoint(wj_slam::s_POSE6D p_trans, FeaturePairPtr p_cloud)
    {
        c_pLoopCheck_->c_pMatcher_.c_trans.transformCloudPoints(
            p_trans.m_quat, p_trans.m_trans, p_cloud->first, p_cloud->first);
        c_pLoopCheck_->c_pMatcher_.c_trans.transformCloudPoints(
            p_trans.m_quat, p_trans.m_trans, p_cloud->second, p_cloud->second);
    }

    ::testing::AssertionResult CheckLoop(bool p_bFlag, int p_iCurId, int p_iMapId)
    {
        
        if (p_bFlag)
        {
            return ::testing::AssertionFailure() << "校验通过的回环点: "
                                                 << "curid" << std::to_string(p_iCurId)
                                                 << 'to mapid:' << std::to_string(p_iMapId);
            std::pair<int, int> cur2map;
            cur2map.first = p_iCurId;
            cur2map.second = p_iCurId;
            succLoopPair.push_back(cur2map);
            printf("执行了1\n");
        }
        else
        {
            return ::testing::AssertionFailure() << "校验失败的回环点: "
                                                 << "curid" << std::to_string(p_iCurId)
                                                 << 'to mapid:' << std::to_string(p_iMapId);
            std::pair<int, int> cur2map;
            cur2map.first = p_iCurId;
            cur2map.second = p_iCurId;
            failLoopPair.push_back(cur2map);
            printf("执行了2\n");
        }
    }
     float getRandomData(const float p_fMin, const float p_fMax)
    {
        std::random_device rd;
        // 用 random_device产生一个真随机数，用作“伪随机数发生器”的种子
        std::default_random_engine gen(rd());
        // 一个正态“分布器”，高斯分布器是 std::normal_distribution
        std::uniform_real_distribution<float> dis(p_fMin, p_fMax);
        return dis(gen);
    }
};

// TEST_F(LOOKCHECK, linshi)
// {
//     double i = 0.0546;
//     double i2 = 0.1561;
//     double i3 = 4164.49849;
//     std::vector<int> nubV;
//     nubV.push_back(i);
//     nubV.push_back(i2);
//     nubV.push_back(i3);

//     for(auto ex : nubV)
//     {
//         std::cout << ex << std::endl;
//     }

//     int i4 = 4164;
//     if(i4 < i3)
//     printf("true!\n");
//     else
//     printf("整形和 浮点数比较错误\n");
// }
// // //异常测试
TEST_F(LOOKCHECK, FUNCTIONTEST)
{
    // pcl::PointCloud<pcl::PointXYZI>::Ptr cloud_in;
    // // // std::string path = FILEPATH +"/p.pcd";/
    // pcl::io::loadPCDFile<pcl::PointXYZI>(pathCLoud, *cloud_in);
    for (int CurId = 1; CurId < 1000; ++CurId)
    {
        for (int mapid = 1; mapid < 1000; (mapid + getRandomData(0.0,10.0)))
        {
            int l_iMapSize = 20;
            wj_slam::s_POSE6D l_MapPose;
            wj_slam::KEYFRAME<PointT>::Ptr l_KFMap(new wj_slam::KEYFRAME<PointT>());
            getMapFeature(mapid, l_iMapSize, l_KFMap, l_MapPose);
            //读取当前帧
            wj_slam::s_POSE6D l_CurPose;
            wj_slam::KEYFRAME<PointT>::Ptr l_KFCur(new wj_slam::KEYFRAME<PointT>());
            getCurFeature(CurId, l_KFCur, l_CurPose);
            //读取当前的前一帧
            wj_slam::s_POSE6D l_CurfrontPose;
            wj_slam::KEYFRAME<PointT>::Ptr l_KFCurfront(new wj_slam::KEYFRAME<PointT>());
            getCurFeature(CurId - 1, l_KFCurfront, l_CurfrontPose);
            wj_slam::s_POSE6D l_Pose;
            wj_slam::KEYFRAME<PointT>::Ptr l_KFMap_one(new wj_slam::KEYFRAME<PointT>());
            getCurFeature(mapid, l_KFMap_one, l_Pose);
            auto dist_shift = c_pLoopCheck_->c_PlaceRecongnize_.distanceBtnScanContext(
                *l_KFCur->m_pFeature->sc, *l_KFMap_one->m_pFeature->sc);
            double l_dSCEstimateYaw =
                dist_shift.second / float(c_pLoopCheck_->c_stSysParam_->m_loop.m_iSector) * 360.0;
            if ((1 - dist_shift.first) > c_pLoopCheck_->c_stSysParam_->m_loop.m_fSCthr)
            {
                wj_slam::s_POSE6D l_stSCEstimatePose;
                l_stSCEstimatePose.setRPY(0, 0, l_dSCEstimateYaw);
                c_pLoopCheck_->c_pMatcher_.setSCEstimate(l_stSCEstimatePose);
                c_pLoopCheck_->c_pMatcher_.c_trans.transformCloudPoints(l_stSCEstimatePose.m_quat,
                                                                        l_stSCEstimatePose.m_trans,
                                                                        l_KFCur->m_pFeature->allPC,
                                                                        l_KFCur->m_pFeature->allPC);
                c_pLoopCheck_->c_pMatcher_.setInputSource(l_KFCur->m_pFeature, l_CurPose);
                c_pLoopCheck_->c_pMatcher_.setInputTarget(
                    l_KFMap->m_pFeature, l_KFMap_one->m_pFeature, l_Pose);
                    printf("step0\n");
                bool res = c_pLoopCheck_->c_pMatcher_.align();
                printf("step1\n");
                if (res)
                {
                    printf("step1\n");
                    wj_slam::s_POSE6D l_stOptResult =
                        c_pLoopCheck_->c_pMatcher_.getFinalTransformation();
                    wj_slam::s_POSE6D p_curNewPose;
                    p_curNewPose = l_stOptResult * l_CurPose;
                    transPoint(l_stOptResult, l_KFCur->m_pFeature);
                    FramePtr l_KFCheck(new Frame);
                    *l_KFCheck = *l_KFCurfront;
                    wj_slam::s_POSE6D l_stNewPose;
                    wj_slam::s_POSE6D l_stDelta = l_CurfrontPose.inverse() * l_CurPose;
                    l_stNewPose = p_curNewPose * l_stDelta.inverse();
                    wj_slam::s_POSE6D l_opt = l_stNewPose * l_CurfrontPose.inverse();
                    c_pLoopCheck_->c_pMatcher_.c_trans.transformCloudPoints(
                        l_opt.m_quat,
                        l_opt.m_trans,
                        l_KFCheck->m_pFeature->first,
                        l_KFCheck->m_pFeature->first);
                    c_pLoopCheck_->c_pMatcher_.c_trans.transformCloudPoints(
                        l_opt.m_quat,
                        l_opt.m_trans,
                        l_KFCheck->m_pFeature->second,
                        l_KFCheck->m_pFeature->second);
                    c_pLoopCheck_->c_pMatcher_.setInputSource(l_KFCheck->m_pFeature, l_stNewPose);

                    std::pair<int, int> cur2map;
                    cur2map.first = CurId;
                    cur2map.second = mapid;
                     printf("step2\n");
                    if (c_pLoopCheck_->c_pMatcher_.checkDistance(true))
                    {
                        if (abs(l_CurPose.norm() - l_MapPose.norm()) > 10)
                        {
                            failLoopPair.push_back(cur2map);
                        }
                        else if (abs(CurId - mapid) > 5)
                            succLoopPair.push_back(cur2map);
                    }
                    else
                    {
                        if (abs(l_CurPose.norm() - l_MapPose.norm()) < 5)
                        {
                            needToCheckLoopPair.push_back(cur2map);
                        }
                    }
                }
            }
        }
    }
    std::cout << "异常情况:" << failLoopPair.size() << "个" << std::endl;
    for (auto Pair : failLoopPair)
    {
        std::cout << "false cur:" << Pair.first << "map:" << Pair.second << std::endl;
    }
    std::cout << "正常回环:" << succLoopPair.size() << "个" << std::endl;
    for (auto Pair : succLoopPair)
    {
        std::cout << "succ cur:" << Pair.first << "map:" << Pair.second << std::endl;
    }
    std::cout << "被过滤的回环点:" << needToCheckLoopPair.size() << "个" << std::endl;
    for (auto Pair : needToCheckLoopPair)
    {
        std::cout << "check cur:" << Pair.first << "map:" << Pair.second << std::endl;
    }

    ASSERT_EQ(failLoopPair.size(), 0);
}
// TEST_F(LOOKCHECK, savepcd)
// {
//     // int l_iMapId = c_pLoopCheck_->c_stSysParam_->m_loop.mapid;
//     // int l_iCurId = c_pLoopCheck_->c_stSysParam_->m_loop.curid;

//     for (auto pairP : succLoopPair)
//     {
//         int l_iCurId = pairP.first;
//         int l_iMapId = pairP.second;
//         int l_iMapSize = 20;  //必须是个偶数.奇数会崩溃
//         wj_slam::s_POSE6D l_MapPose;
//         wj_slam::KEYFRAME<PointT>::Ptr l_KFMap(new wj_slam::KEYFRAME<PointT>());
//         getMapFeature(l_iMapId, l_iMapSize, l_KFMap, l_MapPose);
//         //读取当前帧
//         wj_slam::s_POSE6D l_CurPose;
//         wj_slam::KEYFRAME<PointT>::Ptr l_KFCur(new wj_slam::KEYFRAME<PointT>());
//         getCurFeature(l_iCurId, l_KFCur, l_CurPose);
//         //读取当前的前一帧
//         wj_slam::s_POSE6D l_CurfrontPose;
//         wj_slam::KEYFRAME<PointT>::Ptr l_KFCurfront(new wj_slam::KEYFRAME<PointT>());
//         getCurFeature(l_iCurId - 1, l_KFCurfront, l_CurfrontPose);
//         wj_slam::s_POSE6D l_Pose;
//         wj_slam::KEYFRAME<PointT>::Ptr l_KFMap_one(new wj_slam::KEYFRAME<PointT>());
//         getCurFeature(l_iMapId, l_KFMap_one, l_Pose);

//         // printf("读取总地图角点数:%d\n面点数:%d\nsample2endsize:%d\n",
//         //        l_KFMap->m_pFeature->first->size(),
//         //        l_KFMap->m_pFeature->second->size(),
//         //        l_KFMap->m_pFeature->m_iSample2ndSize);
//         // printf("读取当前帧取角点数:%d\n面点数:%d\nsample2endsize:%d\n",
//         //        l_KFCur->m_pFeature->first->size(),
//         //        l_KFCur->m_pFeature->second->size(),
//         //        l_KFCur->m_pFeature->m_iSample2ndSize);
//         // printf("读取单帧地图角点数:%d\n面点数:%d\nsample2endsize:%d\n",
//         //        l_KFMap_one->m_pFeature->first->size(),
//         //        l_KFMap_one->m_pFeature->second->size(),
//         //        l_KFMap_one->m_pFeature->m_iSample2ndSize);

//         // // newposetransPoint(l_MapPose.inverse(),l_KFMap->m_pFeature);
//         saveCloud_debug("_MAP", l_iCurId, *(l_KFMap->m_pFeature->second));
//         saveCloud_debug("_RAW", l_iCurId, *(l_KFCur->m_pFeature->second));
//         auto dist_shift = c_pLoopCheck_->c_PlaceRecongnize_.distanceBtnScanContext(
//             *l_KFCur->m_pFeature->sc, *l_KFMap_one->m_pFeature->sc);
//         double l_dSCEstimateYaw =
//             dist_shift.second / float(c_pLoopCheck_->c_stSysParam_->m_loop.m_iSector) * 360.0;
//         if ((1 - dist_shift.first) > c_pLoopCheck_->c_stSysParam_->m_loop.m_fSCthr)
//         {
//             wj_slam::s_POSE6D l_stSCEstimatePose;
//             l_stSCEstimatePose.setRPY(0, 0, l_dSCEstimateYaw);
//             c_pLoopCheck_->c_pMatcher_.setSCEstimate(l_stSCEstimatePose);

//             c_pLoopCheck_->c_pMatcher_.c_trans.transformCloudPoints(l_stSCEstimatePose.m_quat,
//                                                                     l_stSCEstimatePose.m_trans,
//                                                                     l_KFCur->m_pFeature->allPC,
//                                                                     l_KFCur->m_pFeature->allPC);

//             saveCloud_debug("_SC", l_iCurId, *(l_KFCur->m_pFeature->allPC));

//             //临时加的 保存匹配点云看情况

//             c_pLoopCheck_->c_pMatcher_.setInputSource(l_KFCur->m_pFeature, l_CurPose);
//             c_pLoopCheck_->c_pMatcher_.setInputTarget(
//                 l_KFMap->m_pFeature, l_KFMap_one->m_pFeature, l_Pose);
//             bool res = c_pLoopCheck_->c_pMatcher_.align();
//             if (res)
//             {
//                 // printf("gtest匹配成功!\n");
//                 wj_slam::s_POSE6D l_stOptResult =
//                     c_pLoopCheck_->c_pMatcher_.getFinalTransformation();
//                 // l_stOptResult.printf("get opt");
//                 wj_slam::s_POSE6D p_curNewPose;
//                 p_curNewPose = l_stOptResult * l_CurPose;
//                 transPoint(l_stOptResult, l_KFCur->m_pFeature);
//                 saveCloud_debug("_result", l_iCurId, *(l_KFCur->m_pFeature->second));
//                 FramePtr l_KFCheck(new Frame);

//                 *l_KFCheck = *l_KFCurfront;
//                 wj_slam::s_POSE6D l_stNewPose;

//                 //  wj_slam::s_POSE6D l_stDelta = l_CurPose * l_CurfrontPose.inverse(); //错
//                 wj_slam::s_POSE6D l_stDelta = l_CurfrontPose.inverse() * l_CurPose;  // 对
//                 // l_stDelta.printf("delta");
//                 // l_stNewPose = l_stDelta.inverse() * p_curNewPose;//错
//                 l_stNewPose = p_curNewPose * l_stDelta.inverse();  //对
//                 // l_stNewPose.printf("newpose");
//                 wj_slam::s_POSE6D l_opt = l_stNewPose * l_CurfrontPose.inverse();
//                 // l_opt.printf("opt");

//                 saveCloud_debug("_CHECKRAW", l_iCurId, *(l_KFCheck->m_pFeature->second));
//                 c_pLoopCheck_->c_pMatcher_.c_trans.transformCloudPoints(
//                     l_opt.m_quat,
//                     l_opt.m_trans,
//                     l_KFCheck->m_pFeature->first,
//                     l_KFCheck->m_pFeature->first);
//                 c_pLoopCheck_->c_pMatcher_.c_trans.transformCloudPoints(
//                     l_opt.m_quat,
//                     l_opt.m_trans,
//                     l_KFCheck->m_pFeature->second,
//                     l_KFCheck->m_pFeature->second);
//                 saveCloud_debug("_CHECK", l_iCurId, *(l_KFCheck->m_pFeature->second));
//                 // pcl::io::savePCDFile<pcl::PointXYZHSV>(
//                 //     "/home/<USER>/data/lrz_ws/src/wanji_slam/data/Map/loop_gtest/回环check测试/"
//                 //         + std::to_string(l_iMapId - 1) + ".pcd",
//                 //     *(l_KFCheck->m_pFeature->second));
//                 c_pLoopCheck_->c_pMatcher_.setInputSource(l_KFCheck->m_pFeature, l_stNewPose);

//                 //自定义断言
//                 // CheckLoop(c_pLoopCheck_->c_pMatcher_.checkDistance(true), l_iCurId, l_iMapId)
//             }
//         }
//     }
// }