<!-- -*- 正式启动系统 -*- -->
<launch>
    <rosparam file="$(find wj_slam)/config/params.yaml" command="load"/>
    <arg name="MarkerTopic" default="UI" />
    <arg name="MarkerFrame" default="wanji_map" />
    <!--  -->
    <node name="wjproxy" pkg="wj_slam" type="wanjiproxy" output="screen" 
     >
      <param name="topic" value="$(arg MarkerTopic)"/>
      <param name="frame" value="$(arg MarkerFrame)"/>
    </node>
    <include file="$(find rosbridge_server)/launch/rosbridge_websocket.launch" />
    <node name="tf2_web_republisher" pkg="tf2_web_republisher" type="tf2_web_republisher"/>
  <!-- launch-prefix="xterm -e gdb -ex run -args" -->
  <!-- launch-prefix="valgrind -tool=memcheck -leak-check=full -log-file=/media/wanji/data/autoDriving/HongQi2/autodriving0928/log/callgrind.out" -->
    <node name="wjmaster" pkg="wj_slam" type="wanjimaster" output="screen" 
   
    />      
  <node name="wanjislamrviz" pkg="rviz" type="rviz" args="-d $(find wj_slam)/rviz_cfg/wjlidar.rviz"/>
</launch>