/**
 * @file map_proc.h
 * <AUTHOR> Li
 * @brief 地图后处理接口类
 * @version 0.1
 * @date 2023-06-28
 *
 * @copyright Copyright (c) 2023
 *
 */
#pragma once
#include "algorithm/postproc/cloud/cloud_coloring.h"
#include "algorithm/postproc/cloud/cloud_postproc.h"
#include "common/type/type_frame.h"
namespace wj_slam {

using namespace wj_cpostproc;
using namespace wj_cColor;

template <typename P> class MapProc {
  private:
    using KeyFrame = KEYFRAME<P>;               /**< 关键帧类*/
    using KeyFramePtr = typename KeyFrame::Ptr; /**< 关键帧类智能指针*/

    using KeyFrameFeature = FEATURE_PAIR<P>;                  /**< 特征帧类*/
    using KeyFrameFeaturePtr = typename KeyFrameFeature::Ptr; /**< 特征帧智能指针*/

    using PointCloud = pcl::PointCloud<P>;          /**< 点云*/
    using PointCloudPtr = typename PointCloud::Ptr; /**< 点云智能指针*/

    using CloudPostProcPtr = typename CloudPostProc<P>::Ptr; /**< 点云处理封装类智能指针*/

  public:
    using Ptr = boost::shared_ptr<MapProc<P>>; /**< 对外本类智能指针封装*/

  private:
    CloudPostProcPtr c_pAllCloudProcer_;         /**< 全部点云处理方法*/
    CloudPostProcPtr c_pPlaneCloudProcer_;       /**< 平面点云处理方法*/
    CloudPostProcPtr c_pLineCloudProcer_;        /**< 直线（仅垂线）点云处理方法*/
    typename CloudColorBase<P>::Ptr c_pColor2D_; /**< RGB上色类*/

    float c_fAllCloudSize_;   /**< 全部点云处理参数*/
    float c_fPlaneCloudSize_; /**< 平面点云处理参数*/
    float c_fLineCLoudSize_;  /**< 垂线点云处理方式*/
  public:
    /**
     * @brief 构造函数
     *
     */
    MapProc()
        : c_pAllCloudProcer_(nullptr), c_pPlaneCloudProcer_(nullptr), c_pLineCloudProcer_(nullptr),
          c_pColor2D_(nullptr), c_fAllCloudSize_(0.2), c_fPlaneCloudSize_(0.2),
          c_fLineCLoudSize_(0.1)
    {
        init_();
    }
    /**
     * @brief 析构函数
     *
     */
    ~MapProc() {}
    /**
     * @brief 输入输出特征帧
     *
     * @param dataIn
     * @param dataOut
     */
    void process(boost::shared_ptr<KeyFrameFeature>& dataIn, KeyFrameFeature& dataOut);
    /**
     * @brief 输入输出关键帧
     *
     * @param dataIn
     * @param dataOut
     */
    void process(boost::shared_ptr<KeyFrame>& dataIn, KeyFrame& dataOut);
    /**
     * @brief 输入特征帧，输出关键帧
     *
     * @param dataIn
     * @param dataOut
     */
    void process(boost::shared_ptr<KeyFrameFeature>& dataIn, KeyFrame& dataOut);
    /**
     * @brief 输入关键帧，输出特征帧
     *
     * @param dataIn
     * @param dataOut
     */
    void process(boost::shared_ptr<KeyFrame>& dataIn, KeyFrameFeature& dataOut);
    /**
     * @brief 输出2D点云
     *
     * @param dataOut 输出2D点云
     */

    void make2D(PointCloudPtr& dataOut);
    /**
     * @brief 设置颜色
     *
     * @param dataIn
     * @param dataOut
     */

    void colour(boost::shared_ptr<pcl::PointCloud<P>>& dataIn, pcl::PointCloud<pcl::RGB>& dataOut);
    /**
     * @brief 设置全部点云、面点、角点下采样尺寸
     *
     * @param p_fAllpc
     * @param p_fPlane
     * @param p_fLize
     */
    void setSampleSize(float p_fAllpc, float p_fPlane, float p_fLize);

  private:
    /**
     * @brief 创造平面和直线两种处理方式，并进行设定
     *
     */
    void init_();
    /**
     * @brief 调用不同处理类处理点云
     *
     * @param p_postProcer 处理类对象
     * @param dataIn 输入点云指针
     * @param dataOut 输出点云
     */
    void process_(CloudPostProcPtr& p_postProcer, PointCloudPtr& dataIn, PointCloud& dataOut);
    /**
     * @brief 调用处理类生成2D点云
     *
     * @param p_postProcer 处理类对象
     * @param dataOut 输出点云
     */
    void gen2D_(CloudPostProcPtr& p_postProcer, PointCloud& dataOut);
};
}  // namespace wj_slam
// #    ifdef WJSLAM_NO_PRECOMPILE
#include "impl/map_proc.hpp"
// #    else
// #define WJSLAM_MapProc(P) template class wj_slam::MapProc<P>;
// #    endif
