/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2021-04-27 19:17:34
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2024-01-12 09:22:20
 */
#include "slam.h"
#include "../calibration/horizonAlign.h"
#include "../calibration/mLidarCalib.h"
#include "../location/location.h"
#include "../odomerty/odometry.h"
#include "../odomerty/wheelOdomerty.h"
#include "../param.hpp"
#include "../preproc/pre_proc.h"
#include "algorithm/optimize/laserTransform.h"
#include "algorithm/preproc/driver/dataBackup/dataBackUp.hpp"
#include "common/type/type_queue.h"
#include "mac/checkMac.h"
#include "net_app/netApp.h"
#include "net_app/net_Message_Proc.h"
#include <iostream>
#include <pcl/filters/voxel_grid.h>
#include <signal.h>
#include <stddef.h>
#include <termio.h>
#include <thread>
#include <visualization_msgs/Marker.h>
#include <visualization_msgs/MarkerArray.h>

#include "../../../../../commonlibrary/src/common.h"

#include "../datapreprocess/datapreprocess.h"

// #include <ros/builtin_message_traits.h>
// #include <ros/message_operations.h>
// #include <pcl_conversions/pcl_conversions.h>
// #include <pcl/point_cloud.h>

using namespace std;

namespace wj_slam {

typedef pcl::PointXYZHSV PCurXYZHSV;
typedef pcl::PointXYZ PMapXYZ;
typedef KEYFRAME<PCurXYZHSV> KEYFRAMECURR;
typedef KEYFRAME<PMapXYZ> MAPFRAME;
typedef typename KEYFRAMECURR::Ptr KEYFRAME_PTR;
typedef typename MAPFRAME::Ptr MAPFRAME_PTR;
typedef typename FEATURE_PAIR<PCurXYZHSV>::Ptr FEATURE_PAIR_PTR;
typedef typename Odometry<PCurXYZHSV>::Ptr Odom_Ptr;
typedef typename WheelOdometry::Ptr WheelOdom_Ptr;
typedef typename Location<PCurXYZHSV, PCurXYZHSV>::Ptr Location_Ptr;
typedef typename DataBackUp<PCurXYZHSV, PCurXYZHSV>::Ptr DataBackUp_Ptr;
typedef typename s_LIDAR_RAW_DATAS::Ptr RawDataPtr;

/**
 * @class slam控制模块
 * @brief 负责其他模块的启动、切换与关闭
 *
 *
 */
class WanJiSLAM {
  private:
    SYSPARAM* c_sysParam_;    /**< 外部传参结构体 */
    ros::NodeHandle& c_node_; /**< ROS 节点 */

  public:
    WanJiSLAM(ros::NodeHandle& node,
              boost::function<void(uint32_t, RawDataPtr&, int, bool)> p_wjOdCallback);
    ~WanJiSLAM() {
        c_DataPreprocessPtr = nullptr;
    }
    /**
     * @brief 监控键盘键入信息
     *
     *
     */
    void startCtrl()
    {
        if (!c_bGetKey_)
            return;
        c_bGetKey_ = false;
        printf("start scan key!\n");
        std::thread pcapCtrl(&WanJiSLAM::scanKeyboard, this);
        pcapCtrl.detach();
    }
    /**
     * @brief 关闭其他模块
     *
     *
     */
    void stop()
    {
        LOGFAE(WWARN, "WLR720混合导航软件SLAM模块正在关闭...");
        c_sysParam_->m_iWorkMode = WorkMode::StandByMode;
        this->setWorkMode(0);
        c_wjNetApp_->shutDown();
        c_wjNetWeb_->shutDown();
        c_preProcess_->shutDown();
        c_odom_->shutDown();
        c_wheelOdom_->shutDown();
        c_location_->shutDown();

        if (c_MLCalib_)
        {
            c_MLCalib_->shutdown();
            c_MLCalib_ = nullptr;
        }
        if (c_HACalib_)
        {
            c_HACalib_->shutdown();
            c_HACalib_ = nullptr;
        }
        c_preProcess_ = nullptr;
        c_odom_ = nullptr;
        c_wheelOdom_ = nullptr;
        c_location_ = nullptr;
        c_dataBak_ = nullptr;
        c_wjNetApp_ = nullptr;
        c_wjNetWeb_ = nullptr;
        LOGFAE(WWARN, "WLR720混合导航软件SLAM模块关闭成功");
    }

  private:
#pragma region "ros"
    ros::NodeHandle node;                 /**< ROS 节点 */
    ros::Subscriber subWanjiPointCloud_;  // hsq
    ros::Subscriber subVLPPointCloud_; // hsq
    ros::Subscriber subGps_;
    ros::Publisher outputPath_;       /**< path 节点 */
    ros::Publisher outputPose_;       /**< pose 节点 */
    ros::Publisher outputPathOdom_;   /**< odometry path 节点 */
    ros::Publisher outputPathHPOdom_; /**< odom预估 path 节点 */
    ros::Publisher outputPathWheel_, outputPathWheelRaw_, outputPathPred_, outputPathCurb_,
        outputPathOutEnd_; /**< ROS 节点 */
    ros::Publisher outputPoseWheel_, outputPoseWheelRaw_, outputPosePred_, outputPoseCurb_,
        outputPoseOutEnd_; /**< ROS 节点 */

    ros::Publisher outputCurr_;      /**< 当前扫描点云 */
    ros::Publisher outputCurrTrans_; /**< 当前扫描点云 */
    ros::Publisher outputMap_;       /**< 当前地图 */
    ros::Publisher outPredBox_;      /**< 位姿预估框 */
    ros::Subscriber wanji_scan_;     /**< 当前扫描点云 */
    ros::Subscriber workMode_;
    ros::Publisher outputCurMarks; /**< 当前靶标  */
    ros::Publisher outputMarksMap; /**< 靶标地图 */
#pragma endregion
    pcl::PointCloud<PCurXYZHSV>::Ptr c_pcMergeNewCloud_;      /**< 扫描积累地图 */
    Queue<FEATURE_PAIR_PTR> c_featureQueue_;                  /**< 特征点云 */
    Queue<KEYFRAME_PTR> c_keyFramesQueue_;                    /**< 关键帧点点云 */
    PreProcess<PCurXYZHSV>::Ptr c_preProcess_;                /**< 预处理 */
    Odom_Ptr c_odom_;                                         /**< 激光里程计 */
    WheelOdom_Ptr c_wheelOdom_;                               /**< 轮式里程计 */
    Location_Ptr c_location_;                                 /**< 定位模块 */
    DataBackUp_Ptr c_dataBak_;                                /**< 异常数据保存 */
    boost::shared_ptr<WJNetApp> c_wjNetApp_;                  /**< 网络模块 */
    boost::shared_ptr<WJNetApp> c_wjNetWeb_;                  /**< Web模块 */
    boost::shared_ptr<MLidarCalib<PCurXYZHSV>> c_MLCalib_;    /**< 雷达标定 */
    boost::shared_ptr<HorizonAlign<PCurXYZHSV>> c_HACalib_;   /**< 水平校准 */
    std::deque<common_msgs::sensorgps::ConstPtr> c_gpsDeque_; /**< gps */
    std::deque<sensor_msgs::PointCloud2ConstPtr> c_pointcloudDeque_; /**< 点云 */
    std::mutex c_gpsMutex;
    std::mutex c_pointcloudMutex;
    int c_iTimeNow_; /**< 初始化第一帧雷达时间戳 */
    nav_msgs::Path c_pathMsg_, c_pathOutEndMsg_, c_pathPredMsg_, c_pathWheelMsg_,
        c_pathWheelRawMsg_, c_pathCurbMsg_;       /**< 水平校准 */
    nav_msgs::Path c_pathOdomMsg_;                /**< odometry path 消息 */
    nav_msgs::Path c_pathHPOdomMsg_;              /**< odometry 预估path 消息 */
    LaserTransform<PCurXYZHSV> c_stCurPCTranser_; /**< 地图转换 */
    std::thread odom;                             /**< odometry 线程 */
    std::thread m_pointcloudProcessThread;  
    std::thread netAppTh;                         /**< 网络线程 */
    std::thread webAppTh;                         /**< Web线程 */
    std::thread location;                         /**< location 线程 */
    std::thread m_pDataPreprocess;                         /**< 数据预处理 线程 */
    bool c_bHasSend_ = false;                     /**< 允许地图刷新标志位 */
    int c_iSendMap_ = 0;                          /**< 空闲模式刷新地图标志位 */
    bool c_bGetKey_ = true;                       /**< 监控键盘开始标志位 */

    s_PCloud c_pcRawOut[64];  //参数3
    s_PCloud l_pcMidOut;      // 参数4
    std::vector<std::vector<pcl::PointXYZI>> pointsMatrix;
    std::vector<std::vector<VelodynePointXYZIRT>> c_vlpPointsMatrix;
    bool pointsSetMatrix[64][1800];
    bool m_pointsSetMatrixVLP[16][1800];
    int l_frameCountTemp = 0;
    bool c_isProcessedGPS = false; 
    common_msgs::sensorgps c_firstSensorgps;
    SensorAxisTransformation c_sensorAxisTransformation;
    Common m_common;

    boost::shared_ptr<DataPreprocess> c_DataPreprocessPtr;             // 传感器数据预处理

    std::vector<Point> m_slamPolygon;
    double m_position1_lon;
    double m_position1_lat;
    double m_position2_lon;
    double m_position2_lat;
    double m_position3_lon;
    double m_position3_lat;
    double m_position4_lon;
    double m_position4_lat;




    /**
     * An enum type
     */
    enum KeyValue {
        Key_Space = 32 /**< 空格键 */,
        Key_C = 67 /**< C键 */,
        Key_c = 99 /**< c键 */,
        Key_D = 68 /**< D键 */,
        Key_d = 100 /**< d键 */,
        Key_E = 69 /**< E键 */,
        Key_e = 101 /**< e键 */,
        Key_1 = 49 /**< 1键 */,
        Key_Add = 43 /**< +键 */,
        Key_Minus = 45 /**< -键 */,
        Key_R = 82 /**< R键 */,
        Key_r = 114 /**< r键 */,
        Key_Well = 35 /**< #键 */,
        Key_H = 72 /**< H键 */,
        Key_h = 104 /**< h键 */
    };

    /**
     * @brief 开启多雷达标定
     *
     * @code
     *
     * @endcode
     * @return [true] \n
     * 多雷达标定启动成功
     * @code
     *
     * @endcode
     * @return [false] \n
     * 多雷达标定启动失败
     *
     */
    bool startMLidarCalib()
    {
        // 已经启动
        if (c_sysParam_->m_calib.m_MLCalib.m_bIsStartCalib)
            return true;

        // 标定前强制使用者自行切换至定位模式，以保证定位模式切换无误
        if (c_sysParam_->m_iWorkMode == WorkMode::LocatMode)
        {
            LOGFAE(WINFO, "多雷达标定启动...");
            c_sysParam_->m_calib.m_MLCalib.m_bIsStartCalib = true;
            c_sysParam_->m_iWorkMode = WorkMode::StandByMode;
            setWorkMode(0);
            c_MLCalib_.reset(new MLidarCalib<PCurXYZHSV>(*c_sysParam_, offsetof(PCurXYZHSV, s)));
            c_MLCalib_->setKeyFrameOutputCallback(
                boost::bind(&WanJiSLAM::locationCallback, this, _1));
            c_MLCalib_->setRawFeatureCallBack(c_preProcess_->getFEOutputCallback());
            c_MLCalib_->setRawPoseCallBack(c_location_->getLocationOutputCb());
            c_preProcess_->setFEOutputCallback(
                boost::bind(&MLidarCalib<PCurXYZHSV>::inputFeatureCallBack, c_MLCalib_, _1, _2));
            c_location_->setLocationOutputCb(
                boost::bind(&MLidarCalib<PCurXYZHSV>::inputPoseTwistCallBack, c_MLCalib_, _1));
            // 开始运作
            c_sysParam_->m_iWorkMode = WorkMode::LocatMode;
            // 切换定位失败 还原至空闲状态
            if (!setWorkMode(0))
            {
                c_sysParam_->m_iWorkMode = WorkMode::StandByMode;
                setWorkMode(0);
                if (c_MLCalib_)
                {
                    c_MLCalib_->shutdown();
                    c_MLCalib_ = nullptr;
                }
                c_preProcess_->setFEOutputCallback(c_MLCalib_->getRawFeatureCallBack());
                c_location_->setLocationOutputCb(c_MLCalib_->getRawPoseCallBack());
                c_sysParam_->m_calib.m_MLCalib.m_bIsStartCalib = false;
                LOGFAE(WWARN, "多雷达标定启动失败 | 无法至定位模式,自动切换至空闲模式");
                return false;
            }
            LOGFAE(WINFO, "多雷达标定启动成功！");
            return true;
        }
        LOGFAE(WWARN, "多雷达标定启动失败 | 请将工作模式切换至定位模式");
        return false;
    }

    /**
     * @brief 关闭多雷达标定
     *
     * @param p_iWorkMode
     *
     */
    void stopMLidarCalib(int p_iWorkMode)
    {
        // 暂停运行
        c_sysParam_->m_iWorkMode = WorkMode::StandByMode;
        setWorkMode(0);
        // 如果设置保存结果
        if (c_MLCalib_)
        {
            c_MLCalib_->shutdown();
            c_preProcess_->setFEOutputCallback(c_MLCalib_->getRawFeatureCallBack());
            c_location_->setLocationOutputCb(c_MLCalib_->getRawPoseCallBack());
            c_MLCalib_ = nullptr;
        }
        // 开始运作
        c_sysParam_->m_iWorkMode = p_iWorkMode;
        setWorkMode(0);
        // 清空标定参数
        c_sysParam_->m_calib.m_MLCalib.m_bIsStartCalib = false;
        c_sysParam_->m_calib.m_MLCalib.resetPoses(0);
    }

    /**
     * @brief 开启水平校准
     *
     *
     */
    void startHorizonAlign()
    {
        // 已经启动
        if (c_sysParam_->m_calib.m_HACalib.m_bIsStartCalib)
            return;
        c_sysParam_->m_calib.m_HACalib.m_bIsStartCalib = true;
        c_sysParam_->m_iWorkMode = WorkMode::StandByMode;
        setWorkMode(0);
        c_HACalib_.reset(
            new HorizonAlign<PCurXYZHSV>(*c_sysParam_,
                                         c_sysParam_->m_calib.m_HACalib.m_iWorkLidarID,
                                         c_sysParam_->m_calib.m_HACalib.m_iMinTimeInit));
        c_HACalib_->setRawFeatureCallBack(c_preProcess_->getFEOutputCallback());
        c_HACalib_->setPubFeatureCallBack(boost::bind(&WanJiSLAM::odomCallback, this, _1));
        c_preProcess_->setFEOutputCallback(
            boost::bind(&HorizonAlign<PCurXYZHSV>::inputFeatureCallBack, c_HACalib_, _1, _2));
        c_HACalib_->start();
    }

    /**
     * @brief 关闭水平校准
     *
     *
     */
    void stopHorizonAlign()
    {
        // 如果设置保存结果
        if (c_HACalib_)
        {
            c_preProcess_->setFEOutputCallback(c_HACalib_->getRawFeatureCallBack());
            c_HACalib_->shutdown();
            c_HACalib_ = nullptr;
        }
        c_sysParam_->m_calib.m_HACalib.m_bIsStartCalib = false;
    }
    /**
     * @brief 根据状态,判断是否传递数据给后方算法
     * @param p_fEPair 当前数据
     * @return true 传递数据
     * @return false 不传递数据
     * @details standby模式下不传递,当使用imu时,必须等待imu初始化成功后再传递,不使用imu时,直接传递
     *
     */
    bool sendDataToQueue_(const FEATURE_PAIR_PTR& p_fEPair)
    {
        if (WorkMode::StandByMode == c_sysParam_->m_iWorkMode)
            return false;
        if (!c_sysParam_->m_bIsUseIMU)
            return true;
        else if (static_cast<int>(p_fEPair->m_imuData.status())
                 > static_cast<int>(IMUData::IMUStatus::BiasDone))
            return true;
        return false;
    }
    /**
     * @brief 预处理回调 将点云压入odometry队列
     *
     * @param p_fEPair
     *
     */
    void odomCallback(FEATURE_PAIR_PTR& p_fEPair)
    {
        static bool init = false;
        if (!init)
        {
            c_iTimeNow_ = p_fEPair->m_tsSyncTime;
            init = true;
        }
        //非standby模式下,若启用imu则需等待imu初始化完成后,再向后方传递点云
        if (sendDataToQueue_(p_fEPair))
            c_featureQueue_.push(p_fEPair);
        if (c_sysParam_->m_fae.m_bPrintfTimeLog)
            LOGW(WINFO,
                 "{} 雷达 [-] 帧 {} Od at {} {}",
                 WJLog::getWholeSysTime(),
                 p_fEPair->m_uiScanFrame,
                 p_fEPair->m_tsSyncTime,
                 p_fEPair->m_tsWallTime);
        if (WorkMode::StandByMode == c_sysParam_->m_iWorkMode)
        {
            if (c_iSendMap_ <= 5)
            {
                showClearPointCloud<PMapXYZ>(outputMap_);
                c_iSendMap_++;
            }
            if (c_sysParam_->m_bSendCurPC && outputCurr_.getNumSubscribers() > 0)
            {
                if (c_sysParam_->m_bDebugModel)
                {
                    pcl::PointCloud<pcl::PointXYZHSV>::Ptr l_pOutPC(
                        new pcl::PointCloud<pcl::PointXYZHSV>());
                    transPcToDebugMode<PCurXYZHSV>(p_fEPair, l_pOutPC, true);
                    showPointCloud<pcl::PointXYZHSV>(outputCurr_, l_pOutPC);
                }
                else
                {
                    if (c_sysParam_->m_iViewMode == 0)
                        showPointCloud<PCurXYZHSV>(outputCurr_, p_fEPair->allPC);
                    else
                        showPointCloud<PCurXYZHSV>(outputCurr_, p_fEPair->fourth);
                }
                showPointCloud<PCurXYZHSV>(outputCurMarks, p_fEPair->third);
            }
        }
    }

    /**
     * @brief odometry关键帧回调
     *
     * @tparam <PcType>
     * @param pc
     *
     */
    template <typename PcType>
    void pcCallbackOdom(std::vector<boost::shared_ptr<KEYFRAME<PcType>>> pc)
    {
        // 如果不是建图模式,则不使用odom发送地图
        if (c_sysParam_->m_iWorkMode != WorkMode::InitMapMode
            && c_sysParam_->m_iWorkMode != WorkMode::ContMapMode)
        {
            return;
        }
        pcl::PointCloud<PCurXYZHSV>::Ptr l_pcNewCloud(new pcl::PointCloud<PCurXYZHSV>());
        pcl::copyPointCloud(*pc[0]->m_pFeature->allPC, *l_pcNewCloud);
        c_stCurPCTranser_.transformCloudPoints(
            pc[0]->m_Pose.m_quat, pc[0]->m_Pose.m_trans, l_pcNewCloud, l_pcNewCloud);
        // 不论是不是debug模式,均mergemap
        // bool l_bMapUpdate = mergeMap(l_pcNewCloud);

        // 建图且非debug情况下发送
        if (c_sysParam_->m_bDebugModel)
            return;

        if (c_sysParam_->m_bSendCurPC && outputCurr_.getNumSubscribers() > 0)
            showPointCloud<PCurXYZHSV>(outputCurr_, l_pcNewCloud);

        // if (c_sysParam_->m_bSendMap && l_bMapUpdate && outputMap_.getNumSubscribers() > 0)
        //     showPointCloud<PCurXYZHSV>(outputMap_, c_pcMergeNewCloud_);
    }

    /**
     * @brief 位姿运动预估框发布
     *
     * @param p_stBox
     *
     */
    void showPredPoseBox(pcl::PointCloud<PMapXYZ>::Ptr p_stBox)
    {
        visualization_msgs::Marker lineList;
        lineList.header.frame_id = "world";
        lineList.lifetime = ros::Duration();
        lineList.ns = "lines";
        lineList.action = visualization_msgs::Marker::DELETEALL;
        lineList.action = visualization_msgs::Marker::ADD;
        lineList.pose.orientation.w = 1.0;
        lineList.type = visualization_msgs::Marker::LINE_LIST;
        lineList.scale.x = 0.05;
        lineList.id = 1;
        lineList.color.r = 1.0;
        lineList.color.g = 0.0;
        lineList.color.b = 0.0;
        lineList.color.a = 1.0;

        for (uint32_t i = 0; i < p_stBox->points.size(); i++)
        {
            geometry_msgs::Point p;
            p.x = p_stBox->points[i].x;
            p.y = p_stBox->points[i].y;
            p.z = p_stBox->points[i].z;
            lineList.points.push_back(p);

            if (i != 0)
                lineList.points.push_back(p);
            if (i == p_stBox->points.size() - 1)
            {
                p.x = p_stBox->points[0].x;
                p.y = p_stBox->points[0].y;
                p.z = p_stBox->points[0].z;
                lineList.points.push_back(p);
            }
        }
        lineList.header.stamp = ros::Time::now();
        outPredBox_.publish(lineList);
    }

    /**
     * @brief 轮式里程计位姿回调函数
     *
     * @param p_vPredPoseList
     * @param p_vPrecPoseList
     *
     */
    void ofPoseCallback(std::vector<s_POSE6D> p_vPredPoseList,
                        std::vector<s_POSE6D> p_vPrecPoseList)
    {
        for (uint32_t i = 0; i < p_vPredPoseList.size(); i++)
        {
            showNavPose(outputPoseWheelRaw_, p_vPredPoseList[i]);
            showNavPath(outputPathWheelRaw_, c_pathWheelRawMsg_, p_vPredPoseList[i]);
        }
        for (uint32_t i = 0; i < p_vPrecPoseList.size(); i++)
        {
            showNavPose(outputPoseWheel_, p_vPrecPoseList[i]);
            showNavPath(outputPathWheel_, c_pathWheelMsg_, p_vPrecPoseList[i]);
        }
    }

    /**
     * @brief 关键帧采样处理
     *
     * @param newMap
     * @code
     *
     * @endcode
     * @return [true] \n
     * 需要刷新显示
     * @code
     *
     * @endcode
     * @return [false] \n
     * 不需要刷新显示
     *
     */
    bool mergeMap(pcl::PointCloud<PCurXYZHSV>::Ptr& newMap)
    {
        bool l_bMapUpdate = false;
        static long l_lLastPubTime = 0;
        static long l_lThisTime = 0;
        l_lThisTime = clock();
        if (l_lThisTime - l_lLastPubTime > 2000)
        {
            pcl::VoxelGrid<PCurXYZHSV> l_downSizeFilter;
            l_downSizeFilter.setLeafSize(0.4, 0.4, 0.8);
            *c_pcMergeNewCloud_ += *newMap;
            l_downSizeFilter.setInputCloud(c_pcMergeNewCloud_);
            l_downSizeFilter.filter(*c_pcMergeNewCloud_);
            l_bMapUpdate = true;
            l_lLastPubTime = l_lThisTime;
        }
        // 用于外部非实时发送Map
        return l_bMapUpdate;
    }

    /**
     * @brief 显示点云
     *
     * @tparam <P>
     * @param pub
     * @param pc
     *
     */
    template <typename P>
    void showPointCloud(ros::Publisher& pub, typename pcl::PointCloud<P>::Ptr& pc)
    {
        if (pub.getNumSubscribers() < 1)
            return;
        sensor_msgs::PointCloud2 msgPC;
        pcl::toROSMsg(*pc, msgPC);
        msgPC.header.frame_id = "world";
        msgPC.header.stamp = ros::Time::now();
        pub.publish(msgPC);
    }
    /**
     * @brief 空闲模式下发送空地图
     *
     * @tparam <P>
     * @param pub
     *
     */
    template <typename P> void showClearPointCloud(ros::Publisher& pub)
    {
        if (pub.getNumSubscribers() < 1)
            return;
        pcl::PointCloud<P> l_clearPc;
        sensor_msgs::PointCloud2 msgPC;
        pcl::toROSMsg(l_clearPc, msgPC);
        msgPC.header.frame_id = "world";
        msgPC.header.stamp = ros::Time::now();
        pub.publish(msgPC);
    }
    /**
     * @brief 显示位姿
     *
     * @param pub
     * @param pose
     *
     */
    void showNavPose(ros::Publisher& pub, s_POSE6D& pose)
    {
        if (pub.getNumSubscribers() < 1)
            return;
        geometry_msgs::PoseStamped lpose;
        lpose.header.frame_id = "world";
        lpose.header.stamp = ros::Time::now();
        lpose.pose.orientation.w = pose.m_quat.w();
        lpose.pose.orientation.x = pose.m_quat.x();
        lpose.pose.orientation.y = pose.m_quat.y();
        lpose.pose.orientation.z = pose.m_quat.z();
        lpose.pose.position.x = pose.m_trans.x();
        lpose.pose.position.y = pose.m_trans.y();
        lpose.pose.position.z = pose.m_trans.z();
        if (pub.getNumSubscribers() > 0)
        {
            pub.publish(lpose);
        }
    }

    /**
     * @brief 显示path
     *
     * @param pub
     * @param path
     * @param pose
     *
     */
    void showNavPath(ros::Publisher& pub, nav_msgs::Path& path, s_POSE6D& pose)
    {
        if (pub.getNumSubscribers() < 1)
            return;
        geometry_msgs::PoseStamped lpose;
        lpose.header.frame_id = "world";
        lpose.header.stamp = ros::Time::now();
        lpose.pose.orientation.w = pose.m_quat.w();
        lpose.pose.orientation.x = pose.m_quat.x();
        lpose.pose.orientation.y = pose.m_quat.y();
        lpose.pose.orientation.z = pose.m_quat.z();
        lpose.pose.position.x = pose.m_trans.x();
        lpose.pose.position.y = pose.m_trans.y();
        lpose.pose.position.z = pose.m_trans.z();
        path.header.frame_id = "world";
        path.header.stamp = ros::Time::now();
        path.poses.push_back(lpose);
        if (pub.getNumSubscribers() > 0)
        {
            pub.publish(path);
        }
    }

    /**
     * @brief odometry将关键帧压入location队列
     *
     * @param p_keyFrame
     *
     */
    void locationCallback(KEYFRAME_PTR p_keyFrame)
    {
        if (c_sysParam_->m_fae.m_bPrintfTimeLog)
            LOGW(WINFO,
                 "{} 雷达 [-] 帧 {} Lo at {} {}",
                 WJLog::getWholeSysTime(),
                 p_keyFrame->m_pFeature->m_uiScanFrame,
                 p_keyFrame->m_pFeature->m_tsSyncTime,
                 p_keyFrame->m_pFeature->m_tsWallTime);

        c_keyFramesQueue_.push(p_keyFrame);
    }
    /**
     * @brief location定位完整更新里程计位姿
     *
     * @param p_pose
     *
     */
    void locationOutputCb(s_PoseWithTwist& p_pose)
    {
        if (p_pose.m_bFlag == PoseStatus::ContinuePose || p_pose.m_bFlag == PoseStatus::InitialPose
            || p_pose.m_bFlag == PoseStatus::CurbPose)
        {
            if (c_sysParam_->m_posCheck.m_bUseOdom)
                c_wheelOdom_->renewPrecisionPose(p_pose);
        }
        else
        {
            // 重置
        }
        c_odom_->renewPrecisionPose(p_pose);
    }
    /**
     * @brief 将点云转换成debug下显示
     *
     * @tparam <P>
     * @param pc
     * @param pcout
     * @param p_bCurrFrame
     *
     */
    template <typename P>
    void transPcToDebugMode(boost::shared_ptr<FEATURE_PAIR<P>> pc,
                            pcl::PointCloud<pcl::PointXYZHSV>::Ptr& pcout,
                            bool p_bCurrFrame = false)
    {
        if (!c_sysParam_->m_bDebugModel)
            return;
        int offset_corn = 0;
        int offset_surf = 0;
        int offset_UnsSurf = 0;
        int offset_curb = 0;
        pcout->clear();
        typename pcl::PointCloud<P>::Ptr l_outPc(new pcl::PointCloud<P>());
        *l_outPc = *pc->first;
        offset_corn = l_outPc->points.size();
        *l_outPc += *pc->second;
        // 当前帧标记采样点
        if (p_bCurrFrame)
        {
            offset_surf = offset_corn + pc->m_iSample2ndSize;
            offset_UnsSurf = l_outPc->points.size();
        }
        else
        {
            offset_surf = l_outPc->points.size();
            offset_UnsSurf = offset_surf;
        }
        *l_outPc += *pc->fourth;
        offset_curb = l_outPc->points.size();
        pcl::copyPointCloud(*l_outPc, *pcout);
        for (int i = 0; i < offset_corn; i++)
            pcout->points[i].h = 1;
        for (int i = offset_corn; i < offset_surf; i++)
            pcout->points[i].h = 2;
        for (int i = offset_surf; i < offset_UnsSurf; i++)
            pcout->points[i].h = 3;
        for (int i = offset_UnsSurf; i < offset_curb; i++)
            pcout->points[i].h = 4;
    }
    /**
     * @brief 显示点云与地图
     *
     * @tparam <PcType>
     * @tparam <MapType>
     * @param pc
     * @param map
     *
     */
    template <typename PcType, typename MapType>
    void showPC(boost::shared_ptr<KEYFRAME<PcType>> pc,
                boost::shared_ptr<KEYFRAME<MapType>> map = nullptr)
    {
        // 如果是debug模式 或者 定位模式 则进入发送程序
        // if (getSendInfoState())
        //     return;
        if (c_sysParam_->m_iWorkMode == WorkMode::InitMapMode
            || c_sysParam_->m_iWorkMode == WorkMode::ContMapMode)
        {
            if (c_sysParam_->m_bDebugModel)
            {
                pcl::PointCloud<pcl::PointXYZHSV>::Ptr l_pOutPC(
                    new pcl::PointCloud<pcl::PointXYZHSV>());
                if (c_sysParam_->m_bSendCurPC && pc && outputCurr_.getNumSubscribers() > 0)
                {
                    transPcToDebugMode<PcType>(pc->m_pFeature, l_pOutPC, true);
                    showPointCloud<pcl::PointXYZHSV>(outputCurr_, l_pOutPC);
                }
                if (c_sysParam_->m_bSendMap && map && outputMap_.getNumSubscribers() > 0)
                {
                    transPcToDebugMode<MapType>(map->m_pFeature, l_pOutPC);
                    showPointCloud<pcl::PointXYZHSV>(outputMap_, l_pOutPC);
                    c_bHasSend_ = false;
                }
                showPointCloud<MapType>(outputMarksMap, map->m_pFeature->third);
                showPointCloud<PcType>(outputCurMarks, pc->m_pFeature->third);
            }
            else
            {
                typename pcl::PointCloud<PcType>::Ptr outPC(new pcl::PointCloud<PcType>());
                c_stCurPCTranser_.transformCloudPoints(
                    pc->m_Pose.m_quat, pc->m_Pose.m_trans, pc->m_pFeature->allPC, outPC);
                if (pc->m_bIsKeyFrame)
                    mergeMap(outPC);
                if (c_sysParam_->m_bSendMap && c_pcMergeNewCloud_ && map
                    && outputMap_.getNumSubscribers() > 0)
                {
                    if (c_sysParam_->m_iViewMode == 0)
                        showPointCloud<MapType>(outputMap_, c_pcMergeNewCloud_);
                    else
                        showPointCloud<MapType>(outputMap_, map->m_pFeature->fourth);
                }
            }
        }
        else
        {
            if (c_sysParam_->m_bDebugModel)
            {
                pcl::PointCloud<pcl::PointXYZHSV>::Ptr l_pOutPC(
                    new pcl::PointCloud<pcl::PointXYZHSV>());
                if (c_sysParam_->m_bSendCurPC && pc && outputCurr_.getNumSubscribers() > 0)
                {
                    transPcToDebugMode<PcType>(pc->m_pFeature, l_pOutPC, true);
                    showPointCloud<pcl::PointXYZHSV>(outputCurr_, l_pOutPC);
                }
                if (c_sysParam_->m_bSendMap && map && outputMap_.getNumSubscribers() > 0)
                {
                    transPcToDebugMode<MapType>(map->m_pFeature, l_pOutPC);
                    showPointCloud<pcl::PointXYZHSV>(outputMap_, l_pOutPC);
                    c_bHasSend_ = false;
                }
                showPointCloud<MapType>(outputMarksMap, map->m_pFeature->third);
                showPointCloud<PcType>(outputCurMarks, pc->m_pFeature->third);
            }
            else
            {
                typename pcl::PointCloud<PcType>::Ptr outPC(new pcl::PointCloud<PcType>());
                c_stCurPCTranser_.transformCloudPoints(
                    pc->m_Pose.m_quat, pc->m_Pose.m_trans, pc->m_pFeature->allPC, outPC);
                if (c_sysParam_->m_bSendCurPC && pc && outputCurr_.getNumSubscribers() > 0)
                {
                    if (c_sysParam_->m_iViewMode == 0)
                        showPointCloud<PcType>(outputCurr_, outPC);
                    else
                        showPointCloud<PcType>(outputCurr_, outPC);
                }
                if (c_sysParam_->m_bSendMap && map && outputMap_.getNumSubscribers() > 0)
                {
                    if (!c_bHasSend_)
                    {
                        c_bHasSend_ = true;
                        if (c_sysParam_->m_iViewMode == 0)
                            showPointCloud<MapType>(outputMap_, map->m_pFeature->allPC);
                        else
                            showPointCloud<MapType>(outputMap_, map->m_pFeature->fourth);
                    }
                }
                else
                {
                    c_bHasSend_ = false;
                }
            }
        }
    }
    /**
     * @brief location完成之后，发送点云与地图
     *
     * @tparam <PcType>
     * @tparam <MapType>
     * @param pc
     * @param map
     *
     */
    template <typename PcType, typename MapType>
    void pcCallback(boost::shared_ptr<KEYFRAME<PcType>> pc,
                    boost::shared_ptr<KEYFRAME<MapType>> map = nullptr)
    {
        boost::shared_ptr<KEYFRAME<PcType>> l_pc(new KEYFRAME<PcType>());
        boost::shared_ptr<KEYFRAME<MapType>> l_map(new KEYFRAME<MapType>());
        *l_pc = *pc;
        *l_map = *map;
        if (pc->m_bIsKeyFrame)
            l_pc->m_bIsKeyFrame = pc->m_bIsKeyFrame;
        std::thread showpc(&WanJiSLAM::showPC<PcType, MapType>, this, l_pc, l_map);
        showpc.detach();
        if (c_dataBak_)
            c_dataBak_->bakData(l_pc);
    }

    /**
     * @brief location完成之后，发送位姿
     *
     * @param pose
     *
     */
    void poseCallback(std::vector<s_POSE6D> pose)
    {
        // Web端显示不区分debug
        if (pose.size() >= 2)
            showWebPose(*(pose.begin() + 0), *(pose.begin() + 1));
        if (getSendInfoState())
            return;
        if (pose.size() >= 1)
        {
            showNavPose(outputPose_, *(pose.begin() + 0));
            showNavPath(outputPath_, c_pathMsg_, *(pose.begin() + 0));
        }
        if (pose.size() >= 2)
        {
            showNavPose(outputPoseOutEnd_, *(pose.begin() + 1));
            showNavPath(outputPathOutEnd_, c_pathOutEndMsg_, *(pose.begin() + 1));
        }
        if (pose.size() >= 3)
        {
            showNavPose(outputPosePred_, *(pose.begin() + 2));
            showNavPath(outputPathPred_, c_pathPredMsg_, *(pose.begin() + 2));
        }
        if (pose.size() >= 4)
        {
            showNavPose(outputPoseCurb_, *(pose.begin() + 3));
            showNavPath(outputPathCurb_, c_pathCurbMsg_, *(pose.begin() + 3));
        }
    }

    /**
     * @brief 里程计完成之后，发送位姿
     *
     * @param pose
     *
     */
    void poseCallbackOdom(std::vector<s_POSE6D> pose)
    {
        if (getSendInfoState())
        {
            showNavPose(outputPose_, *(pose.begin() + 1));
            showNavPath(outputPath_, c_pathMsg_, *(pose.begin() + 1));
        }
        else if (c_sysParam_->m_bDebugModel)
        {
            showNavPath(outputPathOdom_, c_pathOdomMsg_, *(pose.begin()));
            showNavPath(outputPathHPOdom_, c_pathHPOdomMsg_, *(pose.begin() + 1));
        }
    }

    /**
     * @brief 给网页发布位姿
     *
     * @param poseLidar
     * @param poseAGV
     *
     */
    void showWebPose(s_POSE6D& poseLidar, s_POSE6D& poseAGV)
    {
        if (c_sysParam_->m_iWorkMode == WorkMode::LocatMode
            || c_sysParam_->m_iWorkMode == WorkMode::UpdateMapMode)
        {
            if (c_wjNetWeb_)
                c_wjNetWeb_->sendWebPose(poseLidar, poseAGV);
        }
    }

    /**
     * @brief 执行键盘、Web指令
     *
     * @param p_cmd
     * @code
     *
     * @endcode
     * @return [true] \n
     * 执行成功
     * @code
     *
     * @endcode
     * @return [false] \n
     * 执行失败
     *
     */
    bool setWorkMode(int p_cmd)
    {
        paramInit_();
        bool res = false;
        const std::string l_sWorkMode[5] = {
            "空闲模式", "初始建图", "连续建图", "连续定位", "更新地图"};
        const std::string l_sParamMode[4] = {"室内环境", "室外环境", "复杂环境", "测试环境"};
        switch (p_cmd)
        {
            // 切换工作模式
            case 0:
                LOGFAE(WINFO,
                       "正在切换模式 [{}]&[{}]",
                       l_sWorkMode[c_sysParam_->m_iWorkMode],
                       l_sParamMode[c_sysParam_->m_nParam_mode]);
                // initialSetPose();
                resetParam_();
                res = c_preProcess_->setModeConfig(c_sysParam_->m_iWorkMode,
                                                   c_sysParam_->m_nParam_mode);
                res &= c_location_->setWorkMode(c_sysParam_->m_iWorkMode);
                res &= c_odom_->setWorkMode(c_sysParam_->m_iWorkMode);
                c_wheelOdom_->start();
                if (res)
                    LOGFAE(WINFO, "切换模式 [{}] 完成!", l_sWorkMode[c_sysParam_->m_iWorkMode]);
                else
                    LOGFAE(WWARN, "切换模式 [{}] 失败!", l_sWorkMode[c_sysParam_->m_iWorkMode]);
                if (c_MLCalib_)
                    res &= c_MLCalib_->setWorkMode(c_sysParam_->m_iWorkMode);
                if (c_location_->getMap())
                    *c_pcMergeNewCloud_ = *c_location_->getMap();
                // 允许刷新地图
                c_bHasSend_ = false;
                c_iSendMap_ = 0;
                break;
            // 储存地图
            case 1:
                c_location_->saveMap();
                res = true;
                break;
            // 刷新地图显示
            case 2:
                c_bHasSend_ = false;
                res = true;
                break;
            // 水平校准
            case 3:
                startHorizonAlign();
                res = true;
                break;
            // 标定
            case 4: res = startMLidarCalib(); break;
            // 储存参数
            case 5:
            {
                Param l_param;
                std::string path = ros::package::getPath("wj_slam");
                res = l_param.saveSysParam(path);
                break;
            }
            // 设置雷达参数
            case 6:
            {
                // 标定仍在运行时禁止改变外参,否则引发BUG
                if (nullptr == c_MLCalib_ || nullptr == c_HACalib_)
                {
                    c_preProcess_->resetTransToBase();
                    res = true;
                }
                else
                    res = false;
                break;
            }
            case 7:
            {
                stopMLidarCalib(c_sysParam_->m_iWorkMode);
                res = true;
                break;
            }
            case 8:
            {
                stopHorizonAlign();
                res = true;
                break;
            }
            // 设置雷达使能
            case 9:
            {
                //标定仍在运行时禁止改变外参,否则引发BUG
                if (nullptr == c_MLCalib_ || nullptr == c_HACalib_)
                    c_preProcess_->resetLidarEnableStatus();
                res = true;
                break;
            }
            // 设置位姿
            case 10:
            {
                LOGFAE(WINFO, "正在设置位姿");
                initialSetPose();
                c_pathMsg_.poses.clear();
                res = true;
                // 允许刷新地图
                c_bHasSend_ = false;
                break;
            }
            default: break;
        }
        changeSendInfoState();
        return res;
    }
    /**
     * @description: 根据工作模式\Debug模式确定地图\pose发送状态
     * @param {*}
     * @return {*}
     * @other:
     */
    void changeSendInfoState(void)
    {
        // 如果是debug模式,发送所有可见
        if (c_sysParam_->m_bDebugModel)
        {
            outputPathOdom_ = c_node_.advertise<nav_msgs::Path>("wanji_odom", 1);
            outputPathHPOdom_ = c_node_.advertise<nav_msgs::Path>("wanji_HPodom", 1);
        }
        else  // 如果是release模式,则不发送高频里程计
        {
            outputPathOdom_.shutdown();
            outputPathHPOdom_.shutdown();
        }
    }
    /**
     * @brief 判断当前点云与地图显示模式
     *
     * @code
     *
     * @endcode
     * @return [true] \n
     * 非debug且建图模式下
     * @code
     *
     * @endcode
     * @return [false] \n
     * 非debug且非建图模式下
     *
     */
    bool getSendInfoState(void)
    {
        // 如果是release模式并且是建图模式,则使用odom发送,否则均使用loc值发送
        if (!c_sysParam_->m_bDebugModel
            && (c_sysParam_->m_iWorkMode == WorkMode::InitMapMode
                || c_sysParam_->m_iWorkMode == WorkMode::ContMapMode))
        {
            return true;
        }
        return false;
    }

    /**
     * @brief 设定位姿
     *
     *
     */
    void initialSetPose()
    {
        if (WorkMode::StandByMode == c_sysParam_->m_iWorkMode)
            return;
        // 暂停数据流
        c_wheelOdom_->stop();
        if (c_odom_)
            c_odom_->stop();
        // 等待定位进入空闲状态（数据流缓冲结束）
        if (c_location_)
            c_location_->stop();

        // 虚拟位姿的时间设为当前位姿，位置为设定位姿
        while ((!c_location_->isStop()) || (!c_odom_->isStop()))
        {
            sleepMs(1);
        }
        if (c_location_)
            c_location_->start();

        s_PoseWithTwist l_pose;
        l_pose.m_Pose = c_sysParam_->m_pos.m_stSetPose;
        l_pose.m_bFlag = PoseStatus::SettingPose;
        l_pose.m_Pose.m_bFlag = PoseStatus::SettingPose;
        // 设定位姿假设为工作流上最新时间点
        // l_pose.m_iRecvTimestamp = getTimeDiffMs(getTimeNow(),
        // c_sysParam_->m_time.m_sTimeSetSync); l_pose.m_iTimetamp = l_pose.m_iRecvTimestamp;
        // 初始化其他里程计
        c_wheelOdom_->start();
        // c_wheelOdom_->renewPrecisionPose(l_pose);
        if (c_odom_)
        {
            c_odom_->start();
            // c_odom_->renewPrecisionPose(l_pose);
        }
        LOGFAE(WINFO, "SLAM设定位置!");
    }

    /**
     * @brief 根据键盘键入指令执行操作
     *
     * @param key
     *
     */
    void changeState(int key)
    {
        static u_char KeyNum = 0;
        // printf("get Key : %d\n", key);
        switch (key)
        {
            case KeyValue::Key_c: /* 计算标定参数 */
                KeyNum = 0;
                startMLidarCalib();
                break;
            case KeyValue::Key_C: /* 计算标定参数 */
                KeyNum = 0;
                stopMLidarCalib(c_sysParam_->m_iWorkMode);
                break;
            case KeyValue::Key_h: /* 计算水平偏转 */
                KeyNum = 0;
                startHorizonAlign();
                break;
            case KeyValue::Key_H: /* 计算水平偏转 */
                KeyNum = 0;
                stopHorizonAlign();
                break;
            case KeyValue::Key_D: /* Debug模式开关，先按D/d */
                if (KeyNum & 0xF0)
                {
                    KeyNum |= 0x01;
                }
                break;
            case KeyValue::Key_d: /* Debug模式开关，先按D/d */
                if (KeyNum & 0xF0)
                {
                    KeyNum |= 0x01;
                }
                break;
            case KeyValue::Key_E: /* Debug模式开关，再按E/e */
                if (KeyNum & 0xF1)
                {
                    c_sysParam_->m_bDebugModel = !c_sysParam_->m_bDebugModel;
                    changeSendInfoState();
                    printf("changeMode !Debug \n");
                }
                KeyNum = 0;
                break;
            case KeyValue::Key_e:
                if (KeyNum & 0xF1)
                {
                    c_sysParam_->m_bDebugModel = !c_sysParam_->m_bDebugModel;
                    changeSendInfoState();
                    printf("changeMode !Debug \n");
                }
                KeyNum = 0;
                break;
            case KeyValue::Key_Well: /* 工作模式入口 */ KeyNum = 0xf0; break;
            default: break;
        }
    }
    /**
     * @brief 扫描键盘
     *
     *
     */
    void scanKeyboard()
    {
        int in;
        struct termios new_settings;
        struct termios stored_settings;
        tcgetattr(0, &stored_settings);
        new_settings = stored_settings;
        new_settings.c_lflag &= (~ICANON);
        new_settings.c_cc[VTIME] = 0;
        tcgetattr(0, &stored_settings);
        new_settings.c_cc[VMIN] = 1;
        tcsetattr(0, TCSANOW, &new_settings);

        in = getchar();

        tcsetattr(0, TCSANOW, &stored_settings);
        changeState(in);
        c_bGetKey_ = true;
    }
    /**
     * @brief 参数初始化
     *
     *
     */
    void paramInit_()
    {
        c_pathMsg_.poses.clear();
        c_pathOutEndMsg_.poses.clear();
        c_pathWheelMsg_.poses.clear();
        c_pathCurbMsg_.poses.clear();
        c_pathOdomMsg_.poses.clear();
        c_pathHPOdomMsg_.poses.clear();
    }
    /**
     * @brief 参数重置
     *
     *
     */
    void resetParam_()
    {
        wj_slam::Param l_param;
        std::string path = ros::package::getPath("wj_slam");
        l_param.renewParamByWorkMode(path);
        c_pcMergeNewCloud_.reset(new pcl::PointCloud<PCurXYZHSV>());
    }

    void callback_wanjiPointCloud(const sensor_msgs::PointCloud2ConstPtr& msg){
        std::lock_guard<std::mutex> pointcloudLock(c_pointcloudMutex);
        c_pointcloudDeque_.emplace_back(msg);
    }

    void processWanjiPointCloud2()  // hsq
    {
        while(1){
            // std::cout<<"c_pointcloudDeque_ process size = " << c_pointcloudDeque_.size() << std::endl;
            std::lock_guard<std::mutex> pointcloudLock(c_pointcloudMutex);
            if(c_pointcloudDeque_.empty()){
                // std::cout<<"No point cloud found" << std::endl;
                continue;
            }
            sensor_msgs::PointCloud2ConstPtr curPointcloudMsg = c_pointcloudDeque_.front();
            if (!c_gpsDeque_.empty()){
                c_isProcessedGPS = processGps(curPointcloudMsg->header.stamp.toSec());
            }

            // 没有同步GPS数据，返回
            if(!c_isProcessedGPS){
                // std::cout<<"No sync gps data" << std::endl;
                continue;
            }
            
            pcl::PointCloud<pcl::PointXYZI>::Ptr in_cloud_raw(new pcl::PointCloud<pcl::PointXYZI>);
            pcl::PointCloud<pcl::PointXYZI>::Ptr in_cloud(new pcl::PointCloud<pcl::PointXYZI>);
            std::vector<int> indexs;
            pcl::fromROSMsg(*curPointcloudMsg, *in_cloud_raw);
            pcl::removeNaNFromPointCloud(*in_cloud_raw, *in_cloud, indexs);
            memset(&pointsSetMatrix[0][0], false, sizeof(bool) * 64 * 1800);
            l_frameCountTemp++;
            for (int i = 0; i < in_cloud->points.size(); ++i)
            {
                auto& point = in_cloud->points[i];
                if ((point.x == 0 && point.y == 0 && point.z == 0))
                    continue;
                if(abs(point.x) < 1.0 && abs(point.y) < 1.0)//去除牵引车附近的点
                    continue;
                float distanceXY = sqrt(point.x * point.x + point.y * point.y); // 主lidar为原点，FLU坐标系
                float pitchDegree = atan2(point.z, distanceXY) * 180.0 / M_PI;
                float yawDegree = atan2(point.x, point.y) * 180.0 / M_PI; // X轴负方向逆时针0~360度
                yawDegree += 90.0;
                if (yawDegree < 0)
                    yawDegree += 360;
                if (std::isnan(pitchDegree) || angleToChannel.count(int(round(pitchDegree * 10))) == 0)
                {  // pitchDegree不存在于map中
                    std::cout << " no pitch " << pitchDegree << ", " << int(round(pitchDegree * 10))
                            << ", " << angleToChannel.count(int(round(pitchDegree * 10)))
                            << std::endl;
                    continue;
                }

                int lineblock =
                    round(yawDegree / 0.2);  // 向上取整，将360°拓展到360/horizontal_resolution°的范围
                int channel = angleToChannel[int(round(pitchDegree * 10))];  // 查找对应的通道序号
                pointsSetMatrix[64 - channel][lineblock] = true;
                pointsMatrix[64 - channel][lineblock] = point;
            }

            boost::shared_ptr<pcl::PointCloud<pcl::PointXYZHSV>> l_pcAll(
                new pcl::PointCloud<pcl::PointXYZHSV>());  // 参数5
            boost::shared_ptr<pcl::PointCloud<pcl::PointXYZI>> l_pcAllSend(
                new pcl::PointCloud<pcl::PointXYZI>());  // 参数5
            sTimeval l_PktFirstTime;
            l_PktFirstTime.second() = curPointcloudMsg->header.stamp.sec;
            l_PktFirstTime.subsecond() = curPointcloudMsg->header.stamp.nsec / 1000.0;

            if (!c_sysParam_->m_time.isSensorTimeSet())
            {
                c_sysParam_->m_time.setSensorBaseTime(l_PktFirstTime);
                LOGFAE(WINFO,
                    "{} 雷达 [{}] 帧 {} 授时, syncTime {}",
                    WJLog::getWholeSysTime(),
                    c_sysParam_->m_lidar[0].m_sLaserName,
                    l_frameCountTemp,
                    c_sysParam_->m_time.getSensorBaseTime().data());
            }
            double receiveTimestamp = c_sysParam_->m_time.getTimeNowMs();
            double timestamp = l_PktFirstTime.getDiffMs(c_sysParam_->m_time.getSensorBaseTime());
            // std::cout << "pointsMatrix.size() " << pointsMatrix.size() << std::endl;
            for (int i = 0; i < pointsMatrix.size(); i++)
            {
                auto& linePoints = pointsMatrix[i];
                // std::cout << "linePoints.size() " << linePoints.size() << std::endl;
                for (int j = 0; j < linePoints.size(); j++)
                {
                    if (pointsSetMatrix[i][j] == false)
                        continue;
                    auto& point = linePoints[j];
                    float yawDegree = atan2(point.x, point.y) * 180.0 / M_PI;
                    yawDegree += 90.0;
                    if (yawDegree < 0)
                        yawDegree += 360;
                    // 将点放在正确的位置上
                    PRaw pointRaw;
                    pointRaw.x = point.x;
                    pointRaw.y = point.y;
                    pointRaw.z = point.z;

                    PAdd pointAdd;
                    pointAdd.intensity = point.intensity;  // TODO 修改驱动添加强度
                    pointAdd.xydist = sqrt(point.x * point.x + point.y * point.y);
                    pointAdd.depth = sqrt(point.x * point.x + point.y * point.y + point.z * point.z);
                    pointAdd.ang = yawDegree;
                    pointAdd.time = 100 * yawDegree / 360.0;
                    point.intensity = pointAdd.time;
                    // std::cout << " i = " << i << ", k = " << j
                    //           << ", x = " << point.x
                    //           << ", y = " << point.y
                    //           << ", z = " << point.z
                    //           << ", intensity = " << point.intensity
                    //           << ", pointAdd.xydist = " << pointAdd.xydist
                    //           << ", pointAdd.depth = " << pointAdd.depth
                    //           << ", pointAdd.ang = " << pointAdd.ang
                    //           << ", pointAdd.time = " << pointAdd.time
                    //           <<", c_pcRawOut[i].m_praw size = " << c_pcRawOut[i].m_praw->points.size()
                    //           <<", c_pcRawOut[i].m_praw capacity = " << c_pcRawOut[i].m_praw->points.capacity()
                    //           << std::endl;
                            
                    l_pcAllSend->push_back(point);
                    c_pcRawOut[i].m_praw->points.push_back(pointRaw);
                    c_pcRawOut[i].m_padd->push_back(pointAdd);
                }
            }


            // cout << "hsq start runFECallback: " << l_frameCountTemp << ", " << std::to_string(timestamp)
            //      << endl;
            c_preProcess_->runFECallback(static_cast<uint32_t>(0),
                                        c_pcRawOut,
                                        l_pcMidOut,
                                        l_pcAll,
                                        timestamp,  // 每个雷达数据包内部所带的时间戳
                                        receiveTimestamp,  // wireshark获取到每个雷达数据包系统时间戳
                                        l_frameCountTemp);

            // std::cout << " runFECallback end " << std::endl;
            for (int i = 0; i < 64; i++)
                c_pcRawOut[i].reset();
            showPointCloud<pcl::PointXYZI>(outputCurrTrans_, l_pcAllSend);
            c_pointcloudDeque_.pop_front();
        }
        
    }


    void processWanjiPointCloud(const sensor_msgs::PointCloud2ConstPtr& msg)  // hsq
    {
        
        if (!c_gpsDeque_.empty()){
            c_isProcessedGPS = processGps(msg->header.stamp.toSec());
        }

        // 没有同步GPS数据，返回
        if(!c_isProcessedGPS){
            return;
        }
        
        pcl::PointCloud<pcl::PointXYZI>::Ptr in_cloud_raw(new pcl::PointCloud<pcl::PointXYZI>);
        pcl::PointCloud<pcl::PointXYZI>::Ptr in_cloud(new pcl::PointCloud<pcl::PointXYZI>);
        std::vector<int> indexs;
        pcl::fromROSMsg(*msg, *in_cloud_raw);
        pcl::removeNaNFromPointCloud(*in_cloud_raw, *in_cloud, indexs);
        memset(&pointsSetMatrix[0][0], false, sizeof(bool) * 64 * 1800);
        l_frameCountTemp++;
        for (int i = 0; i < in_cloud->points.size(); ++i)
        {
            auto& point = in_cloud->points[i];
            if ((point.x == 0 && point.y == 0 && point.z == 0))
                continue;
            if(abs(point.x) < 1.0 && abs(point.y) < 1.0)//去除牵引车附近的点
                continue;
            float distanceXY = sqrt(point.x * point.x + point.y * point.y); // 主lidar为原点，FLU坐标系
            float pitchDegree = atan2(point.z, distanceXY) * 180.0 / M_PI;
            float yawDegree = atan2(point.x, point.y) * 180.0 / M_PI; // X轴负方向逆时针0~360度
            yawDegree += 90.0;
            if (yawDegree < 0)
                yawDegree += 360;
            if (std::isnan(pitchDegree) || angleToChannel.count(int(round(pitchDegree * 10))) == 0)
            {  // pitchDegree不存在于map中
                std::cout << " no pitch " << pitchDegree << ", " << int(round(pitchDegree * 10))
                          << ", " << angleToChannel.count(int(round(pitchDegree * 10)))
                          << std::endl;
                continue;
            }

            int lineblock =
                round(yawDegree / 0.2);  // 向上取整，将360°拓展到360/horizontal_resolution°的范围
            int channel = angleToChannel[int(round(pitchDegree * 10))];  // 查找对应的通道序号
            pointsSetMatrix[64 - channel][lineblock] = true;
            pointsMatrix[64 - channel][lineblock] = point;
        }

        boost::shared_ptr<pcl::PointCloud<pcl::PointXYZHSV>> l_pcAll(
            new pcl::PointCloud<pcl::PointXYZHSV>());  // 参数5
        boost::shared_ptr<pcl::PointCloud<pcl::PointXYZI>> l_pcAllSend(
            new pcl::PointCloud<pcl::PointXYZI>());  // 参数5
        sTimeval l_PktFirstTime;
        l_PktFirstTime.second() = msg->header.stamp.sec;
        l_PktFirstTime.subsecond() = msg->header.stamp.nsec / 1000.0;

        if (!c_sysParam_->m_time.isSensorTimeSet())
        {
            c_sysParam_->m_time.setSensorBaseTime(l_PktFirstTime);
            LOGFAE(WINFO,
                   "{} 雷达 [{}] 帧 {} 授时, syncTime {}",
                   WJLog::getWholeSysTime(),
                   c_sysParam_->m_lidar[0].m_sLaserName,
                   l_frameCountTemp,
                   c_sysParam_->m_time.getSensorBaseTime().data());
        }
        double receiveTimestamp = c_sysParam_->m_time.getTimeNowMs();
        double timestamp = l_PktFirstTime.getDiffMs(c_sysParam_->m_time.getSensorBaseTime());
        // std::cout << "pointsMatrix.size() " << pointsMatrix.size() << std::endl;
        for (int i = 0; i < pointsMatrix.size(); i++)
        {
            auto& linePoints = pointsMatrix[i];
            // std::cout << "linePoints.size() " << linePoints.size() << std::endl;
            for (int j = 0; j < linePoints.size(); j++)
            {
                if (pointsSetMatrix[i][j] == false)
                    continue;
                auto& point = linePoints[j];
                float yawDegree = atan2(point.x, point.y) * 180.0 / M_PI;
                yawDegree += 90.0;
                if (yawDegree < 0)
                    yawDegree += 360;
                // 将点放在正确的位置上
                PRaw pointRaw;
                pointRaw.x = point.x;
                pointRaw.y = point.y;
                pointRaw.z = point.z;

                PAdd pointAdd;
                pointAdd.intensity = point.intensity;  // TODO 修改驱动添加强度
                pointAdd.xydist = sqrt(point.x * point.x + point.y * point.y);
                pointAdd.depth = sqrt(point.x * point.x + point.y * point.y + point.z * point.z);
                pointAdd.ang = yawDegree;
                pointAdd.time = 100 * yawDegree / 360.0;
                point.intensity = pointAdd.time;
                // std::cout << " i = " << i << ", k = " << j
                //           << ", x = " << point.x
                //           << ", y = " << point.y
                //           << ", z = " << point.z
                //           << ", intensity = " << point.intensity
                //           << ", pointAdd.xydist = " << pointAdd.xydist
                //           << ", pointAdd.depth = " << pointAdd.depth
                //           << ", pointAdd.ang = " << pointAdd.ang
                //           << ", pointAdd.time = " << pointAdd.time
                //           <<", c_pcRawOut[i].m_praw size = " << c_pcRawOut[i].m_praw->points.size()
                //           <<", c_pcRawOut[i].m_praw capacity = " << c_pcRawOut[i].m_praw->points.capacity()
                //           << std::endl;
                          
                l_pcAllSend->push_back(point);
                c_pcRawOut[i].m_praw->points.push_back(pointRaw);
                c_pcRawOut[i].m_padd->push_back(pointAdd);
            }
        }


        // cout << "hsq start runFECallback: " << l_frameCountTemp << ", " << std::to_string(timestamp)
        //      << endl;
        c_preProcess_->runFECallback(static_cast<uint32_t>(0),
                                     c_pcRawOut,
                                     l_pcMidOut,
                                     l_pcAll,
                                     timestamp,  // 每个雷达数据包内部所带的时间戳
                                     receiveTimestamp,  // wireshark获取到每个雷达数据包系统时间戳
                                     l_frameCountTemp);

        // std::cout << " runFECallback end " << std::endl;
        for (int i = 0; i < 64; i++)
            c_pcRawOut[i].reset();
        showPointCloud<pcl::PointXYZI>(outputCurrTrans_, l_pcAllSend);
    }


    void processVLPPointCloud(const sensor_msgs::PointCloud2ConstPtr& msg)  // hsq
    {

        pcl::PointCloud<VelodynePointXYZIRT>::Ptr laserCloudIn(new pcl::PointCloud<VelodynePointXYZIRT>);
        pcl::fromROSMsg(*msg, *laserCloudIn);


        pcl::PointCloud<pcl::PointXYZI>::Ptr in_cloud_raw(new pcl::PointCloud<pcl::PointXYZI>);
        pcl::PointCloud<pcl::PointXYZI>::Ptr in_cloud(new pcl::PointCloud<pcl::PointXYZI>);
        std::vector<int> indexs;
        pcl::fromROSMsg(*msg, *in_cloud_raw);
        pcl::removeNaNFromPointCloud(*in_cloud_raw, *in_cloud, indexs);
        memset(&m_pointsSetMatrixVLP[0][0], false, sizeof(bool) * lineTotalNumber * 1800);
        l_frameCountTemp++;
        for (size_t i = 0; i < laserCloudIn->points.size(); ++i)
        {
            auto& point = laserCloudIn->points[i];
            if ((point.x == 0 && point.y == 0 && point.z == 0))
                continue;
            float distanceXY = sqrt(point.x * point.x + point.y * point.y); // 主lidar为原点，FLU坐标系
            float pitchDegree = atan2(point.z, distanceXY) * 180.0 / M_PI;
            float yawDegree = atan2(point.x, point.y) * 180.0 / M_PI; // X轴负方向逆时针0~360度
            yawDegree += 90.0;
            if (yawDegree < 0)
                yawDegree += 360;

            // std::cout << " i = " << i << ", ring = " << point.ring
            //               << ", time = " << point.time
            //               << std::endl;
                
            // if (std::isnan(pitchDegree) || angleToChannel.count(int(round(pitchDegree * 10))) == 0)
            // {  // pitchDegree不存在于map中
            //     std::cout << " no pitch " << pitchDegree << ", " << int(round(pitchDegree * 10))
            //               << ", " << angleToChannel.count(int(round(pitchDegree * 10)))
            //               << std::endl;
            //     continue;
            // }

            int lineblock =
                round(yawDegree / 0.2);  // 向上取整，将360°拓展到360/horizontal_resolution°的范围
            int channel = point.ring;  // 查找对应的通道序号
            m_pointsSetMatrixVLP[lineTotalNumber - 1 - channel][lineblock] = true;
            c_vlpPointsMatrix[lineTotalNumber - 1 - channel][lineblock] = point;
        }

        boost::shared_ptr<pcl::PointCloud<pcl::PointXYZHSV>> l_pcAll(
            new pcl::PointCloud<pcl::PointXYZHSV>());  // 参数5
        boost::shared_ptr<pcl::PointCloud<pcl::PointXYZI>> l_pcAllSend(
            new pcl::PointCloud<pcl::PointXYZI>());  // 参数5
        sTimeval l_PktFirstTime;
        l_PktFirstTime.second() = msg->header.stamp.sec;
        l_PktFirstTime.subsecond() = msg->header.stamp.nsec / 1000.0;

        if (!c_sysParam_->m_time.isSensorTimeSet())
        {
            c_sysParam_->m_time.setSensorBaseTime(l_PktFirstTime);
            LOGFAE(WINFO,
                   "{} 雷达 [{}] 帧 {} 授时, syncTime {}",
                   WJLog::getWholeSysTime(),
                   c_sysParam_->m_lidar[0].m_sLaserName,
                   l_frameCountTemp,
                   c_sysParam_->m_time.getSensorBaseTime().data());
        }
        double receiveTimestamp = c_sysParam_->m_time.getTimeNowMs();
        double timestamp = l_PktFirstTime.getDiffMs(c_sysParam_->m_time.getSensorBaseTime());
        std::cout << "hsq: finish data parse" << std::endl;
        for (size_t i = 0; i < c_vlpPointsMatrix.size(); i++)
        {
            auto& linePoints = c_vlpPointsMatrix[i];
            // std::cout << "linePoints.size() " << linePoints.size() << std::endl;
            for (size_t j = 0; j < linePoints.size(); j++)
            {
                if (m_pointsSetMatrixVLP[i][j] == false)
                    continue;
                auto& point = linePoints[j];
                float yawDegree = atan2(point.x, point.y) * 180.0 / M_PI;
                yawDegree += 90.0;
                if (yawDegree < 0)
                    yawDegree += 360;
                // 将点放在正确的位置上
                PRaw pointRaw;
                pointRaw.x = point.x;
                pointRaw.y = point.y;
                pointRaw.z = point.z;

                PAdd pointAdd;
                pointAdd.intensity = point.intensity;  // TODO 修改驱动添加强度
                pointAdd.xydist = sqrt(point.x * point.x + point.y * point.y);
                pointAdd.depth = sqrt(point.x * point.x + point.y * point.y + point.z * point.z);
                pointAdd.ang = yawDegree;
                pointAdd.time = 100 * yawDegree / 360.0;
                point.intensity = pointAdd.time;
                // std::cout << " i = " << i << ", k = " << j
                //           << ", x = " << point.x
                //           << ", y = " << point.y
                //           << ", z = " << point.z
                //           << ", intensity = " << point.intensity
                //           << ", pointAdd.xydist = " << pointAdd.xydist
                //           << ", pointAdd.depth = " << pointAdd.depth
                //           << ", pointAdd.ang = " << pointAdd.ang
                //           << ", pointAdd.time = " << pointAdd.time
                //           <<", c_pcRawOut[i].m_praw size = " << c_pcRawOut[i].m_praw->points.size()
                //           <<", c_pcRawOut[i].m_praw capacity = " << c_pcRawOut[i].m_praw->points.capacity()
                //           << std::endl;
                          
                pcl::PointXYZI pointTemp;
                pointTemp.x = point.x;
                pointTemp.y = point.y;
                pointTemp.z = point.z;
                pointTemp.intensity = pointAdd.time;

                l_pcAllSend->push_back(pointTemp);
                c_pcRawOut[i].m_praw->points.push_back(pointRaw);
                c_pcRawOut[i].m_padd->push_back(pointAdd);
            }
        }


        cout << "hsq start runFECallback: " << l_frameCountTemp << ", " << std::to_string(timestamp)
             << endl;
        c_preProcess_->runFECallback(static_cast<uint32_t>(0),
                                     c_pcRawOut,
                                     l_pcMidOut,
                                     l_pcAll,
                                     timestamp,  // 每个雷达数据包内部所带的时间戳
                                     receiveTimestamp,  // wireshark获取到每个雷达数据包系统时间戳
                                     l_frameCountTemp);

        // std::cout << " runFECallback end " << std::endl;
        for (int i = 0; i < lineTotalNumber; i++)
            c_pcRawOut[i].reset();
        showPointCloud<pcl::PointXYZI>(outputCurrTrans_, l_pcAllSend);
    }

    /***
     * 订阅gps话题
     * @param msg gps话题
     */
    void SubCallback_gps(const common_msgs::sensorgps::ConstPtr& msg)
    {
        std::lock_guard<std::mutex> gpsLock(c_gpsMutex);
        c_gpsDeque_.push_back(msg);
    }

    bool processGps(const double& syncFrameStamp)
    {
        GPSInfo l_sGPSInfo;
        common_msgs::sensorgps l_sensorgpsInfo;

        static bool isStartingPoint = true;
        if(isStartingPoint){
            // 获取时间同步数据 
            if(!syncSensorTime(syncFrameStamp)){
                std::cerr << "[debug]: syncSensorTime data: " << ros::Time::now().toSec() << std::endl;
                return false;
            }
            if(c_gpsDeque_.empty()){
                std::cerr << "[debug]: c_gpsDeque_.empty() " << ros::Time::now().toSec() << std::endl;
                return false;
            }

            common_msgs::sensorgps::ConstPtr l_curGPS = c_gpsDeque_.front();

            Point curPoint = {l_curGPS->lon, l_curGPS->lat};
            bool l_isInPolugon = m_common.isPointInPolygon(curPoint, m_slamPolygon);
          
            if(c_sysParam_->m_iWorkMode == WorkMode::LocatMode){
                if(!l_isInPolugon) // 定位模式不在区域内不触发
                {
                    std::cout << FGRN("isStartingPoint drving in area that the gps is useful") << std::endl;
            //         return false;
                }
                else{
                    std::cout << FRED("isStartingPoint drving in area that the gps not useful") << std::endl;
                }
            }
            
            c_firstSensorgps = *l_curGPS;
            cout<< std::setprecision(12) <<"\t建图定位第一帧：Car lon = "<< l_curGPS->lon <<", lat = "<< l_curGPS->lat 
                    <<", Car heading(deg) = "<< l_curGPS->heading
                    <<", status = "<< static_cast<int>(l_curGPS->status)
                    <<", rawstatus = "<< static_cast<int>(l_curGPS->rawstatus)
                <<endl;

            wgs84_utm c_wgs84_utm;
            std::vector<double> curUTM;
            curUTM = c_wgs84_utm.getUTMPosition(l_curGPS->lon, l_curGPS->lat, l_curGPS->alt);
            cout<< std::setprecision(12) <<"\tCar utmX = "<<curUTM[0] <<", utmY = "<<curUTM[1]<<endl;
            int l_utmCode = c_wgs84_utm.getUTMCode(l_curGPS->lon);
            c_sensorAxisTransformation.setSelfCarUTMPosition(curUTM);
            Eigen::Vector3d selfCarEulerDegrees{l_curGPS->pitch, l_curGPS->roll, l_curGPS->heading};
            c_sensorAxisTransformation.setSelfCarEulerDegrees(selfCarEulerDegrees);
            
            // 转成GPS的pose
            l_sGPSInfo.s_sensorgps = *l_curGPS;
            l_sGPSInfo.s_vCurUTM = curUTM;
            Eigen::Vector3d inputUTMPosition{curUTM[0], curUTM[1], 0};
            Eigen::Vector3d objectPositionInCarBackFRU;
            c_sensorAxisTransformation.utm2CarBackRFU(inputUTMPosition, selfCarEulerDegrees, inputUTMPosition, objectPositionInCarBackFRU);
            l_sensorgpsInfo.lon = objectPositionInCarBackFRU[0];
            l_sensorgpsInfo.lat = objectPositionInCarBackFRU[1];
            l_sensorgpsInfo.alt = objectPositionInCarBackFRU[2];
            

            if(c_sysParam_->m_iWorkMode == WorkMode::InitMapMode){
                c_location_->setFirstGPSInfo(l_sGPSInfo);

                c_sysParam_->m_pos.m_stSetStartintPointPose.setXYZ(curUTM[0], curUTM[1], 0);
                c_sysParam_->m_pos.m_stSetStartintPointPose.setRPY(l_curGPS->roll, l_curGPS->pitch, l_curGPS->heading);
                c_sysParam_->m_pos.m_stSetStartintPointGPS.setXYZ(l_curGPS->lon, l_curGPS->lat, l_curGPS->alt);
                c_sysParam_->m_pos.m_UTMCode = l_utmCode;
            
                Param l_param(false);
                std::string path = ros::package::getPath("wj_slam");
                bool res = l_param.saveSysParam(path);
                std::cout << FGRN("save location Param success") << std::endl;
            }
            c_location_->setGPSInfo(l_sGPSInfo);
            isStartingPoint = false;
            return true;   
        }
        else{

            // if(c_sysParam_->m_iWorkMode == WorkMode::InitMapMode){
            //     return true;
            // }


            if(syncSensorTime(syncFrameStamp) && !c_gpsDeque_.empty()){
                common_msgs::sensorgps::ConstPtr l_curGPS = c_gpsDeque_.front();

                cout<< std::setprecision(12) <<"\tCar lon = "<< l_curGPS->lon <<", lat = "<< l_curGPS->lat 
                    <<", Car heading(deg) = "<< l_curGPS->heading
                    <<", status = "<< static_cast<int>(l_curGPS->status)
                    <<", rawstatus = "<< static_cast<int>(l_curGPS->rawstatus)
                <<endl;

                Point curPoint = {l_curGPS->lon, l_curGPS->lat};
                bool l_isInPolugon = m_common.isPointInPolygon(curPoint, m_slamPolygon);
                if(!l_isInPolugon){
                    // std::cout << FRED("drving in area that the gps not useful, use slam location") << std::endl;
                //     return false;
                    
                }
                else{
                    // std::cout << FGRN("drving in area that the gps is useful, use slam location") << std::endl;
                }

                if(l_curGPS->rawstatus == 50){
                    std::cout << FGRN("drving in area that the gps is useful, use slam location") << std::endl;
                }
                else{
                    std::cout << FRED("drving in area that the gps not useful, use slam location") << std::endl;
                }
                
                wgs84_utm c_wgs84_utm;
                std::vector<double> curUTM;
                curUTM = c_wgs84_utm.getUTMPosition(l_curGPS->lon, l_curGPS->lat, l_curGPS->alt);
                cout<< std::setprecision(12) <<"\tCar utmX = "<<curUTM[0] <<", utmY = "<<curUTM[1]<<endl;
                c_sensorAxisTransformation.setSelfCarUTMPosition(curUTM);
                Eigen::Vector3d selfCarEulerDegrees{l_curGPS->pitch, l_curGPS->roll, l_curGPS->heading};
                c_sensorAxisTransformation.setSelfCarEulerDegrees(selfCarEulerDegrees);

                // 转成GPS的pose
                l_sGPSInfo.s_sensorgps = *l_curGPS;
                l_sGPSInfo.s_vCurUTM = curUTM;

                Eigen::Vector3d inputUTMPosition{curUTM[0], curUTM[1], 0};
                Eigen::Vector3d objectPositionInCarBackFRU;
                c_sensorAxisTransformation.utm2CarBackRFU(inputUTMPosition, selfCarEulerDegrees, inputUTMPosition, objectPositionInCarBackFRU);
                l_sensorgpsInfo.lon = objectPositionInCarBackFRU[0];
                l_sensorgpsInfo.lat = objectPositionInCarBackFRU[1];
                l_sensorgpsInfo.alt = objectPositionInCarBackFRU[2];
                c_location_->setGPSInfo(l_sGPSInfo);
                // std::cout << FGRN("setGPSInfo") << std::endl;
            }
            else{
                std::cout << FRED("dont setGPSInfo") << std::endl;
            }
            

            return true;
        }

        // 转成IMU数据


        // wj_slam::IMUPerScan l_vImuData;
        // wj_slam::IMUData imuData;

        // double msgTime = gpsMsg->timestamp / 1000.0;
        // imuData.linearAcceleration() << gpsMsg->accx, gpsMsg->accy, gpsMsg->accz;
        // imuData.angularVelocity() << gpsMsg->rollrate, gpsMsg->pitchrate, gpsMsg->yawrate;

        // cout << "hsq processGps: gpsMsg->accx =  " << gpsMsg->accx
        //  << ", gpsMsg->accy =  " << gpsMsg->accy << ", gpsMsg->accz =  " << gpsMsg->accz
        //  << ", gpsMsg->rollrate =  " << gpsMsg->rollrate
        //  << ", gpsMsg->pitchrate =  " << gpsMsg->pitchrate
        //  << ", gpsMsg->yawrate =  " << gpsMsg->yawrate
        //  << endl;

        // if (c_bImuFirstPkg_)
        //     imuData.imuTime() = msgTime;
        // else
        // {
        //     imuData.imuTime() = 0;
        //     c_tvLastIMUPktTime_ = msgTime;
        //     c_bImuFirstPkg_ = true;
        // }
        // imuData.imuRecvTime() =
        //     p_rawData[offset].m_stRecvTimeval.getDiffMs(c_stSysParam_->m_time.getSystemBaseTime());
        // imuData.syncTime() =
        //     getSyncTime_(p_rawData[offset]).getDiffMs(c_stSysParam_->m_time.getSensorBaseTime());
    
    }

    bool syncSensorTime(const double& syncFrameStamp){
        // cout << "lidar-gps time sync: \n";
        std::lock_guard<std::mutex> gpsLock(c_gpsMutex);

        if(c_gpsDeque_.empty())
            return false;
        
        //找到容器中数据时间戳小于同步时间的最近索引
        int curMsgIndex = 0;
        int msgDequeSize = c_gpsDeque_.size();
        for (int i = 0; i < msgDequeSize; ++i) {
            common_msgs::sensorgps::ConstPtr l_curGPS = c_gpsDeque_[i];
            if(l_curGPS->timestamp / 1000.0  > syncFrameStamp )
                break;
            curMsgIndex = i;
        }
        
        
        if(curMsgIndex + 1 == c_gpsDeque_.size()){//容器中数据时间戳都小于同步时间,只保留最后一个数据
            if(curMsgIndex > 0){//等于0的情况 说明容器内第一帧radar数据就大于当前帧lidar数据，不处理
                while(curMsgIndex--){
                    c_gpsDeque_.pop_front();
                }
            }
        }
        else{
            if(curMsgIndex > 0){//小于0 说明容器内第一帧radar数据就大于当前帧lidar数据，不处理
                if(abs(syncFrameStamp - c_gpsDeque_[curMsgIndex]->timestamp / 1000.0) >//lidar前一帧
                abs(syncFrameStamp - c_gpsDeque_[curMsgIndex + 1]->timestamp / 1000.0)){//lidar后一帧
                    curMsgIndex += 1;//取最近的一帧数据
                }
                while(curMsgIndex--){
                    c_gpsDeque_.pop_front();
                }
            }
        }

        float timeGap = syncFrameStamp - c_gpsDeque_.front()->timestamp / 1000.0;
        static float minLidarGPSSynchroTime = FLT_MAX, maxLidarGPSSynchroTime = FLT_MIN;
        minLidarGPSSynchroTime = abs(minLidarGPSSynchroTime) < abs(timeGap) ? abs(minLidarGPSSynchroTime) : abs(timeGap);
        maxLidarGPSSynchroTime = abs(maxLidarGPSSynchroTime) > abs(timeGap) ? abs(maxLidarGPSSynchroTime) : abs(timeGap);
        cout<<std::setprecision(16)<<"\tlidar-gps时间差："<<timeGap<<", 当前msg时间 = "<<c_gpsDeque_.front()->timestamp / 1000.0 <<", 当前lidar检测时间 ="
            <<syncFrameStamp<<endl;
        // cout<<std::setprecision(16)<<"\tabs(minLidarGPSSynchroTime)： "<<abs(minLidarGPSSynchroTime)<<", abs(maxLidarGPSSynchroTime) = "<<abs(maxLidarGPSSynchroTime) <<endl;
        //cout<<"\tgps Deque.size = "<<c_gpsDeque_.size() <<endl;
        return true;
    }
    
};

WanJiSLAM::WanJiSLAM(ros::NodeHandle& node,
                     boost::function<void(uint32_t, RawDataPtr&, int, bool)> p_wjOdCallback)
    : c_node_(node)
{
    LOGFAE(WINFO, "WLR720混合导航软件SLAM模块初始化...");
    c_sysParam_ = SYSPARAM::getIn();

    m_position1_lon = 0.0;
    m_position1_lat = 0.0;
    m_position2_lon = 0.0;
    m_position2_lat = 0.0;
    m_position3_lon = 0.0;
    m_position3_lat = 0.0;
    m_position4_lon = 0.0;
    m_position4_lat = 0.0;



    c_node_.param("/slamLocationRange/position1_lon",m_position1_lon,m_position1_lon);
    c_node_.param("/slamLocationRange/position1_lat",m_position1_lat,m_position1_lat);
    c_node_.param("/slamLocationRange/position2_lon",m_position2_lon,m_position2_lon);
    c_node_.param("/slamLocationRange/position2_lat",m_position2_lat,m_position2_lat);
    c_node_.param("/slamLocationRange/position3_lon",m_position3_lon,m_position3_lon);
    c_node_.param("/slamLocationRange/position3_lat",m_position3_lat,m_position3_lat);
    c_node_.param("/slamLocationRange/position4_lon",m_position4_lon,m_position4_lon);
    c_node_.param("/slamLocationRange/position4_lat",m_position4_lat,m_position4_lat);

    m_slamPolygon = {
        {m_position1_lon, m_position1_lat},
        {m_position2_lon, m_position2_lat},
        {m_position3_lon, m_position3_lat},
        {m_position4_lon, m_position4_lat}
    };



    for (int i = 0; i < 64; i++)
        c_pcRawOut[i].reset();
    l_pcMidOut.reset();
    pointsMatrix.resize(64);
    for (int i = 0; i < 64; i++)
    {
        pointsMatrix[i].resize(1800);
    }

    c_vlpPointsMatrix.resize(lineTotalNumber);
    for(int i = 0;i < lineTotalNumber; i++){
        c_vlpPointsMatrix[i].resize(1800);
    }

    // 点云回调函数
    subWanjiPointCloud_ = c_node_.subscribe<sensor_msgs::PointCloud2>(
        "/middle/rslidar_points", 100, &WanJiSLAM::callback_wanjiPointCloud, this,ros::TransportHints().tcpNoDelay(true));
    subVLPPointCloud_ = c_node_.subscribe<sensor_msgs::PointCloud2>(
        "/velodyne_points", 100, &WanJiSLAM::processVLPPointCloud, this, ros::TransportHints().tcpNoDelay(true));

    m_pointcloudProcessThread = std::thread(&WanJiSLAM::processWanjiPointCloud2, this);
    m_pointcloudProcessThread.detach();

    subGps_ = c_node_.subscribe("sensorgps", 200, &WanJiSLAM::SubCallback_gps, this,ros::TransportHints().tcpNoDelay(true));

    
    
        

#pragma region "show"
    c_pcMergeNewCloud_.reset(new pcl::PointCloud<PCurXYZHSV>());
#pragma endregion

#pragma region "preProcess"
    c_preProcess_.reset(new PreProcess<PCurXYZHSV>(boost::bind(&WanJiSLAM::odomCallback, this, _1),
                                                   p_wjOdCallback));
    c_preProcess_->start();

    c_DataPreprocessPtr.reset(new DataPreprocess(c_node_, "/middle/rslidar_points1", "WLR720FCW",
        boost::bind(&wj_slam::PreProcess<pcl::PointXYZHSV>::runFECallback, c_preProcess_, _1, _2, _3, _4, _5, _6, _7), 
        boost::bind(&WanJiSLAM::processGps, this, _1),
        c_gpsDeque_)
        );
    m_pDataPreprocess = std::thread(std::bind(&DataPreprocess::processPointCloud, c_DataPreprocessPtr, std::ref(c_gpsDeque_)));
    m_pDataPreprocess.detach();
    if (m_pDataPreprocess.joinable()) {
        std::cerr << "Thread is joinable, it should be running." << std::endl;
    }
#pragma endregion

#pragma region ""
    c_dataBak_.reset(
        new DataBackUp<PCurXYZHSV, PCurXYZHSV>(c_sysParam_->m_fae.m_sLogPath + "ErrorPoseLidar/",
                                               c_sysParam_->m_posCheck.m_iMaxNumLidarBak,
                                               c_sysParam_->m_posCheck.m_iLidarBakFrame));
#pragma endregion

    // initialSetPose();
#pragma region "odom"
    c_odom_.reset(
        new Odometry<PCurXYZHSV>(c_featureQueue_,
                                 boost::bind(&WanJiSLAM::locationCallback, this, _1),
                                 offsetof(PCurXYZHSV, s),
                                 -1,
                                 boost::bind(&WanJiSLAM::poseCallbackOdom, this, _1),
                                 boost::bind(&WanJiSLAM::pcCallbackOdom<PCurXYZHSV>, this, _1)));
    odom = std::thread(&Odometry<PCurXYZHSV>::run, c_odom_);
    odom.detach();
#pragma endregion

#pragma region "wheelOdom"
    c_wheelOdom_.reset(new WheelOdometry((boost::bind(&WanJiSLAM::ofPoseCallback, this, _1, _2))));
    c_wheelOdom_->start();
#pragma endregion

#pragma region "location"
    c_location_.reset(new Location<PCurXYZHSV, PCurXYZHSV>(
        c_keyFramesQueue_,
        boost::bind(&WanJiSLAM::locationOutputCb, this, _1),
        boost::bind(&WheelOdometry::getWheelPredPose, c_wheelOdom_, _1, _2),
        boost::bind(&DataBackUp<PCurXYZHSV, PCurXYZHSV>::setBakDataSign, c_dataBak_, _1, _2),
        boost::bind(&WanJiSLAM::poseCallback, this, _1),
        boost::bind(&WanJiSLAM::pcCallback<PCurXYZHSV, PCurXYZHSV>, this, _1, _2),
        boost::bind(&WanJiSLAM::showPredPoseBox, this, _1),
        boost::bind(&SensorAxisTransformation::ENU2UTM, &c_sensorAxisTransformation, _1)
        ));
    location = std::thread(&Location<PCurXYZHSV, PCurXYZHSV>::run, c_location_);
    location.detach();
#pragma endregion

#pragma region "netApp"
    c_wjNetApp_.reset(new WJNetApp(boost::bind(&WanJiSLAM::setWorkMode, this, _1), "AGV"));
    netAppTh = std::thread(&WJNetApp::start, c_wjNetApp_, c_sysParam_->m_agv.m_dev.m_uiLocalPort);
    netAppTh.detach();
#pragma endregion

#pragma region "webNet"
    c_wjNetWeb_.reset(new WJNetApp(boost::bind(&WanJiSLAM::setWorkMode, this, _1), "Web"));
    webAppTh =
        std::thread(&WJNetApp::start, c_wjNetWeb_, c_sysParam_->m_web.m_socket.m_uiLocalPort);
    webAppTh.detach();
#pragma endregion

#pragma region "ros"
    // debug和定位模式下输出
    changeSendInfoState();
    outputCurr_ = c_node_.advertise<sensor_msgs::PointCloud2>("wanji_lidar", 1);
    outputCurrTrans_ = c_node_.advertise<sensor_msgs::PointCloud2>("wanji_lidartrans", 1);
    outputCurMarks = c_node_.advertise<sensor_msgs::PointCloud2>("wanji_marks", 1);
    outputMarksMap = c_node_.advertise<sensor_msgs::PointCloud2>("wanji_marksMap", 1);
    outputMap_ = c_node_.advertise<sensor_msgs::PointCloud2>("wanji_map", 1);
    outPredBox_ = c_node_.advertise<visualization_msgs::Marker>("wanji_Predbox", 1);
    outputPath_ = c_node_.advertise<nav_msgs::Path>("wanji_path", 1);
    outputPose_ = c_node_.advertise<geometry_msgs::PoseStamped>("wanji_pose", 100);

    outputPathWheel_ = c_node_.advertise<nav_msgs::Path>("wanji_pathW", 1);
    outputPoseWheel_ = c_node_.advertise<geometry_msgs::PoseStamped>("wanji_poseW", 1);
    outputPathWheelRaw_ = c_node_.advertise<nav_msgs::Path>("wanji_pathW_raw", 1);
    outputPoseWheelRaw_ = c_node_.advertise<geometry_msgs::PoseStamped>("wanji_poseW_raw", 1);

    outputPathPred_ = c_node_.advertise<nav_msgs::Path>("wanji_pathPred", 1);
    outputPosePred_ = c_node_.advertise<geometry_msgs::PoseStamped>("wanji_posePred", 1);
    outputPathCurb_ = c_node_.advertise<nav_msgs::Path>("wanji_pathC", 1);
    outputPoseCurb_ = c_node_.advertise<geometry_msgs::PoseStamped>("wanji_poseC", 1);
    outputPathOutEnd_ = c_node_.advertise<nav_msgs::Path>("wanji_pathOut", 1);
    outputPoseOutEnd_ = c_node_.advertise<geometry_msgs::PoseStamped>("wanji_poseOut", 1);

#pragma endregion
    c_sysParam_->m_RunStatus = PROCSTATE::RUNNING;
    LOGFAE(WINFO, "WLR720混合导航软件SLAM模块初始化成功");
    
}

}  // namespace wj_slam

boost::shared_ptr<wj_slam::WanJiSLAM> g_wjslam = nullptr; /**< WanJiSLAM */
bool g_bCheckMacSucc = false;                             /**< 解密标志位 */
int g_cOpenStatus = 0;                                    /**< slam开启状态 */
bool g_bCloseOver = true;                                 /**< slam关闭状态 */

/**
 * @brief 解密函数
 *
 * @param p_sPkgPath
 * @param p_bCheckMacSucc
 *
 */
void checkMac_(std::string p_sPkgPath, bool& p_bCheckMacSucc)
{
    while (!p_bCheckMacSucc)
    {
        p_bCheckMacSucc = checkMacNoFromFile(p_sPkgPath + "/config/macLiense.yaml");
        if (p_bCheckMacSucc)
            break;
        sleep(2);
    }
}

bool openSlam(ros::NodeHandle& p_node,
              std::string p_sPkgPath,
              boost::function<void(uint32_t, boost::shared_ptr<s_LIDAR_RAW_DATAS>&, int, bool)>
                  p_wjOdCallback)
{
    if (g_wjslam)
        return true;
    // 避免快速连续调用，造成多次实例化
    if (g_cOpenStatus == 2)
        return true;
    if (g_cOpenStatus == 1)
        return false;
    // 避免正在关的时候开
    if (!g_bCloseOver)
        return false;

    // 正在开启
    g_cOpenStatus = 1;
    // 循环检查Mac 直到解密
    checkMac_(p_sPkgPath, g_bCheckMacSucc);
    // 避免多开
    if (!g_wjslam)
    {
        g_wjslam.reset(new wj_slam::WanJiSLAM(p_node, p_wjOdCallback));
        g_cOpenStatus = 2;
        return true;
    }
    g_cOpenStatus = 0;
    return false;
}

void closeSlam_()
{
    if (!g_wjslam)
        return;
    // 避免多次关闭
    if (!g_bCloseOver)
        return;
    g_bCloseOver = false;
    // 这里主要进行退出前的数据保存、内存清理、告知其他节点等工作
    g_bCheckMacSucc = true;
    // 正在启动中 则等待启动成功后执行关闭
    while (g_cOpenStatus == 1)
    {
        if (g_cOpenStatus == 2)
            break;
        sleep(1);
    }
    if (g_wjslam)
        g_wjslam->stop();
    sleep(1);
    g_wjslam = nullptr;
    g_bCloseOver = true;
    g_cOpenStatus = 0;
}

void closeSlam()
{
    closeSlam_();
    // std::thread l_thr = std::thread(closeSlam_);
    // l_thr.join();
}

bool isRunSlam()
{
    if (g_wjslam)
        return true;
    else
        return false;
}