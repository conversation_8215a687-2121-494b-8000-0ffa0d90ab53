/*
 * @Description:
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2022-06-08 13:50:44
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2024-01-11 20:55:47
 */
#pragma once

#ifndef _SLAM_H_
#    define _SLAM_H_
#    include "common/config/conf_lidar.h"
#    include "common/config/conf_timer.h"
#    include <geometry_msgs/PoseStamped.h>
#    include <iostream>
#    include <map>
#    include <nav_msgs/Odometry.h>
#    include <nav_msgs/Path.h>
#    include <pcl/point_types.h>
#    include <queue>
#    include <ros/package.h>
#    include <ros/ros.h>
#    include <sensor_msgs/PointCloud2.h>
#    include <std_msgs/Int32.h>
#include "common_msgs/sensorgps.h"
#include "../../../../../commonlibrary/src/coordinateTransformation/sensorAxisTransformation/sensorAxisTransformation.h"
#include "../../../../../commonlibrary/src/coordinateTransformation/wgs84_utm.h"
// #include <ros/builtin_message_traits.h>
// #include <ros/message_operations.h>
// #include <pcl_conversions/pcl_conversions.h>
// #include <pcl/point_cloud.h>

using namespace std;
int lineTotalNumber = 16;
std::map<int, int> angleToChannel = {
    {150, 1},  {120, 2},  {90, 3},    {70, 4},    {50, 5},    {30, 6},    {20, 7},    {15, 8},
    {12, 9},   {9, 10},   {6, 11},    {4, 12},    {2, 13},    {1, 14},    {0, 15},    {-1, 16},
    {-2, 17},  {-3, 18},  {-4, 19},   {-5, 20},   {-6, 21},   {-7, 22},   {-8, 23},   {-9, 24},
    {-10, 25}, {-11, 26}, {-12, 27},  {-13, 28},  {-14, 29},  {-15, 30},  {-16, 31},  {-17, 32},
    {-18, 33}, {-19, 34}, {-20, 35},   {-21, 36},  {-22, 37},  {-23, 38},  {-24, 39},  {-25, 40},
    {-26, 41}, {-27, 42}, {-29, 43},  {-31, 44},  {-33, 45},  {-35, 46},  {-38, 47},  {-41, 48},
    {-44, 49}, {-47, 50}, {-52, 51},  {-57, 52},  {-62, 53},  {-67, 54},  {-72, 55},  {-77, 56},
    {-87, 57}, {-97, 58}, {-107, 59}, {-127, 60}, {-147, 61}, {-177, 62}, {-207, 63}, {-250, 64}};

struct VelodynePointXYZIRT{
    PCL_ADD_POINT4D;
    uint8_t intensity;
    uint16_t ring = 0;
    double time = 0;
    EIGEN_MAKE_ALIGNED_OPERATOR_NEW
}EIGEN_ALIGN16;
POINT_CLOUD_REGISTER_POINT_STRUCT(VelodynePointXYZIRT, (float, x, x)(float, y, y)(float, z, z)(uint8_t, intensity, intensity)(uint16_t, ring, ring)(double, time, time))

/**
 * @brief 启动slam
 *
 * @param p_node ROS node 节点
 * @param p_sPkgPath
 * @param p_wjOdCallback
 * @code
 *
 * @endcode
 * @return [true] \n
 * slam启动成功
 * @code
 *
 * @endcode
 * @return [false] \n
 * slam启动失败
 *
 */
bool openSlam(ros::NodeHandle& p_node,
              std::string p_sPkgPath,
              boost::function<void(uint32_t, boost::shared_ptr<s_LIDAR_RAW_DATAS>&, int, bool)>
                  p_wjOdCallback = NULL);
/**
 * @brief 关闭slam
 *
 *
 */
void closeSlam();

/**
 * @brief 查询当前slam状态
 *
 * @code
 *
 * @endcode
 * @return [true] \n
 * 正在运行
 * @code
 *
 * @endcode
 * @return [false] \n
 * 未在运行
 *
 */
bool isRunSlam();

#endif