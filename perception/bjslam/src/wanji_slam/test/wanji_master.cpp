/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2022-03-22 17:45:38
 * @LastEditors: shuang<PERSON>n han
 * @LastEditTime: 2023-11-30 17:09:29
 */
#include "../../wanji_od/test/obstacleDetect.h"
#include "common/common_master.h"
#include "mac/checkMac.h"
#include "master/driver_virtual/virtual_driver.h"
#include "master/keyboard/keyboard.hpp"
#include "master/netCfg/netCfg.hpp"
#include "master/poseMarker/markerApp.hpp"
#include "master/recordControl/recordControl.hpp"
#include "master/webApp/webApp.h"
#include "param.hpp"
#include "slam/slam.h"
#include "wj_log.h"
#include <malloc.h>
#include <ros/package.h>
#include <signal.h>
// 日志头
generalLog* glog = nullptr;
namespace wj_slam {

class Master {
  public:
    Master(ros::NodeHandle& node);
    ~Master();
#pragma region "公有函数声明"
    void startCtrl();
    void stop();
    void setErrorCode(std::string p_sErrCode);
#pragma endregion

  private:
#pragma region "私有变量"
    s_masterCfg* c_masterParamPtr;
    bool c_bIsChangeDriverMode_ =
        false;  // 是否切换雷达模式 限制用户切换后必须重启 否则无法切换回去
#pragma region "ROS相关"
    ros::NodeHandle& c_node_;
    ros::Subscriber c_subWanjiPointClouds;
#pragma endregion
#pragma region "模块相关"
    boost::shared_ptr<WebApp> c_webnet_;
    boost::shared_ptr<NetCfg> c_netCfg_;                //读取/设置/保存/刷新网络参数
    boost::shared_ptr<VirtualDriver> c_virtualDriver_;  // 虚拟驱动
    boost::shared_ptr<KeyBoard> c_keyboard_;
    boost::shared_ptr<RecordControl> c_recordControl_;  // 虚拟驱动
    boost::shared_ptr<MarkerApp> c_markApp;             // 位姿UI
#pragma endregion

#pragma endregion

#pragma region "私有函数声明"
    void init_();

    /**
     * @description: 调用Param读取参数 可反复读取参数
     * @param {*}
     * @return {*}
     */
    void readParam_();

    /**
     * @description: 日志打印基本参数
     * @param {*}
     * @return {*}
     */
    void logParam_();

    /**
     * @description: 调用Net打包协议-设定Pose
     * @param {*}
     * @return {*}
     */
    void setPose(s_POSE6D p_setPose);
    /**
     * @description: 调用Param读取参数 设置日志等级 离线模式下更改时间参数
     * @param {*}
     * @return {*}
     */
    void initParam_();
    /**
     * @description: 动作回调 用于协议和键盘触发事件
     * @param {int} p_cmd 时间id
     * @param {int} p_signal 动作给予的信号
     * @return {int} 事件执行状态
     */
    int setActionCb_(int p_cmd, int p_signal);
    void savePoseInFile_(s_POSE6D p_sCurrPose, std::string p_sFilePath);
    /**
     * @description: 驱动模式切换
     * @param {uint32_t&} p_uiPlayLidarMode
     * @return {bool} 是否切换成功
     */
    bool changeDriverModel_(bool& p_bIsOnlineMode);
    /**
     * @description: 启动slam,并开辟线程 间隔1s查询SLAM状态 直到启动并初始化完成
     * @param {*}
     * @return {int} 是否启动成功
     */
    int controlSlamStart_();
    int controlSlamStop_(bool p_bIsRestartWeb = true);
    void slamStartCb_();
    void slamStopCb_();
    /**
     * @description: 用于驱动模式切换时 时间复位
     * @param {*}
     * @return {*}
     */
    void resetMasterTime_();
#pragma endregion
};

/****************************************************************************************/
void Master::init_()
{
#pragma region "参数初始化"
    initParam_();
#pragma endregion

#pragma region "网络配置+参数"
    c_netCfg_.reset(new NetCfg(c_masterParamPtr->m_sNetCfgPath, c_masterParamPtr->m_slam->m_fae));
    c_masterParamPtr->m_net = c_netCfg_->getNetCfg();
#pragma endregion

#pragma region "web客户端"
    c_webnet_.reset(new WebApp(c_node_, boost::bind(&Master::setActionCb_, this, _1, _2)));
#pragma endregion

#pragma region "录包控制"
    c_recordControl_.reset(new RecordControl());
#pragma endregion

#pragma region "位姿UI控制"
    c_markApp.reset(new MarkerApp(boost::bind(&Master::setPose, this, _1)));
#pragma endregion

    // 启动后 等待SLAM状态1min后自动结束
    controlSlamStart_();
}

/**
 * @description: 调用Param读取参数 可反复读取参数
 * @param {*}
 * @return {*}
 */
void Master::readParam_()
{
    Param l_param;
    std::string l_sPath = ros::package::getPath("wj_slam");
    c_masterParamPtr->m_slam->m_lidar.clear();
    c_masterParamPtr->m_slam->m_devList.clear();
    c_masterParamPtr->m_slam->m_fae.resetErrorCode();
    l_param.loadSysParam(l_sPath);
    c_bIsChangeDriverMode_ = false;
    logParam_();
}

void Master::logParam_()
{
    LOGFAE(WINFO, "");
    LOGFAE(WINFO, "******  WLR720混合导航软件基础参数 ******");
#pragma region "模式相关"
    if (c_masterParamPtr->m_slam->m_bIsOnlineMode)
        LOGFAE(WINFO, "雷达模式: 在线雷达");
    else
        LOGFAE(WINFO, "雷达模式: 离线仿真");
    switch (c_masterParamPtr->m_slam->m_iWorkMode)
    {
        case 0: LOGFAE(WINFO, "工作模式: 空闲模式"); break;
        case 1: LOGFAE(WINFO, "工作模式: 初始建图"); break;
        case 2: LOGFAE(WINFO, "工作模式: 连续建图"); break;
        case 3: LOGFAE(WINFO, "工作模式: 连续定位"); break;
        case 4: LOGFAE(WINFO, "工作模式: 更新地图"); break;
        default:
            LOGFAE(WERROR,
                   "工作模式: [{}] 错误输入，请确认工作模式是否为可允许设置模式！",
                   c_masterParamPtr->m_slam->m_iWorkMode);
            // c_masterParamPtr->m_slam->m_fae.setErrorCode("D7");
            break;
    }
#pragma endregion
#pragma region "网络配置相关"
    if (!isExistFileOrFolder(c_masterParamPtr->m_sNetCfgPath))
    {
        LOGFAE(WERROR,
               "网络配置文件 [{}] 不存在,请检查网络配置文件!",
               c_masterParamPtr->m_sNetCfgPath);
        // c_masterParamPtr->m_slam->m_fae.setErrorCode("A6");
    }
    else
        LOGFAE(WINFO, "网络配置文件路径: [{}]", c_masterParamPtr->m_sNetCfgPath);
    if (c_masterParamPtr->m_net.size())
    {
        for (uint32_t i = 0; i < c_masterParamPtr->m_net.size(); i++)
        {
            if (!c_masterParamPtr->m_net[i].isSet)
                LOGFAE(WWARN,
                       "网络配置文件中未检测到网卡 [{}] 配置",
                       c_masterParamPtr->m_net[i].m_sNetName);
        }
    }
    else
    {
        LOGFAE(WERROR, "本机未识别到任何网卡,请检查网卡是否被禁用!");
        // c_masterParamPtr->m_slam->m_fae.setErrorCode("A7");
    }
#pragma endregion

#pragma region "地图相关"
    LOGFAE(WINFO, "设定地图名: {}", c_masterParamPtr->m_slam->m_map.m_sMapName.c_str());
#pragma endregion
#pragma region "雷达相关"
    if (c_masterParamPtr->m_slam->m_lidar.size())
    {
        for (uint32_t i = 0; i < c_masterParamPtr->m_slam->m_lidar.size(); i++)
        {
            LOGFAE(WINFO, "雷达 [{}]: ", c_masterParamPtr->m_slam->m_lidar[i].m_sLaserName.c_str());
            LOGFAE(WINFO,
                   "    设备类型: {}",
                   c_masterParamPtr->m_slam->m_lidar[i].m_dev.m_sDevType.c_str());
            LOGFAE(WINFO, "    网络参数:");
            if (c_masterParamPtr->m_slam->m_bIsOnlineMode)
            {
                LOGFAE(WINFO,
                       "      网卡: {} 雷达IP: {} Port: {} PC-IP: {} Port: {}",
                       c_masterParamPtr->m_slam->m_lidar[i].m_dev.m_sNetName.c_str(),
                       c_masterParamPtr->m_slam->m_lidar[i].m_dev.m_sDevIP.c_str(),
                       c_masterParamPtr->m_slam->m_lidar[i].m_dev.m_uiDevPort,
                       c_masterParamPtr->m_slam->m_lidar[i].m_dev.m_sLocalIP.c_str(),
                       c_masterParamPtr->m_slam->m_lidar[i].m_dev.m_uiLocalPort);
                // 校验NetName 是否成功设置
                bool l_bNetActive = false;
                uint32_t l_uiNetID = 0;
                for (uint32_t j = 0; j < c_masterParamPtr->m_net.size(); j++)
                {
                    if (c_masterParamPtr->m_net[j].m_sNetName
                        == c_masterParamPtr->m_slam->m_lidar[i].m_dev.m_sNetName)
                    {
                        l_bNetActive = true;
                        l_uiNetID = j;
                        break;
                    }
                }
                if (!l_bNetActive)
                {
                    c_masterParamPtr->m_slam->m_lidar[i].m_dev.setStatus(DevStatus::NETSETERROR);
                    c_masterParamPtr->m_slam->m_fae.setErrorCode("C1");
                    LOGFAE(WERROR,
                           " 此雷达网卡配置异常 | 未检测到设定网卡[{}]，请按照以下步骤确认：",
                           c_masterParamPtr->m_slam->m_lidar[i].m_dev.m_sNetName);
                    LOGFAE(WERROR, " *********************************************** ");
                    LOGFAE(WERROR, " * 1. 检查工控机连接雷达的网线是否有松动 ");
                    LOGFAE(WERROR, " * 2. 确认雷达接入工控机的网卡名 ");
                    LOGFAE(WERROR, " * 3. 在web端[雷达参数配置]中配置连接雷达工控机的网卡名 ");
                    LOGFAE(WERROR, " * **********************************************");
                }

                // 校验PC-IP 是否符合网卡设定
                if (l_bNetActive)
                {
                    // 读取实时IP
                    std::string l_sNetIp = c_netCfg_->getNetRealIP(
                        c_masterParamPtr->m_slam->m_lidar[i].m_dev.m_sNetName);
                    if (l_sNetIp == "")
                    {
                        // 获取失败意味着网卡不属于激活状态：即网卡未连接
                        if (c_masterParamPtr->m_net[l_uiNetID].isSet)
                        {
                            // 若读到了设置信息 则对比是否一致
                            if (c_masterParamPtr->m_net[l_uiNetID].m_sIpSet
                                != c_masterParamPtr->m_slam->m_lidar[i].m_dev.m_sLocalIP)
                            {
                                c_masterParamPtr->m_slam->m_lidar[i].m_dev.setStatus(
                                    DevStatus::NETIPERROR);
                                c_masterParamPtr->m_slam->m_fae.setErrorCode("C2");
                                LOGFAE(WERROR,
                                       " 此雷达PC-IP异常 | PC-IP: [{}]与网卡配置IP: "
                                       "[{}]不符，请按照以下步骤进行检查:",
                                       c_masterParamPtr->m_slam->m_lidar[i].m_dev.m_sLocalIP,
                                       c_masterParamPtr->m_net[l_uiNetID].m_sIpSet);
                                LOGFAE(WERROR, " *********************************************** ");
                                LOGFAE(WERROR,
                                       " * 1. "
                                       "使用ifconfig确认接入雷达的工控机端口IP地址(默认为192.168.2."
                                       "88) ");
                                LOGFAE(
                                    WERROR,
                                    " * 2. "
                                    "连接web端打开[雷达参数配置]确认PC-IP是否更改为1中确认的地址");
                                LOGFAE(WERROR,
                                       " * 3. 更改完成后点击[配置全部]保存参数后重启Slam生效 ");
                                LOGFAE(WERROR, " * **********************************************");
                            }
                            else
                            {
                                c_masterParamPtr->m_slam->m_lidar[i].m_dev.setStatus(
                                    DevStatus::NETNOALIVE);
                                c_masterParamPtr->m_slam->m_fae.setErrorCode("C3");
                                LOGFAE(WERROR,
                                       " 此雷达网卡 [{}] 未激活,请确认接入雷达的网卡已激活",
                                       c_masterParamPtr->m_slam->m_lidar[i].m_dev.m_sNetName);
                                LOGFAE(WERROR, " *********************************************** ");
                                LOGFAE(WERROR, " * 1. 使用ifconfig确认连接雷达的网卡是否被禁用 ");
                                LOGFAE(WERROR,
                                       " * 2. 如果网卡被禁用，请使用 sudo ifconfig 网卡名 up");
                                LOGFAE(WERROR, " * **********************************************");
                            }
                        }
                        else
                        {
                            c_masterParamPtr->m_slam->m_lidar[i].m_dev.setStatus(
                                DevStatus::NETNOIP);
                            c_masterParamPtr->m_slam->m_fae.setErrorCode("C4");
                            LOGFAE(WERROR,
                                   "此雷达网卡配置异常 | 读取网卡 [{}] 实时IP及配置IP均失败, "
                                   "请参照以下步骤进行检查：",
                                   c_masterParamPtr->m_slam->m_lidar[i].m_dev.m_sNetName);
                            LOGFAE(WERROR, " *********************************************** ");
                            LOGFAE(WERROR,
                                   " * 1. "
                                   "进入web端点击[网络参数配置],"
                                   "配入IP地址后点击对勾并重启工控机使配置生效 ");
                            LOGFAE(WERROR, " * 2. 重启后使用ifconfig查看网卡实时IP是否已更改");
                            LOGFAE(WERROR, " * 3. 确认工控机连接雷达的网口与配置是否一致");
                            LOGFAE(WERROR, " * 4. ping 雷达IP地址，确保雷达已连接");
                            LOGFAE(WERROR, " * **********************************************");
                        }
                    }
                    else if (l_sNetIp != c_masterParamPtr->m_slam->m_lidar[i].m_dev.m_sLocalIP)
                    {
                        c_masterParamPtr->m_slam->m_lidar[i].m_dev.setStatus(DevStatus::NETIPERROR);
                        c_masterParamPtr->m_slam->m_fae.setErrorCode("C5");
                        LOGFAE(WERROR,
                               "此雷达PC-IP异常 | PC-IP: [{}]与网卡实时IP: "
                               "[{}]不符,请参照以下步骤进行检查：",
                               c_masterParamPtr->m_slam->m_lidar[i].m_dev.m_sLocalIP,
                               l_sNetIp);
                        LOGFAE(WERROR, " *********************************************** ");
                        LOGFAE(
                            WERROR,
                            " * 1. "
                            "连接web客户端点击[雷达参数配置]确认配置的PC-IP是否与网卡实时IP一致");
                        LOGFAE(WERROR, " * 2. 如果不一致请修改后点击[配置全部]后生效");
                        LOGFAE(WERROR, " * **********************************************");
                    }
                    else
                    {
                        //  进入此区域 意味着设定网卡已激活，且IP和PC-IP一致
                        //  确定PC-IP 和 雷达IP一致后 校验雷达-IP 是否存在
                        if (!c_masterParamPtr->m_net[l_uiNetID].isSameNetSegment(
                                c_masterParamPtr->m_slam->m_lidar[i].m_dev.m_sLocalIP,
                                c_masterParamPtr->m_slam->m_lidar[i].m_dev.m_sDevIP))
                        {
                            c_masterParamPtr->m_slam->m_lidar[i].m_dev.setStatus(
                                DevStatus::IPINFOERROR);
                            c_masterParamPtr->m_slam->m_fae.setErrorCode("C6");
                            LOGFAE(WERROR,
                                   "此雷达IP异常 | 雷达IP: [{}]与PC-IP: "
                                   "[{}]非同网段,请参照以下步骤进行检查：",
                                   c_masterParamPtr->m_slam->m_lidar[i].m_dev.m_sDevIP,
                                   c_masterParamPtr->m_slam->m_lidar[i].m_dev.m_sLocalIP);
                            LOGFAE(WERROR, " *********************************************** ");
                            LOGFAE(WERROR,
                                   " * 1. "
                                   "打开web端，点击[雷达参数配置]-->[读取全部]查看雷达IP是否与PC-"
                                   "IP在同一网段");
                            LOGFAE(WERROR,
                                   " *    正确例：雷达IP：************	PC-IP:************");
                            LOGFAE(WERROR,
                                   " *    错误例：雷达IP：************	PC-IP:************");
                            LOGFAE(WERROR, " * **********************************************");
                        }
                        else if (!c_netCfg_->isPing(
                                     c_masterParamPtr->m_slam->m_lidar[i].m_dev.m_sDevIP))
                        {
                            c_masterParamPtr->m_slam->m_lidar[i].m_dev.setStatus(
                                DevStatus::NOFINDDEV);
                            c_masterParamPtr->m_slam->m_fae.setErrorCode("C7");
                            LOGFAE(WERROR,
                                   "此雷达IP异常 | Ping {} "
                                   "失败，请按照以下步骤进行检查：",
                                   c_masterParamPtr->m_slam->m_lidar[i].m_dev.m_sDevIP);

                            LOGFAE(WERROR, " *********************************************** ");
                            LOGFAE(WERROR, " * 1. 确认雷达与工控机连接的网线是否有松动");
                            LOGFAE(WERROR, " * 2. 确认雷达IP是否设置正确");
                            LOGFAE(WERROR, " * 3. 手动ping 雷达IP地址，确认雷达地址正确");
                            LOGFAE(WERROR, " * 4. 启动wireshark查看雷达对应的IP是否有数据");
                            LOGFAE(WERROR, " * **********************************************");
                        }
                    }
                }
            }
            else
                LOGFAE(WINFO,
                       "        离线数据包: {}",
                       c_masterParamPtr->m_slam->m_lidar[i].m_dev.m_sPcapName.c_str());
        }
    }
    else
    {
        LOGFAE(WERROR, "雷达参数读取失败，请检查雷达是否正确链接!");
        // c_masterParamPtr->m_slam->m_fae.setErrorCode("D7");
    }
#pragma endregion
    LOGFAE(WINFO, "*****************************************");
    LOGFAE(WINFO, "");
}

/**
 * @description: 调用Net打包协议-设定Pose
 * @param {*}
 * @return {*}
 */
void Master::setPose(s_POSE6D p_setPose)
{
    c_webnet_->sendSetPose(p_setPose);
}

/**
 * @description: 调用Param读取参数 设置日志等级 离线模式下更改时间参数
 * @param {*}
 * @return {*}
 */
void Master::initParam_()
{
    c_masterParamPtr = s_masterCfg::getIn();
}

/**
 * @description: 动作回调 用于协议和键盘触发事件
 * @param {int} p_cmd 时间id
 * @param {int} p_signal 动作给予的信号
 * @return {int} 事件执行状态
 */
int Master::setActionCb_(int p_cmd, int p_signal)
{
    int l_iRes = 0;
    switch (p_cmd)
    {
        case 0:
        {
            // 切换雷达模式
            // 先保存参数
            Param l_param;
            std::string path = ros::package::getPath("wj_slam");
            // 读取node 修改固定参数 保存
            if (!c_bIsChangeDriverMode_ && l_param.saveSysParam(path))
            {
                l_iRes = changeDriverModel_(c_masterParamPtr->m_slam->m_bIsOnlineMode);
                c_bIsChangeDriverMode_ = l_iRes;
            }
            else
            {
                l_iRes = 0;
                if (c_bIsChangeDriverMode_)
                {
                    LOGFAE(WERROR,
                           "{} 驱动模式切换失败 | 多次切换驱动模式未重启SLAM",
                           WJLog::getWholeSysTime());
                    // c_masterParamPtr->m_slam->m_fae.setErrorCode("D4");
                }
                else
                {
                    LOGFAE(WERROR, "{} 驱动模式切换失败 | 保存参数失败!", WJLog::getWholeSysTime());
                    // c_masterParamPtr->m_slam->m_fae.setErrorCode("D5");
                }
            }
            // 重启 外部
            break;
        }
        case 1:
            // 离线数据播放控制
            l_iRes = 0;
            if (c_virtualDriver_)
            {
                c_virtualDriver_->playPause(c_masterParamPtr->m_bIsStart);
                l_iRes = 1;
            }
            break;
        case 2:
            // 录制数据启动关闭
            l_iRes = c_recordControl_->recordPcap(p_signal);
            break;
        case 3:
            // 查询录取数据时长
            c_masterParamPtr->m_iRecordDuration = c_recordControl_->getRecordTime();
            l_iRes = 1;
            break;
        case 4:
            // 保存网卡配置参数
            if (p_signal >= 0 && p_signal < (int)c_masterParamPtr->m_net.size())
                l_iRes = (int)c_netCfg_->saveNetCfg(c_masterParamPtr->m_net[p_signal]);
            break;
        case 5:
            // 重置网卡配置参数
            if (p_signal >= 0 && p_signal < (int)c_masterParamPtr->m_net.size())
                l_iRes = (int)c_netCfg_->resetNetCfg(c_masterParamPtr->m_net[p_signal]);
            break;
        case 6:
            // 获取是否在录制
            l_iRes = c_recordControl_->getRecordStatus();
            break;
        case 7:
            // SLAM重启
            if (p_signal == 0)
                l_iRes = controlSlamStop_();
            else if (p_signal == 1)
                l_iRes = controlSlamStart_();
            else if (p_signal == 2)
            {
                // 重启
                l_iRes = controlSlamStop_();
                if (l_iRes)
                    l_iRes = controlSlamStart_();
            }
            else
                l_iRes = 0;
            break;
        case 8:
        {
            // 恢复出厂时确认出厂配置有效
            Param l_param;
            std::string path = ros::package::getPath("wj_slam");
            l_iRes = (int)l_param.checkDefaultYaml();
            break;
        }
        case 9:
            LOGFAE(WINFO, "用户输入密钥 [{}], 解密中...", c_masterParamPtr->m_sMacNo.c_str());
            // 外部密钥检查+解密
            l_iRes =
                checkMacNoFromSet(c_masterParamPtr->m_slam->m_sPkgPath + "/config/macLiense.yaml",
                                  c_masterParamPtr->m_sMacNo,
                                  c_masterParamPtr->m_sSecretNo);
            if (l_iRes)
                LOGFAE(WINFO, "密钥正确，解密成功！");
            else
                LOGFAE(WWARN, "密钥错误，解密失败！");
            break;
        case 10:
            // 本地密钥检查+解密
            l_iRes =
                checkMacNoFromFile(c_masterParamPtr->m_slam->m_sPkgPath + "/config/macLiense.yaml");
            if (!l_iRes)
            {
                c_masterParamPtr->m_sSecretNo = getSecretNo();
                LOGFAE(WWARN, "本机存储密钥异常： 解密失败！(Ps: 程序如果未解密，可忽略此错误)");
            }
            break;
        case 11:
            // 切换DEBUG
            c_masterParamPtr->m_slam->m_bDebugModel = !c_masterParamPtr->m_slam->m_bDebugModel;
            l_iRes = 1;
            break;
        case 12:
            // 执行雷达标定
            l_iRes = 1;
            break;
        case 13:
        {
            // 保存参数
            if (!c_bIsChangeDriverMode_)
            {
                Param l_param;
                std::string path = ros::package::getPath("wj_slam");
                l_iRes = l_param.saveSysParam(path);
            }
            else
                l_iRes = 0;
            break;
        }
        case 14:
        {
            // 新增雷达，直接保存，且只保存该雷达，该步骤后须外部重启
            Param l_param;
            std::string path = ros::package::getPath("wj_slam");
            // 雷达下标超限 或 未重启状态 不允许增删雷达 新增雷达IP/Port无法监控
            if (p_signal < 0 || p_signal >= (int)c_masterParamPtr->m_slam->m_lidar.size()
                || c_bIsChangeDriverMode_)
                l_iRes = false;
            else
                l_iRes = l_param.addLidarParam(path, c_masterParamPtr->m_slam->m_lidar[p_signal]);
            break;
        }
        case 15:
        {
            // 删除雷达，直接删除对应配置，该步骤后须外部重启
            Param l_param;
            std::string path = ros::package::getPath("wj_slam");
            if (p_signal < 0 || p_signal >= (int)c_masterParamPtr->m_slam->m_lidar.size()
                || c_bIsChangeDriverMode_)
                l_iRes = false;
            else
                l_iRes =
                    l_param.deleteLidarParam(path, c_masterParamPtr->m_slam->m_lidar[p_signal]);
            break;
        }
        case 16:
        {
            // SLAM类是否实例化 用于Web是否不间断查询状态
            l_iRes = isRunSlam();
            break;
        }
        case 17:
        {
            // 重置位姿箭头UI
            c_markApp->resetPoseUI(c_masterParamPtr->m_slam->m_pos.m_stUIPose);
            l_iRes = 1;
            break;
        }
        case 18:
        {
            // 切换2D/3D显示
            if (c_webnet_)
                c_webnet_->setViewMode(!c_masterParamPtr->m_slam->m_iViewMode);
            l_iRes = 1;
            break;
        }
        case 19:
        {
            // if (c_masterParamPtr->m_bOpenObstacle
            //     && (c_masterParamPtr->m_slam->m_iWorkMode == WorkMode::LocatMode
            //         || c_masterParamPtr->m_slam->m_iWorkMode == WorkMode::UpdateMapMode))
            // {
            //     // 避障模块
            //     openWanjiOD(c_node_,
            //                 c_masterParamPtr->m_slam->m_lidar,
            //                 c_masterParamPtr->m_slam->m_time.m_sTimeProcStart);
            //     std::cout << "finished openWanjiOD\n";
            // }
            // else
            //     closeWanjiOD();
            break;
        }
        case 20:
        {
            // 不使用雷达数据
            if (!c_masterParamPtr->m_slam->m_bDebugModel)
                break;
            for (int i = 0; i < (int)c_masterParamPtr->m_slam->m_lidar.size(); i++)
            {
                c_masterParamPtr->m_slam->m_lidar[i].m_bEnable =
                    !c_masterParamPtr->m_slam->m_lidar[i].m_bEnable;
                LOGFAE(WINFO,
                       "{} 是否使用雷达[{}]数据： {}",
                       WJLog::getSystemTime(),
                       c_masterParamPtr->m_slam->m_lidar[i].m_sLaserName,
                       c_masterParamPtr->m_slam->m_lidar[i].m_bEnable);
            }
            if (c_webnet_)
                c_webnet_->resetLidarEnable();
            break;
        }
        case 21:
        {
            // 纯里程计
            if (!c_masterParamPtr->m_slam->m_bDebugModel)
                break;
            c_masterParamPtr->m_slam->m_posCheck.m_bOnlyWheelOdom =
                !c_masterParamPtr->m_slam->m_posCheck.m_bOnlyWheelOdom;
            LOGFAE(WINFO,
                   "{} 是否纯里程计定位： {}",
                   WJLog::getSystemTime(),
                   c_masterParamPtr->m_slam->m_posCheck.m_bOnlyWheelOdom);
            break;
        }
        case 22:
        {
            // 启动校验
            if (!c_masterParamPtr->m_slam->m_bDebugModel)
                break;
            c_masterParamPtr->m_slam->m_posCheck.m_bOpenPoseCheck =
                !c_masterParamPtr->m_slam->m_posCheck.m_bOpenPoseCheck;
            LOGFAE(WINFO,
                   "{} 是否启动校验： {}",
                   WJLog::getSystemTime(),
                   c_masterParamPtr->m_slam->m_posCheck.m_bOpenPoseCheck);
            break;
        }
        default: break;
    }
    return l_iRes;
}

void Master::savePoseInFile_(s_POSE6D p_sCurrPose, std::string p_sFilePath)
{
    int l_nWriteOverSign = 1;
    std::fstream l_filePoseWR;
    l_filePoseWR.open(p_sFilePath.c_str(), std::ios::out | std::ios::trunc);
    if (l_filePoseWR.is_open())
    {
        l_filePoseWR << p_sCurrPose.m_trans[0] << "," << p_sCurrPose.m_trans[1] << ","
                     << p_sCurrPose.m_trans[2] << "," << p_sCurrPose.roll() << ","
                     << p_sCurrPose.pitch() << "," << p_sCurrPose.yaw() << "," << l_nWriteOverSign
                     << std::endl;
        l_filePoseWR.close();
    }
    // else
    //     LOGM(WERROR, "{} saveP open fail: {}", WJLog::getWholeSysTime(), p_sFilePath);
}

/**
 * @description: 驱动模式切换
 * @param {uint32_t} p_uiPlayLidarMode
 * @return {bool} 是否切换成功
 */
bool Master::changeDriverModel_(bool& p_bCurOnlineMode)
{
    // 若Web先切换工作模式->设定Pose，雷达未介入时切换驱动模式 则设定Pose将丢失，无法启作用。
    if (c_masterParamPtr->m_slam->m_pos.m_stSetPose.m_bFlag == PoseStatus::SettingPose)
    {
        savePoseInFile_(c_masterParamPtr->m_slam->m_pos.m_stSetPose,
                        c_masterParamPtr->m_slam->m_pos.m_sPoseFilePath + "pose.csv");
        savePoseInFile_(c_masterParamPtr->m_slam->m_pos.m_stSetPose,
                        c_masterParamPtr->m_slam->m_pos.m_sPoseFilePath + "pose_bak.csv");
    }

    // 当前为离线->切换在线 提取在线备份yaml中的ip和端口等参数 赋值给 default.yaml
    if (!p_bCurOnlineMode)
    {
        std::string l_sSlamBakPath =
            c_masterParamPtr->m_slam->m_sPkgPath + "/config/.user.yaml.bak";
        std::string l_sSlamCurPath = c_masterParamPtr->m_slam->m_sPkgPath + "/config/user.yaml";
        if (isExistFileOrFolder(l_sSlamBakPath))
        {
            if (!isExistFileOrFolder(l_sSlamCurPath))
                l_sSlamCurPath = c_masterParamPtr->m_slam->m_sPkgPath + "/config/default.yaml";
            try
            {
                YAML::Node l_configBak_ = YAML::LoadFile(l_sSlamBakPath);
                YAML::Node l_configCur_ = YAML::LoadFile(l_sSlamCurPath);
                // 修改驱动模式
                l_configCur_["LaserConfig"]["master"]["online_mode"] = !p_bCurOnlineMode;
                // 修改AGV服务器端口
                if (l_configBak_["LaserConfig"]["agv"]["server_port"])
                    l_configCur_["LaserConfig"]["agv"]["server_port"] =
                        l_configBak_["LaserConfig"]["agv"]["server_port"];
                else
                {
                    // 删除该配置,使用默认值
                    YAML::Node l_agv = l_configCur_["LaserConfig"]["agv"];
                    if (l_agv["server_port"])
                        l_agv.remove("server_port");
                }

                // 修改AGV服务器IP, 备份不存在服务器IP 则删除配置
                if (l_configBak_["LaserConfig"]["agv"]["server_ip"])
                    l_configCur_["LaserConfig"]["agv"]["server_ip"] =
                        l_configBak_["LaserConfig"]["agv"]["server_ip"];
                else
                {
                    // 删除该配置,使用默认值
                    YAML::Node l_agv = l_configCur_["LaserConfig"]["agv"];
                    if (l_agv["server_ip"])
                        l_agv.remove("server_ip");
                }

                // 依次还原雷达的端口和IP
                for (uint32_t i = 0; i < c_masterParamPtr->m_slam->m_lidar.size(); i++)
                {
                    std::string l_sId = c_masterParamPtr->m_slam->m_lidar[i].m_sLaserName;
                    // 相同雷达配置下进行还原
                    if (l_configBak_["LaserConfig"]["lidarInfo"][l_sId]
                        && l_configCur_["LaserConfig"]["lidarInfo"][l_sId])
                    {
                        if (l_configBak_["LaserConfig"]["lidarInfo"][l_sId]["pc_ip"])
                            l_configCur_["LaserConfig"]["lidarInfo"][l_sId]["pc_ip"] =
                                l_configBak_["LaserConfig"]["lidarInfo"][l_sId]["pc_ip"];
                        else
                        {
                            // 删除该配置,使用默认值
                            YAML::Node l_lidar = l_configCur_["LaserConfig"]["lidarInfo"][l_sId];
                            if (l_lidar["pc_ip"])
                                l_lidar.remove("pc_ip");
                        }

                        if (l_configBak_["LaserConfig"]["lidarInfo"][l_sId]["pc_port"])
                            l_configCur_["LaserConfig"]["lidarInfo"][l_sId]["pc_port"] =
                                l_configBak_["LaserConfig"]["lidarInfo"][l_sId]["pc_port"];
                        else
                        {
                            // 删除该配置,使用默认值
                            YAML::Node l_lidar = l_configCur_["LaserConfig"]["lidarInfo"][l_sId];
                            if (l_lidar["pc_port"])
                                l_lidar.remove("pc_port");
                        }

                        if (l_configBak_["LaserConfig"]["lidarInfo"][l_sId]["laser_ip"])
                            l_configCur_["LaserConfig"]["lidarInfo"][l_sId]["laser_ip"] =
                                l_configBak_["LaserConfig"]["lidarInfo"][l_sId]["laser_ip"];
                        else
                        {
                            // 删除该配置,使用默认值
                            YAML::Node l_lidar = l_configCur_["LaserConfig"]["lidarInfo"][l_sId];
                            if (l_lidar["laser_ip"])
                                l_lidar.remove("laser_ip");
                        }

                        if (l_configBak_["LaserConfig"]["lidarInfo"][l_sId]["laser_port"])
                            l_configCur_["LaserConfig"]["lidarInfo"][l_sId]["laser_port"] =
                                l_configBak_["LaserConfig"]["lidarInfo"][l_sId]["laser_port"];
                        else
                        {
                            // 删除该配置,使用默认值
                            YAML::Node l_lidar = l_configCur_["LaserConfig"]["lidarInfo"][l_sId];
                            if (l_lidar["laser_port"])
                                l_lidar.remove("laser_port");
                        }
                    }
                    else
                    {
                        LOGFAE(WERROR, "切换在线失败 | user配置文件和备份user中雷达名不一致");
                        // c_masterParamPtr->m_slam->m_fae.setErrorCode("D6");
                        return false;
                    }
                }

                std::ofstream l_file(c_masterParamPtr->m_slam->m_sPkgPath
                                     + "/config/user.yaml");  //有则打开，没有则创建
                if (l_file.is_open())
                {
                    l_file << l_configCur_ << std::endl;
                    l_file.close();
                    p_bCurOnlineMode = !p_bCurOnlineMode;
                    deleteFileOrDir(l_sSlamBakPath);
                    return true;
                }
                else
                {
                    printf("[error] change offline  yaml fail\n");
                    return false;
                }
            }
            catch (YAML::BadFile& e)
            {
                std::cout << "slam configure yaml read fail or no exist" << std::endl;
            }

            p_bCurOnlineMode = !p_bCurOnlineMode;
            return true;
        }
        else
        {
            printf("[warn] reset online yaml fail: yaml no exist\n");
            return false;
        }
    }
    else
    {
        // 当前为在线->切换离线 读取yaml 备份并修改 online_mode agvPort
        // 确认读取Yaml
        std::string l_sReadPath = c_masterParamPtr->m_slam->m_sPkgPath + "/config/user.yaml";
        if (!isExistFileOrFolder(l_sReadPath))
            l_sReadPath = c_masterParamPtr->m_slam->m_sPkgPath + "/config/default.yaml";
        if (!isExistFileOrFolder(l_sReadPath))
        {
            printf("[warn] reset yaml[%s] fail: yaml no exist\n", l_sReadPath.c_str());
            return false;
        }

        // 备份slamYaml
        copyFileOrFolder(l_sReadPath,
                         c_masterParamPtr->m_slam->m_sPkgPath + "/config/.user.yaml.bak");

        uint32_t l_uiPort = 20000;
        try
        {
            YAML::Node l_config_ = YAML::LoadFile(l_sReadPath);
            // 修改驱动模式
            l_config_["LaserConfig"]["master"]["online_mode"] = !p_bCurOnlineMode;
            // 修改AGV服务器端口和 IP
            l_config_["LaserConfig"]["agv"]["server_port"] = l_uiPort++;
            l_config_["LaserConfig"]["agv"]["server_ip"] = c_masterParamPtr->m_sLocalIP;
            // 依次修改雷达的端口和IP
            for (uint32_t i = 0; i < c_masterParamPtr->m_slam->m_lidar.size(); i++)
            {
                std::string l_sId = c_masterParamPtr->m_slam->m_lidar[i].m_sLaserName;
                if (l_config_["LaserConfig"]["lidarInfo"][l_sId])
                {
                    // ip规则：本机IP  端口
                    l_config_["LaserConfig"]["lidarInfo"][l_sId]["pc_ip"] =
                        c_masterParamPtr->m_sLocalIP;
                    l_config_["LaserConfig"]["lidarInfo"][l_sId]["pc_port"] = l_uiPort++;
                    l_config_["LaserConfig"]["lidarInfo"][l_sId]["laser_ip"] =
                        c_masterParamPtr->m_sLocalIP;
                    l_config_["LaserConfig"]["lidarInfo"][l_sId]["laser_port"] = l_uiPort++;
                }
                else
                    printf("[err]: changeDriverModel_ change Virtual ip port yaml not set\n");
            }

            std::ofstream l_file(c_masterParamPtr->m_slam->m_sPkgPath
                                 + "/config/user.yaml");  //有则打开，没有则创建
            if (l_file.is_open())
            {
                l_file << "# -*- coding: UTF-8 -*-" << std::endl;
                l_file << "# 模式切换生成的文件:" << getTimeNowStr() << std::endl;
                l_file << "# 发行版参数配置文件" << std::endl;
                l_file << l_config_ << std::endl;
                l_file.close();
                p_bCurOnlineMode = !p_bCurOnlineMode;
                return true;
            }
            else
            {
                printf("[error] change offline  yaml fail\n");
                return false;
            }
        }
        catch (YAML::BadFile& e)
        {
            std::cout << "slam configure yaml read fail or no exist" << std::endl;
        }
    }
    return false;
}

void Master::slamStartCb_()
{
    if (c_virtualDriver_)
    {
        c_virtualDriver_->shutdown();
        c_virtualDriver_ = nullptr;
    }

    // 离线模式下启动虚拟驱动，此处要求SLAM启动
    if (c_masterParamPtr->m_slam->m_RunStatus == PROCSTATE::RUNNING
        && !c_masterParamPtr->m_slam->m_bIsOnlineMode)
    {
#pragma region "虚拟驱动"
        c_virtualDriver_.reset(new VirtualDriver());
#pragma endregion
    }

    if (!c_keyboard_)
    {
#pragma region "键盘响应"
        c_keyboard_.reset(new KeyBoard(c_masterParamPtr->m_bIsStart,
                                       c_virtualDriver_,
                                       boost::bind(&Master::setActionCb_, this, _1, _2)));
#pragma endregion
    }
    else
        c_keyboard_->resetDriverPtr(c_virtualDriver_);
}

void Master::slamStopCb_()
{
    c_masterParamPtr->m_slam->m_RunStatus = PROCSTATE::STOPING;
    // 断开和SLAM的连接
    if (c_webnet_)
        c_webnet_->quitWebServer();
    // 关闭虚拟驱动
    if (c_keyboard_)
        c_keyboard_->stop();

    if (c_virtualDriver_)
    {
        c_virtualDriver_->shutdown();
        c_virtualDriver_ = nullptr;
    }

    // closeWanjiOD();
}

int Master::controlSlamStop_(bool p_bIsRestartWeb)
{
    bool l_bRes = false;
    LOGFAE(WINFO, "WLR720混合导航软件SLAM模块执行关闭...");
    slamStopCb_();
    LOGFAE(WINFO, "WLR720混合导航软件SLAM模块关闭中...");
    closeSlam();
    if (!isRunSlam())
    {
        l_bRes = true;
        c_masterParamPtr->m_slam->m_RunStatus = PROCSTATE::NOSTART;
    }
    else
        c_masterParamPtr->m_slam->m_RunStatus = PROCSTATE::STOPERR;
    if (p_bIsRestartWeb && c_webnet_)
        c_webnet_->connectWebServer();
    LOGFAE(WINFO, "WLR720混合导航软件SLAM模块已关闭");
    // 回收堆上已分配的空闲内存
    malloc_trim(0);
    return l_bRes;
}
/**
 * @description: 关闭并重新启动slam,并开辟线程 间隔1s查询SLAM状态,最长持续1min 直到启动并初始化完成
 * @param {*}
 * @return {int} 是否启动成功
 */
int Master::controlSlamStart_()
{
    // 启动前首先检测是否解密，避免进入解密循环
    if (!checkMacNoFromFile(c_masterParamPtr->m_slam->m_sPkgPath + "/config/macLiense.yaml"))
    {
        LOGFAE(WERROR, "SLAM模块启动失败: 软件密钥未通过, 请参照以下步骤进行解密：");
        LOGFAE(WERROR, " *********************************************** ");
        LOGFAE(WERROR, " * 1. 连接web客户端，扫描二维码，将扫描后的内容发送给万集开发人员 ");
        LOGFAE(WERROR, " * 2. 在web端输入万集开发人员发送的密钥即可解密 ");
        LOGFAE(WERROR, " * **********************************************");
        c_masterParamPtr->m_slam->m_fae.setErrorCode("D1");
        LOGFAE(WINFO, "本机校验码: [{}]", getSecretNo().c_str());
        return 0;
    }
    LOGFAE(WINFO, "WLR720混合导航软件SLAM模块执行启动...");
    // 重新读取参数
    readParam_();
    LOGFAE(WINFO, "WLR720混合导航软件SLAM模块启动中...");
    c_masterParamPtr->m_slam->m_RunStatus = PROCSTATE::STARTING;
    //SLAM主函数
    if (!openSlam(c_node_,
                  c_masterParamPtr->m_slam->m_sPkgPath,
                  boost::bind(setLidarData, _1, _2, _3, _4)))
    {
        LOGFAE(WWARN, "******WLR720混合导航软件SLAM模块启动失败! : 重复启动 或 正在关闭******");
        c_masterParamPtr->m_slam->m_fae.setErrorCode("D2");
        return 0;
    }
    // if (c_masterParamPtr->m_bOpenObstacle
    //     && (c_masterParamPtr->m_slam->m_iWorkMode == WorkMode::LocatMode
    //         || c_masterParamPtr->m_slam->m_iWorkMode == WorkMode::UpdateMapMode))
    // {
    //     // 避障模块
    //     openWanjiOD(c_node_,
    //                 c_masterParamPtr->m_slam->m_lidar,
    //                 c_masterParamPtr->m_slam->m_time.m_sTimeProcStart);
    //     std::cout << "finished openWanjiOD\n";
    // }

    // 获取SLAM初始化状态
    if (c_webnet_)
        c_webnet_->getSlamStatus();
    slamStartCb_();
    if (c_masterParamPtr->m_slam->m_RunStatus == PROCSTATE::RUNNING)
        return 1;
    else
        return 0;
}

void Master::startCtrl()
{
    if (c_keyboard_)
        c_keyboard_->startCtrl();
}

void Master::stop()
{
    std::cout << "Master stoping..." << std::endl;
    controlSlamStop_(false);
    if (c_recordControl_)
        c_recordControl_->shutdown();
    std::cout << "Master stoped" << std::endl;
}

void Master::setErrorCode(std::string p_sErrCode)
{
    c_masterParamPtr->m_slam->m_fae.setErrorCode(p_sErrCode);
}

Master::Master(ros::NodeHandle& node) : c_node_(node)
{
    init_();
}

Master::~Master()
{
    printf("exit Master\n");
    c_keyboard_ = nullptr;
    c_netCfg_ = nullptr;
    c_webnet_ = nullptr;
    c_recordControl_ = nullptr;
    c_markApp = nullptr;
    printf("exit Master2\n");
}
}  // namespace wj_slam
/****************************************************************************************/

boost::shared_ptr<wj_slam::Master> g_wjmaster = nullptr;
void MySigintHandler(int sig)
{
    if (sig == SIGSEGV)
    {
        g_wjmaster->setErrorCode("D3");
        LOGFAE(WERROR, "******  WLR720混合导航软件意外关闭, 请联系万集开发人员检查!  ******");
    }
    //这里主要进行退出前的数据保存、内存清理、告知其他节点等工作
    // g_bCheckMacSucc = true;

    usleep(10000);
    if (g_wjmaster)
        g_wjmaster->stop();
    sleep(1);
    g_wjmaster = nullptr;
    LOGFAE(WWARN, "******  {} WLR720混合导航软件关闭成功!  ******", WJLog::getWholeSysTime());
    ros::shutdown();
}

// 通用日志
WJLog* generalLog::wjlog = nullptr;
// 路径日志
WJLog* generalLog::pathlog = nullptr;
// 校验日志
WJLog* generalLog::checklog = nullptr;
// 速度日志
WJLog* generalLog::speedlog = nullptr;
// 对外日志
WJLog* generalLog::faelog = nullptr;
#ifdef LOG_PREMOD
// 预处理日志
WJLog* generalLog::prlog = nullptr;
// 特征提取日志
WJLog* generalLog::felog = nullptr;
// 里程计日志
WJLog* generalLog::odlog = nullptr;
// 定位日志
WJLog* generalLog::lmlog = nullptr;
// 子图日志
WJLog* generalLog::submaplog = nullptr;
// 预估里程计日志
WJLog* generalLog::odfslog = nullptr;
// 驱动日志
WJLog* generalLog::drlog = nullptr;
// 解包子日志
WJLog* generalLog::convlog = nullptr;
// 协议日志
WJLog* generalLog::protolog = nullptr;
// web日志
WJLog* generalLog::weblog = nullptr;
#endif
#ifdef LOG_TIMECOST
// 耗时日志
WJLog* generalLog::tclog = nullptr;
#endif
int main(int argc, char** argv)
{
    wj_slam::s_masterCfg* g_masterParamPtr; //创建全局参数指针
    g_masterParamPtr = wj_slam::s_masterCfg::getIn();//获取全局参数

    // 读取参数 静态指针无须传参
    wj_slam::Param l_param(false); //创建SLAM参数
    std::string path = ros::package::getPath("wj_slam");
    l_param.loadSysParam(path);

    // 挂载日志模块
    glog = new generalLog(g_masterParamPtr->m_slam->m_fae.m_sLogPath,
                          "wj",
                          FileMode::DAILY_ROTATE,
                          LogMode::TRIAL_MODE,
                          g_masterParamPtr->m_slam->m_fae.getLogFileSizeByte());
    LOGFAE(WINFO, "");
    LOGFAE(WINFO, "****** {} WLR720混合导航软件启动... ******", WJLog::getWholeSysTime());
    LOGFAE(WINFO, "****** 软件版本号: {} ******", g_masterParamPtr->m_slam->m_sVersion);
    // 重设日志等级
    glog->changeMode(g_masterParamPtr->m_slam->m_fae.m_iLogLevel);
    LOGW(WINFO,
         "{} ****** Set Manual LogLevel: {} LogSize: {} MB ******",
         WJLog::getWholeSysTime(),
         g_masterParamPtr->m_slam->m_fae.m_iLogLevel,
         g_masterParamPtr->m_slam->m_fae.m_uiLogFileSize);
    // 设置glibc中使用brk/mmap分配内存的阈值，128kb
    mallopt(M_MMAP_THRESHOLD, 128 * 1024);
    ros::init(argc, argv, "wanji_master");
    ros::NodeHandle c_node;
    signal(SIGINT, MySigintHandler);
    signal(SIGTERM, MySigintHandler);
    // signal(SIGSEGV, MySigintHandler);
    sleep(2);
    ros::Rate loop_rate(100);
    if (ros::ok())
    {
        g_wjmaster.reset(new wj_slam::Master(c_node));
        while (ros::ok())
        {
            ros::spinOnce();
            g_wjmaster->startCtrl();
            loop_rate.sleep();
        }
        std::cout << "ros no ok!" << std::endl;
    }
    return 0;
}