/*
 * @Author: your name
 * @Date: 2021-07-06 09:48:06
 * @LastEditTime: 2021-07-20 13:52:27
 * @LastEditors: zushuang
 * @Description: In User Settings Edit
 * @FilePath: /720slam/src/wanji_slam/test/loop.hpp
 */
#pragma once
#ifndef _LOOP_H_
#    define _LOOP_H_
#    include "algorithm/loop/closureLoopNew.h"
#    include "algorithm/optimize/impl/laserTransform.hpp"
#    include "common/common_ex.h"
#    include <boost/function.hpp>
#    include <boost/shared_ptr.hpp>
#    include <mutex>
#    include <pcl/io/file_io.h>
#    include <thread>
#    include <vector>

namespace wj_slam {
template <typename P> class LoopGraph {
  private:
    typedef KEYFRAME<P> MapFrame;
    typedef typename MapFrame::Ptr MapFramePtr;
    typedef pcl::PointCloud<P> PointCloudMap;
    typedef typename PointCloudMap::Ptr PointCloudMapPtr;

  private:
    boost::shared_ptr<clousureLoop<P>> c_pLoop_;
    boost::shared_ptr<LaserTransform<P>> c_pTrans_;
    bool c_bShutDown_ = false;
    bool c_bLoopRun_ = false;
    std::mutex& c_mMutexPC_;
    std::mutex c_mMutexFlag_;
    std::vector<MapFramePtr>& c_vKFMap_;
    std::vector<int> c_viMayLoopIndex_;
    bool c_bFindLoop_ = false;
    bool c_bLoopOptDone_ = true;
    bool c_bNotInit = true;
    bool c_bIsCheckLoop_ = false;  //是否正在检测回环
    bool c_bCheckIndex_ = true;
    bool c_bRenewAllPc_ = true;
    int c_iCurrUseKFIndex_ = -1;  //当前地图使用的最远帧
    int c_iCurrKFIndex_ = -1;     //当前地图帧
    int c_iLoopKFIndex_ = -1;     //检测到的回环帧
    int c_iLastLoopIndex_ = 0;
    float c_afVariances_[6] = {1e-6, 1e-6, 1e-6, 1e-6, 1e-6, 1e-6};
    s_POSE6D c_stLastCheckPose_;
    s_POSE6D c_stPoseLastest_;
    boost::function<void(bool)> c_loopCb_;
    void checkLoopThread_();
    /**
     * @description: isStartLoop_:是否开始回环
     * @param {*}
     * @return {bool} true:开始回环
     * @other:
     * 返回true条件：1.找到回环 2.上一次回环已经做完
     * 3.剩余全部点云已经更新完毕 4.当前回环帧不会影响当前定位(只有设置checkIdx时)
     */
    bool isStartLoop_();
    void paramRenew_();

  public:
    LoopGraph(std::vector<MapFramePtr>& p_vKFMaps,
              std::mutex& p_mutex,
              boost::function<void(bool)> p_loopCb);
    ~LoopGraph();
    void start();
    void shutDowm();
    void stop();
    bool isStop();
    /**
     * @description:
     * @param {*}
     * @return {bool} true/false Busy/Free
     */
    bool isLoopOpt(void);
    void renewPoseGraph();
    void renewPoseGraphWhole();
    void setLastestMapIndex(int p_iCurrUseKFIndex_);
    /**
     * @description: checkLoop
     * 返回是否检测到回环匹配
     * @param {int} p_iCurrUseKFIndex_
     * 外部使用地图的最远帧号
     * @param {vector<int>} p_viMayLoopIndex
     * 检测到回环地图的帧号
     * @return {bool}
     * @other:
     * 返回false条件: 1. 当前帧距离上一次回环帧太近; 2. 正在进行回环匹配
     * 3.开启回环检测线程（@3早于@2一帧）
     * 返回true条件:  1. 已经找到回环
     * 开始检测回环匹配条件(checkLoopThread_): 1.当前帧距离上一次回环帧足够远
     * 2.没有进行回环匹配 3.没有进行回环修正 4.没有找到回环
     */
    bool checkLoop(std::vector<int> p_viMayLoopIndex);
    bool checkLoop(void);
    void renewAllKeyFrame();
    void optForce();
    void run();
};

}  // namespace wj_slam

#    ifdef WJSLAM_NO_PRECOMPILE
#        include "impl/loop.hpp"
#    else
#        define WJSLAM_Loop(P) template class wj_slam::LoopGraph<P>;
#    endif
#endif
