/*
 * @Author: your name
 * @Date: 2021-04-27 19:17:34
 * @LastEditTime: 2021-06-30 21:19:18
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /wanji_16_laser/src/wanji_slam/test/wanjislam.cpp
 */
/*
 * This file is part of lslidar_n301 driver.
 *
 * The driver is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * The driver is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with the driver.  If not, see <http://www.gnu.org/licenses/>.
 */

#include "wanji_slam.h"
#include "net_app/netApp.h"
#include "net_app/net_Message_Proc.h"
#include "param.hpp"
namespace wj_slam {

class WanJiSLAMNodelet : public nodelet::Nodelet {
  private:
    SYSPARAM c_sysParam_;

  public:
    WanJiSLAMNodelet() {}
    ~WanJiSLAMNodelet() {}

  private:
#pragma region "ros"
    ros::NodeHandle node;
    ros::Publisher outputCorn_;
    ros::Publisher outputSurf_;
    ros::Publisher outputPath_;
    ros::Publisher outputPose_;
    ros::Publisher outputPathOdom_;
    ros::Publisher outputPathHPOdom_;
    ros::Subscriber wanji_scan_;
    ros::Subscriber workMode_;
#pragma endregion

    virtual void onInit();
    std::queue<FEATURE_PAIR_PTR> c_featureQueue_;
    std::queue<KEYFRAME_PTR> c_keyFramesQueue_;
    PreProcess<PCurXYZHSV>::Ptr c_preProcess_;
    Odom_Ptr c_odom_;
    Location_Ptr c_location_;
    int timenow;
    double t1, t2;
    std::mutex lockKeyframe_;
    nav_msgs::Path c_pathMsg_;
    nav_msgs::Path c_pathOdomMsg_;
    nav_msgs::Path c_pathHPOdomMsg_;
    void odomCallback(FEATURE_PAIR_PTR& p_fEPair)
    {
        static bool init = false;
        if (!init)
        {
            timenow = p_fEPair->m_dTimestamp;
            init = true;
        }
        // wjPrint(COLOR_BLUE, "time : ", p_fEPair->m_dTimestamp - timenow);
        c_featureQueue_.push(p_fEPair);
        // std::thread showth(&WanJiSLAMNodelet::show, this);
        // showth.detach();
    }
    void locationCallback(KEYFRAME_PTR p_keyFrame)
    {
        c_keyFramesQueue_.push(p_keyFrame);
        // std::thread showth(&WanJiSLAMNodelet::show, this);
        // showth.detach();
    }
    void locationOutputCb(s_POSE6D& p_pose)
    {
        c_odom_->renewPrecisionPose(p_pose);
    }
    template <typename PcType, typename MapType>
    void showPC(boost::shared_ptr<KEYFRAME<PcType>> pc,
                boost::shared_ptr<KEYFRAME<MapType>> map = nullptr)
    {
        // c_featureQueue_.pop();
        sensor_msgs::PointCloud2 msgPC;
        typename pcl::PointCloud<PcType>::Ptr outPC(new pcl::PointCloud<PcType>());
        pcl::PointCloud<pcl::PointXYZI>::Ptr outPCXYZI(new pcl::PointCloud<pcl::PointXYZI>());
        int offset = 0;
        *outPC = *pc->m_pFeature->first;
        offset = outPC->points.size();
        *outPC += *pc->m_pFeature->second;
        pcl::copyPointCloud(*outPC, *outPCXYZI);
        for (int i = 0; i < offset; i++)
        {
            outPCXYZI->points[i].intensity = 1;
        }
        pcl::toROSMsg(*outPCXYZI, msgPC);
        msgPC.header.frame_id = "world";
        msgPC.header.stamp = ros::Time::now();
        outputCorn_.publish(msgPC);
        if (map && outputSurf_.getNumSubscribers())
        {
            pcl::PointCloud<pcl::PointXYZI>().swap(*outPCXYZI);
            sensor_msgs::PointCloud2 msgMap;
            typename pcl::PointCloud<MapType>::Ptr outMap(new pcl::PointCloud<MapType>());
            // *outMap = *map->m_pFeature->first;
            // *outMap += *map->m_pFeature->second;
            *outMap = *map->m_pFeature->first;
            offset = outMap->points.size();
            *outMap += *map->m_pFeature->second;
            pcl::copyPointCloud(*outMap, *outPCXYZI);
            for (int i = 0; i < offset; i++)
            {
                outPCXYZI->points[i].intensity = 1;
            }
            pcl::toROSMsg(*outPCXYZI, msgMap);
            msgMap.header.frame_id = "world";
            msgMap.header.stamp = ros::Time::now();
            outputSurf_.publish(msgMap);
            pcl::PointCloud<MapType>().swap(*outMap);
        }
        pcl::PointCloud<PcType>().swap(*outPC);
        pcl::PointCloud<pcl::PointXYZI>().swap(*outPCXYZI);
    }
    template <typename PcType, typename MapType>
    void pcCallback(boost::shared_ptr<KEYFRAME<PcType>> pc,
                    boost::shared_ptr<KEYFRAME<MapType>> map = nullptr)
    {
        std::thread showpc(&WanJiSLAMNodelet::showPC<PcType, MapType>, this, pc, map);
        showpc.detach();
    }
    void pcCallbackLocation(std::vector<FEATURE_PAIR_PTR> pose)
    {
        FEATURE_PAIR_PTR pairTmp = *pose.begin();
        // c_featureQueue_.pop();
        pcl::PointCloud<pcl::PointXYZHSV>::Ptr outPC(new pcl::PointCloud<pcl::PointXYZHSV>());
        *outPC = *pairTmp->first;
        *outPC += *pairTmp->second;
        sensor_msgs::PointCloud2 msg;
        pcl::toROSMsg(*outPC, msg);
        msg.header.frame_id = "world";
        msg.header.stamp = ros::Time::now();
        outputCorn_.publish(msg);
    }
    void poseCallback(std::vector<s_POSE6D> pose)
    {
        geometry_msgs::PoseStamped lpose;
        lpose.header.frame_id = "world";
        lpose.header.stamp = ros::Time::now();
        lpose.pose.orientation.w = pose.begin()->m_quat.w();
        lpose.pose.orientation.x = pose.begin()->m_quat.x();
        lpose.pose.orientation.y = pose.begin()->m_quat.y();
        lpose.pose.orientation.z = pose.begin()->m_quat.z();
        lpose.pose.position.x = pose.begin()->m_trans.x();
        lpose.pose.position.y = pose.begin()->m_trans.y();
        lpose.pose.position.z = pose.begin()->m_trans.z();
        if (outputPose_.getNumSubscribers() > 0)
        {
            outputPose_.publish(lpose);
        }
        if (outputPath_.getNumSubscribers() > 0)
        {
            c_pathMsg_.header.frame_id = "world";
            c_pathMsg_.header.stamp = ros::Time::now();
            c_pathMsg_.poses.push_back(lpose);
            outputPath_.publish(c_pathMsg_);
        }

        // c_keyFramesQueue_.push(p_keyFrame);
        // std::thread showth(&WanJiSLAMNodelet::show, this);
        // showth.detach();
    }
    void poseCallbackOdom(std::vector<s_POSE6D> pose)
    {
        geometry_msgs::PoseStamped lpose;
        lpose.header.frame_id = "world";
        lpose.header.stamp = ros::Time::now();
        lpose.pose.orientation.w = pose.begin()->m_quat.w();
        lpose.pose.orientation.x = pose.begin()->m_quat.x();
        lpose.pose.orientation.y = pose.begin()->m_quat.y();
        lpose.pose.orientation.z = pose.begin()->m_quat.z();
        lpose.pose.position.x = pose.begin()->m_trans.x();
        lpose.pose.position.y = pose.begin()->m_trans.y();
        lpose.pose.position.z = pose.begin()->m_trans.z();
        if (outputPathOdom_.getNumSubscribers() > 0)
        {
            c_pathOdomMsg_.header.frame_id = "world";
            c_pathOdomMsg_.header.stamp = ros::Time::now();
            c_pathOdomMsg_.poses.push_back(lpose);
            outputPathOdom_.publish(c_pathOdomMsg_);
        }
        lpose.header.frame_id = "world";
        lpose.header.stamp = ros::Time::now();
        lpose.pose.orientation.w = pose[1].m_quat.w();
        lpose.pose.orientation.x = pose[1].m_quat.x();
        lpose.pose.orientation.y = pose[1].m_quat.y();
        lpose.pose.orientation.z = pose[1].m_quat.z();
        lpose.pose.position.x = pose[1].m_trans.x();
        lpose.pose.position.y = pose[1].m_trans.y();
        lpose.pose.position.z = pose[1].m_trans.z();
        if (outputPathHPOdom_.getNumSubscribers() > 0)
        {
            c_pathHPOdomMsg_.header.frame_id = "world";
            c_pathHPOdomMsg_.header.stamp = ros::Time::now();
            c_pathHPOdomMsg_.poses.push_back(lpose);
            outputPathHPOdom_.publish(c_pathHPOdomMsg_);
        }

        // c_keyFramesQueue_.push(p_keyFrame);
        // std::thread showth(&WanJiSLAMNodelet::show, this);
        // showth.detach();
    }
    // bool changeWorkMode(WorkMode p_workMode)
    // {
    //     bool res = false;
    //     switch (p_workMode)
    //     {
    //         case WorkMode::StandByMode:
    //             c_odom_->stop();
    //             while (!c_odom_->isStop())
    //                 sleepMs(10);
    //             c_location_->stop();
    //             while (!c_location_->isStop())
    //                 sleepMs(10);
    //             res = true;
    //             break;

    //         case WorkMode::InitMapMode:
    //             if (!c_odom_->isStop() || !c_location_->isStop())  //没有先切换成standby模式
    //             {
    //                 res = false;
    //                 break;
    //             }
    //             c_odom_->disableOnlyLocationMode();
    //             c_odom_->start();

    //             c_location_->disableOnlyLocationMode();
    //             c_location_->start();
    //             /* code */
    //             break;
    //         case WorkMode::ContMapMode:
    //             if (!c_odom_->isStop() || !c_location_->isStop())  //没有先切换成standby模式
    //             {
    //                 res = false;
    //                 break;
    //             }
    //             //加载已存在地图
    //             c_odom_->disableOnlyLocationMode();
    //             c_odom_->start();

    //             c_location_->disableOnlyLocationMode();
    //             c_location_->start();
    //             /* code */
    //             break;
    //         case WorkMode::LocatMode:
    //             if (!c_odom_->isStop() || !c_location_->isStop())  //没有先切换成standby模式
    //             {
    //                 res = false;
    //                 break;
    //             }
    //             //加载地图文件
    //             c_odom_->enableOnlyLocationMode();
    //             c_odom_->start();
    //             c_location_->enableOnlyLocationMode();
    //             c_location_->start();
    //             /* code */
    //             break;

    //         default: break;
    //     }

    //     return res;
    // }
    void show()
    {
        FEATURE_PAIR_PTR& pairTmp = c_featureQueue_.back();
        if (outputCorn_.getNumSubscribers())
        {
            sensor_msgs::PointCloud2 msg;
            pcl::toROSMsg(*pairTmp->first, msg);
            msg.header.frame_id = "wanji";
            msg.header.stamp = ros::Time::now();
            outputCorn_.publish(msg);
        }
        if (outputSurf_.getNumSubscribers())
        {
            sensor_msgs::PointCloud2 msg;
            pcl::toROSMsg(*pairTmp->second, msg);
            msg.header.frame_id = "wanji";
            msg.header.stamp = ros::Time::now();
            outputSurf_.publish(msg);
        }

        // c_preProcess_->releaseMemory(pairTmp);
    }
    // void workModeCb(std_msgs::Int32 msg)
    // {
    //     std::cout << "work mode" << msg.data << std::endl;
    //     changeWorkMode(WorkMode(msg.data));
    // }
};
// #define offsetof(TYPE, MEMBER) ((size_t) & ((TYPE*)0)->MEMBER)

void WanJiSLAMNodelet::onInit()
{
    node = getNodeHandle();
    Param l_param;
    std::string path = ros::package::getPath("wj_slam");
    l_param.loadSysParam(path);
    wj_unpack::s_Config l_LaserConfig;
    l_LaserConfig = l_param.getLaserConfig();
    c_sysParam_ = l_param.getSLAMConfig();
#pragma region "preProcess"
    c_preProcess_.reset(new PreProcess<PCurXYZHSV>(
        &l_LaserConfig, boost::bind(&WanJiSLAMNodelet::odomCallback, this, _1)));
#pragma endregion

#pragma region "odom"
    c_odom_.reset(
        new Odometry<PCurXYZHSV>(c_featureQueue_,
                                 boost::bind(&WanJiSLAMNodelet::locationCallback, this, _1),
                                 c_sysParam_,
                                 offsetof(PCurXYZHSV, s),
                                 -1,
                                 boost::bind(&WanJiSLAMNodelet::poseCallbackOdom, this, _1),
                                 NULL));
    // boost::bind(&WanJiSLAMNodelet::poseCallback, this, _1)
    // boost::bind(&WanJiSLAMNodelet::pcCallback, this, _1)
    std::thread odom(&Odometry<PCurXYZHSV>::run, c_odom_);
#pragma endregion

#pragma region "location"

    c_location_.reset(new Location<PCurXYZHSV, PMapXYZ>(
        c_keyFramesQueue_,
        lockKeyframe_,
        boost::bind(&WanJiSLAMNodelet::locationOutputCb, this, _1),
        c_sysParam_,
        boost::bind(&WanJiSLAMNodelet::poseCallback, this, _1),
        boost::bind(&WanJiSLAMNodelet::pcCallback<PCurXYZHSV, PMapXYZ>, this, _1, _2)));

    std::thread location(&Location<PCurXYZHSV, PMapXYZ>::run, c_location_);

#pragma endregion

    // #pragma region "show"
    //     std::thread show(&WanJiSLAMNodelet::show, this);
    // #pragma endregion

#pragma region "netApp"
    WJNetApp netApp(c_odom_, c_location_, (void*)&c_sysParam_);
    std::thread netAppTh(&WJNetApp::start, netApp);
#pragma endregion

#pragma region "webNet"
    NetMessagePro webNet(c_odom_, c_location_, node, (void*)&c_sysParam_);
#pragma endregion

#pragma region "ros"
    outputCorn_ = node.advertise<sensor_msgs::PointCloud2>("wanji_corn", 10);
    outputSurf_ = node.advertise<sensor_msgs::PointCloud2>("wanji_surf", 10);
    outputPath_ = node.advertise<nav_msgs::Path>("wanji_path", 10);
    outputPathOdom_ = node.advertise<nav_msgs::Path>("wanji_odom", 10);
    outputPathHPOdom_ = node.advertise<nav_msgs::Path>("wanji_HPodom", 10);
    outputPose_ = node.advertise<geometry_msgs::PoseStamped>("wanji_pose", 10);
    wanji_scan_ = node.subscribe<wanji_msgs::WanjiScan>(
        "wanji_packets", 10, &PreProcess<PCurXYZHSV>::processScan, c_preProcess_);
    // workMode_ = node.subscribe<std_msgs::Int32>("workMode", 1, &WanJiSLAMNodelet::workModeCb,
    // this);
#pragma endregion
    // LaserTransform<pcl::PointXYZHSV> lt;
    // changeWorkMode(WorkMode::InitMapMode);
    // lt.setTimeOffset(offsetof(pcl::PointXYZHSV, s));
    // changeWorkMode(WorkMode::InitMapMode);
    netAppTh.join();
    odom.join();
    location.join();
    // show.join();
}

}  // namespace wj_slam

// parameters: class type, base class type
PLUGINLIB_EXPORT_CLASS(wj_slam::WanJiSLAMNodelet, nodelet::Nodelet)
