/*
 * @Author: your name
 * @Date: 2021-04-21 17:00:17
 * @LastEditTime: 2021-10-25 13:45:42
 * @LastEditors: zushuang
 * @Description: In User Settings Edit
 * @FilePath: /catkin_ws/src/wanji_amcl3d/src/amcl/main.cpp
 */
/*!
 * @file main.cpp
 * @copyright Copyright (c) 2019, FADA-CATEC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// #include "Node.h"

// int main(int argc, char** argv)
// {
//   ros::init(argc, argv, "amcl3d_node");

//   printf("[%s] Node initialization.", ros::this_node::getName().data());

//   if (!ros::master::check())
//   {
//     printf("[%s] Roscore is not running.", ros::this_node::getName().data());
//     return EXIT_FAILURE;
//   }

//   amcl3d::Node node;
//   node.spin();

//   printf("[%s] Node finished.", ros::this_node::getName().data());

//   return EXIT_SUCCESS;
// }
#include "amcl/amcl.h"
#include "headerRos.h"

using namespace wanjilocal;

class AmclRosTest {
  public:
    AmclRosTest() : c_nh_("~"), c_bSubmap_(false), c_bLoadMap_(false)
    {
        c_sub_ = c_nh_.subscribe<sensor_msgs::PointCloud2>(
            "/wanji_point", 20, &AmclRosTest::handler_, this);
        c_pubPf_ = c_nh_.advertise<geometry_msgs::PoseArray>("/particle_cloud", 1);
        c_pubMean_ = c_nh_.advertise<geometry_msgs::PoseArray>("/mean_cloud", 1);
        c_pubMergeMap_ = c_nh_.advertise<sensor_msgs::PointCloud2>("/map_merge", 1);

        c_pubPfCcloud_ = c_nh_.advertise<sensor_msgs::PointCloud2>("/scan_cloud", 1);

        c_pAmcl_.reset(new wanjilocal::Amcl2D<pcl::PointXYZ, pcl::PointXYZ>());
    }

    ~AmclRosTest() {}

    void buildMeanPoseMsg(geometry_msgs::PoseArray& p_msg) const
    {
        // int size = p_.size()+1;
        // msg.poses.resize(size);
        geometry_msgs::Pose l_point;
        uint32_t l_i = 0;

        l_point.position.x = static_cast<double>(c_stPfMean_.x);
        l_point.position.y = static_cast<double>(c_stPfMean_.y);
        l_point.position.z = static_cast<double>(c_stPfMean_.z);
        l_point.orientation = tf::createQuaternionMsgFromYaw(c_stPfMean_.a);
        p_msg.poses.push_back(l_point);
    }

    void buildParticlesPoseMsg(geometry_msgs::PoseArray& p_msg) const
    {
        // int size = p_.size()+1;
        // msg.poses.resize(size);
        geometry_msgs::Pose l_point;
        uint32_t l_i = 0;
        for (l_i = 0; l_i < c_vecPtc_.size(); ++l_i)
        {
            l_point.position.x = static_cast<double>(c_vecPtc_[l_i].x);
            l_point.position.y = static_cast<double>(c_vecPtc_[l_i].y);
            l_point.position.z = static_cast<double>(c_vecPtc_[l_i].z);
            l_point.orientation = tf::createQuaternionMsgFromYaw(c_vecPtc_[l_i].a);
            p_msg.poses.push_back(l_point);
        }

        // point.position.x = static_cast<double>(mean_.x);
        // point.position.y = static_cast<double>(mean_.y);
        // point.position.z = static_cast<double>(mean_.z);
        // point.orientation = tf::createQuaternionMsgFromYaw(mean_.a);
        // msg.poses.push_back(point);

        // point.orientation = tf::createQuaternionMsgFromYaw(3.1415926);
        // printf("3 : %f | %f \n",point.orientation.z,point.orientation.w);
        // point.orientation = tf::createQuaternionMsgFromYaw(-3.1415926);
        // printf("-3: %f | %f \n",point.orientation.z,point.orientation.w);
    }

    bool loadMap()
    {
        //输入地图，创建栅格概率地图
        wanjilocal::Amcl2D<pcl::PointXYZ, pcl::PointXYZ>::cloudMapPtr l_pcPath(
            new pcl::PointCloud<pcl::PointXYZ>());

        wanjilocal::Amcl2D<pcl::PointXYZ, pcl::PointXYZ>::cloudMapPtr l_pcMapSurf(
            new pcl::PointCloud<pcl::PointXYZ>());
        wanjilocal::Amcl2D<pcl::PointXYZ, pcl::PointXYZ>::cloudMapPtr l_pcMapCorner(
            new pcl::PointCloud<pcl::PointXYZ>());
        wanjilocal::Amcl2D<pcl::PointXYZ, pcl::PointXYZ>::cloudMapPtr l_pcMapMerge(
            new pcl::PointCloud<pcl::PointXYZ>());
        wanjilocal::Amcl2D<pcl::PointXYZ, pcl::PointXYZ>::cloudMapPtr l_pcSubMap(
            new pcl::PointCloud<pcl::PointXYZ>());
        std::string l_path = "/home/<USER>/catkin_ws/src/wanji_amcl/map/lowpark";

        if (pcl::io::loadPCDFile<pcl::PointXYZ>(l_path + "_2.pcd", *l_pcMapSurf) == -1
            || pcl::io::loadPCDFile<pcl::PointXYZ>(l_path + "_1.pcd", *l_pcMapCorner) == -1
            || pcl::io::loadPCDFile<pcl::PointXYZ>(l_path + "_3.pcd", *l_pcPath) == -1)
        {
            PCL_ERROR("Couldn't read file map.pcd\n");
            return false;
        }

        *l_pcMapMerge = *l_pcMapSurf + *l_pcMapCorner;
        c_initTrans_(0) = 0.1;
        c_initTrans_(1) = 0.1;
        getSubMap(l_pcMapMerge, l_pcSubMap, 30);

        c_pAmcl_->setCloudHeightRange(0.5, 1.7);
        c_pAmcl_->setMapType(MAP_DIMENSION::MAP_2D);
        c_pAmcl_->setCloudMap(l_pcSubMap);

        c_bLoadMap_ = true;

        return true;
    }

    void pubMap()
    {
        if (!c_bLoadMap_)
            return;

        static tf::TransformBroadcaster l_br;
        tf::Transform l_transform;
        tf::Quaternion l_q;
        Eigen::Vector3d l_tMapCurr = c_pAmcl_->getTransCurr();
        Eigen::Quaterniond l_qMapCurr = c_pAmcl_->getQuaCurr();

        l_transform.setOrigin(tf::Vector3(l_tMapCurr(0), l_tMapCurr(1), l_tMapCurr(2)));
        l_q.setW(l_qMapCurr.w());
        l_q.setX(l_qMapCurr.x());
        l_q.setY(l_qMapCurr.y());
        l_q.setZ(l_qMapCurr.z());
        l_transform.setRotation(l_q);
        l_br.sendTransform(tf::StampedTransform(
            l_transform, ros::Time().fromSec(c_dTimeMsgCur_), "wanji_map", "wanji_base"));

        //显示全部地图
        sensor_msgs::PointCloud2::Ptr l_pcMergeMapMsg(new sensor_msgs::PointCloud2());
        wanjilocal::Amcl2D<pcl::PointXYZ, pcl::PointXYZ>::cloudMapPtr l_pcMapMerge =
            c_pAmcl_->getMap();

        pcl::toROSMsg(*l_pcMapMerge, *l_pcMergeMapMsg);
        l_pcMergeMapMsg->header.stamp = ros::Time().fromSec(c_dTimeMsgCur_);
        l_pcMergeMapMsg->header.frame_id = "wanji_map";
        c_pubMergeMap_.publish(l_pcMergeMapMsg);
    }

    void publishParticles_()
    {
        // geometry_msgs::PoseArray l_msgPf;
        // c_pAmcl_->getParticlePoseMsg(l_msgPf);
        // l_msgPf.header.stamp = ros::Time().fromSec(c_dTimeMsgCur_);
        // l_msgPf.header.frame_id = "wanji_map";
        // c_pubPf_.publish(l_msgPf);

        // geometry_msgs::PoseArray l_msgMean;
        // c_pAmcl_->getMeanPoseMsg(l_msgMean);
        // l_msgMean.header.stamp = ros::Time().fromSec(c_dTimeMsgCur_);
        // l_msgMean.header.frame_id = "wanji_map";
        // c_pubMean_.publish(l_msgMean);

        // pub scan cloud
        sensor_msgs::PointCloud2::Ptr l_scanCloudMsg(new sensor_msgs::PointCloud2());
        wanjilocal::Amcl2D<pcl::PointXYZ, pcl::PointXYZ>::cloudScanPtr l_pcScan;
        l_pcScan = c_pAmcl_->getScanCloud();
        pcl::toROSMsg(*l_pcScan, *l_scanCloudMsg);
        l_scanCloudMsg->header.stamp = ros::Time().fromSec(c_dTimeMsgCur_);
        l_scanCloudMsg->header.frame_id = "wanji_base";
        c_pubPfCcloud_.publish(l_scanCloudMsg);

        // printf("amcl publishParticles\n");
    }

    void handler_(const sensor_msgs::PointCloud2ConstPtr& p_msg)
    {
        printf("recv msg\n");
        if (c_bLoadMap_)
        {
            printf("process msg");
            c_dTimeMsgCur_ = p_msg->header.stamp.toSec();
            c_pcFeatureLast_.reset(new pcl::PointCloud<pcl::PointXYZ>());
            pcl::fromROSMsg(*p_msg, *c_pcFeatureLast_);

            //保存返回定位结果
            double l_dTx = -1;
            double l_dTy = -1;
            double l_dTheta = 0;

            c_pAmcl_->setInitialPose(c_initTrans_(0), c_initTrans_(1), 0.1, 0.1, 0.3);

            bool l_bLocated = c_pAmcl_->amclCallBack(c_pcFeatureLast_, l_dTx, l_dTy, l_dTheta);
            double l_dScore = c_pAmcl_->getScore();

            publishParticles_();

            printf("Located result: x=%.3f; y=%.3f; a=%.3f; score=%.3f\n",
                   l_dTx,
                   l_dTy,
                   l_dTheta,
                   l_dScore);
        }
    }

    void getSubMap(const pcl::PointCloud<pcl::PointXYZ>::Ptr p_wholeMap,
                   pcl::PointCloud<pcl::PointXYZ>::Ptr p_subMap,
                   float p_radious)
    {
        pcl::KdTreeFLANN<pcl::PointXYZ>::Ptr kdtreeFeatureMap(new pcl::KdTreeFLANN<pcl::PointXYZ>);
        std::vector<int> indexs;
        std::vector<float> dists;
        pcl::PointXYZ* pointtmp;

        kdtreeFeatureMap->setInputCloud(p_wholeMap);

        pcl::PointXYZ l_pt;
        l_pt.x = c_initTrans_(0);
        l_pt.y = c_initTrans_(1);
        l_pt.z = 0;
        kdtreeFeatureMap->radiusSearch(l_pt, p_radious, indexs, dists);

        int cnt = 0;

        p_subMap->resize(indexs.size());
        for (std::vector<int>::iterator it = indexs.begin(); it != indexs.end(); it++)
        {
            pointtmp = &p_wholeMap->points[*it];
            p_subMap->points[cnt] = *pointtmp;
            cnt++;
        }

        p_subMap->width = cnt;
    }

  private:
    boost::shared_ptr<wanjilocal::Amcl2D<pcl::PointXYZ, pcl::PointXYZ>> c_pAmcl_;
    // wanjilocal::Amcl2D<pcl::PointXYZ, pcl::PointXYZ> amcl_test;
    ros::NodeHandle c_nh_;
    ros::Subscriber c_sub_;  //订阅激光点云
                             // ros::Publisher pub;
    ros::Publisher c_pubPf_;
    ros::Publisher c_pubMean_;
    ros::Publisher c_pubPfCcloud_;  //发布点云
    ros::Publisher c_pubPfCloudMatch_;
    ros::Publisher c_pubMergeMap_;

    std::vector<s_Particle> c_vecPtc_;

    s_Particle c_stPfMean_;

    pcl::PointCloud<pcl::PointXYZ>::Ptr c_pcMapMerge_;
    double c_dTimeMsgCur_ = 0.0;  //当前帧时间戳

    pcl::PointCloud<pcl::PointXYZ>::Ptr c_pcFeatureLast_;
    bool c_bSubmap_;
    bool c_bLoadMap_;

    Eigen::Vector3d c_initTrans_;
};

int main(int argc, char** argv)
{
    ros::init(argc, argv, "wanji_amcl");
    printf("\033[1;32m---->\033[0m wanji_amcl Started.");
    AmclRosTest l_amclRos;
    l_amclRos.loadMap();

    ros::Rate rate(1000);
    while (ros::ok())
    {
        ros::spinOnce();

        l_amclRos.pubMap();
        rate.sleep();
    }

    return 0;
}