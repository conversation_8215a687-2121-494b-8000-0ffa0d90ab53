#ifndef _DATAPREPROCESS_
#define _DATAPREPROCESS_
#include <iostream>
#include <string.h>
#include <queue>
#include <mutex>

#include <ros/ros.h>
#include <sensor_msgs/PointCloud2.h>


#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl_conversions/pcl_conversions.h>
#include <pcl/filters/filter_indices.h>


#include "../slam/slam.h"
#include "../../include/common/common_ex.h"
#include "../../include/common_WLR720.h"
#include "../../include/wj_log.h"

#include "common_msgs/sensorgps.h"

using namespace std;



namespace wj_slam { 
    enum laserNameENUM{
        WLR720FCW,
        VLP16
    };

    class DataPreprocess{
        public:
            typedef pcl::PointCloud<pcl::PointXYZHSV> Feature;
            typedef boost::shared_ptr<Feature> PointCloudPtr;
            typedef boost::function<void(uint32_t,
                                    s_PCloud (&)[WLR720_SCANS_PER_FIRING],
                                    s_PCloud&,
                                    PointCloudPtr&,
                                    timeMs,
                                    timeMs,
                                    u_int32_t)> FeatureCb;

            DataPreprocess(ros::NodeHandle& node, const std::string& topicName, const std::string& laserName, 
                FeatureCb feCallBack, 
                boost::function<bool (const double& syncFrameStamp)> gpspreprocess,
                std::deque<common_msgs::sensorgps::ConstPtr>& gpsDeque
                   );
            ~DataPreprocess();

            ros::NodeHandle m_node;
            ros::Subscriber m_topicSubscriber;

            void callbackPointCloud(const sensor_msgs::PointCloud2ConstPtr& msgPtr);
            void processPointCloud(std::deque<common_msgs::sensorgps::ConstPtr>& gpsDeque);

            void processWanjiPointCloud(const sensor_msgs::PointCloud2ConstPtr& msgPtr, std::deque<common_msgs::sensorgps::ConstPtr>& gpsDeque);
            void processVLPPointCloud(const sensor_msgs::PointCloud2ConstPtr& msgPtr, std::deque<common_msgs::sensorgps::ConstPtr>& gpsDeque);

        private:

            SYSPARAM* c_sysParam_;    /**< 外部传参结构体 */
            FeatureCb c_feCallBack;
            boost::function<bool (const double& syncFrameStamp)> c_gpspreprocess;

            std::string m_topicSubscribedName;
            std::string m_laserName;
            std::queue<sensor_msgs::PointCloud2ConstPtr> m_pointCloudPtrQueue;
            sensor_msgs::PointCloud2ConstPtr m_pointCloudPtr;
            std::deque<common_msgs::sensorgps::ConstPtr> c_gpsDeque; /**< gps */
            std::mutex m_dataMutex;
            bool c_isProcessedGPS = false; 

            bool m_pointsSetMatrix[64][1800];//TODO 根据lidar类型填充线数
            std::vector<std::vector<pcl::PointXYZI>> m_pointsMatrix;
            int m_frameCountTemp = 0;
            s_PCloud c_pcRawOut[64];  //参数3
            s_PCloud l_pcMidOut;      // 参数4

        
    };

} // namespace wj_slam



#endif