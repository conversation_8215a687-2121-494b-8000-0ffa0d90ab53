/*******************************
 * @Descripttion:
 * @version:
 * @Author: zushu<PERSON>
 * @Date: 2021-04-28 14:42:39
 * @LastEditors: zushuang
 * @LastEditTime: 2021-05-16 16:26:01
 *******************************/
#pragma once
#ifndef _PREPROC_H_
#    define _PREPROC_H_
#    include "algorithm/preproc/driver/wanji_driver.h"
#    include "algorithm/preproc/feature_extract/featureExtract.h"
// #    include "algorithm/preproc/feature_extract/impl/curbDetection.hpp"
#    include "algorithm/imu/imuFilter.h"
#    include "algorithm/preproc/feature_extract/markRec.h"
#    include "algorithm/preproc/unpack/convert.h"
#    include "algorithm/preproc/unpack/convert_720NP.h"
#    include "common/common_ex.h"
#    include "impl/timeSyncSensorFusion.hpp"
#    include "tic_toc.h"
#    include <boost/function.hpp>
#    include <thread>
#    include <math.h>
// #define PRINT
// #define FE_DE

// typedef std::pair<FeaturePtr, FeaturePtr> FEATURE_PAIR<P>;
namespace wj_slam {
template <typename P> class PreProcess {
  public:
    typedef boost::shared_ptr<PreProcess> Ptr;

  private:
    typedef typename FEATURE_PAIR<P>::Ptr FeaturePairPtr;
    typedef typename s_LIDAR_RAW_DATAS::Ptr RawDataPtr;
    typedef pcl::PointCloud<P> Feature;
    typedef boost::shared_ptr<Feature> FeaturePtr;
    typedef boost::shared_ptr<Feature> PointCloudPtr;
    typedef std::pair<uint32_t, RawDataPtr> RawDataPair;
    typedef boost::function<void(int, FeaturePairPtr&)> FE_OUTPUT_CB;

    std::vector<boost::shared_ptr<wj_FE::FeatureExtract>> c_vpFExter;
    std::vector<boost::shared_ptr<Mark_Rec>> c_vpMarkExter_;
    std::vector<boost::shared_ptr<wanji_driver::wanjiDriver>> c_vpDriver_;
    std::vector<boost::shared_ptr<wanji_driver::Convert>> c_vpConv_;
    boost::shared_ptr<imuFilter> c_imuFilter_;
    // std::vector<boost::shared_ptr<CurbDetector<P>>> c_vpCurbDector_;
    boost::shared_ptr<TimeSync> c_pSync_;
    boost::function<void(uint32_t, RawDataPtr&, int, bool)> c_wjOdCallback_;
    boost::function<void(FeaturePairPtr&)> c_SyncOutputCb_;
    SYSPARAM* c_stSysParam_;
    FE_OUTPUT_CB c_fEOutputCb_;  //特征提取完毕回调
    std::mutex c_FECBlock_;
    std::mutex c_imuMtx_; /**< imu数据锁*/
    std::map<int, wj_slam::IMUPerScan> m_qIMURawData_;

    // location模块自用结构体,用来模块内控制,此结构体一般保存Location模块初始化后不再改变参数
    struct Config
    {
      private:
        std::string Bool[2] = {"false", "true"};
        std::string WorkModeStr[5] = {"StandByMode",
                                      "InitMapMode",
                                      "ContMapMode",
                                      "LocatMode",
                                      "UpdateMapMode"};
        std::string ScenModeStr[5] = {"IndoorMode", "OutDoorMode", "VacrousMode", "LaborMode"};
        std::string TimeSourceStr[3] = {"Local", "Lidar", "Other"};

      public:
        WorkMode m_workMode;  //当前工作模式
        ScenMode m_ScenMode;  //当前场景模式
        TimeSource m_TimeSource;
        bool m_bIsUseImu;
        Config()
        {
            this->reset();
        }
        void reset()
        {
            m_workMode = WorkMode::StandByMode;
            m_ScenMode = ScenMode::OUTDOOR;
            m_TimeSource = TimeSource::Local;
            m_bIsUseImu = false;
        }
        void printf()
        {
            wjPrint(WJCOL_GREEN, "[Pre Param] WorkMode", WorkModeStr[int(m_workMode)]);
            wjPrint(WJCOL_GREEN, "[Pre Param] ScenMode", ScenModeStr[int(m_ScenMode)]);
            wjPrint(WJCOL_GREEN, "[Pre Param] ScenMode", TimeSourceStr[int(m_TimeSource)]);
            wjPrint(WJCOL_GREEN, "[Pre Param] UseImu", Bool[int(m_bIsUseImu)]);
        }
    };
    Config c_sConfig_;

    void setStatus_();
    void netErrorCb_(uint32_t p_iLaserId);
    void
    processScanCb(uint p_uiLidarID, RawDataPtr& p_pScanMsg, int p_iFrameId, bool isReNewConnect);

    /**
     * @function: fECallback
     * @description: 点云解析后回调
     * @param {s_PCloud}pc: 点云存储位置 {double} timestamp : 点云时间戳
     * @input: null
     * @output: pc,timestamp
     * @return {*}
     * @others: null
     */
    void fECallback(uint32_t p_iLaserId,
                    s_PCloud (&pc)[WLR720_SCANS_PER_FIRING],
                    s_PCloud& pm,
                    PointCloudPtr& allPC,
                    timeMs timestamp,
                    timeMs recvTimestamp,
                    u_int32_t scanFrameID);
    void imuCallback(uint32_t p_iLaserId, const IMUPerScan& p_imuData);
    bool getIMUData(const uint32_t& p_iLaserId, const int& p_iScanId, IMUPerScan& p_getData);

    /**
     * @description: 修改配置
     * @note: 首先根据场景设定特征提取参数组，然后根据模式补充特征提取参数组
     * @param {*}
     * @return {*}
     * @other:
     */
    void configFromScen_FExt_(uint32_t p_Id);
    void setPp_StandBy_();
    void setPp_InitMap_();
    void setPp_ContMap_();
    void setPp_Locat_();
    void setPp_UpdateMap_();
    /**
     * @description: 根据设定文件修改配置
     * @param {*}
     * @return {*}
     * @other:
     */
    void configFromFile_FExt_(uint32_t p_Id);
    void configPrintf_FExt_(uint32_t p_Id);
    void syncDataCallback_(FeaturePairPtr& p_fEPair);

  public:
    /**
     * @brief 设置雷达使能
     *
     */
    void resetLidarEnableStatus();

    /**
     * @brief 设置雷达转移和盲区
     *
     */
    void resetTransToBase();
    /**
     * @brief 通过工作&场景模式切换参数
     *
     * @param[in] p_WorkMode 工作模式
     * @param[in] p_ScenMode 场景模式
     * @return [true] [成功]
     * @return [false] [失败]
     */
    bool setModeConfig(int p_WorkMode, int p_ScenMode);

#    ifdef FE_DE
    pcl::PointCloud<pcl::PointXYZHSV>::Ptr getSegmentCloud(u_int p_uiLidarID)
    {
        return c_vpFExter[p_uiLidarID]->getSegmentCloud();
    }
    pcl::PointCloud<pcl::PointXYZI>::Ptr getDownSizeCloud(u_int p_uiLidarID)
    {
        return c_vpFExter[p_uiLidarID]->getDownSizeCloud();
    }
    pcl::PointCloud<pcl::PointXYZHSV>::Ptr getAllCloud(u_int p_uiLidarID)
    {
        return c_vpFExter[p_uiLidarID]->getAllCloud();
    }
#    endif

    template <typename T> void setFEOutputCallback(T p_fEOutputCb)
    {
        std::lock_guard<std::mutex> l_mtx(c_FECBlock_);
        c_fEOutputCb_ = FE_OUTPUT_CB(p_fEOutputCb);
    }
    FE_OUTPUT_CB getFEOutputCallback()
    {
        return c_fEOutputCb_;
    }
    
    void runFECallback(uint32_t p_iLaserId,
                            s_PCloud (&pc)[WLR720_SCANS_PER_FIRING],
                            s_PCloud& pm,
                            PointCloudPtr& allPC,
                            timeMs timestamp,
                            timeMs recvTimestamp,
                            u_int32_t scanFrameID);

    void start();
    void shutDown();
    PreProcess(boost::function<void(FeaturePairPtr&)> p_callback,
               boost::function<void(uint32_t, RawDataPtr&, int, bool)> p_wjOdCallback);
    ~PreProcess();
};
}  // namespace wj_slam
#    ifdef WJSLAM_NO_PRECOMPILE
#        include "impl/pre_proc.hpp"
#    else
#        define WJSLAM_PREPROC(P) template class wj_slam::PreProcess<P>;
#    endif
#endif