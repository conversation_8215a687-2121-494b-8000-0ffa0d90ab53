/**
 * @file oprateFile.h
 * <AUTHOR> (chenjun<PERSON>@wanji.net.cn)
 * @brief 离线后端的文件操作集合
 * @version 1.0
 * @date 2022-09-23
 *
 * @copyright Copyright (c) 2022 <PERSON><PERSON>
 */
#pragma once
#include "common/type/type_frame.h"
#include "tool/fileTool/fileTool.h"
#include <fstream>
#include <iostream>
#include <map>
#include <opencv2/highgui/highgui.hpp>
#include <opencv2/imgcodecs.hpp>
#include <pcl/io/pcd_io.h>
#include <pcl/point_cloud.h>
#include <sstream>

namespace wj_slam {
/**
 * \defgroup offLineBackEnd offLine-BackEnd
 * @brief 离线化SLAM后端模组
 */
/**
 * @class oprateFile oprateFile.h "oprateFile.h"
 * @brief 离线后端的文件操作集合类
 * @ingroup offLineBackEnd
 * @tparam PT 点类型
 */
template <typename PT = pcl::PointXYZHSV> class oprateFile {
  public:
    using uid = int;                                  /**< 子图ID类型 */
    using FeaturePair = FEATURE_PAIR<PT>;             /**< 输入特征组类型 */
    using FeaturePairPtr = typename FeaturePair::Ptr; /**< 输入特征组指针类型 */
    using Pose = s_POSE6D;                            /**< 6D位姿别名 */
    using Twist = s_POSE6D;                           /**< 6D速度别名 */
    using MapFrame = KEYFRAME<PT>;                    /**< 输入帧类型 */
    using MapFramePtr = typename MapFrame::Ptr;       /**< 输入帧指针类型 */
  public:
    std::string c_sPath = "";  /**< 待填充的外层文件夹路径 */
    std::string c_sKFs = "KF"; /**< 未使用的帧文件夹 */

    std::string c_sAl = "all.pcd";    /**< 全部点云文件名 */
    std::string c_sFi = "fi.pcd";     /**< 角点点云文件名 */
    std::string c_sSe = "se.pcd";     /**< 面点点云文件名 */
    std::string c_sMark = "mark.pcd"; /**< 面点点云文件名 */
    std::string c_sCu = "curb.pcd";   /**< 中线点云文件名 */
    std::string c_sPt = "p.pcd";      /**< 路径点云文件名 */
    std::string c_sPs = "pose.csv";   /**< 位姿及信息文件名 */
    std::string c_sSc = "sc.tif";     /**< 描述符图片文件名 */
  public:
    /**
     * @brief Construct a new oprate File object
     *
     */
    oprateFile(std::string p_sFolderPath);
    /**
     * @brief Destroy the oprate File object
     *
     */
    ~oprateFile();
    /**
     * @brief 设置主文件夹
     * @note exp：../../online
     * @param[in] p_sFolderPath 主文件夹路径
     * @return [true] [设置成功]
     * @return [false] [设置失败]
     */
    bool setFolder(std::string p_sFolderPath);
    /**
     * @brief Get the Folder object
     *
     * @return [std::string] [主文件路径]
     */
    std::string getFolder();
    /**
     * @brief 获取KF文件夹
     * @note exp：../../KF/
     * @return [std::string] [KF文件夹]
     */
    std::string KFFolder()
    {
        // std::cout << "KFFolder(): " + c_sPath + c_sKFs + "/" << std::endl;
        return c_sPath + c_sKFs + "/";
    }
    /**
     * @brief 获取KF下X帧文件夹
     * @note exp：../../KF/0/
     * @param[in] p_uiID 帧号
     * @return [std::string] [KF/X/文件夹]
     */
    std::string KFXFolder(uid p_uiID)
    {
        return KFFolder() + std::to_string(p_uiID) + "/";
    }

    /**
     * @brief 写入指定*帧*数据
     *
     * @param[in] p_pPCs 数据
     * @param[in] p_pose 位置
     */
    void saveOneFrame(FeaturePairPtr p_pPCs, Pose& p_pose);

    /**
     * @brief 读取指定*帧*数据
     *
     * @param[out] p_pPCs 数据
     * @param[out] p_pose 位置
     */
    void loadOneFrame(FeaturePairPtr p_pPCs, Pose& p_pose);

    /**
     * @brief 搜索指定文件夹内所有的*数字*文件夹
     *
     */
    bool findFrameListInFolder(std::string p_sFileDir, std::vector<uid>& p_vIDList);

  private:
    /**
     * @brief 写入指定*帧*数据
     *
     * @param[in] p_sFilePath  文件夹路径
     * @param[in] p_pPCs 点云数据
     * @param[in] p_pose 位置数据
     * @param[in] p_twist 速度数据
     */
    void writeOneFrame_(std::string p_sFilePath, FeaturePairPtr p_pPCs, Pose& p_pose);

    /**
     * @brief 写入指定*描述符*数据
     *
     * @param[in] p_sFilePath 文件路径
     * @param[in] matrix 输入容器
     */
    void writeOneScanContext_(std::string p_sFilePath, Eigen::MatrixXd& matrix);

    /**
     * @brief 读取指定*帧*数据
     *
     * @param[in] p_sFilePath  文件夹路径
     * @param[in] p_pPCs 点云数据
     * @param[in] p_pose 位置数据
     * @param[in] p_twist 速度数据
     */
    void readOneFrame_(std::string p_sFilePath, FeaturePairPtr p_pPCs, Pose& p_pose);

    /**
     * @brief 读取指定*描述符*数据
     *
     * @param[in] p_sFilePath 文件路径
     * @param[in] matrix 输入容器
     */
    void readOneScanContext_(std::string p_sFilePath, Eigen::MatrixXd& matrix);
};

template <typename PT> oprateFile<PT>::oprateFile(std::string p_sFolderPath)
{
    setFolder(p_sFolderPath);
}

template <typename PT> oprateFile<PT>::~oprateFile() {}

template <typename PT> bool oprateFile<PT>::setFolder(std::string p_sFolderPath)
{
    c_sPath = p_sFolderPath + "/";
    std::cout << "oprateFile<PT>::setFolder: " + c_sPath << std::endl;
    if (isExistFileOrFolder(KFFolder()))
    {
        makeDir(KFFolder());
        return true;
    }
    std::cout << "oprateFile<PT>::setFolder error " + p_sFolderPath << std::endl;
    return false;
}

template <typename PT> std::string oprateFile<PT>::getFolder()
{
    return c_sPath;
}

template <typename PT> void oprateFile<PT>::saveOneFrame(FeaturePairPtr p_pPCs, Pose& p_pose)
{
    writeOneFrame_(KFXFolder(p_pPCs->m_uiScanFrame), p_pPCs, p_pose);
}

template <typename PT> void oprateFile<PT>::loadOneFrame(FeaturePairPtr p_pPCs, Pose& p_pose)
{
    readOneFrame_(KFXFolder(p_pPCs->m_uiScanFrame), p_pPCs, p_pose);
}

template <typename PT>
bool oprateFile<PT>::findFrameListInFolder(std::string p_sFileDir, std::vector<uid>& p_vIDList)
{
    std::cout << "find FrameList In Folder " + p_sFileDir << std::endl;
    uint32_t id;
    std::vector<std::string> p_vFolderList;
    findFolderInFolder_(p_sFileDir, p_vFolderList);
    for (std::string l_str : p_vFolderList)
    {
        std::stringstream sin(l_str);
        if (sin >> id)
            p_vIDList.push_back(id);
    }
    if (p_vIDList.empty())
    {
        std::cout << "FrameList In Folder" + p_sFileDir + " is empty." << std::endl;
        return false;
    }
    std::sort(p_vIDList.begin(), p_vIDList.end());
    p_vIDList.erase(std::unique(p_vIDList.begin(), p_vIDList.end()), p_vIDList.end());
    return true;
}

template <typename PT>
void oprateFile<PT>::writeOneFrame_(std::string p_sFilePath, FeaturePairPtr p_pPCs, Pose& p_pose)
{
    // std::cout << "write to " + p_sFilePath << std::endl;
    std::string l_sAl = p_sFilePath + "/" + c_sAl;
    std::string l_sFi = p_sFilePath + "/" + c_sFi;
    std::string l_sSe = p_sFilePath + "/" + c_sSe;
    std::string l_sMark = p_sFilePath + "/" + c_sMark;
    std::string l_sCu = p_sFilePath + "/" + c_sCu;
    std::string l_sPs = p_sFilePath + "/" + c_sPs;
    std::string l_sSc = p_sFilePath + "/" + c_sSc;

    makeDir(p_sFilePath);

    // preprocess
    uid l_uiID = p_pPCs->m_uiScanFrame;
    double l_iTimeStamp = p_pPCs->m_tsSyncTime;
    Pose l_pose = p_pose;

    // pose.csv
    std::ofstream l_filePoseWR(l_sPs, std::ios::out | std::ios::trunc);
    if (!l_filePoseWR)
        std::cout << "Error File:" << l_sPs.c_str() << std::endl;
    else
    {
        // l_filePoseWR << "x,y,z,roll,pitch,yaw,m_fPercent" << std::endl;
        l_filePoseWR << l_pose.m_trans[0] << "," << l_pose.m_trans[1] << "," << l_pose.m_trans[2]
                     << "," << l_pose.roll() << "," << l_pose.pitch() << "," << l_pose.yaw() << ","
                     << l_pose.m_fPercent << ",";
        // l_filePoseWR << "id,time,sampleSize, m_bHasContext" << std::endl;
        l_filePoseWR << l_uiID << "," << l_iTimeStamp << "," << p_pPCs->m_iSample2ndSize << ","
                     << p_pPCs->m_bHasContext << std::endl;
        l_filePoseWR.close();
    }
    // 4x.pcd
    if (!p_pPCs->allPC->empty())
        pcl::io::savePCDFileBinary(l_sAl, *(p_pPCs->allPC));
    if (!p_pPCs->first->empty())
        pcl::io::savePCDFileBinary(l_sFi, *(p_pPCs->first));
    if (!p_pPCs->second->empty())
        pcl::io::savePCDFileBinary(l_sSe, *(p_pPCs->second));
    if (!p_pPCs->third->empty())
    {
        pcl::io::savePCDFileBinary(l_sMark, *(p_pPCs->third));
    }
    if (!p_pPCs->fourth->empty())
        pcl::io::savePCDFileBinary(l_sCu, *(p_pPCs->fourth));
    if (p_pPCs->m_bHasContext)
        writeOneScanContext_(l_sSc, *p_pPCs->sc);
}

template <typename PT>
void oprateFile<PT>::writeOneScanContext_(std::string p_sFilePath, Eigen::MatrixXd& matrix)
{
    int rows = matrix.rows();
    int cols = matrix.cols();
    cv::Mat image = cv::Mat::zeros(rows, cols, CV_32FC1);

    for (int y = 0; y < rows; ++y)
    {
        for (int x = 0; x < cols; ++x)
        {
            // get pixel
            image.at<float>(y, x) = matrix(y, x);
        }
    }
    if (!image.empty())
        cv::imwrite(p_sFilePath, image);
}

template <typename PT>
void oprateFile<PT>::readOneFrame_(std::string p_sFilePath, FeaturePairPtr p_pPCs, Pose& p_pose)
{
    if (nullptr == p_pPCs)
    {
        std::cout << "Input pointer is empty" << std::endl;
        return;
    }

    // std::cout << "read from " + p_sFilePath << std::endl;
    std::string l_sAl = p_sFilePath + "/" + c_sAl;
    std::string l_sFi = p_sFilePath + "/" + c_sFi;
    std::string l_sSe = p_sFilePath + "/" + c_sSe;
    std::string l_sCu = p_sFilePath + "/" + c_sCu;
    std::string l_sPs = p_sFilePath + "/" + c_sPs;
    std::string l_sSc = p_sFilePath + "/" + c_sSc;

    // pose.csv
    std::ifstream l_Psfile(l_sPs.c_str());
    if (!l_Psfile)
        std::cout << "Error File" << l_sPs.c_str() << std::endl;
    else
    {
        double p_dData[7];
        int p_iData[4];
        std::string str;
        // 第一行
        getline(l_Psfile, str);
        // 逗号分隔依次取出
        char* p = strtok((char*)str.data(), ",");
        //  顺序 "x,y,z,roll,pitch,yaw,m_fPercent,id,time,sampleSize,m_bHasContext"
        for (int ie = 0; ie < 7; ++ie)
        {
            p_dData[ie] = atof(p);
            p = strtok(NULL, ",");
        }
        for (int ie = 7; ie < 11; ++ie)
        {
            p_iData[ie - 7] = atoi(p);
            p = strtok(NULL, ",");
        }
        l_Psfile.close();
        // 填写
        p_pose.m_trans << p_dData[0], p_dData[1], p_dData[2];
        p_pose.setRPY(p_dData[3], p_dData[4], p_dData[5]);
        p_pose.m_fPercent = p_dData[6];
        p_pPCs->m_uiScanFrame = p_iData[0];
        p_pPCs->m_tsSyncTime = p_iData[1];
        p_pPCs->m_iSample2ndSize = p_iData[2];
        p_pPCs->m_bHasContext = p_iData[3];
    }
    // 4x .pcd
    pcl::io::loadPCDFile<PT>(l_sAl, *p_pPCs->allPC);
    pcl::io::loadPCDFile<PT>(l_sFi, *p_pPCs->first);
    pcl::io::loadPCDFile<PT>(l_sSe, *p_pPCs->second);
    pcl::io::loadPCDFile<PT>(l_sCu, *p_pPCs->fourth);

    if (p_pPCs->m_bHasContext)
        readOneScanContext_(l_sSc, *p_pPCs->sc);
}

template <typename PT>
void oprateFile<PT>::readOneScanContext_(std::string p_sFilePath, Eigen::MatrixXd& matrix)
{
    //按原格式读取
    cv::Mat image = cv::imread(p_sFilePath, cv::IMREAD_UNCHANGED);
    if (!image.empty())
    {
        int rows = image.rows;
        int cols = image.cols;
        matrix.resize(rows, cols);

        for (int y = 0; y < rows; ++y)
        {
            for (int x = 0; x < cols; ++x)
            {
                // get pixel
                matrix(y, x) = static_cast<double>(image.at<float>(y, x));
            }
        }
    }
}

}  // namespace wj_slam

#define PGO_oprateFile(P) template class oprateFile<P>;
