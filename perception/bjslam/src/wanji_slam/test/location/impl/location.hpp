/*
 * @Description:
 * @Version: 1.0
 * @Autor: zushu<PERSON>
 * @Date: 2021-04-28 19:56:24
 * @LastEditors: shuangquan han
 * @LastEditTime: 2023-11-28 16:28:13
 */
#pragma once
#ifndef _LOCATION_HPP_
#    define _LOCATION_HPP_
#    include "../location.h"
namespace wj_slam {

template <typename C, typename M> void Location<C, M>::setFirstGPSInfo(const GPSInfo& gpsInfo){
    c_sGPSInfoFirst = gpsInfo;
}

template <typename C, typename M> void Location<C, M>::setGPSInfo(const GPSInfo& gpsInfo){
    
    c_sGPSInfo = gpsInfo;
    std::cout << "setGPSInfo...rawstatus = " << static_cast<int>(c_sGPSInfo.s_sensorgps.rawstatus) << std::endl;
    static bool l_isGPSInitialized = false;
    if(!l_isGPSInitialized){
        if(c_stSysParam_->m_iWorkMode == WorkMode::LocatMode){
            c_sGPSInfoFirst = c_sGPSInfo;
        }
        
        c_sGPSInfoLast = c_sGPSInfo;
    }
     
    // GPS加入约束条件：1.GPS状态可用；2.距离间隔大于1米；3.第一帧GPS数据
    float l_UTMDistance_x = c_sGPSInfo.s_vCurUTM[0] - c_sGPSInfoLast.s_vCurUTM[0];
    float l_UTMDistance_y = c_sGPSInfo.s_vCurUTM[1] - c_sGPSInfoLast.s_vCurUTM[1];
    float l_UTMDistance_z = c_sGPSInfo.s_vCurUTM[2] - c_sGPSInfoLast.s_vCurUTM[2];
    float l_UTMDIstance = sqrt(l_UTMDistance_x * l_UTMDistance_x + l_UTMDistance_y * l_UTMDistance_y);
    // std::cout <<"gps translation XYZ: " << l_UTMDistance_x <<", " << l_UTMDistance_y <<", " <<  l_UTMDistance_z  << std::endl;
    if((c_sGPSInfo.s_sensorgps.rawstatus == 50 && c_sGPSInfoLast.s_sensorgps.rawstatus == 50)  || !l_isGPSInitialized){ // && l_UTMDIstance > 0.1
        c_isGPSUseful = true;
    }
    else{
        c_isGPSUseful = false;
    }
    std::cout <<"gps c_isGPSUseful " << c_isGPSUseful << std::endl;
    
    float deltaUTM_x = c_sGPSInfo.s_vCurUTM[0] - c_sGPSInfoFirst.s_vCurUTM[0];
    float deltaUTM_y = c_sGPSInfo.s_vCurUTM[1] - c_sGPSInfoFirst.s_vCurUTM[1];
    float deltaUTM_z = c_sGPSInfo.s_vCurUTM[2] - c_sGPSInfoFirst.s_vCurUTM[2];
    // std::cout <<"gps delta UTM XYZ: " << deltaUTM_x <<", " << deltaUTM_y <<", " <<  deltaUTM_z  
    //     <<", cur UTM XYZ: " << c_sGPSInfo.s_vCurUTM[0] <<", " << c_sGPSInfo.s_vCurUTM[1]
    //     <<", first UTM XYZ: " << c_sGPSInfoFirst.s_vCurUTM[0] <<", " << c_sGPSInfoFirst.s_vCurUTM[1] << std::endl;


    Eigen::Vector3d inputUTMPosition{deltaUTM_x, deltaUTM_y, 0};
    Eigen::Vector3d objectPositionInCarBackFRU;
    Eigen::Vector3d selfCarUTMPosition{0.0, 0.0, 0.0};
    Eigen::Vector3d inputPositionENUInNorthHeading;
	inputPositionENUInNorthHeading[0] = inputUTMPosition[0] - selfCarUTMPosition[0];
	inputPositionENUInNorthHeading[1] = inputUTMPosition[1] - selfCarUTMPosition[1];
	inputPositionENUInNorthHeading[2] = inputUTMPosition[2] - selfCarUTMPosition[2];
    Eigen::Vector3d selfCarEulerXYZDegree{c_sGPSInfoFirst.s_sensorgps.roll, c_sGPSInfoFirst.s_sensorgps.pitch, c_sGPSInfoFirst.s_sensorgps.heading};
    Eigen::MatrixX3d rotation = Eigen::AngleAxisd(selfCarEulerXYZDegree(2) * M_PI / 180.0, Eigen::Vector3d::UnitZ()).matrix()
			* Eigen::AngleAxisd(selfCarEulerXYZDegree(0) * M_PI / 180.0, Eigen::Vector3d::UnitX()).matrix()
			* Eigen::AngleAxisd(selfCarEulerXYZDegree(1) * M_PI / 180.0, Eigen::Vector3d::UnitY()).matrix();
	Eigen::Vector3d objectPositionInCarCenterFRU;
	objectPositionInCarCenterFRU(0) = rotation(0,0) * inputPositionENUInNorthHeading(0) + rotation(0,1) * inputPositionENUInNorthHeading(1) + rotation(0,2) * inputPositionENUInNorthHeading(2);
	objectPositionInCarCenterFRU(1) = rotation(1,0) * inputPositionENUInNorthHeading(0) + rotation(1,1) * inputPositionENUInNorthHeading(1) + rotation(1,2) * inputPositionENUInNorthHeading(2) ;
	objectPositionInCarCenterFRU(2) = rotation(2,0) * inputPositionENUInNorthHeading(0) + rotation(2,1) * inputPositionENUInNorthHeading(1) + rotation(2,2) * inputPositionENUInNorthHeading(2);
	
	objectPositionInCarBackFRU[0] = objectPositionInCarCenterFRU[0];
	objectPositionInCarBackFRU[1] = objectPositionInCarCenterFRU[1];
	objectPositionInCarBackFRU[2] = objectPositionInCarCenterFRU[2];
    // std::cout <<"gps delta objectPositionInCarBackFRU: " << objectPositionInCarBackFRU[0] 
    //     <<", " << objectPositionInCarBackFRU[1] <<", " <<  objectPositionInCarBackFRU[2]  << std::endl;
    
    // TODO 转到XXX前左上坐标系
    c_GPSPose6D.m_trans << objectPositionInCarBackFRU[1], -objectPositionInCarBackFRU[0],objectPositionInCarBackFRU[2];


    // TODO  计算方式
    float l_rollGap = c_sGPSInfo.s_sensorgps.roll - c_sGPSInfoFirst.s_sensorgps.roll;
    float l_pitchGap = c_sGPSInfo.s_sensorgps.pitch - c_sGPSInfoFirst.s_sensorgps.pitch;
    float l_headingGap = c_sGPSInfo.s_sensorgps.heading - c_sGPSInfoFirst.s_sensorgps.heading;
    l_headingGap = -l_headingGap;//转成通里程计因子输入一致，里程计因子输入的是顺时针0~180为负，逆时针0~180为正
    

    std::cout <<"cur heading: " << c_sGPSInfo.s_sensorgps.heading
        <<", first heading: " << c_sGPSInfoFirst.s_sensorgps.heading  << std::endl;

    Eigen::AngleAxisf rotationVectorX = Eigen::AngleAxisf(l_rollGap * M_PI / 180.0, Eigen::Vector3f::UnitX());
    Eigen::AngleAxisf rotationVectorY = Eigen::AngleAxisf(l_pitchGap * M_PI / 180.0, Eigen::Vector3f::UnitY());
    Eigen::AngleAxisf rotationVectorZ = Eigen::AngleAxisf(l_headingGap * M_PI / 180.0, Eigen::Vector3f::UnitZ());
    Eigen::Matrix3f rotationMatrix = (rotationVectorX * rotationVectorY * rotationVectorZ).matrix();
    Eigen::Quaternion rotationQuaternion = rotationVectorX * rotationVectorY * rotationVectorZ;
    c_GPSPose6D.m_quat.x() = rotationQuaternion.x();
    c_GPSPose6D.m_quat.y() = rotationQuaternion.y();
    c_GPSPose6D.m_quat.z() = rotationQuaternion.z();
    c_GPSPose6D.m_quat.w() = rotationQuaternion.w();

    l_isGPSInitialized = true;
    c_sGPSInfoLast = c_sGPSInfo;
    
}

template <typename C, typename M> void Location<C, M>::paramReset_()
{
    if (c_pGraph_)
        c_pGraph_ = nullptr;
    c_bHasInit = false;
    c_bIsKeyFrame_ = false;
    c_CurrLocStatus_ = LocationStatus::CONTINUS_SLAM;
    c_pKeyFrameMap_.reset(new KeyFrameMap());
    c_pKfMapPairNewAdd_.reset(new KfMapPair());
    c_pSubmap_.reset(new Submap(c_pKeyFrameMap_));
    c_pcKeyPoseSearch_.reset(new pcl::PointCloud<POSE>());
    c_pstLocaliVoxMap_.reset(new MapFrame());
    c_pstWholeMap_.reset(new MapFrame());
    c_pcMergeMapCloud_.reset(new pcl::PointCloud<M>());
    c_pcPredPoseBox_.reset(new pcl::PointCloud<pcl::PointXYZ>());
    if (OptimizeMapType::IVOX_TYPE == c_stSysParam_->m_map.m_iOptimizeModel)
        c_pIvoxGrid_->start();
    FilterFactory::getInstance()->setNearByPath(c_pKeyFrameMap_->getKfPose());
}
template <typename C, typename M> void Location<C, M>::matchValueInit()
{
    c_bManualPoseModel = true;
    c_nManualPoseModelNum = 0;
}

template <typename C, typename M> void Location<C, M>::loopCallback_(bool b_loopDone)
{
    c_bLoopHasDone_ = b_loopDone;
}
template <typename C, typename M> void Location<C, M>::paramInit_()
{
    c_pMatcher_->setMaxIterations(1);  // 设置迭代次数
    c_pMatcher_->setMatch2D(true);     // 设置角点2D匹配功能
    // 设置棚顶与地面高度阈值
    c_pMatcher_->setwallhigh(c_stSysParam_->m_map.m_fGroundHigh, c_stSysParam_->m_map.m_fRoofHigh);
    // 设置匹配与优化模块选择Kdtree、iVox
     std::cout << "hsq: location.hpp paramInit_() m_iOptimizeModel = "
               << c_stSysParam_->m_map.m_iOptimizeModel 
               <<", m_bOpenPoseCheck = " << c_stSysParam_->m_posCheck.m_bOpenPoseCheck 
               <<", m_bOpenOccupyVerify = " << c_stSysParam_->m_posCheck.m_bOpenOccupyVerify 
               <<", m_fKdMatchNumPercent = " << c_stSysParam_->m_posCheck.m_fKdMatchNumPercent 
               <<", m_bOpenOccupyVerify = " << c_stSysParam_->m_posCheck.m_bOpenOccupyVerify << std::endl;
    c_pMatcher_->setOptimizeModel(c_stSysParam_->m_map.m_iOptimizeModel);
    // 设置双层墙过滤Z值高度阈值
    c_pMatcher_->setFilterZValue(c_stSysParam_->m_map.m_fFilterZValue);
    // 区分靶标导航还是slam导航
    c_pMatcher_->setSlamModel(c_stSysParam_->m_iSlamMode);
    // 设置靶标权重最小值与最大值
    c_pMatcher_->setMarkWight(c_stSysParam_->m_bMarkWeightMin, c_stSysParam_->m_bMarkWeightMax);
    // 启动概率校验，则匹配内部点数得分将为安全阈值0.1
    if (c_stSysParam_->m_posCheck.m_bOpenPoseCheck
        && (!c_stSysParam_->m_posCheck.m_bOpenOccupyVerify)){
        c_pMatcher_->setMatchScoreThr(
            c_stSysParam_->m_posCheck.m_fKdMatchNumPercent);  // 设置最小匹配数
            std::cout <<"hsq location.hpp paramInit_ c_fMatchPrecentThr_ = " <<  c_stSysParam_->m_posCheck.m_fKdMatchNumPercent << std::endl;
    }
    else{
        c_pMatcher_->setMatchScoreThr(0.1);
            std::cout <<"hsq location.hpp  paramInit_ c_fMatchPrecentThr_ = " << 0.1 << std::endl;
    }

    // c_pAMCLer_->setCloudHeightRange(-0.2, 0.2);
    // c_pAMCLer_->setMapType(MAP_DIMENSION::MAP_2D);
    // c_pAMCLer_->setOverlapThresh(0.3);
    // c_pAMCLer_->setGridmapResolution(0.3);
    // c_pAMCLer_->setScoreThresh(0.9);
    // c_pAMCLer_->setYawResolution(0.1);
    // c_pGlobalAmcl_->loadMap("/home/<USER>/catkin_ws/src/wanji_amcl/map/cd");
    // c_pGlobalAmcl_->initPara();

    c_bLocationRun_ = false;
    c_bLocationRunOver_ = true;
    c_bShutDown_ = false;
    c_bShutDownOver_ = false;
    c_bManualPoseModel = false;
    c_stIncreaseOpt_.reset();
    c_vCallBackIndex_.clear();
    c_nManualPoseModelNum = 0;
    c_uiScanIDCur_ = -1;
    c_uiScanIDLast_ = -1;
    c_nScanTimeCurr_ = -1;
    c_nScanTimeLast_ = -1;
    c_bLoopHasDone_ = false;
    c_fJumpNum_ = -1;
    c_fAlignScore_ = -1;
    c_bOccupyVerify_ = false;
    c_PoseCheckStatus = PoseCheckStatus::Valid;
    c_PoseCheckNoClear = PoseCheckStatus::Valid;
    c_bMatchCurb_ = false;
    c_ucMoveStatus = -1;
    c_isGPSUseful = false;

    if (c_pIvoxGrid_)
    {
        // 设置大栅格内部小栅格尺寸大小
        c_pIvoxGrid_->setInnerResolu(c_stSysParam_->m_map.m_fInGridResolu);
        // 设置大栅格尺寸大小
        c_pIvoxGrid_->setOuterResolu(c_stSysParam_->m_map.m_fOutGridResolu);
        // 设置搜索栅格内部点数
        c_pIvoxGrid_->setSearchPointNum(c_stSysParam_->m_map.m_iGridSearchNum);
        // 设置iVox栅格容量大小
        c_pIvoxGrid_->setGridCapacity(c_stSysParam_->m_map.m_iGridSize);
        // 设置栅格周围栅格环绕类型
        c_pIvoxGrid_->setGridNearbyType(c_stSysParam_->m_map.m_iGridMatchType);
        // 栅格内部初始化
        c_pIvoxGrid_->gridInit();
        c_pMatcher_->setIVoxGrid(c_pIvoxGrid_);
    }
    if (c_pLaserVerify_)
    {
        // 设置高斯分布概率标准差(与栅格尺寸大小相关)
        c_pLaserVerify_->setGuassSize(c_stSysParam_->m_posCheck.m_fVerifyGauss);
        c_pLaserVerify_->setTargetGridMap(c_pIvoxGrid_);
        c_pLaserVerify_->setGroundAndRoofHigh(c_stSysParam_->m_map.m_fGroundHigh,
                                              c_stSysParam_->m_map.m_fRoofHigh);
    }
    c_pMapProcer_->setSampleSize(c_stSysParam_->m_map.m_fAllPcSize,
                                 c_stSysParam_->m_map.m_fSurfPcsize,
                                 c_stSysParam_->m_map.m_fLinePcSize);
    initPoseFuse_();
}
template <typename C, typename M> void Location<C, M>::setInput_(KeyFramePtr& p_pcIn)
{
    c_pMatcher_->setInputSourceCorner(p_pcIn->m_pFeature->first);
    // c_pMatcher_->setInputSourceSurface(p_pcIn->m_pFeature->second);
    c_pMatcher_->setInputSourceSurfaceSample(p_pcIn->m_pFeature->second,
                                             p_pcIn->m_pFeature->m_iSample2ndSize);
    // std::cerr << "p_pcIn->m_pFeature->second : " << p_pcIn->m_pFeature->second
    //           << "p_pcIn->m_pFeature->m_iSample2ndSize : " << p_pcIn->m_pFeature->m_iSample2ndSize
    //           << std::endl;
    c_pMatcher_->setInputSourceMark(p_pcIn->m_pFeature->third);
}

template <typename C, typename M> bool Location<C, M>::renewLocalMap_(s_POSE6D& p_poseCurr)
{
    if (updateLocalMap_(p_poseCurr))
    {
        if (c_pstLocalMap_->m_pFeature->surfSize() == 0)
        {
            LOGFAE(WERROR,
                   "{} 帧[{}] 定位地图异常 | 请确认位姿是否位于地图范围内",
                   WJLog::getWholeSysTime(),
                   c_uiScanIDCur_);
            // c_stSysParam_->m_fae.setErrorCode("J1");
            return false;
        }
        if (c_pstLocalMap_->m_pFeature->surfSize() < 100)
        {
            LOGFAE(WERROR,
                   "{} 帧[{}] 定位地图异常 | 定位地图过于稀疏, 请确认",
                   WJLog::getWholeSysTime(),
                   c_uiScanIDCur_);
        }
        if (c_stSysParam_->m_posCheck.m_bUseCurb && (c_pstLocalMap_->m_pFeature->curbSize() == 0))
        {
            LOGFAE(WWARN,
                   "{} 帧[{}] 定位地图异常 | 未检测到车道线",
                   WJLog::getWholeSysTime(),
                   c_uiScanIDCur_);
        }
    }
    // c_pAMCLer_->setCloudMap(c_pstLocalMap_->m_pFeature->second);
    return true;
}
template <typename C, typename M> bool Location<C, M>::updateLocalMap_(s_POSE6D& p_poseCurr)
{
    if (PoseStatus::SettingPose == p_poseCurr.m_bFlag)
    {
        // submapSetPose_(p_poseCurr);
        c_stSysParam_->m_pos.m_stSetPose.m_bFlag = PoseStatus::Default;
        // p_poseCurr.m_bFlag = PoseStatus::Default;
    }
    switch (c_stSysParam_->m_map.m_iOptimizeModel)
    {
        case OptimizeMapType::KDTREE_TYPE:
            if (c_pSubmap_->transformPointer(c_pstLocalMap_, c_vCallBackIndex_) || (!c_bHasInit))
            {
                setTarget_(c_pstLocalMap_);
                return true;
            }
            break;
        case OptimizeMapType::IVOX_TYPE: {
            MapFramePtr l_pstAddLocalMap_(new MapFrame());
            if (c_pSubmap_->transformAddPointer(
                    c_pstLocalMap_, l_pstAddLocalMap_, c_vCallBackIndex_)
                || (!c_bHasInit))
            {
                if (c_sConfig_.m_bIsEnLoop)
                {
                    setTarget_(c_pstLocalMap_);
                }
                else
                {
                    // 增量式地图角点/curb暂时使用全部子图角点/curb
                    *(l_pstAddLocalMap_->m_pFeature->first) = *(c_pstLocalMap_->m_pFeature->first);
                    *(l_pstAddLocalMap_->m_pFeature->third) = *(c_pstLocalMap_->m_pFeature->third);
                    *(l_pstAddLocalMap_->m_pFeature->fourth) =
                        *(c_pstLocalMap_->m_pFeature->fourth);
                    setTarget_(l_pstAddLocalMap_);
                }
                return true;
            }

            break;
        }
        default: break;
    }
    return false;
}

template <typename C, typename M> bool Location<C, M>::hasEnoughMark_(void)
{
    // if (!c_stSysParam_->m_bIsUseMark)
    //     return false;
    // if (c_pKeyFrameOptToBe_->m_pFeature->third->size() > 3)
    //     return true;
    return false;
}
template <typename C, typename M> void Location<C, M>::setTarget_(MapFramePtr& p_pcIn)
{
    // c_pMatcher_->setInputTargetCorner(p_pcIn->m_pFeature->first);
    PointCloudMapPtr l_pCornerPc(new PointCloudMap);
    pcl::copyPointCloud(*p_pcIn->m_pFeature->first, *l_pCornerPc);
    c_pMatcher_->setInputTargetCorner(l_pCornerPc);

    TicToc tt;
    switch (c_stSysParam_->m_map.m_iOptimizeModel)
    {
        case OptimizeMapType::KDTREE_TYPE:
            c_pMatcher_->setInputTargetSurface(p_pcIn->m_pFeature->second);
            break;
        case OptimizeMapType::IVOX_TYPE:
            tt.tic();
            if (c_sConfig_.m_bIsEnLoop)
            {
                c_pIvoxGrid_->buildGridMap(
                    p_pcIn->m_pFeature->second->points, p_pcIn->m_pFeature->first->points, 3);
            }
            else
            {
                c_pIvoxGrid_->addGridMap(
                    p_pcIn->m_pFeature->second->points, p_pcIn->m_pFeature->first->points, 3);
            }
            COST_TIME_IVOX(tt.toc());
            LOGM(WDEBUG,
                 "{} [IVOX] 帧[{}] iVox Cost Time {:.3f}",
                 WJLog::getWholeSysTime(),
                 c_uiScanIDCur_,
                 tt.toc());
            break;
        default: break;
    }
    c_pCurbMatcher_->setInputTarget(p_pcIn->m_pFeature->fourth);

    // 采用靶标gtsam优化需要实时添加到地图中。
    //  c_pMatcher_->setInputTargetMark(p_pcIn->m_pFeature->third);
    PointCloudMapPtr l_pMarkPc(new PointCloudMap);
    pcl::copyPointCloud(*p_pcIn->m_pFeature->third, *l_pMarkPc);
    c_pMatcher_->setInputTargetMark(l_pMarkPc);
}

template <typename C, typename M> void Location<C, M>::addMapFrames_(KeyFramePtr& p_pKeyFrame)
{
    // 拷贝Pose
    POSE pose;
    pose.x = p_pKeyFrame->m_Pose.m_trans.x();
    pose.y = p_pKeyFrame->m_Pose.m_trans.y();
    pose.z = p_pKeyFrame->m_Pose.m_trans.z();
    pose.intensity = p_pKeyFrame->m_Pose.m_fPercent;  // 将位姿校验的概率值保存值至路径点中

    // 拷贝Map帧
    MapFramePtr l_pMapFrame(new MapFrame());
    pcl::copyPointCloud(*p_pKeyFrame->m_pFeature->first, *l_pMapFrame->m_pFeature->first);
    pcl::copyPointCloud(*p_pKeyFrame->m_pFeature->second, *l_pMapFrame->m_pFeature->second);
    pcl::copyPointCloud(*p_pKeyFrame->m_pFeature->third, *l_pMapFrame->m_pFeature->third);
    pcl::copyPointCloud(*p_pKeyFrame->m_pFeature->fourth, *l_pMapFrame->m_pFeature->fourth);
    pcl::copyPointCloud(*p_pKeyFrame->m_pFeature->allPC, *l_pMapFrame->m_pFeature->allPC);
    *(l_pMapFrame->m_pFeature->sc) = *(p_pKeyFrame->m_pFeature->sc);
    l_pMapFrame->m_pFeature->m_bHasContext = p_pKeyFrame->m_pFeature->m_bHasContext;
    l_pMapFrame->m_pFeature->m_iSample2ndSize = p_pKeyFrame->m_pFeature->m_iSample2ndSize;
    l_pMapFrame->m_pFeature->m_tsWallTime = p_pKeyFrame->m_pFeature->m_tsWallTime;
    l_pMapFrame->m_pFeature->m_uiScanFrame = c_pKeyFrameMap_->getKfSize();
    l_pMapFrame->m_Pose = p_pKeyFrame->m_Pose;
    l_pMapFrame->m_pFeature->m_tsSyncTime = p_pKeyFrame->m_pFeature->m_tsSyncTime;

    if (WorkMode::UpdateMapMode != c_sConfig_.m_workMode)
    {
        // 添加Pose 添加Map帧
        c_pKeyFrameMap_->push(l_pMapFrame, pose);
        std::cout << "add Map " << c_pKeyFrameMap_->getKfSize() << std::endl;
        LOGM(WDEBUG, "add map id {}", c_pKeyFrameMap_->getKfSize() - 1);
    }
    else
    {
        // 添加Pose //添加Map帧
        c_pKfMapPairNewAdd_->push(l_pMapFrame, pose);
        std::cout << "add Update Map " << c_pKfMapPairNewAdd_->getKfSize() << std::endl;
        LOGM(WDEBUG, "add Update Map {}", c_pKfMapPairNewAdd_->getKfSize() - 1);
    }

    c_poseTwistContainer_.setLastAddMapPoseTwist(c_poseTwistContainer_.getFusePoseTwist());
}

template <typename C, typename M> void Location<C, M>::update_(KeyFramePtr& p_pKeyFrame)
{
    renewPose_();
    if (!c_bHasInit)
        initFrameLabel(p_pKeyFrame);

    s_PoseWithTwist l_stCurrFusePoseTwist = c_poseTwistContainer_.getFusePoseTwist();
#    ifdef USESUBMAP
    p_pKeyFrame->m_Pose = l_stCurrFusePoseTwist.m_Pose;  // 将优化&校验后位姿更新给关键帧
    // 当出现无效位姿时不允许使用无效位姿刷新状态
    if (p_pKeyFrame->m_Pose.m_bFlag != PoseStatus::StopPose)
    {
        c_pSubmap_->setPose(p_pKeyFrame->m_Pose.m_trans, p_pKeyFrame->m_Pose.m_quat);
        C l_pcscanRangeMin, l_pcscanRangeMax;
        pcl::getMinMax3D(*(p_pKeyFrame->m_pFeature->second), l_pcscanRangeMin, l_pcscanRangeMax);
        c_pSubmap_->setScanRange(
            l_pcscanRangeMin.x, l_pcscanRangeMin.y, l_pcscanRangeMax.x, l_pcscanRangeMax.y);
    }
#    endif
    bool l_bCorrectFlag_ = false;
    if (isUpdateMap_())
    {
        // 动态过滤
        //  reMovingFrame_(p_pKeyFrame);
        //  若开启双层墙，修改hsv值
        switch (c_stSysParam_->m_map.m_iOptimizeModel)
        {
            case OptimizeMapType::IVOX_TYPE:
                backoutFrameLabel(p_pKeyFrame);
                // 将点云中标签为0、3的点删除
                removeErrorLabelPoint(p_pKeyFrame);
                break;
            default: break;
        }
        addMapFrames_(p_pKeyFrame);
        l_bCorrectFlag_ = renewFactorGraph_();
        l_stCurrFusePoseTwist = c_poseTwistContainer_.getFusePoseTwist();
        FilterFactory::getInstance()->setNearByPath(c_pKeyFrameMap_->getKfPose());
    }

    // 发生回环修正后,强制更新子图
    c_pSubmap_->updateSubmap(l_bCorrectFlag_);
    // 不论是否位姿有效，均必须传递至里程计更新标志位
    // 无效位姿下 已将Pose还原至上一帧预估Pose
    c_highPrecsPoseCb_(l_stCurrFusePoseTwist);

    savePoseThread_(l_stCurrFusePoseTwist.m_Pose,
                    c_stSysParam_->m_pos.m_sPoseFilePath + "pose.csv");
    savePoseInFile2_(l_stCurrFusePoseTwist.m_Pose, p_pKeyFrame->m_pFeature->m_tsSyncTime);
}

template <typename C, typename M>
void Location<C, M>::savePoseThread_(s_POSE6D& p_sCurrPose, std::string p_sFilePath)
{
    static u_int32_t l_uiSaveNum = 0;
    static u_int32_t l_uiScanIDSave_ = 0;
    if (c_uiScanIDCur_ - l_uiScanIDSave_ < 10)
        return;
    l_uiSaveNum++;

    if (l_uiSaveNum % 2 == 0)
        l_uiScanIDSave_ = c_uiScanIDCur_;
    std::thread l_save(&Location<C, M>::savePoseInFile_, this, p_sCurrPose, p_sFilePath);
    l_save.detach();
}

template <typename C, typename M>
void Location<C, M>::savePoseInFile_(s_POSE6D p_sCurrPose, std::string p_sFilePath)
{
    // TicToc l_saveT;
    int l_nWriteOverSign = 1;
    std::fstream l_filePoseWR;
    l_filePoseWR.open(p_sFilePath.c_str(), std::ios::out | std::ios::trunc);
    if (l_filePoseWR.is_open())
    {
        l_filePoseWR << p_sCurrPose.m_trans[0] << "," << p_sCurrPose.m_trans[1] << ","
                     << p_sCurrPose.m_trans[2] << "," << p_sCurrPose.roll() << ","
                     << p_sCurrPose.pitch() << "," << p_sCurrPose.yaw() << "," << l_nWriteOverSign
                     << std::endl;
        l_filePoseWR.close();
    }
    else
        LOGM(WERROR, "{} saveP open fail: {}", WJLog::getWholeSysTime(), p_sFilePath);

    // if (l_saveT.toc() > 1.0)
    //     LOGM(WERROR,
    //          "{} saveP timeout: {:.3} dir: {}",
    //          WJLog::getWholeSysTime(),
    //          l_saveT.toc(),
    //          p_sFilePath);
}

template <typename C, typename M>
void Location<C, M>::savePoseInFile2_(s_POSE6D& p_sCurrPose, timeMs p_iTimetamp)
{
    // static bool l_bSavePath = c_stSysParam_->m_pos.m_bSaveWholePath;
    static std::string p_sFilePath = c_stSysParam_->m_pos.m_sPoseFilePath + "poselist.csv";
    static std::fstream l_filePoseWR;
    static bool l_bInit = true;
    if (c_stSysParam_->m_pos.m_bSaveWholePath)
    {
        if (l_bInit || !l_filePoseWR.is_open())
        {
            l_bInit = false;
            l_filePoseWR.open(p_sFilePath.c_str(), std::ios::out | std::ios::trunc);
            LOGM(WINFO, "{} Try Open {} for save AGV path", WJLog::getWholeSysTime(), p_sFilePath);
        }
        l_filePoseWR << p_iTimetamp << "," << p_sCurrPose.m_trans[0] << ","
                     << p_sCurrPose.m_trans[1] << "," << p_sCurrPose.m_trans[2] << ","
                     << p_sCurrPose.roll() << "," << p_sCurrPose.pitch() << "," << p_sCurrPose.yaw()
                     << std::endl;
    }
}

template <typename C, typename M> void Location<C, M>::renewPose_()
{
    // 当前SLAM优化位姿
    s_PoseWithTwist l_stCurrSlamPoseTwist;

    s_POSE6D l_stCurrSlamPose = c_pKeyFrameOptToBe_->m_Pose;
    PoseStatus l_poseStatus = PoseStatus::ContinuePose;
    if (c_bHasInit)
    {
        l_poseStatus = l_stCurrSlamPose.m_bFlag;
    }

    // 更新增量里程计
    l_stCurrSlamPose = c_stIncreaseOpt_ * l_stCurrSlamPose;

    l_stCurrSlamPose.m_bFlag = l_poseStatus;
    l_stCurrSlamPose.m_fPercent = c_pKeyFrameOptToBe_->m_Pose.m_fPercent;

    // 将优化后位姿更新给关键帧，此时关键帧点云和位姿同步
    c_pKeyFrameOptToBe_->m_Pose = l_stCurrSlamPose;

    l_stCurrSlamPoseTwist.m_Pose = l_stCurrSlamPose;
    l_stCurrSlamPoseTwist.m_iScanId = c_uiScanIDCur_;
    l_stCurrSlamPoseTwist.m_tsSyncTime = c_pKeyFrameOptToBe_->m_pFeature->m_tsSyncTime;
    l_stCurrSlamPoseTwist.m_tsWallTime = c_pKeyFrameOptToBe_->m_pFeature->m_tsWallTime;

    c_fJumpNum_ = (c_nScanTimeCurr_ - c_nScanTimeLast_) / SCAN_TIME_MS;
    c_uiScanIDLast_ = c_uiScanIDCur_;
    c_nScanTimeLast_ = c_nScanTimeCurr_;

    if (isResetTwist_(c_poseTwistContainer_.getSlamOptPose(), l_stCurrSlamPose))
    {
        // 没有初始化 速度清空
        l_stCurrSlamPoseTwist.m_bFlag = l_stCurrSlamPose.m_bFlag;  // 初始定位
        l_stCurrSlamPoseTwist.m_Twist.reset();                     // 清空速度
    }
    else
    {
        s_POSE6D l_stLastSlamPose = c_poseTwistContainer_.getSlamOptPose();
        l_stCurrSlamPoseTwist.m_bFlag = PoseStatus::ContinuePose;
        l_stCurrSlamPoseTwist.m_Twist =
            l_stLastSlamPose.inverse() * l_stCurrSlamPose;  // 计算局部速度
        // 增量是速度（100ms增量）的N倍
        l_stCurrSlamPoseTwist.m_Twist /= c_fJumpNum_;
        // // 平均速度
        // l_stCurrSlamPoseTwist.m_Twist =
        //     (c_stSysParam_->m_pos.m_stLastPose.m_Twist * l_stCurrSlamPoseTwist.m_Twist) * 0.5;
        // if (c_sConfig_.m_bOnlyLocatMode && c_fJumpNum_ > 1.1)
        //     LOGFAE(WWARN,
        //            "{} 帧[{}] 定位超时 | {:.3} > {} ms,请检查!",
        //            WJLog::getWholeSysTime(),
        //            c_uiScanIDCur_,
        //            c_fJumpNum_ * SCAN_TIME_MS,
        //            SCAN_TIME_MS);
        // l_stCurrSlamPoseTwist.m_Twist.printf("Twist per");
    }
    if(c_fJumpNum_ > 1e-6)
        LOGM(WDEBUG,
            "{} [LOC] 帧[{}] pose: X {:.3f} Y {:.3f} A {:.3f}, speed: {:.3f} m/s {:.3f} deg/s",
            WJLog::getWholeSysTime(),
            c_uiScanIDCur_,
            l_stCurrSlamPoseTwist.m_Pose.x(),
            l_stCurrSlamPoseTwist.m_Pose.y(),
            l_stCurrSlamPoseTwist.m_Pose.yaw(),
            l_stCurrSlamPoseTwist.m_Twist.normXY() / (c_fJumpNum_ * SCAN_TIME_MS / 1000.0),
            l_stCurrSlamPoseTwist.m_Twist.yaw() / (c_fJumpNum_ * SCAN_TIME_MS / 1000.0));

    // 更新当前SLAM优化位姿
    c_poseTwistContainer_.setSlamOptPoseTwist(l_stCurrSlamPoseTwist);
    updatePoseStatus_(l_stCurrSlamPoseTwist);
}

template <typename C, typename M>
bool Location<C, M>::optimiz_(KeyFramePtr& p_pKeyFrame, s_POSE6D& p_stPoseOpt)
{
    bool res = false;
    p_stPoseOpt.reset();
#    ifdef ZEROOPT
    c_pMatcher_->setMovePose(p_pKeyFrame->m_Pose.m_trans);
#    else
    c_pMatcher_->setMovePose();
#    endif
    c_pMatcher_->setTransform(Eigen::Quaterniond::Identity(), Eigen::Vector3d::Zero());
    c_pMatcher_->setCurrentPose(p_pKeyFrame->m_Pose.m_trans);

    // std::cout << "hsq: location.hpp optimiz_(KeyFramePtr& p_pKeyFrame, s_POSE6D& p_stPoseOpt) "
    //              "调用laserRegistration.hpp align()"
    //           << std::endl;
    res = c_pMatcher_->align(
        p_stPoseOpt.m_quat, p_stPoseOpt.m_trans, 1, c_uiScanIDCur_, c_bManualPoseModel);
    c_fAlignScore_ = c_pMatcher_->getResultScore();
     std::cout << "hsq: location.hpp optimiz_(), res = " << res <<", score = " << c_fAlignScore_ << std::endl;
    if ((c_stSysParam_->m_iWorkMode == WorkMode::LocatMode && c_stSysParam_->m_loct.optimiz_o3D)
        || c_stSysParam_->m_slam.optimiz_o3D)
    {
        p_stPoseOpt.m_trans.z() = 0.0;
        p_stPoseOpt.setQuat(p_stPoseOpt.m_quat);
        p_stPoseOpt.setRPY(0, 0, p_stPoseOpt.yaw());
    }
#    ifdef PRINT
    p_stPoseOpt.printf("p_stPoseOpt");
    c_stIncreaseOpt_.printf("c_stIncreaseOpt_");
#    endif
    if (res)
    {
        c_stIncreaseOpt_ = p_stPoseOpt * c_stIncreaseOpt_;  // 根据单次优化增量更新帧间优化增量
        transPointCloud_(p_stPoseOpt, p_pKeyFrame);
    }

    return res;
}

template <typename C, typename M> bool Location<C, M>::checkNewKeyFrames_()
{
    if (!c_bLocationRun_ || c_bShutDown_)
    {
        while (!c_qKeyFrameOptToBe_.empty())
            c_qKeyFrameOptToBe_.pop();
        return false;
    }
    return !c_qKeyFrameOptToBe_.empty();
}

template <typename C, typename M> void Location<C, M>::clearKeyFramesAll_()
{
    while (!c_qKeyFrameOptToBe_.empty())
        c_qKeyFrameOptToBe_.pop();
}

template <typename C, typename M> void Location<C, M>::clearKeyFramesBuf_(void)
{
    uint l_uiCnt = c_qKeyFrameOptToBe_.size() - 1;
    while (l_uiCnt--)
    {
        KeyFramePtr l_pDelete = c_qKeyFrameOptToBe_.front();
        LOGM(WWARN,
             "{} pop frame {} stamp {}",
             WJLog::getWholeSysTime(),
             l_pDelete->m_pFeature->m_uiScanFrame,
             l_pDelete->m_pFeature->m_tsSyncTime);
        c_qKeyFrameOptToBe_.pop();
    }
    return;
}

template <typename C, typename M> bool Location<C, M>::matchSlam_(KeyFramePtr& p_pKeyFrame)
{
    // 备份关键帧Pose
    s_POSE6D l_rawPose = p_pKeyFrame->m_Pose;
    bool res = false;
    int l_iOptTimes;
    s_POSE6D l_stPoseOpt;
    // 增量判定组
    s_POSE6D l_stFakePoseLast = p_pKeyFrame->m_Pose;
    s_POSE6D l_stFakePose = p_pKeyFrame->m_Pose;
    // 增量数值
    Eigen::VectorXd l_delta;
    // XY增量
    double l_normXY;

    c_stIncreaseOpt_.reset();  // 将优化增量清0
    if (c_sConfig_.m_bOnlyLocatMode || (!c_bIsKeyFrame_))
        l_iOptTimes = 1;
    else
        l_iOptTimes = 5;

    if (c_bManualPoseModel)
    {
        c_nManualPoseModelNum++;
        if (c_nManualPoseModelNum > c_stSysParam_->m_loct.m_iManualFrameCnt)
        {
            c_nManualPoseModelNum = 0;
            c_bManualPoseModel = false;
        }
    }
    LOGO(WINFO,
             "{} c_sConfig_.m_bOnlyLocatMode = {}, c_bIsKeyFrame_ = {}, l_iOptTimes = {}, c_bManualPoseModel = {}",
             WJLog::getWholeSysTime(),
             c_sConfig_.m_bOnlyLocatMode,
             c_bIsKeyFrame_,
             l_iOptTimes,
             c_bManualPoseModel);
    for (int i = 0; i < l_iOptTimes; i++)
    {
        if (c_sConfig_.m_bOnlyLocatMode)
        {
            if (c_bManualPoseModel){
                setMarchParam_(c_stSysParam_->m_loct.m_manual);
                std::cout << "hsq: matchSlam_: setMarchParam_  m_loct.m_manual "  << std::endl;
            }
            else{
                setMarchParam_(c_stSysParam_->m_loct.m_match);
                std::cout << "hsq: matchSlam_: setMarchParam_  m_loct.m_match "  << std::endl;
            }
            // 纯靶标模式，且第一次设置较大匹配半径
            if (c_stSysParam_->m_iSlamMode == 1 && 0 == i)
            {
                c_pMatcher_->setMarkSearchR(c_stSysParam_->m_loct.m_manual.m_fLine2DMarkRadius);
            }
        }
        else
        {
            setMarchParam_(c_stSysParam_->m_slam.m_match[i]);
        }
        setInput_(p_pKeyFrame);                    // 设置输入点云
        res = optimiz_(p_pKeyFrame, l_stPoseOpt);  // 优化
        std::cout << "hsq: matchSlam_: optimiz_  res = " << res << std::endl;
        // 匹配成功则验证概率值
        if (res)
        {
            res = checkOccupyVerify_(p_pKeyFrame, c_bOccupyVerify_);
            std::cout << "hsq: matchSlam_: checkOccupyVerify_  res = " << res << std::endl;
        }
        // 此处如果之前成功，后面有失败，点云已经根据平移至最后成功处且与位姿对应，因此无影响，
        // 直接返回false，进入amcl即可，后续可增加扩大搜索半径方式
        if (res)  // 匹配成功情况下，验证优化增量   //验证优化增量是否合理？
        {
            if (c_bIsKeyFrame_)
            {
                std::cout << "hsq: matchSlam_: c_bIsKeyFrame_ = true" << std::endl;
                // 虚拟位置
                l_stFakePose = c_stIncreaseOpt_ * l_rawPose;
                // 位姿增量
                l_delta = l_stFakePose.coeffs_6() - l_stFakePoseLast.coeffs_6();
                // XY增量
                l_normXY = sqrt(pow(l_delta[0], 2) + pow(l_delta[1], 2));
                // 记录上次结果
                l_stFakePoseLast = l_stFakePose;
                // 如果优化增量极小
                if (i > 0)
                {
                    if (l_normXY < 0.01 && std::fabs(l_delta[5]) < 0.1)
                        break;
                }
                // 如果建图且最后一次误差还是太大，放弃结果
                if (i == l_iOptTimes - 1)
                {
                    if (l_normXY > 0.05 || std::fabs(l_delta[5]) > 0.5)
                    {
                        c_bIsKeyFrame_ = false;
                        std::cout << "hsq: matchSlam_: c_bIsKeyFrame_ = false" << std::endl;
                        LOGM(WERROR,
                             "{} 帧[{}] keyframe opti too much | {:.3f} {:.3f}",
                             WJLog::getWholeSysTime(),
                             c_uiScanIDCur_,
                             l_normXY,
                             l_delta[5]);
                    }
                }
            }
        }
        else  // 匹配失败直接进入amcl
        {
            std::cout << "hsq: matchSlam_: 匹配失败直接进入amcl" << std::endl;
            // 还原角面curb特征 清空位姿增量  ==> SLAM自校验失败输出原始Pose和点云
            s_POSE6D l_stRevertPoseOpt = c_stIncreaseOpt_.inverse();
            transPointCloud_(l_stRevertPoseOpt, p_pKeyFrame);
            c_stIncreaseOpt_.reset();
            break;
        }
    }
    return res;
}

template <typename C, typename M>
bool Location<C, M>::continueLocationSlam_(KeyFramePtr& p_pKeyFrame)
{
    bool res = false;
    res = matchSlam_(p_pKeyFrame);
    return res;
}

template <typename C, typename M> bool Location<C, M>::initialLocationSlam_(void)
{
    return false;
}

template <typename C, typename M>
void Location<C, M>::updatePoseStatus_(s_PoseWithTwist& p_stCurrPoseTwistRaw_)
{
    // 融合位姿初值为Slam定位位姿
    s_PoseWithTwist l_stFusePoseTwist = p_stCurrPoseTwistRaw_;
    // 当前Odom预估位姿
    s_PoseWithTwist l_stOdomPredPoseTwist;
    getPoseCheckStatus_(l_stFusePoseTwist, l_stOdomPredPoseTwist, c_PoseCheckStatus);
    // 备份获取到的预测值，用于和curbPose区分
    s_PoseWithTwist l_stOdomPredRawPose_ = l_stOdomPredPoseTwist;
    if (c_PoseCheckStatus == PoseCheckStatus::Novalid)
    {
        // 只保存1次 避免丢失状态下疯狂保存
        if (c_PoseCheckNoClear == PoseCheckStatus::Valid && c_bakFeatureCb_
            && (c_stSysParam_->m_fae.m_bSaveErrorPoseLidar)
            && (c_stSysParam_->m_fae.m_bSaveErrorPoseLidar)
            && (!c_stSysParam_->m_posCheck.m_bTestCurb)
            && (!c_stSysParam_->m_posCheck.m_bOnlyWheelOdom))
            c_bakFeatureCb_(c_uiScanIDCur_, 5);

        if (l_stOdomPredPoseTwist.getFlag() != PoseStatus::VirtualPose
            && (!c_stSysParam_->m_posCheck.m_bTestCurb))
        {
            LOGFAE(WWARN,
                   "{} 帧[{}] 定位异常 | 切换至停止位姿 | 无预估Pose",
                   WJLog::getWholeSysTime(),
                   c_uiScanIDCur_);
            l_stFusePoseTwist.m_bFlag = PoseStatus::StopPose;
            l_stFusePoseTwist.m_Twist.reset();
        }
        else
        {
            // 有预估Pose 则将点云转移至预估Pose下
            s_POSE6D l_transOpt =
                l_stOdomPredPoseTwist.m_Pose * c_pKeyFrameOptToBe_->m_Pose.inverse();
            transPointCloud_(l_transOpt, c_pKeyFrameOptToBe_);
            // 马路伢子优化预估Pose
            if (c_stSysParam_->m_posCheck.m_bUseCurb || c_stSysParam_->m_posCheck.m_bTestCurb)
                c_bMatchCurb_ = matchCurb_(c_pKeyFrameOptToBe_, l_stOdomPredPoseTwist);

            // 启动testCurb后，仅为测试curb优化结果.最终仍使用slamPose
            if (c_stSysParam_->m_posCheck.m_bTestCurb)
                c_PoseCheckStatus = PoseCheckStatus::Valid;
            else
            {
                LOGFAE(WWARN,
                       "{} 帧[{}] 定位异常 | 切换至预估位姿",
                       WJLog::getWholeSysTime(),
                       c_uiScanIDCur_);
                // 使用融合后预估的Pose(已对齐至雷达时间) 及 融合速度（局部帧间）
                l_stFusePoseTwist = l_stOdomPredPoseTwist;
                l_stFusePoseTwist.m_iScanId = c_uiScanIDCur_;
            }
        }
        c_PoseCheckNoClear = PoseCheckStatus::Novalid;
    }

    // 更新当前Odom预估位姿
    c_poseTwistContainer_.setOdomPredPoseTwist(l_stOdomPredPoseTwist);
    if (c_PoseCheckStatus == PoseCheckStatus::Valid)
    {
        // 更新上次有效位姿
        c_poseTwistContainer_.setLastSlamPrecPoseTwist(l_stFusePoseTwist);
        if (!c_stSysParam_->m_posCheck.m_bTestCurb)
            c_bMatchCurb_ = false;
        poseFuse_(l_stOdomPredPoseTwist, l_stFusePoseTwist);
        c_PoseCheckNoClear = PoseCheckStatus::Valid;
    }

    s_PoseWithTwist l_poseEnd = l_stFusePoseTwist;
    s_PoseWithTwist l_lastSlamPrecPoseTwist = c_poseTwistContainer_.getLastSlamPrecPoseTwist();
    if (checkVirtualMoveOverFar_(l_lastSlamPrecPoseTwist, l_stFusePoseTwist))
        l_poseEnd.setFlag(PoseStatus::StopPose);

    // 输出转移到真实地图和AGV位置
    updateAGVPose(l_poseEnd);

    // 更新可视化Pose
    viewPose(l_stFusePoseTwist.m_Pose,
             c_stSysParam_->m_pos.m_stLastPose.m_Pose,
             l_stOdomPredRawPose_.m_Pose,
             l_stOdomPredPoseTwist.m_Pose);
    savePoseCheckInfo_(
        l_stFusePoseTwist, p_stCurrPoseTwistRaw_, l_stOdomPredRawPose_, l_stOdomPredPoseTwist, "");

    // 更新融合位姿
    c_poseTwistContainer_.setFusePoseTwist(l_stFusePoseTwist);
}

template <typename C, typename M>
void Location<C, M>::poseFuse_(s_PoseWithTwist& p_stOdomPredPoseTwist,
                               s_PoseWithTwist& p_stSlamPoseTwist)
{
    // 设定位姿期间or未启动融合or未启动校验or更新建图模式
    if (c_bManualPoseModel || (!c_pPoseCheck_) || (!c_stSysParam_->m_posCheck.m_bOpenPoseCheck)
        || WorkMode::UpdateMapMode == c_stSysParam_->m_iWorkMode)
        return;
    // 由无效恢复有效，当帧重置位姿融合,且不融合
    if (c_PoseCheckNoClear == PoseCheckStatus::Novalid)
    {
        initPoseFuse_();
        return;
    }
    // 预估Pose无效
    if (p_stOdomPredPoseTwist.getFlag() != PoseStatus::VirtualPose
        && p_stOdomPredPoseTwist.getFlag() != PoseStatus::CurbPose)
        return;
    // SLAM无效
    if (p_stSlamPoseTwist.getFlag() != PoseStatus::ContinuePose)
        return;

    s_PoseWithTwist l_stFilterPose;
    // 当前时间【秒】
    double l_fCurrTime = p_stSlamPoseTwist.m_tsSyncTime / 1000.0;
    // 传递观测量
    c_pPoseCheck_->slamCallBack(p_stSlamPoseTwist.m_Pose, p_stSlamPoseTwist.m_Twist, l_fCurrTime);
    c_pPoseCheck_->odometryCallback(
        p_stOdomPredPoseTwist.m_Pose, p_stOdomPredPoseTwist.m_Twist, l_fCurrTime);
    // 启动校验 获取校验位姿
    c_pPoseCheck_->integrateMeasurements(l_fCurrTime);
    c_pPoseCheck_->getFilteredOdometryMessage(l_stFilterPose);

    // 有融合Pose 则将点云转移至融合Pose下
    s_POSE6D l_transOpt = l_stFilterPose.m_Pose * p_stSlamPoseTwist.m_Pose.inverse();
    transPointCloud_(l_transOpt, c_pKeyFrameOptToBe_);
    l_stFilterPose.m_Pose.m_bFlag = p_stSlamPoseTwist.m_Pose.m_bFlag;

    p_stSlamPoseTwist.m_Pose = l_stFilterPose.m_Pose;
    // p_stSlamPoseTwist.m_Twist = l_stFilterPose.m_Twist;
}

template <typename C, typename M> void Location<C, M>::initPoseFuse_()
{
    if (!c_stSysParam_->m_posCheck.m_bUsePoseFuse)
        return;
    if (c_pPoseCheck_)
        c_pPoseCheck_ = nullptr;
    // 设置校验距离为 时间*速度
    double l_fMaxLineVelo = 2.0;   // m/s
    double l_fMaxAnglVelo = 90.0;  // deg/s
    double l_fPoseCheck =
        (c_stSysParam_->m_map.m_iMapKFTimeStep / SCAN_TIME_MS + 1) * 0.1 * l_fMaxLineVelo;
    double l_fAnglCheck =
        (c_stSysParam_->m_map.m_iMapKFTimeStep / SCAN_TIME_MS + 1) * 0.1 * l_fMaxAnglVelo;
    c_pPoseCheck_.reset(new PoseCheck(l_fPoseCheck, l_fAnglCheck));
}

template <typename C, typename M>
bool Location<C, M>::quickAnalyPoseStatus(s_PoseWithTwist& p_stPoseCurrSlam,
                                          s_PoseWithTwist& p_stPosePrecOdom,
                                          PoseCheckStatus& p_iLastPostCheckStatus,
                                          bool p_bHasPredPose)
{
    // 设计的顺序是否合理？
    static int l_iOccVerifyBypassCnt = 0;
    // 以下几类情况，校验默认通过：未启动位姿校验 || 手动设定位姿模式
    if (c_bManualPoseModel)
    {
        LOGFAE(WTRACE,
               "{} 帧[{}] 定位状态提示 | 人为设定位姿",
               WJLog::getWholeSysTime(),
               c_uiScanIDCur_);
        p_iLastPostCheckStatus = PoseCheckStatus::Valid;
    }
    else if (!c_stSysParam_->m_posCheck.m_bOpenPoseCheck)
    {
        LOGFAE(
            WTRACE, "{} 帧[{}] 定位状态提示 | 关闭校验", WJLog::getWholeSysTime(), c_uiScanIDCur_);
        p_iLastPostCheckStatus = PoseCheckStatus::Valid;
    }
    // 测试项；slam情况下测试curb优化结果,此项启动后将进行curb优化 但仍输出slamPose有效
    else if (c_stSysParam_->m_posCheck.m_bOnlyWheelOdom || c_stSysParam_->m_posCheck.m_bTestCurb)
    {
        // wjPrint(WJCOL_GREEN, "debug onlyWheelOdom | testCurb", NULL);
        p_iLastPostCheckStatus = PoseCheckStatus::Novalid;
    }
    else if (c_PoseCheckNoClear == PoseCheckStatus::Novalid)
    {
        if (c_bOccupyVerify_)
        {
            l_iOccVerifyBypassCnt++;
            if (l_iOccVerifyBypassCnt >= c_stSysParam_->m_posCheck.m_iOccVerifyBypassThd
                && c_stSysParam_->m_posCheck.m_bUseOccupyVerify)
            {
                p_iLastPostCheckStatus = PoseCheckStatus::Valid;
                // 位姿恢复后使用轮式速度
                if (p_bHasPredPose)
                {
                    LOGFAE(WWARN,
                           "{} 帧[{}] 概率校验通过,恢复SLAM定位",
                           WJLog::getWholeSysTime(),
                           c_uiScanIDCur_);
                    p_stPoseCurrSlam.m_Twist =
                        p_stPosePrecOdom.m_Twist;  // 位姿恢复后使用谁的位姿合理？
                }
                else
                    LOGFAE(WWARN,
                           "{} 帧[{}] 概率校验通过,恢复SLAM定位 | 预估位姿不存在",
                           WJLog::getWholeSysTime(),
                           c_uiScanIDCur_);
                l_iOccVerifyBypassCnt = 0;
            }
            else
            {
                p_iLastPostCheckStatus = PoseCheckStatus::Novalid;
            }
        }
        else
        {
            LOGFAE(WTRACE,
                   "{} 帧[{}] 定位状态提示 | 等待手动设定",
                   WJLog::getWholeSysTime(),
                   c_uiScanIDCur_);
            p_iLastPostCheckStatus = PoseCheckStatus::Novalid;
        }
        // 位姿无效即虚拟Pose时仅依靠SLAM概率恢复
        return true;
    }
    else if (!(p_stPoseCurrSlam.m_bFlag == PoseStatus::ContinuePose
               || p_stPoseCurrSlam.m_bFlag == PoseStatus::InitialPose)
             || !(p_stPoseCurrSlam.m_Pose.m_bFlag == PoseStatus::InitialPose
                  || p_stPoseCurrSlam.m_Pose.m_bFlag == PoseStatus::ContinuePose))
    {
        // LOGFAE(WINFO,
        //        "{}------------定位状态异常 | 帧[{}]: flag {} {}",
        //        WJLog::getWholeSysTime(),
        //        c_uiScanIDCur_,
        //        p_stPoseCurrSlam.m_bFlag,
        //        p_stPoseCurrSlam.m_Pose.m_bFlag);
        p_iLastPostCheckStatus = PoseCheckStatus::Novalid;
        // 此处允许无预估Pose直接返回，即匹配失败无预估直接停车
        return true;
    }
    // 属于不校验区域
    else if (belongNoCheckArea(p_stPoseCurrSlam.m_Pose,
                               c_stSysParam_->m_posCheck.m_vNoCheckAreaList))
    {
        // if (p_iLastPostCheckStatus == PoseCheckStatus::Novalid)
        //     wjPrint(WJCOL_RED, "leave checkArea force valid", NULL);
        LOGFAE(WTRACE, "{} 定位状态提示 | 非校验区域", WJLog::getWholeSysTime());
        p_iLastPostCheckStatus = PoseCheckStatus::Valid;
    }
    else
    {
        if (!c_stSysParam_->m_posCheck.m_bUsePoseCheck)
        {
            // 不再启动poseCheck
            if (c_PoseCheckNoClear == PoseCheckStatus::Novalid)  // 上面会先触发
                p_iLastPostCheckStatus = PoseCheckStatus::Novalid;
            else
                p_iLastPostCheckStatus = PoseCheckStatus::Valid;
            return true;
        }
        else
            return false;
    }

    // 获取PredPose失败 则强制有效
    if ((!p_bHasPredPose) && (p_iLastPostCheckStatus == PoseCheckStatus::Novalid))
    {
        LOGM(WERROR,
             "{} 帧[{}] get pred pose fail | set valid",
             WJLog::getWholeSysTime(),
             c_uiScanIDCur_);
        p_iLastPostCheckStatus = PoseCheckStatus::Valid;
    }
    return true;
}

template <typename C, typename M>
void Location<C, M>::getPoseCheckStatus_(s_PoseWithTwist& p_stPoseCurrSlam,
                                         s_PoseWithTwist& p_stPosePrecOdom,
                                         PoseCheckStatus& p_iLastPostCheckStatus)
{
    p_stPosePrecOdom = p_stPoseCurrSlam;
    p_stPosePrecOdom.setFlag(PoseStatus::Default);
    // 获取预估Pose 进行位姿校验
    bool l_bGetPredPose = getPredPose_(p_stPosePrecOdom, p_stPoseCurrSlam.m_tsSyncTime);
    if (l_bGetPredPose)
    {
        // 预估Pose须更新为当前SLAM-Lidar内部时间戳
        p_stPosePrecOdom.m_tsSyncTime = p_stPoseCurrSlam.m_tsSyncTime;
        p_stPosePrecOdom.m_iScanId = p_stPoseCurrSlam.m_iScanId;
        // if (c_stSysParam_->m_bDebugModel)
        // {
        //     p_stPoseCurrSlam.m_Twist.printf("slam twist");
        //     p_stPosePrecOdom.m_Twist.printf("pred twist");
        // }
        c_ucMoveStatus =
            getMoveStatus(p_stPosePrecOdom, c_stSysParam_->m_posCheck.m_stPoseMoveStatus);
    }
    // 使用 各种标志 分析是否有效
    if (quickAnalyPoseStatus(
            p_stPoseCurrSlam, p_stPosePrecOdom, p_iLastPostCheckStatus, l_bGetPredPose))
        return;

    if (l_bGetPredPose)
    {
        if (!poseTwistCheck(p_stPoseCurrSlam,
                            p_stPosePrecOdom,
                            c_stSysParam_->m_posCheck.m_stPoseCheck,
                            c_stSysParam_->m_posCheck.m_stPoseMoveStatus,
                            "poseCheck"))
            p_iLastPostCheckStatus = PoseCheckStatus::Novalid;
        else
            p_iLastPostCheckStatus = PoseCheckStatus::Valid;
    }
    else
    {
        // 获取预估Pose失败 则默认有效
        p_iLastPostCheckStatus = PoseCheckStatus::Valid;
    }
}

template <typename C, typename M>
bool Location<C, M>::belongNoCheckArea(s_POSE6D p_sCurrPose,
                                       std::vector<std::vector<float>>& p_vAreaList)
{
    for (size_t i = 0; i < p_vAreaList.size(); i++)
    {
        if (belongArea(p_sCurrPose, p_vAreaList[i]))
            return true;
    }
    return false;
}

template <typename C, typename M>
bool Location<C, M>::belongArea(s_POSE6D p_sCurrPose, std::vector<float> p_vArea)
{
    if (p_vArea.size() != 4)
        return false;

    if (p_sCurrPose.x() > p_vArea[0] && p_sCurrPose.x() < p_vArea[1] && p_sCurrPose.y() > p_vArea[2]
        && p_sCurrPose.y() < p_vArea[3])
        return true;
    return false;
}

template <typename C, typename M>
bool Location<C, M>::checkVirtualMoveOverFar_(s_PoseWithTwist& p_stPose_Start,
                                              s_PoseWithTwist& p_stPose_Now)
{
    bool l_bRes = false;
    if (!c_stSysParam_->m_posCheck.m_bSafeModel)
        return l_bRes;

    // 限制虚拟Pose运动距离 仅考虑二维
    s_POSE6D l_increase = p_stPose_Start.m_Pose.inverse() * p_stPose_Now.m_Pose;
    double l_dDist = l_increase.normXY();
    if (l_dDist > c_stSysParam_->m_posCheck.m_fPredPosFarDist)
    {
        l_bRes = true;
        LOGFAE(WERROR,
               "定位异常 | 纯里程计位姿行进距离 {:.3f} 超过 {} m",
               l_dDist,
               c_stSysParam_->m_posCheck.m_fPredPosFarDist);
    }

    // 距离未满足 则限制长时间使用虚拟Pose运动
    if (!l_bRes
        && p_stPose_Now.m_tsSyncTime - p_stPose_Start.m_tsSyncTime
               > (c_stSysParam_->m_posCheck.m_fPredPosFarTime * 1000))
    {
        l_bRes = true;
        LOGFAE(WERROR,
               "定位异常 | 纯里程计时间 {} 超过 {} s | {} -> {}",
               p_stPose_Now.m_tsSyncTime - p_stPose_Start.m_tsSyncTime,
               c_stSysParam_->m_posCheck.m_fPredPosFarTime,
               p_stPose_Start.m_tsSyncTime,
               p_stPose_Now.m_tsSyncTime);
    }
    return l_bRes;
}

template <typename C, typename M>
void Location<C, M>::savePoseCheckInfo_(s_PoseWithTwist& p_sUsePose,
                                        s_PoseWithTwist& p_sSlamPose,
                                        s_PoseWithTwist& p_sPrePose,
                                        s_PoseWithTwist& p_sCurbPose,
                                        std::string p_sFilePath)
{
    if (!c_stSysParam_->m_fae.m_bPrintfCheckLog)
        return;
    TicToc l_t;
    LOGCHECK(WINFO,
             "{} check 帧[{}] {} {} {} use: {} {} {} {} {} {} lidar: {} "
             "{} {} "
             "{} {} {} pred: {} {} {} {} {} {} "
             "curb: {} {} {} {} {} {}",
             WJLog::getWholeSysTime(),
             c_uiScanIDCur_,
             c_PoseCheckStatus,
             p_sUsePose.m_tsWallTime,
             c_stSysParam_->m_time.getTimeNowMs(),
             (int)(p_sUsePose.m_Pose.x() * 1000),
             (int)(p_sUsePose.m_Pose.y() * 1000),
             (int)(p_sUsePose.m_Pose.yaw() * 1000),
             (int)(p_sUsePose.m_Twist.x() * 1000),
             (int)(p_sUsePose.m_Twist.y() * 1000),
             (int)(p_sUsePose.m_Twist.yaw() * 1000),
             (int)(p_sSlamPose.m_Pose.x() * 1000),
             (int)(p_sSlamPose.m_Pose.y() * 1000),
             (int)(p_sSlamPose.m_Pose.yaw() * 1000),
             (int)(p_sSlamPose.m_Twist.x() * 1000),
             (int)(p_sSlamPose.m_Twist.y() * 1000),
             (int)(p_sSlamPose.m_Twist.yaw() * 1000),
             (int)(p_sPrePose.m_Pose.x() * 1000),
             (int)(p_sPrePose.m_Pose.y() * 1000),
             (int)(p_sPrePose.m_Pose.yaw() * 1000),
             (int)(p_sPrePose.m_Twist.x() * 1000),
             (int)(p_sPrePose.m_Twist.y() * 1000),
             (int)(p_sPrePose.m_Twist.yaw() * 1000),
             (int)(p_sCurbPose.m_Pose.x() * 1000),
             (int)(p_sCurbPose.m_Pose.y() * 1000),
             (int)(p_sCurbPose.m_Pose.yaw() * 1000),
             (int)(p_sCurbPose.m_Twist.x() * 1000),
             (int)(p_sCurbPose.m_Twist.y() * 1000),
             (int)(p_sCurbPose.m_Twist.yaw() * 1000));
    if (l_t.toc() > 5.0)
        LOGFAE(WWARN,
               "{} 帧[{}] save checkInfo timeout {} us",
               WJLog::getWholeSysTime(),
               c_uiScanIDCur_,
               (int)(l_t.toc() * 1000));
}

template <typename C, typename M>
void Location<C, M>::getPredValidPoseBox_(s_PoseWithTwist& p_stPredPose,
                                          s_POSE6D& p_stPoseRes,
                                          s_TWIST& p_stBoxRes,
                                          pcl::PointCloud<pcl::PointXYZ>::Ptr& p_stBox)
{
    p_stBox->clear();

    s_POSE6D l_PoseDev_F = p_stBoxRes * p_stPoseRes.x();
    s_POSE6D l_PoseDev_B = p_stBoxRes.inverse() * p_stPoseRes.x();

    s_TWIST l_rotateL, l_rotateR;
    l_rotateL.setRPY(0, 0, 90.0);
    l_rotateR.setRPY(0, 0, -90.0);
    s_POSE6D l_PoseDev_L = (l_rotateL * p_stBoxRes) * p_stPoseRes.y();
    s_POSE6D l_PoseDev_R = (l_rotateR * p_stBoxRes) * p_stPoseRes.y();

    s_POSE6D l_Pose_F = p_stPredPose.m_Pose * l_PoseDev_F;
    s_POSE6D l_Pose_B = p_stPredPose.m_Pose * l_PoseDev_B;
    // 前进方向 左朝向
    s_POSE6D l_Pose_FL = l_Pose_F * l_PoseDev_L;
    p_stBox->points.push_back(pcl::PointXYZ(l_Pose_FL.x(), l_Pose_FL.y(), 0.0f));
    // 前进方向 右朝向
    s_POSE6D l_Pose_FR = l_Pose_F * l_PoseDev_R;
    p_stBox->points.push_back(pcl::PointXYZ(l_Pose_FR.x(), l_Pose_FR.y(), 0.0f));
    // 后退方向 右朝向
    s_POSE6D l_Pose_BR = l_Pose_B * l_PoseDev_R;
    p_stBox->points.push_back(pcl::PointXYZ(l_Pose_BR.x(), l_Pose_BR.y(), 0.0f));
    // 后退方向 左朝向
    s_POSE6D l_Pose_BL = l_Pose_B * l_PoseDev_L;
    p_stBox->points.push_back(pcl::PointXYZ(l_Pose_BL.x(), l_Pose_BL.y(), 0.0f));
}

template <typename C, typename M>
bool Location<C, M>::twistDirectCheck(s_TWIST& p_stNewTwist, s_TWIST& p_stLastTwist, float p_fThr)
{
    // 速度朝向
    float cosarg = std::fabs(p_stLastTwist.m_quat.dot(p_stNewTwist.m_quat));
    if (cosarg > p_fThr)  // 小于一个阈值？
        return true;
    LOGFAE(WWARN,
           "{} 帧[{}] twistDirect fail {}->{} res {}",
           WJLog::getWholeSysTime(),
           c_uiScanIDCur_,
           p_stLastTwist.yaw(),
           p_stNewTwist.yaw(),
           p_fThr);
    // // 线加速度
    // double maxspeed =
    //         (c_lastSpeed_.norm() >= c_curSpeed_.norm()) ? c_lastSpeed_.norm() :
    //         c_curSpeed_.norm();
    // double l_dLinerAcceleration = (c_lastSpeed_.inverse() * c_curSpeed_).norm() / maxspeed;
    // if (l_dLinerAcceleration <= 0.2)
    //     return true;
    return false;
}

template <typename C, typename M>
void Location<C, M>::getMinMax3D_(pcl::PointCloud<pcl::PointXYZ>::Ptr p_stBox,
                                  pcl::PointXYZ& l_pcMin,
                                  pcl::PointXYZ& l_pcMax)
{
    // 判断当前位姿是否在框内
    pcl::getMinMax3D(*p_stBox, l_pcMin, l_pcMax);
    // 不得低于最小运动框
    Eigen::Vector3d& box = c_stSysParam_->m_posCheck.m_stPoseCheck.m_Pose.m_trans;
    pcl::PointXYZ l_mid;
    l_mid.x = 0.5 * (l_pcMax.x + l_pcMin.x);
    l_mid.y = 0.5 * (l_pcMax.y + l_pcMin.y);
    l_mid.z = 0.5 * (l_pcMax.z + l_pcMin.z);
    if (l_pcMax.x - l_pcMin.x < box.x() * 2.0)
    {
        l_pcMin.x = l_mid.x - box.x();
        l_pcMax.x = l_mid.x + box.x();
    }
    if (l_pcMax.y - l_pcMin.y < box.y() * 2.0)
    {
        l_pcMin.y = l_mid.y - box.y();
        l_pcMax.y = l_mid.y + box.y();
    }
}

template <typename C, typename M>
bool Location<C, M>::poseCheck_(s_PoseWithTwist& p_stPoseNow,
                                s_PoseWithTwist& p_stPredPose,
                                s_POSE6D& p_stPoseRes,
                                s_TWIST& p_stBoxRes)
{
    bool l_bRes = true;
    // 预测位姿 结合速度 构建框
    getPredValidPoseBox_(p_stPredPose, p_stPoseRes, p_stBoxRes, c_pcPredPoseBox_);
    // 判断当前位姿是否在框内
    pcl::PointXYZ l_pcMin, l_pcMax;
    getMinMax3D_(c_pcPredPoseBox_, l_pcMin, l_pcMax);
    if ((p_stPoseNow.m_Pose.x() < l_pcMin.x) || (p_stPoseNow.m_Pose.x() > l_pcMax.x))
        l_bRes = false;
    else if ((p_stPoseNow.m_Pose.y() < l_pcMin.y) || (p_stPoseNow.m_Pose.y() > l_pcMax.y))
        l_bRes = false;
    if (!l_bRes)
    {
        LOGFAE(WERROR,
               "{} 帧[{}] pose beyond box x {:.3f} y {:.3f} | min {:.3f} {:.3f} max {:.3f} {:.3f}",
               WJLog::getWholeSysTime(),
               c_uiScanIDCur_,
               p_stPoseNow.m_Pose.x(),
               p_stPoseNow.m_Pose.y(),
               l_pcMin.x,
               l_pcMin.y,
               l_pcMax.x,
               l_pcMax.y);
        p_stPoseNow.m_Pose.printf("curPos");
        p_stPredPose.m_Pose.printf("predPos");
        p_stPoseNow.m_Twist.printf("curPos Twist");
        p_stPredPose.m_Twist.printf("predPos Twist");
    }
    return l_bRes;
}

template <typename C, typename M>
bool Location<C, M>::poseTwistCheck(s_PoseWithTwist& p_stPoseNow,
                                    s_PoseWithTwist& p_stPosePred,
                                    s_PoseWithTwist& p_stPoseDiff,
                                    s_PoseWithTwist& p_stPoseMoveStatus,
                                    std::string p_strPrintf)
{
    // LOGM(WTRACE, "{} {} poseTwistCheck", WJLog::getWholeSysTime(), p_strPrintf);
    if (poseCheck_(p_stPoseNow, p_stPosePred, p_stPoseDiff.m_Twist, p_stPosePred.m_Twist)
        && twistDirectCheck(p_stPoseNow.m_Twist, p_stPosePred.m_Twist, p_stPoseDiff.m_Twist.yaw()))
        return true;
    // LOGFAE(WWARN, "{} 帧[{}] {}校验异常", WJLog::getWholeSysTime(), c_uiScanIDCur_, p_strPrintf);
    return false;
}

template <typename C, typename M>
u_char Location<C, M>::getMoveStatus(s_PoseWithTwist& p_poseCurr, s_PoseWithTwist& p_stPoseRes)
{
    // LOGM(WTRACE, "{} getMoveStatus", WJLog::getWholeSysTime());
    u_char res = static_cast<u_char>(MoveStatus::Noise);
    // 当前状态必然虚拟定位
    if (p_poseCurr.m_bFlag == PoseStatus::VirtualPose)
    {
        if (p_poseCurr.m_Twist.normXY() > p_stPoseRes.m_Pose.x())
        {
            res |= static_cast<u_char>(MoveStatus::Length_Enough);
        }
        else if (p_poseCurr.m_Twist.normXY() < p_stPoseRes.m_Pose.y()
                 && (fabs(p_poseCurr.m_Twist.yaw()) < 1.0))
        {
            res |= static_cast<u_char>(MoveStatus::Static_Enough);
        }
        else
            res |= static_cast<u_char>(MoveStatus::Move_Enough);

        if (std::fabs(p_poseCurr.m_Twist.yaw()) > p_stPoseRes.m_Pose.yaw())
        {
            res |= static_cast<u_char>(MoveStatus::Ang_Enough);
        }
        // todo: 继续增加其他状态
    }
    return res;
}

template <typename C, typename M>
bool Location<C, M>::isLidarDisCon_(timeMs p_iNowMs, timeMs& p_iLastLocalOver)
{
    // 首帧之后才允许判断，执行关闭则直接结束
    if ((!c_bHasInit) || c_bShutDown_)
        return false;
    // 暂时离线不触发 可以考虑加入是否暂停标志
    if (!c_stSysParam_->m_bIsOnlineMode)
        return false;
    // 非定位模式不触发
    if (c_stSysParam_->m_iWorkMode != WorkMode::LocatMode
        && c_stSysParam_->m_iWorkMode != WorkMode::UpdateMapMode)
        return false;
    // 上帧定位结束后超时未接收新帧
    if (p_iNowMs - p_iLastLocalOver > c_stSysParam_->m_agv.m_fPoseValidTime)
        return true;
    return false;
}

template <typename C, typename M> void Location<C, M>::lidarDisconFunc(int& p_iDisConNum)
{
    bool l_stopOutLocal = true;
    c_PoseCheckNoClear = PoseCheckStatus::Novalid;
    c_poseTwistContainer_.setSlamOptPoseFlag(PoseStatus::VirtualPose);
    // 获取轮式/Slam预估位姿,失败则不更新odom，通过行驶时长/距离校验实现停车
    s_PoseWithTwist l_stOdomPredPoseTwist;
    if (getPredPose_(l_stOdomPredPoseTwist, c_nScanTimeCurr_ + c_tsLastestLocatSysTime_))
    {
        // 更新Odom&WheelOdom
        c_poseTwistContainer_.setOdomPredPoseTwist(l_stOdomPredPoseTwist);  // 更新当前Odom预估位姿
        c_poseTwistContainer_.setFusePoseTwist(l_stOdomPredPoseTwist);  // 更新当前融合位姿
        /** @todo 此处无需回调*/
        c_highPrecsPoseCb_(l_stOdomPredPoseTwist);
        // 使用轮式预估位姿 必须校验是否行驶距离/时间过长，否则须要求停车
        s_PoseWithTwist l_lastSlamPrecPoseTwist = c_poseTwistContainer_.getLastSlamPrecPoseTwist();
        if (!checkVirtualMoveOverFar_(l_lastSlamPrecPoseTwist, l_stOdomPredPoseTwist))
        {
            l_stopOutLocal = false;
            // 允许 AGV 使用预估Pose行进
            if (p_iDisConNum % 100 == 0)
            {
                LOGFAE(WERROR,
                       "{} 定位异常 | 切换至预估位姿,雷达帧[{}] 数据超时未收到 {}",
                       WJLog::getWholeSysTime(),
                       c_uiScanIDLast_ + 1,
                       (int)(c_stSysParam_->m_agv.m_fPoseValidTime + p_iDisConNum * 10));
            }
            updateAGVPose(l_stOdomPredPoseTwist);
        }
    }

    // 不允许使用预估Pose / 预估行驶过久 则强制停车
    if (l_stopOutLocal)
    {
        stopAGVPose();
        // 雷达中断避免高频打印
        if (p_iDisConNum % 100 == 0)
        {
            // LOGFAE放置此处仅为了不频繁打印，结合外部线程休眠 约1s打印1次
            LOGFAE(WERROR,
                   "{} 定位异常 | 切换至停止位姿,雷达帧[{}] 数据超时未收到 {}",
                   WJLog::getWholeSysTime(),
                   c_uiScanIDLast_ + 1,
                   (int)(c_stSysParam_->m_agv.m_fPoseValidTime + p_iDisConNum * 10));
            // c_stSysParam_->m_fae.setErrorCode("J5");
        }
    }

    s_POSE6D l_poseout0 = c_poseTwistContainer_.getSlamOptPose();
    l_poseout0.m_bFlag = PoseStatus::StopPose;
    s_POSE6D l_poseout1 = c_stSysParam_->m_pos.m_stLastPose.m_Pose;
    l_poseout1.m_bFlag = PoseStatus::StopPose;

    //  雷达中断后Web位姿继续刷新 只更改Flag
    viewPose(l_poseout0, l_poseout1, l_stOdomPredPoseTwist.m_Pose, l_stOdomPredPoseTwist.m_Pose);
    p_iDisConNum++;
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
}

template <typename C, typename M>
bool Location<C, M>::getPredPose_(s_PoseWithTwist& p_outPred, timeMs p_iNowTimeMs)
{
    bool l_bRes = false;
    if (c_stSysParam_->m_posCheck.m_bUseOdom)
    {
        l_bRes = c_getWheelOdomCb_(p_outPred, p_iNowTimeMs);
        if (!l_bRes)
            LOGM(
                WERROR, "{} 帧[{}] 获取轮式预估Pose失败", WJLog::getWholeSysTime(), c_uiScanIDCur_);
    }
    // 启动轮式里程计 但获取预估Pose失败 则视情况选择SLAM预估Pose
    if ((!l_bRes) && c_stSysParam_->m_posCheck.m_bUseLoct)
    {
        s_PoseWithTwist l_stLastSlamPrecPoseTwist =
            c_poseTwistContainer_.getLastSlamPrecPoseTwist();
        l_bRes = getSlamPredPose(p_outPred, p_iNowTimeMs, l_stLastSlamPrecPoseTwist);
        if (!l_bRes)
            LOGM(WERROR,
                 "{} 帧[{}] 获取Slam预估Pose失败 flag: {} nowT: {} lastT {}",
                 WJLog::getWholeSysTime(),
                 c_uiScanIDCur_,
                 l_stLastSlamPrecPoseTwist.getFlag(),
                 p_iNowTimeMs,
                 l_stLastSlamPrecPoseTwist.m_tsWallTime);
    }
    return l_bRes;
}

template <typename C, typename M>
bool Location<C, M>::getSlamPredPose(s_PoseWithTwist& p_outPred,
                                     timeMs& p_iNowTimeMs,
                                     s_PoseWithTwist& p_lastSlamPrec)
{
    if (!c_stSysParam_->m_posCheck.m_bUseLoct)
        return false;
    if (abs(p_lastSlamPrec.m_tsSyncTime - p_iNowTimeMs)
        > c_stSysParam_->m_posCheck.m_iPredValidTime)
        return false;
    if (PoseStatus::ContinuePose != p_lastSlamPrec.m_bFlag)
        return false;

    p_outPred = p_lastSlamPrec;
    p_outPred.recvTimeAlign(p_iNowTimeMs); /** @todo 预测处的位姿需要确定*/
    p_outPred.m_Pose.m_bFlag = PoseStatus::VirtualPose;
    p_outPred.m_bFlag = PoseStatus::VirtualPose;
    return true;
}

template <typename C, typename M> void Location<C, M>::updateAGVPose(s_PoseWithTwist& p_outLidarPos)
{
    s_PoseWithTwist l_outAgvPose = p_outLidarPos;
    s_PoseWithTwist l_stLastFusePoseTwist = c_poseTwistContainer_.getFusePoseTwist();
    // 速度平滑处理
    l_outAgvPose.m_Twist = (l_outAgvPose.m_Twist * l_stLastFusePoseTwist.m_Twist) / 2;
    // 对齐至计算完成时刻
    // l_outAgvPose.recvTimeAlign(getTimeDiffMs(getTimeNow(),
    // c_stSysParam_->m_time.m_sTimeSetSync)); 转移坐标系 Morg     Morg    M    A  -1
    //     T =      T *  T *  T
    //    A        M    L    L
    l_outAgvPose.m_Pose = c_stSysParam_->m_agv.m_stTrans * l_outAgvPose.m_Pose
                          * c_stSysParam_->m_agv.m_stLidarToAgv.inverse();

    //    A        A    L    A1  -1
    //     T =      T *  T *   T
    //    A1       L    L1   L1
    l_outAgvPose.m_Twist = c_stSysParam_->m_agv.m_stLidarToAgv * l_outAgvPose.m_Twist
                           * c_stSysParam_->m_agv.m_stLidarToAgv.inverse();

    // 更改flag 用于web显示
    l_outAgvPose.m_Pose.m_bFlag = l_outAgvPose.m_bFlag;
    l_outAgvPose.m_Pose.m_fPercent = p_outLidarPos.m_Pose.m_fPercent;
    c_stSysParam_->m_pos.m_stCurrPose = l_outAgvPose;
    c_stSysParam_->m_pos.m_stCurrPoseWJ = l_outAgvPose;
    c_stSysParam_->m_pos.m_stLastPose = l_outAgvPose;
    c_stSysParam_->m_pos.m_stLastPoseWJ = l_outAgvPose;
}

template <typename C, typename M> void Location<C, M>::stopAGVPose()
{
    // currPose SICK/WJ用前会重置为Default,此时配置为StopPose 避免频繁获取锁
    if (c_stSysParam_->m_pos.m_stCurrPose.getFlag() == PoseStatus::Default)
        c_stSysParam_->m_pos.m_stCurrPose.setFlag(PoseStatus::StopPose);
    if (c_stSysParam_->m_pos.m_stCurrPoseWJ.getFlag() == PoseStatus::Default)
        c_stSysParam_->m_pos.m_stCurrPoseWJ.setFlag(PoseStatus::StopPose);
    // lastPose SICK/WJ不会重置Flag，非Stop则配置
    if (c_stSysParam_->m_pos.m_stLastPose.getFlag() != PoseStatus::StopPose)
        c_stSysParam_->m_pos.m_stLastPose.setFlag(PoseStatus::StopPose);
    if (c_stSysParam_->m_pos.m_stLastPoseWJ.getFlag() != PoseStatus::StopPose)
        c_stSysParam_->m_pos.m_stLastPoseWJ.setFlag(PoseStatus::StopPose);
}

template <typename C, typename M>
void Location<C, M>::viewPose(s_POSE6D p_lidarPos,
                              s_POSE6D p_agvPose,
                              s_POSE6D p_predPose,
                              s_POSE6D p_curbPose)
{
    std::vector<s_POSE6D> l_poseout;
    if (c_vSendPose_)
    {
        // 注意：此处Pose顺序前2者分别为lidarPose和AGVPose，用于Web显示，不可变更
        l_poseout.push_back(p_lidarPos);
        l_poseout.push_back(p_agvPose);
        l_poseout.push_back(p_predPose);
        l_poseout.push_back(p_curbPose);
        c_vSendPose_(l_poseout);
    }
}

template <typename C, typename M>
void Location<C, M>::changeToMarkSt_(boost::shared_ptr<pcl::PointCloud<C>> p_mark)
{
    // int cnt = 0;
    // for (auto point : p_mark)
    // {
    //     // c_stCurrMark_.m_StructMarkInfoNoC
    //     cnt++;
    // }
    // c_stCurrMark_.m_u8In = cnt;
}

template <typename C, typename M>
bool Location<C, M>::location_(KeyFramePtr& p_pKeyFrame, LocationStatus& p_locate)
{
    if (!c_bHasInit)
    {
        p_pKeyFrame->m_Pose.m_fPercent = -1;  // 首帧点云不作匹配，所以首帧概率为-1
        return true;
    }
    bool isSucess = false;
    // 临时，在进入时强制使模式变为CONTINUS_SLAM
    // todo: 当模式不为CONTINUS_SLAM 进入初始定位|靶标定位
    if (p_locate != LocationStatus::CONTINUS_SLAM)
        p_locate = LocationStatus::CONTINUS_SLAM;
    if (!renewLocalMap_(p_pKeyFrame->m_Pose))
    {
        p_pKeyFrame->m_Pose.m_bFlag = PoseStatus::StopPose;
        return isSucess;
    }
    LocationStatus l_curLocStat = p_locate;
    TicToc tic;
    s_POSE6D l_stPose;
    s_POSE6D l_stLastPose;
    s_POSE6D l_stIncrese;
    double l_dTx, l_dTy, l_dTheta;
    double l_tmpYaw = 0;

    while (c_bLocationRun_)
    {
        l_curLocStat = p_locate;
        switch (p_locate)
        {
            case LocationStatus::CONTINUS_SLAM:  // SLAM连续定位
                isSucess = matchSlam_(p_pKeyFrame);
                if (!isSucess)
                {
                    if (c_sConfig_.m_bIsEnAmcl)
                        p_locate = LocationStatus::INITITAL_SLAM;  // 失败跳转:SLAM初始定位
                    else
                        p_locate = LocationStatus::STOP;  // 失败: 直接退出
                }

                break;
            case LocationStatus::INITITAL_SLAM:  // SLAM初始定位
                isSucess = initialLocationSlam_();
                if (isSucess)
                    p_locate = LocationStatus::CONTINUS_SLAM;
                else
                    p_locate = LocationStatus::STOP;

                break;
            case LocationStatus::CONTINUS_MARK:  // 靶标连续定位

                // changeToMarkSt_(p_pKeyFrame->m_pFeature->third);
                // isSucess = c_pMarkMatcher_->getMarkPose(
                //     &c_stCurrMark_, l_stPose.m_trans, l_stPose.m_quat, &c_stMarkMatchInfo_,
                //     false);

                // if (!isSucess)
                // {
                //     p_locate = LocationStatus::INITITAL_MARK;  //失败跳转:靶标初始定位
                // }

                break;
            case LocationStatus::INITITAL_MARK:  // 靶标初始定位
                // isSucess = c_pMarkMatcher_->getMarkPose(
                //     &c_stCurrMark_, l_stPose.m_trans, l_stPose.m_quat, &c_stMarkMatchInfo_,
                //     true);
                // if (isSucess)
                // {
                //     p_locate = LocationStatus::CONTINUS_MARK;  //成功跳转:靶标连续定位
                //     p_pKeyFrame->m_Pose.m_bFlag = PoseStatus::InitialPose;
                // }
                // else
                // {
                //     p_locate = LocationStatus::CONTINUS_SLAM;  //失败跳转:SLAM连续定位
                // }
                break;
            case LocationStatus::STOP:
                isSucess = false;
                p_pKeyFrame->m_Pose.m_bFlag = PoseStatus::StopPose;
                // LOGFAE(WERROR,
                //        "{} 帧[{}] 定位得分不足 | {:.2}<{:.2} 设定Pose? {} 使用概率? {}",
                //        WJLog::getWholeSysTime(),
                //        c_uiScanIDCur_,
                //        c_fAlignScore_,
                //        c_stSysParam_->m_posCheck.m_fKdMatchNumPercent,
                //        c_bManualPoseModel,
                //        c_stSysParam_->m_posCheck.m_bOpenOccupyVerify);
                break;

            default: break;
        }
        COST_TIME_MATCH(tic.toc());
        LOGM(WDEBUG,
             "{} [LOC] 帧[{}] Match Cost Time {:.3f}",
             WJLog::getWholeSysTime(),
             c_uiScanIDCur_,
             tic.toc());

        // 当没有被标记时,说明是连续定位
        if (PoseStatus::Default == p_pKeyFrame->m_Pose.m_bFlag
            || PoseStatus::SettingPose == p_pKeyFrame->m_Pose.m_bFlag)
        {
            p_pKeyFrame->m_Pose.m_bFlag = PoseStatus::ContinuePose;
            // c_bManualPoseModel = false;
        }
        if (isSucess
            && (l_curLocStat == LocationStatus::CONTINUS_MARK
                || l_curLocStat == LocationStatus::CONTINUS_SLAM))
            break;
        // slam初始定位都失败,直接返回
        if (LocationStatus::STOP == l_curLocStat)
            break;
    }
    return isSucess;
}
template <typename C, typename M> void Location<C, M>::buildMarkMap_(void)
{
    // c_pMarkMatcher_->updateMarkMap(&c_stMarkSet_);
}
template <typename C, typename M> void Location<C, M>::saveMap_()
{
    TicToc tic;
    LOGFAE(WINFO, "地图 [{}] 保存中...", c_stSysParam_->m_map.m_sMapName);
    // 是否存在目录否则创建目录
    if (!makeDir(c_stSysParam_->m_sPkgPath + "/data/Map/" + c_stSysParam_->m_map.m_sMapName))
    {
        LOGFAE(WINFO, "地图 [{}] 保存失败 | 创建文件夹失败", c_stSysParam_->m_map.m_sMapName);
        return;
    }
    int sizeContAdd = c_pKeyFrameMap_->getKfSize();
    int sizeRenewAdd = c_pKfMapPairNewAdd_->getKfSize();
    MapFramePtr l_saveMap(new MapFrame());
    std::vector<typename pcl::PointCloud<M>::Ptr> l_feature;
    std::vector<typename pcl::PointCloud<M>::Ptr> l_pcVisible;
    typename pcl::PointCloud<M>::Ptr l_allPer(new pcl::PointCloud<M>());
    typename pcl::PointCloud<M>::Ptr l_allPer2d(new pcl::PointCloud<M>());
    typename pcl::PointCloud<pcl::PointXYZRGB>::Ptr l_2dMap(
        new pcl::PointCloud<pcl::PointXYZRGB>());
    std::string l_sMapPath =
        c_stSysParam_->m_sPkgPath + "/data/Map/" + c_stSysParam_->m_map.m_sMapName;
    oprateFile<M> wt(l_sMapPath);
    pcl::PointCloud<POSE>::Ptr l_savePose(new pcl::PointCloud<POSE>());
    if (c_stSysParam_->m_bDebugModel && c_sConfig_.m_bIsEnLoop)
        c_pGraph_->saveGraph(l_sMapPath + "/.graph.g2o");
    if (c_sConfig_.m_bIsEnLoop)  // 只要回环就保存
    {
        c_pGraph_->saveMapDate(l_sMapPath + "/mapdata.wj");
        c_pGraph_->saveGraphDate(l_sMapPath + "/Graphdata.wj");
        c_pGraph_->saveKFBinary(l_sMapPath + "/KF.gz");
    }
    *l_savePose = *c_pKeyFrameMap_->getKfPose();
    *l_savePose += *c_pKfMapPairNewAdd_->getKfPose();
    {
        // 访问期间,不可执行回环修正
        std::unique_lock<std::mutex> locker(c_mutexSavemap_);
        // 增加contmap新增地图
        for (int i = c_sConfig_.m_iOldMapNum; i < sizeContAdd; i++)
        {
            KeyFramePtr l_vMapFrame = c_pKeyFrameMap_->getKfMapByInd(i);
            s_POSE6D l_pose = l_vMapFrame->m_Pose;
            c_pMatcher_->transformCloudPoints(
                l_pose.m_quat, l_pose.m_trans, l_vMapFrame->m_pFeature->allPC, l_allPer);
            for (M& p : l_allPer->points)
            {
                if (fabs(p.z - l_pose.z()) < 0.2)
                {
                    p.z = 0;
                    l_allPer2d->push_back(p);
                }
            }
            *(l_saveMap->m_pFeature->allPC) += *l_allPer;
            *(l_saveMap->m_pFeature->first) += *(l_vMapFrame->m_pFeature->first);
            *(l_saveMap->m_pFeature->second) += *(l_vMapFrame->m_pFeature->second);
            *(l_saveMap->m_pFeature->third) += *(l_vMapFrame->m_pFeature->third);
            *(l_saveMap->m_pFeature->fourth) += *(l_vMapFrame->m_pFeature->fourth);
            if (c_stSysParam_->m_bDebugModel || c_stSysParam_->m_map.m_bSaveKFMap)
                wt.saveOneFrame(l_vMapFrame->m_pFeature, l_vMapFrame->m_Pose);
            sleepMs(1);
        }

        // 增加 UpdateMapMode 新增地图
        for (int i = 0; i < sizeRenewAdd; i++)
        {
            KeyFramePtr l_vMapFrameNewAdd = c_pKfMapPairNewAdd_->getKfMapByInd(i);
            s_POSE6D l_pose = l_vMapFrameNewAdd->m_Pose;
            c_pMatcher_->transformCloudPoints(
                l_pose.m_quat, l_pose.m_trans, l_vMapFrameNewAdd->m_pFeature->allPC, l_allPer);
            *(l_saveMap->m_pFeature->allPC) += *l_allPer;
            *(l_saveMap->m_pFeature->first) += *(l_vMapFrameNewAdd->m_pFeature->first);
            *(l_saveMap->m_pFeature->second) += *(l_vMapFrameNewAdd->m_pFeature->second);
            *(l_saveMap->m_pFeature->third) += *(l_vMapFrameNewAdd->m_pFeature->third);
            *(l_saveMap->m_pFeature->fourth) += *(l_vMapFrameNewAdd->m_pFeature->fourth);
            if (c_stSysParam_->m_bDebugModel || c_stSysParam_->m_map.m_bSaveKFMap)
                wt.saveOneFrame(l_vMapFrameNewAdd->m_pFeature, l_vMapFrameNewAdd->m_Pose);
            sleepMs(1);
        }
    }
    // todo: 从老地图中扣掉新更新的地图
    // 当前版本没有增加扣图功能,因此分开保存
    // 更新扣图功能后,可将if/else删除,保存功能合并.
    if (WorkMode::UpdateMapMode != c_sConfig_.m_workMode)
    {
        *(l_saveMap->m_pFeature) += *(c_pstWholeMap_->m_pFeature);
        l_feature.push_back(l_saveMap->m_pFeature->first);
        l_feature.push_back(l_saveMap->m_pFeature->second);
        l_feature.push_back(l_saveMap->m_pFeature->fourth);
        // 为了兼容v2版本地图third放在后面
        l_feature.push_back(l_saveMap->m_pFeature->third);
        if (c_stSysParam_->m_bDebugModel)
        {
            writeBinary_(c_stSysParam_->m_sPkgPath + "/data/Map/" + c_stSysParam_->m_map.m_sMapName
                             + "/.rall.pcd",
                         *(l_saveMap->m_pFeature->allPC));
            writeBinary_(c_stSysParam_->m_sPkgPath + "/data/Map/" + c_stSysParam_->m_map.m_sMapName
                             + "/.rfi.pcd",
                         *(l_saveMap->m_pFeature->first));
            writeBinary_(c_stSysParam_->m_sPkgPath + "/data/Map/" + c_stSysParam_->m_map.m_sMapName
                             + "/.rse.pcd",
                         *(l_saveMap->m_pFeature->second));
            writeBinary_(c_stSysParam_->m_sPkgPath + "/data/Map/" + c_stSysParam_->m_map.m_sMapName
                             + "/.rmark.pcd",
                         *(l_saveMap->m_pFeature->third));
            writeBinary_(c_stSysParam_->m_sPkgPath + "/data/Map/" + c_stSysParam_->m_map.m_sMapName
                             + "/.curb.pcd",
                         *(l_saveMap->m_pFeature->fourth));
            writeBinary_(c_stSysParam_->m_sPkgPath + "/data/Map/" + c_stSysParam_->m_map.m_sMapName
                             + "/.p.pcd",
                         *l_savePose);
        }
        if (c_pMapProcer_ && c_stSysParam_->m_map.m_bSaveMapProc)
        {
            // 下采样&滤波
            c_pMapProcer_->process(l_saveMap, *l_saveMap);
            pcl::VoxelGrid<M> l_vox;
            l_vox.setLeafSize(0.1, 0.1, 0.1);
            l_vox.setInputCloud(l_allPer2d);
            l_vox.filter(*l_allPer2d);

            pcl::StatisticalOutlierRemoval<M> sor;
            sor.setInputCloud(l_allPer2d);
            sor.setMeanK(15);
            sor.setStddevMulThresh(0.1);
            sor.filter(*l_allPer2d);
            // c_pMapProcer_->make2D(l_allPer2d);
        }
        else
        {
            pcl::copyPointCloud(*l_saveMap->m_pFeature->allPC, *l_allPer2d);
            for (size_t j = 0; j < l_allPer2d->size(); j++)
                l_allPer2d->points[j].z = 0;
        }
        l_pcVisible.push_back(l_saveMap->m_pFeature->allPC);
        l_pcVisible.push_back(l_allPer2d);

        c_pReadWriteMap_->writeBinary(c_stSysParam_->m_sPkgPath + "/data/Map/"
                                          + c_stSysParam_->m_map.m_sMapName + "/"
                                          + c_stSysParam_->m_map.m_sMapName + ".wj",
                                      l_savePose,
                                      l_feature,
                                      l_pcVisible);

        // 遍历l_savePose、l_feature、l_pcVisible转到车后轴，车后轴转到UTM
        cout << "hsq: save transed pointcloud" << endl;
        float roll = 0, pitch = 0, yaw = 87.4399991 * M_PI / 180.0;
        Eigen::AngleAxisf rollAngle(roll, Eigen::Vector3f::UnitX());
        Eigen::AngleAxisf pitchAngle(pitch, Eigen::Vector3f::UnitY());
        Eigen::AngleAxisf yawAngle(yaw, Eigen::Vector3f::UnitZ());
        Eigen::Quaternionf q = rollAngle * pitchAngle * yawAngle;
        Eigen::Vector3f translation = Eigen::Vector3f(0, 1.635, 1.64);  //
        Eigen::Matrix4f transform = Eigen::Matrix4f::Identity();
        transform.block<3, 3>(0, 0) = q.matrix();
        transform.block<3, 1>(0, 3) = translation;

        printf("hsq: saving l_savePoseTransedPointcloud......\n");
        pcl::PointCloud<POSE>::Ptr l_savePoseTransedPointcloud(new pcl::PointCloud<POSE>());
        pcl::transformPointCloud(*l_savePose, *l_savePoseTransedPointcloud, transform);
        l_savePoseTransedPointcloud->height = 1;
        l_savePoseTransedPointcloud->width = l_savePose->points.size();

        Eigen::Vector3d ENUPosition;
        std::vector<double> l_centerPointUTM(3, 0.0);
        for (auto& cloud : l_savePoseTransedPointcloud->points)
        {
            ENUPosition << cloud.x, cloud.y, cloud.z;
            l_centerPointUTM.clear();
            if (c_vEnu2UTM)
            {
                l_centerPointUTM = c_vEnu2UTM(ENUPosition);
            }
            else
            {
                cout << "c_vEnu2UTM = null" << endl;
            }

            cloud.x = l_centerPointUTM[0];
            cloud.y = l_centerPointUTM[1];
            cloud.z = l_centerPointUTM[2];
        }

        cout << "hsq: l_savePoseTransedPointcloud width: = " << l_savePoseTransedPointcloud->width
             << ", l_savePoseTransedPointcloud->points = "
             << l_savePoseTransedPointcloud->points.size()
             << ", l_savePose->points.size() = " << l_savePose->points.size() << endl;
        writeBinary_(c_stSysParam_->m_sPkgPath + "/data/Map/" + c_stSysParam_->m_map.m_sMapName
                         + "/p_transed_hsq1.pcd",
                     *l_savePoseTransedPointcloud);
        // pcl::io::savePCDFileBinaryCompressed(c_stSysParam_->m_sPkgPath + "/data/Map/" +
        // c_stSysParam_->m_map.m_sMapName
        //                      + "/p_transed_hsq2.pcd",
        //                  *l_savePoseTransedPointcloud);
        // pcl::io::savePCDFileASCII(c_stSysParam_->m_sPkgPath + "/data/Map/" +
        // c_stSysParam_->m_map.m_sMapName
        //                      + "/p_transed_hsq3.pcd",
        //                  *l_savePoseTransedPointcloud);

        printf("hsq: saving l_featureVector......\n");
        typename pcl::PointCloud<M>::Ptr l_featureTransedPointcloudUTM(new pcl::PointCloud<M>);
        std::vector<typename pcl::PointCloud<M>::Ptr> l_featureVector;
        for (const typename pcl::PointCloud<M>::Ptr pointcloudPtr : l_feature)
        {
            typename pcl::PointCloud<M>::Ptr l_featureTransedPointcloud(new pcl::PointCloud<M>);
            pcl::transformPointCloud(*pointcloudPtr, *l_featureTransedPointcloud, transform);

            for (auto& cloud : l_featureTransedPointcloud->points)
            {
                ENUPosition << cloud.x, cloud.y, cloud.z;

                l_centerPointUTM.clear();
                if (c_vEnu2UTM)
                {
                    l_centerPointUTM = c_vEnu2UTM(ENUPosition);
                }

                cloud.x = l_centerPointUTM[0];
                cloud.y = l_centerPointUTM[1];
                cloud.z = l_centerPointUTM[2];

                l_featureTransedPointcloudUTM->points.push_back(cloud);
            }
            l_featureTransedPointcloud->height = 1;
            l_featureTransedPointcloud->width = l_featureTransedPointcloud->points.size();
            // cout<<"hsq: l_featureTransedPointcloud size = " <<
            // l_featureTransedPointcloud->points.size() <<endl;
            l_featureVector.push_back(l_featureTransedPointcloud);
        }
        l_featureTransedPointcloudUTM->height = 1;
        l_featureTransedPointcloudUTM->width = l_featureTransedPointcloudUTM->points.size();
        cout << "hsq: UTM: l_featureTransedPointcloudUTM size = "
             << l_featureTransedPointcloudUTM->points.size()
             << ", cloud.x = " << l_featureTransedPointcloudUTM->points[0].x
             << ", cloud.y = " << l_featureTransedPointcloudUTM->points[0].y
             << ", cloud.z = " << l_featureTransedPointcloudUTM->points[10].z
             << ", cloud.x = " << l_featureTransedPointcloudUTM->points[10].x
             << ", cloud.y = " << l_featureTransedPointcloudUTM->points[10].y
             << ", cloud.z = " << l_featureTransedPointcloudUTM->points[10].z << endl;
        writeBinary_(c_stSysParam_->m_sPkgPath + "/data/Map/" + c_stSysParam_->m_map.m_sMapName
                         + "/l_featureTransedPointcloudUTM_hsq1.pcd",
                     *l_featureTransedPointcloudUTM);

        // pcl::io::savePCDFileBinaryCompressed(c_stSysParam_->m_sPkgPath + "/data/Map/" +
        // c_stSysParam_->m_map.m_sMapName
        //                             + "/l_featureTransedPointcloudUTM_hsq2.pcd",
        //                         *l_featureTransedPointcloudUTM);
        // pcl::io::savePCDFileASCII(c_stSysParam_->m_sPkgPath + "/data/Map/" +
        // c_stSysParam_->m_map.m_sMapName
        //                             + "/l_featureTransedPointcloudUTM_hsq3.pcd",
        //                         *l_featureTransedPointcloudUTM);

        printf("hsq: saving l_pcVisibleVector......\n");
        std::vector<typename pcl::PointCloud<M>::Ptr> l_pcVisibleVector;
        for (const typename pcl::PointCloud<M>::Ptr pointcloudPtr : l_pcVisible)
        {
            typename pcl::PointCloud<M>::Ptr l_pcVisibleTransedPointcloud(new pcl::PointCloud<M>);
            pcl::transformPointCloud(*pointcloudPtr, *l_pcVisibleTransedPointcloud, transform);

            for (auto& cloud : l_pcVisibleTransedPointcloud->points)
            {
                ENUPosition << cloud.x, cloud.y, cloud.z;
                l_centerPointUTM.clear();
                if (c_vEnu2UTM)
                    l_centerPointUTM = c_vEnu2UTM(ENUPosition);
                cloud.x = l_centerPointUTM[0];
                cloud.y = l_centerPointUTM[1];
                cloud.z = l_centerPointUTM[2];
            }
            l_pcVisibleTransedPointcloud->height = 1;
            l_pcVisibleTransedPointcloud->width = l_pcVisibleTransedPointcloud->points.size();
            // cout<<"hsq: l_pcVisibleTransedPointcloud width: = " <<
            // l_pcVisibleTransedPointcloud->width
            //         << ", pointcloudPtr->points = " << pointcloudPtr->points.size()
            //         <<endl;
            l_pcVisibleVector.push_back(l_pcVisibleTransedPointcloud);
        }
        c_pReadWriteMap_->writeBinary(c_stSysParam_->m_sPkgPath + "/data/Map/"
                                          + c_stSysParam_->m_map.m_sMapName + "/"
                                          + c_stSysParam_->m_map.m_sMapName + "_transed.wj",
                                      l_savePoseTransedPointcloud,
                                      l_featureVector,
                                      l_pcVisibleVector);
        printf("hsq: saved transed UTM map......\n");
    }
    else
    {
        l_feature.push_back(l_saveMap->m_pFeature->first);
        l_feature.push_back(l_saveMap->m_pFeature->second);
        l_feature.push_back(l_saveMap->m_pFeature->fourth);
        l_feature.push_back(l_saveMap->m_pFeature->third);
        if (c_stSysParam_->m_bDebugModel)
        {
            writeBinary_(c_stSysParam_->m_sPkgPath + "/data/Map/" + c_stSysParam_->m_map.m_sMapName
                             + "/.r_newAdd.pcd",
                         *(l_saveMap->m_pFeature->allPC));
            writeBinary_(c_stSysParam_->m_sPkgPath + "/data/Map/" + c_stSysParam_->m_map.m_sMapName
                             + "/.rfi_newAdd.pcd",
                         *(l_saveMap->m_pFeature->first));
            writeBinary_(c_stSysParam_->m_sPkgPath + "/data/Map/" + c_stSysParam_->m_map.m_sMapName
                             + "/.rse_newAdd.pcd",
                         *(l_saveMap->m_pFeature->second));
            writeBinary_(c_stSysParam_->m_sPkgPath + "/data/Map/" + c_stSysParam_->m_map.m_sMapName
                             + "/.rmark_newAdd.pcd",
                         *(l_saveMap->m_pFeature->third));
            writeBinary_(c_stSysParam_->m_sPkgPath + "/data/Map/" + c_stSysParam_->m_map.m_sMapName
                             + "/.curb_newAdd.pcd",
                         *(l_saveMap->m_pFeature->fourth));
            writeBinary_(c_stSysParam_->m_sPkgPath + "/data/Map/" + c_stSysParam_->m_map.m_sMapName
                             + "/.p_newAdd.pcd",
                         *c_pKfMapPairNewAdd_->getKfPose());
        }
        if (c_pMapProcer_ && c_stSysParam_->m_map.m_bSaveMapProc)
        {
            // 下采样&滤波
            c_pMapProcer_->process(l_saveMap, *l_saveMap);
            pcl::VoxelGrid<M> l_vox;
            l_vox.setLeafSize(0.1, 0.1, 0.1);
            l_vox.setInputCloud(l_allPer2d);
            l_vox.filter(*l_allPer2d);

            pcl::StatisticalOutlierRemoval<M> sor;
            sor.setInputCloud(l_allPer2d);
            sor.setMeanK(15);
            sor.setStddevMulThresh(0.1);
            sor.filter(*l_allPer2d);
            // c_pMapProcer_->make2D(l_allPer2d);
        }
        else
        {
            pcl::copyPointCloud(*l_saveMap->m_pFeature->allPC, *l_allPer2d);
            for (size_t j = 0; j < l_allPer2d->size(); j++)
                l_allPer2d->points[j].z = 0;
        }
        l_pcVisible.push_back(l_saveMap->m_pFeature->allPC);
        l_pcVisible.push_back(l_allPer2d);

        c_pReadWriteMap_->writeBinary(c_stSysParam_->m_sPkgPath + "/data/Map/"
                                          + c_stSysParam_->m_map.m_sMapName + "/"
                                          + c_stSysParam_->m_map.m_sMapName + "_newAdd.wj",
                                      c_pKfMapPairNewAdd_->getKfPose(),
                                      l_feature,
                                      l_pcVisible);
    }

    LOGFAE(WINFO,
           "地图 [{}] 保存成功 | 路径: {} | 用时:{:.3f}",
           c_stSysParam_->m_map.m_sMapName,
           c_stSysParam_->m_sPkgPath + "/data/Map/" + c_stSysParam_->m_map.m_sMapName,
           tic.toc());
}

template <typename C, typename M> bool Location<C, M>::isUpdateMap_()
{
    bool res = false;
    u_char l_ucMoveStatus = 0;
    s_POSE6D l_stCurrPose = c_poseTwistContainer_.getSlamOptPose();  // 取的是融合之后的位姿
    s_PoseWithTwist l_stFusePoseTwist = c_poseTwistContainer_.getFusePoseTwist();
    s_PoseWithTwist l_stLastAddMapPoseTwist = c_poseTwistContainer_.getLastAddMapPoseTwist();
    switch (c_sConfig_.m_workMode)
    {
        case WorkMode::InitMapMode:
            // std::cout << "hsq: c_bIsKeyFrame_ = " << c_bIsKeyFrame_
            //           << ", l_stCurrPose.m_bFlag= " << l_stCurrPose.m_bFlag
            //           << ", PoseStatus::ContinuePose= " << PoseStatus::ContinuePose << std::endl;
            res = c_bIsKeyFrame_ & (l_stCurrPose.m_bFlag == PoseStatus::ContinuePose);
            break;
        case WorkMode::ContMapMode:
            res = c_bIsKeyFrame_ & (l_stCurrPose.m_bFlag == PoseStatus::ContinuePose);
            break;
        case WorkMode::UpdateMapMode:
            l_ucMoveStatus = checkMoveStatus_(l_stLastAddMapPoseTwist, l_stFusePoseTwist);
            // res = (l_ucMoveStatus & static_cast<u_char>(MoveStatus::Length_Enough))
            //       | (l_ucMoveStatus & static_cast<u_char>(MoveStatus::Time_Enough));
            res = (l_ucMoveStatus & static_cast<u_char>(MoveStatus::Length_Enough));
            if (res)
            {
                c_poseTwistContainer_.setLastAddMapPoseTwist(l_stFusePoseTwist);
            }
            break;
        default: break;
    }
    return res;
}
template <typename C, typename M> void Location<C, M>::configUpdate_()
{
    // 更新workmode
    c_sConfig_.m_workMode = WorkMode(c_stSysParam_->m_iWorkMode);

    // 根据工作模式选择各参数状态
    switch (c_sConfig_.m_workMode)
    {
        case WorkMode::StandByMode: c_sConfig_.reset(); break;

        case WorkMode::InitMapMode:
            c_sConfig_.m_bIsEnLoop = c_stSysParam_->m_slam.m_bEnableLoop;  // loop由外部控制
            c_sConfig_.m_bOnlyLocatMode = false;                           // 非只定位模式
            c_sConfig_.m_bCopyLatestKeyFrame = false;                      // 不丢弃以前帧
            c_sConfig_.m_bIsEnAmcl = false;                                // 不开启AMCL
            c_sConfig_.m_bIsEnRM = true;                                   // 开启RM
            break;

        case WorkMode::ContMapMode:
            c_sConfig_.m_bIsEnLoop = c_stSysParam_->m_slam.m_bEnableLoop;  // loop由外部控制
            c_sConfig_.m_bOnlyLocatMode = false;                           // 非只定位模式
            c_sConfig_.m_bCopyLatestKeyFrame = false;                      // 不丢弃以前帧
            c_sConfig_.m_bIsEnAmcl = false;                                // 不开启AMCL
            c_sConfig_.m_bIsEnRM = true;                                   // 开启RM
            break;

        case WorkMode::LocatMode:
            c_sConfig_.m_bIsEnLoop = false;           // 不允许开loop
            c_sConfig_.m_bOnlyLocatMode = true;       // 仅定位模式
            c_sConfig_.m_bCopyLatestKeyFrame = true;  // 丢弃以前帧至最新
            c_sConfig_.m_bIsEnAmcl = false;           // 不开启AMCL
            c_sConfig_.m_bIsEnRM = false;             // 不开启RM
            break;

        case WorkMode::UpdateMapMode:
            c_sConfig_.m_bIsEnLoop = false;           // 不允许开回环
            c_sConfig_.m_bOnlyLocatMode = true;       // 仅定位模式
            c_sConfig_.m_bCopyLatestKeyFrame = true;  // 丢弃以前帧至最新,可改false
            c_sConfig_.m_bIsEnAmcl = false;           // 不开启AMCL
            c_sConfig_.m_bIsEnRM = false;             // 不开启RM
            break;

        default: c_sConfig_.reset(); break;
    }

    c_pKeyFrameMap_->setIdModel(c_sConfig_.m_bOnlyLocatMode);
    c_sConfig_.m_bIsSendPC = c_vSendPC_;

    if (c_stSysParam_->m_bDebugModel)
        c_sConfig_.printf();
}

template <typename C, typename M> void Location<C, M>::submapSetPose_(s_POSE6D& p_poseCurr)
{
    if (!c_stSysParam_->m_pos.m_bReWirteByPathZ)
    {
        p_poseCurr.setZ(FilterFactory::getInstance()->getNearbyPathZ(p_poseCurr.m_trans));
    }
    p_poseCurr.printf("setPose");
    LOGFAE(WINFO,
           "{} 设定位姿 | x {:.3f} y {:.3f} z {:.3f} roll {:.3f} pitch {:.3f} yaw {:.3f}",
           WJLog::getWholeSysTime(),
           p_poseCurr.x(),
           p_poseCurr.y(),
           p_poseCurr.z(),
           p_poseCurr.roll(),
           p_poseCurr.pitch(),
           p_poseCurr.yaw());
    c_pSubmap_->setPose(p_poseCurr.m_trans, p_poseCurr.m_quat);
    float scanRange = 35;
    c_pSubmap_->setScanRange(p_poseCurr.m_trans.x() - scanRange,
                             p_poseCurr.m_trans.y() - scanRange,
                             p_poseCurr.m_trans.x() + scanRange,
                             p_poseCurr.m_trans.y() + scanRange);
    c_pSubmap_->updateSubmap(true);

    c_bManualPoseModel = true;
    c_nManualPoseModelNum = 0;
    c_stSysParam_->m_pos.m_bReWirteByPathZ = false;
}

template <typename C, typename M> void Location<C, M>::submapUpdate_()
{
    c_pSubmap_->setEnableLoop(c_sConfig_.m_bIsEnLoop);
    FilterFactory::getInstance()->setFilterType(c_stSysParam_->m_map.m_iOptimizeModel);
}
template <typename C, typename M>
u_char Location<C, M>::checkMoveStatus_(s_PoseWithTwist& p_poseBefore, s_PoseWithTwist& p_poseCurr)
{
    u_char res = static_cast<u_char>(MoveStatus::Noise);
    s_POSE6D l_sPoseDev;
    // 当当前状态不是连续定位,则MoveStatus::Noise
    if (p_poseCurr.m_bFlag == PoseStatus::ContinuePose)
    {
        l_sPoseDev = p_poseBefore.m_Pose.inverse() * p_poseCurr.m_Pose;
        if (l_sPoseDev.normXY() > 1)
        {
            res |= static_cast<u_char>(MoveStatus::Length_Enough);
        }
        if (std::fabs(l_sPoseDev.yaw()) > 60)
        {
            res |= static_cast<u_char>(MoveStatus::Ang_Enough);
        }
        // 当前时间戳无法使用,后续版本更新时间戳
        if (p_poseCurr.m_tsSyncTime - p_poseBefore.m_tsSyncTime > 1000)
        {
            res |= static_cast<u_char>(MoveStatus::Time_Enough);
        }
        // todo: 继续增加其他状态
    }
    return res;
}
template <typename C, typename M> void Location<C, M>::paramUpdate_()
{
    paramReset_();
    configUpdate_();
    submapUpdate_();
    // 设置模式用来判定iVox需要计算的点数
    c_pMatcher_->setWorkModel(c_stSysParam_->m_iWorkMode);
}
template <typename C, typename M> void Location<C, M>::mapParamUpdate_()
{
    c_pstLocalMap_.reset(new MapFrame());
    c_sConfig_.m_iOldMapNum = c_pKeyFrameMap_->getKfSize();  // 记录老地图数量
    submapSetPose_(c_stSysParam_->m_pos.m_stSetPose);
    c_poseTwistContainer_.reset();
}
template <typename C, typename M> void Location<C, M>::setMarchParam_(s_MatcherConfig& p_stParam)
{
    // 设置点到线、面距离阈值
    c_pMatcher_->setDistThreshold(p_stParam.m_fMaxDist);
    // 设置角点2D搜索半径
    c_pMatcher_->setCornerSearchR(p_stParam.m_fLine2DRadius);
    // 设置靶标搜索半径
    c_pMatcher_->setMarkSearchR(p_stParam.m_fLine2DMarkRadius);
    // 设置角点2D匹配有效高差范围
    c_pMatcher_->setZAxisThreshold(p_stParam.m_fLineMaxZDiff);
    // 设置角点2D匹配最小点数
    c_pMatcher_->setSearchNumThreshold(p_stParam.m_uiLineMinPoints);
    // 设置面点k近邻搜索
    c_pMatcher_->setSearchK(p_stParam.m_uiPlaneMaxPoints);
    // 设置面点搜索到的N点的最大距离差
    c_pMatcher_->setRadiusThreshold(p_stParam.m_fPlaneMaxRadius);
    // 设置平面评价阈值
    c_pMatcher_->setPlaneMeanDiffThr(p_stParam.m_fPlaneMeanDiff);
    if (p_stParam.m_vfPlanePCA.size() == 2)
        c_pMatcher_->setPlanePcaThr((int)(p_stParam.m_vfPlanePCA[0]), p_stParam.m_vfPlanePCA[1]);
}
template <typename C, typename M>
void Location<C, M>::transPointCloud_(s_POSE6D& p_stPose, KeyFramePtr& p_pKeyFrame)
{
    c_pMatcher_->transformCloudPoints(p_stPose.m_quat,
                                      p_stPose.m_trans,
                                      p_pKeyFrame->m_pFeature->first,
                                      p_pKeyFrame->m_pFeature->first);
    c_pMatcher_->transformCloudPoints(p_stPose.m_quat,
                                      p_stPose.m_trans,
                                      p_pKeyFrame->m_pFeature->second,
                                      p_pKeyFrame->m_pFeature->second);
    c_pMatcher_->transformCloudPoints(p_stPose.m_quat,
                                      p_stPose.m_trans,
                                      p_pKeyFrame->m_pFeature->fourth,
                                      p_pKeyFrame->m_pFeature->fourth);
    c_pMatcher_->transformCloudPoints(p_stPose.m_quat,
                                      p_stPose.m_trans,
                                      p_pKeyFrame->m_pFeature->third,
                                      p_pKeyFrame->m_pFeature->third);
}
template <typename C, typename M>
bool Location<C, M>::isResetTwist_(const s_POSE6D& p_stLastPose, const s_POSE6D& p_stCurrPose)
{
    // 没有初始化时,返回true
    if (!c_bHasInit)
    {
        return true;
    }
    else
    {
        // 第一帧或者上一帧异常
        if (PoseStatus::ContinuePose != p_stLastPose.m_bFlag)
        {
            // wjPrintError("reset twist while LastPose! | flag: ", p_stLastPose.m_bFlag);
            LOGM(WERROR,
                 "{} 帧[{}] reset twist while LastPose! | flag {}",
                 WJLog::getWholeSysTime(),
                 c_uiScanIDCur_,
                 p_stLastPose.m_bFlag);
            return true;
        }
        // 当前帧定位异常
        if (PoseStatus::ContinuePose != p_stCurrPose.m_bFlag)
        {
            // wjPrintError("reset twist while CurrPose! | flag: ", p_stCurrPose.m_bFlag);
            LOGM(WERROR,
                 "{} 帧[{}] reset twist while CurrPose! | flag {}",
                 WJLog::getWholeSysTime(),
                 c_uiScanIDCur_,
                 p_stCurrPose.m_bFlag);
            return true;
        }
        // 跳帧太多
        if (c_sConfig_.m_bOnlyLocatMode && (c_fJumpNum_ > 10))
        {
            LOGM(WWARN,
                 "{} 帧[{}] reset twist while locate jump {}ms!",
                 WJLog::getWholeSysTime(),
                 c_uiScanIDCur_,
                 c_fJumpNum_ * SCAN_TIME_MS);
            return true;
        }
    }

    return false;
}

template <typename C, typename M> void Location<C, M>::initFrameLabel(KeyFramePtr& p_pKeyFrame)
{
    for (int i = 0; i < p_pKeyFrame->m_pFeature->surfSize(); i++)
    {
        // 区分房顶、地面点与双层墙，房顶、地面点不参与双层墙计算
        p_pKeyFrame->m_pFeature->second->points[i].s = 0;
        if (((p_pKeyFrame->m_pFeature->second->points[i].z - p_pKeyFrame->m_Pose.m_trans.z())
             < (-c_stSysParam_->m_map.m_fGroundHigh))
            || ((p_pKeyFrame->m_pFeature->second->points[i].z - p_pKeyFrame->m_Pose.m_trans.z())
                > c_stSysParam_->m_map.m_fRoofHigh))
            p_pKeyFrame->m_pFeature->second->points[i].v =
                2.0;  // 如果是第一帧，要先覆盖之前写的v值，防止后续滤掉该点了而v值没有被覆盖
        else
            p_pKeyFrame->m_pFeature->second->points[i].v = 0;
    }
}
template <typename C, typename M> void Location<C, M>::backoutFrameLabel(KeyFramePtr& p_pKeyFrame)
{
    // 采用kdtree实现当前帧点云的标签再次回退
    c_pKdtKeyFrame_->setInputCloud(p_pKeyFrame->m_pFeature->second);

    std::vector<int> l_viSearchIdx;
    std::vector<float> l_vfSearchDis;
    for (size_t id = 0; id < p_pKeyFrame->m_pFeature->second->size(); id++)
    {
        if (0 == p_pKeyFrame->m_pFeature->second->points[id].v)
        {
            M l_pntSrh;
            l_pntSrh.x = p_pKeyFrame->m_pFeature->second->points[id].x;
            l_pntSrh.y = p_pKeyFrame->m_pFeature->second->points[id].y;
            l_pntSrh.z = p_pKeyFrame->m_pFeature->second->points[id].z;

            if (c_pKdtKeyFrame_->nearestKSearch(l_pntSrh, 12, l_viSearchIdx, l_vfSearchDis) < 4)
            {
                continue;
            }
            // h存的是法线的角度
            p_pKeyFrame->m_pFeature->second->points[id].h = c_pMatcher_->getNormalAngle(
                p_pKeyFrame->m_Pose.m_trans.x() - p_pKeyFrame->m_pFeature->second->points[id].x,
                p_pKeyFrame->m_Pose.m_trans.y() - p_pKeyFrame->m_pFeature->second->points[id].y);

            std::vector<M> l_vntnear;
            // 高度过滤
            for (std::size_t it = 0; it < l_viSearchIdx.size(); it++)
            {
                if (fabs(l_pntSrh.z - p_pKeyFrame->m_pFeature->second->points[l_viSearchIdx[it]].z)
                    < c_stSysParam_->m_map.m_fFilterZValue)
                    l_vntnear.emplace_back(
                        p_pKeyFrame->m_pFeature->second->points[l_viSearchIdx[it]]);
            }

            if (l_vntnear.size() < 4)
            {
                continue;
            }
            // 找直线
            // s存的是墙的角度
            p_pKeyFrame->m_pFeature->second->points[id].s = c_pMatcher_->getWallLine(l_vntnear);
            // 处理边界角度问题
            c_pMatcher_->edgeAngleHandle(p_pKeyFrame->m_pFeature->second->points[id].h,
                                         p_pKeyFrame->m_pFeature->second->points[id].s);
            if (c_pMatcher_->angleFormula(
                    p_pKeyFrame->m_pFeature->second->points[id].h,
                    p_pKeyFrame->m_pFeature->second->points[id].s))  // 判断当前点的标签
                p_pKeyFrame->m_pFeature->second->points[id].v = 1.0;
            else
                p_pKeyFrame->m_pFeature->second->points[id].v = -1.0;
        }
    }
#    ifdef IVOXDEBUG
    // 用于显示当前IVox栅格内部存储的地图点
    c_pstLocaliVoxMap_->free();
    c_pIvoxGrid_->getiVoxMap(c_pstLocaliVoxMap_->m_pFeature->second);
#    endif  // IVOXDEBUG
    // 每次将当前帧标签为0的点云尽可能赋予标签后，
    // 清空未赋予标签的标签为0的点(应将其挪动值标签为-2的vector中，暂时未实现)，为下帧作准备
    //  c_pIvoxGrid_->clearGrid();
}
template <typename C, typename M> bool Location<C, M>::renewFactorGraph_()
{
    if (c_sConfig_.m_bIsEnLoop)
    {
        // 这就是当前新加的帧
        MapFramePtr l_pNewFrame = c_pKeyFrameMap_->back().map;
        int l_iNewIdx = c_pKeyFrameMap_->getKfSize() - 1;
        // 添加新节点和连续因子到因子图
        c_pGraph_->addPoseNodeFactor(l_iNewIdx, l_pNewFrame->m_Pose);
        // 添加GPS因子
        if(c_stSysParam_->m_map.m_isUseGPSOptimize && c_isGPSUseful){// && l_iNewIdx!= 0
            std::cout<<"c_isGPSUseful,  l_iNewIdx = " << l_iNewIdx << std::endl;
            c_pGraph_->addGpsNodeFactor(l_iNewIdx, c_GPSPose6D);
            // c_pGraph_->setGPSTrigger();
        }
        else if(!c_stSysParam_->m_map.m_isUseGPSOptimize){
            std::cout<<"不使用GPS优化" << std::endl;
        }
        else{
             std::cerr<<"GPS is not Useful " << std::endl;
        }
        
        // 添加靶标因子
        // c_pGraph_->addMarksNodeFactor(l_iNewIdx, l_pNewFrame->m_pFeature->third);
        // 检测到回环帧
        if (c_pGraph_->readyCorrect())
        {
            std::cout<<"gps correct: " << c_pGraph_->readyGpsCorrect() << std::endl;
            std::cout<<"loop correct: " << c_pGraph_->readyLoopCorrect() << std::endl;
            std::cout<<"mark correct: " << c_pGraph_->readyMarkCorrect() << std::endl;
            if (c_mutexSavemap_.try_lock())
            {
                c_pGraph_->correct();
                std::cerr<<"-------------------finished correct" << std::endl;
                c_mutexSavemap_.unlock();
                if (c_pGraph_->getIfUpdate())
                {  // 若点云发生改变,则更新子图
                    s_PoseWithTwist l_stFusePoseTwist;
                    l_stFusePoseTwist = c_poseTwistContainer_.getFusePoseTwist();
                    l_stFusePoseTwist.m_Pose = (c_pKeyFrameMap_->back().map)->m_Pose;
                    c_poseTwistContainer_.setFusePoseTwist(l_stFusePoseTwist);
                    c_poseTwistContainer_.setSlamOptPoseTwist(l_stFusePoseTwist);
                    // 更新子图（启动线程）
                    c_pSubmap_->setPose(l_stFusePoseTwist.m_Pose.m_trans,
                                        l_stFusePoseTwist.m_Pose.m_quat);
                    std::cerr<<"-------------------点云发生改变,则更新子图" << std::endl;
                    return true;
                }
            }
        }
        else{
            std::cerr<<"do not use factor " << std::endl;
        }

        c_pGraph_->addLoopCheckPair(l_iNewIdx, c_vCallBackIndex_);
    }
    return false;
}
template <typename C, typename M> void Location<C, M>::reMovingFrame_(MapFramePtr& p_pFrame)
{
    // 动态过滤
    if (c_sConfig_.m_bIsEnRM)
    {
        size_t l_iIndex = 0;
        if (WorkMode::UpdateMapMode != c_stSysParam_->m_iWorkMode)
            l_iIndex = c_pKeyFrameMap_->getKfSize();
        else
            l_iIndex = c_pKfMapPairNewAdd_->getKfSize();
        // // 插入最新帧
        c_pRMoving_->setInputCloud(p_pFrame->m_pFeature->first,
                                   p_pFrame->m_pFeature->second,
                                   p_pFrame->m_pFeature->allPC,
                                   p_pFrame->m_Pose.m_trans,
                                   l_iIndex);
        // // 更新局部地图
        c_pRMoving_->renewLocalMap();
        // // 过滤中间帧
        // // size_t index = c_pRMoving_->getFilterIndex();
        c_pRMoving_->filter(
            p_pFrame->m_pFeature->first, p_pFrame->m_pFeature->second, p_pFrame->m_pFeature->allPC);
        // if (ULONG_MAX != index)
        // {
        //     LOGM(WDEBUG, "filter Map-[{}]", index);
        //     MapFramePtr& l_pToFrame = p_pFrame;
        //     // 旧帧在地图中
        //     if (index < l_iIndex)
        //     {
        //         if (WorkMode::UpdateMapMode != c_stSysParam_->m_iWorkMode)
        //             l_pToFrame = c_pKeyFrameMap_->getKfMapByInd(index);
        //         else
        //             l_pToFrame = c_pKfMapPairNewAdd_->getKfMapByInd(index);
        //     }
        //     c_pRMoving_->filter(l_pToFrame->m_pFeature->first,
        //                         l_pToFrame->m_pFeature->second,
        //                         l_pToFrame->m_pFeature->allPC);
        // }
    }
}
template <typename C, typename M>
void Location<C, M>::removeErrorLabelPoint(KeyFramePtr& p_pKeyFrame)
{
    int l_iNum = 0;
    int l_iSampleNum = p_pKeyFrame->m_pFeature->m_iSample2ndSize;
    for (auto id = p_pKeyFrame->m_pFeature->second->begin();
         id != p_pKeyFrame->m_pFeature->second->end();)
    {
        l_iNum++;
        if ((0 == id->v) || (3.0 == id->v))
        {
            id = p_pKeyFrame->m_pFeature->second->erase(id);
            if (l_iNum <= l_iSampleNum)
                p_pKeyFrame->m_pFeature->m_iSample2ndSize--;
        }
        else
            id++;
    }
}
template <typename C, typename M>
bool Location<C, M>::occupyVerifyResult_(float& p_fMatchNumPercent,
                                         float& p_fMatchOccupyScore,
                                         float& p_fPath)
{
    float l_fPathNum = p_fPath - ((int)p_fPath);
    float l_fPathScore = (p_fPath / 1000);
    // 考虑匹配点数为1的情况
    if (l_fPathNum < 1e-4)
        l_fPathNum = 1.0;
    if ((p_fMatchNumPercent / l_fPathNum) > c_stSysParam_->m_posCheck.m_fMatchNumPercent)
    {
        if ((p_fMatchOccupyScore / l_fPathScore) > c_stSysParam_->m_posCheck.m_fMatchOccupyScore)
        {
            // 恢复时打印数据
            if (c_PoseCheckNoClear == PoseCheckStatus::Novalid)
                LOGFAE(WERROR,
                       "{} 帧[{}] 概率校验恢复 当前帧 {} {} 路径 {} {}",
                       WJLog::getWholeSysTime(),
                       c_uiScanIDCur_,
                       p_fMatchNumPercent,
                       p_fMatchOccupyScore,
                       l_fPathNum,
                       l_fPathScore);
            return true;
        }
        else
            return false;
    }
    return false;
}
template <typename C, typename M>
bool Location<C, M>::checkOccupyVerify_(KeyFramePtr& p_pKeyFrame, bool& p_bOccupyVerify_)
{
    // 校验类未实例化 || 测试纯里程计下 默认通过
    if ((!c_pLaserVerify_) || c_stSysParam_->m_posCheck.m_bOnlyWheelOdom)
    {
        p_bOccupyVerify_ = true;
        return true;
    }

    s_POSE6D l_newPose = c_stIncreaseOpt_ * p_pKeyFrame->m_Pose;
    // 角点+面点实现位姿校验
    c_pLaserVerify_->setInputSourceCloud(p_pKeyFrame->m_pFeature->second,
                                         p_pKeyFrame->m_pFeature->first);
    c_pLaserVerify_->setInputCurrentPose(l_newPose);
    c_pLaserVerify_->calcuLaserVerifyScore();
    float l_fMatchOccupyScore = c_pLaserVerify_->getLaserVerifyScore();
    float l_fMatchNumPercent = c_pLaserVerify_->getLaserVerifyMatNum();
    int l_iPathScore = (int)((l_fMatchOccupyScore * 1000) / 1);
    // 概率写成复合值 小数点之前表示匹配概率 小数点之后表示匹配点数
    p_pKeyFrame->m_Pose.setP((float)l_iPathScore + l_fMatchNumPercent);
    // 未开启概率校验 || 更新地图模式下 校验通过
    if ((!c_stSysParam_->m_posCheck.m_bOpenOccupyVerify)
        || WorkMode::UpdateMapMode == c_stSysParam_->m_iWorkMode)
    {
        p_bOccupyVerify_ = true;
        return true;
    }
    p_bOccupyVerify_ = getOccupyVerify_(p_pKeyFrame, l_fMatchNumPercent, l_fMatchOccupyScore);
    return p_bOccupyVerify_;
}

template <typename C, typename M>
bool Location<C, M>::getOccupyVerify_(KeyFramePtr& p_keyFramePtr,
                                      float& p_fMatchNumPercent,
                                      float& p_fMatchOccupyScore)
{
    bool l_bCheckRes = false;
    float l_fNearbyPathI = -1.0;

    l_bCheckRes =
        FilterFactory::getInstance()->getNearbyPathI(p_keyFramePtr->m_Pose.m_trans, l_fNearbyPathI);
    if ((!l_bCheckRes) || l_fNearbyPathI < 1e-6)
    {
        // LOGFAE(WERROR,
        //        "{} 帧[{}] 概率校验异常 | 路径概率异常 | Pose: {:.3f} {:.3f} {:.3f} | getSign: {}
        //        | " "getValue {:.3f}", WJLog::getWholeSysTime(), c_uiScanIDCur_,
        //        p_keyFramePtr->m_Pose.x(),
        //        p_keyFramePtr->m_Pose.y(),
        //        p_keyFramePtr->m_Pose.z(),
        //        l_bCheckRes,
        //        l_fNearbyPathI);
        return true;
    }
    l_bCheckRes = occupyVerifyResult_(p_fMatchNumPercent, p_fMatchOccupyScore, l_fNearbyPathI);
    if (!l_bCheckRes)
        LOGFAE(WERROR,
               "{} 帧[{}] 概率校验异常 | score {} {} refer {}",
               WJLog::getWholeSysTime(),
               c_uiScanIDCur_,
               p_fMatchNumPercent,
               p_fMatchOccupyScore,
               l_fNearbyPathI);

    return l_bCheckRes;
}

template <typename C, typename M>
bool Location<C, M>::matchCurb_(KeyFramePtr& p_pKeyFrame, s_PoseWithTwist& p_stPosePrec)
{
    bool l_bRes = false;
    TicToc l_tt;
    s_POSE6D l_sOptied = p_stPosePrec.m_Pose;

    if (!p_pKeyFrame->m_pFeature->curbSize())
        return l_bRes;
    // 旋转时不作curb优化,结果不可信
    // 静止时不作curb优化，雷达朝向与车道线不一定平行
    if (c_ucMoveStatus & static_cast<u_char>(MoveStatus::Ang_Enough)
        | (c_ucMoveStatus & static_cast<u_char>(MoveStatus::Static_Enough)))
    {
        // LOGFAE(WINFO,
        //        "{} 帧[{}] 忽略curb, 运动状态不符 {}",
        //        WJLog::getWholeSysTime(),
        //        c_uiScanIDCur_,
        //        c_ucMoveStatus);
        return l_bRes;
    }

    c_pCurbMatcher_->setInputSource(p_pKeyFrame->m_pFeature->fourth);
    c_pCurbMatcher_->setInitialPose(l_sOptied.m_quat, l_sOptied.m_trans);

    s_POSE6D l_stPoseOpt, Incresase, optied, l_stPoseOpt1, l_stIncrease;
    // std::cout << "hsq: location.hpp matchCurb_ "
    //              "调用laserRegistration.hpp align()"
    //           << std::endl;
    if (c_pCurbMatcher_->align(l_stPoseOpt.m_quat, l_stPoseOpt.m_trans, l_stPoseOpt1.m_trans))
    {
        Incresase.m_quat = l_stPoseOpt.m_quat;
        Incresase.m_trans =
            l_sOptied.m_trans + l_stPoseOpt.m_trans - l_stPoseOpt.m_quat * l_sOptied.m_trans;
        optied = Incresase * l_sOptied;

        l_stIncrease = l_sOptied.inverse() * optied;
        l_bRes = (l_stIncrease.normXY() < c_stSysParam_->m_posCheck.m_stCurbCheck.m_Pose.x());

        if (l_bRes)
        {
            p_stPosePrec.m_Pose = optied;
            p_stPosePrec.m_Pose.m_bFlag = PoseStatus::CurbPose;
            p_stPosePrec.setFlag(PoseStatus::CurbPose);
        }
    }
    if ((!l_bRes) && l_tt.toc() > 30.0)
        LOGM(WERROR,
             "{} 帧[{}] matCurb fail Cost Time {}",
             WJLog::getWholeSysTime(),
             c_uiScanIDCur_,
             l_tt.toc());
    return l_bRes;
}

template <typename C, typename M> void Location<C, M>::shutDown()
{
    c_bLocationRun_ = false;
    c_bShutDown_ = true;
    // c_pRMoving_->shutDown();
    while (1)
    {
        // printf("location shutdown\n");
        if (c_bShutDownOver_)
            break;
        else
            usleep(1000);
    }
}
template <typename C, typename M> void Location<C, M>::start()
{
    mapParamUpdate_();
    if (c_bHasInit)
    {
        clearKeyFramesAll_();
    }
    if (c_sConfig_.m_bIsEnRM)
    {
        c_pRMoving_.reset(new RemoveMoving<M>(&c_stSysParam_->m_slam.m_stReMv));
        c_pRMoving_->setHeightSpace(-c_stSysParam_->m_map.m_fGroundHigh,
                                    c_stSysParam_->m_map.m_fRoofHigh);
    }
    else
        c_pRMoving_ = nullptr;
    if (c_sConfig_.m_bIsEnLoop)
        c_pGraph_.reset(new LoopGraph<M>(c_pKeyFrameMap_));
    else
        c_pGraph_ = nullptr;
    c_bLocationRun_ = true;
}
template <typename C, typename M> void Location<C, M>::setMarkMap(std::string p_path)
{
    // c_pMarkMatcher_->loadMarkMap(p_path, &c_stMarkSet_);
    // c_pMarkMatcher_->updateMarkMap(&c_stMarkSet_);
}
template <typename C, typename M> bool Location<C, M>::setWorkMode(int p_workMode)
{
    bool res = false;
    switch (p_workMode)
    {
        case WorkMode::StandByMode:
            this->stop();
            while (!this->isStop())
                sleepMs(10);
            paramReset_();
            res = true;
            break;
        case WorkMode::InitMapMode:
            this->stop();
            while (!this->isStop())
                sleepMs(10);
            paramUpdate_();
            this->start();
            res = true;
            break;
        case WorkMode::ContMapMode:
            this->stop();
            while (!this->isStop())
                sleepMs(10);
            paramUpdate_();
            if (!this->loadMap(c_stSysParam_->m_sPkgPath, c_stSysParam_->m_map.m_sMapName))
            {
                LOGFAE(WERROR, "地图加载失败，请检查地图文件是否存在");
                c_stSysParam_->m_fae.setErrorCode("J4");
                res = false;
            }
            else
            {
                LOGFAE(WINFO, "地图加载成功!");
                this->start();
                res = true;
            }
            break;
        case WorkMode::LocatMode:
            this->stop();
            while (!this->isStop())
                sleepMs(10);
            paramUpdate_();
            if (!this->loadMap(c_stSysParam_->m_sPkgPath, c_stSysParam_->m_map.m_sMapName))
            {
                LOGFAE(WERROR, "地图加载失败，请检查地图文件是否存在");
                // c_stSysParam_->m_fae.setErrorCode("J4");
                res = false;
            }
            else
            {
                LOGFAE(WINFO, "地图加载成功!");
                this->start();
                res = true;
            }
            break;
        case WorkMode::UpdateMapMode:
            this->stop();
            while (!this->isStop())
                sleepMs(10);
            paramUpdate_();
            if (!this->loadMap(c_stSysParam_->m_sPkgPath, c_stSysParam_->m_map.m_sMapName))
            {
                LOGFAE(WERROR, "地图加载失败，请检查地图文件是否存在");
                // c_stSysParam_->m_fae.setErrorCode("J4");
                res = false;
            }
            else
            {
                LOGFAE(WINFO, "地图加载成功!");
                this->start();
                res = true;
            }
            break;
        default: break;
    }

    return res;
}
template <typename C, typename M> void Location<C, M>::run()
{
    static double l_dLoctime = 0;
    static int L_iLoccnt = 0;
    static int l_iNoLidarViewPoseNum_ = 0;
    while (1)
    {
        if (checkNewKeyFrames_())
        {
            if (l_iNoLidarViewPoseNum_ > 0)
                LOGM(WERROR,
                     "{} 定位异常 | 雷达恢复 统计次数 {} 数据中断时间 {}",
                     WJLog::getWholeSysTime(),
                     l_iNoLidarViewPoseNum_,
                     c_stSysParam_->m_time.getTimeNowMs() - c_tsLasDataRecvTime_);
            TicToc ticLoc;
            s_POSE6D l_stSlamOptPose = c_poseTwistContainer_.getSlamOptPose();
            savePoseThread_(l_stSlamOptPose, c_stSysParam_->m_pos.m_sPoseFilePath + "pose_bak.csv");
            c_bLocationRunOver_ = false;
            c_pKeyFrameOptToBe_ = getKeyFrame_();

            c_uiScanIDCur_ = c_pKeyFrameOptToBe_->m_pFeature->m_uiScanFrame;
            c_nScanTimeCurr_ = c_pKeyFrameOptToBe_->m_pFeature->m_tsSyncTime;
            c_tsLastestLocatSysTime_ = c_pKeyFrameOptToBe_->m_pFeature->m_tsWallTime;
            c_tsLasDataRecvTime_ = c_stSysParam_->m_time.getTimeNowMs();
            c_bIsKeyFrame_ = c_pKeyFrameOptToBe_->m_bIsKeyFrame;
            // std::cout << "hsq: location.hpp run(), c_bIsKeyFrame_ = " << c_bIsKeyFrame_
            //           << std::endl;
            if (!c_bHasInit)
            {
                c_uiScanIDLast_ = c_uiScanIDCur_;
                c_nScanTimeLast_ = c_nScanTimeCurr_;
            }
            // if (hasEnoughMark_())
            // {
            //     //靶标匹配定位
            //     location_(c_pKeyFrameOptToBe_, c_CurrLocStatus_);
            //     if (isUpdateMap_())
            //     //如果建图模式，更新靶标地图,此处因还没有renewPose_(),可能会造成冲突
            //         buildMarkMap_();
            // }
            // else
            {
                TicToc ticLocLocation;
                location_(c_pKeyFrameOptToBe_, c_CurrLocStatus_);
                LOGM(WDEBUG,
                 "{} [LOC] 帧[{}] location_ Cost Time {:.3f}",
                 WJLog::getWholeSysTime(),
                 c_uiScanIDCur_,
                 ticLocLocation.toc());
            }

            
            {
                TicToc ticUpdate;
                update_(c_pKeyFrameOptToBe_);
                LOGM(WDEBUG,
                 "{} [LOC] 帧[{}] location_: update_Cost Time {:.3f}",
                 WJLog::getWholeSysTime(),
                 c_uiScanIDCur_,
                 ticUpdate.toc());
            }

            l_dLoctime += ticLoc.toc();
            L_iLoccnt++;
            COST_TIME_LOCTION(ticLoc.toc());
            COST_TIME_RECODE;
            LOGM(WDEBUG,
                 "{} [LOC] 帧[{}] Loct Cost Time {:.3f}",
                 WJLog::getWholeSysTime(),
                 c_uiScanIDCur_,
                 ticLoc.toc());
            LOGM(WDEBUG,
                 "{} [LOC] 帧[{}] Aver Cost Time {:.3f}",
                 WJLog::getWholeSysTime(),
                 c_uiScanIDCur_,
                 l_dLoctime / L_iLoccnt);

            if (c_sConfig_.m_bIsSendPC)
            {
                KeyFramePtr l_sendKFPtr = c_pKeyFrameOptToBe_;
                if (c_bIsKeyFrame_
                    && PoseStatus::ContinuePose == c_pKeyFrameOptToBe_->m_Pose.m_bFlag)
                    l_sendKFPtr->m_bIsKeyFrame = c_bIsKeyFrame_;
                if (c_sConfig_.m_bOnlyLocatMode)
                {
                    if (c_stSysParam_->m_bDebugModel)
                        c_vSendPC_(l_sendKFPtr, c_pstLocalMap_);
                    else
                        c_vSendPC_(l_sendKFPtr, c_pstWholeMap_);
                }
                else
                    c_vSendPC_(l_sendKFPtr, c_pstLocalMap_);
            }
            if (c_vSendBox_ && c_pcPredPoseBox_->points.size())
            {
                c_vSendBox_(c_pcPredPoseBox_);
            }
            c_bHasInit = true;
            c_bLocationRunOver_ = true;
            l_iNoLidarViewPoseNum_ = 0;
        }
        else if (isLidarDisCon_(c_stSysParam_->m_time.getTimeNowMs(), c_tsLasDataRecvTime_))
            lidarDisconFunc(l_iNoLidarViewPoseNum_);
        if (c_bShutDown_)
            break;

        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
    printf("exit Location\n");
    c_bShutDownOver_ = true;
}

template <typename C, typename M> void Location<C, M>::stop()
{
    c_bLocationRun_ = false;
    stopRemoveMoving();
}

template <typename C, typename M> bool Location<C, M>::isStop()
{
    if (false == c_bLocationRun_ && true == c_bLocationRunOver_)
        return true;
    return false;
}
template <typename C, typename M> void Location<C, M>::saveMap()
{
    std::thread saveMap(&Location<C, M>::saveMap_, this);
    saveMap.join();
}
template <typename C, typename M>
bool Location<C, M>::setNewCumulativeScan(PointCloudMapPtr p_map)  // 作用？
{
    static boost::shared_ptr<pcl::VoxelGrid<M>> l_downSizeFilter(new pcl::VoxelGrid<M>());
    static bool once = true;
    if (once)
    {
        l_downSizeFilter->setLeafSize(0.4, 0.4, 0.8);
        once = false;
    }
    // 如果Map正被占用则返回否
    if (false == c_mutexCumulativeMap_.try_lock())
        return false;
    *c_pcMergeMapCloud_ += *p_map;
    l_downSizeFilter->setInputCloud(c_pcMergeMapCloud_);
    l_downSizeFilter->filter(*c_pcMergeMapCloud_);
    c_mutexCumulativeMap_.unlock();
    return true;
}
template <typename C, typename M>
boost::shared_ptr<pcl::PointCloud<M>> Location<C, M>::getCumulativeScanCloud()  //?
{
    return c_pcMergeMapCloud_;
}
template <typename C, typename M>
void Location<C, M>::setMap(pcl::PointCloud<POSE>::Ptr p_pose, MapFramePtr p_map)
{
    c_pKeyFrameMap_->generateKfMap(p_pose, p_map, c_stSysParam_->m_map.m_fMapKFSubRange);
    FilterFactory::getInstance()->setNearByPath(c_pcKeyPoseSearch_);
}
template <typename C, typename M>
bool Location<C, M>::loadMap(std::string l_sFilePath, std::string l_sMapHeader)
{
    MapFramePtr l_map(new MapFrame());
    std::vector<typename pcl::PointCloud<M>::Ptr> l_feature;
    l_feature.push_back(l_map->m_pFeature->first);
    l_feature.push_back(l_map->m_pFeature->second);
    l_feature.push_back(l_map->m_pFeature->fourth);
    // 注意顺序
    l_feature.push_back(l_map->m_pFeature->third);

    std::vector<typename pcl::PointCloud<M>::Ptr> l_pcVisible;
    l_pcVisible.push_back(l_map->m_pFeature->allPC);
    PointCloudMapPtr l_p2DMap(new PointCloudMap());
    l_pcVisible.push_back(l_p2DMap);

    type::PosePcPtr l_pcKeyFramesPose = boost::make_shared<type::PosePc>();
    if (!checkReadMap(l_sFilePath, l_sMapHeader, l_feature, l_pcVisible, l_pcKeyFramesPose))
        return false;

    typename pcl::VoxelGrid<M> l_ds;
    cout << "hsq: setLeafSize: 0.3\n" << endl;
    l_ds.setLeafSize(0.3, 0.3, 0.3);  // hsq: raw(0.3, 0.3, 0.3)
    l_ds.setInputCloud(l_map->m_pFeature->allPC);
    l_ds.filter(*c_pstWholeMap_->m_pFeature->allPC);
    *c_pstWholeMap_->m_pFeature->first = *l_map->m_pFeature->first;
    *c_pstWholeMap_->m_pFeature->second = *l_map->m_pFeature->second;
    *c_pstWholeMap_->m_pFeature->third = *l_map->m_pFeature->third;
    *c_pstWholeMap_->m_pFeature->fourth = *l_p2DMap;
    pcl::copyPointCloud(*l_pcKeyFramesPose, *c_pcKeyPoseSearch_);  // 格式转换
    setMap(l_pcKeyFramesPose, l_map);
    PointCloudMapPtr l_pcPath(new PointCloudMap());
    pcl::copyPointCloud(*l_pcKeyFramesPose, *l_pcPath);  // 格式转换
    // if (c_sConfig_.m_bIsEnAmcl)
    // c_pGlobalAmcl_->setCloudMap(l_map->m_pFeature->first, l_map->m_pFeature->second, l_pcPath);
    cout << "\nhsq: loadMap successed\n" << endl;
    return true;
}

template <typename C, typename M>
bool Location<C, M>::checkReadMap(std::string p_sFilePath,
                                  std::string p_sMapHeader,
                                  std::vector<typename pcl::PointCloud<M>::Ptr>& p_feature,
                                  std::vector<typename pcl::PointCloud<M>::Ptr>& p_pcVisible,
                                  pcl::PointCloud<POSE>::Ptr p_Pose)
{
    bool l_bRes = true;
    // 地图不存在
    if (!isExistFileOrFolder(p_sFilePath + "/data/Map/" + p_sMapHeader))
    {
        LOGFAE(WERROR, "地图检测异常 | 文件 [{}] 不存在，请检查!", p_sMapHeader);
        return false;
    }
    if (!isExistFileOrFolder(p_sFilePath + "/data/Map/" + p_sMapHeader + "/" + p_sMapHeader
                             + ".wj"))
    {
        LOGFAE(WERROR, "地图检测异常 | 地图文件不存在,请检查!");
        return false;
    }
    c_pReadWriteMap_->readBinary(p_sFilePath + "/data/Map/" + p_sMapHeader + "/" + p_sMapHeader
                                     + ".wj",
                                 p_Pose,
                                 p_feature,
                                 p_pcVisible);
    // if (!p_feature[Corn - 1]->points.size() || !p_feature[Surf - 1]->points.size()
    //     || !p_Pose->points.size())
    // {
    //     LOGFAE(WERROR, "地图检测异常 | 地图核心文件损坏,请检查!");
    //     l_bRes = false;
    // }

    if (!p_pcVisible[255 - Cloud3D]->points.size() || !p_pcVisible[255 - Cloud2D]->points.size())
    {
        LOGFAE(WERROR, "地图检测异常 | 可视化地图核心文件损坏,请检查!");
        l_bRes = false;
    }

    return l_bRes;
}

template <typename C, typename M> void Location<C, M>::stopRemoveMoving()
{
    // c_pRMoving_->stop();
    // while (!c_pRMoving_->isStop())
    // {
    //     sleepMs(10);
    // }
}
template <typename C, typename M>
Location<C, M>::Location(
    Queue<KeyFramePtr>& p_keyframe,
    LT_OUTPUT_CB p_highPrecsPoseCb,
    WHEELOdom_CB p_getWheelOdomCb,
    boost::function<void(int, int)> p_bakFeature,
    boost::function<void(std::vector<s_POSE6D>)> p_sendPos,
    boost::function<void(KeyFramePtr, MapFramePtr)> p_sendPC,
    boost::function<void(pcl::PointCloud<pcl::PointXYZ>::Ptr)> p_sendBox,
    boost::function<std::vector<double>(const Eigen::Vector3d& bodyAxisPosition)> p_enu2UTM)
    : c_qKeyFrameOptToBe_(p_keyframe), c_highPrecsPoseCb_(p_highPrecsPoseCb),
      c_getWheelOdomCb_(p_getWheelOdomCb), c_bakFeatureCb_(p_bakFeature), c_vSendPC_(p_sendPC),
      c_vSendPose_(p_sendPos), c_vSendBox_(p_sendBox), c_vEnu2UTM(p_enu2UTM)
{
    c_stSysParam_ = SYSPARAM::getIn();
    if (OptimizeMapType::IVOX_TYPE == c_stSysParam_->m_map.m_iOptimizeModel)
    {
        c_pIvoxGrid_.reset(new IVox<3, M>());
        c_pLaserVerify_.reset(new GridMapScore<C, M>(*c_stSysParam_));
    }
    c_pKdtKeyFrame_.reset(new KdTree);
    c_pMatcher_.reset(new LaserRegistration<C, M>());
    // c_pMarkMatcher_.reset(new MarkMatch());            //靶标定位
    // c_pAMCLer_.reset(new wanjilocal::Amcl2D<M, C>());  // locm初始定位
    // c_pGlobalAmcl_.reset(new wanjilocal::GlobalAmcl<M, C>());      //全局amcl
    // c_pLooper_.reset(new LoopGraph<M>(
    // c_vMapFrames_, c_mutexKeyFrame_, boost::bind(&Location<C, M>::loopCallback_, this, _1)));
    c_pReadWriteMap_.reset(new LaserIO<M, POSE>());
    c_pMapProcer_.reset(new MapProc<M>());
    c_pCurbMatcher_.reset(new CurbRegistration<C, M>());
    // std::thread loop(&LoopGraph<M>::run, c_pLooper_);
    paramInit_();
    setWorkMode(c_stSysParam_->m_iWorkMode);
    // loop.detach();
}
template <typename C, typename M> Location<C, M>::~Location()
{
    std::cout << "exit loc" << std::endl;
    c_pKdtKeyFrame_ = nullptr;
    c_pcKeyPoseSearch_ = nullptr;
    c_pstLocalMap_ = nullptr;
    c_pstLocaliVoxMap_ = nullptr;
    c_pLaserVerify_ = nullptr;
    c_pstWholeMap_ = nullptr;
    c_pKeyFrameOptToBe_ = nullptr;
    c_pMatcher_ = nullptr;
    // c_pMarkMatcher_ = nullptr;
    c_pRMoving_ = nullptr;
    // c_pAMCLer_ = nullptr;
    // c_pLooper_ = nullptr;
    // c_pGlobalAmcl_ = nullptr;
    c_pReadWriteMap_ = nullptr;
    c_pCurbMatcher_ = nullptr;
    c_pIvoxGrid_ = nullptr;
    c_pPoseCheck_ = nullptr;
    c_pcMergeMapCloud_ = nullptr;
    c_pcPredPoseBox_ = nullptr;
    c_pMapProcer_ = nullptr;
}
}  // namespace wj_slam
#    define WJSLAM_Location(T, P) template class wj_slam::Location<T, P>;
#endif