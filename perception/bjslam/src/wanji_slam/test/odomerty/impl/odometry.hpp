/*
 * @Description:
 * @Version: 1.0
 * @Autor: zushuang
 * @Date: 2021-07-14 19:04:55
 * @LastEditors: shuangquan han
 * @LastEditTime: 2023-11-28 16:51:09
 */
#pragma once
#ifndef _ODOMETRY_HPP_
#    define _ODOMETRY_HPP_
#    include "../odometry.h"

#    include "common/time_consuming.hpp"
namespace wj_slam {
template <typename P> void Odometry<P>::paramReset_()
{
    if (PoseStatus::SettingPose == c_stSysParam_->m_pos.m_stSetPose.m_bFlag)  // 设置位姿
    {
        c_stIncreaseOdom_ = c_stSysParam_->m_pos.m_stSetPose;
        c_stIncreaseOdomLast_ = c_stIncreaseOdom_;
    }
    else
    {
        c_stIncreaseOdom_.reset();
        c_stIncreaseOdomLast_.reset();
    }
    c_stOdomDev_.reset();
    c_stCurSpeed_.reset();
    c_stIncreaseOpt_.reset();
    c_stLastIMUData_.reset();
    c_pFeatureMap_ = nullptr;
    c_bSysHasInit = false;
    c_bHasLocationDone_ = true;
    c_bAddKeyFrame_ = true;
    c_bRun_ = false;
    c_KFBuf_.clear();
    c_vDisplay_.clear();
    c_pCheckKeyFrame_.reset(new IsKeyFrame(c_stIncreaseOdom_, c_stCurSpeed_));
    c_pCheckKeyFrame_->setKFRenewTime(c_stSysParam_->m_map.m_iMapKFTimeStep);
    c_pCheckKeyFrame_->setKFRenewDist(c_stSysParam_->m_map.m_fMapKFDistStep);
    c_pCheckKeyFrame_->setKFRenewAng(c_stSysParam_->m_map.m_fMapKFAnglStep);
    clearLidarOdom_();
}
template <typename P> void Odometry<P>::paramInit_()
{
    c_bOdomRun_ = false;
    c_bOdomRunOver_ = true;
    c_bShutDown_ = false;
    c_bShutDownOver_ = false;

    c_stIncrease_.reset();
    c_stHPrecsOdomCur_.reset();
    c_stHPrecsOdomLast_.reset();
    c_stHPrecsOdomPredict_.reset();
    c_stHPrecsPose_.reset();
    c_stIncreaseHpDev_.reset();

    c_pCurrFeature_ = nullptr;
    c_pMatcher_->setMaxIterations(1);  // 设置迭代次数
    c_pMatcher_->setMatch2D(true);     // 设置角点2D匹配功能
    c_pMatcher_->setOptimizeModel(0);  // 匹配模块使用Kdtree还是iVox 0:Kdtree 1:iVox
    // 设置点到线、面距离阈值
    c_pMatcher_->setDistThreshold(c_stSysParam_->m_odom.m_match.m_fMaxDist);
    //设置靶标搜索半径
    c_pMatcher_->setMarkSearchR(c_stSysParam_->m_odom.m_match.m_fLine2DMarkRadius);
    // 设置角点2D搜索半径
    c_pMatcher_->setCornerSearchR(c_stSysParam_->m_odom.m_match.m_fLine2DRadius);
    // 设置角点2D匹配有效高差范围
    c_pMatcher_->setZAxisThreshold(c_stSysParam_->m_odom.m_match.m_fLineMaxZDiff);
    // 设置角点2D匹配最小点数
    c_pMatcher_->setSearchNumThreshold(c_stSysParam_->m_odom.m_match.m_uiLineMinPoints);
    // 设置面点k近邻搜索
    c_pMatcher_->setSearchK(c_stSysParam_->m_odom.m_match.m_uiPlaneMaxPoints);
    // 设置面点搜索到的N点的最大距离差
    c_pMatcher_->setRadiusThreshold(c_stSysParam_->m_odom.m_match.m_fPlaneMaxRadius);
    // 设置点到线、面距离阈值
    c_pMatcher_->setSlamModel(c_stSysParam_->m_iSlamMode);
    // 设置靶标权重最小值与最大值
    c_pMatcher_->setMarkWight(c_stSysParam_->m_bMarkWeightMin, c_stSysParam_->m_bMarkWeightMax);
    c_bUseIMU_ = c_stSysParam_->m_bIsUseIMU;
    c_fImuQuatPrec_ = c_stSysParam_->m_odom.m_fImuQuatPrec;
}
template <typename P> void Odometry<P>::corretPC_(s_POSE6D p_stPose, FEATURE_PAIR_PTR& p_pcInput)
{
    // 点云时间段T_sp内的的增量为 V[1/100ms] * (T_sp[ms] / 100[ms])
    s_TWIST l_sPoseTimeSpan = p_stPose * (p_pcInput->m_dTimespan / SCAN_TIME_MS);
    // 如果要畸变修正到帧尾，则l_sPoseTimeSpan需要转换成l_sPoseTimeSpan.inverse()
    c_pMatcher_->transformCloudSlerp(Eigen::Quaterniond::Identity(),
                                     Eigen::Vector3d::Zero(),
                                     l_sPoseTimeSpan.m_quat,
                                     l_sPoseTimeSpan.m_trans,
                                     p_pcInput->first,
                                     p_pcInput->first,
                                     p_pcInput->m_dTimespan);
    c_pMatcher_->transformCloudSlerp(Eigen::Quaterniond::Identity(),
                                     Eigen::Vector3d::Zero(),
                                     l_sPoseTimeSpan.m_quat,
                                     l_sPoseTimeSpan.m_trans,
                                     p_pcInput->second,
                                     p_pcInput->second,
                                     p_pcInput->m_dTimespan);
}
template <typename P> void Odometry<P>::finishCorretPC_(FEATURE_PAIR_PTR& p_pcInput)
{
    // 点云时间段T_sp内的的增量为 V[1/100ms] * (T_sp[ms] / 100[ms])
    s_TWIST l_sPoseTimeSpan = c_stCurSpeed_ * (p_pcInput->m_dTimespan / SCAN_TIME_MS);
    c_pMatcher_->transformCloudSlerp(Eigen::Quaterniond::Identity(),
                                     Eigen::Vector3d::Zero(),
                                     l_sPoseTimeSpan.m_quat,
                                     l_sPoseTimeSpan.m_trans,
                                     p_pcInput->third,
                                     p_pcInput->third,
                                     p_pcInput->m_dTimespan);
    c_pMatcher_->transformCloudSlerp(Eigen::Quaterniond::Identity(),
                                     Eigen::Vector3d::Zero(),
                                     l_sPoseTimeSpan.m_quat,
                                     l_sPoseTimeSpan.m_trans,
                                     p_pcInput->allPC,
                                     p_pcInput->allPC,
                                     p_pcInput->m_dTimespan);
    c_pMatcher_->transformCloudSlerp(Eigen::Quaterniond::Identity(),
                                     Eigen::Vector3d::Zero(),
                                     l_sPoseTimeSpan.m_quat,
                                     l_sPoseTimeSpan.m_trans,
                                     p_pcInput->fourth,
                                     p_pcInput->fourth,
                                     p_pcInput->m_dTimespan);
    //  定位模式不需要转移到时间尾
    if (c_bOnlyLocationMode_)
        return;
    // 转移到帧末尾-同步到时间戳
    // PoseDev l_stIncreaseInv = c_stCurSpeed_.inverse();
    // c_pMatcher_->transformCloudPoints(
    //     l_stIncreaseInv.m_quat, l_stIncreaseInv.m_trans, p_pcInput->allPC, p_pcInput->allPC);
    // c_pMatcher_->transformCloudPoints(
    //     l_stIncreaseInv.m_quat, l_stIncreaseInv.m_trans, p_pcInput->fourth, p_pcInput->fourth);
}
template <typename P> void Odometry<P>::setInput_(FEATURE_PAIR_PTR& p_pcInput)
{
    c_pMatcher_->setInputSourceCorner(p_pcInput->first);
    c_pMatcher_->setInputSourceSurface(p_pcInput->second);
    c_pMatcher_->setInputSourceMark(p_pcInput->third);
}
template <typename P> void Odometry<P>::renewMap_(PoseDev p_stTrans)
{
    //协同模式注意
    if (c_bOnlyLocationMode_)
        return;
    PoseDev l_stIncreaseInv = p_stTrans.inverse();
    //将特征转移至帧尾,用于匹配
    c_pMatcher_->transformCloudPoints(l_stIncreaseInv.m_quat,
                                      l_stIncreaseInv.m_trans,
                                      c_pFeatureMap_->first,
                                      c_pFeatureMap_->first);
    c_pMatcher_->transformCloudPoints(l_stIncreaseInv.m_quat,
                                      l_stIncreaseInv.m_trans,
                                      c_pFeatureMap_->second,
                                      c_pFeatureMap_->second);
    c_pMatcher_->transformCloudPoints(l_stIncreaseInv.m_quat,
                                      l_stIncreaseInv.m_trans,
                                      c_pFeatureMap_->third,
                                      c_pFeatureMap_->third);
    c_pFeatureMap_->m_tsSyncTime = c_timeCurr_;
    c_pFeatureMap_->m_tsWallTime = c_timeCurrLidarRecv_;
}
template <typename P> void Odometry<P>::setTarget_(FEATURE_PAIR_PTR& p_pcInput)
{
    if (c_bOnlyLocationMode_)
        return;

    renewMap_(c_stCurSpeed_ * c_fJumpNum_);
    c_pMatcher_->setInputTargetCorner(p_pcInput->first);
    c_pMatcher_->setInputTargetSurface(p_pcInput->second);
    c_pMatcher_->setInputTargetMark(p_pcInput->third);
}
template <typename P> void Odometry<P>::update_()
{
    // 更新速度&位姿
    renewOdom_();

    if (!c_bOnlyLocationMode_)
        c_pFeatureMap_ = c_pCurrFeature_;
    if (c_bSendPos)
    {
        std::vector<Pose> l_vPoseOut;
        l_vPoseOut.push_back(c_stIncreaseOdom_); // hsq 
        l_vPoseOut.push_back(c_stOdomDev_ * c_stHPrecsOdomCur_);
        c_bSendPos(l_vPoseOut);
    }
}
template <typename P> void Odometry<P>::renewOdom_()
{
    std::lock_guard<std::mutex> l_mtx(c_mtxOdomLock_);
    if (!c_bOnlyLocationMode_)
        c_stCurSpeed_ *= c_stIncreaseOpt_;  //建图模式下,根据优化结果更新速度
    c_stIncrease_ = c_stCurSpeed_ * c_fJumpNum_;
    c_stCurSpeed_.printf("odom speed\n");
    if ((abs(c_fJumpNum_) > 1.05) || (c_fJumpNum_ < -0.05))
        LOGO(WERROR,
             "{} [{}] renew odom jump {}ms",
             WJLog::getWholeSysTime(),
             c_scanIDCurr_,
             c_fJumpNum_ * SCAN_TIME_MS);
    if (abs(c_fJumpNum_) > 10)
    {
        c_stIncrease_ = c_stCurSpeed_;
        LOGO(WERROR, "{} jump too big reset twist", WJLog::getWholeSysTime());
    }

    //保证imu状态可用
    if (c_bUseIMU_ && (static_cast<int>(c_stCurIMUData_.status()) >= IMUData::IMUStatus::QuatValid)
        && (static_cast<int>(c_stLastIMUData_.status()) >= IMUData::IMUStatus::QuatValid))
    {
        Eigen::Quaterniond l_ImuIncreaseQuat;
        l_ImuIncreaseQuat = c_stLastIMUData_.quat().inverse() * c_stCurIMUData_.quat();
        if (std::fabs(l_ImuIncreaseQuat.dot(c_stIncrease_.m_quat)) < c_fImuQuatPrec_)
        {
            LOGO(WWARN,
                 "[{}] IMU and LidarOdom Quat is different, syncTime: {}, scanId: {}, "
                 "LidarOdomSpeed: "
                 "{:.6f}, {:.6f}, {:.6f}, IMUSpeed: {:.6f}, {:.6f}, {:.6f}",
                 WJLog::getWholeSysTime(),
                 c_timeCurr_,
                 c_scanIDCurr_,
                 c_stCurSpeed_.roll(),
                 c_stCurSpeed_.pitch(),
                 c_stCurSpeed_.yaw(),
                 c_stCurIMUData_.wx() / M_PI * 18,
                 c_stCurIMUData_.wy() / M_PI * 18,
                 c_stCurIMUData_.wz() / M_PI * 18);
            LOGO(WWARN,
                 "[{}] IMU and LidarOdom Quat data, syncTime: {}, scanId: {}, IMUQuat: {:.6f}, "
                 "{:.6f}, {:.6f}, {:.6f}, LidarOdomQuat: {:.6f}, {:.6f}, {:.6f}, {:.6f}, diff: "
                 "{:.6f}",
                 WJLog::getWholeSysTime(),
                 c_timeCurr_,
                 c_scanIDCurr_,
                 l_ImuIncreaseQuat.x(),
                 l_ImuIncreaseQuat.y(),
                 l_ImuIncreaseQuat.z(),
                 l_ImuIncreaseQuat.w(),
                 c_stIncrease_.m_quat.x(),
                 c_stIncrease_.m_quat.y(),
                 c_stIncrease_.m_quat.z(),
                 c_stIncrease_.m_quat.w(),
                 c_fImuQuatPrec_);
        }
        c_stIncrease_.setQuat(l_ImuIncreaseQuat);
    }

    c_stIncreaseOdom_ = c_stIncreaseOdomLast_ * c_stIncrease_;
    // 更新上一帧增量里程计
    c_stIncreaseOdomLast_ = c_stIncreaseOdom_;

    // 更新当前点云对应的高精度位姿（但点云马上会被更新到预测SCAN_TIME_MS后）
    c_stHPrecsOdomCur_ = c_stIncreaseOdom_;
    c_stHPrecsOdomPredict_ = c_stHPrecsOdomCur_;

    c_timeLast_ = c_timeCurr_;

    c_stLastIMUData_ = c_stCurIMUData_;
}
template <typename P> void Odometry<P>::addKeyFrame_()
{
    bool isrealKF = false;
    // 将除应用特征之外的其他点云转移到正确的坐标系
    finishCorretPC_(c_pCurrFeature_);
    if (c_bOnlyLocationMode_
        || c_pCheckKeyFrame_->isKeyFrame(
               hasEnoughMark_(c_pCurrFeature_), c_pCurrFeature_->m_tsSyncTime, isrealKF))
    {
        std::cout << "add kf\n";
        KEYFRAME_PTR l_pKeyFrame;
        l_pKeyFrame.reset(new KEYFRAME<P>());
        *(l_pKeyFrame->m_pFeature) = *c_pCurrFeature_;

        // 将当前位姿存入队列,用于location回调更新 Todom->Tmap: c_stOdomDev_
        if (!c_bOnlyLocationMode_)
            c_stOdomQueue_.push(std::make_pair(c_scanIDCurr_, c_stIncreaseOdom_));
        // 清空位姿标志
        if (PoseStatus::SettingPose == c_stSysParam_->m_pos.m_stSetPose.m_bFlag)
            c_stHPrecsOdomPredict_.m_bFlag = PoseStatus::SettingPose;
        else
            c_stHPrecsOdomPredict_.m_bFlag = PoseStatus::Default;

        // 将终点时间戳的点云从局部坐标系转移到全局坐标系
        l_pKeyFrame->m_Pose = c_stHPrecsOdomPredict_;
        if (isrealKF)
            l_pKeyFrame->m_bIsKeyFrame = true;
        else
            l_pKeyFrame->m_bIsKeyFrame = false;

        c_KFBuf_.push(l_pKeyFrame);
        if (c_pThread_)
            c_pThread_->resume();

        // s_TWIST imuTwist;
        // c_poseIMU_.m_Pose.setQuat(c_pCurrFeature_->m_imuData.quat());
        // c_poseIMU_.m_iTimetamp = c_pCurrFeature_->m_imuData.imuTime();
        // c_poseIMU_.m_iRecvTimestamp = c_pCurrFeature_->m_imuData.imuRecvTime();
        // c_poseIMU_.m_Twist = c_poseLastIMU_.m_Pose.inverse() * c_poseIMU_.m_Pose;
        // c_poseLastIMU_ = c_poseIMU_;
    }
}
template <typename P> void Odometry<P>::optimiz_()
{
    c_pMatcher_->setTransform(Eigen::Quaterniond::Identity(), Eigen::Vector3d::Zero());

    // std::cout << "hsq: odometry.hpp optimiz_() 调用laserRegistration.hpp align()" << std::endl;

    c_pMatcher_->align(c_stIncreaseOpt_.m_quat, c_stIncreaseOpt_.m_trans, 0, c_scanIDCurr_);
    std::cout << "hsq: odometry.hpp optimiz_(),  score = " << c_pMatcher_->getResultScore() << std::endl;
    if (c_stSysParam_->m_odom.optimiz_o3D)
    {
        c_stIncreaseOpt_.m_trans.z() = 0;
        c_stIncreaseOpt_.setQuat(c_stIncreaseOpt_.m_quat);
        c_stIncreaseOpt_.setRPY(0, 0, c_stIncreaseOpt_.yaw());
    }
    // 优化完成后,(根据优化增量修正速度),同样使用增量补充修正点云
    // 点云时间段T_sp内的的增量为 V[1/100ms] * (T_sp[ms] / 100[ms])
    corretPC_(c_stIncreaseOpt_, c_pCurrFeature_);
}
template <typename P> void Odometry<P>::clearFeatureBuf2Lastest_(void)
{
    uint l_uiCnt = c_featureBuf_.size() - 1;
    while (l_uiCnt--)
    {
        FEATURE_PAIR_PTR l_fe = c_featureBuf_.front();
        LOGO(WWARN,
             "{} Pop 帧[{}] StampT {} RecvT {}",
             WJLog::getWholeSysTime(),
             l_fe->m_uiScanFrame,
             l_fe->m_tsSyncTime,
             l_fe->m_tsWallTime);
        c_featureBuf_.pop();
    }
    return;
}
template <typename P> void Odometry<P>::clearFeatureBuf_(void)
{
    while (!c_featureBuf_.empty())
    {
        FEATURE_PAIR_PTR l_fe = c_featureBuf_.front();
        if (c_stSysParam_->m_iWorkMode != WorkMode::StandByMode)
            LOGO(WWARN,
                 "{} Pop 帧[{}] StampT {} By Shutdown or Outtime",
                 WJLog::getWholeSysTime(),
                 l_fe->m_uiScanFrame,
                 l_fe->m_tsSyncTime);
        c_featureBuf_.pop();
    }
}
template <typename P> bool Odometry<P>::hasPointCloud_()
{
    if (c_featureBuf_.empty())
        return false;
    if (c_bShutDown_ || !c_bOdomRun_)
    {
        clearFeatureBuf_();
        return false;
    }
    if (c_bOnlyLocationMode_)
    {
        // 定位模块返回高精Pose后pop至最新帧
        if (c_bHasLocationDone_)
        {
            clearFeatureBuf2Lastest_();
            return true;
        }
        else
            return false;
    }
    return !c_featureBuf_.empty();
}
template <typename P>
bool Odometry<P>::location_(FEATURE_PAIR_PTR& p_pCurrFeature, FEATURE_PAIR_PTR& p_Map)
{
    if (c_bOnlyLocationMode_)
        return true;
    bool isSucess = false;
    OptAlgoType l_algoType;
    l_algoType = selectAlgoType_(p_pCurrFeature, p_Map);
    if (c_bSysHasInit)
        while (1)
        {
            switch (l_algoType)
            {
                case OptAlgoType::useMark:
                    /* code */
                    if (!isSucess)
                        l_algoType = OptAlgoType::useFeature;
                    break;
                case OptAlgoType::useFeature:
                    setInput_(p_pCurrFeature);  // 设置输入点云
                    optimiz_();                 // 优化
                    isSucess = true;
                    break;
                default: break;
            }
            if (isSucess)
                break;
        }
    return true;
}
template <typename P> bool Odometry<P>::hasEnoughMark_(FEATURE_PAIR_PTR& p_pFeature)
{
    return false;
    // return p_pFeature->markSize() >= MinMarkSize;
}
template <typename P> float Odometry<P>::getJumpNum_()
{
    return (c_timeCurr_ - c_timeLast_) / SCAN_TIME_MS;
}
template <typename P> void Odometry<P>::clearLidarOdom_()
{
    while (!c_stOdomQueue_.empty())
    {
        c_stOdomQueue_.pop();
    }
}
template <typename P> typename Odometry<P>::Pose Odometry<P>::findLidarOdomById_(const int p_scanId)
{
    Pose l_odom;
    bool res = false;
    int size = c_stOdomQueue_.size();
    while (!c_stOdomQueue_.empty())
    {
        std::pair<int, Pose> odomTep = c_stOdomQueue_.front();
        c_stOdomQueue_.pop();
        if (odomTep.first == p_scanId)
        {
            l_odom = odomTep.second;
            res = true;
            break;
        }
    }
    assert(res == true);
    return l_odom;
}
template <typename P> void Odometry<P>::renewPrecisionPose(s_PoseWithTwist& p_stPoseGlobal)
{
    std::lock_guard<std::mutex> l_mtx(c_mtxOdomLock_);
    if (c_bOnlyLocationMode_)
    {
        // 定位模式下 非StopPose状态均可更新速度和位姿
        if (p_stPoseGlobal.m_bFlag == PoseStatus::ContinuePose
            || p_stPoseGlobal.m_bFlag == PoseStatus::VirtualPose
            || p_stPoseGlobal.m_bFlag == PoseStatus::CurbPose)
        {
            c_stHPrecsPose_ = p_stPoseGlobal.m_Pose;
            c_stOdomDev_ = c_stHPrecsPose_ * c_stIncreaseOdom_.inverse();  // 更新全局差值
            speedSmooth(p_stPoseGlobal.m_Twist, c_stCurSpeed_);
        }
        else
            LOGO(WERROR,
                 "{} Update Odomdev Fail Flag [{}] Error",
                 WJLog::getWholeSysTime(),
                 p_stPoseGlobal.m_bFlag);
        c_scanIDLast_ = p_stPoseGlobal.m_iScanId;
    }
    else
    {
        s_PoseWithTwist l_delta;
        l_delta = p_stPoseGlobal;
        l_delta.m_Twist = c_stHPrecsPose_.inverse() * p_stPoseGlobal.m_Pose;

        // 建图模式下 仅Continue状态更新Odomdev
        if (p_stPoseGlobal.m_bFlag == PoseStatus::ContinuePose)
        {
            c_stHPrecsPose_ = p_stPoseGlobal.m_Pose;
            Pose odom = findLidarOdomById_(p_stPoseGlobal.m_iScanId);
            c_stOdomDev_ = c_stHPrecsPose_ * odom.inverse();  // 更新全局差值
        }
        else
            LOGO(WERROR,
                 "{} Update Odomdev Fail Flag [{}] Not Continue",
                 WJLog::getWholeSysTime(),
                 p_stPoseGlobal.m_bFlag);
    }
    c_bHasLocationDone_ = true;
    c_bAddKeyFrame_ = true;  //下一关键帧发送标志

    if (c_stSysParam_->m_fae.m_bPrintfTimeLog)
        LOGO(WINFO,
             "{} Locat Renew 帧[{}] StampT {} RecvT {}",
             WJLog::getWholeSysTime(),
             p_stPoseGlobal.m_iScanId,
             p_stPoseGlobal.m_tsSyncTime,
             p_stPoseGlobal.m_tsWallTime);
}

template <typename P> void Odometry<P>::outputKeyFrame_()
{
    static double l_dTranstime = 0, l_dTranstimeper = 0;
    static int l_iTranscnt = 0;
    while (c_bRun_)
    {
        if (c_KFBuf_.empty())
        {
            //若队列中没有数据,则暂停线程避免资源浪费
            c_pThread_->pause();
            c_pThread_->wait_pause();
        }
        if (!c_bRun_)
            break;
        if (c_bAddKeyFrame_)
        {
            //防止假唤醒,判断一次队列是否空
            if (!c_KFBuf_.empty())
            {
                KEYFRAME_PTR l_pKeyFrame;
                l_pKeyFrame.reset(new KEYFRAME<P>());
                l_pKeyFrame = c_KFBuf_.front();
                c_KFBuf_.pop();
                l_pKeyFrame->m_Pose = c_stOdomDev_ * l_pKeyFrame->m_Pose;
                TicToc tickey;
                c_pMatcher_->transformCloudPoints(l_pKeyFrame->m_Pose.m_quat,
                                                  l_pKeyFrame->m_Pose.m_trans,
                                                  l_pKeyFrame->m_pFeature->first,
                                                  l_pKeyFrame->m_pFeature->first);
                c_pMatcher_->transformCloudPoints(l_pKeyFrame->m_Pose.m_quat,
                                                  l_pKeyFrame->m_Pose.m_trans,
                                                  l_pKeyFrame->m_pFeature->second,
                                                  l_pKeyFrame->m_pFeature->second);
                c_pMatcher_->transformCloudPoints(l_pKeyFrame->m_Pose.m_quat,
                                                  l_pKeyFrame->m_Pose.m_trans,
                                                  l_pKeyFrame->m_pFeature->third,
                                                  l_pKeyFrame->m_pFeature->third);
                c_pMatcher_->transformCloudPoints(l_pKeyFrame->m_Pose.m_quat,
                                                  l_pKeyFrame->m_Pose.m_trans,
                                                  l_pKeyFrame->m_pFeature->fourth,
                                                  l_pKeyFrame->m_pFeature->fourth);

                LOGO(WDEBUG,
                     "{} [OD] 帧[{}] Trans Cost Time {:.3f}",
                     WJLog::getWholeSysTime(),
                     l_pKeyFrame->m_pFeature->m_uiScanFrame,
                     tickey.toc());

                l_dTranstime += tickey.toc();
                l_iTranscnt++;

                int l_iSize =
                    l_pKeyFrame->m_pFeature->cornerSize() + l_pKeyFrame->m_pFeature->surfSize();

                l_dTranstimeper += tickey.toc() / l_iSize;
                LOGO(WDEBUG,
                     "{} [OD] 帧[{}] Aver Trans Per Cost Time {:.3f}",
                     WJLog::getWholeSysTime(),
                     c_scanIDCurr_,
                     (l_dTranstimeper / l_iTranscnt));
                std::cout << "publish kf\n";
                c_isKeyFrameCb_(l_pKeyFrame);
                c_bAddKeyFrame_ = false;
            }
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
}

template <typename P>
void Odometry<P>::speedSmooth(MoveSpeed& p_stNewSpeed, MoveSpeed& p_stLastSpeed)
{
    bool l_bIsSmooth = false;
    if (fabs(p_stNewSpeed.yaw()) < c_stSysParam_->m_loct.m_fTwistSmoothRes)
    {
        p_stLastSpeed = (p_stLastSpeed * p_stNewSpeed) / 2;
        l_bIsSmooth = true;
    }
    else
        p_stLastSpeed = p_stNewSpeed;
}

template <typename P> void Odometry<P>::setSpeed(MoveSpeed p_speed)
{
    c_stCurSpeed_ = p_speed;
}
template <typename P> void Odometry<P>::enableOnlyLocationMode()
{
    c_bOnlyLocationMode_ = true;
}
template <typename P> void Odometry<P>::disableOnlyLocationMode()
{
    c_bOnlyLocationMode_ = false;
}
template <typename P> void Odometry<P>::shutDown()
{
    c_bOdomRun_ = false;
    c_bShutDown_ = true;
    //退出回调线程
    c_bRun_ = false;
    //等待检测线程退出
    if (c_pThread_)
        c_pThread_->stop();
    c_pThread_ = nullptr;
    while (1)
    {
        if (c_bShutDownOver_)
            break;
        else
            usleep(1000);
    }
}
template <typename P> void Odometry<P>::start()
{
    paramReset_();        //加载参数
    c_bRun_ = true;       //开启发送线程标志
    c_pThread_->start();  //开启线程
    c_bOdomRun_ = true;
}
template <typename P> bool Odometry<P>::setWorkMode(int p_workMode)
{
    bool res = false;
    switch (p_workMode)
    {
        case WorkMode::StandByMode:
            this->stop();
            while (!this->isStop())
                sleepMs(10);
            paramReset_();
            res = true;
            break;
        case WorkMode::InitMapMode:
            this->stop();
            while (!this->isStop())
                sleepMs(10);
            this->disableOnlyLocationMode();
            this->start();
            res = true;
            break;
        case WorkMode::ContMapMode:
            this->stop();
            while (!this->isStop())
                sleepMs(10);
            this->disableOnlyLocationMode();
            this->start();
            res = true;
            break;
        case WorkMode::LocatMode:
            this->stop();
            while (!this->isStop())
                sleepMs(10);
            this->enableOnlyLocationMode();
            this->start();
            res = true;
            break;
        case WorkMode::UpdateMapMode:
            this->stop();
            while (!this->isStop())
                sleepMs(10);
            this->enableOnlyLocationMode();
            this->start();
            res = true;
            break;
        default: break;
    }

    return res;
}

template <typename P> void Odometry<P>::selectSpeed_()
{
    if (c_bUseIMU_)
    {
        //注意单位为100ms,注意旋转顺序
        /** @todo 此处的角速度咱时为瞬时变量,是否有必要滤波或平均,或用帧间增量代替*/
        if (c_stCurIMUData_.status() == IMUData::IMUStatus::AllValid)
            c_stCurSpeed_.setRPY(c_stCurIMUData_.wx() * 180 / M_PI / 10.0,
                                 c_stCurIMUData_.wy() * 180 / M_PI / 10.0,
                                 c_stCurIMUData_.wz() * 180 / M_PI / 10.0);
        else
            LOGW(WWARN,
                 "[{}] ScanId: {}. Get IMU Speed Error. Use Last Speed. yaw: {:.6f}, {}",
                 WJLog::getWholeSysTime(),
                 c_scanIDCurr_,
                 c_stCurSpeed_.yaw(),
                 c_stCurIMUData_.status());
    }
}
template <typename P> void Odometry<P>::run()
{
    static double l_dOdomtime = 0;
    static int l_iOdomcnt = 0;

    while (1)
    {
        if (hasPointCloud_())
        {
            c_bOdomRunOver_ = false;
            TicToc tic;
            // 更新当前信息(当前帧,ID,时间戳),当前时间戳速度假设为上次速度
            c_pCurrFeature_ = getCurrFeature_();
            c_timeCurr_ = c_pCurrFeature_->m_tsSyncTime;
            c_timeCurrLidarRecv_ = c_pCurrFeature_->m_tsWallTime;
            c_scanIDCurr_ = c_pCurrFeature_->m_uiScanFrame;
            c_stCurIMUData_ = c_pCurrFeature_->m_imuData;
            selectSpeed_();
            wjPrint(WJCOL_GREEN, "curID", c_scanIDCurr_);
            // 若未初始化,设置旧帧为当前帧
            if (!c_bSysHasInit)
            {
                c_scanIDLast_ = c_scanIDCurr_;
                c_timeLast_ = c_timeCurr_;
                //仅初始建图模式下,使用imu初始化
                if (c_bUseIMU_ && (WorkMode::InitMapMode == c_stSysParam_->m_iWorkMode)
                    && (static_cast<int>(c_stCurIMUData_.status())
                        >= static_cast<int>(IMUData::IMUStatus::QuatValid)))
                {
                    c_stIncreaseOdomLast_.setQuat(c_stCurIMUData_.quat());
                    LOGO(WINFO,
                         "[{}] Init SLAM Quat. roll: {:.6f}, pitch: {:.6f}, yaw: {:.6f}, {}",
                         WJLog::getWholeSysTime(),
                         c_stIncreaseOdomLast_.roll(),
                         c_stIncreaseOdomLast_.pitch(),
                         c_stIncreaseOdomLast_.yaw(),
                         c_stCurIMUData_.status());
                }
            }

            corretPC_(c_stCurSpeed_, c_pCurrFeature_);  // 点云畸变修正,修正至帧头
            // std::cout << "hsq: getCurrFeature_" << std::endl;
            // 定位时为0.99～1.01,建图时为-0.01～0.01
            c_fJumpNum_ = getJumpNum_();
            std::cout << "c_fJumpNum_ : " << c_fJumpNum_ << std::endl;
            // 补充修正时间，定位时实际没用
            if (c_bSysHasInit)
                setTarget_(c_pFeatureMap_);  // 设置地图

            // 使用c_timeCurr_的c_pFeatureMap_和c_timeCurr_的c_pCurrFeature_进行匹配优化
            location_(c_pCurrFeature_, c_pFeatureMap_);
            // std::cout << "hsq: location_" << std::endl;

            // 使用优化增量修正当前速度,估计当前位姿
            // 将c_pCurrFeature_转移到预计的下次时间点
            update_();
            // std::cout << "hsq: update_" << std::endl;
            // 打包预估位姿和帧发布
            addKeyFrame_();
            l_iOdomcnt++;
            l_dOdomtime += tic.toc();
            COST_TIME_ODOM(tic.toc());
            LOGO(WDEBUG,
                 "{} [OD] 帧[{}] Odom Cost Time {:.3f}",
                 WJLog::getWholeSysTime(),
                 c_scanIDCurr_,
                 tic.toc());
            LOGO(WDEBUG,
                 "{} [OD] 帧[{}] Aver Odom Cost Time {:.3f}",
                 WJLog::getWholeSysTime(),
                 c_scanIDCurr_,
                 (l_dOdomtime / l_iOdomcnt));

            if (c_bSendPC)
            {
                c_vDisplay_.clear();
                KEYFRAME_PTR l_pKeyOut(new KEYFRAME<P>());
                l_pKeyOut->m_Pose = c_stOdomDev_ * c_stHPrecsOdomCur_;
                *l_pKeyOut->m_pFeature->allPC = *c_pCurrFeature_->allPC;
                c_vDisplay_.push_back(l_pKeyOut);
                c_bSendPC(c_vDisplay_);  // 向外发送点云
            }
            c_bSysHasInit = true;
            c_bOdomRunOver_ = true;
        }
        if (c_bShutDown_)
            break;
        else
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
    printf("exit LIOdometry\n");
    c_bShutDownOver_ = true;
}
template <typename P> void Odometry<P>::stop()
{
    //退出回调线程
    c_bRun_ = false;
    //等待检测线程退出
    if (c_pThread_)
        c_pThread_->stop();
    c_bOdomRun_ = false;
}
template <typename P> bool Odometry<P>::isStop()
{
    if ((false == c_bOdomRun_) && (true == c_bOdomRunOver_))
        return true;
    return false;
}
template <typename P>
Odometry<P>::Odometry(Queue<FEATURE_PAIR_PTR>& p_feature,
                      boost::function<void(KEYFRAME_PTR)> p_keyFrameCb,
                      int timeOffset,
                      int ringOffset,
                      boost::function<void(std::vector<Pose>)> sendpos,
                      boost::function<void(std::vector<KEYFRAME_PTR>)> sendPc)
    : c_isKeyFrameCb_(p_keyFrameCb), c_featureBuf_(p_feature), c_bSendPos(sendpos),
      c_bSendPC(sendPc), c_pThread_(nullptr), c_bAddKeyFrame_(true), c_bRun_(false)
{
    c_stSysParam_ = SYSPARAM::getIn();
    c_pMatcher_.reset(new LaserRegistration<P, P>());
    c_pThread_.reset(new thread(boost::bind(&Odometry<P>::outputKeyFrame_, this)));
    if (timeOffset != -1)
        c_pMatcher_->setTimeOffset(timeOffset);
    if (ringOffset != -1)
        c_pMatcher_->setRingOffset(ringOffset);
    paramInit_();
    setWorkMode(c_stSysParam_->m_iWorkMode);
}
template <typename P> Odometry<P>::~Odometry()
{
    std::cout << "exit odom" << std::endl;
    //退出回调线程
    c_bRun_ = false;
    //等待检测线程退出
    if (c_pThread_)
        c_pThread_->stop();
    c_pThread_ = nullptr;
    clearLidarOdom_();
    c_pCurrFeature_ = nullptr;
    c_pFeatureMap_ = nullptr;
    c_pCheckKeyFrame_ = nullptr;
    c_pMatcher_ = nullptr;
}
}  // namespace wj_slam
#    define INSTANTIATE_Odometry(P) template class wj_slam::Odometry<P>;
#endif