/*
 * @Description:
 * @Version: 1.0
 * @Autor: zushuang
 * @Date: 2021-06-23 21:56:51
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2023-07-12 13:09:41
 */

#include "common/common_master.h"
#include "tool/fileTool/fileTool.h"
#include <fstream>
#include <iostream>
#include <sys/time.h>
#include <yaml-cpp/yaml.h>
// #include "../gtest/gtest.h"
#pragma once
namespace wj_slam {
class Param {
  private:
    YAML::Node c_config;
    std::string configFile = "";
    s_masterCfg* c_MasterCfg_;
    using vFloat = std::vector<float>;
    bool c_bLogValid_ = false;
    bool checkYaml_(std::string p_sYamlPath)
    {
        YAML::Node l_config;
        try
        {
            l_config = YAML::LoadFile(p_sYamlPath);
            return true;
        }
        catch (YAML::BadFile& e)
        {
            // wjPrintError("Yaml [%s] Not Exist or Fail", p_sYamlPath.c_str());
            return false;
        }
    }

    template <typename T> T loadParam_(std::string name)
    {
        if (c_config["LaserConfig"][name])
        {
            return c_config["LaserConfig"][name].as<T>();
        }
        return NULL;
    }
    template <typename T> bool loadParam_(const YAML::Node& node, T& out)
    {
        if (node)
        {
            out = node.as<T>();
            return true;
        }
        return false;
    }
    template <typename T> bool loadParam_(std::string name, T& out)
    {
        if (c_config["LaserConfig"][name])
        {
            out = c_config["LaserConfig"][name].as<T>();
            return true;
        }
        return false;
    }
    template <typename T> bool loadParam_(std::string dict, std::string name, T& out)
    {
        if (c_config["LaserConfig"][dict][name])
        {
            out = c_config["LaserConfig"][dict][name].as<T>();
            return true;
        }
        return false;
    }
    template <typename T>
    bool loadParam_(std::string dict, std::string dict2, std::string name, T& out)
    {
        if (c_config["LaserConfig"][dict][dict2][name])
        {
            out = c_config["LaserConfig"][dict][dict2][name].as<T>();
            return true;
        }
        return false;
    }
    template <typename T>
    bool
    loadParam_(std::string dict, std::string dict2, std::string dict3, std::string name, T& out)
    {
        if (c_config["LaserConfig"][dict][dict2][dict3][name])
        {
            out = c_config["LaserConfig"][dict][dict2][dict3][name].as<T>();
            return true;
        }
        return false;
    }
    template <typename T> void saveParam_(YAML::Node& node, const T& in)
    {
        node = in;
    }
    template <typename T> void saveParam_(std::string name, const T& in)
    {
        if (c_config["LaserConfig"][name])
            c_config["LaserConfig"][name] = in;
    }
    template <typename T> void saveParam_(std::string dict, std::string name, const T& in)
    {
        if (c_config["LaserConfig"][dict][name])
            c_config["LaserConfig"][dict][name] = in;
    }
    template <typename T>
    void saveParam_(std::string dict, std::string dict2, std::string name, const T& in)
    {
        if (c_config["LaserConfig"][dict][dict2][name])
            c_config["LaserConfig"][dict][dict2][name] = in;
    }
    template <typename T>
    void saveParam_(std::string dict,
                    std::string dict2,
                    std::string dict3,
                    std::string name,
                    const T& in)
    {
        if (c_config["LaserConfig"][dict][dict2][dict3][name])
            c_config["LaserConfig"][dict][dict2][dict3][name] = in;
    }

    template <typename T> void addParam_(YAML::Node& node, const T& in)
    {
        node = in;
    }
    template <typename T> void addParam_(std::string name, const T& in)
    {
        c_config["LaserConfig"][name] = in;
    }
    template <typename T> void addParam_(std::string dict, std::string name, const T& in)
    {
        c_config["LaserConfig"][dict][name] = in;
    }
    template <typename T>
    void addParam_(std::string dict, std::string dict2, std::string name, const T& in)
    {
        c_config["LaserConfig"][dict][dict2][name] = in;
    }
    template <typename T>
    void
    addParam_(std::string dict, std::string dict2, std::string dict3, std::string name, const T& in)
    {
        c_config["LaserConfig"][dict][dict2][dict3][name] = in;
    }
    template <typename T> void saveParamFormat_(YAML::Node& node, const T& in)
    {
        node = in;
        node.SetStyle(YAML::EmitterStyle::Flow);
    }
    template <typename T> void saveParamFormat_(std::string name, const T& in)
    {
        if (c_config["LaserConfig"][name])
        {
            c_config["LaserConfig"][name] = in;
            c_config["LaserConfig"][name].SetStyle(YAML::EmitterStyle::Flow);
        }
    }
    template <typename T> void saveParamFormat_(std::string dict, std::string name, const T& in)
    {
        if (c_config["LaserConfig"][dict][name])
        {
            c_config["LaserConfig"][dict][name] = in;
            c_config["LaserConfig"][dict][name].SetStyle(YAML::EmitterStyle::Flow);
        }
    }
    template <typename T>
    void saveParamFormat_(std::string dict, std::string dict2, std::string name, const T& in)
    {
        if (c_config["LaserConfig"][dict][dict2][name])
        {
            c_config["LaserConfig"][dict][dict2][name] = in;
            c_config["LaserConfig"][dict][dict2][name].SetStyle(YAML::EmitterStyle::Flow);
        }
    }
    template <typename T>
    void saveParamFormat_(std::string dict,
                          std::string dict2,
                          std::string dict3,
                          std::string name,
                          const T& in)
    {
        if (c_config["LaserConfig"][dict][dict2][dict3][name])
        {
            c_config["LaserConfig"][dict][dict2][dict3][name] = in;
            c_config["LaserConfig"][dict][dict2][dict3][name].SetStyle(YAML::EmitterStyle::Flow);
        }
    }

    template <typename T> void addParamFormat_(YAML::Node& node, const T& in)
    {
        node = in;
        node.SetStyle(YAML::EmitterStyle::Flow);
    }
    template <typename T> void addParamFormat_(std::string name, const T& in)
    {
        c_config["LaserConfig"][name] = in;
        c_config["LaserConfig"][name].SetStyle(YAML::EmitterStyle::Flow);
    }
    template <typename T> void addParamFormat_(std::string dict, std::string name, const T& in)
    {
        c_config["LaserConfig"][dict][name] = in;
        c_config["LaserConfig"][dict][name].SetStyle(YAML::EmitterStyle::Flow);
    }
    template <typename T>
    void addParamFormat_(std::string dict, std::string dict2, std::string name, const T& in)
    {
        c_config["LaserConfig"][dict][dict2][name] = in;
        c_config["LaserConfig"][dict][dict2][name].SetStyle(YAML::EmitterStyle::Flow);
    }
    template <typename T>
    void addParamFormat_(std::string dict,
                         std::string dict2,
                         std::string dict3,
                         std::string name,
                         const T& in)
    {
        c_config["LaserConfig"][dict][dict2][dict3][name] = in;
        c_config["LaserConfig"][dict][dict2][dict3][name].SetStyle(YAML::EmitterStyle::Flow);
    }

    template <typename T, typename M> bool faeLog(M p_level, std::string p_sStr, T p_Data = NULL)
    {
        if (!c_bLogValid_)
            return false;
        if (p_Data)
            LOGFAE(p_level, p_sStr.c_str(), p_Data);
        else
            LOGFAE(p_level, p_sStr.c_str());
        return true;
    }

  public:
    /**
     * @description: 检查出厂配置是否存在且未损坏
     * @param {*}
     * @return {*} true 文件有效  false 文件异常
     */
    bool checkDefaultYaml()
    {
        return checkYaml_(c_MasterCfg_->m_slam->m_sPkgPath + "/config/default.yaml");
    }

    /**
     * @description: 检查参数配置文件是否存在 且 未损坏
     * @param {*}
     * @return {*} 1: 正常 0 不存在 -1 损坏
     */
    int checkParamYaml(std::string p_sYamlPath)
    {
        if (!isExistFileOrFolder(p_sYamlPath))
            return 0;
        if (checkYaml_(p_sYamlPath))
            return 1;
        else
            return -1;
    }

    /**
     * @description: 加载默认值
     * @param {string} p_sPkgPath
     * @return {*} 此函数功能：加载必要参数-保证在没有任何Yaml存在时 程序可正常运作
     */
    void initParam_(std::string p_sPkgPath)
    {
#pragma region "时间戳初始化相关"
        // 设置系统启动时间
        c_MasterCfg_->m_slam->m_time.setSystemBaseTime();
#pragma endregion

#pragma region "模式相关"

#pragma endregion

#pragma region "其他相关"
        c_MasterCfg_->m_slam->m_sPkgPath = p_sPkgPath;
        c_MasterCfg_->m_slam->m_sVersion = (std::string)SLAM_VERSION;
        c_MasterCfg_->m_slam->m_fae.m_sLogPath =
            c_MasterCfg_->m_slam->m_sPkgPath + "/data/Log/Logfiles/";
#pragma endregion

#pragma region "雷达相关"
        // 默认1个雷达
        c_MasterCfg_->m_slam->m_iLidarNum = 1;
        c_MasterCfg_->m_slam->m_lidar.resize(c_MasterCfg_->m_slam->m_iLidarNum);
        setLaserFrameInfo(c_MasterCfg_->m_slam->m_lidar[0]);
        setDataMinLen(c_MasterCfg_->m_slam->m_lidar[0].m_dev);
        c_MasterCfg_->m_slam->m_lidar[0].m_sCalibrationFile =
            c_MasterCfg_->m_slam->m_sPkgPath + "/data/Wanji_lidar_16/"
            + c_MasterCfg_->m_slam->m_lidar[0].m_dev.m_sDevType;
        c_MasterCfg_->m_slam->m_lidar[0].m_bIsBaseLaser = true;
#pragma endregion

#pragma region "agv相关"
        setDataMinLen(c_MasterCfg_->m_slam->m_agv.m_dev);
#pragma endregion

#pragma region "标定相关"
        /*位姿保存文件路径*/
        c_MasterCfg_->m_slam->m_calib.m_MLCalib.m_sProcessFile =
            c_MasterCfg_->m_slam->m_sPkgPath + "/data/Pose/calib.csv";
#pragma endregion

#pragma region "web相关"

#pragma endregion

#pragma region "Pos校验相关"

#pragma endregion

#pragma region "地图相关"
        c_MasterCfg_->m_slam->m_map.setModeDefault(c_MasterCfg_->m_slam->m_iWorkMode,
                                                   c_MasterCfg_->m_slam->m_nParam_mode);
#pragma endregion

#pragma region "位置相关"
        /*位姿保存文件路径*/
        c_MasterCfg_->m_slam->m_pos.m_sPoseFilePath =
            c_MasterCfg_->m_slam->m_sPkgPath + "/data/Pose/";
        //判断路径是否存在，若不存在，则创建
        if (access(c_MasterCfg_->m_slam->m_pos.m_sPoseFilePath.c_str(), F_OK) != 0)
        {
            if (mkdir(c_MasterCfg_->m_slam->m_pos.m_sPoseFilePath.c_str(), S_IRWXU) == -1)
            {
                faeLog(WERROR,
                       "{} 创建失败，请手动创建",
                       c_MasterCfg_->m_slam->m_pos.m_sPoseFilePath.c_str());
            }
        }
#pragma endregion

#pragma region "里程计相关"
        c_MasterCfg_->m_slam->m_odom.setModeDefault(c_MasterCfg_->m_slam->m_iWorkMode,
                                                    c_MasterCfg_->m_slam->m_nParam_mode);
#pragma endregion

#pragma region "定位相关"
        c_MasterCfg_->m_slam->m_loct.setModeDefault(c_MasterCfg_->m_slam->m_iWorkMode,
                                                    c_MasterCfg_->m_slam->m_nParam_mode);
#pragma endregion

#pragma region "建图相关"
        c_MasterCfg_->m_slam->m_slam.setModeDefault(c_MasterCfg_->m_slam->m_iWorkMode,
                                                    c_MasterCfg_->m_slam->m_nParam_mode);
#pragma endregion

#pragma region "设备相关"
        c_MasterCfg_->m_slam->m_devList.clear();
        for (uint32_t i = 0; i < c_MasterCfg_->m_slam->m_lidar.size(); i++)
        {
            c_MasterCfg_->m_slam->m_devList.push_back(&c_MasterCfg_->m_slam->m_lidar[i].m_dev);
            c_MasterCfg_->m_slam->m_devList[c_MasterCfg_->m_slam->m_devList.size() - 1]->m_uiId =
                c_MasterCfg_->m_slam->m_devList.size() - 1;
        }
        // agv客户端端口为任意端口 ，IP和服务器相同
        c_MasterCfg_->m_slam->m_agv.m_dev.m_uiDevPort = 0;
        c_MasterCfg_->m_slam->m_agv.m_dev.m_sDevIP = c_MasterCfg_->m_slam->m_agv.m_dev.m_sLocalIP;
        c_MasterCfg_->m_slam->m_devList.push_back(&c_MasterCfg_->m_slam->m_agv.m_dev);
        c_MasterCfg_->m_slam->m_devList[c_MasterCfg_->m_slam->m_devList.size() - 1]->m_uiId =
            c_MasterCfg_->m_slam->m_devList.size() - 1;
#pragma endregion

#pragma region "master其他相关参数"
        c_MasterCfg_->m_sOffLineDataPath =
            getDefaultLidarDataPath(c_MasterCfg_->m_slam->m_sPkgPath);
        if (!isExistFileOrFolder(c_MasterCfg_->m_sNetCfgPath))
            autoFindNetCfgYaml_(c_MasterCfg_->m_sNetCfgPath);
        // 默认不播放 重启SLAM须复位
        c_MasterCfg_->m_bIsStart = false;
        // 默认不启动避障
        c_MasterCfg_->m_bOpenObstacle = false;
#pragma endregion
    }

    /**
     * @description: 检查yaml中IP和驱动模式是否对应
     * @param {string} p_sPkgPath
     * @return {*}
     */
    bool checkYamlIP_()
    {
        int l_iIsLoNum_ = 0;  // lidar PC-IP Lidar-IP + agv Server-IP  = 2*lidarNum +1;
        for (int i = 0; i < (int)c_MasterCfg_->m_slam->m_lidar.size(); i++)
        {
            if (c_MasterCfg_->m_slam->m_lidar[i].m_dev.m_sLocalIP == "127.0.0.1")
                l_iIsLoNum_++;
            if (c_MasterCfg_->m_slam->m_lidar[i].m_dev.m_sDevIP == "127.0.0.1")
                l_iIsLoNum_++;
        }
        if (c_MasterCfg_->m_slam->m_agv.m_dev.m_sLocalIP == "127.0.0.1")
            l_iIsLoNum_++;

        // 在线模式,均不可为回环IP
        if (c_MasterCfg_->m_slam->m_bIsOnlineMode && l_iIsLoNum_ > 0)
            return false;
        // 离线模式,均须为回环IP
        if (!c_MasterCfg_->m_slam->m_bIsOnlineMode
            && l_iIsLoNum_ != (int)c_MasterCfg_->m_slam->m_lidar.size() * 2 + 1)
            return false;
        return true;
    }

    /**
     * @description: 检查yaml中端口和驱动模式是否对应
     * @param {string} p_sPkgPath
     * @return {*}
     */
    bool checkYamlPort_()
    {
        int l_iIsOfflinePortNum_ = 0;  // lidar PC-IP Lidar-IP + agv Server-IP  = 2*lidarNum +1;
        for (int i = 0; i < (int)c_MasterCfg_->m_slam->m_lidar.size(); i++)
        {
            if (c_MasterCfg_->m_slam->m_lidar[i].m_dev.m_uiDevPort >= 20000)
                l_iIsOfflinePortNum_++;
            if (c_MasterCfg_->m_slam->m_lidar[i].m_dev.m_uiLocalPort >= 20000)
                l_iIsOfflinePortNum_++;
        }
        if (c_MasterCfg_->m_slam->m_agv.m_dev.m_uiLocalPort >= 20000)
            l_iIsOfflinePortNum_++;

        // 在线模式,均不可为20000以上端口
        if (c_MasterCfg_->m_slam->m_bIsOnlineMode && l_iIsOfflinePortNum_ > 0)
            return false;
        // 离线模式,均须为20000以上端口
        if (!c_MasterCfg_->m_slam->m_bIsOnlineMode
            && l_iIsOfflinePortNum_ != (int)c_MasterCfg_->m_slam->m_lidar.size() * 2 + 1)
            return false;
        return true;
    }

    /**
     * @description: 读取 user / default 参数
     * @param {string} p_sPkgPath
     * @return {*}
     */
    void loadSysParam(std::string p_sPkgPath)
    {
        std::string l_sYamlPath = "";
        initParam_(p_sPkgPath);
        l_sYamlPath = p_sPkgPath + "/config/default.yaml";
        if (isExistFileOrFolder(l_sYamlPath))
        {
            if (loadSysParam_(l_sYamlPath))
            {
                if (!checkYamlIP_() || !checkYamlPort_())
                {
                    faeLog(WERROR, "默认参数配置文件异常 | IP/端口配置与驱动模式不对应!", NULL);
                    // c_MasterCfg_->m_slam->m_fae.setErrorCode("K1");
                }
                else
                    faeLog(WINFO, "默认参数配置加载成功!", NULL);
            }
            else
            {
                faeLog(WERROR,
                       "默认参数配置文件 [default.yaml] 加载失败 | 格式错误，请参照以下步骤检查：",
                       NULL);
                faeLog(WERROR, " *********************************************** ", NULL);
                faeLog(WERROR, " * 1. 进入/wanjislam/src/wanji_slam/config/文件夹是否存在 ", NULL);
                faeLog(WERROR, " * 2. 在config文件夹下确认default.yaml文件是否存在", NULL);
                faeLog(WERROR,
                       " * 3. 如果上述方法不能解决，请启动wireshark抓包，并联系万集开发人员。 ",
                       NULL);
                faeLog(WERROR, " * **********************************************", NULL);
                c_MasterCfg_->m_slam->m_fae.setErrorCode("K1");
                // c_MasterCfg_->m_slam->m_fae.setErrorCode("K2");
            }
        }
        else
        {
            faeLog(WERROR,
                   "默认参数配置加载失败 | 文件 [default.yaml] 不存在，请确认文件是否存在!",
                   NULL);
            // c_MasterCfg_->m_slam->m_fae.setErrorCode("K3");
            c_MasterCfg_->m_slam->m_fae.setErrorCode("K2");
        }
        l_sYamlPath = p_sPkgPath + "/config/user.yaml";
        if (isExistFileOrFolder(l_sYamlPath))
        {
            if (loadSysParam_(l_sYamlPath))
            {
                if (!checkYamlIP_() || !checkYamlPort_())
                {
                    faeLog(WERROR,
                           "用户参数配置文件异常 | "
                           "IP/端口配置与驱动模式不对应，请检查日志参数配置文件!",
                           NULL);
                    // c_MasterCfg_->m_slam->m_fae.setErrorCode("K4");
                }
                else
                    faeLog(WINFO, "用户参数配置加载成功!", NULL);
            }
            else
            {
                faeLog(WERROR,
                       "用户参数配置文件 [user.yaml] 加载失败 | 格式错误，请检查user文件!",
                       NULL);
                // c_MasterCfg_->m_slam->m_fae.setErrorCode("K5");
                c_MasterCfg_->m_slam->m_fae.setErrorCode("K3");
            }
        }
        else
            faeLog(WWARN, "用户参数配置文件 [user.yaml] 不存在,已忽略！", NULL);

        l_sYamlPath = p_sPkgPath + "/config/logCfg.yaml";
        if (isExistFileOrFolder(l_sYamlPath))
        {
            if (loadSysParam_(l_sYamlPath))
            {
                if (!checkYamlIP_() || !checkYamlPort_())
                {
                    faeLog(WERROR,
                           "用户日志参数配置文件异常 | "
                           "IP/端口配置与驱动模式不对应，请检查日志参数配置文件!",
                           NULL);
                    // c_MasterCfg_->m_slam->m_fae.setErrorCode("K6");
                }
                else
                    faeLog(WINFO, "用户日志参数配置加载成功!", NULL);
            }
            else
            {
                faeLog(WERROR,
                       "用户日志参数配置文件 [logCfg.yaml] 加载失败 | "
                       "格式错误，请检查日志参数配置文件是否正确!",
                       NULL);
                c_MasterCfg_->m_slam->m_fae.setErrorCode("K4");
                // c_MasterCfg_->m_slam->m_fae.setErrorCode("K7");
            }
        }
        else
            faeLog(WWARN, "用户日志参数配置文件 [logCfg.yaml] 不存在,已忽略！", NULL);
        /*保证Pose被设置*/
        if (c_MasterCfg_->m_slam->m_pos.m_stSetPose.m_bFlag != PoseStatus::SettingPose)
        {
            /* Yaml未配置的话，读取文件存储位姿*/
            if (c_MasterCfg_->m_slam->m_iWorkMode != WorkMode::InitMapMode)
                readPose_(c_MasterCfg_->m_slam->m_pos.m_stSetPose,
                          c_MasterCfg_->m_slam->m_sPkgPath);
        }
    }

    /**
     * @description: 读取 user / default 参数
     * @param {string} p_sPkgPath
     * @return {*}
     */
    bool loadSysParam_(std::string p_sYamlPath)
    {
        try
        {
            c_config = YAML::LoadFile(p_sYamlPath);

#pragma region "master相关参数"
            /*lidar雷达模式 */
            loadParam_<bool>("master", "online_mode", c_MasterCfg_->m_slam->m_bIsOnlineMode);
            // /*播包速度*/
            // loadParam_<uint32_t>("master", "play_bag_rate", c_MasterCfg_->m_uiPlayBagRate);
            /*离线数据路径*/
            std::string l_sDataPath = "";
            loadParam_<std::string>("master", "lidar_data_path", l_sDataPath);
            if (l_sDataPath != "")
            {
                c_MasterCfg_->m_sOffLineDataPath = l_sDataPath;
                if ('/' != l_sDataPath[l_sDataPath.length() - 1])
                    c_MasterCfg_->m_sOffLineDataPath += "/";
            }

            /*默认保存pcap*/
            loadParam_<bool>("master", "default_save_pcap", c_MasterCfg_->m_bDefaultSavePcap);
            /*录制网卡时间间隔*/
            loadParam_<uint32_t>("master", "record_time", c_MasterCfg_->m_uiRecordTimeInterval);
            /*录制PCAP数量*/
            loadParam_<uint32_t>("master", "maxNum_pcap", c_MasterCfg_->m_uiRecordMaxNum);
            if (!loadParam_<uint32_t>(
                    "master", "maxNum_agvpcap", c_MasterCfg_->m_uiRecordAGVMaxNum))
                c_MasterCfg_->m_uiRecordAGVMaxNum = c_MasterCfg_->m_uiRecordMaxNum;
            /*网络配置文件路径*/
            loadParam_<std::string>("master", "net_cfg_path", c_MasterCfg_->m_sNetCfgPath);
            if (!isExistFileOrFolder(c_MasterCfg_->m_sNetCfgPath))
                autoFindNetCfgYaml_(c_MasterCfg_->m_sNetCfgPath);
            /*启动避障*/
            loadParam_<bool>("master", "use_od", c_MasterCfg_->m_bOpenObstacle);
#pragma endregion

#pragma region "模式相关"
            /*工作模式 */
            if (loadParam_<int>("work_mode", c_MasterCfg_->m_slam->m_iWorkMode))
            {
                // 此处仅使用读取到的m_iWorkMode 和 默认的m_nParam_mode 加载参数 ，
                c_MasterCfg_->m_slam->m_map.setModeDefault(c_MasterCfg_->m_slam->m_iWorkMode,
                                                           c_MasterCfg_->m_slam->m_nParam_mode);
                c_MasterCfg_->m_slam->m_odom.setModeDefault(c_MasterCfg_->m_slam->m_iWorkMode,
                                                            c_MasterCfg_->m_slam->m_nParam_mode);
                c_MasterCfg_->m_slam->m_loct.setModeDefault(c_MasterCfg_->m_slam->m_iWorkMode,
                                                            c_MasterCfg_->m_slam->m_nParam_mode);
                c_MasterCfg_->m_slam->m_slam.setModeDefault(c_MasterCfg_->m_slam->m_iWorkMode,
                                                            c_MasterCfg_->m_slam->m_nParam_mode);
            }

            /*场景模式 */
            if (loadParam_<int>("param_mode", c_MasterCfg_->m_slam->m_nParam_mode))
            {
                // 此处将使用读取到的m_iWorkMode和m_nParam_mode 共同加载参数
                c_MasterCfg_->m_slam->m_map.setModeDefault(c_MasterCfg_->m_slam->m_iWorkMode,
                                                           c_MasterCfg_->m_slam->m_nParam_mode);
                c_MasterCfg_->m_slam->m_odom.setModeDefault(c_MasterCfg_->m_slam->m_iWorkMode,
                                                            c_MasterCfg_->m_slam->m_nParam_mode);
                c_MasterCfg_->m_slam->m_loct.setModeDefault(c_MasterCfg_->m_slam->m_iWorkMode,
                                                            c_MasterCfg_->m_slam->m_nParam_mode);
                c_MasterCfg_->m_slam->m_slam.setModeDefault(c_MasterCfg_->m_slam->m_iWorkMode,
                                                            c_MasterCfg_->m_slam->m_nParam_mode);
            }
            /*调试模式 */
            loadParam_<bool>("debugModel", c_MasterCfg_->m_slam->m_bDebugModel);
            bool l_bXYMovModel = false;
            loadParam_<bool>("XYMovingModel", l_bXYMovModel);
#pragma endregion

#pragma region "其他相关"
            /*可视化雷达/地图*/
            loadParam_<bool>("send_map", c_MasterCfg_->m_slam->m_bSendMap);
            loadParam_<bool>("send_point", c_MasterCfg_->m_slam->m_bSendCurPC);
            loadParam_<int>("view_mode", c_MasterCfg_->m_slam->m_iViewMode);
#pragma endregion

#pragma region "靶标相关"
            loadParam_<bool>("use_mark", c_MasterCfg_->m_slam->m_bIsUseMark);
            loadParam_<bool>("use_imu", c_MasterCfg_->m_slam->m_bIsUseIMU);
            loadParam_<int>("slam_model", c_MasterCfg_->m_slam->m_iSlamMode);
            loadParam_<int>("markWightMax", c_MasterCfg_->m_slam->m_bMarkWeightMax);
            loadParam_<int>("markWightMin", c_MasterCfg_->m_slam->m_bMarkWeightMin);
            loadParam_<int>("markRec_model", c_MasterCfg_->m_slam->m_iMarkRecModel);
            loadParam_<float>("mark_size", c_MasterCfg_->m_slam->m_fMarkSize);

#pragma endregion

#pragma region "雷达相关"
            YAML::Node l_lidarInfo = c_config["LaserConfig"]["lidarInfo"];
            if (l_lidarInfo.IsDefined())
            {
                // 确定有数据前清空
                if (l_lidarInfo.size())
                    c_MasterCfg_->m_slam->m_lidar.clear();
                for (auto it = l_lidarInfo.begin(); it != l_lidarInfo.end(); it++)
                {
                    // 雷达名称
                    std::string l_sName = it->first.as<std::string>();
                    // 不可有重复的雷达名
                    if (isNameRepeat_(l_sName, c_MasterCfg_->m_slam->m_lidar))
                    {
                        faeLog(WERROR,
                               "雷达配置异常 | 存在多个相同雷达名称: {}, 请检查雷达名称!",
                               l_sName.c_str());
                        // c_MasterCfg_->m_slam->m_fae.setErrorCode("K8");
                        continue;
                    }
                    // 增加一个空位
                    u_int l_uiSize = c_MasterCfg_->m_slam->m_lidar.size();
                    c_MasterCfg_->m_slam->m_lidar.resize(l_uiSize + 1);
                    // 雷达名称
                    c_MasterCfg_->m_slam->m_lidar[l_uiSize].m_sLaserName = l_sName;
                }

                // 雷达名
                std::string l_sID = "";
                c_MasterCfg_->m_slam->m_iLidarNum = c_MasterCfg_->m_slam->m_lidar.size() > 0
                                                        ? c_MasterCfg_->m_slam->m_lidar.size()
                                                        : 1;

                // 循环指向下一组雷达配置
                for (u_int l_iLd = 0; l_iLd < c_MasterCfg_->m_slam->m_lidar.size(); ++l_iLd)
                {
                    if (!c_MasterCfg_->m_slam->m_bIsOnlineMode)
                        c_MasterCfg_->m_slam->m_lidar[l_iLd].m_dev.setStatus(DevStatus::VIRTUALDEV);
                    else
                        c_MasterCfg_->m_slam->m_lidar[l_iLd].m_dev.setStatus(
                            DevStatus::WAITCONNECT);
                    // 雷达序列
                    l_sID = c_MasterCfg_->m_slam->m_lidar[l_iLd].m_sLaserName;

                    loadParam_<std::string>("lidarInfo",
                                            l_sID,
                                            "laser_type",
                                            c_MasterCfg_->m_slam->m_lidar[l_iLd].m_dev.m_sDevType);

                    setLaserFrameInfo(c_MasterCfg_->m_slam->m_lidar[l_iLd]);
                    setDataMinLen(c_MasterCfg_->m_slam->m_lidar[l_iLd].m_dev);

                    loadParam_<bool>("lidarInfo",
                                     l_sID,
                                     "calibration_online",
                                     c_MasterCfg_->m_slam->m_lidar[l_iLd].m_bGetCalibrationOnline);

                    loadParam_<bool>("lidarInfo",
                                     l_sID,
                                     "is_baseLaser",
                                     c_MasterCfg_->m_slam->m_lidar[l_iLd].m_bIsBaseLaser);

                    loadParam_<std::string>("lidarInfo",
                                            l_sID,
                                            "laser_sn",
                                            c_MasterCfg_->m_slam->m_lidar[l_iLd].m_sLaserSN);

                    loadParam_<std::string>("lidarInfo",
                                            l_sID,
                                            "net_name",
                                            c_MasterCfg_->m_slam->m_lidar[l_iLd].m_dev.m_sNetName);

                    /*雷达偏心垂直表存储文件夹名 后续可将不同型号雷达数据表放置于不同文件夹  eg
                     * /home/<USER>/wanji_slam/src/wanji_slam/src/data/Wanji_lidar_16/WLR720C    */
                    if (!loadParam_<std::string>(
                            "lidarInfo",
                            l_sID,
                            "calibration_file",
                            c_MasterCfg_->m_slam->m_lidar[l_iLd].m_sCalibrationFile))
                        c_MasterCfg_->m_slam->m_lidar[l_iLd].m_sCalibrationFile =
                            c_MasterCfg_->m_slam->m_sPkgPath + "/data/Wanji_lidar_16/"
                            + c_MasterCfg_->m_slam->m_lidar[l_iLd].m_dev.m_sDevType;
                    loadParam_<bool>("lidarInfo",
                                     l_sID,
                                     "laser_enable",
                                     c_MasterCfg_->m_slam->m_lidar[l_iLd].m_bEnable);
                    loadParam_<std::string>("lidarInfo",
                                            l_sID,
                                            "laser_ip",
                                            c_MasterCfg_->m_slam->m_lidar[l_iLd].m_dev.m_sDevIP);
                    loadParam_<std::string>("lidarInfo",
                                            l_sID,
                                            "pc_ip",
                                            c_MasterCfg_->m_slam->m_lidar[l_iLd].m_dev.m_sLocalIP);
                    loadParam_<std::string>(
                        "lidarInfo",
                        l_sID,
                        "multicast_ip",
                        c_MasterCfg_->m_slam->m_lidar[l_iLd].m_dev.m_sMulticastIP);
                    loadParam_<uint16_t>("lidarInfo",
                                         l_sID,
                                         "min_num",
                                         c_MasterCfg_->m_slam->m_lidar[l_iLd].m_uiPointMinNum);
                    loadParam_<uint32_t>("lidarInfo",
                                         l_sID,
                                         "min_pkt",
                                         c_MasterCfg_->m_slam->m_lidar[l_iLd].m_uiMinFramePkgNum);
                    loadParam_<uint32_t>("lidarInfo",
                                         l_sID,
                                         "frame_sec",
                                         c_MasterCfg_->m_slam->m_lidar[l_iLd].m_uiFrameTimeMax);
                    loadParam_<uint32_t>("lidarInfo",
                                         l_sID,
                                         "maxFrame_sec",
                                         c_MasterCfg_->m_slam->m_lidar[l_iLd].m_uiOldFrameTimeMax);
                    loadParam_<u_int16_t>("lidarInfo",
                                          l_sID,
                                          "laser_port",
                                          c_MasterCfg_->m_slam->m_lidar[l_iLd].m_dev.m_uiDevPort);
                    loadParam_<u_int16_t>("lidarInfo",
                                          l_sID,
                                          "pc_port",
                                          c_MasterCfg_->m_slam->m_lidar[l_iLd].m_dev.m_uiLocalPort);
                    loadParam_<u_int>("lidarInfo",
                                      l_sID,
                                      "laser_rpm",
                                      c_MasterCfg_->m_slam->m_lidar[l_iLd].m_uiRPM);
                    loadParam_<float>("lidarInfo",
                                      l_sID,
                                      "laser_height",
                                      c_MasterCfg_->m_slam->m_lidar[l_iLd].m_fFeatureHeight);
                    loadParam_<float>("lidarInfo",
                                      l_sID,
                                      "dist_res",
                                      c_MasterCfg_->m_slam->m_lidar[l_iLd].m_fDistComp);
                    loadParam_<bool>("lidarInfo",
                                     l_sID,
                                     "use_floor",
                                     c_MasterCfg_->m_slam->m_lidar[l_iLd].m_bUseFloor);
                    loadParam_<float>("lidarInfo",
                                      l_sID,
                                      "max_distance",
                                      c_MasterCfg_->m_slam->m_lidar[l_iLd].m_fMaxDist);
                    loadParam_<float>("lidarInfo",
                                      l_sID,
                                      "min_distance",
                                      c_MasterCfg_->m_slam->m_lidar[l_iLd].m_fMinDist);
                    loadParam_<std::string>("lidarInfo",
                                            l_sID,
                                            "pcap_name",
                                            c_MasterCfg_->m_slam->m_lidar[l_iLd].m_dev.m_sPcapName);
                    loadParam_<std::string>("lidarInfo",
                                            l_sID,
                                            "bag_name",
                                            c_MasterCfg_->m_slam->m_lidar[l_iLd].m_dev.m_sBagName);
                    loadParam_<std::vector<int>>("lidarInfo",
                                                 l_sID,
                                                 "curbLine",
                                                 c_MasterCfg_->m_slam->m_lidar[l_iLd].m_vCurbLine);
                    loadParam_<bool>("lidarInfo",
                                     l_sID,
                                     "use_multicast",
                                     c_MasterCfg_->m_slam->m_lidar[l_iLd].m_bUseMulticast);
                    for (u_int l_ibd = 0; l_ibd < 4; ++l_ibd)
                    {
                        std::string l_sBlind = "blind" + std::to_string(l_ibd);
                        std::vector<float> l_vBlindAngs;
                        /*雷达必须设置名称，否则认为不存在*/
                        if (loadParam_<std::vector<float>>(
                                "lidarInfo", l_sID, "blindInfo", l_sBlind, l_vBlindAngs))
                        {
                            // 盲区要求起始<终止 且均<=360deg
                            if (l_vBlindAngs.size() == 2 && l_vBlindAngs[0] <= 360.0
                                && l_vBlindAngs[1] <= 360.0 && l_vBlindAngs[0] < l_vBlindAngs[1])
                            {
                                // 增加一个空位
                                u_int l_uiSize =
                                    c_MasterCfg_->m_slam->m_lidar[l_iLd].m_vBlindSector.size();
                                c_MasterCfg_->m_slam->m_lidar[l_iLd].m_vBlindSector.resize(l_uiSize
                                                                                           + 1);
                                c_MasterCfg_->m_slam->m_lidar[l_iLd]
                                    .m_vBlindSector[l_uiSize]
                                    .m_fStartAng = l_vBlindAngs[0];
                                c_MasterCfg_->m_slam->m_lidar[l_iLd]
                                    .m_vBlindSector[l_uiSize]
                                    .m_fEndAng = l_vBlindAngs[1];
                            }
                        }
                    }

                    YAML::Node l_trans = c_config["LaserConfig"]["lidarInfo"][l_sID]["transToBase"];
                    YAML::Node l_transDip = c_config["LaserConfig"]["lidarInfo"][l_sID]["DipAngle"];
                    YAML::Node l_transCalib =
                        c_config["LaserConfig"]["lidarInfo"][l_sID]["Calibration"];
                    // 默认使用分离转移矩阵
                    if (l_transDip.IsDefined() && l_transDip.size() == 2)
                        loadParam_<std::vector<double>>(
                            l_transDip, c_MasterCfg_->m_slam->m_lidar[l_iLd].m_fDipAngle);
                    if (l_transCalib.IsDefined() && l_transCalib.size() == 6)
                        loadParam_<std::vector<double>>(
                            l_transCalib, c_MasterCfg_->m_slam->m_lidar[l_iLd].m_fCalibration);
                    if (l_trans.IsDefined() && l_trans.size() % 6 == 0)
                    {
                        std::vector<double> l_vInstallInfo(l_trans.size(), 0);
                        c_MasterCfg_->m_slam->m_lidar[l_iLd].m_transToBase.reset();
                        for (size_t i = 0; i < l_trans.size(); ++i)
                        {
                            loadParam_<double>(l_trans[i], l_vInstallInfo[i]);
                        }
                        c_MasterCfg_->m_slam->m_lidar[l_iLd].m_transToBase.m_bFlag =
                            PoseStatus::Default;
                        for (size_t i = 0; i < l_vInstallInfo.size(); i += 6)
                        {
                            s_POSE6D l_transSq;
                            l_transSq.setX(l_vInstallInfo[i + 0]);
                            l_transSq.setY(l_vInstallInfo[i + 1]);
                            l_transSq.setZ(l_vInstallInfo[i + 2]);
                            l_transSq.setRPY(l_vInstallInfo[i + 3],
                                             l_vInstallInfo[i + 4],
                                             l_vInstallInfo[i + 5]);
                            c_MasterCfg_->m_slam->m_lidar[l_iLd].m_transToBase =
                                l_transSq * c_MasterCfg_->m_slam->m_lidar[l_iLd].m_transToBase;
                        }
                        // 如果分离都没有定义则自动填写
                        if (!l_transDip.IsDefined() && !l_transCalib.IsDefined())
                        {
                            // dip不填写
                            // calib填最终矩阵
                            Eigen::VectorXd l_ThisRT =
                                c_MasterCfg_->m_slam->m_lidar[l_iLd].m_transToBase.coeffs_6();
                            for (int j = 0; j < 6; ++j)
                                c_MasterCfg_->m_slam->m_lidar[l_iLd].m_fCalibration[j] =
                                    l_ThisRT[j];
                        }
                    }
                }

                // 多雷达时，寻找首个基准雷达，并更换至队首
                if (c_MasterCfg_->m_slam->m_lidar.size() > 1)
                {
                    int l_iFirstBaseLidarID = -1;
                    for (u_int l_iLd = 0; l_iLd < c_MasterCfg_->m_slam->m_lidar.size(); ++l_iLd)
                    {
                        if (c_MasterCfg_->m_slam->m_lidar[l_iLd].m_bIsBaseLaser)
                        {
                            if (l_iFirstBaseLidarID == -1)
                                l_iFirstBaseLidarID = l_iLd;
                            else
                            {
                                faeLog(WERROR,
                                       "雷达配置异常 | 存在多个基准雷达: "
                                       "{}，程序将取消该项配置，请检查!",
                                       c_MasterCfg_->m_slam->m_lidar[l_iLd].m_sLaserName.c_str());
                                // c_MasterCfg_->m_slam->m_fae.setErrorCode("K9");
                                c_MasterCfg_->m_slam->m_lidar[l_iLd].m_bIsBaseLaser = false;
                            }
                        }
                    }
                    if (l_iFirstBaseLidarID == -1)
                    {
                        faeLog(WERROR,
                               "雷达配置异常 | 多雷达配置未设置基准雷达, "
                               "程序将随机配置基准雷达，请检查!",
                               NULL);
                        faeLog(WERROR, " *********************************************** ", NULL);
                        faeLog(WERROR,
                               " * 1. 连接web客户端，进入[雷达参数配置]设置，确认当前基准雷达名称 ",
                               NULL);
                        faeLog(WERROR, " * 2. 将当前需要设置为基准雷达的选项选择”是“", NULL);
                        faeLog(WERROR, " * 3. 确认其他雷达基准雷达选项为”否“ ", NULL);
                        faeLog(WERROR, " * **********************************************", NULL);
                        c_MasterCfg_->m_slam->m_fae.setErrorCode("K5");
                        // c_MasterCfg_->m_slam->m_fae.setErrorCode("K10");
                        c_MasterCfg_->m_slam->m_lidar[0].m_bIsBaseLaser = true;
                    }

                    // 将基准雷达移动至队首
                    if (l_iFirstBaseLidarID > 0)
                    {
                        s_LidarConfig l_BaseLidar =
                            c_MasterCfg_->m_slam->m_lidar[l_iFirstBaseLidarID];
                        // 删除基准雷达
                        c_MasterCfg_->m_slam->m_lidar.erase(c_MasterCfg_->m_slam->m_lidar.begin()
                                                            + l_iFirstBaseLidarID);
                        // 插入基准雷达
                        c_MasterCfg_->m_slam->m_lidar.insert(c_MasterCfg_->m_slam->m_lidar.begin(),
                                                             l_BaseLidar);
                    }
                }  // 单雷达时 默认为基准雷达
                else if (c_MasterCfg_->m_slam->m_lidar.size() == 1)
                {
                    if (!c_MasterCfg_->m_slam->m_lidar[c_MasterCfg_->m_slam->m_lidar.size() - 1]
                             .m_bIsBaseLaser)
                        c_MasterCfg_->m_slam->m_lidar[c_MasterCfg_->m_slam->m_lidar.size() - 1]
                            .m_bIsBaseLaser = true;
                }
            }
#pragma endregion

#pragma region "标定相关"
            s_MLCalibConfig& l_pMLC = c_MasterCfg_->m_slam->m_calib.m_MLCalib;
            loadParam_<int>("LidarCalib", "init_Time", l_pMLC.m_iMinTimeInit);
            loadParam_<int>("LidarCalib", "init_ReInitTime", l_pMLC.m_iMaxTimeReInit);
            loadParam_<float>("LidarCalib", "init_MaxVelocity", l_pMLC.m_fInitVeloTHR);
            std::vector<float> l_vStdDevTHR;
            if (loadParam_<std::vector<float>>("LidarCalib", "init_MaxStdDev", l_vStdDevTHR))
            {
                if (l_vStdDevTHR.size() == 2)
                {
                    l_pMLC.m_fInitStdDevTHR[0] = l_vStdDevTHR[0];
                    l_pMLC.m_fInitStdDevTHR[1] = l_vStdDevTHR[1];
                }
            }
            loadParam_<float>("LidarCalib", "calib_MaxVelocity", l_pMLC.m_fMoveVeloTHR);
            loadParam_<float>("LidarCalib", "calib_MaxRotSpeed", l_pMLC.m_fMaxRotSpeedTHR);
            loadParam_<float>("LidarCalib", "calib_MaxAccel", l_pMLC.m_fVeloDiffTHR);
            loadParam_<float>("LidarCalib", "calib_MinChangeDis", l_pMLC.m_fMinChangeDistance);
            loadParam_<float>("LidarCalib", "calib_MinChangeAng", l_pMLC.m_fMinChangeAng);
            l_pMLC.m_fMinChangeAng = (fmodf(std::fabs(l_pMLC.m_fMinChangeAng), 360.0f)) < 180
                                         ? (fmodf(std::fabs(l_pMLC.m_fMinChangeAng), 360.0f))
                                         : 360 - (fmodf(std::fabs(l_pMLC.m_fMinChangeAng), 360.0f));
            l_pMLC.m_fMinChangeDistance = std::fabs(l_pMLC.m_fMinChangeDistance);
            // l_pMLC.m_fMinChangeAng = std::fmod(l_pMLC.m_fMinChangeAng, 360.0f) > 180?
            // std::fmod(l_pMLC.m_fMinChangeAng, 360.0f) - 180: std::fmod(l_pMLC.m_fMinChangeAng,
            // 360.0f);

#pragma endregion

#pragma region "agv相关"
            /*agv通讯相关参数*/
            // 读取不到则设定为0.0.0.0 任意IP
            loadParam_<std::string>(
                "agv", "server_ip", c_MasterCfg_->m_slam->m_agv.m_dev.m_sLocalIP);
            loadParam_<uint16_t>(
                "agv", "server_port", c_MasterCfg_->m_slam->m_agv.m_dev.m_uiLocalPort);
            loadParam_<float>(
                "agv", "pose_valid_time", c_MasterCfg_->m_slam->m_agv.m_fPoseValidTime);
            loadParam_<std::string>(
                "agv", "pcap_name", c_MasterCfg_->m_slam->m_agv.m_dev.m_sPcapName);
            loadParam_<std::string>(
                "agv", "net_name", c_MasterCfg_->m_slam->m_agv.m_dev.m_sNetName);
            loadParam_<bool>("agv", "poseAlign", c_MasterCfg_->m_slam->m_agv.m_bPoseAlign);
            setDataMinLen(c_MasterCfg_->m_slam->m_agv.m_dev);

            /*车体校正参数*/
            std::vector<float> l_vAgv;
            if (loadParam_<std::vector<float>>("agv", "lidarToAgv", l_vAgv))
            {
                if (l_vAgv.size() == 6)
                {
                    s_POSE6D l_lidarToAgv;
                    c_MasterCfg_->m_slam->m_agv.m_stLidarToAgv.setX(l_vAgv[0]);
                    c_MasterCfg_->m_slam->m_agv.m_stLidarToAgv.setY(l_vAgv[1]);
                    c_MasterCfg_->m_slam->m_agv.m_stLidarToAgv.setZ(l_vAgv[2]);
                    c_MasterCfg_->m_slam->m_agv.m_stLidarToAgv.setRPY(
                        l_vAgv[3], l_vAgv[4], l_vAgv[5]);
                }
            }
            /*地图校正参数*/
            std::vector<float> l_vMyMap, l_vToMap;
            if (loadParam_<std::vector<float>>("agv", "my_map", l_vMyMap)
                && loadParam_<std::vector<float>>("agv", "to_map", l_vToMap))
            {
                if (l_vMyMap.size() == 3 && l_vToMap.size() == 3)
                {
                    memcpy(c_MasterCfg_->m_slam->m_agv.m_fMymap, &l_vMyMap[0], 3 * sizeof(float));
                    memcpy(c_MasterCfg_->m_slam->m_agv.m_fTomap, &l_vToMap[0], 3 * sizeof(float));
                    double l_f64YawAngleDiff = (c_MasterCfg_->m_slam->m_agv.m_fTomap[2]
                                                - c_MasterCfg_->m_slam->m_agv.m_fMymap[2])
                                               * M_PI / 180.0;
                    double l_f64x =
                        c_MasterCfg_->m_slam->m_agv.m_fMymap[0] * cos(l_f64YawAngleDiff)
                        - c_MasterCfg_->m_slam->m_agv.m_fMymap[1] * sin(l_f64YawAngleDiff);
                    double l_f64y =
                        c_MasterCfg_->m_slam->m_agv.m_fMymap[0] * sin(l_f64YawAngleDiff)
                        + c_MasterCfg_->m_slam->m_agv.m_fMymap[1] * cos(l_f64YawAngleDiff);

                    s_POSE6D l_stTrans;
                    l_stTrans.setRPY(0.0,
                                     0.0,
                                     c_MasterCfg_->m_slam->m_agv.m_fTomap[2]
                                         - c_MasterCfg_->m_slam->m_agv.m_fMymap[2]);
                    l_stTrans.setX(c_MasterCfg_->m_slam->m_agv.m_fTomap[0] - l_f64x);
                    l_stTrans.setY(c_MasterCfg_->m_slam->m_agv.m_fTomap[1] - l_f64y);
                    c_MasterCfg_->m_slam->m_agv.m_stTrans = l_stTrans;
                }
            }
            std::vector<float> l_vWheel;
            if (loadParam_<std::vector<float>>("agv", "wheelToLidar", l_vWheel))
            {
                if (l_vWheel.size() == 6)
                {
                    s_POSE6D l_lidarToAgv;
                    c_MasterCfg_->m_slam->m_vel.m_stWheelToLidar.setX(l_vWheel[0]);
                    c_MasterCfg_->m_slam->m_vel.m_stWheelToLidar.setY(l_vWheel[1]);
                    c_MasterCfg_->m_slam->m_vel.m_stWheelToLidar.setZ(l_vWheel[2]);
                    c_MasterCfg_->m_slam->m_vel.m_stWheelToLidar.setRPY(
                        l_vWheel[3], l_vWheel[4], l_vWheel[5]);
                }
            }
            if (loadParam_<std::vector<float>>("agv", "wheelTwistMax", l_vWheel))
            {
                if (l_vWheel.size() == 6)
                {
                    c_MasterCfg_->m_slam->m_vel.m_stWheelTwistMax.setXYZ(
                        l_vWheel[0], l_vWheel[1], l_vWheel[2]);
                    c_MasterCfg_->m_slam->m_vel.m_stWheelTwistMax.setRPY(
                        l_vWheel[3], l_vWheel[4], l_vWheel[5]);
                }
            }
            if (loadParam_<std::vector<float>>("agv", "wheelTwistMin", l_vWheel))
            {
                if (l_vWheel.size() == 6)
                {
                    c_MasterCfg_->m_slam->m_vel.m_stWheelTwistMin.setXYZ(
                        l_vWheel[0], l_vWheel[1], l_vWheel[2]);
                    c_MasterCfg_->m_slam->m_vel.m_stWheelTwistMin.setRPY(
                        l_vWheel[3], l_vWheel[4], l_vWheel[5]);
                }
            }
            loadParam_<bool>("agv", "wheelFilter", c_MasterCfg_->m_slam->m_vel.m_bWheelFilter);
            loadParam_<double>(
                "agv", "wheelFilter_ProcessPosN", c_MasterCfg_->m_slam->m_vel.m_fWheelProcPN);
            loadParam_<double>(
                "agv", "wheelFilter_ProcessRotN", c_MasterCfg_->m_slam->m_vel.m_fWheelProcRN);
            loadParam_<double>(
                "agv", "wheelFilter_MeasurePosN", c_MasterCfg_->m_slam->m_vel.m_fWheelMeasPN);
            loadParam_<double>(
                "agv", "wheelFilter_MeasureRotN", c_MasterCfg_->m_slam->m_vel.m_fWheelMeasRN);
#pragma endregion

#pragma region "web相关"
            loadParam_<uint16_t>("web_port", c_MasterCfg_->m_slam->m_web.m_socket.m_uiLocalPort);
#pragma endregion

#pragma region "Pos校验相关"
            s_PoseCheckConfig& l_pPosCheck = c_MasterCfg_->m_slam->m_posCheck;

            loadParam_<int>("posVerify", "maxNum_bak", l_pPosCheck.m_iMaxNumLidarBak);
            loadParam_<int>("posVerify", "frameBak", l_pPosCheck.m_iLidarBakFrame);
            loadParam_<int>("posVerify", "pred_valid_time", l_pPosCheck.m_iPredValidTime);
            loadParam_<bool>("posVerify", "poseCheck", l_pPosCheck.m_bOpenPoseCheck);
            loadParam_<bool>("posVerify", "testWheel", l_pPosCheck.m_bOnlyWheelOdom);
            loadParam_<bool>("posVerify", "useFuse", l_pPosCheck.m_bUsePoseFuse);
            loadParam_<bool>("posVerify", "safeModel", l_pPosCheck.m_bSafeModel);
            loadParam_<bool>("posVerify", "useCurb", l_pPosCheck.m_bUseCurb);
            loadParam_<bool>("posVerify", "useOdom", l_pPosCheck.m_bUseOdom);
            loadParam_<bool>("posVerify", "useLoct", l_pPosCheck.m_bUseLoct);
            loadParam_<bool>("posVerify", "testCurb", l_pPosCheck.m_bTestCurb);
            loadParam_<bool>("posVerify", "usePoseCheck", l_pPosCheck.m_bUsePoseCheck);
            loadParam_<float>("posVerify", "wheelFarDist", l_pPosCheck.m_fPredPosFarDist);
            loadParam_<float>("posVerify", "wheelFarTime", l_pPosCheck.m_fPredPosFarTime);
            loadParam_<float>("posVerify", "matchKdNumPer", l_pPosCheck.m_fKdMatchNumPercent);
            loadParam_<bool>("posVerify", "openOccupyVerify", l_pPosCheck.m_bOpenOccupyVerify);
            loadParam_<bool>("posVerify", "useOccupyVerify", l_pPosCheck.m_bUseOccupyVerify);
            loadParam_<int>("posVerify", "occVerifyBypassThd", l_pPosCheck.m_iOccVerifyBypassThd);
            loadParam_<float>("posVerify", "matchNumPer", l_pPosCheck.m_fMatchNumPercent);
            loadParam_<float>("posVerify", "matchOccupyScore", l_pPosCheck.m_fMatchOccupyScore);
            loadParam_<float>("posVerify", "verifyGauss", l_pPosCheck.m_fVerifyGauss);

            while (1)
            {
                int l_areaSize = l_pPosCheck.m_vNoCheckAreaList.size();
                std::string l_sArea = "nocheckArea_" + std::to_string(l_areaSize);
                std::vector<float> l_vNoCheckArea;
                if (loadParam_<std::vector<float>>("posVerify", l_sArea, l_vNoCheckArea))
                {
                    if (l_vNoCheckArea.size() == 4)
                        l_pPosCheck.m_vNoCheckAreaList.push_back(l_vNoCheckArea);
                    else
                        printf(
                            "%s set fail | size %d\n", l_sArea.c_str(), (int)l_vNoCheckArea.size());
                }
                else
                    break;
            }

            vFloat l_vfRead;
            /*运动状态参数*/
            l_vfRead.clear();
            loadParam_<vFloat>("posVerify", "move_res", l_vfRead);
            if (l_vfRead.size() == 6)
            {
                l_pPosCheck.m_stPoseMoveStatus.m_Pose.setXYZ(l_vfRead[0], l_vfRead[1], l_vfRead[2]);
                l_pPosCheck.m_stPoseMoveStatus.m_Pose.setRPY(l_vfRead[3], l_vfRead[4], l_vfRead[5]);
            }
            /*slamPose校验参数*/
            l_vfRead.clear();
            loadParam_<vFloat>("posVerify", "pos_res", l_vfRead);
            if (l_vfRead.size() == 6)
            {
                l_pPosCheck.m_stPoseCheck.m_Pose.setXYZ(l_vfRead[0], l_vfRead[1], l_vfRead[2]);
                l_pPosCheck.m_stPoseCheck.m_Pose.setRPY(l_vfRead[3], l_vfRead[4], l_vfRead[5]);
            }

            l_vfRead.clear();
            loadParam_<vFloat>("posVerify", "pos_vel", l_vfRead);
            if (l_vfRead.size() == 6)
            {
                l_pPosCheck.m_stPoseCheck.m_Twist.setXYZ(l_vfRead[0], l_vfRead[1], l_vfRead[2]);
                l_pPosCheck.m_stPoseCheck.m_Twist.setRPY(l_vfRead[3], l_vfRead[4], l_vfRead[5]);
            }

            /*curbPose校验参数*/
            l_vfRead.clear();
            loadParam_<vFloat>("posVerify", "cbpos_res", l_vfRead);
            if (l_vfRead.size() == 6)
            {
                l_pPosCheck.m_stCurbCheck.m_Pose.setXYZ(l_vfRead[0], l_vfRead[1], l_vfRead[2]);
                l_pPosCheck.m_stCurbCheck.m_Pose.setRPY(l_vfRead[3], l_vfRead[4], l_vfRead[5]);
            }

            l_vfRead.clear();
            loadParam_<vFloat>("posVerify", "cbpos_vel", l_vfRead);
            if (l_vfRead.size() == 6)
            {
                l_pPosCheck.m_stCurbCheck.m_Twist.setXYZ(l_vfRead[0], l_vfRead[1], l_vfRead[2]);
                l_pPosCheck.m_stCurbCheck.m_Twist.setRPY(l_vfRead[3], l_vfRead[4], l_vfRead[5]);
            }
#pragma endregion

#pragma region "地图相关"
            loadParam_<std::string>("map", "map_name", c_MasterCfg_->m_slam->m_map.m_sMapName);
            loadParam_<float>("map", "map_grid", c_MasterCfg_->m_slam->m_map.m_fMap_grid);
            loadParam_<float>("map", "map_size", c_MasterCfg_->m_slam->m_map.m_fMapRange);
            loadParam_<float>(
                "map", "map_EuclideanDis", c_MasterCfg_->m_slam->m_map.m_fEuclideanDis);
            loadParam_<float>("map", "map_pathGrid", c_MasterCfg_->m_slam->m_map.m_fMapKFPoseGrid);
            loadParam_<float>("map", "map_maxRange", c_MasterCfg_->m_slam->m_map.m_fMapMaxRange);
            loadParam_<float>("map", "map_pathRange", c_MasterCfg_->m_slam->m_map.m_fMapKFSubRange);
            loadParam_<int>("map", "map_TimeStep", c_MasterCfg_->m_slam->m_map.m_iMapKFTimeStep);
            loadParam_<float>("map", "map_DistStep", c_MasterCfg_->m_slam->m_map.m_fMapKFDistStep);
            loadParam_<float>("map", "map_AnglStep", c_MasterCfg_->m_slam->m_map.m_fMapKFAnglStep);
            loadParam_<float>(
                "map", "grid_outResolu", c_MasterCfg_->m_slam->m_map.m_fOutGridResolu);
            loadParam_<float>("map", "grid_inResolu", c_MasterCfg_->m_slam->m_map.m_fInGridResolu);
            loadParam_<int>("map", "grid_searchNum", c_MasterCfg_->m_slam->m_map.m_iGridSearchNum);
            loadParam_<int>("map", "grid_matchType", c_MasterCfg_->m_slam->m_map.m_iGridMatchType);
            loadParam_<std::size_t>("map", "grid_Size", c_MasterCfg_->m_slam->m_map.m_iGridSize);
            loadParam_<float>("map", "grid_groundHigh", c_MasterCfg_->m_slam->m_map.m_fGroundHigh);
            loadParam_<bool>("map", "map_proc", c_MasterCfg_->m_slam->m_map.m_bSaveMapProc);
            loadParam_<bool>("map", "ifSaveKF", c_MasterCfg_->m_slam->m_map.m_bSaveKFMap);
            loadParam_<float>("map", "grid_roofHigh", c_MasterCfg_->m_slam->m_map.m_fRoofHigh);
            loadParam_<int>(
                "map", "grid_optimizeModel", c_MasterCfg_->m_slam->m_map.m_iOptimizeModel);
            loadParam_<float>(
                "map", "grid_filterZValue", c_MasterCfg_->m_slam->m_map.m_fFilterZValue);
            loadParam_<float>(
                "map", "grid_corSamplSize", c_MasterCfg_->m_slam->m_map.m_fCorSampleSize);
            loadParam_<float>(
                "map", "grid_curbSamplSize", c_MasterCfg_->m_slam->m_map.m_fCurbSampleSize);
            loadParam_<float>(
                "map", "grid_surSamplSize", c_MasterCfg_->m_slam->m_map.m_fSurSampleSize);
            loadParam_<float>(
                "map", "grid_pathSamplSize", c_MasterCfg_->m_slam->m_map.m_fPathSampleSize);
            loadParam_<float>("map", "map_allPcSize", c_MasterCfg_->m_slam->m_map.m_fAllPcSize);
            loadParam_<float>("map", "map_linePcSize", c_MasterCfg_->m_slam->m_map.m_fLinePcSize);
            loadParam_<float>("map", "map_surfPcSize", c_MasterCfg_->m_slam->m_map.m_fSurfPcsize);
            loadParam_<bool>("map", "isUseGPSOptimize", c_MasterCfg_->m_slam->m_map.m_isUseGPSOptimize);

#pragma endregion

#pragma region "位置相关"

            /*位姿保存文件路径*/
            float l_x, l_y, l_z, l_r, l_p, l_a;
            if (loadParam_<float>("laserPose_x", l_x) && loadParam_<float>("laserPose_y", l_y)
                && loadParam_<float>("laserPose_z", l_z) && loadParam_<float>("laserPose_r", l_r)
                && loadParam_<float>("laserPose_p", l_p) && loadParam_<float>("laserPose_a", l_a))
            {
                c_MasterCfg_->m_slam->m_pos.m_stSetPose.setX(l_x);
                c_MasterCfg_->m_slam->m_pos.m_stSetPose.setY(l_y);
                c_MasterCfg_->m_slam->m_pos.m_stSetPose.setZ(l_z);
                c_MasterCfg_->m_slam->m_pos.m_stSetPose.setRPY(l_r, l_p, l_a);
                c_MasterCfg_->m_slam->m_pos.m_stSetPose.m_bFlag = PoseStatus::SettingPose;
            }
            // hsq 建图定位设置初始位姿参数success
            double l_lidarRFU2CarBackRFU_x, l_lidarRFU2CarBackRFU_y, l_lidarRFU2CarBackRFU_z, 
                l_lidarRFU2CarBackRFU_rollDegree, l_lidarRFU2CarBackRFU_pitchDegree, l_lidarRFU2CarBackRFU_yawDegree,
                l_startingPoint_lonDegree, l_startingPoint_latDegree, l_startingPoint_altDegree,
                l_startingPoint_UTM_x, l_startingPoint_UTM_y,
                l_startingPoint_rollDegree, l_startingPoint_pitchDegree, l_startingPoint_yawDegree;
            int l_startingPoint_UTM_Code;

            if(
                loadParam_<double>("localization", "lidarRFU2CarBackRFU_x", l_lidarRFU2CarBackRFU_x) 
                && loadParam_<double>("localization", "lidarRFU2CarBackRFU_y", l_lidarRFU2CarBackRFU_y) 
                && loadParam_<double>("localization", "lidarRFU2CarBackRFU_z", l_lidarRFU2CarBackRFU_z) 
                && loadParam_<double>("localization", "lidarRFU2CarBackRFU_rollDegree", l_lidarRFU2CarBackRFU_rollDegree) 
                && loadParam_<double>("localization", "lidarRFU2CarBackRFU_pitchDegree", l_lidarRFU2CarBackRFU_pitchDegree) 
                && loadParam_<double>("localization", "lidarRFU2CarBackRFU_yawDegree", l_lidarRFU2CarBackRFU_yawDegree) 
                && loadParam_<double>("localization", "startingPoint_lonDegree", l_startingPoint_lonDegree) 
                && loadParam_<double>("localization", "startingPoint_latDegree", l_startingPoint_latDegree) 
                && loadParam_<double>("localization", "startingPoint_altDegree", l_startingPoint_altDegree) 
                && loadParam_<int>("localization", "startingPoint_UTM_Code", l_startingPoint_UTM_Code) 
                && loadParam_<double>("localization", "startingPoint_UTM_x", l_startingPoint_UTM_x) 
                && loadParam_<double>("localization", "startingPoint_UTM_y", l_startingPoint_UTM_y) 
                && loadParam_<double>("localization", "startingPoint_rollDegree", l_startingPoint_rollDegree) 
                && loadParam_<double>("localization", "startingPoint_pitchDegree", l_startingPoint_pitchDegree) 
                && loadParam_<double>("localization", "startingPoint_yawDegree", l_startingPoint_yawDegree)
                )
            {
                c_MasterCfg_->m_slam->m_pos.m_stSetLidarRFU2CarBackRFUPose.setXYZ(l_lidarRFU2CarBackRFU_x, l_lidarRFU2CarBackRFU_y, l_lidarRFU2CarBackRFU_z);
                c_MasterCfg_->m_slam->m_pos.m_stSetLidarRFU2CarBackRFUPose.setRPY(l_lidarRFU2CarBackRFU_rollDegree, l_lidarRFU2CarBackRFU_pitchDegree, l_lidarRFU2CarBackRFU_yawDegree);
                c_MasterCfg_->m_slam->m_pos.m_stSetStartintPointPose.setXYZ(l_startingPoint_UTM_x, l_startingPoint_UTM_y, 0);
                c_MasterCfg_->m_slam->m_pos.m_stSetStartintPointPose.setRPY(l_startingPoint_rollDegree, l_startingPoint_pitchDegree, l_startingPoint_yawDegree);
                c_MasterCfg_->m_slam->m_pos.m_stSetStartintPointGPS.setXYZ(l_startingPoint_lonDegree, l_startingPoint_latDegree, l_startingPoint_altDegree);
                c_MasterCfg_->m_slam->m_pos.m_UTMCode = l_startingPoint_UTM_Code;
                std::cout << "hsq: load location and startingpoint params success\n " 
                        << "l_lidarRFU2CarBackRFU_xyz = " << l_lidarRFU2CarBackRFU_x
                        << ", " << l_lidarRFU2CarBackRFU_y<< ", " << l_lidarRFU2CarBackRFU_z
                        << "\nl_lidarRFU2CarBackRFU_rollDegree = " << l_lidarRFU2CarBackRFU_rollDegree
                        << std::setprecision(15)<< ", " << l_lidarRFU2CarBackRFU_pitchDegree<< ", " << l_lidarRFU2CarBackRFU_yawDegree
                        << "\nl_startingPoint_lla = " << l_startingPoint_lonDegree
                        << ", " << l_startingPoint_latDegree<< ", " << l_startingPoint_altDegree
                        << "\nl_startingPoint_UTM_Code = " << l_startingPoint_UTM_Code 
                        << "\nl_startingPoint_UTM_xy = " << l_startingPoint_UTM_x 
                        << ", " << l_startingPoint_UTM_y
                        << "\nl_startingPoint_rpy = " << l_startingPoint_rollDegree
                        << ", " << l_startingPoint_pitchDegree<< ", " << l_startingPoint_yawDegree
                        << std::endl;
            }
            // else{
            //     std::cout << "hsq: load location and startingpoint params error\n " 
            //             << "l_lidarRFU2CarBackRFU_xyz = " << c_MasterCfg_->m_slam->m_pos.m_stSetLidarRFU2CarBackRFUPose.x()
            //             << ", " << c_MasterCfg_->m_slam->m_pos.m_stSetLidarRFU2CarBackRFUPose.y()<< ", " << c_MasterCfg_->m_slam->m_pos.m_stSetLidarRFU2CarBackRFUPose.z()
            //             << "\nl_lidarRFU2CarBackRFU_rollDegree = " << c_MasterCfg_->m_slam->m_pos.m_stSetLidarRFU2CarBackRFUPose.roll()
            //             << ", " << c_MasterCfg_->m_slam->m_pos.m_stSetLidarRFU2CarBackRFUPose.pitch() << ", " << c_MasterCfg_->m_slam->m_pos.m_stSetLidarRFU2CarBackRFUPose.yaw()
            //             << "\nl_startingPoint_lla = " << c_MasterCfg_->m_slam->m_pos.m_stSetStartintPointGPS.x()
            //             << ", " << c_MasterCfg_->m_slam->m_pos.m_stSetStartintPointGPS.y()<< ", " << c_MasterCfg_->m_slam->m_pos.m_stSetStartintPointGPS.z()
            //             << "\nl_startingPoint_UTM_xy = " <<  c_MasterCfg_->m_slam->m_pos.m_stSetStartintPointPose.x() << ", " <<  c_MasterCfg_->m_slam->m_pos.m_stSetStartintPointPose.y()
            //             << "\nl_startingPoint_rpy = " <<  c_MasterCfg_->m_slam->m_pos.m_stSetStartintPointPose.roll()
            //             << ", " <<  c_MasterCfg_->m_slam->m_pos.m_stSetStartintPointPose.pitch()<< ", " <<  c_MasterCfg_->m_slam->m_pos.m_stSetStartintPointPose.yaw()
            //             << ",\n temp = " << tempUTMX 
            //             << ", " <<  templateUTMY
            //             << "\nl_startingPoint_UTM_xy = " << l_startingPoint_UTM_x 
            //             << ",\n temp = " << c_MasterCfg_->m_slam->m_pos.m_stSetStartintPointPose.m_trans.x() << ", " <<  c_MasterCfg_->m_slam->m_pos.m_stSetStartintPointPose.m_trans.y()
            //             << std::endl;
            // }


            loadParam_<bool>("savePath", c_MasterCfg_->m_slam->m_pos.m_bSaveWholePath);
#pragma endregion

#pragma region "预处理相关"
            CurvFeatureConfig& l_pCurv = c_MasterCfg_->m_slam->m_prep.m_curv;
            using vInt = std::vector<int>;
            using vFloat = std::vector<float>;
            loadParam_<vInt>("prep", "FEXT", "using_type", l_pCurv.m_vUseStat);
            loadParam_<float>("prep", "FEXT", "bondary_Angle", l_pCurv.m_basic.m_fMaxBondaryAng);
            loadParam_<vFloat>("prep", "FEXT", "L_Angle", l_pCurv.m_basic.m_fCornAngRange);
            loadParam_<float>("prep", "FEXT", "P_Angle", l_pCurv.m_basic.m_fMinSurfAng);
            loadParam_<vFloat>("prep", "FEXT", "smallThing_Scale", l_pCurv.m_scale.m_fSmallScale);
            loadParam_<vFloat>(
                "prep", "FEXT", "feature_HeightRange", l_pCurv.m_scale.m_fFeatHighRange);
            loadParam_<vFloat>("prep", "FEXT", "L_DistRange", l_pCurv.m_scale.m_fCornDistRange);
            loadParam_<float>(
                "prep", "FEXT", "ground_SpaceHeight", l_pCurv.m_scale.m_fGroundZSpace);
            loadParam_<bool>(
                "prep", "FEXT", "intenSmooth_check", l_pCurv.m_smooth.m_bIntensitySmoothCheck);
            loadParam_<bool>(
                "prep", "FEXT", "LineSmooth_check", l_pCurv.m_smooth.m_bLineSmoothCheck);
            loadParam_<int>(
                "prep", "FEXT", "intenSmooth_diff", l_pCurv.m_smooth.m_uiMaxIntensityDiff);
            loadParam_<float>("prep", "FEXT", "lineSmooth_diff", l_pCurv.m_smooth.m_fMaxLineAng);
            loadParam_<float>(
                "prep", "FEXT", "intenSmooth_perct", l_pCurv.m_smooth.m_fIntenDiffPercent);
            loadParam_<float>(
                "prep", "FEXT", "lineSmooth_perct", l_pCurv.m_smooth.m_fAngDiffPercent);
            loadParam_<bool>(
                "prep", "FEXT", "L_verticFilter", l_pCurv.m_filter.m_bVerticalCornFilter);
            loadParam_<bool>(
                "prep", "FEXT", "P_uniSample", l_pCurv.m_filter.m_bUniSampleSurfFilter);
            loadParam_<int>(
                "prep", "FEXT", "P_uniSample_maxPoints", l_pCurv.m_filter.m_uiMaxSurfPntNum);
            loadParam_<vFloat>(
                "prep", "FEXT", "P_uniSample_range", l_pCurv.m_filter.m_fSampleRange);
            loadParam_<vFloat>(
                "prep", "FEXT", "P_uniSample_scale", l_pCurv.m_filter.m_fSampleScale);
            loadParam_<float>(
                "prep", "FEXT", "L_verticFilter_radius", l_pCurv.m_filter.m_fCornCyldRadius);
            loadParam_<float>(
                "prep", "FEXT", "L_verticFilter_height", l_pCurv.m_filter.m_fCornCyldMinHeight);
            loadParam_<int>(
                "prep", "FEXT", "L_verticFilter_miPoints", l_pCurv.m_filter.m_uiCornCyldMinPoints);

            CurbFeatureConfig& l_pCurb = c_MasterCfg_->m_slam->m_prep.m_curb;
            loadParam_<float>("prep", "FEXT", "curb_height", l_pCurb.m_fCurbHeight);
            loadParam_<float>("prep", "FEXT", "axis_ang", l_pCurb.m_fAxisAngleDiff);
            loadParam_<float>("prep", "FEXT", "ground_height", l_pCurb.m_fGroundClearance);

            TimeSyncSensorConfig& l_pSync = c_MasterCfg_->m_slam->m_prep.m_sync;
            loadParam_<int>("prep", "Sync", "MaxTimeBehind_ms", l_pSync.m_iMaxTimeBehind);
            loadParam_<int>("prep", "Sync", "MaxTimeSync_ms", l_pSync.m_iMaxTimeSync);
            loadParam_<int>("prep", "Sync", "MaxTimeDelay_ms", l_pSync.m_iMaxTimeDelay);

            loadParam_<int>("prep", "VisualCloudMode", c_MasterCfg_->m_slam->m_prep.m_iVisualCloud);
            std::vector<double> IMUNoise;
            if (loadParam_<std::vector<double>>("prep", "imu", "angularVelNoise", IMUNoise))
            {
                if (IMUNoise.size() == 3)
                {
                    c_MasterCfg_->m_slam->m_prep.m_imu.m_vAngularVelNoise.x() = IMUNoise[0];
                    c_MasterCfg_->m_slam->m_prep.m_imu.m_vAngularVelNoise.y() = IMUNoise[1];
                    c_MasterCfg_->m_slam->m_prep.m_imu.m_vAngularVelNoise.z() = IMUNoise[2];
                }
            }
#pragma endregion

#pragma region "里程计相关"
            loadParam_<u_int>("odom",
                              "match",
                              "PMaxPoints",
                              c_MasterCfg_->m_slam->m_odom.m_match.m_uiPlaneMaxPoints);
            loadParam_<float>("odom",
                              "match",
                              "PMaxRadius",
                              c_MasterCfg_->m_slam->m_odom.m_match.m_fPlaneMaxRadius);
            loadParam_<float>("odom",
                              "match",
                              "PMeanDiff",
                              c_MasterCfg_->m_slam->m_odom.m_match.m_fPlaneMeanDiff);
            loadParam_<u_int>("odom",
                              "match",
                              "LMinPoints",
                              c_MasterCfg_->m_slam->m_odom.m_match.m_uiLineMinPoints);
            loadParam_<float>("odom",
                              "match",
                              "LMaxRadius",
                              c_MasterCfg_->m_slam->m_odom.m_match.m_fLine2DRadius);
            loadParam_<float>(
                "odom", "match", "LMaxZDiff", c_MasterCfg_->m_slam->m_odom.m_match.m_fLineMaxZDiff);
            loadParam_<float>(
                "odom", "match", "maxDist", c_MasterCfg_->m_slam->m_odom.m_match.m_fMaxDist);
            loadParam_<bool>(
                "odom", "match", "onlySample", c_MasterCfg_->m_slam->m_odom.m_match.m_bSampleMatch);
            loadParam_<std::vector<float>>(
                "odom", "match", "Pvalid", c_MasterCfg_->m_slam->m_odom.m_match.m_vfPlanePCA);
            loadParam_<bool>("odom", "XYMoveMode", c_MasterCfg_->m_slam->m_odom.optimiz_o3D);
            loadParam_<float>("odom", "imuQuatPrec", c_MasterCfg_->m_slam->m_odom.m_fImuQuatPrec);
            if (l_bXYMovModel)
                c_MasterCfg_->m_slam->m_odom.optimiz_o3D = true;
#pragma endregion

#pragma region "定位相关"
            loadParam_<u_int>("locate",
                              "match",
                              "PMaxPoints",
                              c_MasterCfg_->m_slam->m_loct.m_match.m_uiPlaneMaxPoints);
            loadParam_<float>("locate",
                              "match",
                              "PMaxRadius",
                              c_MasterCfg_->m_slam->m_loct.m_match.m_fPlaneMaxRadius);
            loadParam_<float>("locate",
                              "match",
                              "PMeanDiff",
                              c_MasterCfg_->m_slam->m_loct.m_match.m_fPlaneMeanDiff);
            loadParam_<std::vector<float>>(
                "locate", "match", "Pvalid", c_MasterCfg_->m_slam->m_loct.m_match.m_vfPlanePCA);
            loadParam_<u_int>("locate",
                              "match",
                              "LMinPoints",
                              c_MasterCfg_->m_slam->m_loct.m_match.m_uiLineMinPoints);
            loadParam_<float>("locate",
                              "match",
                              "LMaxRadius",
                              c_MasterCfg_->m_slam->m_loct.m_match.m_fLine2DRadius);
            loadParam_<float>("locate",
                              "match",
                              "LMaxZDiff",
                              c_MasterCfg_->m_slam->m_loct.m_match.m_fLineMaxZDiff);
            loadParam_<float>(
                "locate", "match", "maxDist", c_MasterCfg_->m_slam->m_loct.m_match.m_fMaxDist);
            loadParam_<bool>("locate",
                             "match",
                             "onlySample",
                             c_MasterCfg_->m_slam->m_loct.m_match.m_bSampleMatch);
            loadParam_<u_int>("locate",
                              "manual",
                              "PMaxPoints",
                              c_MasterCfg_->m_slam->m_loct.m_manual.m_uiPlaneMaxPoints);
            loadParam_<float>("locate",
                              "manual",
                              "PMaxRadius",
                              c_MasterCfg_->m_slam->m_loct.m_manual.m_fPlaneMaxRadius);
            loadParam_<float>("locate",
                              "manual",
                              "PMeanDiff",
                              c_MasterCfg_->m_slam->m_loct.m_manual.m_fPlaneMeanDiff);
            loadParam_<std::vector<float>>(
                "locate", "manual", "Pvalid", c_MasterCfg_->m_slam->m_loct.m_manual.m_vfPlanePCA);
            loadParam_<u_int>("locate",
                              "manual",
                              "LMinPoints",
                              c_MasterCfg_->m_slam->m_loct.m_manual.m_uiLineMinPoints);
            loadParam_<float>("locate",
                              "manual",
                              "LMaxRadius",
                              c_MasterCfg_->m_slam->m_loct.m_manual.m_fLine2DRadius);
            loadParam_<float>("locate",
                              "manual",
                              "LMaxZDiff",
                              c_MasterCfg_->m_slam->m_loct.m_manual.m_fLineMaxZDiff);
            loadParam_<float>(
                "locate", "manual", "maxDist", c_MasterCfg_->m_slam->m_loct.m_manual.m_fMaxDist);
            loadParam_<bool>("locate",
                             "manual",
                             "onlySample",
                             c_MasterCfg_->m_slam->m_loct.m_manual.m_bSampleMatch);
            loadParam_<bool>("locate", "XYMoveMode", c_MasterCfg_->m_slam->m_loct.optimiz_o3D);
            loadParam_<int>(
                "locate", "ManualFrameCnt", c_MasterCfg_->m_slam->m_loct.m_iManualFrameCnt);
            loadParam_<float>(
                "locate", "speed_res", c_MasterCfg_->m_slam->m_loct.m_fTwistSmoothRes);
            if (l_bXYMovModel)
                c_MasterCfg_->m_slam->m_loct.optimiz_o3D = true;
#pragma endregion

#pragma region "建图相关"
            for (int l_cnt = 1; l_cnt < 5; ++l_cnt)
            {
                std::string l_stMatchTime = "match_" + std::to_string(l_cnt);
                loadParam_<u_int>("mapping",
                                  l_stMatchTime,
                                  "PMaxPoints",
                                  c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_uiPlaneMaxPoints);
                loadParam_<float>("mapping",
                                  l_stMatchTime,
                                  "PMaxRadius",
                                  c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_fPlaneMaxRadius);
                loadParam_<float>("mapping",
                                  l_stMatchTime,
                                  "PMeanDiff",
                                  c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_fPlaneMeanDiff);
                loadParam_<u_int>("mapping",
                                  l_stMatchTime,
                                  "LMinPoints",
                                  c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_uiLineMinPoints);
                loadParam_<float>("mapping",
                                  l_stMatchTime,
                                  "LMaxRadius",
                                  c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_fLine2DRadius);
                loadParam_<float>("mapping",
                                  l_stMatchTime,
                                  "LMaxZDiff",
                                  c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_fLineMaxZDiff);
                loadParam_<float>("mapping",
                                  l_stMatchTime,
                                  "maxDist",
                                  c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_fMaxDist);
                loadParam_<bool>("mapping",
                                 l_stMatchTime,
                                 "onlySample",
                                 c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_bSampleMatch);
                loadParam_<std::vector<float>>(
                    "mapping",
                    l_stMatchTime,
                    "Pvalid",
                    c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_vfPlanePCA);
            }

            loadParam_<bool>("enableLoop", c_MasterCfg_->m_slam->m_slam.m_bEnableLoop);
            loadParam_<bool>("mapping", "XYMoveMode", c_MasterCfg_->m_slam->m_slam.optimiz_o3D);
            if (l_bXYMovModel)
                c_MasterCfg_->m_slam->m_slam.optimiz_o3D = true;

#pragma endregion
#pragma region "回环相关"
            // sc提取参数
            loadParam_<int>("mapping", "SCSector", c_MasterCfg_->m_slam->m_loop.m_iSector);
            loadParam_<int>("mapping", "SCRing", c_MasterCfg_->m_slam->m_loop.m_iRing);
            loadParam_<double>("mapping", "SCRadius", c_MasterCfg_->m_slam->m_loop.m_dRadius);
            loadParam_<float>("mapping", "SCRecongnizeThr", c_MasterCfg_->m_slam->m_loop.m_fSCthr);
            //回环匹配参数
            loadParam_<int>("mapping", "LoopOptTimes", c_MasterCfg_->m_slam->m_loop.m_iOptTimes);
            loadParam_<float>("mapping",
                              "LoopMaxAveDistance",
                              c_MasterCfg_->m_slam->m_loop.m_fMatchAveDistanceThr);
            loadParam_<float>("mapping",
                              "LoopMinMatchPercent",
                              c_MasterCfg_->m_slam->m_loop.m_fMatchMinNumPercentThr);
            loadParam_<float>(
                "mapping", "LoopVerifyScoreThr", c_MasterCfg_->m_slam->m_loop.m_fVerifyScoreThr);
            loadParam_<float>("mapping",
                              "LoopVerifyNumThr",
                              c_MasterCfg_->m_slam->m_loop.m_fVerifyMatchPercentThr);
            //粒子滤波参数
            loadParam_<float>(
                "mapping", "FilterRangeX", c_MasterCfg_->m_slam->m_loop.m_fParticleFilterRangeX);
            loadParam_<float>(
                "mapping", "FilterRangeY", c_MasterCfg_->m_slam->m_loop.m_fParticleFilterRangeY);
            loadParam_<float>(
                "mapping", "FilterRangeZ", c_MasterCfg_->m_slam->m_loop.m_fParticleFilterRangeZ);
            loadParam_<float>(
                "mapping", "FilterRangeA", c_MasterCfg_->m_slam->m_loop.m_fParticleFilterRangeA);
            loadParam_<int>(
                "mapping", "ParticleNums", c_MasterCfg_->m_slam->m_loop.m_iParticleNums);
            loadParam_<int>(
                "mapping", "AMCLFilterTimes", c_MasterCfg_->m_slam->m_loop.m_iParticleFilterTimes);
#pragma endregion

#pragma region "设备相关"
            c_MasterCfg_->m_slam->m_devList.clear();
            for (uint32_t i = 0; i < c_MasterCfg_->m_slam->m_lidar.size(); i++)
            {
                c_MasterCfg_->m_slam->m_devList.push_back(&c_MasterCfg_->m_slam->m_lidar[i].m_dev);
                c_MasterCfg_->m_slam->m_devList[c_MasterCfg_->m_slam->m_devList.size() - 1]
                    ->m_uiId = c_MasterCfg_->m_slam->m_devList.size() - 1;
            }
            // agv客户端端口为任意端口 ，IP和服务器相同
            c_MasterCfg_->m_slam->m_agv.m_dev.m_uiDevPort = 0;
            c_MasterCfg_->m_slam->m_agv.m_dev.m_sDevIP =
                c_MasterCfg_->m_slam->m_agv.m_dev.m_sLocalIP;
            c_MasterCfg_->m_slam->m_devList.push_back(&c_MasterCfg_->m_slam->m_agv.m_dev);
            c_MasterCfg_->m_slam->m_devList[c_MasterCfg_->m_slam->m_devList.size() - 1]->m_uiId =
                c_MasterCfg_->m_slam->m_devList.size() - 1;

#pragma endregion

#pragma region "日志相关"
            loadParam_<int>("log", "log_level", c_MasterCfg_->m_slam->m_fae.m_iLogLevel);
            loadParam_<bool>("log", "log_printf", c_MasterCfg_->m_slam->m_fae.m_bPrintfLog);
            loadParam_<bool>(
                "log", "log_printf_time", c_MasterCfg_->m_slam->m_fae.m_bPrintfTimeLog);
            loadParam_<bool>(
                "log", "log_printf_check", c_MasterCfg_->m_slam->m_fae.m_bPrintfCheckLog);
            loadParam_<bool>(
                "log", "save_error_local", c_MasterCfg_->m_slam->m_fae.m_bSaveErrorPoseLidar);
            loadParam_<uint32_t>("log", "log_size", c_MasterCfg_->m_slam->m_fae.m_uiLogFileSize);
            c_MasterCfg_->m_slam->m_fae.setLogFileSize();
            if (loadParam_<std::string>("log", "log_path", c_MasterCfg_->m_slam->m_fae.m_sLogPath))
            {
                if (c_MasterCfg_->m_slam->m_fae
                        .m_sLogPath[c_MasterCfg_->m_slam->m_fae.m_sLogPath.length() - 1]
                    != '/')
                    c_MasterCfg_->m_slam->m_fae.m_sLogPath += "/";
            }

#pragma endregion

#pragma region "时间戳初始化相关"
            /*时间戳源*/
            int timeSource = 0;
            if (loadParam_<int>("time", "TimeSource", timeSource))
            {
                if (timeSource != 0 || timeSource != 1)
                    c_MasterCfg_->m_slam->m_time.setTimeSource(TimeSource(timeSource));
                else
                {
                    c_MasterCfg_->m_slam->m_time.setTimeSource(TimeSource::Local);
                    std::cerr << "[Error] Time Source Error " << timeSource << std::endl;
                }
            }
#pragma endregion

            if (c_bLogValid_)
                c_MasterCfg_->m_slam->printf();
        }
        catch (std::exception& ex)
        {
            // std::cerr << "Load Yaml File Failed ! : " << p_sYamlPath.c_str() << std::endl;
            return false;
        }
        return true;
    }

    /**
     * @description: 保存 user / default 参数
     * @param {string} p_sPkgPath
     * @return {*}
     */
    bool saveSysParam(std::string p_sPkgPath)
    {
        return (saveSysParam_(p_sPkgPath) && saveLogParam(p_sPkgPath));
    }

    /**
     * @description: 保存 log 参数
     * @param {string} p_sPkgPath
     * @return {*}
     */
    bool saveLogParam(std::string p_sPkgPath)
    {
        try
        {
            /*默认写入 user 配置文件，不存在则创建*/
            configFile = p_sPkgPath + "/config/logCfg.yaml";

            // 有则打开，没有则创建
            std::ofstream l_file(configFile);
            YAML::Node l_node;
            c_config = l_node;
            saveParam_<std::string>("log", "log_path", c_MasterCfg_->m_slam->m_fae.m_sLogPath);
            l_file << c_config << std::endl;
            l_file.close();
        }
        catch (std::exception& ex)
        {
            std::cerr << "Save Log Yaml File Failed !" << std::endl;
            return false;
        }
        return true;
    }

    /**
     * @description: 保存 user / default 参数
     * @param {string} p_sPkgPath
     * @return {*}
     */
    bool saveSysParam_(std::string p_sPkgPath)
    {
        try
        {
            /*默认写入 user 配置文件，不存在则创建*/
            configFile = p_sPkgPath + "/config/user.yaml";
            std::string l_sBackFile = p_sPkgPath + "/config/last.yaml.bak";
            std::string l_sDefaultFile = p_sPkgPath + "/config/default.yaml";
            // std::string l_sBackFile = p_sPkgPath +  "/config/user.yaml.bak";
            // 备份文件
            if (isExistFileOrFolder(configFile))
            {
                c_config = YAML::LoadFile(configFile);
                copyFileOrFolder(configFile, l_sBackFile);
                // LOGW(WINFO, "{} [param] back copy {} to {}", WJLog::getWholeSysTime(),
                // configFile, l_sBackFile);
            }
            else
            {
                LOGW(WINFO, "{} [param] {} do not exit!", WJLog::getWholeSysTime(), configFile);
                if (isExistFileOrFolder(l_sBackFile))
                {
                    copyFileOrFolder(l_sBackFile, configFile);
                    return true;
                }
                else if (isExistFileOrFolder(l_sDefaultFile))
                {
                    copyFileOrFolder(l_sDefaultFile, configFile);
                    return true;
                }
                else
                {
                    return false;
                }
            }

            // 有则打开，没有则创建
            std::ofstream l_file(configFile);
#pragma region "模式相关"
            /*工作模式 */
            saveParam_<int>("work_mode", c_MasterCfg_->m_slam->m_iWorkMode);
            /*场景模式 */
            saveParam_<int>("param_mode", c_MasterCfg_->m_slam->m_nParam_mode);
            /*调试模式 */
            saveParam_<bool>("debugModel", c_MasterCfg_->m_slam->m_bDebugModel);
            if (c_MasterCfg_->m_slam->m_loct.optimiz_o3D == true
                || c_MasterCfg_->m_slam->m_slam.optimiz_o3D == true)
                saveParam_<bool>("XYMovingModel", true);
            else
                saveParam_<bool>("XYMovingModel", false);
#pragma endregion

#pragma region "其他相关"
            /*可视化雷达/地图*/
            saveParam_<bool>("send_map", c_MasterCfg_->m_slam->m_bSendMap);
            saveParam_<bool>("send_point", c_MasterCfg_->m_slam->m_bSendCurPC);
            saveParam_<int>("view_mode", c_MasterCfg_->m_slam->m_iViewMode);
#pragma endregion

#pragma region "web相关"
            saveParam_<uint16_t>("web_port", c_MasterCfg_->m_slam->m_web.m_socket.m_uiLocalPort);
#pragma endregion

#pragma region "靶标相关"
            saveParam_<bool>("use_mark", c_MasterCfg_->m_slam->m_bIsUseMark);
            saveParam_<bool>("use_imu", c_MasterCfg_->m_slam->m_bIsUseIMU);
            saveParam_<int>("slam_model", c_MasterCfg_->m_slam->m_iSlamMode);
            saveParam_<int>("markWightMax", c_MasterCfg_->m_slam->m_bMarkWeightMax);
            saveParam_<int>("markWightMin", c_MasterCfg_->m_slam->m_bMarkWeightMin);
            saveParam_<int>("markRec_model", c_MasterCfg_->m_slam->m_iMarkRecModel);
            saveParam_<float>("mark_size", c_MasterCfg_->m_slam->m_fMarkSize);

#pragma endregion

#pragma region "雷达相关"
            for (u_int l_iLd = 0; l_iLd < c_MasterCfg_->m_slam->m_lidar.size(); ++l_iLd)
                saveLidarParam(c_MasterCfg_->m_slam->m_lidar[l_iLd]);
#pragma endregion

#pragma region "标定相关"
            s_MLCalibConfig& l_pMLC = c_MasterCfg_->m_slam->m_calib.m_MLCalib;
            saveParam_<int>("LidarCalib", "init_Time", l_pMLC.m_iMinTimeInit);
            saveParam_<int>("LidarCalib", "init_ReInitTime", l_pMLC.m_iMaxTimeReInit);
            saveParam_<float>("LidarCalib", "init_MaxVelocity", l_pMLC.m_fInitVeloTHR);
            std::vector<float> l_vStdDevTHR(2, 0);
            l_vStdDevTHR[0] = l_pMLC.m_fInitStdDevTHR[0];
            l_vStdDevTHR[1] = l_pMLC.m_fInitStdDevTHR[1];
            saveParam_<std::vector<float>>("LidarCalib", "init_MaxStdDev", l_vStdDevTHR);
            saveParam_<float>("LidarCalib", "calib_MaxAccel", l_pMLC.m_fVeloDiffTHR);
            saveParam_<float>("LidarCalib", "calib_MaxVelocity", l_pMLC.m_fMoveVeloTHR);
            saveParam_<float>("LidarCalib", "calib_MaxRotSpeed", l_pMLC.m_fMaxRotSpeedTHR);
            saveParam_<float>("LidarCalib", "calib_MinChangeDis", l_pMLC.m_fMinChangeDistance);
            saveParam_<float>("LidarCalib", "calib_MinChangeAng", l_pMLC.m_fMinChangeAng);
            saveParam_<std::string>("LidarCalib", "calib_analysis", l_pMLC.m_sProcessFile);
#pragma endregion

#pragma region "agv相关"
            /*agv通讯相关参数*/
            saveParam_<std::string>(
                "agv", "server_ip", c_MasterCfg_->m_slam->m_agv.m_dev.m_sLocalIP);
            saveParam_<uint16_t>(
                "agv", "server_port", c_MasterCfg_->m_slam->m_agv.m_dev.m_uiLocalPort);
            saveParam_<std::string>(
                "agv", "pcap_name", c_MasterCfg_->m_slam->m_agv.m_dev.m_sPcapName);
            saveParam_<std::string>(
                "agv", "net_name", c_MasterCfg_->m_slam->m_agv.m_dev.m_sNetName);
            saveParam_<bool>("agv", "poseAlign", c_MasterCfg_->m_slam->m_agv.m_bPoseAlign);
            saveParam_<float>(
                "agv", "pose_valid_time", c_MasterCfg_->m_slam->m_agv.m_fPoseValidTime);
            /*车体校正参数*/
            std::vector<double> l_vAgv(6, 0);
            Eigen::VectorXd l_vAgvX = c_MasterCfg_->m_slam->m_agv.m_stLidarToAgv.coeffs_6();
            for (int i = 0; i < 6; ++i)
                l_vAgv[i] = l_vAgvX[i];
            saveParamFormat_<std::vector<double>>("agv", "lidarToAgv", l_vAgv);
            /*地图校正参数*/
            std::vector<float> l_vMyMap(c_MasterCfg_->m_slam->m_agv.m_fMymap,
                                        c_MasterCfg_->m_slam->m_agv.m_fMymap + 3);
            std::vector<float> l_vToMap(c_MasterCfg_->m_slam->m_agv.m_fTomap,
                                        c_MasterCfg_->m_slam->m_agv.m_fTomap + 3);
            saveParamFormat_<std::vector<float>>("agv", "my_map", l_vMyMap);
            saveParamFormat_<std::vector<float>>("agv", "to_map", l_vToMap);
            vFloat l_vfRead;
            l_vfRead = c_MasterCfg_->m_slam->m_vel.m_stWheelToLidar.dataVec();
            saveParamFormat_<vFloat>("agv", "wheelToLidar", l_vfRead);
            l_vfRead = c_MasterCfg_->m_slam->m_vel.m_stWheelTwistMax.dataVec();
            saveParamFormat_<vFloat>("agv", "wheelTwistMax", l_vfRead);
            l_vfRead = c_MasterCfg_->m_slam->m_vel.m_stWheelTwistMin.dataVec();
            saveParam_<vFloat>("agv", "wheelTwistMin", l_vfRead);
            saveParam_<bool>("agv", "wheelFilter", c_MasterCfg_->m_slam->m_vel.m_bWheelFilter);
            saveParam_<double>(
                "agv", "wheelFilter_ProcessPosN", c_MasterCfg_->m_slam->m_vel.m_fWheelProcPN);
            saveParam_<double>(
                "agv", "wheelFilter_ProcessRotN", c_MasterCfg_->m_slam->m_vel.m_fWheelProcRN);
            saveParam_<double>(
                "agv", "wheelFilter_MeasurePosN", c_MasterCfg_->m_slam->m_vel.m_fWheelMeasPN);
            saveParam_<double>(
                "agv", "wheelFilter_MeasureRotN", c_MasterCfg_->m_slam->m_vel.m_fWheelMeasRN);
#pragma endregion

#pragma region "Pos校验相关"
            s_PoseCheckConfig& l_pPosCheck = c_MasterCfg_->m_slam->m_posCheck;
            saveParam_<int>("posVerify", "maxNum_bak", l_pPosCheck.m_iMaxNumLidarBak);
            saveParam_<int>("posVerify", "frameBak", l_pPosCheck.m_iLidarBakFrame);
            saveParam_<int>("posVerify", "pred_valid_time", l_pPosCheck.m_iPredValidTime);
            saveParam_<bool>("posVerify", "poseCheck", l_pPosCheck.m_bOpenPoseCheck);
            saveParam_<bool>("posVerify", "useLoct", l_pPosCheck.m_bUseLoct);
            saveParam_<bool>("posVerify", "useOdom", l_pPosCheck.m_bUseOdom);
            saveParam_<float>("posVerify", "matchKdNumPer", l_pPosCheck.m_fKdMatchNumPercent);
            saveParam_<bool>("posVerify", "openOccupyVerify", l_pPosCheck.m_bOpenOccupyVerify);
            saveParam_<bool>("posVerify", "useOccupyVerify", l_pPosCheck.m_bUseOccupyVerify);
            saveParam_<int>("posVerify", "occVerifyBypassThd", l_pPosCheck.m_iOccVerifyBypassThd);
            saveParam_<float>("posVerify", "matchNumPer", l_pPosCheck.m_fMatchNumPercent);
            saveParam_<float>("posVerify", "matchOccupyScore", l_pPosCheck.m_fMatchOccupyScore);
            saveParam_<bool>("posVerify", "usePoseCheck", l_pPosCheck.m_bUsePoseCheck);
            saveParam_<bool>("posVerify", "testWheel", l_pPosCheck.m_bOnlyWheelOdom);
            saveParam_<bool>("posVerify", "useFuse", l_pPosCheck.m_bUsePoseFuse);
            saveParam_<bool>("posVerify", "safeModel", l_pPosCheck.m_bSafeModel);
            saveParam_<bool>("posVerify", "useCurb", l_pPosCheck.m_bUseCurb);
            saveParam_<bool>("posVerify", "testCurb", l_pPosCheck.m_bTestCurb);
            saveParam_<float>("posVerify", "wheelFarDist", l_pPosCheck.m_fPredPosFarDist);
            saveParam_<float>("posVerify", "wheelFarTime", l_pPosCheck.m_fPredPosFarTime);
            saveParam_<float>("posVerify", "verifyGauss", l_pPosCheck.m_fVerifyGauss);

            /*运动状态参数*/
            l_vfRead = l_pPosCheck.m_stPoseMoveStatus.m_Pose.dataVec();
            saveParamFormat_<vFloat>("posVerify", "move_res", l_vfRead);
            /*slamPose校验参数*/
            l_vfRead = l_pPosCheck.m_stPoseCheck.m_Pose.dataVec();
            saveParamFormat_<vFloat>("posVerify", "pos_res", l_vfRead);

            l_vfRead = l_pPosCheck.m_stPoseCheck.m_Twist.dataVec();
            saveParamFormat_<vFloat>("posVerify", "pos_vel", l_vfRead);

            /*curbPose校验参数*/
            l_vfRead = l_pPosCheck.m_stCurbCheck.m_Pose.dataVec();
            saveParam_<vFloat>("posVerify", "cbpos_res", l_vfRead);

            l_vfRead = l_pPosCheck.m_stCurbCheck.m_Twist.dataVec();
            saveParam_<vFloat>("posVerify", "cbpos_vel", l_vfRead);

#pragma endregion

#pragma region "地图相关"
            saveParam_<std::string>("map", "map_name", c_MasterCfg_->m_slam->m_map.m_sMapName);
            saveParam_<float>(
                "map", "grid_outResolu", c_MasterCfg_->m_slam->m_map.m_fOutGridResolu);
            saveParam_<float>("map", "grid_inResolu", c_MasterCfg_->m_slam->m_map.m_fInGridResolu);
            saveParam_<int>("map", "grid_searchNum", c_MasterCfg_->m_slam->m_map.m_iGridSearchNum);
            saveParam_<int>("map", "grid_matchType", c_MasterCfg_->m_slam->m_map.m_iGridMatchType);
            saveParam_<std::size_t>("map", "grid_Size", c_MasterCfg_->m_slam->m_map.m_iGridSize);
            saveParam_<float>("map", "grid_groundHigh", c_MasterCfg_->m_slam->m_map.m_fGroundHigh);
            saveParam_<bool>("map", "map_proc", c_MasterCfg_->m_slam->m_map.m_bSaveMapProc);
            saveParam_<bool>("map", "ifSaveKF", c_MasterCfg_->m_slam->m_map.m_bSaveKFMap);
            saveParam_<float>("map", "grid_roofHigh", c_MasterCfg_->m_slam->m_map.m_fRoofHigh);
            saveParam_<int>(
                "map", "grid_optimizeModel", c_MasterCfg_->m_slam->m_map.m_iOptimizeModel);
            saveParam_<float>(
                "map", "grid_filterZValue", c_MasterCfg_->m_slam->m_map.m_fFilterZValue);
            saveParam_<float>(
                "map", "grid_corSamplSize", c_MasterCfg_->m_slam->m_map.m_fCorSampleSize);
            saveParam_<float>(
                "map", "grid_curbSamplSize", c_MasterCfg_->m_slam->m_map.m_fCurbSampleSize);
            saveParam_<float>(
                "map", "grid_surSamplSize", c_MasterCfg_->m_slam->m_map.m_fSurSampleSize);
            saveParam_<float>(
                "map", "grid_pathSamplSize", c_MasterCfg_->m_slam->m_map.m_fPathSampleSize);
            saveParam_<float>("map", "map_allPcSize", c_MasterCfg_->m_slam->m_map.m_fAllPcSize);
            saveParam_<float>("map", "map_linePcSize", c_MasterCfg_->m_slam->m_map.m_fLinePcSize);
            saveParam_<float>("map", "map_surfPcSize", c_MasterCfg_->m_slam->m_map.m_fSurfPcsize);
            saveParam_<float>("map", "map_grid", c_MasterCfg_->m_slam->m_map.m_fMap_grid);
            saveParam_<float>("map", "map_size", c_MasterCfg_->m_slam->m_map.m_fMapRange);
            saveParam_<float>(
                "map", "map_EuclideanDis", c_MasterCfg_->m_slam->m_map.m_fEuclideanDis);
            saveParam_<float>("map", "map_pathGrid", c_MasterCfg_->m_slam->m_map.m_fMapKFPoseGrid);
            saveParam_<float>("map", "map_maxRange", c_MasterCfg_->m_slam->m_map.m_fMapMaxRange);
            saveParam_<float>("map", "map_pathRange", c_MasterCfg_->m_slam->m_map.m_fMapKFSubRange);
            saveParam_<int>("map", "map_TimeStep", c_MasterCfg_->m_slam->m_map.m_iMapKFTimeStep);
            saveParam_<float>("map", "map_DistStep", c_MasterCfg_->m_slam->m_map.m_fMapKFDistStep);
            saveParam_<float>("map", "map_AnglStep", c_MasterCfg_->m_slam->m_map.m_fMapKFAnglStep);
#pragma endregion

#pragma region "位置相关"
            /*设定位姿参数*/
            saveParam_<float>("laserPose_x", c_MasterCfg_->m_slam->m_pos.m_stSetPose.x());
            saveParam_<float>("laserPose_y", c_MasterCfg_->m_slam->m_pos.m_stSetPose.y());
            saveParam_<float>("laserPose_z", c_MasterCfg_->m_slam->m_pos.m_stSetPose.z());
            saveParam_<float>("laserPose_r", c_MasterCfg_->m_slam->m_pos.m_stSetPose.roll());
            saveParam_<float>("laserPose_p", c_MasterCfg_->m_slam->m_pos.m_stSetPose.pitch());
            saveParam_<float>("laserPose_a", c_MasterCfg_->m_slam->m_pos.m_stSetPose.yaw());
            saveParam_<bool>("savePath", c_MasterCfg_->m_slam->m_pos.m_bSaveWholePath);

            // hsq 保存建图定位初始位姿参数
            // saveParam_<double>("localization", "lidarRFU2CarBackRFU_x", c_MasterCfg_->m_slam->m_pos.m_stSetLidarRFU2CarBackRFUPose.x());
            // saveParam_<double>("localization", "lidarRFU2CarBackRFU_y", c_MasterCfg_->m_slam->m_pos.m_stSetLidarRFU2CarBackRFUPose.y());
            // saveParam_<double>("localization", "lidarRFU2CarBackRFU_z", c_MasterCfg_->m_slam->m_pos.m_stSetLidarRFU2CarBackRFUPose.z());
            // saveParam_<double>("localization", "lidarRFU2CarBackRFU_rollDegree", c_MasterCfg_->m_slam->m_pos.m_stSetLidarRFU2CarBackRFUPose.roll());
            // saveParam_<double>("localization", "lidarRFU2CarBackRFU_pitchDegree", c_MasterCfg_->m_slam->m_pos.m_stSetLidarRFU2CarBackRFUPose.pitch());
            // saveParam_<double>("localization", "lidarRFU2CarBackRFU_yawDegree", c_MasterCfg_->m_slam->m_pos.m_stSetLidarRFU2CarBackRFUPose.yaw());
            saveParam_<double>("localization", "startingPoint_lonDegree", c_MasterCfg_->m_slam->m_pos.m_stSetStartintPointGPS.x());
            saveParam_<double>("localization", "startingPoint_latDegree", c_MasterCfg_->m_slam->m_pos.m_stSetStartintPointGPS.y());
            saveParam_<double>("localization", "startingPoint_altDegree", c_MasterCfg_->m_slam->m_pos.m_stSetStartintPointGPS.z());
            saveParam_<int>("localization", "startingPoint_UTM_Code", c_MasterCfg_->m_slam->m_pos.m_UTMCode);
            saveParam_<double>("localization", "startingPoint_UTM_x", c_MasterCfg_->m_slam->m_pos.m_stSetStartintPointPose.x());
            saveParam_<double>("localization", "startingPoint_UTM_y", c_MasterCfg_->m_slam->m_pos.m_stSetStartintPointPose.y());
            saveParam_<double>("localization", "startingPoint_rollDegree", c_MasterCfg_->m_slam->m_pos.m_stSetStartintPointPose.roll());
            saveParam_<double>("localization", "startingPoint_pitchDegree", c_MasterCfg_->m_slam->m_pos.m_stSetStartintPointPose.pitch());
            saveParam_<double>("localization", "startingPoint_yawDegree", c_MasterCfg_->m_slam->m_pos.m_stSetStartintPointPose.yaw());

#pragma endregion

#pragma region "预处理相关"
            CurvFeatureConfig& l_pCurv = c_MasterCfg_->m_slam->m_prep.m_curv;
            using vInt = std::vector<int>;
            using vFloat = std::vector<float>;
            saveParam_<bool>(
                "prep", "FEXT", "P_uniSample", l_pCurv.m_filter.m_bUniSampleSurfFilter);
            saveParam_<int>(
                "prep", "FEXT", "P_uniSample_maxPoints", l_pCurv.m_filter.m_uiMaxSurfPntNum);
            saveParam_<vFloat>(
                "prep", "FEXT", "P_uniSample_range", l_pCurv.m_filter.m_fSampleRange);
            saveParam_<vFloat>(
                "prep", "FEXT", "P_uniSample_scale", l_pCurv.m_filter.m_fSampleScale);
            saveParam_<vInt>("prep", "FEXT", "using_type", l_pCurv.m_vUseStat);
            saveParam_<float>("prep", "FEXT", "bondary_Angle", l_pCurv.m_basic.m_fMaxBondaryAng);
            saveParam_<vFloat>("prep", "FEXT", "L_Angle", l_pCurv.m_basic.m_fCornAngRange);
            saveParam_<float>("prep", "FEXT", "P_Angle", l_pCurv.m_basic.m_fMinSurfAng);
            saveParam_<vFloat>("prep", "FEXT", "smallThing_Scale", l_pCurv.m_scale.m_fSmallScale);
            saveParam_<vFloat>(
                "prep", "FEXT", "feature_HeightRange", l_pCurv.m_scale.m_fFeatHighRange);
            saveParam_<vFloat>("prep", "FEXT", "L_DistRange", l_pCurv.m_scale.m_fCornDistRange);
            saveParam_<float>(
                "prep", "FEXT", "ground_SpaceHeight", l_pCurv.m_scale.m_fGroundZSpace);
            saveParam_<bool>(
                "prep", "FEXT", "intenSmooth_check", l_pCurv.m_smooth.m_bIntensitySmoothCheck);
            saveParam_<bool>(
                "prep", "FEXT", "LineSmooth_check", l_pCurv.m_smooth.m_bLineSmoothCheck);
            saveParam_<int>(
                "prep", "FEXT", "intenSmooth_diff", l_pCurv.m_smooth.m_uiMaxIntensityDiff);
            saveParam_<float>("prep", "FEXT", "lineSmooth_diff", l_pCurv.m_smooth.m_fMaxLineAng);
            saveParam_<float>(
                "prep", "FEXT", "intenSmooth_perct", l_pCurv.m_smooth.m_fIntenDiffPercent);
            saveParam_<float>(
                "prep", "FEXT", "lineSmooth_perct", l_pCurv.m_smooth.m_fAngDiffPercent);
            saveParam_<bool>(
                "prep", "FEXT", "L_verticFilter", l_pCurv.m_filter.m_bVerticalCornFilter);
            saveParam_<float>(
                "prep", "FEXT", "L_verticFilter_radius", l_pCurv.m_filter.m_fCornCyldRadius);
            saveParam_<float>(
                "prep", "FEXT", "L_verticFilter_height", l_pCurv.m_filter.m_fCornCyldMinHeight);
            saveParam_<int>(
                "prep", "FEXT", "L_verticFilter_miPoints", l_pCurv.m_filter.m_uiCornCyldMinPoints);

            CurbFeatureConfig& l_pCurb = c_MasterCfg_->m_slam->m_prep.m_curb;
            saveParam_<float>("prep", "FEXT", "curb_height", l_pCurb.m_fCurbHeight);
            saveParam_<float>("prep", "FEXT", "axis_ang", l_pCurb.m_fAxisAngleDiff);
            saveParam_<float>("prep", "FEXT", "ground_height", l_pCurb.m_fGroundClearance);

            TimeSyncSensorConfig& l_pSync = c_MasterCfg_->m_slam->m_prep.m_sync;
            saveParam_<int>("prep", "Sync", "MaxTimeBehind_ms", l_pSync.m_iMaxTimeBehind);
            saveParam_<int>("prep", "Sync", "MaxTimeSync_ms", l_pSync.m_iMaxTimeSync);
            saveParam_<int>("prep", "Sync", "MaxTimeDelay_ms", l_pSync.m_iMaxTimeDelay);

            saveParam_<int>("prep", "VisualCloudMode", c_MasterCfg_->m_slam->m_prep.m_iVisualCloud);
            std::vector<double> IMUNoise(3);
            IMUNoise[0] = c_MasterCfg_->m_slam->m_prep.m_imu.m_vAngularVelNoise.x();
            IMUNoise[1] = c_MasterCfg_->m_slam->m_prep.m_imu.m_vAngularVelNoise.y();
            IMUNoise[2] = c_MasterCfg_->m_slam->m_prep.m_imu.m_vAngularVelNoise.z();
            saveParam_<std::vector<double>>("prep", "imu", "angularVelNoise", IMUNoise);
#pragma endregion

#pragma region "里程计相关"
            saveParam_<u_int>("odom",
                              "match",
                              "PMaxPoints",
                              c_MasterCfg_->m_slam->m_odom.m_match.m_uiPlaneMaxPoints);
            saveParam_<float>("odom",
                              "match",
                              "PMaxRadius",
                              c_MasterCfg_->m_slam->m_odom.m_match.m_fPlaneMaxRadius);
            saveParam_<float>("odom",
                              "match",
                              "PMeanDiff",
                              c_MasterCfg_->m_slam->m_odom.m_match.m_fPlaneMeanDiff);
            saveParam_<u_int>("odom",
                              "match",
                              "LMinPoints",
                              c_MasterCfg_->m_slam->m_odom.m_match.m_uiLineMinPoints);
            saveParam_<float>("odom",
                              "match",
                              "LMaxRadius",
                              c_MasterCfg_->m_slam->m_odom.m_match.m_fLine2DRadius);
            saveParam_<float>(
                "odom", "match", "LMaxZDiff", c_MasterCfg_->m_slam->m_odom.m_match.m_fLineMaxZDiff);
            saveParam_<float>(
                "odom", "match", "maxDist", c_MasterCfg_->m_slam->m_odom.m_match.m_fMaxDist);
            saveParam_<bool>(
                "odom", "match", "onlySample", c_MasterCfg_->m_slam->m_odom.m_match.m_bSampleMatch);
            saveParam_<std::vector<float>>(
                "odom", "match", "Pvalid", c_MasterCfg_->m_slam->m_odom.m_match.m_vfPlanePCA);
            saveParam_<bool>("odom", "XYMoveMode", c_MasterCfg_->m_slam->m_odom.optimiz_o3D);
            saveParam_<float>("odom", "imuQuatPrec", c_MasterCfg_->m_slam->m_odom.m_fImuQuatPrec);

#pragma endregion

#pragma region "定位相关"
            saveParam_<float>(
                "locate", "speed_res", c_MasterCfg_->m_slam->m_loct.m_fTwistSmoothRes);
            saveParam_<float>("locate",
                              "match",
                              "PMeanDiff",
                              c_MasterCfg_->m_slam->m_loct.m_match.m_fPlaneMeanDiff);
            saveParam_<std::vector<float>>(
                "locate", "match", "Pvalid", c_MasterCfg_->m_slam->m_loct.m_match.m_vfPlanePCA);
            saveParam_<u_int>("locate",
                              "match",
                              "PMaxPoints",
                              c_MasterCfg_->m_slam->m_loct.m_match.m_uiPlaneMaxPoints);
            saveParam_<float>("locate",
                              "match",
                              "PMaxRadius",
                              c_MasterCfg_->m_slam->m_loct.m_match.m_fPlaneMaxRadius);
            saveParam_<u_int>("locate",
                              "match",
                              "LMinPoints",
                              c_MasterCfg_->m_slam->m_loct.m_match.m_uiLineMinPoints);
            saveParam_<float>("locate",
                              "match",
                              "LMaxRadius",
                              c_MasterCfg_->m_slam->m_loct.m_match.m_fLine2DRadius);
            saveParam_<float>("locate",
                              "match",
                              "LMaxZDiff",
                              c_MasterCfg_->m_slam->m_loct.m_match.m_fLineMaxZDiff);
            saveParam_<float>(
                "locate", "match", "maxDist", c_MasterCfg_->m_slam->m_loct.m_match.m_fMaxDist);
            saveParam_<bool>("locate",
                             "match",
                             "onlySample",
                             c_MasterCfg_->m_slam->m_loct.m_match.m_bSampleMatch);
            saveParam_<float>("locate",
                              "manual",
                              "PMeanDiff",
                              c_MasterCfg_->m_slam->m_loct.m_manual.m_fPlaneMeanDiff);
            saveParam_<std::vector<float>>(
                "locate", "manual", "Pvalid", c_MasterCfg_->m_slam->m_loct.m_manual.m_vfPlanePCA);
            saveParam_<u_int>("locate",
                              "manual",
                              "PMaxPoints",
                              c_MasterCfg_->m_slam->m_loct.m_manual.m_uiPlaneMaxPoints);
            saveParam_<float>("locate",
                              "manual",
                              "PMaxRadius",
                              c_MasterCfg_->m_slam->m_loct.m_manual.m_fPlaneMaxRadius);
            saveParam_<u_int>("locate",
                              "manual",
                              "LMinPoints",
                              c_MasterCfg_->m_slam->m_loct.m_manual.m_uiLineMinPoints);
            saveParam_<float>("locate",
                              "manual",
                              "LMaxRadius",
                              c_MasterCfg_->m_slam->m_loct.m_manual.m_fLine2DRadius);
            saveParam_<float>("locate",
                              "manual",
                              "LMaxZDiff",
                              c_MasterCfg_->m_slam->m_loct.m_manual.m_fLineMaxZDiff);
            saveParam_<float>(
                "locate", "manual", "maxDist", c_MasterCfg_->m_slam->m_loct.m_manual.m_fMaxDist);
            saveParam_<bool>("locate",
                             "manual",
                             "onlySample",
                             c_MasterCfg_->m_slam->m_loct.m_manual.m_bSampleMatch);
            saveParam_<bool>("locate", "XYMoveMode", c_MasterCfg_->m_slam->m_loct.optimiz_o3D);
            saveParam_<int>(
                "locate", "ManualFrameCnt", c_MasterCfg_->m_slam->m_loct.m_iManualFrameCnt);
#pragma endregion

#pragma region "建图相关"
            for (int l_cnt = 1; l_cnt < 5; ++l_cnt)
            {
                std::string l_stMatchTime = "match_" + std::to_string(l_cnt);
                saveParam_<float>("mapping",
                                  l_stMatchTime,
                                  "PMeanDiff",
                                  c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_fPlaneMeanDiff);
                saveParam_<std::vector<float>>(
                    "mapping",
                    l_stMatchTime,
                    "Pvalid",
                    c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_vfPlanePCA);
                saveParam_<u_int>("mapping",
                                  l_stMatchTime,
                                  "PMaxPoints",
                                  c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_uiPlaneMaxPoints);
                saveParam_<float>("mapping",
                                  l_stMatchTime,
                                  "PMaxRadius",
                                  c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_fPlaneMaxRadius);
                saveParam_<u_int>("mapping",
                                  l_stMatchTime,
                                  "LMinPoints",
                                  c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_uiLineMinPoints);
                saveParam_<float>("mapping",
                                  l_stMatchTime,
                                  "LMaxRadius",
                                  c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_fLine2DRadius);
                saveParam_<float>("mapping",
                                  l_stMatchTime,
                                  "LMaxZDiff",
                                  c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_fLineMaxZDiff);
                saveParam_<float>("mapping",
                                  l_stMatchTime,
                                  "maxDist",
                                  c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_fMaxDist);
                saveParam_<bool>("mapping",
                                 l_stMatchTime,
                                 "onlySample",
                                 c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_bSampleMatch);
            }

            saveParam_<bool>("enableLoop", c_MasterCfg_->m_slam->m_slam.m_bEnableLoop);
            saveParam_<bool>("mapping", "XYMoveMode", c_MasterCfg_->m_slam->m_slam.optimiz_o3D);

#pragma endregion

#pragma region "回环相关"
            // sc提取参数
            saveParam_<int>("mapping", "SCSector", c_MasterCfg_->m_slam->m_loop.m_iSector);
            saveParam_<int>("mapping", "SCRing", c_MasterCfg_->m_slam->m_loop.m_iRing);
            saveParam_<double>("mapping", "SCRadius", c_MasterCfg_->m_slam->m_loop.m_dRadius);
            saveParam_<float>("mapping", "SCRecongnizeThr", c_MasterCfg_->m_slam->m_loop.m_fSCthr);
            //回环匹配参数
            saveParam_<int>("mapping", "LoopOptTimes", c_MasterCfg_->m_slam->m_loop.m_iOptTimes);
            saveParam_<float>("mapping",
                              "LoopMaxAveDistance",
                              c_MasterCfg_->m_slam->m_loop.m_fMatchAveDistanceThr);
            saveParam_<float>("mapping",
                              "LoopMinMatchPercent",
                              c_MasterCfg_->m_slam->m_loop.m_fMatchMinNumPercentThr);
            saveParam_<float>(
                "mapping", "LoopVerifyScoreThr", c_MasterCfg_->m_slam->m_loop.m_fVerifyScoreThr);
            saveParam_<float>("mapping",
                              "LoopVerifyNumThr",
                              c_MasterCfg_->m_slam->m_loop.m_fVerifyMatchPercentThr);
            //粒子滤波参数
            saveParam_<float>(
                "mapping", "FilterRangeX", c_MasterCfg_->m_slam->m_loop.m_fParticleFilterRangeX);
            saveParam_<float>(
                "mapping", "FilterRangeY", c_MasterCfg_->m_slam->m_loop.m_fParticleFilterRangeY);
            saveParam_<float>(
                "mapping", "FilterRangeZ", c_MasterCfg_->m_slam->m_loop.m_fParticleFilterRangeZ);
            saveParam_<float>(
                "mapping", "FilterRangeA", c_MasterCfg_->m_slam->m_loop.m_fParticleFilterRangeA);
            saveParam_<int>(
                "mapping", "ParticleNums", c_MasterCfg_->m_slam->m_loop.m_iParticleNums);
            saveParam_<int>(
                "mapping", "AMCLFilterTimes", c_MasterCfg_->m_slam->m_loop.m_iParticleFilterTimes);
#pragma endregion
#pragma region "日志相关"
            saveParam_<std::string>("log", "log_path", c_MasterCfg_->m_slam->m_fae.m_sLogPath);
            saveParam_<bool>("log", "log_printf", c_MasterCfg_->m_slam->m_fae.m_bPrintfLog);
            saveParam_<bool>(
                "log", "log_printf_time", c_MasterCfg_->m_slam->m_fae.m_bPrintfTimeLog);
            saveParam_<bool>(
                "log", "log_printf_check", c_MasterCfg_->m_slam->m_fae.m_bPrintfCheckLog);
            saveParam_<bool>(
                "log", "save_error_local", c_MasterCfg_->m_slam->m_fae.m_bSaveErrorPoseLidar);
            saveParam_<uint32_t>("log", "log_size", c_MasterCfg_->m_slam->m_fae.m_uiLogFileSize);
            saveParam_<int>("log", "log_level", c_MasterCfg_->m_slam->m_fae.m_iLogLevel);
#pragma endregion

#pragma region "时间戳初始化相关"
            /*时间戳源*/
            saveParam_<int>("time", "TimeSource", int(c_MasterCfg_->m_slam->m_time.timeSource()));
#pragma endregion

#pragma region "master其他相关参数"
            /*lidar雷达模式 */
            saveParam_<bool>("master", "online_mode", c_MasterCfg_->m_slam->m_bIsOnlineMode);
            /*播包速度*/
            // saveParam_<uint32_t>("master", "play_bag_rate", c_MasterCfg_->m_uiPlayBagRate);
            /*离线数据路径*/
            saveParam_<std::string>("master", "lidar_data_path", c_MasterCfg_->m_sOffLineDataPath);
            /*默认保存pcap*/
            saveParam_<bool>("master", "default_save_pcap", c_MasterCfg_->m_bDefaultSavePcap);
            /*启动避障*/
            saveParam_<bool>("master", "use_od", c_MasterCfg_->m_bOpenObstacle);
            /*录制网卡时间间隔*/
            saveParam_<uint32_t>("master", "record_time", c_MasterCfg_->m_uiRecordTimeInterval);
            /*录制网卡PCAP最大数量*/
            saveParam_<uint32_t>("master", "maxNum_pcap", c_MasterCfg_->m_uiRecordMaxNum);
            saveParam_<uint32_t>("master", "maxNum_agvpcap", c_MasterCfg_->m_uiRecordAGVMaxNum);
            /*网络配置文件路径*/
            saveParam_<std::string>("master", "net_cfg_path", c_MasterCfg_->m_sNetCfgPath);
#pragma endregion

            /*打印*/
            // if(c_bLogValid_)
            //     c_MasterCfg_->m_slam->printf();
            l_file << "# -*- coding: UTF-8 -*-" << std::endl;
            l_file << "# 自动生成的文件:" << getTimeNowStr() << std::endl;
            l_file << "# 发行版参数配置文件" << std::endl;

            l_file << c_config << std::endl;
            l_file.close();
        }
        catch (std::exception& ex)
        {
            std::cerr << "Save Yaml File Failed !" << std::endl;
            return false;
        }
        return true;
    }

    void addLidarParams(s_LidarConfig& p_stLidar)
    {
        std::string l_sL = "lidarInfo";
        // 雷达ID号
        std::string l_sID = p_stLidar.m_sLaserName;
        addParam_<std::string>(l_sL, l_sID, "laser_type", p_stLidar.m_dev.m_sDevType);
        addParam_<std::string>(l_sL, l_sID, "laser_sn", p_stLidar.m_sLaserSN);
        setLaserFrameInfo(p_stLidar);
        // addParam_<bool>(l_sL, l_sID, "calibration_online", p_stLidar.m_bGetCalibrationOnline);
        addParam_<std::string>(l_sL, l_sID, "laser_ip", p_stLidar.m_dev.m_sDevIP);
        addParam_<uint16_t>(l_sL, l_sID, "laser_port", p_stLidar.m_dev.m_uiDevPort);
        addParam_<std::string>(l_sL, l_sID, "pc_ip", p_stLidar.m_dev.m_sLocalIP);
        addParam_<uint16_t>(l_sL, l_sID, "pc_port", p_stLidar.m_dev.m_uiLocalPort);
        addParam_<bool>(l_sL, l_sID, "use_multicast", p_stLidar.m_bUseMulticast);
        addParam_<std::string>(l_sL, l_sID, "multicast_ip", p_stLidar.m_dev.m_sMulticastIP);
        addParam_<std::string>(l_sL, l_sID, "net_name", p_stLidar.m_dev.m_sNetName);
        addParam_<float>(l_sL, l_sID, "laser_height", p_stLidar.m_fFeatureHeight);
        addParam_<bool>(l_sL, l_sID, "use_floor", p_stLidar.m_bUseFloor);
        addParam_<float>(l_sL, l_sID, "min_distance", p_stLidar.m_fMinDist);
        addParam_<float>(l_sL, l_sID, "max_distance", p_stLidar.m_fMaxDist);
        addParam_<std::string>(l_sL, l_sID, "pcap_name", p_stLidar.m_dev.m_sPcapName);

        // 存盲区
        std::string l_sBlind;
        std::vector<float> l_vBlindAngs(2, 0);
        for (u_int l_ibd = 0; l_ibd < 4; ++l_ibd)
        {
            p_stLidar.m_vBlindSector.resize(l_ibd + 1);
            l_vBlindAngs[0] = p_stLidar.m_vBlindSector[l_ibd].m_fStartAng;
            l_vBlindAngs[1] = p_stLidar.m_vBlindSector[l_ibd].m_fEndAng;
            if (l_vBlindAngs[0] <= l_vBlindAngs[1])
            {
                l_sBlind = "blind" + std::to_string(l_ibd);
                addParamFormat_<std::vector<float>>(
                    l_sL, l_sID, "blindInfo", l_sBlind, l_vBlindAngs);
            }
        }
        // addParam_<std::string>(l_sL,
        //                         l_sID,
        //                         "calibration_file",
        //                         p_stLidar.m_sCalibrationFile);
        // addParam_<uint32_t>(l_sL, l_sID, "min_pkt", p_stLidar.m_uiMinFramePkgNum);
        // addParam_<uint32_t>(l_sL, l_sID, "frame_sec", p_stLidar.m_uiFrameTimeMax);
        // addParam_<uint32_t>(l_sL, l_sID, "maxFrame_sec", p_stLidar.m_uiOldFrameTimeMax);
        // addParam_<uint16_t>(l_sL, l_sID, "min_num", p_stLidar.m_uiPointMinNum);
        // addParam_<u_int>(l_sL, l_sID, "laser_rpm", p_stLidar.m_uiRPM);
        // addParam_<float>(l_sL, l_sID, "dist_res", p_stLidar.m_fDistComp);
        // 存转移
        std::vector<double> l_vInstallInfo(6, 0);
        Eigen::VectorXd l_vInstallInfoX = p_stLidar.m_transToBase.coeffs_6();
        for (int i = 0; i < 6; ++i)
            l_vInstallInfo[i] = l_vInstallInfoX[i];
        addParamFormat_<std::vector<double>>(l_sL, l_sID, "transToBase", l_vInstallInfo);
        addParam_<bool>(l_sL, l_sID, "is_baseLaser", p_stLidar.m_bIsBaseLaser);
        addParamFormat_<std::vector<double>>(l_sL, l_sID, "DipAngle", p_stLidar.m_fDipAngle);
        addParamFormat_<std::vector<double>>(l_sL, l_sID, "Calibration", p_stLidar.m_fCalibration);
        addParam_<bool>(l_sL, l_sID, "laser_enable", p_stLidar.m_bEnable);

        // addParam_<std::string>(
        //     l_sL, l_sID, "bag_name",
        //     p_stLidar.m_dev.m_sBagName);

        // addParamFormat_<std::vector<int>>(l_sL, l_sID, "curbLine", p_stLidar.m_vCurbLine);
    }

    void saveLidarParam(s_LidarConfig& p_stLidar)
    {
        std::string l_sL = "lidarInfo";
        // 雷达ID号
        std::string l_sID = p_stLidar.m_sLaserName;
        saveParam_<std::string>(l_sL, l_sID, "laser_type", p_stLidar.m_dev.m_sDevType);
        // setLaserFrameInfo(p_stLidar);
        saveParam_<bool>(l_sL, l_sID, "calibration_online", p_stLidar.m_bGetCalibrationOnline);
        saveParam_<bool>(l_sL, l_sID, "is_baseLaser", p_stLidar.m_bIsBaseLaser);
        saveParam_<std::string>(l_sL, l_sID, "laser_sn", p_stLidar.m_sLaserSN);

        // saveParam_<std::string>(l_sL,
        //                         l_sID,
        //                         "calibration_file",
        //                         p_stLidar.m_sCalibrationFile);
        saveParam_<std::string>(l_sL, l_sID, "net_name", p_stLidar.m_dev.m_sNetName);
        saveParam_<std::string>(l_sL, l_sID, "laser_ip", p_stLidar.m_dev.m_sDevIP);
        saveParam_<uint32_t>(l_sL, l_sID, "min_pkt", p_stLidar.m_uiMinFramePkgNum);
        saveParam_<uint32_t>(l_sL, l_sID, "frame_sec", p_stLidar.m_uiFrameTimeMax);
        saveParam_<uint32_t>(l_sL, l_sID, "maxFrame_sec", p_stLidar.m_uiOldFrameTimeMax);
        saveParam_<uint16_t>(l_sL, l_sID, "laser_port", p_stLidar.m_dev.m_uiDevPort);
        saveParam_<uint16_t>(l_sL, l_sID, "pc_port", p_stLidar.m_dev.m_uiLocalPort);
        saveParam_<std::string>(l_sL, l_sID, "pc_ip", p_stLidar.m_dev.m_sLocalIP);
        saveParam_<std::string>(l_sL, l_sID, "multicast_ip", p_stLidar.m_dev.m_sMulticastIP);
        saveParam_<uint16_t>(l_sL, l_sID, "min_num", p_stLidar.m_uiPointMinNum);
        saveParam_<u_int>(l_sL, l_sID, "laser_rpm", p_stLidar.m_uiRPM);
        saveParam_<float>(l_sL, l_sID, "laser_height", p_stLidar.m_fFeatureHeight);
        saveParam_<float>(l_sL, l_sID, "dist_res", p_stLidar.m_fDistComp);
        saveParam_<bool>(l_sL, l_sID, "use_floor", p_stLidar.m_bUseFloor);
        saveParam_<bool>(l_sL, l_sID, "use_multicast", p_stLidar.m_bUseMulticast);
        saveParam_<bool>(l_sL, l_sID, "laser_enable", p_stLidar.m_bEnable);
        saveParam_<float>(l_sL, l_sID, "max_distance", p_stLidar.m_fMaxDist);
        saveParam_<float>(l_sL, l_sID, "min_distance", p_stLidar.m_fMinDist);
        saveParam_<std::string>(l_sL, l_sID, "pcap_name", p_stLidar.m_dev.m_sPcapName);
        // saveParam_<std::string>(
        //     l_sL, l_sID, "bag_name",
        //     p_stLidar.m_dev.m_sBagName);

        // 存盲区
        std::string l_sBlind;
        std::vector<float> l_vBlindAngs(2, 0);
        u_int l_uiSize = p_stLidar.m_vBlindSector.size();
        for (u_int l_ibd = 0; l_ibd < l_uiSize; ++l_ibd)
        {
            l_vBlindAngs[0] = p_stLidar.m_vBlindSector[l_ibd].m_fStartAng;
            l_vBlindAngs[1] = p_stLidar.m_vBlindSector[l_ibd].m_fEndAng;
            if (l_vBlindAngs[0] <= l_vBlindAngs[1])
            {
                l_sBlind = "blind" + std::to_string(l_ibd);
                saveParamFormat_<std::vector<float>>(
                    l_sL, l_sID, "blindInfo", l_sBlind, l_vBlindAngs);
            }
        }
        saveParamFormat_<std::vector<int>>(l_sL, l_sID, "curbLine", p_stLidar.m_vCurbLine);
        // 存转移
        std::vector<double> l_vInstallInfo(6, 0);
        Eigen::VectorXd l_vInstallInfoX = p_stLidar.m_transToBase.coeffs_6();
        for (int i = 0; i < 6; ++i)
            l_vInstallInfo[i] = l_vInstallInfoX[i];
        saveParamFormat_<std::vector<double>>(l_sL, l_sID, "transToBase", l_vInstallInfo);
        saveParamFormat_<std::vector<double>>(l_sL, l_sID, "DipAngle", p_stLidar.m_fDipAngle);
        saveParamFormat_<std::vector<double>>(l_sL, l_sID, "Calibration", p_stLidar.m_fCalibration);
    }

    /**
     * @description: 增加 雷达 配置
     * @param {string} p_sPkgPath 功能包路径
     * @param {s_LidarConfig} p_stLidar
     * @return {*} 外部验证新增配置的name是否唯一  否则将导致后续读取异常
     */
    bool addLidarParam(std::string p_sPkgPath, s_LidarConfig& p_stLidar)
    {
        // 必须有名
        if (p_stLidar.m_sLaserName == "")
            return false;
        bool res = false;
        /*默认读取 user 配置文件，不存在user时 读取 默认配置文件*/
        if (checkYaml_(p_sPkgPath + "/config/user.yaml"))
            configFile = p_sPkgPath + "/config/user.yaml";
        else if (checkYaml_(p_sPkgPath + "/config/default.yaml"))
            configFile = p_sPkgPath + "/config/default.yaml";
        else
        {
            faeLog(WERROR,
                   "增加雷达配置失败：配置文件格式错误或不存在，请确认配置文件是否存在！",
                   NULL);
            // c_MasterCfg_->m_slam->m_fae.setErrorCode("K11");
            return res;
        }
        c_config = YAML::LoadFile(configFile);
        // 判断是否存在同名配置 存在则删除
        std::string l_sDeleteLidarName = p_stLidar.m_sLaserName;
        YAML::Node l_lidarInfo = c_config["LaserConfig"]["lidarInfo"];
        // 存在同名id 推出
        if (l_lidarInfo[l_sDeleteLidarName])
        {
            faeLog(WERROR, "增加雷达配置失败：新增雷达名称已存在，请确认雷达名称是否重复！", NULL);
            // c_MasterCfg_->m_slam->m_fae.setErrorCode("K12");
            return false;
        }

        addLidarParams(p_stLidar);
        std::ofstream l_file(configFile);  // 有则打开，没有则创建
        if (l_file.is_open())
        {
            l_file << c_config << std::endl;
            l_file.close();
            res = true;
        }
        else
        {
            faeLog(WERROR, "增加雷达配置失败：文件错误，请检查配置文件！", NULL);
            // c_MasterCfg_->m_slam->m_fae.setErrorCode("K13");
        }
        return res;
    }

    /**
     * @description: 删除 雷达 配置
     * @param {string} p_sPkgPath 功能包路径
     * @param {s_LidarConfig} p_stLidar
     * @return {*}
     */
    bool deleteLidarParam(std::string p_sPkgPath, s_LidarConfig& p_stLidar)
    {
        bool res = false;
        /*默认读取 user 配置文件，不存在user时 读取 默认配置文件*/
        if (checkYaml_(p_sPkgPath + "/config/user.yaml"))
            configFile = p_sPkgPath + "/config/user.yaml";
        else if (checkYaml_(p_sPkgPath + "/config/default.yaml"))
            configFile = p_sPkgPath + "/config/default.yaml";
        else
        {
            faeLog(WERROR, "删除雷达配置失败：配置文件格式错误或不存在，请检查配置文件！", NULL);
            // c_MasterCfg_->m_slam->m_fae.setErrorCode("K14");
            return res;
        }
        c_config = YAML::LoadFile(configFile);
        // 判断是否存在对应名字的雷达 存在则删除
        std::string l_sDeleteLidarName = p_stLidar.m_sLaserName;
        YAML::Node l_lidarInfo = c_config["LaserConfig"]["lidarInfo"];
        if (l_lidarInfo[l_sDeleteLidarName])
        {
            l_lidarInfo.remove(l_sDeleteLidarName);  // 只可以使用字符串删除 且这个node为引用
                                                     // 主node相同区域同样被删除
            std::ofstream l_file(configFile);  // 有则打开，没有则创建
            if (l_file.is_open())
            {
                l_file << c_config << std::endl;
                l_file.close();
                res = true;
            }
            else
            {
                faeLog(WERROR, "删除雷达配置失败：文件错误，请确认是否更改过配置文件！", NULL);
                // c_MasterCfg_->m_slam->m_fae.setErrorCode("K15");
            }
        }
        else
        {
            // c_MasterCfg_->m_slam->m_fae.setErrorCode("K16");
            faeLog(WERROR,
                   "删除雷达配置失败：未找到雷达 {} 配置，请确认是否更改过配置文件！",
                   l_sDeleteLidarName.c_str());
        }
        return res;
    }

    /**
     * @description: 读取上次定位存储位姿
     * @param {s_POSE6D} &
     * @return {*}
     */
    void readPose_(s_POSE6D& p_sPose, std::string p_sFilePathHeader)
    {
        if (!readPoseInFile_(p_sPose, p_sFilePathHeader + "/data/Pose/pose.csv"))
            readPoseInFile_(p_sPose, p_sFilePathHeader + "/data/Pose/pose_bak.csv");
    }

    /**
     * @description: 从文件读取上次定位存储位姿
     * @param {s_POSE6D} &
     * @param {string} p_sFilePath
     * @return {*}
     */
    bool readPoseInFile_(s_POSE6D& p_sPose, std::string p_sFilePath)
    {
        bool l_bloadSucc = false;

        std::fstream l_filePoseR;
        l_filePoseR.open(p_sFilePath.c_str(), std::ios::in);
        if (l_filePoseR.is_open())
        {
            std::string l_strLine;
            while (!l_filePoseR.eof())
            {
                getline(l_filePoseR, l_strLine);
                float tx, ty, tz, tr, tp, ta;
                int l_writeSuc = 0;

                if (7
                    == sscanf(l_strLine.c_str(),
                              "%f,%f,%f,%f,%f,%f,%d",
                              &tx,
                              &ty,
                              &tz,
                              &tr,
                              &tp,
                              &ta,
                              &l_writeSuc))
                {
                    if (l_writeSuc == 1)
                    {
                        p_sPose.setX(tx);
                        p_sPose.setY(ty);
                        p_sPose.setZ(tz);
                        p_sPose.setRPY(tr, tp, ta);
                        p_sPose.m_bFlag = PoseStatus::SettingPose;
                        l_bloadSucc = true;
                        break;
                    }
                    else
                        l_bloadSucc = false;
                }
                else
                    l_bloadSucc = false;
            }
        }
        l_filePoseR.close();
        return l_bloadSucc;
    }

    /**
     * @description: 获取默认wanji_data路径
     * @param {s_POSE6D} &
     * @param {string} p_sFilePath
     * @return {*}
     */
    std::string getDefaultLidarDataPath(std::string p_sPkgPath)
    {
        std::string l_sDataPath = "";
        l_sDataPath = p_sPkgPath.erase(p_sPkgPath.size() - 10, 10) + "wanji_data/";
        return l_sDataPath;
    }

    void setLaserFrameInfo(s_LidarConfig& p_stLidar)
    {
        std::string l_sLaserType = p_stLidar.m_dev.m_sDevType;
        if ("WLR720A" == l_sLaserType || "WLR720F" == l_sLaserType || "WLR720FCW" == l_sLaserType)
        {
            p_stLidar.m_uiFramePkgNum = 120;
            p_stLidar.m_uiFramePktSize = 1260;
            p_stLidar.m_uiFrameTime = 100;
            // 雷达0deg和盲区坐标系设定0deg偏移量
            if ("WLR720A" == l_sLaserType || "WLR720F" == l_sLaserType)
            {
                p_stLidar.m_bIsAntiRotate = true;
                p_stLidar.m_uiBlindZeroDegOffset = 270;
            }
            else if ("WLR720FCW" == l_sLaserType)
            {
                p_stLidar.m_bIsAntiRotate = false;
                p_stLidar.m_uiBlindZeroDegOffset = 270;
            }
        }
        else if ("WLR720F_NP" == l_sLaserType || "WLR720FCW_NP" == l_sLaserType)
        {
            p_stLidar.m_uiFramePkgNum = 120;
            p_stLidar.m_uiFramePktSize = 1235;
            p_stLidar.m_uiFrameTime = 100;
        }
        else if ("WLR720C" == l_sLaserType)
        {
            p_stLidar.m_uiFramePkgNum = 100;
            p_stLidar.m_uiFramePktSize = 1253;
            p_stLidar.m_uiFrameTime = 100;
        }
        else
        {
            faeLog(WERROR,
                   "雷达类型 [{}] 设置异常 | 未定义此类型，请确认雷达类型后重试！",
                   l_sLaserType.c_str());
            // c_MasterCfg_->m_slam->m_fae.setErrorCode("C14");
        }
    }

    /**
     * @description: 根据设备类型，设置数据最小长度
     * @param {s_DevCfg&} p_dev
     * @return {*}
     * @other: 用于离线驱动PCAP过滤，暂时雷达720为1260，协议更改后待定，轮式SICK为1
     */
    void setDataMinLen(s_DevCfg& p_dev)
    {
        if ("WLR720FCW" == p_dev.m_sDevType || "WLR720A" == p_dev.m_sDevType
            || "WLR720F" == p_dev.m_sDevType)
            p_dev.m_uiDataMinLen = 1260;
        else if ("WLR720F_NP" == p_dev.m_sDevType || "WLR720FCW_NP" == p_dev.m_sDevType)
            p_dev.m_uiDataMinLen = 1235;
        else if ("WLR720C" == p_dev.m_sDevType)
            p_dev.m_uiDataMinLen = 1253;
        else if ("WheelSick" == p_dev.m_sDevType)
            p_dev.m_uiDataMinLen = 1;
        else
        {
            faeLog(WERROR, "设备类型 [{}] 设置异常 | 未定义此类型", p_dev.m_sDevType.c_str());
            // c_MasterCfg_->m_slam->m_fae.setErrorCode("K18");
        }
    }

    /**
     * @description: 只有1个分割符c, 将s 按照分割符c 切割写入v
     * @param {string}  s 字符串
     * @param {vector<string>} v 输出列表
     * @param {string}  c 分割符
     * @return {*} 注意： 此函数 如果字符串为lidar_0 分隔符为lidar_  则将输出2个字符串 空字符 和0
     */
    void splitString_(const std::string& s, std::vector<std::string>& v, const std::string& c)
    {
        std::string::size_type pos1, pos2;
        pos2 = s.find(c);
        pos1 = 0;
        while (std::string::npos != pos2)
        {
            v.push_back(s.substr(pos1, pos2 - pos1));

            pos1 = pos2 + c.size();
            pos2 = s.find(c, pos1);
        }
        if (pos1 != s.length())
            v.push_back(s.substr(pos1));
    }

    /**
     * @description: 字符串提取雷达id  eg lidar_0  -> 0
     * @param {string}  p_sLidarId 字符串
     * @param {string} v 输出id
     * @return {bool}  是否提取成功
     */
    bool splitToId_(std::string p_sLidarId, int& p_iId)
    {
        int l_iId = -1;
        std::vector<std::string> l_vStr;
        splitString_(p_sLidarId, l_vStr, "_");
        if (l_vStr.size() == 2)
        {
            if (l_vStr[0] == "lidar")
                l_iId = atoi(l_vStr[1].c_str());
        }
        if (l_iId != -1)
        {
            p_iId = l_iId;
            return true;
        }
        return false;
    }

    /**
     * @description: 雷达名是否重复
     * @param {string}  p_sName 当检查雷达名
     * @param {vector<s_LidarConfig>} p_vLidar 当前雷达信息列表
     * @return {bool}  雷达列表为空时 默认不重复，雷达名为空字符 默认重复
     */
    bool isNameRepeat_(std::string p_sName, std::vector<s_LidarConfig>& p_vLidar)
    {
        if (p_vLidar.empty())
            return false;

        if (p_sName == "")
        {
            // printf("isNameRepeat1: %s\n", p_sName.c_str());
            return true;
        }

        for (uint32_t i = 0; i < p_vLidar.size(); i++)
        {
            if (p_vLidar[i].m_sLaserName == p_sName)
            {
                // printf("isNameRepeat2: %s\n", p_sName.c_str());
                return true;
            }
        }
        return false;
    }

    /**
     * @description: 雷达id是否重复
     * @param {string}  p_sName 当检查雷达id
     * @param {vector<s_LidarConfig>} p_vLidar 当前雷达信息列表
     * @return {bool}  雷达列表为空时 默认不重复，雷达名为空字符 默认重复
     */
    bool isIdRepeat_(uint32_t p_uiId, std::vector<s_LidarConfig>& p_vLidar)
    {
        if (p_vLidar.empty())
            return false;
        for (uint32_t i = 0; i < p_vLidar.size(); i++)
        {
            if (p_vLidar[i].id == p_uiId)
                return true;
        }
        return false;
    }

    uint32_t getOnlyId_(std::vector<s_LidarConfig>& p_vLidar)
    {
        uint32_t l_id = 0;
        while (1)
        {
            uint32_t i = 0;
            for (i = 0; i < p_vLidar.size(); i++)
            {
                if (p_vLidar[i].id == l_id)
                {
                    l_id++;
                    break;
                }
            }
            // 认为id未重复
            if (i == p_vLidar.size())
                break;
        }
        return l_id;
    }

    /**
     * @description: 获取/etc/netplan/下的网络配置文件
     * @param {string} p_sYamlPath 文件路径
     * @return {bool} 返回任意1个yaml 逻辑简单
     */
    bool autoFindNetCfgYaml_(std::string& p_sYamlPath)
    {
        std::vector<std::string> l_vsYamlList;
        // yaml默认只有1个yaml ，程序将自动备份 yaml.bak
        findFileInFolder_("/etc/netplan/", "yaml", l_vsYamlList);
        if (!l_vsYamlList.empty())
        {
            p_sYamlPath = "/etc/netplan/" + l_vsYamlList[0];
            faeLog(WINFO, "网络配置文件不存在，自动获取成功：[{}] ", p_sYamlPath.c_str());
            if (l_vsYamlList.size() > 1)
                faeLog(WWARN, "存在多个网络配置文件，默认选择：[{}] ", l_vsYamlList[0].c_str());
            return true;
        }
        else
        {
            faeLog(WERROR, "网络配置文件[{}] 不存在，自动获取失败", p_sYamlPath.c_str());
            // c_MasterCfg_->m_slam->m_fae.setErrorCode("K19");
            return false;
        }
    }

    void renewParamByWorkMode(std::string p_sPkgPath)
    {
        c_MasterCfg_->m_slam->m_map.setModeDefault(c_MasterCfg_->m_slam->m_iWorkMode,
                                                   c_MasterCfg_->m_slam->m_nParam_mode);
        c_MasterCfg_->m_slam->m_odom.setModeDefault(c_MasterCfg_->m_slam->m_iWorkMode,
                                                    c_MasterCfg_->m_slam->m_nParam_mode);
        c_MasterCfg_->m_slam->m_loct.setModeDefault(c_MasterCfg_->m_slam->m_iWorkMode,
                                                    c_MasterCfg_->m_slam->m_nParam_mode);
        c_MasterCfg_->m_slam->m_slam.setModeDefault(c_MasterCfg_->m_slam->m_iWorkMode,
                                                    c_MasterCfg_->m_slam->m_nParam_mode);
        std::string l_sYamlPath = p_sPkgPath + "/config/user.yaml";
        if (isExistFileOrFolder(l_sYamlPath))
        {
            c_config = YAML::LoadFile(l_sYamlPath);
            loadParam_<float>("map", "map_size", c_MasterCfg_->m_slam->m_map.m_fMapRange);
            loadParam_<float>("map", "map_pathGrid", c_MasterCfg_->m_slam->m_map.m_fMapKFPoseGrid);
            loadParam_<float>("map", "map_maxRange", c_MasterCfg_->m_slam->m_map.m_fMapMaxRange);
            loadParam_<float>("map", "map_pathRange", c_MasterCfg_->m_slam->m_map.m_fMapKFSubRange);

            loadParam_<u_int>("odom",
                              "match",
                              "PMaxPoints",
                              c_MasterCfg_->m_slam->m_odom.m_match.m_uiPlaneMaxPoints);
            loadParam_<float>("odom",
                              "match",
                              "PMaxRadius",
                              c_MasterCfg_->m_slam->m_odom.m_match.m_fPlaneMaxRadius);
            loadParam_<float>("odom",
                              "match",
                              "PMeanDiff",
                              c_MasterCfg_->m_slam->m_odom.m_match.m_fPlaneMeanDiff);
            loadParam_<u_int>("odom",
                              "match",
                              "LMinPoints",
                              c_MasterCfg_->m_slam->m_odom.m_match.m_uiLineMinPoints);
            loadParam_<float>("odom",
                              "match",
                              "LMaxRadius",
                              c_MasterCfg_->m_slam->m_odom.m_match.m_fLine2DRadius);
            loadParam_<float>(
                "odom", "match", "LMaxZDiff", c_MasterCfg_->m_slam->m_odom.m_match.m_fLineMaxZDiff);
            loadParam_<float>(
                "odom", "match", "maxDist", c_MasterCfg_->m_slam->m_odom.m_match.m_fMaxDist);
            loadParam_<bool>(
                "odom", "match", "onlySample", c_MasterCfg_->m_slam->m_odom.m_match.m_bSampleMatch);
            loadParam_<std::vector<float>>(
                "odom", "match", "Pvalid", c_MasterCfg_->m_slam->m_odom.m_match.m_vfPlanePCA);

            loadParam_<u_int>("locate",
                              "match",
                              "PMaxPoints",
                              c_MasterCfg_->m_slam->m_loct.m_match.m_uiPlaneMaxPoints);
            loadParam_<float>("locate",
                              "match",
                              "PMaxRadius",
                              c_MasterCfg_->m_slam->m_loct.m_match.m_fPlaneMaxRadius);
            loadParam_<float>("locate",
                              "match",
                              "PMeanDiff",
                              c_MasterCfg_->m_slam->m_loct.m_match.m_fPlaneMeanDiff);
            loadParam_<std::vector<float>>(
                "locate", "match", "Pvalid", c_MasterCfg_->m_slam->m_loct.m_match.m_vfPlanePCA);
            loadParam_<u_int>("locate",
                              "match",
                              "LMinPoints",
                              c_MasterCfg_->m_slam->m_loct.m_match.m_uiLineMinPoints);
            loadParam_<float>("locate",
                              "match",
                              "LMaxRadius",
                              c_MasterCfg_->m_slam->m_loct.m_match.m_fLine2DRadius);
            loadParam_<float>("locate",
                              "match",
                              "LMaxZDiff",
                              c_MasterCfg_->m_slam->m_loct.m_match.m_fLineMaxZDiff);
            loadParam_<float>(
                "locate", "match", "maxDist", c_MasterCfg_->m_slam->m_loct.m_match.m_fMaxDist);
            loadParam_<bool>("locate",
                             "match",
                             "onlySample",
                             c_MasterCfg_->m_slam->m_loct.m_match.m_bSampleMatch);

            for (int l_cnt = 1; l_cnt < 5; ++l_cnt)
            {
                std::string l_stMatchTime = "match_" + std::to_string(l_cnt);
                loadParam_<u_int>("mapping",
                                  l_stMatchTime,
                                  "PMaxPoints",
                                  c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_uiPlaneMaxPoints);
                loadParam_<float>("mapping",
                                  l_stMatchTime,
                                  "PMaxRadius",
                                  c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_fPlaneMaxRadius);
                loadParam_<float>("mapping",
                                  l_stMatchTime,
                                  "PMeanDiff",
                                  c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_fPlaneMeanDiff);
                loadParam_<u_int>("mapping",
                                  l_stMatchTime,
                                  "LMinPoints",
                                  c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_uiLineMinPoints);
                loadParam_<float>("mapping",
                                  l_stMatchTime,
                                  "LMaxRadius",
                                  c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_fLine2DRadius);
                loadParam_<float>("mapping",
                                  l_stMatchTime,
                                  "LMaxZDiff",
                                  c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_fLineMaxZDiff);
                loadParam_<float>("mapping",
                                  l_stMatchTime,
                                  "maxDist",
                                  c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_fMaxDist);
                loadParam_<bool>("mapping",
                                 l_stMatchTime,
                                 "onlySample",
                                 c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_bSampleMatch);
                loadParam_<std::vector<float>>(
                    "mapping",
                    l_stMatchTime,
                    "Pvalid",
                    c_MasterCfg_->m_slam->m_slam.m_match[l_cnt].m_vfPlanePCA);
            }
        }
        else
        {
            faeLog(WERROR, "更新参数配置文件 [user.yaml] 加载失败，请检查user文件!", NULL);
        }
    }

    Param(bool p_bLogValid = true)
    {
        // 日志是否实例化 注意：若日志模块未实例化 须传入false
        c_bLogValid_ = p_bLogValid;
        c_MasterCfg_ = s_masterCfg::getIn();
    }
    ~Param() {}
};
}  // namespace wj_slam