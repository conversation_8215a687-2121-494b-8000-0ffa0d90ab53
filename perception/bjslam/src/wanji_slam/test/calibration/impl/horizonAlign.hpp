/**
 * @file horizonAlign.hpp
 * <AUTHOR> Li
 * @brief 地平校准接口模块
 * @version 0.1
 * @date 2023-07-20
 *
 * @copyright Copyright (c) 2023
 *
 */
#pragma once
#include "../horizonAlign.h"

namespace wj_slam {

template <typename P>
HorizonAlign<P>::HorizonAlign(SYSPARAM& p_stSysParam, int p_iWorkLidarID, int p_iAlignNum)
    : c_stSysParam_(p_stSysParam), c_stHAConfig_(p_stSysParam.m_calib.m_HACalib),
      c_iWorkLidarID_(p_iWorkLidarID), c_iAlignMin_(p_iAlignNum)
{
    LOGFAE(WINFO, "雷达 [{}] 地平校准启动中...", c_iWorkLidarID_);
    paramInit_();
}
template <typename P> HorizonAlign<P>::~HorizonAlign()
{
    c_pCAlign_ = nullptr;
}

template <typename P> void HorizonAlign<P>::paramInit_()
{
    c_pCAlign_.reset(new wj_cA::GroundContinueAlign());  //实例化continueAlign文件实现的类
    c_pCAlign_->setAlignTime(c_iAlignMin_);  //设置标定次数 外部传入:默认值20
}

template <typename P> void HorizonAlign<P>::start()
{
    std::lock_guard<std::mutex> l_mtx(c_runLock_);
    c_planAlign_ = std::thread(&wj_cA::GroundContinueAlign::run, c_pCAlign_);
    c_planAlign_.detach();
    c_iAlignNum_ = 0;
    c_bRun_ = true;
    c_iAlignState_ = (int)wj_cA::GroundContinueAlign::c_State::WAITDATA;
    c_stHAConfig_.m_data.m_iScanID = c_iAlignNum_;
    c_stHAConfig_.m_data.m_iTimeStamp = c_iAlignState_;  //? 这是为了传出去
    LOGFAE(WINFO, "雷达 [{}] 地平校准已开始!", c_iWorkLidarID_);
}
//! 这个函数似乎没有使用
template <typename P> void HorizonAlign<P>::stop()
{
    std::lock_guard<std::mutex> l_mtx(c_runLock_);
    c_bRun_ = false;
    LOGP(WINFO, "[HA] stop HorizonAlign-{}.", c_iWorkLidarID_);
}

template <typename P> void HorizonAlign<P>::shutdown()
{
    std::lock_guard<std::mutex> l_mtx(c_runLock_);
    c_bRun_ = false;
    c_pCAlign_->shutdown();
    c_stHAConfig_.m_data.m_iScanID = 0;
    c_stHAConfig_.m_data.m_iTimeStamp = 0;
    LOGFAE(WINFO, "雷达 [{}] 地平校准已关闭!", c_iWorkLidarID_);
}

template <typename P>
void HorizonAlign<P>::inputFeatureCallBack(int p_iLaserId, FEATURE_PAIR_PTR& p_featurePair)
{
    std::lock_guard<std::mutex> l_mtx(c_runLock_);
    if (c_bRun_ && p_iLaserId == c_iWorkLidarID_ && !c_pCAlign_->checkPushOver())
    {
        // 没有校准完成,添加到对应的序列中
        c_pCAlign_->setInputCloud(c_iAlignNum_, p_featurePair->allPC);  //逐帧输入点云至待处理队列
        c_iAlignNum_++;
    }
    c_iAlignState_ = c_pCAlign_->checkAlignState();
    c_stHAConfig_.m_mtRequestSend->lock();
    c_stHAConfig_.m_data.m_iScanID = c_iAlignNum_;
    c_stHAConfig_.m_data.m_iTimeStamp = c_iAlignState_;
    if (c_iAlignState_ != (int)wj_cA::GroundContinueAlign::c_State::InProgress)
    {
        //标定流程完毕,数据外传
        s_POSE6D l_tempTrans;
        l_tempTrans.setQuat(c_pCAlign_->getOutputQurt());  //获取地平校准的旋转矩阵
        s_POSE6D l_OldQT;
        l_OldQT.setRPY(c_stSysParam_.m_lidar[c_iWorkLidarID_].m_fDipAngle[0],
                       c_stSysParam_.m_lidar[c_iWorkLidarID_].m_fDipAngle[1],
                       0);
        l_OldQT = l_tempTrans * l_OldQT;
        c_stHAConfig_.m_data.m_stCalibData =
            l_OldQT.coeffs_6();  // trans X-Y-Z + pry roll-pitch-yaw
        c_stHAConfig_.m_data.m_stStdDev[0] = c_pCAlign_->getGroundHeight();
        c_stHAConfig_.m_data.m_stStdDev[1] = c_pCAlign_->getGroundAngle();
        LOGP(WDEBUG,
             "{} [HA] HorizonAlign-{} state:{} res:[{},{}], std:[{},{}]",
             WJLog::getWholeSysTime(),
             c_iWorkLidarID_,
             c_iAlignState_,
             c_stHAConfig_.m_data.m_stCalibData(3),  // roll
             c_stHAConfig_.m_data.m_stCalibData(4),  // pitch
             c_stHAConfig_.m_data.m_stStdDev[0],
             c_stHAConfig_.m_data.m_stStdDev[1]);
        testCorrectResult_(l_tempTrans.m_quat, p_featurePair->allPC);  // 根据校准矩阵旋转可视化点云
    }
    else
    {
        LOGP(WDEBUG,
             "{} [HA] HorizonAlign-{} state:{} data:[{}/{}].",
             WJLog::getWholeSysTime(),
             c_iWorkLidarID_,
             c_iAlignState_,
             c_iAlignNum_,
             c_iAlignMin_);
    }
    c_stHAConfig_.m_mtRequestSend->unlock();
    c_fEOutputCb_(p_featurePair);
}

template <typename P>
void HorizonAlign<P>::testCorrectResult_(Eigen::Quaterniond& p_roat, PC_PTR& p_pcInput)
{
    Eigen::Vector3d l_orgPnt_;
    int l_iCldSize = p_pcInput->points.size();
    for (int i = 0; i < l_iCldSize; i++)
    {
        l_orgPnt_ << p_pcInput->points[i].x, p_pcInput->points[i].y, p_pcInput->points[i].z;
        l_orgPnt_ = p_roat * l_orgPnt_;
        p_pcInput->points[i].x = l_orgPnt_.x();
        p_pcInput->points[i].y = l_orgPnt_.y();
        p_pcInput->points[i].z = l_orgPnt_.z();
    }
}

template <typename P> void HorizonAlign<P>::acceptResult_()
{
    LOGP(WINFO,
         "{} [HA] acceptResult HorizonAlign-{} res:[{},{}].",
         WJLog::getWholeSysTime(),
         c_iWorkLidarID_,
         c_stHAConfig_.m_data.m_stCalibData(3),
         c_stHAConfig_.m_data.m_stCalibData(4));
    Eigen::VectorXd& l_ThisRT = c_stHAConfig_.m_data.m_stCalibData;
    c_stSysParam_.m_lidar[c_iWorkLidarID_].m_fDipAngle[0] = l_ThisRT[3];
    c_stSysParam_.m_lidar[c_iWorkLidarID_].m_fDipAngle[1] = l_ThisRT[4];
}

}  // namespace wj_slam
#define WJSLAM_HorizonAlign(P) template class wj_slam::HorizonAlign<P>;