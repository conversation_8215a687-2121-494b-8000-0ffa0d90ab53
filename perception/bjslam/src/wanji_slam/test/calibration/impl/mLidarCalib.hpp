/**
 * @file mLidarCalib.hpp
 * <AUTHOR> Li
 * @brief 多雷达标定数据流模块
 * @version 1.0
 * @date 2023-07-28
 * @copyright Copyright (c)2023 Vanjee
 */
#pragma once
#include "../mLidarCalib.h"

namespace wj_slam {

template <typename P>
MLidarCalib<P>::MLidarCalib(SYSPARAM& p_stSysParam, int timeOffset)
    : c_stSysParam_(p_stSysParam), c_calibConfig_(c_stSysParam_.m_calib.m_MLCalib),
      c_iTimeOffset_(timeOffset), c_iLidarNum_(c_stSysParam_.m_iLidarNum),
      c_iBaseLidarID_(c_calibConfig_.m_iBaseLidarID)
{
    LOGO(WINFO, "{} [MLC] construct MultiLidarCalib.", WJLog::getWholeSysTime());
    paramInit_();
}
template <typename P> MLidarCalib<P>::~MLidarCalib()
{
    std::cout << "exit MLidarCalib" << std::endl;
}

template <typename P> void MLidarCalib<P>::paramInit_()
{
    c_iWorkLidarID_ = c_iBaseLidarID_;  //第一个工作雷达设置为基雷达,标号0
    c_iLastWorkLidarID_ = -1;           //上一个工作的雷达id先设置为-1
    c_bFastSwitcher_ = false;           // 快速切换开关设置为false
    c_bRun_ = false;                    // start() 运行开关 start()后置位true
    c_bRunOver_ = true;                 //指示一个执行的运行状态
    c_bShutdown_ = false;               //指示停止执行标志位
    c_bShutdownOver_ = false;           //指示线程已经停止退出标志

    // 初始化大小为雷达个数的vector容器,为每个雷达初始化独立序列
    c_vFeatureQueues_.resize(c_iLidarNum_);  // 存储对应雷达的关键帧队列queue
    c_vFakeOdometry_.resize(c_iLidarNum_);   //存储对应雷达的FakeOdometry类指针
    c_vOdomThread_.resize(c_iLidarNum_);     //存储对应雷达的odomtry::run线程
    c_vStaticTrajectory_.resize(c_iLidarNum_);  //存储对应雷达的静态位姿计算 StaticAnalysis类指针
    c_vInitPose_.resize(c_iLidarNum_);  //存储对应雷达的初始位姿
    c_vTrajectory_.resize(c_iLidarNum_);  //存储对应雷达的运动位姿计算 TrajectoryAnalysis类指针

    // 没有初始化则执行静态位姿初始化,否则使用设定的位姿
    c_bAllLidarInit_ = c_calibConfig_.checkInitial();
    if (!c_bAllLidarInit_)  //网页未设置位姿
    {
        LOGO(WDEBUG, "{} [MLC] start calib without initial poses.", WJLog::getWholeSysTime());
        // 重置标定参数记录
        c_calibConfig_.resetPoses(c_iLidarNum_);
        //给最少静态持续时间阈值赋值
        c_iMinStaticTimeForInit_ = c_calibConfig_.m_iMinTimeInit;
        //根据雷达个数实例化 StaticAnalysis 类对象指针,启用静止采集器采集
        for (int l_ilidar = 0; l_ilidar < c_iLidarNum_; ++l_ilidar)
        {
            //传入抖动时间阈值\速度阈值以及计算结果标准差阈值
            c_vStaticTrajectory_[l_ilidar].reset(
                new StaticAnalysis(c_calibConfig_.m_iMaxTimeReInit,
                                   c_calibConfig_.m_fInitVeloTHR,
                                   c_calibConfig_.m_fInitStdDevTHR[0],
                                   c_calibConfig_.m_fInitStdDevTHR[1]));
            //重置对应雷达位姿
            c_vInitPose_[l_ilidar].reset();
        }
        // 启用初始化线程
        c_initProcess_ = std::thread(&MLidarCalib<P>::initProcess_, this);
        c_initProcess_.detach();
    }
    else
    {
        LOGO(WDEBUG, "{} [MLC] start calib with initial poses.", WJLog::getWholeSysTime());
        // 使用预加载初始位置进行初始化
        for (int l_ilidar = 0; l_ilidar < c_iLidarNum_; ++l_ilidar)
        {
            //在设定完位姿后,完成粗标定,该雷达点云已经"基本"转移至基准雷达
            c_vInitPose_[l_ilidar].m_Pose.setVector6d(c_calibConfig_.m_data[0].m_stCalibData);
        }
    }

    // 启动虚拟里程计
    for (int l_ilidar = 0; l_ilidar < c_iLidarNum_; ++l_ilidar)
    {
        c_vFakeOdometry_[l_ilidar].reset(
            new FakeOdometry<P>(c_vFeatureQueues_[l_ilidar],
                                boost::bind(&MLidarCalib<P>::keyframeOutputCallback_, this, _1),
                                c_iTimeOffset_));
        // 清空容器
        std::queue<FEATURE_PAIR_PTR>().swap(c_vFeatureQueues_[l_ilidar]);
        //逐个雷达开启run线程
        c_vOdomThread_[l_ilidar] = std::thread(&FakeOdometry<P>::run, c_vFakeOdometry_[l_ilidar]);
        c_vOdomThread_[l_ilidar].detach();
    }
    // LOGO(WDEBUG, "{} [MLC] start calib first transform.", WJLog::getWholeSysTime());
}

template <typename P> void MLidarCalib<P>::start()
{
    LOGO(WINFO, "{} [MLC] starting MultiLidarCalib.", WJLog::getWholeSysTime());
    std::lock_guard<std::mutex> l_mtx1(c_featureLock_);   // input回调这里互锁
    std::lock_guard<std::mutex> l_mtx2(c_keyframeLock_);  // location回调 互锁
    for (int l_ilidar = 0; l_ilidar < c_iLidarNum_; ++l_ilidar)
    {
        //启动虚拟里程计
        c_vFakeOdometry_[l_ilidar]->start();
        // 若已经初始化完成
        if (c_bAllLidarInit_)
        {
            c_vFakeOdometry_[l_ilidar]->setInitPose(c_vInitPose_[l_ilidar]);
            LOGO(WINFO, "{} [MLC] {} setPose by init.", WJLog::getWholeSysTime(), l_ilidar);
            c_vInitPose_[l_ilidar].m_Pose.printf("setPose");
        }
        else if (PoseStatus::SettingPose == c_stSysParam_.m_pos.m_stSetPose.m_bFlag)
        {
            //!待确认
            s_PoseWithTwist l_stSetPose;
            l_stSetPose.m_Pose = c_stSysParam_.m_pos.m_stSetPose;
            c_vFakeOdometry_[l_ilidar]->setInitPose(l_stSetPose);
            LOGO(WINFO, "{} [MLC] {} setPose by manual.", WJLog::getWholeSysTime(), l_ilidar);
            l_stSetPose.m_Pose.printf("setPose");
        }
    }
    // 当完成初始化，开始进行快速切换
    if (c_bAllLidarInit_)
    {
        // 此时启动路径记录器
        for (int l_ilidar = 0; l_ilidar < c_iLidarNum_; ++l_ilidar)
            //实例化静态标定类 *0.1是转化为 100ms单位变化量
            c_vTrajectory_[l_ilidar].reset(
                new TrajectoryAnalysis(c_calibConfig_.m_iJumpDuration,
                                       c_calibConfig_.m_fMoveVeloTHR * 0.1,
                                       c_calibConfig_.m_fMaxRotSpeedTHR * 0.1,
                                       c_calibConfig_.m_fMinChangeDistance,
                                       c_calibConfig_.m_fMinChangeAng));

        c_pCalib_.reset(new MultiTrajectoryAnalysis(c_iLidarNum_, c_iBaseLidarID_));
        // 注入初始数据
        for (int l_ilidar = 0; l_ilidar < c_iLidarNum_; ++l_ilidar)
            c_pCalib_->renewPose(l_ilidar, 0, c_vInitPose_[l_ilidar].m_Pose);
        c_CalibProcess_ = std::thread(&MLidarCalib<P>::run_, this);
        c_CalibProcess_.detach();
        //开启工作雷达切换
        c_bFastSwitcher_ = true;
    }
    c_bRun_ = true;
    LOGO(WINFO, "{} [MLC] start MultiLidarCalib.", WJLog::getWholeSysTime());
}
template <typename P> void MLidarCalib<P>::stop()
{
    c_bRun_ = false;
    for (int l_ilidar = 0; l_ilidar < c_iLidarNum_; ++l_ilidar)
    {
        //逐个雷达停止虚拟里程计活动
        c_vFakeOdometry_[l_ilidar]->stop();
    }
    LOGO(WINFO, "{} [MLC] stop MultiLidarCalib.", WJLog::getWholeSysTime());
}

template <typename P> void MLidarCalib<P>::shutdown()
{
    c_bRun_ = false;
    c_bShutdown_ = true;
    for (int l_ilidar = 0; l_ilidar < c_iLidarNum_; ++l_ilidar)
    {
        c_vFakeOdometry_[l_ilidar]->shutDown();
    }
    // 保证已经结束
    while (!c_bShutdownOver_)
        sleepMs(100);
    LOGO(WINFO, "{} [MLC] shutdown MultiLidarCalib.", WJLog::getWholeSysTime());
    // 设置初始位置，预测为基准雷达的最新位置
    c_stSysParam_.m_pos.m_stSetPose = c_vFakeOdometry_[c_iBaseLidarID_]->getPrecisionPose().m_Pose;
    c_stSysParam_.m_pos.m_stSetPose.m_bFlag = PoseStatus::SettingPose;
}
template <typename P> bool MLidarCalib<P>::isStop()
{
    bool l_bIsStop = true;
    for (int l_ilidar = 0; l_ilidar < c_iLidarNum_; ++l_ilidar)
    {
        if (!c_vFakeOdometry_[l_ilidar]->isStop())
            l_bIsStop = false;
    }
    if (!c_bRunOver_)
        l_bIsStop = false;
    return l_bIsStop;
}
template <typename P> bool MLidarCalib<P>::setWorkMode(const int p_workMode)
{
    bool res = false;
    switch (p_workMode)
    {
        case WorkMode::LocatMode:
            stop();
            while (!isStop())
                sleepMs(10);
            start();
            res = true;
            break;
        default:
            stop();
            while (!isStop())
                sleepMs(10);
            res = true;
            break;
    }
    return res;
}
template <typename P> void MLidarCalib<P>::initProcess_()
{
    //该线程是未进行初始化的工作模式,也就是网页端没给雷达设定位姿,直接点击开始标定的情况
    c_bShutdownOver_ = false;
    // 如果关闭或者初始化完成则退出
    while (!c_bShutdown_ && !c_bAllLidarInit_)
    {
        // 如果运行则操作，不运行则休眠
        if (c_bRun_)
        {
            c_bRunOver_ = false;
            // 如果当前工作雷达静止时间已达到要求，认为初始化位置成功
            // todo:静止-->静止且正确:确保静止条件符合但位置没变,即容忍1000ms的抖动,但若以1000ms内的时间间隔移动AGV,静止时间仍可累积
            //获取当前工作雷达静止持续时间
            int l_iStaticTime = c_vStaticTrajectory_[c_iWorkLidarID_]->hasStaticTime();
            // 记录静止持续时间
            c_calibConfig_.m_data[c_iWorkLidarID_].m_iTimeStamp = l_iStaticTime;
            LOGO(WINFO,
                 "{} lidar-{}'s pose is static with {} ms.",
                 WJLog::getWholeSysTime(),
                 c_iWorkLidarID_,
                 l_iStaticTime);
            // 完成一个雷达的初始化工作
            if (l_iStaticTime > c_iMinStaticTimeForInit_
                && c_vStaticTrajectory_[c_iWorkLidarID_]->getLatestStaticMeanPose(
                       c_vInitPose_[c_iWorkLidarID_]))
            {
                // 如果没有全部初始化
                if (getNextWorkLidarID() != c_iBaseLidarID_)
                {
                    LOGO(WINFO,
                         "{} lidar-{} has initPose.",
                         WJLog::getWholeSysTime(),
                         c_iWorkLidarID_);
                    //记录上次的工作雷达
                    c_iLastWorkLidarID_ = c_iWorkLidarID_;
                    //获取下一个工作雷达
                    c_iWorkLidarID_ = getNextWorkLidarID();
                }
                // 已经全部初始化
                else
                {
                    // 设置完成初始化标志位
                    c_bAllLidarInit_ = true;
                    c_bRunOver_ = true;
                    // 销毁记录器
                    for (int l_ilidar = 0; l_ilidar < c_iLidarNum_; ++l_ilidar)
                    {
                        c_vStaticTrajectory_[l_ilidar] = nullptr;
                    }
                    LOGO(WINFO,
                         "{} All lidar has initPose.",
                         WJLog::getWholeSysTime(),
                         c_iWorkLidarID_);
                    setWorkMode(WorkMode::LocatMode);
                    return;
                }
            }
        }
        c_bRunOver_ = true;
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }
    c_bShutdownOver_ = true;
    LOGO(WINFO, "{} [MLC] shutdown calib init.", WJLog::getWholeSysTime());
    return;
}
template <typename P> void MLidarCalib<P>::run_()
{
    c_bShutdownOver_ = false;
    int l_iLastCalibNum = 0;
    std::vector<s_PoseWithTwist> l_newPoseList;
    Eigen::VectorXd l_pose = Eigen::VectorXd::Zero(6);
    Eigen::VectorXd l_dev = Eigen::VectorXd::Zero(6);
    while (!c_bShutdown_)
    {
        if (c_bRun_)
        {
            c_bRunOver_ = false;
            // LOGO(WERROR, "{} Renew path analysis queue.", WJLog::getWholeSysTime());
            // 将采集到的新数据加入分析队列
            for (int l_ilidar = 0; l_ilidar < c_iLidarNum_; ++l_ilidar)
            {
                l_newPoseList.clear();
                c_vTrajectory_[l_ilidar]->getNewPoseList(l_newPoseList);
                for (auto l_poseTwist : l_newPoseList)
                {
                    c_pCalib_->renewPose(
                        l_ilidar, int(l_poseTwist.m_tsSyncTime), l_poseTwist.m_Pose);
                }
            }
            c_pCalib_->calc();
            // LOGO(WERROR, "{} calc new calibData.", WJLog::getWholeSysTime());
            int l_iCalibNum = 0;
            for (int l_ilidar = 0; l_ilidar < c_iLidarNum_; ++l_ilidar)
            {
                l_pose = Eigen::VectorXd::Zero(6);
                l_dev = Eigen::VectorXd::Zero(6);
                // 获取非基准雷达的位置/速度 的均值和 标准差
                if (l_ilidar != c_iBaseLidarID_)
                    l_iCalibNum = c_pCalib_->getNewCalibData(l_ilidar, l_pose, l_dev);

                if (l_iCalibNum == l_iLastCalibNum)  //如果没更新,则跳过
                    continue;
                sendCalibData_(l_ilidar, l_pose, l_dev, l_iCalibNum);

                // LOGO(WINFO,
                //      "{} [MLC] ld:{}--Ts:{} newAdd: [{:.3f},{:.3f},{:.3f}]",
                //      WJLog::getWholeSysTime(),
                //      l_ilidar,
                //      l_iCalibNum,
                //      l_pose(0),
                //      l_pose(1),
                //      l_pose(5));
                // savePoseInFile_(l_ilidar, l_iCalibNum, l_refPoseNew, l_pose, l_dev);
            }
            l_iLastCalibNum = l_iCalibNum;
        }
        c_bRunOver_ = true;
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));  //休眠1s
    }
    c_bShutdownOver_ = true;
    LOGO(WINFO, "{} [MLC] shutdown calib.", WJLog::getWholeSysTime());
    return;
}
template <typename P>
void MLidarCalib<P>::sendCalibData_(int p_iLildar,
                                    Eigen::VectorXd& p_pose,
                                    Eigen::VectorXd& p_dev,
                                    int p_iCalibNum)
{
    // 计算结果
    s_POSE6D l_OldQT, l_NewQt, l_ResultQt;
    l_OldQT.setX(c_stSysParam_.m_lidar[p_iLildar].m_fCalibration[0]);
    l_OldQT.setY(c_stSysParam_.m_lidar[p_iLildar].m_fCalibration[1]);
    l_OldQT.setZ(c_stSysParam_.m_lidar[p_iLildar].m_fCalibration[2]);
    l_OldQT.setRPY(c_stSysParam_.m_lidar[p_iLildar].m_fCalibration[3],
                   c_stSysParam_.m_lidar[p_iLildar].m_fCalibration[4],
                   c_stSysParam_.m_lidar[p_iLildar].m_fCalibration[5]);
    l_NewQt.setX(p_pose[0]);
    l_NewQt.setY(p_pose[1]);
    l_NewQt.setZ(p_pose[2]);
    l_NewQt.setRPY(p_pose[3], p_pose[4], p_pose[5]);
    l_ResultQt = l_NewQt * l_OldQT;
    p_pose = l_ResultQt.coeffs_6();
    // 发送结果
    c_calibConfig_.m_mtRequestSend->lock();
    c_calibConfig_.m_data[p_iLildar].m_iScanID = p_iCalibNum;
    c_calibConfig_.m_data[p_iLildar].m_stCalibData = p_pose;
    c_calibConfig_.m_data[p_iLildar].m_stStdDev = p_dev;
    c_calibConfig_.m_mtRequestSend->unlock();
    LOGO(WINFO,
         "{} [MLC] {}--Ts:{} newPos: [{:.3f},{:.3f},{:.3f}]",
         WJLog::getWholeSysTime(),
         c_stSysParam_.m_lidar[p_iLildar].m_sLaserName,
         p_iCalibNum,
         p_pose(0),
         p_pose(1),
         p_pose(5));
    LOGO(WINFO,
         "{} [MLC] stddev: [{:.3f},{:.3f},{:.3f}]",
         WJLog::getWholeSysTime(),
         p_dev(0),
         p_dev(1),
         p_dev(5));
}

template <typename P>
void MLidarCalib<P>::inputFeatureCallBack(int p_iLaserId, FEATURE_PAIR_PTR& p_featurePair)
{
    std::lock_guard<std::mutex> l_mtx(c_featureLock_);
    if (p_iLaserId == c_iWorkLidarID_ && p_iLaserId != c_iLastWorkLidarID_)
    {
        //复用m_uiScanFrame区分是哪个雷达的数据
        p_featurePair->m_uiScanFrame = p_iLaserId;
        // 发布到对应的序列中
        c_vFeatureQueues_[p_iLaserId].push(p_featurePair);
        // LOGO(WDEBUG, "{} [MLC] input-{} at {}.", WJLog::getWholeSysTime(), p_iLaserId,
        // p_featurePair->m_dTimestamp); 当fastswith时必须切换发送
        if (c_bFastSwitcher_)
            c_iLastWorkLidarID_ = c_iWorkLidarID_;
    }
}

template <typename P> void MLidarCalib<P>::inputPoseTwistCallBack(s_PoseWithTwist& p_pose)
{
    // 若未完成初始化，进行记录
    if (!c_bAllLidarInit_)
    {
        //复用pose.m_iScanId区分雷达号,在Location中被赋值
        // 更新到对应的序列中
        c_vStaticTrajectory_[p_pose.m_iScanId]->renewPose(
            c_vFakeOdometry_[p_pose.m_iScanId]->renewPrecisionPose(p_pose));
    }
    else if (c_bFastSwitcher_)
    {
        // s_PoseWithTwist l_newLidarPose = c_vFakeOdometry_[p_pose.m_iScanId]->getPrecisionPose();
        // 更新到对应的序列中
        c_vTrajectory_[p_pose.m_iScanId]->renewPose(
            c_vFakeOdometry_[p_pose.m_iScanId]->renewPrecisionPose(p_pose));
        // 切换下一台雷达
        c_iWorkLidarID_ = getNextWorkLidarID();
    }
}

template <typename P> void MLidarCalib<P>::keyframeOutputCallback_(KEYFRAME_PTR p_keyframe)
{
    std::lock_guard<std::mutex> l_mtx(c_keyframeLock_);
    // 发布工作雷达帧
    c_keyFrameCb_(p_keyframe);
    // LOGO(WDEBUG,
    //      "{} [MLC] push-{} at {}.",
    //      WJLog::getWholeSysTime(),
    //      p_keyframe->m_pFeature->m_uiScanFrame,
    //      p_keyframe->m_pFeature->m_dTimestamp);
}

template <typename P> void MLidarCalib<P>::acceptResult_()
{
    for (int i = 0; i < c_iLidarNum_; ++i)
    {
        if (i == c_iBaseLidarID_)
            continue;
        Eigen::VectorXd& l_ThisRT = c_calibConfig_.m_data[i].m_stCalibData;
        for (int j = 0; j < 6; ++j)
            c_stSysParam_.m_lidar[i].m_fCalibration[i] = l_ThisRT[i];
    }
}
template <typename P>
void MLidarCalib<P>::savePoseInFile_(int p_iID,
                                     int p_iTimes,
                                     std::vector<s_PoseWithTwist>& p_stRefPose,
                                     Eigen::VectorXd& p_pose,
                                     Eigen::VectorXd& p_dev)
{
    static bool l_bSavePath = c_calibConfig_.m_bSaveProcess;
    static std::string p_sFilePath = c_calibConfig_.m_sProcessFile;
    static std::fstream l_filePoseWR;
    static bool l_bInit = true;
    if (l_bSavePath)
    {
        if (l_bInit || !l_filePoseWR.is_open())
        {
            l_bInit = false;
            l_filePoseWR.open(p_sFilePath.c_str(), std::ios::out | std::ios::trunc);
            LOGO(WINFO,
                 "{} Try Open {} for save calib process",
                 WJLog::getWholeSysTime(),
                 p_sFilePath);
            l_filePoseWR << "Timetamp,ref_x,ref_y,ref_yaw,Timetamp,ths_x,ths_y,ths_yaw,Lid,Times,"
                            "Tx,Ty,Tyaw,Tx_stdev,Ty_stdev,Tyaw_stdev"
                         << std::endl;
        }
        l_filePoseWR << p_stRefPose[0].m_tsSyncTime << "," << p_stRefPose[0].m_Pose.x() << ","
                     << p_stRefPose[0].m_Pose.y() << "," << p_stRefPose[0].m_Pose.yaw() << ","
                     << p_stRefPose[1].m_tsSyncTime << "," << p_stRefPose[1].m_Pose.x() << ","
                     << p_stRefPose[1].m_Pose.y() << "," << p_stRefPose[1].m_Pose.yaw() << ","
                     << p_iID << "," << p_iTimes << "," << p_pose[0] << "," << p_pose[1] << ","
                     << p_pose[5] << "," << p_dev[0] << "," << p_dev[1] << "," << p_dev[5]
                     << std::endl;
    }
}
}  // namespace wj_slam
#define WJSLAM_MLidarCalib(P) template class wj_slam::MLidarCalib<P>;
