/**
 * @file mLidarCalib.h
 * <AUTHOR> Li
 * @brief 多雷达标定数据流模块
 * @version 1.0
 * @date 2023-07-28
 * @copyright Copyright (c)2023 <PERSON>jee
 */
#pragma once
#include "algorithm/calibration/mLidarCalib/fakeOdometry.h"
#include "algorithm/calibration/mLidarCalib/trajectoryAnalysis.h"
#include "common/common_ex.h"
#include <boost/bind.hpp>
#include <boost/function.hpp>
#include <fstream>
#include <thread>

namespace wj_slam {

template <typename P> class MLidarCalib {
  public:
    using Ptr = boost::shared_ptr<MLidarCalib<P>>;

  private:
    using KEYFRAME_PTR = typename KEYFRAME<P>::Ptr;
    using FEATURE_PAIR_PTR = typename FEATURE_PAIR<P>::Ptr;
    using FE_OUTPUT_CB = boost::function<void(int, FEATURE_PAIR_PTR&)>;
    using LT_OUTPUT_CB = boost::function<void(s_PoseWithTwist&)>;
    using KF_OUTPUT_CB = boost::function<void(KEYFRAME_PTR)>;
    using FAKEODOM_PTR = typename FakeOdometry<P>::Ptr;
    using STATICANAL_PTR = typename StaticAnalysis::Ptr;
    using MOVEANAL_PTR = typename TrajectoryAnalysis::Ptr;
    using CALIB_PTR = typename MultiTrajectoryAnalysis::Ptr;

    std::vector<std::queue<FEATURE_PAIR_PTR>> c_vFeatureQueues_; /**< 不同雷达对应的关键帧队列*/
    std::vector<FAKEODOM_PTR> c_vFakeOdometry_;       /**< 不同雷达对应的虚拟里程计*/
    std::vector<STATICANAL_PTR> c_vStaticTrajectory_; /**< 不同雷达对应的静态定位处理器*/
    std::vector<MOVEANAL_PTR> c_vTrajectory_;  /**< 不同雷达对应的的动态定位处理器*/
    std::vector<s_PoseWithTwist> c_vInitPose_; /**< 不同嗯雷达初始位置容器*/

    std::thread c_initProcess_;              /**< 初始化线程*/
    std::vector<std::thread> c_vOdomThread_; /**< 各雷达虚拟里程计线程容器*/

    CALIB_PTR c_pCalib_;         /**< 多雷达标定实现类*/
    std::thread c_CalibProcess_; /**< 多雷达标定时时计算及获取结果线程*/

    KF_OUTPUT_CB c_keyFrameCb_;      /**< 关键帧输出回调-外部函数*/
    FE_OUTPUT_CB c_fEOutputCb_;      /**< 特征提取完毕回调-原始*/
    LT_OUTPUT_CB c_highPrecsPoseCb_; /**< 定位完毕调用回调-原始*/

    SYSPARAM& c_stSysParam_;         /**< 系统参数*/
    s_MLCalibConfig& c_calibConfig_; /**< 标定参数*/
    std::mutex c_featureLock_;       /**< 接受特征帧函数锁*/
    std::mutex c_keyframeLock_;      /**< 接受关键帧函数锁*/

    int c_iTimeOffset_;           /**< 点的时间戳偏移*/
    int c_iMinStaticTimeForInit_; /**< 静态初始化的最短时间限制[ms]*/

    int c_iLidarNum_;        /**< 雷达数量*/
    int c_iBaseLidarID_;     /**< 基准雷达号*/
    int c_iWorkLidarID_;     /**< 当前数据流工作雷达号*/
    int c_iLastWorkLidarID_; /**< 标定的上次工作中雷达号*/

    bool c_bAllLidarInit_; /**< 初始位姿初始化成功*/
    bool c_bFastSwitcher_; /**< 开始切换雷达开关*/
    bool c_bRun_;          /**< 运行开关*/
    bool c_bRunOver_;      /**< 运行情况指示*/
    bool c_bShutdown_;     /**< 关闭开关*/
    bool c_bShutdownOver_; /**< 关闭指示*/

  public:
    MLidarCalib(SYSPARAM& p_stSysParam, int timeOffset);
    ~MLidarCalib();
    void start();
    void stop();
    void shutdown();
    bool isStop();
    bool setWorkMode(const int p_iWorkMode);

    /**
     * @brief 设置工作雷达号
     * @param p_iWorkLidarID
     *
     */
    void setWorkLidarID(const int p_iWorkLidarID)
    {
        c_iWorkLidarID_ = p_iWorkLidarID;
    }

    /**
     * @brief 获取工作雷达号
     * @code
     *
     * @endcode
     * @return [int] \n
     * [当前工作雷达号]
     *
     */
    int getWorkLidarID()
    {
        return c_iWorkLidarID_;
    }

    /**
     * @brief 获取下一个工作雷达号
     * @code
     *
     * @endcode
     * @return [int] \n
     * [下一个工作雷达号]
     *
     */
    int getNextWorkLidarID()
    {
        return c_iWorkLidarID_ == c_stSysParam_.m_iLidarNum - 1 ? 0 : c_iWorkLidarID_ + 1;
    }

    /**
     * @brief 接受特征帧的回调函数
     * @param p_iLaserId
     * @param p_featurePair
     *
     */
    void inputFeatureCallBack(int p_iLaserId, FEATURE_PAIR_PTR& p_featurePair);

    /**
     * @brief 接受位姿的回调函数
     * @param p_pose
     *
     */
    void inputPoseTwistCallBack(s_PoseWithTwist& p_pose);

    /**
     * @brief 从外部获取关键帧输出函数
     * @param p_funKeyFrame
     *
     */
    void setKeyFrameOutputCallback(const KF_OUTPUT_CB p_funKeyFrame)
    {
        c_keyFrameCb_ = p_funKeyFrame;
    }

    /**
     * @description: 设置原有的特征帧回调
     * @param {FE_OUTPUT_CB} p_funFE
     * @return {*}
     * @other:
     */

    /**
     * @brief 暂时存储特征帧回调函数
     * @param p_funFE
     *
     */
    void setRawFeatureCallBack(const FE_OUTPUT_CB p_funFE)
    {
        c_fEOutputCb_ = p_funFE;
    }

    /**
     * @brief 暂时存储原有的位姿回调
     * @param p_funLT
     *
     */
    void setRawPoseCallBack(const LT_OUTPUT_CB p_funLT)
    {
        c_highPrecsPoseCb_ = p_funLT;
    }

    /**
     * @brief 获取原有的特征帧回调
     * @code
     *
     * @endcode
     * @return [FE_OUTPUT_CB] \n
     * [暂存在类内的函数]
     *
     */
    FE_OUTPUT_CB getRawFeatureCallBack()
    {
        return c_fEOutputCb_;
    }

    /**
     * @brief 获取原有的位姿回调
     * @code
     *
     * @endcode
     * @return [LT_OUTPUT_CB] \n
     * [暂存在类内的函数]
     *
     */
    LT_OUTPUT_CB getRawPoseCallBack()
    {
        return c_highPrecsPoseCb_;
    }

  private:
    /**
     * @brief 各数据流容器初始化
     *
     */
    void paramInit_();
    /**
     * @brief 静态初始化线程
     *
     */
    void initProcess_();

    /**
     * @brief 时时计算并获取标定结果线程
     *
     */
    void run_();
    /**
     * @brief 计算当前存储的数据的标定结果
     * @param p_iLildar
     * @param p_pose
     * @param p_dev
     * @param p_iCalibNum
     *
     */
    void
    sendCalibData_(int p_iLildar, Eigen::VectorXd& p_pose, Eigen::VectorXd& p_dev, int p_iCalibNum);

    /**
     * @brief 从fakeOdometry[X]中获取关键帧 数据交互
     * @param p_keyframe
     *
     */
    void keyframeOutputCallback_(KEYFRAME_PTR p_keyframe);
    /**
     * @brief 应用结果(未启用)
     *
     */
    void acceptResult_();
    /**
     * @brief 保存标定结果(未启用)
     * @param p_iID
     * @param p_iTimes
     * @param p_stRefPose
     * @param p_pose
     * @param p_dev
     *
     */
    void savePoseInFile_(int p_iID,
                         int p_iTimes,
                         std::vector<s_PoseWithTwist>& p_stRefPose,
                         Eigen::VectorXd& p_pose,
                         Eigen::VectorXd& p_dev);
};

}  // namespace wj_slam
#ifdef WJSLAM_NO_PRECOMPILE
#    include "impl/mLidarCalib.hpp"
#else
#    define WJSLAM_MLidarCalib(P) template class wj_slam::MLidarCalib<P>;
#endif