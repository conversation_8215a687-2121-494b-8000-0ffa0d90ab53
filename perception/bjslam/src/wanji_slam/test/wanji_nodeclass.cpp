/*
 * @Description:
 * @Version: 1.0
 * @Autor: zu<PERSON><PERSON>
 * @Date: 2021-04-27 19:17:34
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-28 15:59:18
 */

/*
 * This file is part of lslidar_n301 driver.
 *
 * The driver is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * The driver is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with the driver.  If not, see <http://www.gnu.org/licenses/>.
 */
#include "param.hpp"
#include "slam/slam.h"
#include <signal.h>
#include <termio.h>

// 日志头
generalLog* glog = nullptr;
// 通用日志
WJLog* generalLog::wjlog = nullptr;
// 路径日志
WJLog* generalLog::pathlog = nullptr;
// 校验日志
WJLog* generalLog::checklog = nullptr;
// 速度日志
WJLog* generalLog::speedlog = nullptr;
// 对外日志
WJLog* generalLog::faelog = nullptr;
#ifdef LOG_PREMOD
// 预处理日志
WJLog* generalLog::prlog = nullptr;
// 特征提取日志
WJLog* generalLog::felog = nullptr;
// 里程计日志
WJLog* generalLog::odlog = nullptr;
// 定位日志
WJLog* generalLog::lmlog = nullptr;
// 子图日志
WJLog* generalLog::submaplog = nullptr;
// 预估里程计日志
WJLog* generalLog::odfslog = nullptr;
// 驱动日志
WJLog* generalLog::drlog = nullptr;
// 解包子日志
WJLog* generalLog::convlog = nullptr;
// 协议日志
WJLog* generalLog::protolog = nullptr;
// web日志
WJLog* generalLog::weblog = nullptr;
#endif
#ifdef LOG_TIMECOST
// 耗时日志
WJLog* generalLog::tclog = nullptr;
#endif
void MySigintHandler(int sig)
{
    printf("closeSlam\n");
    closeSlam();
}

int main(int argc, char** argv)
{
    wj_slam::SYSPARAM* g_sysParam_;
    g_sysParam_ = wj_slam::SYSPARAM::getIn();

    // 读取参数 静态指针无须传参
    wj_slam::Param l_param(false);
    std::string path = ros::package::getPath("wj_slam");
    l_param.loadSysParam(path);

    printf("lidarName: %s\n", g_sysParam_->m_lidar[0].m_sLaserName.c_str());

    // 挂载日志模块
    glog = new generalLog(g_sysParam_->m_fae.m_sLogPath,
                          "wj",
                          FileMode::DAILY_ROTATE,
                          LogMode::TRIAL_MODE,
                          g_sysParam_->m_fae.getLogFileSizeByte());

    LOGW(WINFO, "****** Creat Log File at Time: {} ******", WJLog::getWholeSysTime());
    LOGW(WINFO,
         "{} ****** SLAM_VERSION: {} ******",
         WJLog::getWholeSysTime(),
         g_sysParam_->m_sVersion);
    // 重设日志等级
    glog->changeMode(g_sysParam_->m_fae.m_iLogLevel);
    LOGW(WINFO,
         "{} ****** Set Manual LogLevel: {} LogSize: {} MB******",
         WJLog::getWholeSysTime(),
         g_sysParam_->m_fae.m_iLogLevel,
         g_sysParam_->m_fae.m_uiLogFileSize);
    wjPrint(WJCOL_GREEN, "SLAM_VERSION", g_sysParam_->m_sVersion);

    ros::init(argc, argv, "wj_slam");
    ros::NodeHandle node;
    ros::Rate loop_rate(1000);
    signal(SIGINT, MySigintHandler);
    signal(SIGTERM, MySigintHandler);
    if (ros::ok())
    {
        openSlam(node, g_sysParam_->m_sPkgPath, NULL);
        while (ros::ok())
        {
            ros::spinOnce();
            loop_rate.sleep();
        }
        std::cout << "ros no ok!" << std::endl;
    }
    return 0;
}