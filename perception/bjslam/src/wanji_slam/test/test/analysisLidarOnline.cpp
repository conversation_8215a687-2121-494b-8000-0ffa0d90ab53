/**
 * @file analysisLidar.cpp
 * <AUTHOR>
 * @brief
 * @version 1.0
 * @date 2023-09-06
 */

#include "algorithm/preproc/driver/input.h"
#include "tool/fileTool/fileTool.h"
#include <fstream>
#include <thread>
#include <pcap.h>

constexpr static const int RAW_SCAN_SIZE = 4;     // 占用字节 （距离2 脉宽1 置信度1）
constexpr static const int SCANS_PER_BLOCK = 19;  // 发光通道19线
constexpr static const int BLOCK_DATA_SIZE =
    (SCANS_PER_BLOCK * RAW_SCAN_SIZE);              //  19*4 每块数据长度
constexpr static const int BLOCKS_PER_PACKET = 15;  // 120包每包15块 每块80字节
struct s_pcapInfo
{
    uint32_t m_uiSrcPort;
    uint32_t m_uiDstPort;
    std::string m_sSrcIP;
    std::string m_sDstIP;
    std::string m_sProtocalMode;  // 0x11[17] UDP  | 0x06[6] TCP
};
typedef struct RawBlock
{
    uint16_t header;
    uint16_t rotation;
    uint8_t data[BLOCK_DATA_SIZE];  // data[19*4]
} s_RawBlock;

typedef struct RawAddtionMsg
{
    uint16_t header;
    uint8_t rads[2];
    uint8_t time[6];
    uint8_t nsec[4];
    uint8_t angVel[6];
    uint8_t accel[6];
} s_RawAddtionMsg;

typedef struct RawPacket
{
    s_RawBlock blocks[BLOCKS_PER_PACKET];  // blocks[15]
    s_RawAddtionMsg addmsg;
} s_RawPacket;

std::string c_ErrorInfo[4] = {"雷达异常", "网络连接异常", "网络阻塞", "未定义"};

float getTimeDiffMs(timeval p_Time1, timeval p_Time2)
{
    float l_fTimeDiff =
        (p_Time1.tv_sec - p_Time2.tv_sec) * 1000 + (p_Time1.tv_usec - p_Time2.tv_usec) * 0.001;
    return l_fTimeDiff;
}

class analysisLidar {
  private:
    pcap_t* c_pacp_ = nullptr;
    bool c_bRun_ = false;
    bool c_bRunOver_ = false;
    struct pcap_pkthdr* c_stHeader_;
    const u_char* c_ucPktData_;
    int c_iID_ = -1;
    uint32_t c_uiOffset_ = 0;
    uint32_t c_uiDiscardNum_ = 30;
    boost::array<uint8_t, 1600> c_data_;
    int c_LastCircle_ = 0;
    bool c_bOutFile_ = false;
    timeval c_stLastT_, c_DataLast_, c_HeadLast_, c_CircleDataLast_, c_CircleHeadLast_;
    std::fstream c_filePoseWR_;
    char* c_path = getcwd(NULL, 0);
    std::string c_DirOuput_;
    s_pcapInfo c_stPcapInfo;

    bool analysisPcapHeader(const u_char* p_ucData, uint32_t p_iLen, s_pcapInfo& p_stPcapInfo)
    {
        if (!p_iLen)
            return false;
        int l_offset = 0;
        while (l_offset < (int)(p_iLen - 1))
        {
            // 找IP Header
            if (*(p_ucData + l_offset) == 0x45 && *(p_ucData + l_offset + 1) == 0)
            {
                int l_totalLen = *(p_ucData + l_offset + 2) << 8 | *(p_ucData + l_offset + 3);
                if (l_totalLen == (int)(p_iLen - l_offset))
                {
                    if (*(p_ucData + l_offset + 9) == 0x11)
                        p_stPcapInfo.m_sProtocalMode = "UDP";
                    else if (*(p_ucData + l_offset + 9) == 0x06)
                        p_stPcapInfo.m_sProtocalMode = "TCP";
                    else
                        p_stPcapInfo.m_sProtocalMode = "ERROR";

                    p_stPcapInfo.m_sSrcIP = std::to_string(*(p_ucData + l_offset + 12)) + "."
                                            + std::to_string(*(p_ucData + l_offset + 13)) + "."
                                            + std::to_string(*(p_ucData + l_offset + 14)) + "."
                                            + std::to_string(*(p_ucData + l_offset + 15));
                    p_stPcapInfo.m_sDstIP = std::to_string(*(p_ucData + l_offset + 16)) + "."
                                            + std::to_string(*(p_ucData + l_offset + 17)) + "."
                                            + std::to_string(*(p_ucData + l_offset + 18)) + "."
                                            + std::to_string(*(p_ucData + l_offset + 19));
                    p_stPcapInfo.m_uiSrcPort =
                        *(p_ucData + l_offset + 20) << 8 | *(p_ucData + l_offset + 21);
                    p_stPcapInfo.m_uiDstPort =
                        *(p_ucData + l_offset + 22) << 8 | *(p_ucData + l_offset + 23);
                    return true;
                }
                else
                    printf("error ip header totalLength %d - %d - %d \n",
                           p_iLen,
                           l_totalLen,
                           l_offset);
            }
            l_offset++;
        }
        return false;
    }
    bool setOffset_(const u_char* p_ucData, uint32_t p_iLen, uint32_t& p_uiOffset)
    {
        int l_numFFEE = 0;
        int l_iLastIdFFEE = 0;

        if (p_uiOffset)
            return true;
        for (uint32_t i = 0; i < p_iLen - 1; i++)
        {
            if (*(p_ucData + i) == 255 && *(p_ucData + i + 1) == 238)
            {
                p_uiOffset = i;
                break;
            }
        }

        if (p_uiOffset)
            return true;
        return false;
    }
    bool checkScanErr_(const boost::array<uint8_t, 1600>& p_rawData, int& p_iErrScanPointNum)
    {
        p_iErrScanPointNum = 0;
        const s_RawPacket* raw = (const s_RawPacket*)&p_rawData[0];
        // 15块
        for (int i = 0; i < BLOCKS_PER_PACKET; i++)
        {
            // 19块
            for (int j = 0, k = 0; j < SCANS_PER_BLOCK; j++, k += RAW_SCAN_SIZE)
            {
                if (raw->blocks[i].data[k] == 0x10 && raw->blocks[i].data[k + 1] == 0x00
                    && raw->blocks[i].data[k + 2] == 0x00 && raw->blocks[i].data[k + 3] == 0x10)
                    p_iErrScanPointNum++;
            }
        }
        // 点数15*19  若大于60%的点  则异常
        if (p_iErrScanPointNum == BLOCKS_PER_PACKET * SCANS_PER_BLOCK)
            return false;
        return true;
    }
    void initPcap_(std::string p_sNetName, std::string p_sFilterRule)
    {
        char* l_pNetCard_ = const_cast<char*>(p_sNetName.c_str());
        char l_cErrBuf[256];
        c_pacp_ = pcap_open_live(l_pNetCard_, 65535, 1, 0, l_cErrBuf);
        if (!c_pacp_)
        {
            l_pNetCard_ = pcap_lookupdev(l_cErrBuf);
            printf("Set lidar_dev error, auto open lidarNetCard: %s\n", l_pNetCard_);
            if (!l_pNetCard_)
            {
                printf("No Find NetWord\n");
                return;
            }
            c_pacp_ = pcap_open_live(l_pNetCard_, 65535, 1, 0, l_cErrBuf);
            if (!c_pacp_)
            {
                printf("Workcard open fail\n");
                return;
            }
        }
        pcap_setnonblock(c_pacp_, 1, l_cErrBuf);

        struct bpf_program l_stFilter;
        int l_iRes = pcap_compile(c_pacp_, &l_stFilter, p_sFilterRule.c_str(), 1, 0);
        pcap_setfilter(c_pacp_, &l_stFilter);
    }

  public:
    bool isRun()
    {
        if (c_bRun_)
            return true;
        else
            return false;
    }
    void shutDown()
    {
        c_bRun_ = false;
        while (!c_bRunOver_)
        {
            sleep(10);
        }
        pcap_close(c_pacp_);
    }
    void analysis()
    {
        int l_lineNum = 0;
        int l_uiAllPktNum = 0;
        int l_lidarPktNumLast = 0;
        int l_angPktLast = 0;
        int l_iTimeErrNum = 0, l_iScanErrNum = 0;
        int l_iTimeDiffMs = 0;
        if (c_bOutFile_)
        {
            c_filePoseWR_.open(c_DirOuput_.c_str(), std::ios::out | std::ios::trunc);
            if (!c_filePoseWR_.is_open())
            {
                printf("输出文件< %s> 打开失败\n", c_DirOuput_.c_str());
                return;
            }
        }

        while (c_bRun_)
        {
            c_bRunOver_ = false;
            if (pcap_next_ex(c_pacp_, &c_stHeader_, &c_ucPktData_) > 0)
            {
                // if (analysisPcapHeader(c_ucPktData_, c_stHeader_->len, c_stPcapInfo))
                // {
                //     std::cout << std::endl;
                //     std::cout << "***********"
                //               << "PCAP_Info"
                //               << "***********" << std::endl;
                //     std::cout << "**  ProtocalMode : " << c_stPcapInfo.m_sProtocalMode <<
                //     std::endl; std::cout << "**  srcIP        : " << c_stPcapInfo.m_sSrcIP <<
                //     std::endl; std::cout << "**  srcPort      : " << c_stPcapInfo.m_uiSrcPort <<
                //     std::endl; std::cout << "**  dstIP        : " << c_stPcapInfo.m_sDstIP <<
                //     std::endl; std::cout << "**  dstPort      : " << c_stPcapInfo.m_uiDstPort <<
                //     std::endl; std::cout << std::endl; std::cout <<
                //     "*****************************" << std::endl; std::cout << std::endl;
                // }
                if (c_stHeader_->len < 1230)
                {
                    printf("雷达[%d]数据帧长度异常 %d < 1230\n", c_iID_, c_stHeader_->len);
                    continue;
                }

                if (!setOffset_(c_ucPktData_, c_stHeader_->len, c_uiOffset_))
                {
                    printf("雷达[%d]获取偏移失败 %d\n", c_iID_, c_stHeader_->len);
                    continue;
                }

                if ((c_stHeader_->len != (1230 + c_uiOffset_ + c_uiDiscardNum_)))
                {
                    printf("雷达[%d] 长度异常 %d != %d\n",
                           c_iID_,
                           c_stHeader_->len,
                           (1230 + c_uiOffset_ + c_uiDiscardNum_));
                    continue;
                }

                memcpy(&c_data_[0], c_ucPktData_ + c_uiOffset_, 1260);

                l_lineNum++;
                int l_angPkt = (c_data_[3] << 8 | c_data_[2]);
                int l_lidarPktNum = (c_data_[1256] << 8 | c_data_[1257]);
                int l_lidarCycleNum = (c_data_[1202] << 8 | c_data_[1203]);
                tm l_time = {
                    .tm_sec = c_data_[1204],         //秒
                    .tm_min = c_data_[1205],         //分
                    .tm_hour = c_data_[1206],        //时
                    .tm_mday = c_data_[1207],        //日
                    .tm_mon = c_data_[1208] - 1,     //月
                    .tm_year = c_data_[1209] + 100,  //年
                };
                timeval l_DataTime;
                l_DataTime.tv_sec = mktime(&l_time);
                l_DataTime.tv_usec = (((c_data_[1213] & 0x0F) << 24) + (c_data_[1212] << 16)
                                      + (c_data_[1211] << 8) + c_data_[1210])
                                     / 100;
                // printf(
                //     "lidar %d pcap time %ld.%ld\n", c_iID_, l_DataTime.tv_sec,
                //     l_DataTime.tv_usec);
                // printf("lidar %d pcap timediff %f %d %d\n",
                //        c_iID_,
                //        getTimeDiffMs(l_DataTime, c_DataLast_),
                //        l_lidarCycleNum,
                //        l_lidarPktNum);
                // c_DataLast_ = l_DataTime;
                // if (c_LastCircle_ != l_lidarCycleNum)
                // {
                // printf("lidar %d circle %f\n",
                //        c_iID_,
                //        getTimeDiffMs(l_DataTime, c_CircleDataLast_));
                // printf("lidar %d headcircle %f\n",
                //        c_iID_,
                //        getTimeDiffMs(c_stHeader_->ts, c_CircleHeadLast_));
                // c_CircleHeadLast_ = c_stHeader_->ts;
                // c_LastCircle_ = l_lidarCycleNum;
                // c_CircleDataLast_ = l_DataTime;
                // }
                // if (c_iFirstSendOffset && l_lineNum >= c_iFirstSendOffset
                //     && l_lineNum < c_iFirstSendOffset + 12)
                // {
                //     std::cout << "id : " << l_lineNum << "    角度  :  " << l_angPkt
                //               << "    计数 : " << l_lidarPktNum << " 圈号: " << l_lidarCycleNum
                //               << std::endl;
                // }
                // if (l_lineNum == c_iFirstSendOffset + 12)
                // {
                //     c_iFirstSendOffset += 120;
                //     printf("----------------------------------------------------------\n");
                // }

                // 检查雷达状态
                bool l_bStatus = true;
                if (l_angPktLast && l_angPkt != l_angPktLast + 300)
                {
                    if (l_angPktLast == 36000 && l_angPkt == 300)
                        ;
                    else
                        l_bStatus = false;
                }

                if (l_lidarPktNumLast && l_lidarPktNum != l_lidarPktNumLast + 1)
                {
                    if (l_lidarPktNumLast == 65535 && l_lidarPktNum == 0)
                        ;
                    else
                        l_bStatus = false;
                }
                // 检查包间耗时 理论值 12包1发，发晚后休眠等待满足10ms

                // printf("lidar %d head time %ld.%ld\n",
                //        c_iID_,
                //        c_stHeader_->ts.tv_sec,
                //        c_stHeader_->ts.tv_usec);
                // printf("lidar %d Head timediff %f\n",
                //        c_iID_,
                //        getTimeDiffMs(c_stHeader_->ts, c_HeadLast_));
                // c_HeadLast_ = c_stHeader_->ts;

                if (c_stLastT_.tv_sec == 0)
                    c_stLastT_ = c_stHeader_->ts;
                l_iTimeDiffMs = ((c_stHeader_->ts.tv_sec - c_stLastT_.tv_sec) * 1e+6
                                 + (c_stHeader_->ts.tv_usec - c_stLastT_.tv_usec))
                                * 1e-3;
                if (l_iTimeDiffMs > 20)
                    l_bStatus = false;
                c_stLastT_ = c_stHeader_->ts;
                // 打印雷达异常状态
                if (!l_bStatus)
                {
                    l_iTimeErrNum++;
                    char l_time[40] = {0};
                    strftime(l_time,
                             sizeof(l_time),
                             "%Y-%m-%d %H-%M-%S",
                             localtime(&(c_stHeader_->ts.tv_sec)));
                    std::string l_analy = c_ErrorInfo[3];
                    int l_angDiff = (l_angPkt - l_angPktLast) > 0
                                        ? (l_angPkt - l_angPktLast)
                                        : (l_angPkt - l_angPktLast + 36000);
                    if (l_angDiff / 300 == (l_lidarPktNum - l_lidarPktNumLast) % 120)
                    {
                        if (l_angDiff / 300 == 1)
                            l_analy = c_ErrorInfo[2];
                        else
                            l_analy = c_ErrorInfo[1];
                    }

                    printf("雷达 %d 耗时异常 | 耗时 [%-5d] Ms 角度变化: %-5d -> %-5d "
                           "理论包号变化: %-5d | "
                           "内部包号变化: %-10d -> %-10d 理论包号变化: %-10d |  分析结果: %s "
                           "%s\n", c_iID_, l_iTimeDiffMs, l_angPktLast, l_angPkt, (l_angPkt -
                           l_angPktLast) / 300, l_lidarPktNumLast, l_lidarPktNum, (l_lidarPktNum
                           - l_lidarPktNumLast) % 120, l_time, l_analy.c_str());
                }
                int l_iErrPointNum = 0;
                if (!checkScanErr_(c_data_, l_iErrPointNum))
                {
                    char l_time[40] = {0};
                    strftime(l_time,
                             sizeof(l_time),
                             "%Y-%m-%d %H-%M-%S",
                             localtime(&(c_stHeader_->ts.tv_sec)));
                    printf("雷达 %d 扫描异常 | 异常点数 [%-3d/%-3d] | 分析结果: %s %s\n",
                           c_iID_,
                           l_iErrPointNum,
                           BLOCKS_PER_PACKET * SCANS_PER_BLOCK,
                           l_time,
                           c_ErrorInfo[0].c_str());
                    l_iScanErrNum++;
                }
                l_lidarPktNumLast = l_lidarPktNum;
                l_angPktLast = l_angPkt;

                if (c_bOutFile_ && c_filePoseWR_.is_open())
                {
                    if (l_angPkt == 35700)
                        c_filePoseWR_ << l_uiAllPktNum << "," << l_lidarPktNum << "," << l_angPkt
                                      << "," << c_stHeader_->ts.tv_sec << ","
                                      << c_stHeader_->ts.tv_usec << std::endl;
                }
                c_bRunOver_ = true;
            }
        }
        c_filePoseWR_.close();
        if (l_iTimeErrNum == 0 && l_iScanErrNum == 0)
            printf("离线分析结束: 数据无异常\n");
        else
            printf("离线分析结束\n");
        if (c_bOutFile_)
        {
            printf("输出文件: %s\n", c_DirOuput_.c_str());
        }
    }
    analysisLidar(std::string p_sNetName, std::string p_sFilterRule, int p_iID)
    {
        c_iID_ = p_iID;
        c_bRun_ = true;
        c_DirOuput_ += c_path;
        c_DirOuput_ += "/lidarAnaly_" + std::to_string(c_iID_) + ".csv";
        c_stLastT_.tv_sec = 0;
        c_stLastT_.tv_usec = 0;
        initPcap_(p_sNetName, p_sFilterRule);
    }
    ~analysisLidar()
    {
        c_pacp_ = nullptr;
    }
};

int main(int argc, char const* argv[])
{
    if (argc < 2)
    {
        std::cout << "请输入雷达连接网卡" << std::endl;
        return -1;
    }

    std::vector<std::string> c_vsNetName;
    std::vector<std::string> c_vsLidarIP;
    std::vector<analysisLidar*> c_vAnalysisLidar;

    if (argc == 2)
    {
        c_vAnalysisLidar.resize(1);
        std::string l_sNetName = argv[1];
        c_vsNetName.push_back(l_sNetName);
        std::string l_sIP = "************";
        c_vsLidarIP.push_back(l_sIP);
    }
    else if (argc == 3)
    {
        c_vAnalysisLidar.resize(2);
        for (size_t i = 0; i < 2; i++)
        {
            std::string l_sNetName = argv[i + 1];
            c_vsNetName.push_back(l_sNetName);
        }
        std::string l_sIP = "************";
        c_vsLidarIP.push_back(l_sIP);
        l_sIP = "************";
        c_vsLidarIP.push_back(l_sIP);
    }

    for (size_t i = 0; i < c_vAnalysisLidar.size(); i++)
    {
        if (c_vsNetName[i] == "" || c_vsNetName[i] == "NOSET")
            continue;
        std::string l_sFilter = "not tcp && src host " + c_vsLidarIP[i];
        c_vAnalysisLidar[i] = new analysisLidar(c_vsNetName[i], l_sFilter, i);
        std::thread l_tdAna = std::thread(&analysisLidar::analysis, c_vAnalysisLidar[i]);
        l_tdAna.detach();
    }

    while (1)
    {
        for (size_t i = 0; i < c_vAnalysisLidar.size(); i++)
        {
            if (c_vAnalysisLidar[i])
            {
                if (!c_vAnalysisLidar[i]->isRun())
                    break;
            }
            else
                break;
        }
        sleep(10);
    }

    for (size_t j = 0; j < c_vAnalysisLidar.size(); j++)
    {
        if (c_vAnalysisLidar[j])
            c_vAnalysisLidar[j] = nullptr;
    }

    return 0;
}