/*
 * @Description:
 * @Version: 1.0
 * @Autor: zu<PERSON><PERSON>
 * @Date: 2021-10-18 13:38:32
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-05-17 21:15:53
 */
#include "algorithm/map/secret_map/laserIO.h"
#include "common/common_ex.h"
#include <pcl/filters/filter.h>
#include <pcl/filters/radius_outlier_removal.h>
#include <pcl/filters/statistical_outlier_removal.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/io/pcd_io.h>

typedef pcl ::PointXYZ PointType;
typedef pcl::PointCloud<PointType> PointCloud;
typedef boost::shared_ptr<pcl::PointCloud<PointType>> PointCloudTypePtr;
using namespace wj_slam;

class mapTransFromTo {
  public:
    s_POSE6D c_stTrans;
    void setMapTransParam(std::vector<float> l_vMyMap, std::vector<float> l_vToMap)
    {
        /*地图校正参数*/
        if (l_vMyMap.size() == 3 && l_vToMap.size() == 3)
        {
            double l_f64YawAngleDiff = (l_vToMap[2] - l_vMyMap[2]) * M_PI / 180.0;
            double l_f64x =
                l_vMyMap[0] * cos(l_f64YawAngleDiff) - l_vMyMap[1] * sin(l_f64YawAngleDiff);
            double l_f64y =
                l_vMyMap[0] * sin(l_f64YawAngleDiff) + l_vMyMap[1] * cos(l_f64YawAngleDiff);

            c_stTrans.setRPY(0.0, 0.0, l_vToMap[2] - l_vMyMap[2]);
            c_stTrans.setX(l_vToMap[0] - l_f64x);
            c_stTrans.setY(l_vToMap[1] - l_f64y);
        }
    }
    void transformCloudPoints(PointCloudTypePtr p_pPcOut)
    {
        int l_iCldSize = p_pPcOut->size();
        Eigen::Vector3d l_updPnt;
#pragma omp parallel for nowait num_threads(4) private(l_updPnt)
        for (int j = 0; j < l_iCldSize; j++)
        {
            l_updPnt << p_pPcOut->points[j].x, p_pPcOut->points[j].y, p_pPcOut->points[j].z;
            l_updPnt = c_stTrans.m_quat * l_updPnt + c_stTrans.m_trans;
            p_pPcOut->points[j].x = l_updPnt.x();
            p_pPcOut->points[j].y = l_updPnt.y();
            p_pPcOut->points[j].z = l_updPnt.z();
        }
    }
};

void voxelFilter(PointCloudTypePtr cloud, float leftSize = 0.1)
{
    pcl::VoxelGrid<PointType> sor;
    sor.setInputCloud(cloud);
    sor.setLeafSize(leftSize, leftSize, leftSize);
    sor.filter(*cloud);
}

void StatisticalOutlierFilter(PointCloudTypePtr cloud, int K = 6, float sigma = 1.0)
{
    // 创建滤波对象
    pcl::StatisticalOutlierRemoval<PointType> sor;
    sor.setInputCloud(cloud);
    // 设置平均距离估计的最近邻居的数量K
    sor.setMeanK(K);
    // 设置标准差阈值系数，值越小，滤波效果越强
    sor.setStddevMulThresh(sigma);
    // sor.setNegative(True); 滤波器取反
    // 执行过滤
    sor.filter(*cloud);
}

void RadiusOutlierFilter(PointCloudTypePtr cloud, int Neighbors = 4, float Radius = 0.6)
{
    pcl::RadiusOutlierRemoval<PointType> outrem;
    // build the filter
    outrem.setInputCloud(cloud);
    outrem.setRadiusSearch(Radius);
    outrem.setMinNeighborsInRadius(Neighbors);
    // apply filter
    outrem.filter(*cloud);
}

int main(int argc, char const* argv[])
{
    if (argc < 3)
    {
        std::cout << "输入  文件地址  文件名" << std::endl;
        return -1;
    }
    KEYFRAME<PointType>::Ptr l_map(new KEYFRAME<PointType>());
    PointCloudTypePtr c_pcKeyFramesPose_(new pcl::PointCloud<PointType>());
    PointCloudTypePtr c_pcViewerMap_(new pcl::PointCloud<PointType>());
    std::string path = argv[1];
    std::string filename = argv[2];
    boost::shared_ptr<LaserIO<PointType, PointType>> c_pReadWriteMap_;
    c_pReadWriteMap_.reset(new LaserIO<PointType, PointType>());
    pcl::PCDWriter writer;
    std::vector<typename boost::shared_ptr<pcl::PointCloud<PointType>>> l_feature;
    l_feature.push_back(l_map->m_pFeature->first);
    l_feature.push_back(l_map->m_pFeature->second);
    l_feature.push_back(l_map->m_pFeature->fourth);
    c_pReadWriteMap_->readBinary(path + "/." + filename + ".wj", c_pcKeyFramesPose_, l_feature);
    pcl::io::loadPCDFile(path + "/" + filename + ".pcd", *c_pcViewerMap_);

    mapTransFromTo mt;
    std::vector<float> l_vMyMap;
    std::vector<float> l_vToMap;
    std::cout << "输入MYMAP 和 TOMAP： X Y A X Y A" << std::endl;
    for (int i = 0; i < 3; ++i)
    {
        int temp;
        cin >> temp;
        l_vMyMap.push_back(temp);
    }
    for (int i = 0; i < 3; ++i)
    {
        int temp;
        cin >> temp;
        l_vToMap.push_back(temp);
    }
    std::cout << "MYMAP:" << l_vMyMap[0] << "," << l_vMyMap[1] << "," << l_vMyMap[2] << std::endl;
    std::cout << "TOMAP:" << l_vToMap[0] << "," << l_vToMap[1] << "," << l_vToMap[2] << std::endl;

    mt.setMapTransParam(l_vMyMap, l_vToMap);
    if (!c_pcViewerMap_->empty())
    {
        mt.transformCloudPoints(c_pcViewerMap_);
        voxelFilter(c_pcViewerMap_, 0.3);
        StatisticalOutlierFilter(c_pcViewerMap_);
        RadiusOutlierFilter(c_pcViewerMap_, 4, 0.9);
    }
    if (l_map->m_pFeature->cornerSize())
    {
        mt.transformCloudPoints(l_map->m_pFeature->first);
        voxelFilter(l_map->m_pFeature->first, 0.06);
        StatisticalOutlierFilter(l_map->m_pFeature->first, 4, 0.8);
        RadiusOutlierFilter(l_map->m_pFeature->first, 4, 1.0);
    }
    if (l_map->m_pFeature->surfSize())
    {
        mt.transformCloudPoints(l_map->m_pFeature->second);
        voxelFilter(l_map->m_pFeature->first, 0.1);
        StatisticalOutlierFilter(l_map->m_pFeature->first, 4, 1.0);
        RadiusOutlierFilter(l_map->m_pFeature->first, 6, 0.8);
    }
    if (l_map->m_pFeature->curbSize())
    {
        mt.transformCloudPoints(l_map->m_pFeature->fourth);
        voxelFilter(l_map->m_pFeature->first, 0.1);
        StatisticalOutlierFilter(l_map->m_pFeature->first, 4, 1.0);
        RadiusOutlierFilter(l_map->m_pFeature->first, 6, 0.8);
    }
    if (c_pcKeyFramesPose_->size())
        mt.transformCloudPoints(c_pcKeyFramesPose_);

    pcl::io::savePCDFileBinary(path + "/" + filename + ".pcd", *c_pcViewerMap_);
    c_pReadWriteMap_->writeBinary(path + "/." + filename + ".wj", c_pcKeyFramesPose_, l_feature);
    return 0;
}
