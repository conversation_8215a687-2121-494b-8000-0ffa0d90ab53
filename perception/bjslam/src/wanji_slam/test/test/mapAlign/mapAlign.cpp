#include "./dispatchMap/dispatchMap.h"
#include "./trajectory/trajectory.hpp"
#include "./trajectoryAlign/trajectoryAlign.h"
#include <boost/shared_ptr.hpp>

enum MapMode {
    NOTRANS = 0,  // 默认无平移
    HG_TW,        //哈工库讯成都通威调度
};

MapMode c_mapMode;              //调度地图类型
float c_fMyMap[3] = {0, 0, 0};  // 地图转换参数
float c_fToMap[3] = {0, 0, 0};
std::string c_sFilePath = "";           // 文件路径
std::vector<std::string> c_vsFileName;  //调度、轨迹文件名
boost::shared_ptr<wj_slam::DispatchMap> c_pDispatchMap_ = nullptr;
boost::shared_ptr<wj_slam::Trajectory> c_pTrajectory_ = nullptr;
boost::shared_ptr<wj_slam::TrajectoryAlign> c_pTrajectoryAlign_ = nullptr;

bool paramInit(int argc, const char** argv)
{
    if (argc < 5)
    {
        std::cout << "输入 调度地图类型 文件路径 调度地图文件名name [eg:name_xy.csv name_yaw.csv] "
                     "轨迹点文件名name [eg:name_old.csv | name_new.csv]"
                  << std::endl;
        return false;
    }

    std::string l_param = argv[1];
    c_mapMode = MapMode(stoi(l_param));
    c_sFilePath = argv[2];
    c_vsFileName.push_back(argv[3]);
    c_vsFileName.push_back(argv[4]);

    switch (c_mapMode)
    {
        case MapMode::HG_TW:
        {
            c_pDispatchMap_.reset(new wj_slam::DispatchMapHG(c_sFilePath, c_vsFileName[0]));
            c_pTrajectory_.reset(new wj_slam::Trajectory(c_sFilePath, c_vsFileName[1]));
            c_fToMap[0] = 250;
            c_fToMap[1] = 400;
            break;
        }
        default:
        {
            std::cout << "调度地图类型未定义, 默认平移量" << std::endl;
            c_pDispatchMap_.reset(new wj_slam::DispatchMapHG(c_sFilePath, c_vsFileName[0]));
            c_pTrajectory_.reset(new wj_slam::Trajectory(c_sFilePath, c_vsFileName[1]));
            break;
        }
    }

    if (!(c_pDispatchMap_ && c_pTrajectory_))
    {
        printf("参数配置错误\n");
        return false;
    }
    c_pTrajectory_->setBigYawValue(100.0);
    return true;
}

int main(int argc, const char** argv)
{
    // 参数初始化、类实例化
    if (!paramInit(argc, argv))
        return -1;

    // 轨迹同名检测是否通过
    if (!c_pTrajectory_->isValidTrajectory())
        return -1;

    // 实例化对齐类、检测输入源是否合法
    c_pTrajectoryAlign_.reset(new wj_slam::TrajectoryAlign(c_pDispatchMap_->getSiteXYInfoBuf(),
                                                           c_pDispatchMap_->getSiteYawInfoBuf(),
                                                           c_pTrajectory_->getSrcTrajectory(),
                                                           c_pTrajectory_->getDstTrajectory()));

    if (c_pTrajectoryAlign_->isValidData())
    {
        /*定位轨迹和调度站点转换参数*/
        c_pTrajectoryAlign_->setMapTrans(c_fMyMap, c_fToMap);
        c_pTrajectoryAlign_->setFilePath(c_sFilePath, c_vsFileName[0], c_vsFileName[1]);
        /*定位轨迹插值密度*/
        c_pTrajectoryAlign_->setInterPolationSize(0.5);
        /*站点搜索定位轨迹半径*/
        c_pTrajectoryAlign_->setSiteSearchSize(10.0);
        /*定位轨迹直线下采样参数*/
        c_pTrajectoryAlign_->setPathDownSize(0.3);
        /*定位轨迹分割直线角度阈值，超过此阈值 则分段*/
        c_pTrajectoryAlign_->setPathCutLineYaw(0.2);
        /*角度超过此阈值，则此点为转弯点  不作RT求解*/
        c_pTrajectoryAlign_->setAlignRotateAng(5.0);
        /*站点修正角度低于此阈值，则不转换*/
        c_pTrajectoryAlign_->setIgnoreCorrectAng(1);

        printf("配置正确，开始对齐...\n");
        c_pTrajectoryAlign_->startAlign();
        printf("对齐完毕。\n");
        c_pDispatchMap_->saveSiteMapFile();
    }
    else
        printf("no valid data\n");
    return 0;
}