#pragma once
#include "common/type/type_pose.h"
#include <iostream>

namespace wj_slam {

struct st_Site
{
    st_Site()
    {
        m_iSiteID = -1;
        m_bIsTrans = false;
        m_SitePose.reset();
    }

    int m_iSiteID;        // 站点ID
    bool m_bIsTrans;      // 是否转换
    s_POSE6D m_SitePose;  // 站点坐标
};

struct st_SiteLineInfo
{
    st_SiteLineInfo()
    {
        m_bIsLine = false;
        m_viSiteID.clear();
        m_vfSiteAng.clear();
    }

    bool m_bIsLine;                  // 是否为线（直线、弧线均为线，反之为孤点）
    std::vector<int> m_viSiteID;     // 站点ID
    std::vector<float> m_vfSiteAng;  // 站点角度
};

struct st_Trajectory
{
    st_Trajectory()
    {
        m_iFrameID = -1;
        m_trajectory.reset();
    }

    int m_iFrameID;                // 轨迹ID
    s_PoseWithTwist m_trajectory;  // 轨迹坐标&转换参数
};
}  // namespace wj_slam