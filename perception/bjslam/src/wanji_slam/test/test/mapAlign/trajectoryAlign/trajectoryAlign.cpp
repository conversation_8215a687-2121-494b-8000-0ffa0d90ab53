#include "trajectoryAlign.h"

namespace wj_slam {

TrajectoryAlign::TrajectoryAlign(std::vector<st_Site>& p_vSiteBuf,
                                 std::vector<st_SiteLineInfo>& p_vSiteLineBuf,
                                 std::deque<st_Trajectory>& p_vSrcPoseBuf,
                                 std::deque<st_Trajectory>& p_vDstPoseBuf)
    : c_vSiteBuf_(p_vSiteBuf), c_vSiteLineBuf(p_vSiteLineBuf), c_vSrcPoseBuf(p_vSrcPoseBuf),
      c_vDstPoseBuf(p_vDstPoseBuf)
{
    if (c_vSiteBuf_.empty())
        printf("siteBuf is empty\n");
    if (p_vSrcPoseBuf.empty())
        printf("src pose Buf is empty\n");
    if (p_vSrcPoseBuf.size() != p_vDstPoseBuf.size())
        printf("src pose Buf != dst pose Buf\n");
    if (c_vSiteBuf_.empty() || p_vSrcPoseBuf.empty()
        || (p_vSrcPoseBuf.size() != p_vDstPoseBuf.size()))
        c_bIsValid_ = false;
    c_downSizeFilter.setLeafSize(c_fPathDownSize_, c_fPathDownSize_, 0.0);
}

TrajectoryAlign::~TrajectoryAlign()
{
    clearDeque_(c_vSrcPoseDSBuf);
    clearDeque_(c_vDstPoseDsBuf);
    clearDeque_(c_vDstPoseAddPoseBuf);
}

void TrajectoryAlign::setMapTrans(float p_fMyMap[3], float p_fToMap[3])
{
    double l_f64YawAngleDiff = (p_fToMap[2] - p_fMyMap[2]) * M_PI / 180.0;
    double l_f64x = p_fMyMap[0] * cos(l_f64YawAngleDiff) - p_fMyMap[1] * sin(l_f64YawAngleDiff);
    double l_f64y = p_fMyMap[0] * sin(l_f64YawAngleDiff) + p_fMyMap[1] * cos(l_f64YawAngleDiff);
    c_stTrans.setRPY(0.0, 0.0, p_fToMap[2] - p_fMyMap[2]);
    c_stTrans.setX(p_fToMap[0] - l_f64x);
    c_stTrans.setY(p_fToMap[1] - l_f64y);
}

/*设置文件路径*/
void TrajectoryAlign::setFilePath(std::string a, std::string b, std::string c)
{
    c_sFilePath_ = a;
    c_sDispatchFileName_ = b;
    c_sTrajectoryFileName_ = c;
    if ('/' != c_sFilePath_[c_sFilePath_.length() - 1])
        c_sFilePath_ += "/";
}

void TrajectoryAlign::setInterPolationSize(float p_value)
{
    c_finterpolation_Size_ = p_value;
}
void TrajectoryAlign::setSiteSearchSize(float p_value)
{
    c_fKdSearchSize_ = p_value;
}
void TrajectoryAlign::setPathDownSize(float p_value)
{
    c_fPathDownSize_ = p_value;
}
void TrajectoryAlign::setPathCutLineYaw(float p_value)
{
    c_fRotateYawDiff_ = p_value;
}
void TrajectoryAlign::setAlignRotateAng(float p_value)
{
    c_fTurnYawDiffRes_ = p_value;
}
void TrajectoryAlign::setIgnoreCorrectAng(float p_value)
{
    c_fIgnoreAngDiff_ = p_value;
}

bool TrajectoryAlign::isValidData()
{
    return c_bIsValid_;
}

void TrajectoryAlign::startAlign()
{
    pointCloudT_Ptr l_DstPoseAddPoseInSityMapPtr(new pointCloudT());
    pointCloudT_Ptr l_SrcPoseAddPoseInSityMapPtr(new pointCloudT());
    // 轨迹下采样
    if (c_bIsValid_)
    {
        // 轨迹下采样
        makeTrajectoryDS_(c_vSrcPoseBuf, c_vDstPoseBuf, c_vSrcPoseDSBuf, c_vDstPoseDsBuf);
        // 老轨迹转移至调度坐标系
        transTrajtoryToSiteMap_(c_vSrcPoseDSBuf, c_stTrans, l_DstPoseAddPoseInSityMapPtr);
        transTrajtoryToSiteMap_(c_vDstPoseDsBuf, c_stTrans, l_SrcPoseAddPoseInSityMapPtr);

        // 对齐轨迹点 求解RT 存入c_vDstPoseDsBuf
        align_(c_vSrcPoseDSBuf, c_vDstPoseDsBuf);

        // 对老轨迹进行插值，存入c_vDstPoseAddPoseBuf
        interpolation_(c_vDstPoseDsBuf, c_vDstPoseAddPoseBuf);

#ifdef DEBUGSAVE
        // 可视化插值后的path
        pointCloudT_Ptr l_savePath(new pointCloudT());
        toPointCloud_(c_vDstPoseAddPoseBuf, l_savePath);
        std::string l_path = c_sFilePath_ + c_sTrajectoryFileName_ + "_old_insert.pcd";
        pcl::PCDWriter writer;
        writer.writeBinary(l_path, *l_savePath);
#endif  // DEBUGSAVE
    }

    // 校正调度地图
    if (!c_vDstPoseAddPoseBuf.empty())
    {
        // 校正调度地图
        correctSiteMap_(
            c_vDstPoseAddPoseBuf, l_DstPoseAddPoseInSityMapPtr, c_vSiteBuf_, c_fKdSearchSize_);
    }
}

void TrajectoryAlign::transTrajtoryToSiteMap_(std::deque<st_Trajectory>& p_vPoseBuf,
                                              s_POSE6D& p_MapTrans,
                                              pointCloudT_Ptr& p_NewPosePtr)
{
    p_NewPosePtr->clear();
    s_POSE6D l_pose;
    T l_transPose;
    for (uint32_t i = 0; i < p_vPoseBuf.size(); i++)
    {
        l_pose = p_MapTrans * p_vPoseBuf[i].m_trajectory.m_Pose;
        p_vPoseBuf[i].m_trajectory.m_Pose = l_pose;
        l_transPose = T(l_pose.x(), l_pose.y(), 0.0);
        p_NewPosePtr->points.push_back(l_transPose);
    }
}

void TrajectoryAlign::correctSiteMap_(std::deque<st_Trajectory>& p_vPoseBuf,
                                      pointCloudT_Ptr& p_NewPosePtr,
                                      std::vector<st_Site>& p_vSiteBuf,
                                      float& p_fRadius)
{
    std::vector<int> l_vecIndexs;
    std::vector<float> l_vecDists;

    pointCloudT_Ptr l_transSite(new pointCloudT());
    pointCloudT_Ptr l_searchPathDst(new pointCloudT());
    pointCloudT_Ptr l_searchPathSrc(new pointCloudT());
    T l_sitePose;

    kdtreeT_Ptr l_kdtreeDstPose(new pcl::KdTreeFLANN<T>());
    l_kdtreeDstPose->setInputCloud(p_NewPosePtr);

#ifdef DEBUGSAVE
    // 调试打印
    std::fstream l_filePoseWR;
    l_filePoseWR.open(c_sFilePath_ + c_sDispatchFileName_ + "_correct.csv",
                      std::ios::out | std::ios::trunc);
    if (!l_filePoseWR.is_open())
        return;
#endif  // DEBUGSAVE
    for (size_t i = 0; i < p_vSiteBuf.size(); i++)
    {
        T l_pose = T(p_vSiteBuf[i].m_SitePose.x(), p_vSiteBuf[i].m_SitePose.y(), 0);
        l_kdtreeDstPose->radiusSearch(l_pose, p_fRadius, l_vecIndexs, l_vecDists);
        if (!l_vecIndexs.empty())
        {
            // p_vPoseBuf[l_vecIndexs[0]].m_trajectory.m_Twist.printf(
            //     std::to_string(p_vSiteBuf[i].m_iSiteID) + " _RT id: " +
            //     std::to_string(l_vecIndexs[0]));
            // p_vSiteBuf[i].m_SitePose.printf(std::to_string(p_vSiteBuf[i].m_iSiteID) + " _raw Pos
            // id: " + std::to_string(l_vecIndexs[0]));
            s_POSE6D l_rawPose = p_vSiteBuf[i].m_SitePose;
            p_vSiteBuf[i].m_SitePose =
                p_vPoseBuf[l_vecIndexs[0]].m_trajectory.m_Twist * p_vSiteBuf[i].m_SitePose;
            // p_vSiteBuf[i].m_SitePose.printf(std::to_string(p_vSiteBuf[i].m_iSiteID) + " _correct
            // Pos id: " + std::to_string(l_vecIndexs[0])); 校正角度
            correctSiteLine_(p_vSiteBuf[i].m_iSiteID,
                             p_vPoseBuf[l_vecIndexs[0]].m_trajectory.m_Twist,
                             c_vSiteLineBuf);

            p_vSiteBuf[i].m_bIsTrans = true;

#ifdef DEBUGSAVE
            l_sitePose = T(p_vSiteBuf[i].m_SitePose.x(),
                           p_vSiteBuf[i].m_SitePose.y(),
                           p_vSiteBuf[i].m_SitePose.z());
            l_transSite->points.push_back(l_sitePose);
            l_sitePose = T(c_vDstPoseDsBuf[l_vecIndexs[0]].m_trajectory.m_Pose.x(),
                           c_vDstPoseDsBuf[l_vecIndexs[0]].m_trajectory.m_Pose.y(),
                           c_vDstPoseDsBuf[l_vecIndexs[0]].m_trajectory.m_Pose.z());
            l_searchPathDst->points.push_back(l_sitePose);
            l_sitePose = T(c_vSrcPoseDSBuf[l_vecIndexs[0]].m_trajectory.m_Pose.x(),
                           c_vSrcPoseDSBuf[l_vecIndexs[0]].m_trajectory.m_Pose.y(),
                           c_vSrcPoseDSBuf[l_vecIndexs[0]].m_trajectory.m_Pose.z());
            l_searchPathSrc->points.push_back(l_sitePose);

            l_filePoseWR << p_vSiteBuf[i].m_iSiteID << "," << l_rawPose.x() << "," << l_rawPose.y()
                         << "," << p_vSiteBuf[i].m_SitePose.x() << ","
                         << p_vSiteBuf[i].m_SitePose.y() << std::endl;
#endif  // DEBUGSAVE
        }
        // else
        //     printf("调度地图附近无轨迹 | 站点ID: %d\n", p_vSiteBuf[i].m_iSiteID);
    }

#ifdef DEBUGSAVE
    l_filePoseWR.close();
    if (l_transSite->points.size() == 0)
    {
        printf("修正站点数量：[%d/%d], 请检查地图转换参数、搜索半径是否配置正确\n",
               (int)l_transSite->points.size(),
               (int)p_vSiteBuf.size());
        return;
    }
    pcl::PCDWriter writer;
    std::string l_path = c_sFilePath_ + c_sDispatchFileName_ + "_site_Correct.pcd";
    writer.writeBinary(l_path, *l_transSite);
    l_path = c_sFilePath_ + c_sDispatchFileName_ + "_site_searchDst.pcd";
    writer.writeBinary(l_path, *l_searchPathDst);
    l_path = c_sFilePath_ + c_sDispatchFileName_ + "_site_searchSrc.pcd";
    writer.writeBinary(l_path, *l_searchPathSrc);
    printf("修正站点数量：[%d/%d]\n", (int)l_transSite->points.size(), (int)p_vSiteBuf.size());
#endif  // DEBUGSAVE
}

void TrajectoryAlign::correctSiteLine_(int p_iSiteID,
                                       s_TWIST p_Trans,
                                       std::vector<st_SiteLineInfo>& p_vSiteLineBuf)
{
    for (size_t i = 0; i < p_vSiteLineBuf.size(); i++)
    {
        for (size_t j = 0; j < p_vSiteLineBuf[i].m_viSiteID.size(); j++)
        {
            // 找到对应ID,确定该行须校正角度
            if (p_iSiteID == p_vSiteLineBuf[i].m_viSiteID[j])
            {
                // 非线段 则所有角度均校正
                if (!p_vSiteLineBuf[i].m_bIsLine)
                {
                    for (size_t k = 0; k < p_vSiteLineBuf[i].m_viSiteID.size(); k++)
                        correctAng_(p_iSiteID,
                                    p_vSiteLineBuf[i].m_vfSiteAng[k],
                                    p_Trans,
                                    c_fIgnoreAngDiff_);
                }
                else
                    correctAng_(
                        p_iSiteID, p_vSiteLineBuf[i].m_vfSiteAng[j], p_Trans, c_fIgnoreAngDiff_);
            }
        }
    }
}

void TrajectoryAlign::correctAng_(int p_iSiteID,
                                  float& p_fAng,
                                  s_TWIST p_Trans,
                                  float p_fIgnoreAngDiff)
{
    s_POSE6D l_pose;
    l_pose.setRPY(0, 0, p_fAng);
    l_pose = p_Trans * l_pose;
    if (fabs(p_fAng - l_pose.yaw()) > p_fIgnoreAngDiff)
    {
        printf("站点[%d]: angDiff %f | %f -> %f\n",
               p_iSiteID,
               fabs(p_fAng - l_pose.yaw()),
               p_fAng,
               l_pose.yaw());
        p_fAng = l_pose.yaw();
    }
    // 约束(-180 180]
    if (p_fAng > 180.0)
        p_fAng -= 360.0;
    if (p_fAng < -180.0)
        p_fAng += 360.0;
}

void TrajectoryAlign::makeTrajectoryDS_(std::deque<st_Trajectory>& p_vSrcPoseBuf,
                                        std::deque<st_Trajectory>& p_vDstPoseBuf,
                                        std::deque<st_Trajectory>& p_vSrcPoseBufDs,
                                        std::deque<st_Trajectory>& p_vDstPoseBufDs)
{
    p_vSrcPoseBufDs.assign(p_vSrcPoseBuf.begin(), p_vSrcPoseBuf.end());
    p_vDstPoseBufDs.assign(p_vDstPoseBuf.begin(), p_vDstPoseBuf.end());
    // //整体体素栅格下采样 取最近点
    // downSizeWholeTrajectory(p_vSrcPoseBufDs, p_vDstPoseBufDs, c_downSizeFilter);
    // 分割直线，分别体素栅格下采样 取最近点
    downSizeLineTrajectory(p_vSrcPoseBufDs, p_vDstPoseBufDs, c_downSizeFilter);

#ifdef DEBUGSAVE
    pcl::PCDWriter writer;
    pointCloudT_Ptr l_save(new pointCloudT());
    toPointCloud_(p_vSrcPoseBuf, l_save);
    writer.writeBinary(c_sFilePath_ + c_sDispatchFileName_ + "_DS_before.pcd", *l_save);
    pointCloudT_Ptr l_save2(new pointCloudT());
    toPointCloud_(p_vSrcPoseBufDs, l_save2);
    writer.writeBinary(c_sFilePath_ + c_sDispatchFileName_ + "_DS_end.pcd", *l_save2);
#endif  // DEBUGSAVE
}

void TrajectoryAlign::downSizeWholeTrajectory(std::deque<st_Trajectory>& p_vSrcPoseBuf,
                                              std::deque<st_Trajectory>& p_vDstPoseBuf,
                                              pcl::VoxelGrid<T>& p_downSizeFilter)
{
    // 下采样
    pointCloudT_Ptr l_downsizePath(new pointCloudT());
    pointCloudT_Ptr l_downsizePathFilter(new pointCloudT());
    toPointCloud_(p_vDstPoseBuf, l_downsizePath);
    p_downSizeFilter.setInputCloud(l_downsizePath);
    p_downSizeFilter.filter(*l_downsizePathFilter);

    // 找最近id
    std::vector<int> l_vSearchId_;
    findNearestPointId_(l_downsizePath, l_downsizePathFilter, l_vSearchId_);

    if (l_vSearchId_.empty())
    {
        printf("downSize value too big\n");
        return;
    }

    // 拷贝最近id作为下采样后轨迹
    std::deque<st_Trajectory> l_dqDstFilter_;
    std::deque<st_Trajectory> l_dqSrcFilter_;
    for (size_t i = 0; i < l_vSearchId_.size(); i++)
    {
        l_dqSrcFilter_.emplace_back(p_vSrcPoseBuf[l_vSearchId_[i]]);
        l_dqDstFilter_.emplace_back(p_vDstPoseBuf[l_vSearchId_[i]]);
    }

    clearDeque_(p_vSrcPoseBuf);
    clearDeque_(p_vDstPoseBuf);
    p_vDstPoseBuf.assign(l_dqDstFilter_.begin(), l_dqDstFilter_.end());
    p_vSrcPoseBuf.assign(l_dqSrcFilter_.begin(), l_dqSrcFilter_.end());
}

void TrajectoryAlign::downSizeLineTrajectory(std::deque<st_Trajectory>& p_vSrcPoseBuf,
                                             std::deque<st_Trajectory>& p_vDstPoseBuf,
                                             pcl::VoxelGrid<T>& p_downSizeFilter)
{
    bool l_bIsLine = false;
    T l_pose;
    std::vector<int> l_vFilterId_;
    pointCloudT_Ptr l_wholePose(new pointCloudT());
    pointCloudT_Ptr l_lineBefore(new pointCloudT());
    toPointCloud_(p_vSrcPoseBuf, l_wholePose);

    // 遍历 分段 进行下采样 得到最近的Pose
    for (size_t i = 0; i < p_vSrcPoseBuf.size(); i++)
    {
        l_pose = T(p_vSrcPoseBuf[i].m_trajectory.m_Pose.x(),
                   p_vSrcPoseBuf[i].m_trajectory.m_Pose.y(),
                   p_vSrcPoseBuf[i].m_trajectory.m_Pose.z());
        if (i + 1 == p_vSrcPoseBuf.size())
        {
            // 最后一个点前序为直线 则此点也属于直线，否则已分段
            if (l_bIsLine)
            {
                l_lineBefore->push_back(l_pose);
                downSizeLine_(l_lineBefore, l_wholePose, p_downSizeFilter, l_vFilterId_);
                // 复位
                l_bIsLine = false;
            }
            else
            {
                // 最后为孤立的转弯点，丢弃？
            }
            l_lineBefore->clear();
        }
        else
        {
            float l_yaw1 = p_vSrcPoseBuf[i].m_trajectory.m_Pose.yaw() + 180.0;
            float l_yaw2 = p_vSrcPoseBuf[i + 1].m_trajectory.m_Pose.yaw() + 180.0;
            // printf("[%d] %f | %f %f\n", i, l_yaw1-l_yaw2, l_yaw1, l_yaw2);
            // 当前点和下一点角度差变大且非孤点则分割前序为直线
            if (fabs(l_yaw1 - l_yaw2) > c_fRotateYawDiff_)
            {
                // printf("cut Line [%d] -> [%d] isLine: %d| %f %f  res: %f\n",
                //        i,
                //        i + 1,
                //        l_bIsLine,
                //        p_vSrcPoseBuf[i].m_trajectory.m_Pose.yaw(),
                //        p_vSrcPoseBuf[i + 1].m_trajectory.m_Pose.yaw(),
                //        c_fRotateYawDiff_);
                // 如果为直线 则下采样
                if (l_bIsLine)
                {
                    downSizeLine_(l_lineBefore, l_wholePose, p_downSizeFilter, l_vFilterId_);
                    // 复位
                    l_bIsLine = false;
                }
                l_lineBefore->clear();
            }
            else
            {
                l_lineBefore->push_back(l_pose);
                l_bIsLine = true;
            }
        }
    }

    // 将过滤后ID填充
    std::deque<st_Trajectory> l_dqDstFilter_;
    std::deque<st_Trajectory> l_dqSrcFilter_;
    for (size_t i = 0; i < l_vFilterId_.size(); i++)
    {
        l_dqSrcFilter_.emplace_back(p_vSrcPoseBuf[l_vFilterId_[i]]);
        l_dqDstFilter_.emplace_back(p_vDstPoseBuf[l_vFilterId_[i]]);
    }

    clearDeque_(p_vSrcPoseBuf);
    clearDeque_(p_vDstPoseBuf);
    p_vDstPoseBuf.assign(l_dqDstFilter_.begin(), l_dqDstFilter_.end());
    p_vSrcPoseBuf.assign(l_dqSrcFilter_.begin(), l_dqSrcFilter_.end());
}

void TrajectoryAlign::downSizeLine_(pointCloudT_Ptr& p_filterIn,
                                    pointCloudT_Ptr& p_wholeBuf,
                                    pcl::VoxelGrid<T>& p_downSizeFilter,
                                    std::vector<int>& p_viFilterId)
{
    pointCloudT_Ptr l_filterIn(new pointCloudT());
    pointCloudT_Ptr l_filterInAddHeadTail(new pointCloudT());
    // 过滤
    p_downSizeFilter.setInputCloud(p_filterIn);
    p_downSizeFilter.filter(*l_filterIn);

    // printf("filter: %d -> %d\n", p_filterIn->points.size(), l_filterIn->points.size());
    // 过滤后追加首尾点
    l_filterInAddHeadTail->points.push_back(p_filterIn->points[0]);
    *l_filterInAddHeadTail += *l_filterIn;
    l_filterInAddHeadTail->points.push_back(p_filterIn->points[p_filterIn->size() - 1]);
    // printf("filter2: %d -> %d\n", p_filterIn->points.size(),
    // l_filterInAddHeadTail->points.size()); 找最近id
    std::vector<int> l_vSearchId_;
    findNearestPointId_(p_wholeBuf, l_filterInAddHeadTail, l_vSearchId_);

    // printf("filter3: %d + %d\n", p_viFilterId.size(), l_vSearchId_.size());
    // 追加至容器
    for (size_t i = 0; i < l_vSearchId_.size(); i++)
        p_viFilterId.emplace_back(l_vSearchId_[i]);
}

void TrajectoryAlign::findNearestPointId_(pointCloudT_Ptr& p_in1,
                                          pointCloudT_Ptr& p_in2,
                                          std::vector<int>& p_vSearchId)
{
    // 创建kd
    std::vector<int> l_vecIndexs;
    std::vector<float> l_vecDists;
    kdtreeT_Ptr l_kdtree(new pcl::KdTreeFLANN<T>());
    l_kdtree->setInputCloud(p_in1);

    for (size_t i = 0; i < p_in2->points.size(); i++)
    {
        l_kdtree->nearestKSearch(p_in2->points[i], 1, l_vecIndexs, l_vecDists);
        if (!l_vecIndexs.empty())
            p_vSearchId.emplace_back(l_vecIndexs[0]);
    }
}

void TrajectoryAlign::alignSrcToDst_(std::deque<st_Trajectory>& p_vSrcPoseBufDs,
                                     std::deque<st_Trajectory>& p_vDstPoseBufDs)
{
    for (size_t i = 0; i < p_vSrcPoseBufDs.size(); i++)
    {
        p_vSrcPoseBufDs[i].m_trajectory.m_Pose =
            p_vSrcPoseBufDs[i].m_trajectory.m_Pose * p_vDstPoseBufDs[i].m_trajectory.m_Twist;
    }

    std::string l_path = c_sFilePath_ + c_sTrajectoryFileName_ + "_new_align_old.pcd";
    saveTrajectoryPcd_(l_path, p_vSrcPoseBufDs);
}

void TrajectoryAlign::alignDstToSrc_(std::deque<st_Trajectory>& p_vSrcPoseBufDs,
                                     std::deque<st_Trajectory>& p_vDstPoseBufDs)
{
    for (size_t i = 0; i < p_vDstPoseBufDs.size(); i++)
    {
        p_vDstPoseBufDs[i].m_trajectory.m_Pose =
            p_vDstPoseBufDs[i].m_trajectory.m_Twist * p_vDstPoseBufDs[i].m_trajectory.m_Pose;
    }

    // std::string l_path = c_sFilePath_ + c_sTrajectoryFileName_ + "_new_align_old.pcd";
    // saveTrajectoryPcd_(l_path, p_vSrcPoseBufDs);
}

void TrajectoryAlign::saveTrajectoryPcd_(std::string p_stPath,
                                         std::deque<st_Trajectory>& p_dqSrcPoseBuf)
{
    pointCloudT_Ptr l_savePath(new pointCloudT());
    T l_transPose;
    for (uint32_t i = 0; i < p_dqSrcPoseBuf.size(); i++)
    {
        l_transPose.x = p_dqSrcPoseBuf[i].m_trajectory.m_Pose.x();
        l_transPose.y = p_dqSrcPoseBuf[i].m_trajectory.m_Pose.y();
        l_transPose.z = p_dqSrcPoseBuf[i].m_trajectory.m_Pose.z();
        l_savePath->points.push_back(l_transPose);
    }
    pcl::PCDWriter writer;
    writer.writeBinary(p_stPath, *l_savePath);
}
// void TrajectoryAlign::alignICP_(std::deque<st_Trajectory>& p_vSrcPoseBufDs,
//                             std::deque<st_Trajectory>& p_vDstPoseBufDs)
// {

// }

// void estimation_umeyama(
// 	std::deque<st_Trajectory>&  pts1,
// 	std::deque<st_Trajectory>&  pts2,
//     int p_size,
// 	Eigen::Matrix4f & TransMatrix
// )
// {
// 	Eigen::Matrix<float, 3, Eigen::Dynamic> cloud_tgt(3, pts1.size());
// 	Eigen::Matrix<float, 3, Eigen::Dynamic> cloud_src(3, pts1.size());
// 	for (int i = 0; i < pointcapture.pts1.size(); i++) {
// 		cloud_src(0, i) = pts1[i].x;
// 		cloud_src(1, i) = pts1[i].y;
// 		cloud_src(2, i) = pts1[i].z;
// 		cloud_tgt(0, i) = pts2[i].x;
// 		cloud_tgt(1, i) = pts2[i].y;
// 		cloud_tgt(2, i) = pts2[i].z;
// 	}
// 	TransMatrix = Eigen::umeyama(cloud_src, cloud_tgt,
// true);//置为true,则可估计不同尺度点集的配准，包含cR、T,c为尺度因子;
// }

void TrajectoryAlign::align_(std::deque<st_Trajectory>& p_vSrcPoseBufDs,
                             std::deque<st_Trajectory>& p_vDstPoseBufDs)
{
    std::vector<int> l_vigetIncrease_(p_vSrcPoseBufDs.size(), 0);
    for (size_t i = 0; i < p_vSrcPoseBufDs.size(); i++)
    {
        // if ((i + 1) < p_vSrcPoseBufDs.size())
        {
            // 角度过大 即转弯处 不作平差
            // if (isAngTooBig_(p_vSrcPoseBufDs[i], p_vSrcPoseBufDs[i + 1]))
            //     continue;

            p_vDstPoseBufDs[i].m_trajectory.m_Twist =
                getIncrease_(p_vDstPoseBufDs[i], p_vSrcPoseBufDs[i]);

            // p_vDstPoseBufDs[i].m_trajectory.m_Twist.printf(std::to_string(i));
            // p_vDstPoseBufDs[i + 1].m_trajectory.m_Twist =
            //     getIncrease_(p_vDstPoseBufDs[i + 1], p_vSrcPoseBufDs[i + 1]);
            l_vigetIncrease_[i] = 1;
            // l_vigetIncrease_[i + 1] = 1;
        }
    }

    // 未设置RT 则使用最近RT
    std::vector<int> l_viNoSetRTId;
    std::vector<int> l_viNearstId;
    for (size_t i = 0; i < l_vigetIncrease_.size(); i++)
    {
        // 未设置RT 则选择最近
        if (l_vigetIncrease_[i] == 0)
            l_viNoSetRTId.emplace_back(i);
    }
    findNearestId_(p_vDstPoseBufDs, l_viNoSetRTId, l_viNearstId);
    for (size_t i = 0; i < l_viNoSetRTId.size(); i++)
        p_vDstPoseBufDs[l_viNoSetRTId[i]].m_trajectory.m_Twist =
            p_vDstPoseBufDs[l_viNearstId[i]].m_trajectory.m_Twist;

#ifdef DEBUGSAVE
    // 调试打印
    std::fstream l_filePoseWR;
    l_filePoseWR.open(c_sFilePath_ + c_sDispatchFileName_ + "_old_align_RT.csv",
                      std::ios::out | std::ios::trunc);
    if (!l_filePoseWR.is_open())
        return;
    for (size_t i = 0; i < p_vDstPoseBufDs.size(); i++)
    {
        l_filePoseWR << i << "," << p_vDstPoseBufDs[i].m_iFrameID << ","
                     << p_vDstPoseBufDs[i].m_trajectory.m_Pose.x() << ","
                     << p_vDstPoseBufDs[i].m_trajectory.m_Pose.y() << ","
                     << p_vDstPoseBufDs[i].m_trajectory.m_Pose.z() << ","
                     << p_vDstPoseBufDs[i].m_trajectory.m_Pose.yaw() << ","

                     << p_vSrcPoseBufDs[i].m_iFrameID << ","
                     << p_vSrcPoseBufDs[i].m_trajectory.m_Pose.x() << ","
                     << p_vSrcPoseBufDs[i].m_trajectory.m_Pose.y() << ","
                     << p_vSrcPoseBufDs[i].m_trajectory.m_Pose.z() << ","
                     << p_vSrcPoseBufDs[i].m_trajectory.m_Pose.yaw() << ","

                     << p_vDstPoseBufDs[i].m_trajectory.m_Twist.x() << ","
                     << p_vDstPoseBufDs[i].m_trajectory.m_Twist.y() << ","
                     << p_vDstPoseBufDs[i].m_trajectory.m_Twist.z() << ","
                     << p_vDstPoseBufDs[i].m_trajectory.m_Twist.roll() << ","
                     << p_vDstPoseBufDs[i].m_trajectory.m_Twist.pitch() << ","
                     << p_vDstPoseBufDs[i].m_trajectory.m_Twist.yaw() << std::endl;
    }
    l_filePoseWR.close();
#endif  // DEBUGSAVE
}

void TrajectoryAlign::findNearestId_(std::deque<st_Trajectory>& p_vPoseBuf,
                                     std::vector<int>& p_vSearchId,
                                     std::vector<int>& p_vNearbyId)
{
    // 转换point 输入kd
    pointCloudT_Ptr l_Path(new pointCloudT());
    toPointCloud_(p_vPoseBuf, l_Path);

    // 创建kd
    std::vector<int> l_vecIndexs;
    std::vector<float> l_vecDists;
    kdtreeT_Ptr l_kdtree(new pcl::KdTreeFLANN<T>());
    l_kdtree->setInputCloud(l_Path);

    for (size_t i = 0; i < p_vSearchId.size(); i++)
    {
        T l_pose = T(p_vPoseBuf[p_vSearchId[i]].m_trajectory.m_Pose.x(),
                     p_vPoseBuf[p_vSearchId[i]].m_trajectory.m_Pose.y(),
                     p_vPoseBuf[p_vSearchId[i]].m_trajectory.m_Pose.z());
        l_kdtree->nearestKSearch(l_pose, 2, l_vecIndexs, l_vecDists);
        if (l_vecIndexs.size() > 1)
            p_vNearbyId.emplace_back(l_vecIndexs[1]);
    }
}

void TrajectoryAlign::toPointCloud_(std::deque<st_Trajectory>& p_vInputBuf,
                                    pointCloudT_Ptr& l_outPointPtr)
{
    l_outPointPtr->clear();
    T l_Pose;
    for (uint32_t i = 0; i < p_vInputBuf.size(); i++)
    {
        l_Pose.x = p_vInputBuf[i].m_trajectory.m_Pose.x();
        l_Pose.y = p_vInputBuf[i].m_trajectory.m_Pose.y();
        l_Pose.z = p_vInputBuf[i].m_trajectory.m_Pose.z();
        l_outPointPtr->points.push_back(l_Pose);
    }
}

void TrajectoryAlign::toPointCloud_I_(std::vector<st_Site>& p_vInputBuf,
                                      pointCloudP_Ptr& l_outPointPtr)
{
    l_outPointPtr->clear();
    P l_Pose;
    for (uint32_t i = 0; i < p_vInputBuf.size(); i++)
    {
        l_Pose.x = p_vInputBuf[i].m_SitePose.x(), l_Pose.y = p_vInputBuf[i].m_SitePose.y();
        l_Pose.z = p_vInputBuf[i].m_SitePose.z();
        l_Pose.intensity = p_vInputBuf[i].m_SitePose.yaw();
        l_outPointPtr->points.push_back(l_Pose);
    }
}

void TrajectoryAlign::interpolation_(std::deque<st_Trajectory>& p_vRawPoseBuf,
                                     std::deque<st_Trajectory>& p_vNewPoseBuf)
{
    for (size_t i = 0; i < p_vRawPoseBuf.size(); i++)
    {
        p_vNewPoseBuf.emplace_back(p_vRawPoseBuf[i]);
        // if ((i + 1) == p_vRawPoseBuf.size())
        //     break;
        // while (1)
        // {
        //     // 转弯处不插值
        //     if (isAngTooBig_(p_vNewPoseBuf.back(), p_vRawPoseBuf[i + 1]))
        //         break;

        //     //密度是否允许插值
        //     if (ableInsert_(p_vNewPoseBuf.back(), p_vRawPoseBuf[i + 1], c_finterpolation_Size_))
        //     {
        //         p_vNewPoseBuf.emplace_back(makeInsertPoint_(
        //             p_vNewPoseBuf.back(), p_vRawPoseBuf[i + 1], c_finterpolation_Size_));
        //     }
        //     else
        //         break;
        // }
    }
#ifdef DEBUGSAVE
    std::fstream l_filePoseWR;
    l_filePoseWR.open(c_sFilePath_ + c_sTrajectoryFileName_ + "_old_insert_RT",
                      std::ios::out | std::ios::trunc);
    if (!l_filePoseWR.is_open())
        return;
    for (size_t i = 0; i < p_vNewPoseBuf.size(); i++)
    {
        l_filePoseWR << i << "," << p_vNewPoseBuf[i].m_trajectory.m_Pose.x() << ","
                     << p_vNewPoseBuf[i].m_trajectory.m_Pose.y() << ","
                     << p_vNewPoseBuf[i].m_trajectory.m_Pose.z() << ","
                     << p_vNewPoseBuf[i].m_trajectory.m_Twist.roll() << ","
                     << p_vNewPoseBuf[i].m_trajectory.m_Twist.pitch() << ","
                     << p_vNewPoseBuf[i].m_trajectory.m_Twist.yaw() << std::endl;
    }
    l_filePoseWR.close();
#endif  // DEBUGSAVE
}
bool TrajectoryAlign::isAngTooBig_(st_Trajectory& a, st_Trajectory& b)
{
    if (fabs(a.m_trajectory.m_Pose.yaw() - b.m_trajectory.m_Pose.yaw()) > c_fTurnYawDiffRes_)
        return true;
    return false;
}

s_POSE6D TrajectoryAlign::getIncrease_(st_Trajectory& a, st_Trajectory& b)
{
    // return a.m_trajectory.m_Pose.inverse() * b.m_trajectory.m_Pose;
    return b.m_trajectory.m_Pose * a.m_trajectory.m_Pose.inverse();
}

bool TrajectoryAlign::ableInsert_(st_Trajectory& a, st_Trajectory& b, float& p_fMinLength)
{
    s_POSE6D l_increase = a.m_trajectory.m_Pose.inverse() * b.m_trajectory.m_Pose;
    if (l_increase.normXY() < p_fMinLength)
        return false;
    return true;
}

st_Trajectory
TrajectoryAlign::makeInsertPoint_(st_Trajectory& a, st_Trajectory& b, float& p_fMinLength)
{
    s_POSE6D l_allIncrease = a.m_trajectory.m_Pose.inverse() * b.m_trajectory.m_Pose;
    float l_DT = p_fMinLength / l_allIncrease.normXY();
    s_POSE6D l_increase = l_allIncrease * l_DT;
    st_Trajectory l_poseTwist;
    l_poseTwist.m_trajectory.m_Pose = a.m_trajectory.m_Pose * l_increase;
    l_poseTwist.m_trajectory.m_Twist =
        transformSlerp_(a.m_trajectory.m_Twist, b.m_trajectory.m_Twist, l_DT);
    return l_poseTwist;
}

s_TWIST TrajectoryAlign::transformSlerp_(s_TWIST& a, s_TWIST& b, float& p_fScale)
{
    s_TWIST l_out;
    //使用匀速运动假设下的姿势变化的四元数线性插值修正
    l_out.m_quat = a.m_quat.slerp(p_fScale, b.m_quat);
    l_out.m_trans = a.m_trans + p_fScale * (b.m_trans - a.m_trans);
    return l_out;
}
}  // namespace wj_slam