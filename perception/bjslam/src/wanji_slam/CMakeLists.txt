cmake_minimum_required(VERSION 2.8.3)
project(wj_slam)
EXECUTE_PROCESS(COMMAND lsb_release -sc
        TIMEOUT 5
        OUTPUT_VARIABLE SYSTEM_TYPE
        OUTPUT_STRIP_TRAILING_WHITESPACE
        )
MESSAGE(STATUS "ubuntu system version: ${SYSTEM_TYPE}")

# 编译设定
set(CMAKE_OPTION FAE) #使用o3编译
# set (CMAKE_OPTION R&D) #使用o1编译
# set (CMAKE_OPTION DEBUG) #使用debug编译

#C++标准17
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_FLAGS "-std=c++17 ${CMAKE_CXX_FLAGS}")




# if(${CMAKE_OPTION} STREQUAL "DEBUG")
#   set(CMAKE_BUILD_TYPE Debug)
# elseif(${CMAKE_OPTION} STREQUAL "R&D")
#   set(CMAKE_BUILD_TYPE Release)
#   set(CMAKE_CXX_FLAGS_RELEASE "-O1 -Wall -g -pthread -DNDEBUG=1 -fsigned-char")
# else()
#   set(CMAKE_BUILD_TYPE Release)
#   set(CMAKE_CXX_FLAGS_RELEASE "-O3 -Wall -g -pthread -DNDEBUG=1 -fsigned-char")
# endif()

if(NOT CMAKE_BUILD_TYPE)
set(CMAKE_BUILD_TYPE Release)
endif()
set(CMAKE_CXX_FLAGS_RELEASE -Ofast)
 
set(CMAKE_CXX_FLAGS "${CMAKE_C_FLAGS} -O3  -Wall")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -O3  -Wall")


# 排除警告
add_compile_options(-Wno-unknown-pragmas -Wno-unused-variable -Wno-unused-but-set-variable -Wno-char-subscripts -Wno-stringop-truncation -Wno-deprecated-declarations -Wno-parentheses -Wno-sequence-point)
# add_compile_options(-Wno-unknown-pragmas -Wno-unused-variable -Wno-unused-but-set-variable -Wno-char-subscripts -Wno-stringop-truncation -Wno-deprecated-declarations -Wno-parentheses -Wno-sequence-point)
# add_compile_definitions(WJSLAM_NO_PRECOMPILE) #不进行预编译

message("#### CMAKE_OPTION= ${CMAKE_OPTION}")
message("#### CMAKE_CXX_STANDARD= ${CMAKE_CXX_STANDARD}")
message("#### CMAKE_CXX_FLAGS= ${CMAKE_CXX_FLAGS}")
message("#### CMAKE_CXX_FLAGS_RELEASE= ${CMAKE_CXX_FLAGS_RELEASE}")
message("#### CMAKE_CXX_COMPILER= ${CMAKE_CXX_COMPILER}")
message("#### CMAKE_CCACHE_COMPILER= ${CCACHE_FOUND}")
message("#### CMAKE_UNITY_BUILD= ${CMAKE_UNITY_BUILD}")
message("#### CMAKE_USE_CORTIRE= ${USE_CORTIRE}")


find_package(
  catkin REQUIRED
  COMPONENTS pcl_ros
            pcl_conversions
             roscpp
             sensor_msgs
             geometry_msgs
             nav_msgs
             message_generation
            visualization_msgs
            interactive_markers)

find_package(
  Boost REQUIRED
  COMPONENTS serialization
             system
             filesystem
             thread
             program_options
             date_time
             timer
             chrono
             regex)

find_package(PkgConfig REQUIRED)
find_package(Eigen3 REQUIRED)
find_package(Ceres REQUIRED)
find_package(GTSAM REQUIRED)
find_package(OpenCV REQUIRED)

#marker client 服务创建
add_service_files(FILES
  GetInit.srv
)

generate_messages(
  DEPENDENCIES visualization_msgs std_msgs geometry_msgs nav_msgs
)

catkin_package(
  INCLUDE_DIRS
  include
  LIBRARIES
  wj_slam
  CATKIN_DEPENDS
  pcl_ros
  roscpp
  sensor_msgs
  geometry_msgs
  nav_msgs
  message_runtime
  visualization_msgs
  interactive_markers
  DEPENDS
  Boost)

  string(REGEX REPLACE "(.*)/(.*)/(.*)" "\\1" CURRENT_FOLDER ${PROJECT_BINARY_DIR})
  MESSAGE(STATUS "LibDIRNET :" ${CURRENT_FOLDER} )
  
  include_directories(include ${CURRENT_FOLDER}/src/wanji_od/test ${catkin_INCLUDE_DIRS} ${Boost_INCLUDE_DIR})
                      # ${EIGEN3_INCLUDE_DIRS} ${GTSAM_INCLUDE_DIR})
  link_directories(${CATKIN_DEVEL_PREFIX}/lib)
  link_directories(${CURRENT_FOLDER}/devel lib)

 # ###################位姿UI########################
if(${SYSTEM_TYPE} STREQUAL "bionic")
    add_executable(wanjiproxy src/master/poseMarker/marker_proxy_18.cpp)
    target_link_libraries(wanjiproxy ${catkin_LIBRARIES})
elseif(${SYSTEM_TYPE} STREQUAL "focal")
    add_executable(wanjiproxy src/master/poseMarker/marker_proxy_20.cpp)
    target_link_libraries(wanjiproxy ${catkin_LIBRARIES})
else()
    message(FATAL_ERROR "Error System is not define")
endif()

 # ###################SLAM主体########################
if(${CMAKE_OPTION} STREQUAL "FAE")
  # ###################网络模块########################
  # ----------------创建 net----------------
  add_library(wj_0 src/net_app/protocol.cpp src/net_app/sick_Protocol.cpp
                   src/net_app/wj_Protocol.cpp 
                   include/tool/protocolTool/protocolTool.cpp
                   include/tool/fileTool/fileTool.cpp)
  target_link_libraries(wj_0 ${catkin_LIBRARIES})
  # ----------------创建 wj_netApp----------------
  add_library(wj_1 src/net_app/netApp.cpp)
  target_link_libraries(wj_1 wj_0 ${catkin_LIBRARIES})

  # ----------------创建 wj_webNetApp----------------
  add_library(wj_2 src/net_app/net_Message_Proc.cpp)
  target_link_libraries(wj_2 wj_0 ${catkin_LIBRARIES})
  # ############################################################################

  # ###################预处理模块########################
  # ----------------创建 特征提取 wj_fE----------------
  add_library(wj_3 src/algorithm/preproc/feature_extract/featureExtract.cpp)
  target_link_libraries(wj_3 ${catkin_LIBRARIES})
  # ----------------创建 预处理 wj_preproc(包括驱动、解包、特征提取、靶标提取)----------------
  add_library(
    wj_4 test/preproc/preproc.cpp src/algorithm/preproc/driver/wanji_driver.cpp
         src/algorithm/preproc/driver/input.cc
         src/algorithm/imu/imuFilter.cpp
         src/algorithm/imu/complementary_filter.cpp)
  target_link_libraries(wj_4 wj_3 ${catkin_LIBRARIES} pcap)
  # ############################################################################

  # ###################里程计模块########################
  # ----------------创建 wj_odom----------------
  add_library(wj_5 test/odomerty/odometry.cpp)
  target_link_libraries(wj_5 ${catkin_LIBRARIES} Ceres::ceres)
  # ############################################################################

  # ###################定位模块########################
  # ----------------创建 动态过滤 wj_rm----------------
  # add_library(wj_6 test/rm/reMoving.cpp)
  # target_link_libraries(wj_6 ${catkin_LIBRARIES})
  add_library(wj_6 src/algorithm/remove_moving/removeMoving.cpp)
  target_link_libraries(wj_6 ${catkin_LIBRARIES})
  # ----------------创建 回环 wj_loop----------------
  add_library(wj_7 test/loop/loopGraph.cpp)
  target_link_libraries(wj_7 ${catkin_LIBRARIES} gtsam ${OpenCV_LIBS} Ceres::ceres)
  # ----------------创建 全局----------------
  # add_library(wj_8 src/algorithm/location/initial_location/initial_location.cpp)
  # target_link_libraries(wj_8 ${catkin_LIBRARIES})
  # ----------------创建 markLocation----------------
  # file(GLOB MarkLocSrc src/algorithm/location/mark_location/*.cpp)
  # file(GLOB_RECURSE MarkLocInc include/algorithm/location/mark_location/*.h)
  # add_library(
  #   wj_8
  #   src/algorithm/location/mark_location/markMatch.cpp
  #   src/algorithm/location/mark_location/mark/EdgeList.cpp
  #   src/algorithm/location/mark_location/mark/FindMarkCenter.cpp
  #   src/algorithm/location/mark_location/mark/IcpSpeedCorr.cpp
  #   src/algorithm/location/mark_location/mark/kdtree.cpp
  #   src/algorithm/location/mark_location/mark/Marks.cpp
  #   src/algorithm/location/mark_location/mark/quicksort.cpp
  #   src/algorithm/location/mark_location/mark/TaskTargetApp.cpp)
  # target_link_libraries(wj_8 ${catkin_LIBRARIES})
  # ----------------创建 定位 wj_loct----------------
  add_library(wj_9 test/location/location.cpp)
  target_link_libraries(wj_9 wj_6 wj_7 ${catkin_LIBRARIES} Ceres::ceres)
  # target_link_libraries(wj_9 wj_7 wj_6 wj_8 ${catkin_LIBRARIES} Ceres::ceres)
  # ############################################################################

  # ###################加密###########################
  add_library(wj_10 src/mac/checkMac.cpp src/mac/AuthAlgo.cpp)
  target_link_libraries(wj_10 ${catkin_LIBRARIES})
  
  # ###################标定###########################
  add_library(wj_11 test/calibration/mLidarCalib.cpp test/calibration/horizonAlign.cpp)
  target_link_libraries(wj_11 ${catkin_LIBRARIES})  

  # ###################地图后处理###########################
  add_library(wj_12 test/mapproc/map_proc.cpp)
  target_link_libraries(wj_12 ${catkin_LIBRARIES})    

  # ###################传感器数据预处理###########################
  add_library(wj_13 test/datapreprocess/datapreprocess.cpp)
  target_link_libraries(wj_13 ${catkin_LIBRARIES})   


  # ###################SLAM###########################
  add_library(slam test/slam/slam.cpp)
  target_link_libraries(
    slam
    wj_1
    wj_2
    wj_4
    wj_5
    wj_9
    wj_10
    wj_11
    wj_12
    ${catkin_LIBRARIES}
    libyaml-cpp.so
    ${CMAKE_SOURCE_DIR}/perception/commonlibrary/lib/libcommonlibrary.so
    )

  # ############################################################################
  # add_executable(wanjislam test/wanji_nodeclass.cpp)
  # target_link_libraries(
  #   wanjislam
  #   slam
  #   ${catkin_LIBRARIES})

  # ############################ Master主体 ########################################
  # ----------------创建 webnet----------------
  add_library(master_0  src/master/webApp/webApp.cpp
                        src/master/webApp/protocolProc.cpp
                        src/master/webApp/web_Protocol.cpp
                        include/tool/fileTool/fileTool.cpp
                        include/tool/protocolTool/protocolTool.cpp)
  target_link_libraries(master_0 ${catkin_LIBRARIES})

  # ----------------创建 virtual_driver----------------
  add_library(master_1  src/master/driver_virtual/virtual_driver.cpp
                        src/master/driver_virtual/input_offline.cpp)
  target_link_libraries(master_1 ${catkin_LIBRARIES} pcap)

  # ----------------创建 recordControl----------------
  add_library(master_2  include/master/recordControl/recordControl.hpp
                        include/master/recordControl/pcapHandle/pcapHandle.cpp
                        include/tool/fileTool/fileTool.cpp)
  target_link_libraries(master_2 ${catkin_LIBRARIES} libpcap.so)

  # ----------------创建 master----------------
  add_executable(wanjimaster test/wanji_master.cpp)
  target_link_libraries(wanjimaster ${catkin_LIBRARIES} slam master_0  master_1 master_2 wj_10 wj_13 libyaml-cpp.so)

  # ###################雷达PCAP分析程序###########################
  add_executable(faeAnalyLidar test/test/analysisLidar.cpp include/tool/fileTool/fileTool.cpp)
  target_link_libraries(faeAnalyLidar ${catkin_LIBRARIES} pcap)

  # ########################在线分析雷达数据包##################################
  add_executable(analysisLidarOnline test/test/analysisLidarOnline.cpp include/tool/fileTool/fileTool.cpp)
  target_link_libraries(analysisLidarOnline ${catkin_LIBRARIES} pcap)
  
  # ############################################################################
    # ###################地图加密程序###########################
    add_executable(faeMapTrans test/test/transformMap.cpp include/tool/fileTool/fileTool.cpp)
    target_link_libraries(faeMapTrans ${catkin_LIBRARIES})
    # ###################地图V1转V2###########################
    add_executable(faeMapTransV1ToV2 test/test/transformMapV1ToV2.cpp include/tool/fileTool/fileTool.cpp)
    target_link_libraries(faeMapTransV1ToV2 ${catkin_LIBRARIES})
    ###################建图过程数据解密程序###########################
    add_executable(faeDataTrans test/test/transformData.cpp include/tool/fileTool/fileTool.cpp)
    target_link_libraries(faeDataTrans ${catkin_LIBRARIES} ${OpenCV_LIBS} gtsam Ceres::ceres)
else()
  # ###################地图加密程序###########################
  add_executable(faeMapTrans test/test/transformMap.cpp include/tool/fileTool/fileTool.cpp)
  target_link_libraries(faeMapTrans ${catkin_LIBRARIES})
  # ###################地图V1转V2###########################
  add_executable(faeMapTransV1ToV2 test/test/transformMapV1ToV2.cpp include/tool/fileTool/fileTool.cpp)
  target_link_libraries(faeMapTransV1ToV2 ${catkin_LIBRARIES})
  # ############################################################################
  
  # ###################AGV数据分析程序###########################
  add_executable(analyAGV test/test/analyAGV.cpp include/tool/fileTool/fileTool.cpp)
  target_link_libraries(analyAGV ${catkin_LIBRARIES} pcap)
  # ############################################################################

  # # ###################地图过滤程序###########################
  # add_executable(mapproc test/test/mapProc.cpp)
  # target_link_libraries(mapproc ${catkin_LIBRARIES})
  # # ############################################################################

  # ###################PCAP合并程序###########################
  add_executable(mergePcap test/test/mergePcap.cpp)
  target_link_libraries(mergePcap ${catkin_LIBRARIES} pcap)
  # ############################################################################

  # ###################地图对齐###########################
  add_executable(mapAlign test/test/mapAlign/mapAlign.cpp test/test/mapAlign/dispatchMap/dispatchMap.cpp include/tool/fileTool/fileTool.cpp
  test/test/mapAlign/trajectoryAlign/trajectoryAlign.cpp)
  target_link_libraries(mapAlign ${catkin_LIBRARIES})
  # ############################################################################
endif()

