/*
 * @Description:
 * @Version: 1.0
 * @Autor: zushuang
 * @Date: 2021-10-25 13:45:12
 * @LastEditors: zushuang
 * @LastEditTime: 2021-10-25 13:45:12
 */
#pragma once

#include <chrono>
#include <cstdlib>
#include <ctime>

class TicToc {
  public:
    TicToc()
    {
        tic();
    }

    TicToc(long p_preT)
    {
      tic(p_preT);
    }

    void tic()
    {
        start = std::chrono::system_clock::now();
    }

    double toc()
    {
        end = std::chrono::system_clock::now();
        std::chrono::duration<double> elapsed_seconds = end - start;
        return elapsed_seconds.count() * 1000;  // ms
    }

    void tic(long p_preT)
    {
      c_preT_ = p_preT;
    }

    long toc(long p_curT)
    {
      c_curT_ = p_curT;
      return c_curT_ - c_preT_;
    }

  private:
    std::chrono::time_point<std::chrono::system_clock> start, end;
    long c_preT_;
    long c_curT_;
};