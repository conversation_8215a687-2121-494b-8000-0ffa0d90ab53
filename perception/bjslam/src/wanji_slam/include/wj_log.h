/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON>
 * @Date: 2021-09-16 09:29:16
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-06 18:16:56
 */
#pragma once
#include "spdlog/common.h"
#include "spdlog/logger.h"
#include "spdlog/pattern_formatter.h"
#include "spdlog/sinks/basic_file_sink.h"
#include "spdlog/sinks/daily_file_sink.h"
#include "spdlog/sinks/rotating_file_sink.h"
#include "spdlog/sinks/stdout_color_sinks.h"
#include "spdlog/sinks/wj_sink.h"
#include "spdlog/spdlog.h"
#include "wj_color.h"
#include <chrono>
#include <cstdlib>
#include <ctime>
#include <iostream>
#include <sstream>
#include <string>
#include <sys/stat.h>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>

////////////////////////////// usecase //////////////////////////////
/*
1：通过如下define 控制基本选项：
    （1）LOG_PRINT                      默认开启    关闭：不打印/保存任何日志
    （2）LOG_PREMOD                     默认开启    关闭：模块日志（LOG-PFOM）全部输出到LOGW
    （3）LOG_TIMECOST                   默认关闭    开启：输出耗时分析文件
    （4）CONSOLE_PRINT_ONLYWARN         默认关闭    开启：控制台可打印日志同等级信息
    （5）LOG_SEC/LOG_MILLI/LOG_MICRO    默认LOG_MICRO 日志时间精度：秒/微妙/毫秒级别的时间
2：通过 LogMode+FileMode 控制日志形式和输出等级
    LogMode：
    （1）LAB_MODE     研发模式：输出/保存全部信息（Trace->Debug->info->warn->error）
    （2）TRIAL_MODE   试用模式：输出/保存INFO信息（info->warn->error）
    （3）USER_MODE    用户模式：保存INFO信息（info->warn->error）
    （4）TEST_MODE    测试模式：保存全部信息（针对测试使用）
    FileMode：
    （1）BASIC普通模式：
每次启动创建一个日志文件，如果一分钟内重复启动则覆盖（文件名：自定义_年-月-日_时-分.log）
    （2）DAILY每日模式：
每天第一次启动时创建一个日志文件，如果程序运行跨天创建新日志文件，如果当日重复启动则追加（文件名：自定义_年-月-日.log）
    （3）ROTATE轮转模式： 启动创建一个日志文件，当大小溢出转移文件，并创建新的文件
（文件名：自定义_年-月-日.log -> .1.log -> .2.log ---） 3：日志定义（最好在main函数所在文件）：
    （4）DAILY_ROTATE 每日半轮转模式： 每天第一次启动时创建一个日志文件
同时当大小溢出转移文件，并创建新的文件 将当前文件名字增加当前时分秒 （文件名：自定义_年-月-日.log ->
自定义_年-月-日-23/05/01.log）
    // 通用日志
    WJLog* generalLog::wjlog = nullptr;
    // FAE日志
    WJLog* generalLog::faelog = nullptr;
    // agv path日志
    WJLog* generalLog::pathlog = nullptr;
    // 校验日志
    WJLog* generalLog:checklog = nullptr;
    // agv speed日志
    WJLog* generalLog::speedlog = nullptr;

    #ifdef LOG_PREMOD
    // 预处理日志
    WJLog* generalLog::prlog = nullptr;
    // 特征提取日志
    WJLog* generalLog::felog = nullptr;
    // 里程计日志
    WJLog* generalLog::odlog = nullptr;
    // 定位日志
    WJLog* generalLog::lmlog = nullptr;
    // 子图日志
    WJLog* generalLog::submaplog = nullptr;
    // 预估里程计日志
    WJLog* generalLog::odfslog = nullptr;
    // 驱动日志
    WJLog* generalLog::drlog = nullptr;
    // 解包子日志
    WJLog* generalLog::convlog = nullptr;
    // 协议日志
    WJLog* generalLog::protolog = nullptr;
    // web日志
    WJLog* generalLog::weblog = nullptr;
    #endif
    #ifdef LOG_TIMECOST
    // 耗时日志
    WJLog* generalLog::tclog = nullptr;
    #endif
4：日志初始化：
    generalLog* glog = new generalLog( 全局路径 + "/log/", "自定义名", FileMode::文件形式,
LogMode::日志等级); 5：日志使用： （1）日志分为不同模块： LOGW -- 通用日志    LOGT -- 时间测试日志
        LOGP -- 预处理日志  LOGF -- 特征提取日志
        LOGO -- 里程计日志  LOGM -- 定位建图日志
    （2）日志分为不同等级：
        /--WDEBUG--/--WINFO--/--WWARN--/--WERROR--/
*/
/////////////////////////////////////////////////////////////////////

// 是否记录日志
#define LOG_PRINT
// 是否创建独立模块日志
// #define LOG_PREMOD

// 是否进行耗时分析
// #define LOG_TIMECOST

//  是否控制打印等级
// #define CONSOLE_PRINT_ONLYWARN

/* 时间精度 */
// 秒级
// #define LOG_SEC
// 微妙级
// #define LOG_MILLI
// 毫秒级
#define LOG_MICRO

/*
日志级别：
*/
enum LogMode {
    LAB_MODE = 0,    //实验模式，打印全部信息（同时输出控制台和文件）
    TRIAL_MODE = 1,  //试用模式，打印重要信息（同时输出控制台和文件）
    USER_MODE = 2,   //用户模式，打印重要信息（只输出文件）
    TEST_MODE = 3,   //测试模式，打印全部信息（输出文件）
};
/*
文件形式
*/
enum FileMode {
    BASIC = 0,         // 普通模式（启动创建文件后追加内容）
    DAILY = 1,         // 每日重置（一次启动越过设定时刻则创建新日志）
    ROTATE = 2,        // 轮转模式（每个日志达到设定大小则创建新日志）
    DAILY_ROTATE = 3,  //每日新建-超过大小增加时间后缀并保存
};
//变量重命名
using logLev = spdlog::level::level_enum;
using fmt_str = spdlog::string_view_t;

#define WTRACE logLev::trace
#define WDEBUG logLev::debug
#define WINFO logLev::info
#define WWARN logLev::warn
#define WERROR logLev::err

class program_duration_formatter_flag : public spdlog::custom_flag_formatter {
  public:
    void
    format(const spdlog::details::log_msg&, const std::tm&, spdlog::memory_buf_t& dest) override
    {
        //日志创建时间
        static std::chrono::time_point<std::chrono::system_clock> c_sT =
            std::chrono::system_clock::now();
#ifdef LOG_SEC
        std::string duration = std::to_string(std::chrono::duration_cast<std::chrono::seconds>(
                                                  std::chrono::system_clock::now() - c_sT)
                                                  .count());
#elif defined(LOG_MILLI)
        std::string duration = std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(
                                                  std::chrono::system_clock::now() - c_sT)
                                                  .count()
                                              / 1000.0f);
#elif defined(LOG_MICRO)
        std::string duration = std::to_string(std::chrono::duration_cast<std::chrono::microseconds>(
                                                  std::chrono::system_clock::now() - c_sT)
                                                  .count()
                                              / 1000000.0f);
#endif
        dest.append(duration.data(), duration.data() + duration.size());
    }

    std::unique_ptr<custom_flag_formatter> clone() const override
    {
        return spdlog::details::make_unique<program_duration_formatter_flag>();
    }
};

class WJLog {
  protected:
    // spdlog指针
    std::shared_ptr<spdlog::logger> c_pLogger_;
    // 日志文件全路径
    std::string c_sLogFullName_;
    // 日志文件名
    std::string c_sLogPath_;
    std::string c_sLogName_;
    //标识日志创建状态
    bool c_bLogCreated_;
    //标识日志关闭状态
    bool c_bLogDroped_;

  public:
    WJLog()
    {
        c_bLogCreated_ = false;
        c_bLogDroped_ = false;
    }
    virtual ~WJLog()
    {
        // 销毁日志指针
        if (c_bLogCreated_ && !c_bLogDroped_)
            destroyLogger();
    }
    /**
     * @description: 创建logger的纯虚函数，只能在子函数中实现
     * @param {*}
     * @return {*}
     * @other:
     */
    virtual void createLogger() = 0;
    /* 销毁logger */
    void destroyLogger()
    {
        spdlog::drop_all();
        c_bLogDroped_ = true;
    }
    /**
     * @description: 设置日志的打印级别
     * @param {logLev} lvl
     * @return {*}
     * @other:
     */
    void setPrintLevel(logLev lvl = logLev::info)
    {
        c_pLogger_->set_level(lvl);
    }
    /**
     * @description: 设置日志的打印前缀
     * @param {logLev} lvl
     * @return {*}
     * @other:
     */
    void setPrintPattern(std::string pattern)
    {
        c_pLogger_->set_pattern(pattern.c_str());
    }
    void setDefaultFormat()
    {
        auto formatter = std::make_unique<spdlog::pattern_formatter>();
        formatter->add_flag<program_duration_formatter_flag>('*').set_pattern(
            "[%*][%n][%^%L%$] %v");
        c_pLogger_->set_formatter(std::move(formatter));
    }
    static std::string getSystemTime()
    {
        struct tm* cur_time;
        time_t local_time;
        time(&local_time);
        cur_time = localtime(&local_time);

        // 通过时间命名日志文件
        char timename[200];
        strftime(timename, 100, "%m/%d_%H_%M_%S", cur_time);
        return std::string(timename);
    }
    static std::string getWholeSysTime()
    {
        struct timeval l_CurrTime_;
        gettimeofday(&l_CurrTime_, NULL);

        char timename[100];
        struct tm* ptm;
        double milliseconds;

        ptm = localtime(&(l_CurrTime_.tv_sec));
        strftime(timename, sizeof(timename), "%Y-%m-%d %H:%M:%S", ptm);

        sprintf(timename, "%s.%06ld", timename, l_CurrTime_.tv_usec);
        return std::string(timename);
    }

    /* 重新封装logger的trace, debug, warn, error和critical打印接口 */
    template <typename... Args> inline void log(int level, fmt_str fmt, const Args&... args)
    {
#ifdef LOG_PRINT
        switch (level)
        {
            case logLev::trace: c_pLogger_->trace(fmt, args...); break;
            case logLev::debug: c_pLogger_->debug(fmt, args...); break;
            case logLev::info: c_pLogger_->info(fmt, args...); break;
            case logLev::warn: c_pLogger_->warn(fmt, args...); break;
            case logLev::err: c_pLogger_->error(fmt, args...); break;
            case logLev::critical: c_pLogger_->critical(fmt, args...); break;
            default: break;
        }
#endif
    }

  protected:
    /**
     * @description: 创建以时间命名的日志文件（同时检查/创建文件路径）
     * @param {*}
     * @return {*}
     * @other:
     */
    virtual void autoSetFileName(std::string p_sFilename)
    {
        int npos = p_sFilename.find_last_of('.');
        std::string sub1 = p_sFilename.substr(0, npos);
        std::string sub2 = p_sFilename.substr(npos);

        // 文件名
        c_sLogName_ = sub1 + "_" + getSystemTime() + sub2;

        // 判断日志路径是否存在，若不存在，则创建
        if (access(c_sLogPath_.c_str(), F_OK) != 0)
        {
            mkdir(c_sLogPath_.c_str(), S_IRWXU);
        }

        // 日志文件全路径
        c_sLogFullName_ = c_sLogPath_ + c_sLogName_;
    }
    /**
     * @description: 除了使用时间命名日志文件，用户还可以自定义日志文件名称
     * @param {string} filename 自定义日志文件名称
     * @return {*}
     * @other:
     */
    virtual void menuSetFileName(std::string p_sFilename)
    {
        //判断日志路径是否存在，若不存在，则创建
        if (access(c_sLogPath_.c_str(), F_OK) != 0)
        {
            mkdir(c_sLogPath_.c_str(), S_IRWXU);
        }
        c_sLogName_ = p_sFilename;
        // 日志文件全路径
        c_sLogFullName_ = c_sLogPath_ + c_sLogName_;
    }
};

/* 控制台logger子类 */
class ConsoleLogger : public WJLog {
  public:
    ConsoleLogger() {}

    //对于控制台子类来说，无需生成file文件，因此该方法的函数体置空
    void autoSetFileName(std::string p_sFilename) {}
    void menuSetFileName(std::string p_sFilename) {}

    virtual void createLogger()
    {
        try
        {
            c_pLogger_ = spdlog::stdout_color_mt("console");
        }
        catch (const spdlog::spdlog_ex& ex)
        {
            std::cout << WJCOL_RED << "Create console logger failed: " << ex.what() << WJCOL_RESET
                      << std::endl;
            exit(EXIT_FAILURE);
        }
        if (!c_pLogger_)
        {
            std::cout << WJCOL_RED << "Create console logger failed." << WJCOL_RESET << std::endl;
        }
        else
        {
            c_bLogCreated_ = true;
        }
    }
};

/* 文件logger子类 */
class FileLogger : public WJLog {
  public:
    FileLogger(std::string p_logName,
               std::string p_strPath,
               std::string p_strName,
               int p_iFileMode,
               int p_iFileSize = 10485760)
        : c_iFileMode_(p_iFileMode), c_iFileSize_(p_iFileSize), c_sName_(p_logName)
    {
        c_sLogPath_ = p_strPath;
        c_sLogName_ = p_strName;
    }

    virtual void createLogger()
    {
        try
        {
            switch (c_iFileMode_)
            {
                case FileMode::BASIC:
                {
                    autoSetFileName(c_sLogName_);
                    //创建basic_logger，注意该函数创建的是支持多线程的文件输出
                    c_pLogger_ = spdlog::basic_logger_mt(c_sName_, c_sLogFullName_.c_str());
                    break;
                }
                case FileMode::DAILY:
                {
                    menuSetFileName(c_sLogName_);
                    // 按文件大小
                    c_pLogger_ =
                        spdlog::rotating_logger_mt(c_sName_, c_sLogFullName_.c_str(), 1e7, 3);
                    break;
                }
                case FileMode::ROTATE:
                {
                    menuSetFileName(c_sLogName_);
                    // 每天2:30 am 新建一个日志文件
                    c_pLogger_ = spdlog::daily_logger_mt(c_sName_, c_sLogFullName_.c_str(), 2, 30);
                    break;
                }
                case FileMode::DAILY_ROTATE:
                {
                    menuSetFileName(c_sLogName_);
                    // 跨天新建，同时按文件大小 单位字节 1MB=1024kb=1024字节 设定近100MB = 104857600
                    c_pLogger_ =
                        spdlog::wj_logger_mt(c_sName_, c_sLogFullName_.c_str(), c_iFileSize_, 0);
                    break;
                }
                default:
                {
                    autoSetFileName(c_sLogName_);
                    //创建basic_logger，注意该函数创建的是支持多线程的文件输出
                    c_pLogger_ = spdlog::basic_logger_mt(c_sName_, c_sLogFullName_.c_str());
                    break;
                }
            }
            // 遇到warn flush日志，防止丢失
            c_pLogger_->flush_on(spdlog::level::warn);
        }
        catch (const spdlog::spdlog_ex& ex)
        {
            std::cout << WJCOL_RED << "Create file logger failed: " << ex.what() << WJCOL_RESET
                      << std::endl;
            exit(EXIT_FAILURE);
        }

        if (!c_pLogger_)
        {
            std::cout << WJCOL_RED << "Create file logger failed." << WJCOL_RESET << std::endl;
        }
        else
        {
            c_bLogCreated_ = true;
        }
    }

  private:
    int c_iFileMode_;
    int c_iFileSize_;
    std::string c_sName_;
};

/* 控制台+文件复合logger子类 */
class MultiLogger : public WJLog {
  public:
    MultiLogger(std::string p_logName,
                std::string p_strPath,
                std::string p_strName,
                int p_iFileMode,
                int p_iFileSize = 10485760,
                bool p_bLimitConsoleLev = false)
        : c_iFileMode_(p_iFileMode), c_iFileSize_(p_iFileSize), c_sName_(p_logName),
          c_bLimitConsoleLev_(p_bLimitConsoleLev)
    {
        c_sLogPath_ = p_strPath;
        c_sLogName_ = p_strName;
    }

    virtual void createLogger()
    {
        try
        {
            /* 通过multi-sink的方式创建复合logger，实现方式为：先分别创建文件sink和控制台sink，并将两者放入sink
             * 向量中，组成一个复合logger */
            /* 文件sink */
            spdlog::sink_ptr file_sink;
            switch (c_iFileMode_)
            {
                case FileMode::BASIC:
                {
                    autoSetFileName(c_sLogName_);
                    //创建basic_logger，注意该函数创建的是支持多线程的文件输出
                    file_sink = std::make_shared<spdlog::sinks::basic_file_sink_mt>(
                        c_sLogFullName_.c_str(), true);
                    break;
                }
                case FileMode::ROTATE:
                {
                    menuSetFileName(c_sLogName_);
                    // 按文件大小（Bytes）
                    file_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
                        c_sLogFullName_.c_str(), 1e7, 5, true);
                    break;
                }
                case FileMode::DAILY:
                {
                    menuSetFileName(c_sLogName_);
                    // 每天2:30 am 新建一个日志文件
                    file_sink = std::make_shared<spdlog::sinks::daily_file_sink_mt>(
                        c_sLogFullName_.c_str(), 2, 30, true);
                    break;
                }
                case FileMode::DAILY_ROTATE:
                {
                    menuSetFileName(c_sLogName_);
                    // 隔天新建一个日志文件 超过大小1G则新建 增加时分秒
                    file_sink = std::make_shared<spdlog::sinks::wj_sink_mt>(
                        c_sLogFullName_.c_str(), c_iFileSize_, 0);
                    break;
                }
                default:
                {
                    autoSetFileName(c_sLogName_);
                    //创建basic_logger，注意该函数创建的是支持多线程的文件输出
                    c_pLogger_ = spdlog::basic_logger_mt(c_sName_, c_sLogFullName_.c_str());
                    break;
                }
            }
            /* 控制台sink */
            auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
            if (c_bLimitConsoleLev_)
                console_sink->set_level(spdlog::level::warn);
#ifdef CONSOLE_PRINT_ONLYWARN
            // 单独限制控制台打印级别
            if (!c_bLimitConsoleLev_)
                console_sink->set_level(spdlog::level::warn);
#endif
            /* Sink组合 */
            std::vector<spdlog::sink_ptr> sinks;
            sinks.push_back(console_sink);
            sinks.push_back(file_sink);
            c_pLogger_ = std::make_shared<spdlog::logger>(c_sName_, begin(sinks), end(sinks));
            // 遇到warn flush日志，防止丢失
            c_pLogger_->flush_on(spdlog::level::trace);
        }
        catch (const spdlog::spdlog_ex& ex)
        {
            std::cout << WJCOL_RED << "Create file logger failed: " << ex.what() << WJCOL_RESET
                      << std::endl;
            exit(EXIT_FAILURE);
        }
        if (!c_pLogger_)
        {
            std::cout << WJCOL_RED << "Create logger failed." << WJCOL_RESET << std::endl;
        }
        else
        {
            c_bLogCreated_ = true;
        }
    }

  private:
    int c_iFileMode_;
    int c_iFileSize_;
    std::string c_sName_;
    bool c_bLimitConsoleLev_;
};

/* Logger选择类，通过传入的logger模式生成相应的logger */
class WJLogSelect {
  public:
    WJLogSelect() {}
    WJLogSelect(std::string p_slogName,
                std::string p_strPath,
                std::string p_strName,
                int p_iFileMode,
                bool p_bLimitConsoleLev = false)
        : c_sFilePath_(p_strPath), c_sFileName_(p_strName), c_sName_(p_slogName),
          c_iFileMode_(p_iFileMode), c_bLimitConsoleLev_(p_bLimitConsoleLev)
    {
    }
    ~WJLogSelect() {}

    WJLog* setMode(int p_iLogMode, int p_iFileSize)
    {
        WJLog* p_logger = NULL;

        switch (p_iLogMode)
        {
            case LAB_MODE:
            {
                p_logger = new MultiLogger(c_sName_,
                                           c_sFilePath_,
                                           c_sFileName_,
                                           c_iFileMode_,
                                           p_iFileSize,
                                           c_bLimitConsoleLev_);
                p_logger->createLogger();
                p_logger->setPrintLevel(logLev::debug);
                p_logger->setDefaultFormat();
                break;
            }
            case TRIAL_MODE:
            {
                p_logger = new MultiLogger(c_sName_,
                                           c_sFilePath_,
                                           c_sFileName_,
                                           c_iFileMode_,
                                           p_iFileSize,
                                           c_bLimitConsoleLev_);
                p_logger->createLogger();
                p_logger->setPrintLevel(logLev::info);
                p_logger->setDefaultFormat();
                break;
            }
            case USER_MODE:
            {
                p_logger =
                    new FileLogger(c_sName_, c_sFilePath_, c_sFileName_, c_iFileMode_, p_iFileSize);
                p_logger->createLogger();
                p_logger->setPrintLevel(logLev::info);
                p_logger->setDefaultFormat();
                break;
            }
            case TEST_MODE:
            {
                p_logger =
                    new FileLogger(c_sName_, c_sFilePath_, c_sFileName_, c_iFileMode_, p_iFileSize);
                p_logger->createLogger();
                p_logger->setPrintLevel(logLev::trace);
                p_logger->setDefaultFormat();
                break;
            }
            default:
            {
                p_logger =
                    new FileLogger(c_sName_, c_sFilePath_, c_sFileName_, c_iFileMode_, p_iFileSize);
                p_logger->createLogger();
                p_logger->setPrintLevel(logLev::info);
                p_logger->setDefaultFormat();
                break;
            }
        }

        return p_logger;
    }

  private:
    std::string c_sFilePath_;
    std::string c_sFileName_;
    std::string c_sName_;
    int c_iFileMode_;
    bool c_bLimitConsoleLev_;
};

/*全局日志记录器*/
class generalLog {
  private:
    /* data */
  public:
    // 通用日志
    static WJLog* wjlog;
    // 对外日志
    static WJLog* faelog;
    // 路径日志
    static WJLog* pathlog;
    // 校验日志
    static WJLog* checklog;
    // 速度日志
    static WJLog* speedlog;
#ifdef LOG_PREMOD
    // 预处理日志
    static WJLog* prlog;
    // 特征提取日志
    static WJLog* felog;
    // 里程计日志
    static WJLog* odlog;
    // 定位日志
    static WJLog* lmlog;
    // 子图日志
    static WJLog* submaplog;
    // 预估里程计日志
    static WJLog* odfslog;
    // 驱动日志
    static WJLog* drlog;
    // 解包日志
    static WJLog* convlog;
    // 协议日志
    static WJLog* protolog;
    // web日志
    static WJLog* weblog
#endif
#ifdef LOG_TIMECOST
        // 耗时日志
        static WJLog* tclog;
#endif
  public:
    generalLog(std::string p_strPath,
               std::string p_strName,
               int p_iFileMode,
               int p_iLogMode,
               int p_iFileSize)
    {
        std::string l_strName_main = p_strName + ".log";
        wjlog = (new WJLogSelect("W", p_strPath, l_strName_main, p_iFileMode))
                    ->setMode(p_iLogMode, p_iFileSize);

        std::string l_strName_path = p_strName + "_path.log";
        pathlog = (new WJLogSelect("path", p_strPath, l_strName_path, p_iFileMode, true))
                      ->setMode(p_iLogMode, p_iFileSize);

        std::string l_strName_check = p_strName + "_check.log";
        checklog = (new WJLogSelect("check", p_strPath, l_strName_check, p_iFileMode, true))
                       ->setMode(p_iLogMode, p_iFileSize);

        std::string l_strName_speed = p_strName + "_speed.log";
        speedlog = (new WJLogSelect("speed", p_strPath, l_strName_speed, p_iFileMode, true))
                       ->setMode(p_iLogMode, p_iFileSize);

        std::string l_strName_fae = p_strName + "_main.log";
        faelog = (new WJLogSelect("FAE", p_strPath, l_strName_fae, p_iFileMode))
                     ->setMode(p_iLogMode, p_iFileSize);

#ifdef LOG_PREMOD

        std::string l_strName_preproc = p_strName + "_pp.log";
        prlog = (new WJLogSelect("pp", p_strPath, l_strName_preproc, p_iFileMode))
                    ->setMode(p_iLogMode, p_iFileSize);

        std::string l_strName_feature = p_strName + "_fe.log";
        felog = (new WJLogSelect("fe", p_strPath, l_strName_feature, p_iFileMode))
                    ->setMode(p_iLogMode, p_iFileSize);

        std::string l_strName_odometry = p_strName + "_od.log";
        odlog = (new WJLogSelect("od", p_strPath, l_strName_odometry, p_iFileMode))
                    ->setMode(p_iLogMode, p_iFileSize);

        std::string l_strName_mapping = p_strName + "_lm.log";
        lmlog = (new WJLogSelect("lm", p_strPath, l_strName_mapping, p_iFileMode))
                    ->setMode(p_iLogMode, p_iFileSize);

        std::string l_strName_submap = p_strName + "_sm.log";
        submaplog = (new WJLogSelect("sm", p_strPath, l_strName_submap, p_iFileMode))
                        ->setMode(p_iLogMode, p_iFileSize);
        std::string l_strName_odomfuse = p_strName + "_odfs.log";
        odfslog = (new WJLogSelect("odfs", p_strPath, l_strName_odomfuse, p_iFileMode))
                      ->setMode(p_iLogMode, p_iFileSize);
        std::string l_strName_input = p_strName + "_dr.log";
        drlog = (new WJLogSelect("dr", p_strPath, l_strName_input, p_iFileMode))
                    ->setMode(p_iLogMode, p_iFileSize);

        std::string l_strName_conv = p_strName + "_conv.log";
        convlog = (new WJLogSelect("conv", p_strPath, l_strName_conv, p_iFileMode))
                      ->setMode(p_iLogMode, p_iFileSize);

        std::string l_strName_protol = p_strName + "_pt.log";
        protolog = (new WJLogSelect("pt", p_strPath, l_strName_protol, p_iFileMode))
                       ->setMode(p_iLogMode, p_iFileSize);

        std::string l_strName_web = p_strName + "_web.log";
        weblog = (new WJLogSelect("web", p_strPath, l_strName_web, p_iFileMode))
                     ->setMode(p_iLogMode, p_iFileSize);
#endif
#ifdef LOG_TIMECOST
        std::string l_strName_timecost = p_strName + "_t.log";
        tclog = (new WJLogSelect("T", p_strPath, l_strName_timecost, p_iFileMode))
                    ->setMode(LogMode::TEST_MODE, p_iFileSize);
#endif
    }
    ~generalLog()
    {
        wjlog->~WJLog();
        pathlog->~WJLog();
        checklog->~WJLog();
        speedlog->~WJLog();
        faelog->~WJLog();
#ifdef LOG_PREMOD
        prlog->~WJLog();
        felog->~WJLog();
        odlog->~WJLog();
        lmlog->~WJLog();
        submaplog->~WJLog();
        odfslog->~WJLog();
        drlog->~WJLog();
        convlog->~WJLog();
        protolog->~WJLog();
        weblog->~WJLog();
#endif
#ifdef LOG_TIMECOST
        tclog->~WJLog();
#endif
    }

    void changeMode(int p_iLogMode)
    {
        if (p_iLogMode == 0)
            return;
        logLev l_logLevel = logLev::info;
        switch (p_iLogMode)
        {
            case 1: l_logLevel = logLev::err; break;
            case 2: l_logLevel = logLev::warn; break;
            case 3: l_logLevel = logLev::info; break;
            case 4: l_logLevel = logLev::debug; break;
            case 5: l_logLevel = logLev::trace; break;
            default: l_logLevel = logLev::info; break;
        }
        wjlog->setPrintLevel(l_logLevel);
        pathlog->setPrintLevel(l_logLevel);
        checklog->setPrintLevel(l_logLevel);
        speedlog->setPrintLevel(l_logLevel);
        faelog->setPrintLevel(l_logLevel);
#ifdef LOG_PREMOD
        prlog->setPrintLevel(l_logLevel);
        felog->setPrintLevel(l_logLevel);
        odlog->setPrintLevel(l_logLevel);
        lmlog->setPrintLevel(l_logLevel);
        submaplog->setPrintLevel(l_logLevel);
        odfslog->setPrintLevel(l_logLevel);
        drlog->setPrintLevel(l_logLevel);
        convlog->setPrintLevel(l_logLevel);
        protolog->setPrintLevel(l_logLevel);
        weblog->setPrintLevel(l_logLevel);
#endif
#ifdef LOG_TIMECOST
        tclog->setPrintLevel(l_logLevel);
#endif
    }
};

//封装全局日志输出
#define LOGW(...) generalLog::wjlog->log(__VA_ARGS__)
#define LOGPATH(...) generalLog::pathlog->log(__VA_ARGS__)
#define LOGCHECK(...) generalLog::checklog->log(__VA_ARGS__)
#define LOGSPEED(...) generalLog::speedlog->log(__VA_ARGS__)
#define LOGFAE(...) generalLog::faelog->log(__VA_ARGS__)

//封装独立日志模块
#ifdef LOG_PREMOD
#    define LOGP(...) generalLog::prlog->log(__VA_ARGS__)
#    define LOGF(...) generalLog::felog->log(__VA_ARGS__)
#    define LOGO(...) generalLog::odlog->log(__VA_ARGS__)
#    define LOGM(...) generalLog::lmlog->log(__VA_ARGS__)
#    define LOGSP(...) generalLog::submaplog->log(__VA_ARGS__)
#    define LOGOF(...) generalLog::odfslog->log(__VA_ARGS__)
#    define LOGDR(...) generalLog::drlog->log(__VA_ARGS__)
#    define LOGCONV(...) generalLog::convlog->log(__VA_ARGS__)
#    define LOGPT(...) generalLog::protolog->log(__VA_ARGS__)
#    define LOGWEB(...) generalLog::weblog->log(__VA_ARGS__)
#else
#    define LOGP(...) generalLog::wjlog->log(__VA_ARGS__)
#    define LOGF(...) generalLog::wjlog->log(__VA_ARGS__)
#    define LOGO(...) generalLog::wjlog->log(__VA_ARGS__)
#    define LOGM(...) generalLog::wjlog->log(__VA_ARGS__)
#    define LOGSP(...) generalLog::wjlog->log(__VA_ARGS__)
#    define LOGOF(...) generalLog::wjlog->log(__VA_ARGS__)
#    define LOGDR(...) generalLog::wjlog->log(__VA_ARGS__)
#    define LOGCONV(...) generalLog::wjlog->log(__VA_ARGS__)
#    define LOGPT(...) generalLog::wjlog->log(__VA_ARGS__)
#    define LOGWEB(...) generalLog::wjlog->log(__VA_ARGS__)
#endif
// 时间日志
#ifdef LOG_TIMECOST
#    define LOGT(...) generalLog::tclog->log(__VA_ARGS__)
#else
#    define LOGT(...)
#endif
