/*
 * @Description:
 * @Version: 1.0
 * @Autor: zushuang
 * @Date: 2021-07-22 13:32:47
 * @LastEditors: zushuang
 * @LastEditTime: 2021-10-25 13:42:38
 */
#ifndef _AuthAlgo_H_
#define _AuthAlgo_H_
#include <cstring>
#include <dirent.h>
#include <exception>
#include <string>
#include <vector>
#define BLOCK_SIZE 16
using namespace std;

class CAuthAlgo {
  public:
    enum { ECB = 0, CBC = 1, CFB = 2 };

  private:
    enum { DEFAULT_BLOCK_SIZE = 16 };
    enum { MAX_BLOCK_SIZE = 32, MAX_ROUNDS = 14, MAX_KC = 8, MAX_BC = 8 };

  public:
    CAuthAlgo();
    virtual ~CAuthAlgo();

  private:
    // Key Initialization Flag
    bool m_bKeyInit;
    // Encryption (m_Ke) round key
    int m_Ke[MAX_ROUNDS + 1][MAX_BC];
    // Decryption (m_Kd) round key
    int m_Kd[MAX_ROUNDS + 1][MAX_BC];
    // Key Length
    int m_keylength;
    // Block Size
    int m_blockSize;
    // Number of Rounds
    int m_iROUNDS;
    // Chain Block
    char m_chain0[MAX_BLOCK_SIZE];
    char m_chain[MAX_BLOCK_SIZE];
    // Auxiliary private use buffers
    int tk[MAX_KC];
    int a[MAX_BC];
    int t[MAX_BC];
    char m_cMac[256];

  private:
    void Xor(char* buff, char const* chain);
    void DefEncryptBlock(char const* in, char* result);
    void DefDecryptBlock(char const* in, char* result);
    void EncryptBlock(char const* in, char* result);
    void DecryptBlock(char const* in, char* result);

  private:
    void MakeKey(char const* key,
                 char const* chain,
                 int keylength = DEFAULT_BLOCK_SIZE,
                 int blockSize = DEFAULT_BLOCK_SIZE);
    void Encrypt(char const* in, char* result, size_t n, int iMode = ECB);
    void Decrypt(char const* in, char* result, size_t n, int iMode = ECB);
    string base64_encode(unsigned char const*, unsigned int len);
    string base64_decode(std::string const& s);

    string EncryptionAES(const string& strSrc);  // AES����
    string DecryptionAES(const string& strSrc);  // AES����

    char* GetMACAddress();
    long String2InterSerial(const string& strSrc);
    int getSubnetMask(std::vector<string>& macName);
    void GetMACAddresses(std::vector<string>& macAddrs);
    bool isVirtualNet(std::string& ifname);
    bool isWirelessNet(std::string& ifname);
    std::vector<string> netNameVector_;
    std::vector<string> macNameVector_;

  public:
    string MakeSerialNo();
    string MakeSerialCd(char* pAuthSN, int iAuthId);
    bool CheckAuthorityNo(char* pAuthorityNo);
    string CheckAuthorityNo1(char* pAuthorityNo);
    void WriteLog(string txt);
};
#endif
