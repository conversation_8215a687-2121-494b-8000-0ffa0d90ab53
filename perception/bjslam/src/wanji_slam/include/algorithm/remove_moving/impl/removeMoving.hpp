/**
 * @file removeMoving.hpp
 * <AUTHOR> (<EMAIL>)
 * @brief 动态过滤实现
 * @version 1.0
 * @date 2022-08-21
 *
 * @copyright Copyright (c) 2022 <PERSON><PERSON>
 */
#pragma once

#include "../removeMoving.h"

template <typename P> RemoveMoving<P>::RemoveMoving(wj_slam::s_RemovingConfig* p_conf)
{
    c_bDebug_ = false;
    allocateMemery_();
    // setParameterDefault_();
    if (nullptr != p_conf)
    {
        setFilterRawPointCloud(p_conf->m_bReMovingRawCloud, p_conf->m_iReMovingRawGrow);
        setLocalMapSize(p_conf->m_iReMovingWindowSize);
        // setHeightSpace(-p_conf->m_fGroundHigh, p_conf->m_fRoofHigh);
        setLeafSize(p_conf->m_fReMovingGrid);
        setKoeff(p_conf->m_iReMovingKoeff);
        setOverlapRayLevel(p_conf->m_iReMovingOverlapLevel);
    }
}

template <typename P> RemoveMoving<P>::RemoveMoving(bool p_bDebug) : c_bDebug_(p_bDebug)
{
    allocateMemery_();
    // setParameterDefault_();
}

template <typename P> void RemoveMoving<P>::setLeafSize(float p_fGrid)
{
    setLeafSize(p_fGrid, p_fGrid, p_fGrid);
}

template <typename P>
void RemoveMoving<P>::setLeafSize(float p_fXGrid, float p_fYGrid, float p_fZGrid)
{
    c_stCfg_.m_fLeafSize[0] = p_fXGrid;
    c_stCfg_.m_fLeafSize[1] = p_fYGrid;
    c_stCfg_.m_fLeafSize[2] = p_fZGrid;

    c_stCfg_.m_fInvLeafSize[0] = 1.0f / c_stCfg_.m_fLeafSize[0];
    c_stCfg_.m_fInvLeafSize[1] = 1.0f / c_stCfg_.m_fLeafSize[1];
    c_stCfg_.m_fInvLeafSize[2] = 1.0f / c_stCfg_.m_fLeafSize[2];

    if (c_bDebug_)
        fprintf(stderr,
                "setLeafSize[%f,%f,%f]\n",
                c_stCfg_.m_fLeafSize[0],
                c_stCfg_.m_fLeafSize[1],
                c_stCfg_.m_fLeafSize[2]);
}

template <typename P> void RemoveMoving<P>::setHeightSpace(float p_fZmin, float p_fZmax)
{
    c_stCfg_.m_fMoveZMax = p_fZmax;
    c_stCfg_.m_fMoveZMin = p_fZmin;
    if (c_bDebug_)
        fprintf(stderr, "setHeightSpace[%f,%f]\n", c_stCfg_.m_fMoveZMin, c_stCfg_.m_fMoveZMax);
}

template <typename P> void RemoveMoving<P>::setLocalMapSize(u_int p_uiLocalMapFrameNumber)
{
    c_stCfg_.m_ulWindowSize = p_uiLocalMapFrameNumber;
}

template <typename P> void RemoveMoving<P>::setKoeff(u_int (&p_koeff)[4])
{
    c_stCfg_.m_uiMinNeighb = p_koeff[0];
    c_stCfg_.m_uiMinOcc = p_koeff[1];
    c_stCfg_.m_uiMinRay = p_koeff[2];
    c_stCfg_.m_uiMaxOcc = p_koeff[3];
}

template <typename P> void RemoveMoving<P>::setFilterRawPointCloud(bool p_bDoFilter, int p_iGrow)
{
    c_stCfg_.m_bRawFlt = p_bDoFilter;
    c_stCfg_.c_iGrowCount_[0] = p_iGrow;
    c_stCfg_.c_iGrowCount_[1] = c_stCfg_.c_iGrowCount_[0] + 1;
}
template <typename P> bool RemoveMoving<P>::getFilterRawPointCloud()
{
    return c_stCfg_.m_bRawFlt;
}

template <typename P> void RemoveMoving<P>::setOverlapRayLevel(int p_iLevel)
{
    c_stCfg_.m_iOverlapRayLevel = p_iLevel;
}
template <typename P> int RemoveMoving<P>::getOverlapRayLevel()
{
    return c_stCfg_.m_iOverlapRayLevel;
}

template <typename P>
void RemoveMoving<P>::setInputCloud(PCFeaturePtr p_pcFeature1,
                                    PCFeaturePtr p_pcFeature2,
                                    Eigen::Vector3d p_pose3D,
                                    size_t p_ulFrameIndex)
{
    if (!p_pcFeature1 || !p_pcFeature2)
    {
        fprintf(stderr, "[RM][setInputCloud]:p_pcFeature1 is null.\n");
        return;
    }

    // 内建计数器同步
    if (p_ulFrameIndex != SIZE_T_MAX)
        c_ulNewFrame_ = p_ulFrameIndex;
    else
        c_ulNewFrame_++;

    c_localFrames_[c_ulNewFrame_].reset(new st_keyFrame());
    c_localFrames_[c_ulNewFrame_]->m_pose = p_pose3D;

    *c_localFrames_[c_ulNewFrame_]->m_pcf = *p_pcFeature1;
    *c_localFrames_[c_ulNewFrame_]->m_pcs = *p_pcFeature2;
    printf("输入深拷贝:%ld  %ld  %ld\n",
           c_ulNewFrame_,
           p_pcFeature2->points.size(),
           c_localFrames_[c_ulNewFrame_]->m_pcs->points.size());
    if (c_bDebug_)
        fprintf(stderr, "setInputCloud frameIndex-%ld \n", p_ulFrameIndex);
}

template <typename P>
void RemoveMoving<P>::setInputCloud(PCFeaturePtr p_pcFeature1,
                                    PCFeaturePtr p_pcFeature2,
                                    PCRawPtr p_pcRaw,
                                    Eigen::Vector3d p_pose3D,
                                    size_t p_ulFrameIndex)
{
    setInputCloud(p_pcFeature1, p_pcFeature2, p_pose3D, p_ulFrameIndex);
    if (c_stCfg_.m_bRawFlt)
    {
        if (!p_pcRaw)
        {
            fprintf(stderr, "[RM][setInputCloud]:p_pcRaw is null.\n");
            return;
        }
        *c_localFrames_[c_ulNewFrame_]->m_pct = *p_pcRaw;
    }
}

template <typename P> void RemoveMoving<P>::renewLocalMap()
{
    // 使用新帧更新栅格地图
    insertNewFrame_(c_localFrames_[c_ulNewFrame_]);
    // 使用旧帧更新穿透
    if (c_stCfg_.m_iOverlapRayLevel > 0 && c_localFrames_.size() >= 3)
    {
        keyFrame_Ptr l_oldFrame = c_localFrames_.begin()->second;
        insertRay_(l_oldFrame->m_pose, *(l_oldFrame->m_idxmap));
        insertRay_(l_oldFrame->m_pose, *(l_oldFrame->m_idxNomap));
    }
    if (c_stCfg_.m_iOverlapRayLevel > 1 && c_localFrames_.size() >= 3)
    {
        keyFrame_Ptr l_oldFrame = std::prev(c_localFrames_.end(), 3)->second;
        insertRay_(l_oldFrame->m_pose, *(l_oldFrame->m_idxmap));
        insertRay_(l_oldFrame->m_pose, *(l_oldFrame->m_idxNomap));
    }
}

template <typename P>
size_t
RemoveMoving<P>::filter(PCFeaturePtr& p_pcFeature1, PCFeaturePtr& p_pcFeature2, PCRawPtr& p_pcRaw)
{
    // 当前要处理的帧序号
    size_t l_ulFrameIndex = getFilterIndex();

    // 如果此时未填满localmap则退出
    if (!c_bIsFilteringNewst_ && c_localFrames_.size() < c_stCfg_.m_ulWindowSize)
        return SIZE_T_MAX;
    if (c_bDebug_)
        fprintf(stderr, "removing movingObject in frameIndex-%ld\n", l_ulFrameIndex);
    filterOutliersOneFrame_(c_localFrames_[l_ulFrameIndex]);
    p_pcFeature1 = c_localFrames_[l_ulFrameIndex]->m_pcf;
    p_pcFeature2 = c_localFrames_[l_ulFrameIndex]->m_pcs;
    if (c_stCfg_.m_bRawFlt)
        p_pcRaw = c_localFrames_[l_ulFrameIndex]->m_pct;

    if (c_localFrames_.size() >= c_stCfg_.m_ulWindowSize)
    {
        size_t l_ulEraseFrameIndex = c_localFrames_.begin()->first;
        removeOldFrame_(l_ulEraseFrameIndex);
        if (c_bDebug_)
            fprintf(stderr, "remove frameIndex-%ld from localMap\n", l_ulEraseFrameIndex);
    }
    return l_ulFrameIndex;
}

template <typename P>
size_t RemoveMoving<P>::filter(PCFeaturePtr& p_pcFeature1, PCFeaturePtr& p_pcFeature2)
{
    // 当前要处理的帧序号
    size_t l_ulFrameIndex = getFilterIndex();

    // 如果此时未填满localmap则退出
    if (!c_bIsFilteringNewst_ && c_localFrames_.size() < c_stCfg_.m_ulWindowSize)
        return SIZE_T_MAX;

    if (c_bDebug_)
        fprintf(stderr, "removing movingObject in frameIndex-%ld\n", l_ulFrameIndex);
    filterOutliersOneFrame_(c_localFrames_[l_ulFrameIndex]);

    p_pcFeature1 = c_localFrames_[l_ulFrameIndex]->m_pcf;
    p_pcFeature2 = c_localFrames_[l_ulFrameIndex]->m_pcs;

    if (c_localFrames_.size() >= c_stCfg_.m_ulWindowSize)
    {
        size_t l_ulEraseFrameIndex = c_localFrames_.begin()->first;
        removeOldFrame_(l_ulEraseFrameIndex);
        if (c_bDebug_)
            fprintf(stderr, "remove frameIndex-%ld from localMap\n", l_ulEraseFrameIndex);
    }
    return l_ulFrameIndex;
}

template <typename P> pcl::PointCloud<pcl::PointXYZHSV>::Ptr RemoveMoving<P>::getFilteredPoints()
{
    return c_movingPoints_;
}
template <typename P> pcl::PointCloud<pcl::PointXYZHSV>::Ptr RemoveMoving<P>::getFilterCloud()
{
    return c_lastCloud_;
}

template <typename P> void RemoveMoving<P>::allocateMemery_()
{
    c_lastCloud_.reset(new pcl::PointCloud<pcl::PointXYZHSV>());
    c_movingPoints_.reset(new pcl::PointCloud<pcl::PointXYZHSV>());
    c_gridMap_.reset(new gridMap());
    c_gridMap_->reserve(10009ul);
}

template <typename P> void RemoveMoving<P>::setParameterDefault_()
{
    setLeafSize();
    setLocalMapSize();
    setHeightSpace();
    setFilterRawPointCloud();
    setOverlapRayLevel();
}

template <typename P>
inline Eigen::Vector3i RemoveMoving<P>::getGridCoordinates_(float x, float y, float z)
{
    return (Eigen::Vector3i(static_cast<int>(floor(x * c_stCfg_.m_fInvLeafSize[0])),
                            static_cast<int>(floor(y * c_stCfg_.m_fInvLeafSize[1])),
                            static_cast<int>(floor(z * c_stCfg_.m_fInvLeafSize[2]))));
}

template <typename P>
template <typename PT>
inline typename RemoveMoving<P>::st_gridId RemoveMoving<P>::getGridCoordinates_(PT& p)
{
    return (st_gridId(static_cast<int>(floor(p.x * c_stCfg_.m_fInvLeafSize[0])),
                      static_cast<int>(floor(p.y * c_stCfg_.m_fInvLeafSize[1])),
                      static_cast<int>(floor(p.z * c_stCfg_.m_fInvLeafSize[2]))));
}

template <typename P> void RemoveMoving<P>::insertNewFrame_(keyFrame_Ptr p_nF)
{
    // 计算当前帧的占据栅格
    getIdxmap_(
        *(p_nF->m_pcf), 0ul, *(p_nF->m_idxmap), *(p_nF->m_idxNomap), (float)p_nF->m_pose.z());
    getIdxmap_(*(p_nF->m_pcs),
               p_nF->m_pcf->size(),
               *(p_nF->m_idxmap),
               *(p_nF->m_idxNomap),
               (float)p_nF->m_pose.z());
    // 更新占据栅格次数到栅格地图
    insertCloud_(*(p_nF->m_idxmap));
    // 当前帧的穿透更新
    insertRay_(p_nF->m_pose, *(p_nF->m_idxmap));
    if (c_stCfg_.m_iOverlapRayLevel > -1)
        insertRay_(p_nF->m_pose, *(p_nF->m_idxNomap));
}

template <typename P>
template <typename PC>
void RemoveMoving<P>::getIdxmap_(PC& p_pc,
                                 size_t p_ulOffset,
                                 indexmmap& p_idxmap,
                                 indexmmap& p_idxNomap,
                                 float p_fz)
{
    float l_fMinZ = p_fz + c_stCfg_.m_fMoveZMin;
    float l_fMaxZ = p_fz + c_stCfg_.m_fMoveZMax;
    st_gridId l_stTempId = st_gridId();
    size_t index = 0;
    for (size_t i = 0; i < p_pc.size(); i++)
    {
        l_stTempId = st_gridId(getGridCoordinates_(p_pc.points[i]));
        // mmap中增加id-index对
        index = i + p_ulOffset;
        // 只处理特定区域(雷达上下范围)
        if (p_pc.points[i].z > l_fMinZ && p_pc.points[i].z < l_fMaxZ)
        {
            p_idxmap.insert(typename indexmmap::value_type(l_stTempId, index));
        }
        else
        {
            p_idxNomap.insert(typename indexmmap::value_type(l_stTempId, index));
        }
    }
}

template <typename P> void RemoveMoving<P>::insertCloud_(indexmmap& p_idxmap)
{
    st_gridInfo l_stTempInfo = st_gridInfo();
    mapGridIter gridInfoPair;
    for (auto gridIdxPair = p_idxmap.begin(); gridIdxPair != p_idxmap.end();)
    {
        gridInfoPair = c_gridMap_->find(gridIdxPair->first);
        // 查找栅格地图中是否存在栅格点，存在则增加占用，若不存在则添加
        if (gridInfoPair == c_gridMap_->end())
        {
            l_stTempInfo.m_iOcc = p_idxmap.count(gridIdxPair->first);
            c_gridMap_->insert(typename gridMap::value_type(gridIdxPair->first, l_stTempInfo));
        }
        else
        {
            gridInfoPair->second.m_iOcc += p_idxmap.count(gridIdxPair->first);
        }
        // 获取下一组第一个键值对的iter
        gridIdxPair = p_idxmap.upper_bound(gridIdxPair->first);
    }
}

template <typename P> void RemoveMoving<P>::insertRay_(Eigen::Vector3d& p_pose, indexmmap& p_idxmap)
{
    Eigen::Vector3i l_pose = getGridCoordinates_(p_pose.x(), p_pose.y(), p_pose.z());
    Eigen::Vector3i l_p;
    for (auto gridIdxPair = p_idxmap.begin(); gridIdxPair != p_idxmap.end();)
    {
        l_p = gridIdxPair->first.vector();
        trace3DLine_(l_pose.x(),
                     l_pose.y(),
                     l_pose.z(),
                     l_p.x(),
                     l_p.y(),
                     l_p.z(),
                     p_idxmap.count(gridIdxPair->first));  //
        // 获取下一组第一个键值对的iter
        gridIdxPair = p_idxmap.upper_bound(gridIdxPair->first);
    }
}

template <typename P>
void RemoveMoving<P>::trace3DLine_(int x1, int y1, int z1, int x2, int y2, int z2, size_t rayNum)
{
    int xd, yd, zd;
    int x, y, z;
    int ax, ay, az;
    int sx, sy, sz;
    int dx, dy, dz;

    dx = x2 - x1;
    dy = y2 - y1;
    dz = z2 - z1;

    ax = std::abs(dx) << 1;
    ay = std::abs(dy) << 1;
    az = std::abs(dz) << 1;

    sx = ((dx) < 0) ? -1 : (dx) > 0 ? 1 : 0;
    sy = ((dy) < 0) ? -1 : (dy) > 0 ? 1 : 0;
    sz = ((dz) < 0) ? -1 : (dz) > 0 ? 1 : 0;

    x = x1;
    y = y1;
    z = z1;

    if (ax >= std::max(ay, az)) /* x dominant */
    {
        yd = ay - (ax >> 1);
        zd = az - (ax >> 1);
        for (;;)
        {
            // ijk已获取
            addRayThisGrid_(x, y, z, rayNum);
            // 如果直线段已到达当前帧旁边，则退出,避免hit临近方块
            // if (x == x2)
            if (std::abs(x - x2) < 2 && std::abs(y - y2) < 2 && std::abs(z - z2) < 2)
            {
                return;
            }

            if (yd >= 0)
            {
                y += sy;
                yd -= ax;
            }

            if (zd >= 0)
            {
                z += sz;
                zd -= ax;
            }

            x += sx;
            yd += ay;
            zd += az;
        }
    }
    else if (ay >= std::max(ax, az)) /* y dominant */
    {
        xd = ax - (ay >> 1);
        zd = az - (ay >> 1);
        for (;;)
        {
            // ijk获取
            addRayThisGrid_(x, y, z, rayNum);
            // if (y == y2)
            if (std::abs(x - x2) < 2 && std::abs(y - y2) < 2 && std::abs(z - z2) < 2)
            {
                return;
            }

            if (xd >= 0)
            {
                x += sx;
                xd -= ay;
            }

            if (zd >= 0)
            {
                z += sz;
                zd -= ay;
            }

            y += sy;
            xd += ax;
            zd += az;
        }
    }
    else if (az >= std::max(ax, ay)) /* z dominant */
    {
        xd = ax - (az >> 1);
        yd = ay - (az >> 1);
        for (;;)
        {
            // ijk获取
            addRayThisGrid_(x, y, z, rayNum);
            // if (z == z2)
            if (std::abs(x - x2) < 2 && std::abs(y - y2) < 2 && std::abs(z - z2) < 2)
            {
                return;
            }

            if (xd >= 0)
            {
                x += sx;
                xd -= az;
            }

            if (yd >= 0)
            {
                y += sy;
                yd -= az;
            }

            z += sz;
            xd += ax;
            yd += ay;
        }
    }
}

template <typename P> void RemoveMoving<P>::addRayThisGrid_(int i, int j, int k, size_t rayNum)
{
    st_gridId l_tempGrid(i, j, k);
    mapGridIter gridInfoPair = c_gridMap_->find(l_tempGrid);
    if (gridInfoPair != c_gridMap_->end())
    {
        gridInfoPair->second.m_iRay += rayNum;
    }
}

template <typename P>
bool RemoveMoving<P>::removeGridStrategySet_(const st_gridId& p_id, const st_gridInfo& p_info)
{
    if (tooMuchRayHit_(p_id, p_info))
    {
        return true;
    }
    else if (lessOccAndLessNeighber_(p_id, p_info))
    {
        return true;
    }
    return false;
}

template <typename P>
bool RemoveMoving<P>::removeGridStrategySetNewF_(const st_gridId& p_id, const st_gridInfo& p_info)
{
    if (tooMuchRayHit_(p_id, p_info))
    {
        return true;
    }
    return false;
}

template <typename P>
bool RemoveMoving<P>::lessOccAndLessNeighber_(const st_gridId& p_id, const st_gridInfo& p_info)
{
    if (p_info.m_iOcc < (int)c_stCfg_.m_uiMinOcc)  //小于最小占用次数阈值
    {
        uint l_uiNeighber = 0;
        int l_iMaxOcc = p_info.m_iOcc;
        st_gridId l_tempGrid;
        mapGridIter gridInfoPair;
        // 搜索3*3立方体中所有可能ijk
        for (int i = p_id.i - 1; i < p_id.i + 2; i++)
            for (int j = p_id.j - 1; j < p_id.j + 2; j++)
                for (int k = p_id.k - 1; k < p_id.k + 2; k++)
                {
                    l_tempGrid.set(i, j, k);
                    // if (l_tempGrid == p_id) continue;

                    gridInfoPair = c_gridMap_->find(l_tempGrid);
                    if (gridInfoPair != c_gridMap_->end())
                    {
                        l_uiNeighber++;
                        if (gridInfoPair->second.m_iOcc > l_iMaxOcc)
                            l_iMaxOcc = gridInfoPair->second.m_iOcc;
                        // 如果邻居数足够多，或相邻栅格占用大
                        if (l_uiNeighber - 1 >= c_stCfg_.m_uiMinNeighb
                            || l_iMaxOcc >= (int)c_stCfg_.m_uiMinOcc)
                        {
                            return false;
                        }
                    }
                }
        // 邻居数量少且邻居占用不足
        return true;
    }
    return false;
}

template <typename P>
bool RemoveMoving<P>::tooMuchRayHit_(const st_gridId& p_id, const st_gridInfo& p_info)
{
    if (p_info.m_iRay > (int)c_stCfg_.m_uiMinRay
        && p_info.m_iOcc < (int)c_stCfg_.m_uiMaxOcc)  //为什么占用要小于阈值
    {
        return true;
    }
    return false;
}

template <typename P> void RemoveMoving<P>::getDebugCloud_(keyFrame_Ptr p_oF)
{
    indexmmap& p_idxmap = *(p_oF->m_idxmap);
    mapGridIter gridInfoPair;

    c_lastCloud_->clear();
    // debug 加入debug点云
    for (auto gridIdxPair = p_idxmap.begin(); gridIdxPair != p_idxmap.end();)
    {
        gridInfoPair = c_gridMap_->find(gridIdxPair->first);
        debugCloudPushGrid_(gridIdxPair->first,
                            (float)gridInfoPair->second.m_iOcc,
                            (float)gridInfoPair->second.m_iRay,
                            0);
        // 获取下一组第一个键值对的iter
        gridIdxPair = p_idxmap.upper_bound(gridIdxPair->first);
    }
}

template <typename P> void RemoveMoving<P>::getOutlieGrids_(indexmmap& p_idxmap, gridSet& p_set)
{
    mapGridIter gridInfoPair;
    // 中间帧过滤规则
    auto p_StrategyFun = &RemoveMoving::removeGridStrategySet_;
    // 最新帧过滤规则
    if (c_bIsFilteringNewst_)
        p_StrategyFun = &RemoveMoving::removeGridStrategySetNewF_;
    for (auto gridIdxPair = p_idxmap.begin(); gridIdxPair != p_idxmap.end();)
    {
        gridInfoPair = c_gridMap_->find(gridIdxPair->first);
        // 根据条件添加删除表
        if ((this->*p_StrategyFun)(gridInfoPair->first, gridInfoPair->second))
        {
            p_set.insert(gridInfoPair->first);
        }
        // 获取下一组第一个键值对的iter
        gridIdxPair = p_idxmap.upper_bound(gridIdxPair->first);
    }
}

template <typename P>
void RemoveMoving<P>::filterOutliersPointsInGrids_(keyFrame_Ptr p_oF, gridSet& p_set)
{
    indexmmap& p_idxmap = *(p_oF->m_idxmap);
    size_t p_ulSecondOffset = p_oF->m_pcf->size();
    size_t p_ulAllOffset = p_oF->m_pcf->size() + p_oF->m_pcs->size();

    // 要移除的原始点的序号
    indexSet* l_ulMoveIndexFirst = new indexSet();
    indexSet* l_ulMoveIndexSecond = new indexSet();

    // debug加载点到debug点云
    if (c_bDebug_)
        c_movingPoints_->clear();

    for (auto& grid : p_set)
    {
        if (c_bDebug_)
            eraseCloudPushGrid_(grid);
        // 添加删除表
        for (auto thisGridIdxPair = p_idxmap.lower_bound(grid);
             thisGridIdxPair != p_idxmap.upper_bound(grid);
             thisGridIdxPair++)
        {
            // first中的删除点，逆序
            if (thisGridIdxPair->second < p_ulSecondOffset)
                l_ulMoveIndexFirst->insert(thisGridIdxPair->second);
            // second中的删除点，逆序
            else if (thisGridIdxPair->second < p_ulAllOffset)
                l_ulMoveIndexSecond->insert(thisGridIdxPair->second - p_ulSecondOffset);
        }
    }

    eraseIndeiesInCloud_(*l_ulMoveIndexFirst, *(p_oF->m_pcf));
    delete l_ulMoveIndexFirst;
    eraseIndeiesInCloud_(*l_ulMoveIndexSecond, *(p_oF->m_pcs));
    delete l_ulMoveIndexSecond;
}

template <typename P>
template <typename PC>
void RemoveMoving<P>::eraseIndeiesInCloud_(indexSet& p_ulMoveIndex, PC& p_pc)
{
    // 逆序erase，不影响前面序号
    for (auto l_ulInd = p_ulMoveIndex.rbegin(); l_ulInd != p_ulMoveIndex.rend(); l_ulInd++)
    {
        p_pc.erase(p_pc.begin() + *l_ulInd);
    }
}

template <typename P>
void RemoveMoving<P>::filterOutliersRawPointsNearGrids_(keyFrame_Ptr p_oF, gridSet& p_set)
{
    // 临时清单
    indexmmap_Ptr l_idxRawMap(new indexmmap());
    indexmmap_Ptr l_idxRawNoMap(new indexmmap());
    // 栅格扩张，将特征栅格扩散到3*3立方体(至少90cm)
    st_gridId l_tempGrid;
    gridSet* l_growSet = new gridSet(p_set);
    // 要移除的原始点的序号
    indexSet* l_ulMoveIndex = new indexSet();
    getIdxmap_(*(p_oF->m_pct), 0ul, *l_idxRawMap, *l_idxRawNoMap, p_oF->m_pose.z());
    indexmmap().swap(*(l_idxRawNoMap));
    for (auto& grid : p_set)
    {
        // 搜索3*3立方体中所有可能ijk
        for (int i = grid.i - c_stCfg_.c_iGrowCount_[0]; i < grid.i + c_stCfg_.c_iGrowCount_[1];
             i++)
            for (int j = grid.j - c_stCfg_.c_iGrowCount_[0]; j < grid.j + c_stCfg_.c_iGrowCount_[1];
                 j++)
                for (int k = grid.k - c_stCfg_.c_iGrowCount_[0];
                     k < grid.k + c_stCfg_.c_iGrowCount_[1];
                     k++)
                {
                    l_tempGrid.set(i, j, k);
                    // 如果原始点云包含这个ijk，则加入删除列表
                    if (l_idxRawMap->find(l_tempGrid) != l_idxRawMap->end())
                    {
                        l_growSet->insert(l_tempGrid);
                    }
                }
    }

    // 添加删除表
    for (auto& grid : *l_growSet)
    {
        for (auto thisGridIdxPair = l_idxRawMap->lower_bound(grid);
             thisGridIdxPair != l_idxRawMap->upper_bound(grid);
             thisGridIdxPair++)
        {
            l_ulMoveIndex->insert(thisGridIdxPair->second);
        }
    }

    // if (c_bDebug_) fprintf(stderr, "rawSize = %d, rmSize = %d\n", p_oF->m_pct->size(),
    // l_ulMoveIndex->size());
    eraseIndeiesInCloud_(*l_ulMoveIndex, *(p_oF->m_pct));
    // if (c_bDebug_) fprintf(stderr, "newSize = %d\n", p_oF->m_pct->size());

    delete l_ulMoveIndex;
    delete l_growSet;
    indexmmap().swap(*(l_idxRawMap));
}

template <typename P> size_t RemoveMoving<P>::getFilterIndex()
{
    if (c_localFrames_.empty())
        return SIZE_T_MAX;
    // 当前要处理的帧序号
    // size_tl_ulFrameIndex = c_localFrames_.begin()->first;
    //处理末尾帧
    size_t l_ulFrameIndex = c_localFrames_.rbegin()->first;  // 处理最新帧
    c_bIsFilteringNewst_ = true;

    // size_t l_ulFrameIndex = std::prev(c_localFrames_.end(), c_stCfg_.m_ulWindowSize / 2)->first;  //处理旧帧
    // c_bIsFilteringNewst_ = false;

    return l_ulFrameIndex;
}

template <typename P> void RemoveMoving<P>::filterOutliersOneFrame_(keyFrame_Ptr p_oF)
{
    indexmmap& p_idxmap = *(p_oF->m_idxmap);
    // 异常点所在的栅格集合
    gridSet* p_set = new gridSet();

    if (c_bDebug_)
        getDebugCloud_(p_oF);

    getOutlieGrids_(p_idxmap, *p_set);
    filterOutliersPointsInGrids_(p_oF, *p_set);

    if (c_stCfg_.m_bRawFlt)
        filterOutliersRawPointsNearGrids_(p_oF, *p_set);

    delete p_set;
}

template <typename P> void RemoveMoving<P>::removeOldFrame_(size_t p_ulFrameIndex)
{
    indexmmap& p_idxmap = *(c_localFrames_[p_ulFrameIndex]->m_idxmap);
    mapGridIter gridInfoPair;
    int l_gridCnt;

    for (auto gridIdxPair = p_idxmap.begin(); gridIdxPair != p_idxmap.end();)
    {
        gridInfoPair = c_gridMap_->find(gridIdxPair->first);

        // 如果这一栅格的占用仅来源于当前退出帧，则删除栅格
        l_gridCnt = p_idxmap.count(gridIdxPair->first);
        if (gridInfoPair->second.m_iOcc <= l_gridCnt)
        {
            c_gridMap_->erase(gridInfoPair);
        }
        // 移除当前退出帧造成的占用
        else
        {
            gridInfoPair->second.m_iOcc -= l_gridCnt;
        }
        // 获取下一组第一个键值对的iter
        gridIdxPair = p_idxmap.upper_bound(gridIdxPair->first);
    }
    c_localFrames_.erase(p_ulFrameIndex);
}

template <typename P>
template <typename PointT>
inline void RemoveMoving<P>::debugCloudPushPoint_(PointT& p_pds, float h, float s, float v)
{
    pcl::PointXYZHSV l_pdebug;
    l_pdebug.x = p_pds.x;
    l_pdebug.y = p_pds.y;
    l_pdebug.z = p_pds.z;
    l_pdebug.h = h;
    l_pdebug.s = s;
    l_pdebug.v = v;
    c_lastCloud_->push_back(l_pdebug);
}
template <typename P>
inline void RemoveMoving<P>::debugCloudPushGrid_(const st_gridId& p_id, float h, float s, float v)
{
    pcl::PointXYZHSV l_pdebug;
    l_pdebug.x = float(p_id.i / c_stCfg_.m_fInvLeafSize[0]) + 0.5 * c_stCfg_.m_fLeafSize[0];
    l_pdebug.y = float(p_id.j / c_stCfg_.m_fInvLeafSize[1]) + 0.5 * c_stCfg_.m_fLeafSize[1];
    l_pdebug.z = float(p_id.k / c_stCfg_.m_fInvLeafSize[2]) + 0.5 * c_stCfg_.m_fLeafSize[2];
    l_pdebug.h = h;
    l_pdebug.s = s;
    l_pdebug.v = v;
    c_lastCloud_->push_back(l_pdebug);
}
template <typename P> inline void RemoveMoving<P>::eraseCloudPushGrid_(const st_gridId& p_id)
{
    auto info = c_gridMap_->find(p_id);
    pcl::PointXYZHSV l_pdebug;
    l_pdebug.x = float(p_id.i / c_stCfg_.m_fInvLeafSize[0]) + 0.5 * c_stCfg_.m_fLeafSize[0];
    l_pdebug.y = float(p_id.j / c_stCfg_.m_fInvLeafSize[1]) + 0.5 * c_stCfg_.m_fLeafSize[1];
    l_pdebug.z = float(p_id.k / c_stCfg_.m_fInvLeafSize[2]) + 0.5 * c_stCfg_.m_fLeafSize[2];
    l_pdebug.h = info->second.m_iOcc;
    l_pdebug.s = info->second.m_iRay;
    l_pdebug.v = 0;
    c_movingPoints_->push_back(l_pdebug);
}
#define WJSLAM_RemoveMoving(P) template class RemoveMoving<P>;