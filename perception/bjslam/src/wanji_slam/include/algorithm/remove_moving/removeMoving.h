/**
 * @file removeMoving.h
 * <AUTHOR> (<EMAIL>)
 * @brief 动态过滤类声明
 * @version 1.0
 * @date 2022-08-21
 * @code
// 1.创建对象：
    RemoveMoving<P,P>*    rM;
    // 默认构造
    rM = new RemoveMoving<P,P>();
    // debug模式
    //rM = new RemoveMoving<P,P>(true);
// 2.设置参数：
    //必须设置且禁止运行中修改
    rM->setLeafSize(X栅格，Y栅格，Z栅格)     //默认 0.3,0.3,0.3
    rM->setFilterRawPointCloud(false);//设置是否处理原始点云
    //可以设置且允许运行中修改
    rM->setHeightSpace(最小z，最大z)        //默认 -FLT_MAX , FLT_MAX
    rM->setLocalMapSize(局部地图最小帧数)    //默认10
    // 占用数小于MinOccNum且邻居数小于MinNeighberNum将删除
    // 占用数小于MaxOccNum且穿透数大于半窗大小且占用小于穿透将删除
    rM->setKoeff(允许最小占用次数，允许最大占用次数)    //默认 [3,5]
    rM->setMinNeighberNum(邻居个数);              //默认3
    // 穿透级别：
    // -1级 只使用栅格地图范围内的点计算穿透
    // 0级(默认) 增加使用栅格地图外的点计算穿透（例如穿透人射向地面点,地面不在栅格地图范围内）
    // 1级 在更新地图后增加使用1次旧帧计算穿透（可删除速度相近的动态物体）
    // 2级 在更新地图后增加使用2次不同旧帧计算穿透（增加耗时）
    rM->setOverlapRayLevel(穿透级别)；          //  默认0

// 3.线程内循环：
    // 输入新关键帧
    rM->setInputCloud(cornerCloudKeyFrames[l_thisFrameNo],surfCloudKeyFrames[l_thisFrameNo],fullResCloudKeyFrames[l_thisFrameNo],l_3dPose,l_thisFrameNo);
    // 向localMap添加新的关键帧，不读取外部数据
    rM->renewLocalMap();
    // 创建一些新的空点云指针
    pcl::PointCloud<P>::Ptr thisCornerFrame;
    pcl::PointCloud<P>::Ptr thisSurfFrame;
    pcl::PointCloud<P>::Ptr thisFullFrame;
    // 对localMap中间帧进行过滤，并从localMap中移除最旧帧：
    //
--中间帧为序号顺序排列的中位数，例如localmap持有[4，5，6，7，8]帧，filter会过滤帧号6，并返回帧号6
    // --最旧帧为序号最小帧，例如localmap持有[4，5，6，7，8]帧，filter完成后从localMap中删除帧号4
    //
**支持插入不连续的帧号，例如localmap持有[4，5，7，110，114，119，120]，filter会过滤帧号110，localmap删除帧号4，并返回帧号110
    size_t l_ulRenewFrame = rM->filter(thisCornerFrame, thisSurfFrame, thisFullFrame);//
函数返回被过滤帧号
    // 如果 返回值 == size_t最大值表示未进行更新
    if (l_ulRenewFrame != ULONG_MAX)
    {
        // 刷新持有关键帧序列中的点云指针
        cornerCloudKeyFrames[(int)l_ulRenewFrame] = thisCornerFrame;
        surfCloudKeyFrames[(int)l_ulRenewFrame] = thisSurfFrame;
        if (rM->getFilterRawPointCloud())
            fullResCloudKeyFrames[l_thisFrameNo] = thisFullFrame;
    }
    // debug发布被过滤的点:
    pcl::toROSMsg(*rM->getFilteredPoints(), tempCloudMsg);
1.金融港B5室外环境(开debug输出，主要目标为树叶、行人)：
    参数： localmapSize=10 grid = 0.2m ^3，raylevel-2，过滤原始点云
    时间： 平均13ms，峰值16ms
    效果： 特征点云：稀疏树叶全部过滤，行人膝盖以上全部过滤
    效果： 原始点云：只有部分行人留影被过滤，树叶无效；
2.成都厂区室外环境(关debug输出，主要目标为树叶、行人、卡车)：
    参数： map=10 grid = 0.2m ^3，raylevel-2，过滤原始点云
    时间： 平均13ms，峰值48ms
    效果： 特征点云：稀疏树叶全部过滤，行人全部过滤，卡车全部过滤
    效果： 原始点云：行人留影存在，树叶无效，卡车存在极少留影；
 * @endcode
 *
 * @copyright Copyright (c) 2022 Wuhan Vanjee
 */

#pragma once
#include "common/config/conf_location.h"
#include <Eigen/Dense>
#include <boost/unordered_map.hpp>
#include <boost/unordered_set.hpp>
#include <float.h>
#include <map>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <vector>

/**
 * @class RemoveMoving removeMoving.h "removeMoving.h"
 * @attention 动态过滤器最佳状态是对序列中间帧进行过滤,
 * 但为了满足构架需求,当前设置为对最新帧进行过滤,
 * 此状态下过滤效果一般,但能实时输出最佳局部地图
 * @brief 点云动态过滤
 * \ingroup SubMapManager
 * @tparam P 点云点类型
 */
template <typename P> class RemoveMoving {
  public:
    using Ptr = boost::shared_ptr<RemoveMoving<P>>; /**< 类指针别名 */

  private:
    ////////////////////////////////////////////////////////////////////////////
    using PointCloudRaw = pcl::PointCloud<P>;                  /**< 输入点云1类型 */
    using PCRawPtr = boost::shared_ptr<PointCloudRaw>;         /**< 输入点云1指针类型 */
    using PointCloudFeature = pcl::PointCloud<P>;              /**< 输入点云2类型 */
    using PCFeaturePtr = boost::shared_ptr<PointCloudFeature>; /**< 输入点云2指针类型 */

    /** @brief ijk：key type */
    typedef struct st_gridId
    {
        int i; /**< ix */
        int j; /**< jy */
        int k; /**< kz */

        /**
         * @brief Construct a new st gridId object
         *
         */
        st_gridId() : i(0), j(0), k(0) {}

        /**
         * @brief Construct a new st gridId object
         *
         * @param[in] ix 指定i
         * @param[in] jy 指定j
         * @param[in] kz 指定k
         */
        st_gridId(int ix, int jy, int kz) : i(ix), j(jy), k(kz) {}

        /**
         * @brief Construct a new st gridId object
         *
         * @param[in] ijk 以eigen方法指定ijk
         */
        st_gridId(Eigen::Vector3i ijk) : i(ijk.x()), j(ijk.y()), k(ijk.z()) {}

        /**
         * @brief 等于：ijk分别相等
         *
         * @param p
         * @return [true] [=]
         * @return [false] [！=]
         */
        bool operator==(const st_gridId& p) const
        {
            return i == p.i && j == p.j && k == p.k;
        }

        /**
         * @brief 小于：排序优先级i>j>k
         *
         * @param p
         * @return [true] [<]
         * @return [false] [!<]
         */
        bool operator<(const st_gridId& p) const
        {
            if (i != p.i)
                return i < p.i;
            else if (j != p.j)
                return j < p.j;
            else
                return k < p.k;
        }

        /**
         * @brief hash描述子
         * @see pcl-近似voxel的描述子
         *
         * @return [size_t] [hash描述子]
         */
        size_t hash_key() const
        {
            return static_cast<size_t>(i * 4231 + j * 3079 + k * 5669);
        }

        /**
         * @brief eigen类型输出ijk
         *
         * @return [Eigen::Vector3i] [ijk]
         */
        Eigen::Vector3i vector() const
        {
            return Eigen::Vector3i(i, j, k);
        }

        /**
         * @brief 设置ijk
         *
         * @param[in] ix
         * @param[in] jy
         * @param[in] kz
         */
        void set(int ix, int jy, int kz)
        {
            i = ix;
            j = jy;
            k = kz;
        }
    } st_gridId;

    /** @brief 生成散列值规则 */
    typedef struct hash_func
    {
        size_t operator()(const st_gridId& p) const
        {
            // size_t seed = 0;
            // boost::hash_combine(seed, boost::hash_value(p.i));
            // boost::hash_combine(seed, boost::hash_value(p.j));
            // boost::hash_combine(seed, boost::hash_value(p.k));
            // return seed;
            return static_cast<size_t>(p.i * 4231 + p.j * 3079 + p.k * 5669);
        }
    } hash_func;

    /** @brief grid-info：value type */
    typedef struct st_gridInfo
    {
        int m_iOcc; /**< 占有次 */
        int m_iRay; /**< 穿透次 */
        st_gridInfo() : m_iOcc(1), m_iRay(0) {}
    } st_gridInfo;

    using gridMap =
        boost::unordered_map<st_gridId, st_gridInfo, hash_func>; /**< 栅格地图类型<ijk,info>映射表
                                                                  */
    using gridMap_Ptr = boost::shared_ptr<gridMap>;              /**< 栅格地图类型指针 */
    using mapGridIter = typename gridMap::iterator;              /**< 栅格地图类型迭代器 */
    using indexmmap =
        std::multimap<st_gridId, size_t>; /**< 栅格点序号<ijk,index>映射表（有序有重复） */
    using indexmmap_Ptr = boost::shared_ptr<indexmmap>; /**< 栅格点序号<ijk,index>映射表指针 */
    using gridSet = boost::unordered_set<st_gridId, hash_func>; /**< 栅格集合<ijk>（无序无重复） */
    using indexSet = std::set<size_t>; /**< 序号集合<index>（有序无重复） */

    // 关键帧储存内容
    typedef struct st_keyFrame
    {
        Eigen::Vector3d m_pose; /**< 位姿 */
        PCFeaturePtr m_pcf;     /**< 第一点云 */
        PCFeaturePtr m_pcs;     /**< 第二点云 */
        PCRawPtr m_pct;         /**< 第三点云 */
        indexmmap_Ptr m_idxmap; /**< 栅格地图范围内的ijk-点序号对，需要加入栅格地图 */
        indexmmap_Ptr m_idxNomap; /**< 栅格地图范围外的ijk-点序号对，仅更新穿透 */

        /**
         * @brief Construct a new st keyFrame object
         *
         */
        st_keyFrame()
        {
            m_pcf.reset(new PointCloudFeature());
            m_pcs.reset(new PointCloudFeature());
            m_pct.reset(new PointCloudRaw());
            m_idxmap.reset(new indexmmap());
            m_idxNomap.reset(new indexmmap());
        }

        /**
         * @brief 释放内部储存的映射表
         *
         */
        void free()
        {
            if (this->m_idxmap)
                indexmmap().swap(*(this->m_idxmap));
            if (this->m_idxNomap)
                indexmmap().swap(*(this->m_idxNomap));
        }
    } st_keyFrame;

    using keyFrame_Ptr = boost::shared_ptr<st_keyFrame>; /**< 关键帧指针 */
    ////////////////////////////////////////////////////////////////////////////

    // 设置参数
    struct st_cfg
    {
        float m_fLeafSize[3];    /**<  下采样栅格 */
        float m_fInvLeafSize[3]; /**<  下采样栅格inv */
        float m_fMoveZMax;       /**<  最大删除高度 */
        float m_fMoveZMin;       /**<  最小删除高度 */
        u_int m_uiMinOcc;        /**<  最小占用次数阈值 */
        u_int m_uiMinNeighb;     /**<  最小邻居阈值 */
        u_int m_uiMaxOcc;        /**<  穿透判断中的最大占用阈值 */
        u_int m_uiMinRay;        /**<  穿透判断中的最小穿透阈值 */
        size_t m_ulWindowSize;   /**<  localMap list最小长度 */
        bool m_bRawFlt;          /**<  是否过滤原始点云 */
        int c_iGrowCount_[2];    /**<  过滤原始点云膨胀半径 */
        int m_iOverlapRayLevel; /**<  倍增穿透次数（插入新帧后重新更新旧帧的穿透） */
    } c_stCfg_;

    static constexpr size_t SIZE_T_MAX = ULONG_MAX;
    static constexpr float FLOAT_MAX = FLT_MAX;
    bool c_bDebug_ = false;                                 /**<  debug模式 */
    bool c_bIsFilteringNewst_ = false;                      /**< 正在过滤最新帧 */
    size_t c_ulNewFrame_ = -1;                              /**<  计数器 */
    std::map<int, keyFrame_Ptr> c_localFrames_;             /**<  localMap中的帧集合 */
    gridMap_Ptr c_gridMap_;                                 /**<  localMap栅格地图 */
    pcl::PointCloud<pcl::PointXYZHSV>::Ptr c_movingPoints_; /**<  debug 全部移除点可视化 */
    pcl::PointCloud<pcl::PointXYZHSV>::Ptr c_lastCloud_; /**<  debug 当前处理点云可视化 */

  public:
    /**
     * @brief 默认构造函数
     */
    RemoveMoving(wj_slam::s_RemovingConfig* p_conf = nullptr);

    /**
     * @brief debug构造函数
     * @param[in] p_bDebug 是否debug
     */
    RemoveMoving(bool p_bDebug);

    /**
     * @brief 设置RM栅格大小，支持xyz-gridSize不等
     * @attention !!不允许运行中修改!!
     * @param[in] p_fGrid 栅格大小
     */
    void setLeafSize(float p_fGrid = 0.3f);

    /**
     * @brief 设置RM栅格大小，支持xyz-gridSize不等
     * @attention !!不允许运行中修改!!
     * @param[in] p_fXGrid 栅格X大小
     * @param[in] p_fYGrid 栅格Y大小
     * @param[in] p_fZGrid 栅格Z大小
     */
    void setLeafSize(float p_fXGrid, float p_fYGrid, float p_fZGrid);

    /**
     * @brief 设置过滤空间的高度范围
     * @note 允许运行中修改
     * @param[in] p_fZmin z最小值
     * @param[in] p_fZmax z最大值
     */
    void setHeightSpace(float p_fZmin = -FLOAT_MAX, float p_fZmax = FLOAT_MAX);

    /**
     * @brief 设置localMap的使用的最小帧数
     * @note 允许运行中修改
     * @param[in] p_uiLocalMapFrameNumber 帧数
     */
    void setLocalMapSize(u_int p_uiLocalMapFrameNumber = 10);

    /**
     * @brief 设置最大最小占用次数，小于最小占用且邻居少将删除，小于最大占用并射穿透多将删除
     * @note 使用 <> 判断，而非 <=和>=
     * @note 允许运行中修改
     * @param[in] p_uiMinOccNumber 最小次数
     * @param[in] p_uiMaxOccNumber 最大次数
     */
    void setKoeff(u_int (&p_koeff)[4]);

    /**
     * @brief 占用少且邻居个数不足的删除
     * @param[in] p_uiMinNeighberNum 邻居个数
     */
    void setMinNeighberNum(u_int p_uiMinNeighberNum = 5);

    /**
     * @brief 设置是否同时过滤原始点云
     * @param[in] {bool} p_bDoFilter
     */
    void setFilterRawPointCloud(bool p_bDoFilter = false, int p_iGrow = 1);

    /**
     * @brief Get the Filter Raw Point Cloud object
     *
     * @return [true] [滤波器组件开]
     * @return [false] [滤波器组件关]
     */
    bool getFilterRawPointCloud();

    /**
     * @brief 设置穿透过滤级别
     * @note
     * -1级 只使用栅格地图范围内的点计算穿透
     * 0级(默认); 增加使用栅格地图外的点计算穿透（例如穿透人射向地面点,地面不在栅格地图范围内）
     * 1级 在更新地图后增加使用1次旧帧计算穿透（可删除速度相近的动态物体）
     * 2级 在更新地图后增加使用2次不同旧帧计算穿透（增加耗时）
     * @param[in] p_iLevel 穿透过滤级别
     */
    void setOverlapRayLevel(int p_iLevel = 0);

    /**
     * @brief Get the Overlap Ray Level object
     * @return [int] [穿透过滤级别]
     */
    int getOverlapRayLevel();

    /**
     * @brief 拷贝点云到localMap，使用外部计数器
     * @param[in] p_pcFeature1 当前点云1（全局坐标系下的）
     * @param[in] p_pcFeature2 当前点云2（全局坐标系下的）
     * @param[in] p_pose3D 当前位置
     * @param[in] p_ulFrameIndex 当前帧序号
     */
    void setInputCloud(PCFeaturePtr p_pcFeature1,
                       PCFeaturePtr p_pcFeature2,
                       Eigen::Vector3d p_pose3D,
                       size_t p_ulFrameIndex = SIZE_T_MAX);

    /**
     * @brief 拷贝点云到localMap，使用外部计数器
     * @param[in] p_pcFeature1 当前点云1（全局坐标系下的）
     * @param[in] p_pcFeature2 当前点云2（全局坐标系下的）
     * @param[in] p_pcRaw 当前点云3（全局坐标系下的）
     * @param[in] p_pose3D 当前位置
     * @param[in] p_ulFrameIndex 当前帧序号
     */
    void setInputCloud(PCFeaturePtr p_pcFeature1,
                       PCFeaturePtr p_pcFeature2,
                       PCRawPtr p_pcRaw,
                       Eigen::Vector3d p_pose3D,
                       size_t p_ulFrameIndex = SIZE_T_MAX);

    /**
     * @brief 使用最新InputCloud更新LocalMap
     * @note 如果穿透过滤级别>0，会使用旧帧增加穿透效果
     */
    void renewLocalMap();

    /**
     * @brief Get the Filter Index_ object
     *
     * @return [size_t] [当前过滤器输出帧号]
     */
    size_t getFilterIndex();

    /**
     * @brief 过滤第N帧点云，填充指针并返回帧号N
     * @note 过滤完成同时会更新地图，移除最旧帧以维持地图大小
     * @param[out] p_pcFeature1 输出点云指针
     * @param[out] p_pcFeature2 输出点云指针
     * @param[out] p_pcRaw 输出点云指针
     * @return [size_t] [过滤帧号]
     */
    size_t filter(PCFeaturePtr& p_pcFeature1, PCFeaturePtr& p_pcFeature2, PCRawPtr& p_pcRaw);

    /**
     * @brief 过滤第N帧点云，填充指针并返回帧号N
     * @note 过滤完成同时会更新地图，移除最旧帧以维持地图大小
     * @note 仅过滤特征点云
     * @param[out] p_pcFeature1 输出点云指针
     * @param[out] p_pcFeature2 输出点云指针
     * @return [size_t] [过滤帧号]
     */
    size_t filter(PCFeaturePtr& p_pcFeature1, PCFeaturePtr& p_pcFeature2);

    /**
     * @brief Get the Filtered Points object
     *
     * @return [pcl::PointCloud<pcl::PointXYZHSV>::Ptr] [debug,输出过滤点]
     */
    pcl::PointCloud<pcl::PointXYZHSV>::Ptr getFilteredPoints();

    /**
     * @brief Get the Filter Cloud object
     *
     * @return [pcl::PointCloud<pcl::PointXYZHSV>::Ptr] [debug,输出保存点]
     */
    pcl::PointCloud<pcl::PointXYZHSV>::Ptr getFilterCloud();

  private:
    /**
     * @brief 释放内存
     *
     */
    void allocateMemery_();

    /**
     * @brief Set the Default Parameter
     *
     */
    void setParameterDefault_();

    /**
     * @brief 获取 xyz位置的栅格
     * @param[in] x x值
     * @param[in] y y值
     * @param[in] z z值
     */
    inline Eigen::Vector3i getGridCoordinates_(float x, float y, float z);

    /**
     * @brief 获取 xyz位置的栅格
     * @tparam PT 点类型
     * @param[in] p 点
     */
    template <typename PT> inline st_gridId getGridCoordinates_(PT& p);

    /**
     * @brief 向localMap插入新的关键帧，更新栅格地图，占用和穿透值
     * @note 同步计算这一帧特征点云的<栅格,点序>关联表
     * @param[in] p_nF 特征帧
     */
    void insertNewFrame_(keyFrame_Ptr p_nF);

    /**
     * @brief计算点云每个点的栅格，储存<栅格,点序>关联表
     * @param[in] p_pc 要计算的点云
     * @param[in] p_ulOffset 点序号增量，如果是first点云则为0ul
     * @param[in] p_idxmap 栅格地图范围内的<栅格,点序>关联表
     * @param[in] p_idxNomap 栅格地图范围外的<栅格,点序>关联表，用于增加穿透
     * @param[in] p_fz 当前雷达高度值，用于计算栅格地图范围
     */
    template <typename PC>
    void
    getIdxmap_(PC& p_pc, size_t p_ulOffset, indexmmap& p_idxmap, indexmmap& p_idxNomap, float p_fz);

    /**
     * @brief 使用某一帧的合法栅格表更新地图
     * @param[in] p_idxmap 某一帧的栅格地图范围内的<栅格,点序>关联表
     */
    void insertCloud_(indexmmap& p_idxmap);

    /**
     * @brief 使用某一栅格表更新穿透
     * @param[in] p_pose 当前雷达位置
     * @param[in] p_idxmap 随意一张栅格表
     */
    void insertRay_(Eigen::Vector3d& p_pose, indexmmap& p_idxmap);

    /**
     * @brief 获取线段途径的栅格(i,j,k);
     * @note 避开终点旁边3*3格内栅格
     * @param[in] x1，y1，z1 起点栅格（雷达点）
     * @param[in] x2，y2，z2 终点栅格（点云点）
     * @param[in] rayNum 同步增加穿透数
     */
    void trace3DLine_(int x1, int y1, int z1, int x2, int y2, int z2, size_t rayNum);

    /**
     * @brief 在ijk位置若存在地图栅格则增加穿透数
     * @param[in] i，j，k 被穿透栅格
     * @param[in] rayNum 增加穿透数
     */
    void addRayThisGrid_(int i, int j, int k, size_t rayNum);

    /**
     * @brief  判定这一帧内栅格（同时也是地图栅格）是否符合删除条例
     * @param[in] p_id 栅格id
     * @param[in] p_info 栅格信息
     * @return [true] [删除]
     * @return [false] [否]
     */
    bool removeGridStrategySet_(const st_gridId& p_id, const st_gridInfo& p_info);

    /**
     * @brief  判定这一帧内栅格（同时也是地图栅格）是否符合删除条例
     * @param[in] p_id 栅格id
     * @param[in] p_info 栅格信息
     * @return [true] [删除]
     * @return [false] [否]
     */
    bool removeGridStrategySetNewF_(const st_gridId& p_id, const st_gridInfo& p_info);

    /**
     * @brief 判定占用率低且稀疏的栅格
     * @param[in] p_id 栅格id
     * @param[in] p_info 栅格信息
     * @return [true] [占用率低且稀疏]
     * @return [false] [否]
     */
    bool lessOccAndLessNeighber_(const st_gridId& p_id, const st_gridInfo& p_info);

    /**
     * @brief 判定穿透次数高的栅格
     * @param[in] p_id 栅格id
     * @param[in] p_info 栅格信息
     * @return [true] [穿透次数高]
     * @return [false] [否]
     */
    bool tooMuchRayHit_(const st_gridId& p_id, const st_gridInfo& p_info);

    /**
     * @brief 将特征点云转移到debug点云中，携带所在栅格的占用数和穿透数
     * @param[in] p_oF 特征帧
     */
    void getDebugCloud_(keyFrame_Ptr p_oF);

    /**
     * @brief 调用removeGridStrategySet_();获取当前帧的全部移除栅格
     * @param[in] p_idxmap 当前帧的栅格点序关联表
     * @param[in] p_set 输出移除栅格的集合（有序且无重复）
     */
    void getOutlieGrids_(indexmmap& p_idxmap, gridSet& p_set);

    /**
     * @brief 根据输入的移除栅格集合找出所有需删除的点号，在特征点云中删除他们
     * @param[in] p_oF 当前帧
     * @param[in] p_set 输入移除栅格的集合（有序且无重复）
     */
    void filterOutliersPointsInGrids_(keyFrame_Ptr p_oF, gridSet& p_set);

    /**
     * @brief 在pc中移除对应序号点云
     * @param[in] p_pc 当前处理的点云的引用

     */
    template <typename PC> void eraseIndeiesInCloud_(indexSet& p_ulMoveIndex, PC& p_pc);

    /**
     * @brief 根据输入的移除栅格集合找出所有需删除的点号，在原始点云中删除他们
     * @note 删除的栅格在输入栅格的基础上扩展到3*3立方
     * @param[in] p_oF 当前帧
     * @param[in] p_set 输入移除栅格的集合（有序且无重复）
     */
    void filterOutliersRawPointsNearGrids_(keyFrame_Ptr p_oF, gridSet& p_set);

    /**
     * @brief 计算这一帧需要删除的栅格，删除栅格内的点
     * @param[in] p_oF 特征帧
     */
    void filterOutliersOneFrame_(keyFrame_Ptr p_oF);

    /**
     * @brief 从localMap中移除这一帧，同时缩减grid数
     * @param[in] p_ulFrameIndex 删除帧号
     */
    void removeOldFrame_(size_t p_ulFrameIndex);

    /**
     * @brief debug，赋值一个带色彩的点云点
     *
     * @param[out] p_pds
     * @param[in] h
     * @param[in] s
     * @param[in] v
     */
    template <typename PointT>
    inline void debugCloudPushPoint_(PointT& p_pds, float h, float s, float v);

    /**
     * @brief  debug，生成一个带色彩的点云点
     *
     * @param[in] p_id 栅格号
     * @param[in] h
     * @param[in] s
     * @param[in] v
     */
    inline void debugCloudPushGrid_(const st_gridId& p_id, float h, float s, float v);

    /**
     * @brief  debug，生成一个带色彩的点云
     *
     * @param[in] p_id  栅格号
     */
    inline void eraseCloudPushGrid_(const st_gridId& p_id);
};
#ifdef WJSLAM_NO_PRECOMPILE
#    include "impl/removeMoving.hpp"
#else
#    define WJSLAM_RemoveMoving(P) template class RemoveMoving<P>;
#endif