
/*--------------------usecases--------------------
// 1.创建对象：
    removeMoving<PointFeature,PointRaw>*    rM;
    // 默认构造
    rM = new removeMoving<PointFeature,PointRaw>();
    // debug模式
    //rM = new removeMoving<PointFeature,PointRaw>(true);
// 2.设置参数：
    //必须设置且禁止运行中修改
    rM->setLeafSize(X栅格，Y栅格，Z栅格)     //默认 0.3,0.3,0.3
    rM->setFilterRawPointCloud(false);//设置是否处理原始点云
    //可以设置且允许运行中修改
    rM->setHeightSpace(最小z，最大z)        //默认 -FLT_MAX , FLT_MAX
    rM->setLocalMapSize(局部地图最小帧数)    //默认10
    // 占用数小于MinOccNum且邻居数小于MinNeighberNum将删除
    // 占用数小于MaxOccNum且穿透数大于半窗大小且占用小于穿透将删除
    rM->setMinMaxOccNum(允许最小占用次数，允许最大占用次数)    //默认 [3,5]
    rM->setMinNeighberNum(邻居个数);              //默认3
    // 穿透级别：
    // -1级 只使用栅格地图范围内的点计算穿透
    // 0级(默认) 增加使用栅格地图外的点计算穿透（例如穿透人射向地面点,地面不在栅格地图范围内）
    // 1级 在更新地图后增加使用1次旧帧计算穿透（可删除速度相近的动态物体）
    // 2级 在更新地图后增加使用2次不同旧帧计算穿透（增加耗时）
    rM->setOverlapRayLevel(穿透级别)；          //  默认0

// 3.线程内循环：
    // 输入新关键帧
    rM->setInputCloud(cornerCloudKeyFrames[l_thisFrameNo],surfCloudKeyFrames[l_thisFrameNo],fullResCloudKeyFrames[l_thisFrameNo],l_3dPose,l_thisFrameNo);
    // 向localMap添加新的关键帧，不读取外部数据
    rM->renewLocalMap();
    // 创建一些新的空点云指针
    pcl::PointCloud<PointFeature>::Ptr thisCornerFrame;
    pcl::PointCloud<PointFeature>::Ptr thisSurfFrame;
    pcl::PointCloud<PointRaw>::Ptr thisFullFrame;
    // 对localMap中间帧进行过滤，并从localMap中移除最旧帧：
    //
--中间帧为序号顺序排列的中位数，例如localmap持有[4，5，6，7，8]帧，filter会过滤帧号6，并返回帧号6
    // --最旧帧为序号最小帧，例如localmap持有[4，5，6，7，8]帧，filter完成后从localMap中删除帧号4
    //
**支持插入不连续的帧号，例如localmap持有[4，5，7，110，114，119，120]，filter会过滤帧号110，localmap删除帧号4，并返回帧号110
    size_t l_ulRenewFrame = rM->filter(thisCornerFrame, thisSurfFrame, thisFullFrame);//
函数返回被过滤帧号
    // 如果 返回值 == size_t最大值表示未进行更新
    if (l_ulRenewFrame != ULONG_MAX)
    {
        // 刷新持有关键帧序列中的点云指针
        cornerCloudKeyFrames[(int)l_ulRenewFrame] = thisCornerFrame;
        surfCloudKeyFrames[(int)l_ulRenewFrame] = thisSurfFrame;
        if (rM->getFilterRawPointCloud())
            fullResCloudKeyFrames[l_thisFrameNo] = thisFullFrame;
    }
    // debug发布被过滤的点:
    pcl::toROSMsg(*rM->getFilteredPoints(), tempCloudMsg);
*/

/*--------------------Test--------------------
1.金融港B5室外环境(开debug输出，主要目标为树叶、行人)：
    参数： localmapSize=10 grid = 0.2m ^3，raylevel-2，过滤原始点云
    时间： 平均13ms，峰值16ms
    效果： 特征点云：稀疏树叶全部过滤，行人膝盖以上全部过滤
    效果： 原始点云：只有部分行人留影被过滤，树叶无效；
2.成都厂区室外环境(关debug输出，主要目标为树叶、行人、卡车)：
    参数： map=10 grid = 0.2m ^3，raylevel-2，过滤原始点云
    时间： 平均13ms，峰值48ms
    效果： 特征点云：稀疏树叶全部过滤，行人全部过滤，卡车全部过滤
    效果： 原始点云：行人留影存在，树叶无效，卡车存在极少留影；
*/

#pragma once

#include <Eigen/Dense>
#include <boost/unordered_map.hpp>
#include <boost/unordered_set.hpp>
#include <float.h>
#include <map>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <vector>

// ijk：key type
typedef struct st_gridId
{
    int i;
    int j;
    int k;
    st_gridId() : i(0), j(0), k(0) {}
    st_gridId(int ix, int jy, int kz) : i(ix), j(jy), k(kz) {}
    st_gridId(Eigen::Vector3i ijk) : i(ijk.x()), j(ijk.y()), k(ijk.z()) {}
    bool operator==(const st_gridId& p) const
    {
        return i == p.i && j == p.j && k == p.k;
    }
    bool operator<(const st_gridId& p) const
    {
        if (i != p.i)
            return i < p.i;
        else if (j != p.j)
            return j < p.j;
        else
            return k < p.k;
    }
    size_t hash_key() const
    {
        return static_cast<size_t>(i * 4231 + j * 3079 + k * 5669);
    }
    Eigen::Vector3i vector() const
    {
        return Eigen::Vector3i(i, j, k);
    }
    void set(int ix, int jy, int kz)
    {
        i = ix;
        j = jy;
        k = kz;
    }
} st_gridId;
// 生成散列值规则
typedef struct hash_func
{
    size_t operator()(const st_gridId& p) const
    {
        // size_t seed = 0;
        // boost::hash_combine(seed, boost::hash_value(p.i));
        // boost::hash_combine(seed, boost::hash_value(p.j));
        // boost::hash_combine(seed, boost::hash_value(p.k));
        // return seed;
        return static_cast<size_t>(p.i * 4231 + p.j * 3079 + p.k * 5669);
    }
} hash_func;

// grid-info：value type
typedef struct st_gridInfo
{
    int m_iOcc;
    int m_iRay;
    st_gridInfo() : m_iOcc(1), m_iRay(0) {}
} st_gridInfo;

// 栅格地图类型<ijk,info>映射表
typedef boost::unordered_map<st_gridId, st_gridInfo, hash_func> gridMap;
// 栅格地图类型指针
typedef boost::shared_ptr<gridMap> gridMap_Ptr;
// 栅格地图类型迭代器
typedef gridMap::iterator mapGridIter;

// 栅格点序号<ijk,index>映射表（有序有重复）
typedef std::multimap<st_gridId, size_t> indexmmap;
typedef boost::shared_ptr<indexmmap> indexmmap_Ptr;
// 栅格集合<ijk>（无序无重复）
typedef boost::unordered_set<st_gridId, hash_func> gridSet;
// 序号集合<index>（有序无重复）
typedef std::set<size_t> indexSet;

const size_t SIZE_T_MAX = ULONG_MAX;
const float FLOAT_MAX = FLT_MAX;

template <typename PointFeature, typename PointRaw> class removeMoving {
  private:
    ////////////////////////////////////////////////////////////////////////////

    // typedef pcl::PointSurfel PointFeature;
    // typedef pcl::PointXYZ PointRaw;
    typedef pcl::PointCloud<PointRaw> PointCloudRaw;
    typedef boost::shared_ptr<pcl::PointCloud<PointRaw>> PCRawPtr;
    typedef pcl::PointCloud<PointFeature> PointCloudFeature;
    typedef boost::shared_ptr<pcl::PointCloud<PointFeature>> PCFeaturePtr;

    // 关键帧储存内容
    typedef struct st_keyFrame
    {
        Eigen::Vector3d m_pose;  //位姿
        PCFeaturePtr m_pcf;      //第一点云
        PCFeaturePtr m_pcs;      //第二点云
        PCRawPtr m_pct;          //第三点云
        indexmmap_Ptr m_idxmap;  //栅格地图范围内的ijk-点序号对，需要加入栅格地图
        indexmmap_Ptr m_idxNomap;  //栅格地图范围外的ijk-点序号对，仅更新穿透
        st_keyFrame()
        {
            m_pcf.reset(new PointCloudFeature());
            m_pcs.reset(new PointCloudFeature());
            m_pct.reset(new PointCloudRaw());
            m_idxmap.reset(new indexmmap());
            m_idxNomap.reset(new indexmmap());
        }
        void free()
        {
            if (this->m_idxmap)
                indexmmap().swap(*(this->m_idxmap));
            if (this->m_idxNomap)
                indexmmap().swap(*(this->m_idxNomap));
        }
    } st_keyFrame;
    // 关键帧指针
    typedef boost::shared_ptr<st_keyFrame> keyFrame_Ptr;
    ////////////////////////////////////////////////////////////////////////////

    // 设置参数
    struct st_cfg
    {
        float m_fLeafSize[3];     // 下采样栅格
        float m_fInvLeafSize[3];  // 下采样栅格inv
        float m_fMoveZMax;        // 最大删除高度
        float m_fMoveZMin;        // 最小删除高度
        u_int m_uiMinOcc;         // 最小占用次数阈值
        u_int m_uiMinNeighb;      // 最小邻居阈值
        u_int m_uiMaxOcc;         // 穿透判断中的最大占用阈值
        size_t m_ulWindowSize;    // localMap list最小长度
        bool m_bRawFlt;           // 是否过滤原始点云
        int m_iOverlapRayLevel;  // 倍增穿透次数（插入新帧后重新更新旧帧的穿透）
    } c_stCfg_;
    // debug模式
    bool c_bDebug_;
    // 计数器
    size_t c_ulNewFrame_ = -1;

    // localMap中的帧集合
    std::map<int, keyFrame_Ptr> c_localFrames_;
    // localMap栅格地图
    gridMap_Ptr c_gridMap_;

    // debug 全部移除点可视化
    pcl::PointCloud<pcl::PointXYZ>::Ptr c_movingPoints_;
    // debug 当前处理点云可视化
    pcl::PointCloud<pcl::PointXYZHSV>::Ptr c_lastCloud_;

  public:
    /**
     * @description: 默认构造函数
     * @param {*}
     * @return {*}
     */
    removeMoving()
    {
        c_bDebug_ = false;
        allocateMemery_();
        setParameterDefault_();
    }

    /**
     * @description: debug构造函数
     * @param {bool} p_bDebug 是否debug
     * @return {*}
     */
    removeMoving(bool p_bDebug) : c_bDebug_(p_bDebug)
    {
        allocateMemery_();
        setParameterDefault_();
    }

    /**
     * @description: 设置RM栅格大小，支持xyz-gridSize不等
     * @note: !!不允许运行中修改!!
     * @param {float} p_fXGrid
     * @param {float} p_fYGrid
     * @param {float} p_fZGrid
     * @return {*}
     */
    void setLeafSize(float p_fXGrid = 0.3f, float p_fYGrid = 0.3f, float p_fZGrid = 0.3f)
    {
        c_stCfg_.m_fLeafSize[0] = p_fXGrid;
        c_stCfg_.m_fLeafSize[1] = p_fYGrid;
        c_stCfg_.m_fLeafSize[2] = p_fZGrid;

        c_stCfg_.m_fInvLeafSize[0] = 1.0f / c_stCfg_.m_fLeafSize[0];
        c_stCfg_.m_fInvLeafSize[1] = 1.0f / c_stCfg_.m_fLeafSize[1];
        c_stCfg_.m_fInvLeafSize[2] = 1.0f / c_stCfg_.m_fLeafSize[2];

        if (c_bDebug_)
            fprintf(stderr,
                    "setLeafSize[%f,%f,%f]\n",
                    c_stCfg_.m_fLeafSize[0],
                    c_stCfg_.m_fLeafSize[1],
                    c_stCfg_.m_fLeafSize[2]);
    }

    /**
     * @description: 设置过滤空间的高度范围
     * @note: 允许运行中修改
     * @param {float} p_fZmin z最小值
     * @param {float} p_fZmax z最大值
     * @return {*}
     */
    void setHeightSpace(float p_fZmin = -FLOAT_MAX, float p_fZmax = FLOAT_MAX)
    {
        c_stCfg_.m_fMoveZMax = p_fZmax;
        c_stCfg_.m_fMoveZMin = p_fZmin;
        if (c_bDebug_)
            fprintf(stderr, "setHeightSpace[%f,%f]\n", c_stCfg_.m_fMoveZMin, c_stCfg_.m_fMoveZMax);
    }

    /**
     * @description: 设置localMap的使用的最小帧数
     * @note: 允许运行中修改
     * @param {u_int} p_uiLocalMapFrameNumber 帧数
     * @return {*}
     */
    void setLocalMapSize(u_int p_uiLocalMapFrameNumber = 10)
    {
        c_stCfg_.m_ulWindowSize = p_uiLocalMapFrameNumber;
    }

    /**
     * @description: 设置最大最小占用次数，小于最小占用且邻居少将删除，小于最大占用并射穿透多将删除
     * @note: 使用 <> 判断，而非 <=和>=
     * @note: 允许运行中修改
     * @param {u_int} p_uiMinOccNumber 最小次数
     * @param {u_int} p_uiMaxOccNumber 最大次数
     * @return {*}
     */
    void setMinMaxOccNum(u_int p_uiMinOccNumber = 3, u_int p_uiMaxOccNumber = 5)
    {
        c_stCfg_.m_uiMinOcc = p_uiMinOccNumber;
        c_stCfg_.m_uiMaxOcc = p_uiMaxOccNumber;
    }

    /**
     * @description: 占用少且邻居个数不足的删除
     * @param {u_int} p_uiMinNeighberNum 邻居个数
     * @return {*}
     */
    void setMinNeighberNum(u_int p_uiMinNeighberNum = 5)
    {
        c_stCfg_.m_uiMinNeighb = p_uiMinNeighberNum;
    }

    /**
     * @description: 设置是否同时过滤原始点云
     * @param {bool} p_bDoFilter
     * @return {*}
     */
    void setFilterRawPointCloud(bool p_bDoFilter = false)
    {
        c_stCfg_.m_bRawFlt = p_bDoFilter;
    }
    bool getFilterRawPointCloud()
    {
        return c_stCfg_.m_bRawFlt;
    }

    /**
     * @description:设置穿透过滤级别
     * -1级 只使用栅格地图范围内的点计算穿透
     * 0级(默认) 增加使用栅格地图外的点计算穿透（例如穿透人射向地面点,地面不在栅格地图范围内）
     * 1级 在更新地图后增加使用1次旧帧计算穿透（可删除速度相近的动态物体）
     * 2级 在更新地图后增加使用2次不同旧帧计算穿透（增加耗时）
     * @param {int} p_iLevel
     * @return {*}
     */
    void setOverlapRayLevel(int p_iLevel = 0)
    {
        c_stCfg_.m_iOverlapRayLevel = p_iLevel;
    }
    int getOverlapRayLevel()
    {
        return c_stCfg_.m_iOverlapRayLevel;
    }

    /**
     * @description: 拷贝点云到localMap，使用外部计数器
     * @param p_pcFeature1 当前点云1（全局坐标系下的）
     * @param p_pcFeature2 当前点云2（全局坐标系下的）
     * @param p_pose3D 当前位置
     * @param p_ulFrameIndex 当前帧序号
     * @return 当前帧序号
     */
    void setInputCloud(PCFeaturePtr p_pcFeature1,
                       PCFeaturePtr p_pcFeature2,
                       Eigen::Vector3d p_pose3D,
                       size_t p_ulFrameIndex = SIZE_T_MAX)
    {
        if (!p_pcFeature1 || !p_pcFeature2)
        {
            fprintf(stderr, "[RM][setInputCloud]:p_pcFeature1 is null.\n");
            return;
        }

        // 内建计数器同步
        if (p_ulFrameIndex != SIZE_T_MAX)
            c_ulNewFrame_ = p_ulFrameIndex;
        else
            c_ulNewFrame_++;

        c_localFrames_[c_ulNewFrame_].reset(new st_keyFrame());
        c_localFrames_[c_ulNewFrame_]->m_pose = p_pose3D;

        *c_localFrames_[c_ulNewFrame_]->m_pcf = *p_pcFeature1;
        *c_localFrames_[c_ulNewFrame_]->m_pcs = *p_pcFeature2;

        if (c_bDebug_)
            fprintf(stderr, "setInputCloud frameIndex-%ld \n", p_ulFrameIndex);
    }

    /**
     * @description: 拷贝点云到localMap，使用外部计数器
     * @param p_pcFeature1 当前点云1（全局坐标系下的）
     * @param p_pcFeature2 当前点云2（全局坐标系下的）
     * @param p_pcRaw 当前点云3（全局坐标系下的）
     * @param p_pose3D 当前位置
     * @param p_ulFrameIndex 当前帧序号
     * @return 当前帧序号
     */
    void setInputCloud(PCFeaturePtr p_pcFeature1,
                       PCFeaturePtr p_pcFeature2,
                       PCRawPtr p_pcRaw,
                       Eigen::Vector3d p_pose3D,
                       size_t p_ulFrameIndex = SIZE_T_MAX)
    {
        setInputCloud(p_pcFeature1, p_pcFeature2, p_pose3D, p_ulFrameIndex);
        if (c_stCfg_.m_bRawFlt)
        {
            if (!p_pcRaw)
            {
                fprintf(stderr, "[RM][setInputCloud]:p_pcRaw is null.\n");
                return;
            }
            *c_localFrames_[c_ulNewFrame_]->m_pct = *p_pcRaw;
        }
    }

    /**
     * @description: 使用最新InputCloud更新LocalMap
     * @note: 如果穿透过滤级别>0，会使用旧帧增加穿透效果
     * @param {*}
     * @return {*}
     */
    void renewLocalMap()
    {
        // 使用新帧更新栅格地图
        insertNewFrame_(c_localFrames_[c_ulNewFrame_]);
        // 使用旧帧更新穿透
        if (c_localFrames_.size() > 3)
        {
            keyFrame_Ptr l_oldFrame;
            if (c_stCfg_.m_iOverlapRayLevel > 0)
            {
                l_oldFrame = (--(--c_localFrames_.rbegin()))->second;
                insertRay_(l_oldFrame->m_pose, *(l_oldFrame->m_idxmap));
                insertRay_(l_oldFrame->m_pose, *(l_oldFrame->m_idxNomap));
            }
            if (c_stCfg_.m_iOverlapRayLevel > 1)
            {
                l_oldFrame = (++(++c_localFrames_.begin()))->second;
                insertRay_(l_oldFrame->m_pose, *(l_oldFrame->m_idxmap));
                insertRay_(l_oldFrame->m_pose, *(l_oldFrame->m_idxNomap));
            }
        }
    }

    /**
     * @description: 过滤第N帧点云，填充指针并返回帧号N
     * @note: 过滤完成同时会更新地图，移除最旧帧以维持地图大小
     * @param {PCFeaturePtr} p_pcFeature1 输出点云指针
     * @param {PCFeaturePtr} p_pcFeature2 输出点云指针
     * @return {*}
     */
    size_t filter(PCFeaturePtr& p_pcFeature1, PCFeaturePtr& p_pcFeature2, PCRawPtr& p_pcRaw)
    {
        // 如果此时未填满localmap则退出
        if (c_localFrames_.size() < c_stCfg_.m_ulWindowSize)
            return SIZE_T_MAX;
        // return -1;

        // 当前要处理的帧序号
        size_t l_ulFrameIndex = c_localFrames_.begin()->first;  //处理末尾帧
        // size_t l_ulFrameIndex =  c_localFrames_.rbegin()->first; //处理最新帧

        // size_t l_ulFrameIndex;
        // size_t length = c_localFrames_.size() * 0.5;  //处理帧位置（任意位置）
        // for (auto& frame : c_localFrames_)
        // {
        //     if (length == 0)
        //         l_ulFrameIndex = frame.first;
        //     length--;
        // }

        if (c_bDebug_)
            fprintf(stderr, "removing movingObject in frameIndex-%ld\n", l_ulFrameIndex);
        filterOutliersOneFrame_(c_localFrames_[l_ulFrameIndex]);

        p_pcFeature1 = c_localFrames_[l_ulFrameIndex]->m_pcf;
        p_pcFeature2 = c_localFrames_[l_ulFrameIndex]->m_pcs;
        if (c_stCfg_.m_bRawFlt)
            p_pcRaw = c_localFrames_[l_ulFrameIndex]->m_pct;

        if (c_localFrames_.size() >= c_stCfg_.m_ulWindowSize)
        {
            size_t l_ulEraseFrameIndex = c_localFrames_.begin()->first;
            removeOldFrame_(l_ulEraseFrameIndex);
            if (c_bDebug_)
                fprintf(stderr, "remove frameIndex-%ld from localMap\n", l_ulEraseFrameIndex);
        }
        return l_ulFrameIndex;
    }

    size_t filter(PCFeaturePtr& p_pcFeature1, PCFeaturePtr& p_pcFeature2)
    {
        // 如果此时未填满localmap则退出
        if (c_localFrames_.size() < c_stCfg_.m_ulWindowSize)
            return SIZE_T_MAX;
        // return -1;

        // 当前要处理的帧序号
        typename std::map<int, keyFrame_Ptr>::iterator beg;
        beg = c_localFrames_.begin();
        size_t l_ulFrameIndex = (beg++)->first;  //处理末尾帧
        // size_t l_ulFrameIndex =  c_localFrames_.rbegin()->first; //处理最新帧

        // size_t l_ulFrameIndex;
        // size_t length = c_localFrames_.size() * 0.5;  //处理帧位置（任意位置）
        // printf("length = %d , %d\n", length, c_localFrames_.size());
        // for (auto& frame : c_localFrames_)
        // {
        //     if (length == 0)
        //         l_ulFrameIndex = frame.first;
        //     length--;
        // }
        printf("l_ulFrameIndex = %d\n", l_ulFrameIndex);
        if (c_bDebug_)
            fprintf(stderr, "removing movingObject in frameIndex-%ld\n", l_ulFrameIndex);
        filterOutliersOneFrame_(c_localFrames_[l_ulFrameIndex]);

        p_pcFeature1 = c_localFrames_[l_ulFrameIndex]->m_pcf;
        p_pcFeature2 = c_localFrames_[l_ulFrameIndex]->m_pcs;

        if (c_localFrames_.size() >= c_stCfg_.m_ulWindowSize)
        {
            size_t l_ulEraseFrameIndex = c_localFrames_.begin()->first;
            removeOldFrame_(l_ulEraseFrameIndex);
            if (c_bDebug_)
                fprintf(stderr, "remove frameIndex-%d from localMap\n", l_ulEraseFrameIndex);
        }
        return l_ulFrameIndex;
    }

    pcl::PointCloud<pcl::PointXYZ>::Ptr getFilteredPoints()
    {
        return c_movingPoints_;
    }
    pcl::PointCloud<pcl::PointXYZHSV>::Ptr getFilterCloud()
    {
        return c_lastCloud_;
    }

  private:
    void allocateMemery_()
    {
        c_lastCloud_.reset(new pcl::PointCloud<pcl::PointXYZHSV>());
        c_movingPoints_.reset(new pcl::PointCloud<pcl::PointXYZ>());
        c_gridMap_.reset(new gridMap());
        c_gridMap_->reserve(10009ul);
    }

    void setParameterDefault_()
    {
        setLeafSize();
        setLocalMapSize();
        setHeightSpace();
        setMinMaxOccNum();
        setMinNeighberNum();
        setFilterRawPointCloud();
        setOverlapRayLevel();
    }

    /**
     * @description: 获取 xyz位置的栅格
     * @param {float} x
     * @param {float} y
     * @param {float} z
     * @return {*}
     */
    inline Eigen::Vector3i getGridCoordinates_(float x, float y, float z)
    {
        return (Eigen::Vector3i(static_cast<int>(floor(x * c_stCfg_.m_fInvLeafSize[0])),
                                static_cast<int>(floor(y * c_stCfg_.m_fInvLeafSize[1])),
                                static_cast<int>(floor(z * c_stCfg_.m_fInvLeafSize[2]))));
    }

    /**
     * @description: 获取 xyz位置的栅格
     * @param {*}
     * @return {*}
     */
    template <typename PT> inline st_gridId getGridCoordinates_(PT& p)
    {
        return (st_gridId(static_cast<int>(floor(p.x * c_stCfg_.m_fInvLeafSize[0])),
                          static_cast<int>(floor(p.y * c_stCfg_.m_fInvLeafSize[1])),
                          static_cast<int>(floor(p.z * c_stCfg_.m_fInvLeafSize[2]))));
    }

    /**
     * @description: 向localMap插入新的关键帧，更新栅格地图，占用和穿透值
     * @note: 同步计算这一帧特征点云的<栅格,点序>关联表
     * @param {keyFrame_Ptr} p_nF
     * @return {*}
     */
    void insertNewFrame_(keyFrame_Ptr p_nF)
    {
        //计算当前帧的占据栅格
        getIdxmap_(
            *(p_nF->m_pcf), 0ul, *(p_nF->m_idxmap), *(p_nF->m_idxNomap), (float)p_nF->m_pose.z());
        getIdxmap_(*(p_nF->m_pcs),
                   p_nF->m_pcf->size(),
                   *(p_nF->m_idxmap),
                   *(p_nF->m_idxNomap),
                   (float)p_nF->m_pose.z());
        // 更新占据栅格次数到栅格地图
        insertCloud_(*(p_nF->m_idxmap));
        // 当前帧的穿透更新
        insertRay_(p_nF->m_pose, *(p_nF->m_idxmap));
        if (c_stCfg_.m_iOverlapRayLevel > -1)
            insertRay_(p_nF->m_pose, *(p_nF->m_idxNomap));
    }

    /**
     * @description:计算点云每个点的栅格，储存<栅格,点序>关联表
     * @param p_pc 要计算的点云
     * @param p_ulOffset 点序号增量，如果是first点云则为0ul
     * @param p_idxmap 栅格地图范围内的<栅格,点序>关联表
     * @param p_idxNomap 栅格地图范围外的<栅格,点序>关联表，用于增加穿透
     * @param p_fz 当前雷达高度值，用于计算栅格地图范围
     * @return {*}
     */
    template <typename PC>
    void
    getIdxmap_(PC& p_pc, size_t p_ulOffset, indexmmap& p_idxmap, indexmmap& p_idxNomap, float p_fz)
    {
        float l_fMinZ = p_fz + c_stCfg_.m_fMoveZMin;
        float l_fMaxZ = p_fz + c_stCfg_.m_fMoveZMax;
        st_gridId l_stTempId = st_gridId();
        size_t index = 0;
        for (size_t i = 0; i < p_pc.size(); i++)
        {
            l_stTempId = st_gridId(getGridCoordinates_(p_pc.points[i]));
            // mmap中增加id-index对
            index = i + p_ulOffset;
            // 只处理特定区域(雷达上下范围)
            if (p_pc.points[i].z > l_fMinZ && p_pc.points[i].z < l_fMaxZ)
            {
                p_idxmap.insert(indexmmap::value_type(l_stTempId, index));
            }
            else
            {
                p_idxNomap.insert(indexmmap::value_type(l_stTempId, index));
            }
        }
    }

    /**
     * @description: 使用某一帧的合法栅格表更新地图
     * @param p_idxmap 某一帧的栅格地图范围内的<栅格,点序>关联表
     * @return {*}
     */
    void insertCloud_(indexmmap& p_idxmap)
    {
        st_gridInfo l_stTempInfo = st_gridInfo();
        mapGridIter gridInfoPair;
        for (auto gridIdxPair = p_idxmap.begin(); gridIdxPair != p_idxmap.end();)
        {
            gridInfoPair = c_gridMap_->find(gridIdxPair->first);
            // 查找栅格地图中是否存在栅格点，存在则增加占用，若不存在则添加
            if (gridInfoPair == c_gridMap_->end())
            {
                l_stTempInfo.m_iOcc = p_idxmap.count(gridIdxPair->first);
                c_gridMap_->insert(gridMap::value_type(gridIdxPair->first, l_stTempInfo));
            }
            else
            {
                gridInfoPair->second.m_iOcc += p_idxmap.count(gridIdxPair->first);
            }
            // 获取下一组第一个键值对的iter
            gridIdxPair = p_idxmap.upper_bound(gridIdxPair->first);
        }
    }

    /**
     * @description: 使用某一栅格表更新穿透
     * @param p_pose 当前雷达位置
     * @param p_idxmap 随意一张栅格表
     * @return {*}
     */
    void insertRay_(Eigen::Vector3d& p_pose, indexmmap& p_idxmap)
    {
        Eigen::Vector3i l_pose = getGridCoordinates_(p_pose.x(), p_pose.y(), p_pose.z());
        Eigen::Vector3i l_p;
        for (auto gridIdxPair = p_idxmap.begin(); gridIdxPair != p_idxmap.end();)
        {
            l_p = gridIdxPair->first.vector();
            trace3DLine_(l_pose.x(),
                         l_pose.y(),
                         l_pose.z(),
                         l_p.x(),
                         l_p.y(),
                         l_p.z(),
                         p_idxmap.count(gridIdxPair->first));  //
            // 获取下一组第一个键值对的iter
            gridIdxPair = p_idxmap.upper_bound(gridIdxPair->first);
        }
    }

    /**
     * @description: 获取线段途径的栅格(i,j,k)
     * @note: 避开终点旁边3*3格内栅格
     * @param {int} x1，y1，z1 起点栅格（雷达点）
     * @param {int} x2，y2，z2 终点栅格（点云点）
     * @param {size_t} rayNum 同步增加穿透数
     * @return {*}
     */
    void trace3DLine_(int x1, int y1, int z1, int x2, int y2, int z2, size_t rayNum)
    {
        int xd, yd, zd;
        int x, y, z;
        int ax, ay, az;
        int sx, sy, sz;
        int dx, dy, dz;

        dx = x2 - x1;
        dy = y2 - y1;
        dz = z2 - z1;

        ax = std::abs(dx) << 1;
        ay = std::abs(dy) << 1;
        az = std::abs(dz) << 1;

        sx = ((dx) < 0) ? -1 : (dx) > 0 ? 1 : 0;
        sy = ((dy) < 0) ? -1 : (dy) > 0 ? 1 : 0;
        sz = ((dz) < 0) ? -1 : (dz) > 0 ? 1 : 0;

        x = x1;
        y = y1;
        z = z1;

        if (ax >= std::max(ay, az)) /* x dominant */
        {
            yd = ay - (ax >> 1);
            zd = az - (ax >> 1);
            for (;;)
            {
                // ijk已获取
                addRayThisGrid_(x, y, z, rayNum);
                // 如果直线段已到达当前帧旁边，则退出,避免hit临近方块
                // if (x == x2)
                if (std::abs(x - x2) < 2 && std::abs(y - y2) < 2 && std::abs(z - z2) < 2)
                {
                    return;
                }

                if (yd >= 0)
                {
                    y += sy;
                    yd -= ax;
                }

                if (zd >= 0)
                {
                    z += sz;
                    zd -= ax;
                }

                x += sx;
                yd += ay;
                zd += az;
            }
        }
        else if (ay >= std::max(ax, az)) /* y dominant */
        {
            xd = ax - (ay >> 1);
            zd = az - (ay >> 1);
            for (;;)
            {
                // ijk获取
                addRayThisGrid_(x, y, z, rayNum);
                // if (y == y2)
                if (std::abs(x - x2) < 2 && std::abs(y - y2) < 2 && std::abs(z - z2) < 2)
                {
                    return;
                }

                if (xd >= 0)
                {
                    x += sx;
                    xd -= ay;
                }

                if (zd >= 0)
                {
                    z += sz;
                    zd -= ay;
                }

                y += sy;
                xd += ax;
                zd += az;
            }
        }
        else if (az >= std::max(ax, ay)) /* z dominant */
        {
            xd = ax - (az >> 1);
            yd = ay - (az >> 1);
            for (;;)
            {
                // ijk获取
                addRayThisGrid_(x, y, z, rayNum);
                // if (z == z2)
                if (std::abs(x - x2) < 2 && std::abs(y - y2) < 2 && std::abs(z - z2) < 2)
                {
                    return;
                }

                if (xd >= 0)
                {
                    x += sx;
                    xd -= az;
                }

                if (yd >= 0)
                {
                    y += sy;
                    yd -= az;
                }

                z += sz;
                xd += ax;
                yd += ay;
            }
        }
    }

    /**
     * @description: 在ijk位置若存在地图栅格则增加穿透数
     * @param {int} i，j，k 被穿透栅格
     * @param {size_t} rayNum 增加穿透数
     * @return {*}
     */
    void addRayThisGrid_(int i, int j, int k, size_t rayNum)
    {
        st_gridId l_tempGrid(i, j, k);
        mapGridIter gridInfoPair = c_gridMap_->find(l_tempGrid);
        if (gridInfoPair != c_gridMap_->end())
        {
            gridInfoPair->second.m_iRay += rayNum;
        }
    }

    /**
     * @description:  判定这一帧内栅格（同时也是地图栅格）是否符合删除条例
     * @param {const} p_id 栅格id
     * @param {const} p_info 栅格信息
     * @return 是否删除
     */
    bool removeGridStrategySet_(const st_gridId& p_id, const st_gridInfo& p_info)
    {
        if (lessOccAndLessNeighber_(p_id, p_info))
        {
            return true;
        }
        else if (tooMuchRayHit_(p_id, p_info))
        {
            return true;
        }
        return false;
    }

    /**
     * @description: 判定占用率低且稀疏的栅格
     * @param {const} p_id 栅格id
     * @param {const} p_info 栅格信息
     * @return {*}
     */
    bool lessOccAndLessNeighber_(const st_gridId& p_id, const st_gridInfo& p_info)
    {
        if (p_info.m_iOcc < (int)c_stCfg_.m_uiMinOcc)
        {
            uint l_uiNeighber = 0;
            int l_iMaxOcc = p_info.m_iOcc;
            st_gridId l_tempGrid;
            mapGridIter gridInfoPair;
            // 搜索3*3立方体中所有可能ijk
            for (int i = p_id.i - 1; i < p_id.i + 2; i++)
                for (int j = p_id.j - 1; j < p_id.j + 2; j++)
                    for (int k = p_id.k - 1; k < p_id.k + 2; k++)
                    {
                        l_tempGrid.set(i, j, k);
                        // if (l_tempGrid == p_id) continue;

                        gridInfoPair = c_gridMap_->find(l_tempGrid);
                        if (gridInfoPair != c_gridMap_->end())
                        {
                            l_uiNeighber++;
                            if (gridInfoPair->second.m_iOcc > l_iMaxOcc)
                                l_iMaxOcc = gridInfoPair->second.m_iOcc;
                            // 如果邻居数足够多，或相邻栅格占用大
                            if (l_uiNeighber - 1 >= c_stCfg_.m_uiMinNeighb
                                || l_iMaxOcc >= (int)c_stCfg_.m_uiMinOcc)
                            {
                                return false;
                            }
                        }
                    }
            // 邻居数量少且邻居占用不足
            return true;
        }
        return false;
    }

    /**
     * @description: 判定穿透次数高的栅格
     * @param {const} p_id 栅格id
     * @param {const} p_info 栅格信息
     * @return {*}
     */
    bool tooMuchRayHit_(const st_gridId& p_id, const st_gridInfo& p_info)
    {
        if (p_info.m_iRay > 0.5 * c_stCfg_.m_ulWindowSize
            && p_info.m_iOcc < (int)c_stCfg_.m_uiMaxOcc && p_info.m_iOcc < p_info.m_iRay)
        {
            return true;
        }
        return false;
    }

    /**
     * @description: 将特征点云转移到debug点云中，携带所在栅格的占用数和穿透数
     * @param {keyFrame_Ptr} p_oF
     * @return {*}
     */
    void getDebugCloud_(keyFrame_Ptr p_oF)
    {
        indexmmap& p_idxmap = *(p_oF->m_idxmap);
        mapGridIter gridInfoPair;

        c_lastCloud_->clear();
        // debug 加入debug点云
        for (auto gridIdxPair = p_idxmap.begin(); gridIdxPair != p_idxmap.end();)
        {
            gridInfoPair = c_gridMap_->find(gridIdxPair->first);
            debugCloudPushGrid_(gridIdxPair->first,
                                (float)gridInfoPair->second.m_iOcc,
                                (float)gridInfoPair->second.m_iRay,
                                0);
            // 获取下一组第一个键值对的iter
            gridIdxPair = p_idxmap.upper_bound(gridIdxPair->first);
        }
    }

    /**
     * @description: 调用removeGridStrategySet_()获取当前帧的全部移除栅格
     * @param p_idxmap 当前帧的栅格点序关联表
     * @param p_set 输出移除栅格的集合（有序且无重复）
     * @return {*}
     */
    void getOutlieGrids_(indexmmap& p_idxmap, gridSet& p_set)
    {
        mapGridIter gridInfoPair;

        for (auto gridIdxPair = p_idxmap.begin(); gridIdxPair != p_idxmap.end();)
        {
            gridInfoPair = c_gridMap_->find(gridIdxPair->first);
            // 根据条件添加删除表
            if (removeGridStrategySet_(gridInfoPair->first, gridInfoPair->second))
            {
                p_set.insert(gridInfoPair->first);
            }
            // 获取下一组第一个键值对的iter
            gridIdxPair = p_idxmap.upper_bound(gridIdxPair->first);
        }
    }

    /**
     * @description: 根据输入的移除栅格集合找出所有需删除的点号，在特征点云中删除他们
     * @param {keyFrame_Ptr} p_oF 当前帧
     * @param p_set 输入移除栅格的集合（有序且无重复）
     * @return {*}
     */
    void filterOutliersPointsInGrids_(keyFrame_Ptr p_oF, gridSet& p_set)
    {
        indexmmap& p_idxmap = *(p_oF->m_idxmap);
        size_t p_ulSecondOffset = p_oF->m_pcf->size();
        size_t p_ulAllOffset = p_oF->m_pcf->size() + p_oF->m_pcs->size();

        // 要移除的原始点的序号
        indexSet* l_ulMoveIndexFirst = new indexSet();
        indexSet* l_ulMoveIndexSecond = new indexSet();

        // debug加载点到debug点云
        if (c_bDebug_)
            c_movingPoints_->clear();

        for (auto& grid : p_set)
        {
            if (c_bDebug_)
                eraseCloudPushGrid_(grid);
            // 添加删除表
            for (auto thisGridIdxPair = p_idxmap.lower_bound(grid);
                 thisGridIdxPair != p_idxmap.upper_bound(grid);
                 thisGridIdxPair++)
            {
                // first中的删除点，逆序
                if (thisGridIdxPair->second < p_ulSecondOffset)
                    l_ulMoveIndexFirst->insert(thisGridIdxPair->second);
                // second中的删除点，逆序
                else if (thisGridIdxPair->second < p_ulAllOffset)
                    l_ulMoveIndexSecond->insert(thisGridIdxPair->second - p_ulSecondOffset);
            }
        }

        eraseIndeiesInCloud_(*l_ulMoveIndexFirst, *(p_oF->m_pcf));
        delete l_ulMoveIndexFirst;
        eraseIndeiesInCloud_(*l_ulMoveIndexSecond, *(p_oF->m_pcs));
        delete l_ulMoveIndexSecond;
    }

    /**
     * @description: 在pc中移除对应序号点云
     * @param p_pc 当前处理的点云的引用
     * @return {*}
     */
    template <typename PC> void eraseIndeiesInCloud_(indexSet& p_ulMoveIndex, PC& p_pc)
    {
        // 逆序erase，不影响前面序号
        for (auto l_ulInd = p_ulMoveIndex.rbegin(); l_ulInd != p_ulMoveIndex.rend(); l_ulInd++)
        {
            p_pc.erase(p_pc.begin() + *l_ulInd);
        }
    }

    /**
     * @description: 根据输入的移除栅格集合找出所有需删除的点号，在原始点云中删除他们
     * @note: 删除的栅格在输入栅格的基础上扩展到3*3立方
     * @param {keyFrame_Ptr} p_oF 当前帧
     * @param p_set 输入移除栅格的集合（有序且无重复）
     * @return {*}
     */
    void filterOutliersRawPointsNearGrids_(keyFrame_Ptr p_oF, gridSet& p_set)
    {
        // 临时清单
        indexmmap_Ptr l_idxRawMap(new indexmmap());
        indexmmap_Ptr l_idxRawNoMap(new indexmmap());
        // 栅格扩张，将特征栅格扩散到3*3立方体
        st_gridId l_tempGrid;
        gridSet* l_growSet = new gridSet(p_set);
        // 要移除的原始点的序号
        indexSet* l_ulMoveIndex = new indexSet();
        getIdxmap_(*(p_oF->m_pct), 0ul, *l_idxRawMap, *l_idxRawNoMap, p_oF->m_pose.z());
        indexmmap().swap(*(l_idxRawNoMap));

        for (auto& grid : p_set)
        {
            // 搜索3*3立方体中所有可能ijk
            for (int i = grid.i - 1; i < grid.i + 2; i++)
                for (int j = grid.j - 1; j < grid.j + 2; j++)
                    for (int k = grid.k - 1; k < grid.k + 2; k++)
                    {
                        l_tempGrid.set(i, j, k);
                        // 如果原始点云包含这个ijk，则加入删除列表
                        if (l_idxRawMap->find(l_tempGrid) != l_idxRawMap->end())
                        {
                            l_growSet->insert(l_tempGrid);
                        }
                    }
        }

        // 添加删除表
        for (auto& grid : *l_growSet)
        {
            for (auto thisGridIdxPair = l_idxRawMap->lower_bound(grid);
                 thisGridIdxPair != l_idxRawMap->upper_bound(grid);
                 thisGridIdxPair++)
            {
                l_ulMoveIndex->insert(thisGridIdxPair->second);
            }
        }

        // if (c_bDebug_) fprintf(stderr, "rawSize = %d, rmSize = %d\n", p_oF->m_pct->size(),
        // l_ulMoveIndex->size());
        eraseIndeiesInCloud_(*l_ulMoveIndex, *(p_oF->m_pct));
        // if (c_bDebug_) fprintf(stderr, "newSize = %d\n", p_oF->m_pct->size());

        delete l_ulMoveIndex;
        delete l_growSet;
        indexmmap().swap(*(l_idxRawMap));
    }

    /**
     * @description: 计算这一帧需要删除的栅格，删除栅格内的点
     * @param {keyFrame_Ptr} p_oF
     * @return {*}
     */
    void filterOutliersOneFrame_(keyFrame_Ptr p_oF)
    {
        indexmmap& p_idxmap = *(p_oF->m_idxmap);
        // 异常点所在的栅格集合
        gridSet* p_set = new gridSet();

        if (c_bDebug_)
            getDebugCloud_(p_oF);

        getOutlieGrids_(p_idxmap, *p_set);
        filterOutliersPointsInGrids_(p_oF, *p_set);

        if (c_stCfg_.m_bRawFlt)
            filterOutliersRawPointsNearGrids_(p_oF, *p_set);

        delete p_set;
    }

    /**
     * @description: 从localMap中移除这一帧，同时缩减grid数
     * @param {size_t} p_ulFrameIndex 删除帧号
     * @return {*}
     */
    void removeOldFrame_(size_t p_ulFrameIndex)
    {
        indexmmap& p_idxmap = *(c_localFrames_[p_ulFrameIndex]->m_idxmap);
        mapGridIter gridInfoPair;
        int l_gridCnt;

        for (auto gridIdxPair = p_idxmap.begin(); gridIdxPair != p_idxmap.end();)
        {
            gridInfoPair = c_gridMap_->find(gridIdxPair->first);

            // 如果这一栅格的占用仅来源于当前退出帧，则删除栅格
            l_gridCnt = p_idxmap.count(gridIdxPair->first);
            if (gridInfoPair->second.m_iOcc <= l_gridCnt)
            {
                c_gridMap_->erase(gridInfoPair);
            }
            // 移除当前退出帧造成的占用
            else
            {
                gridInfoPair->second.m_iOcc -= l_gridCnt;
            }
            // 获取下一组第一个键值对的iter
            gridIdxPair = p_idxmap.upper_bound(gridIdxPair->first);
        }
        c_localFrames_.erase(p_ulFrameIndex);
    }

    /**
     * @description: 一个点加入debug点云
     * @param {*}
     * @return {*}
     */
    template <typename PointT>
    inline void debugCloudPushPoint_(PointT& p_pds, float h, float s, float v)
    {
        pcl::PointXYZHSV l_pdebug;
        l_pdebug.x = p_pds.x;
        l_pdebug.y = p_pds.y;
        l_pdebug.z = p_pds.z;
        l_pdebug.h = h;
        l_pdebug.s = s;
        l_pdebug.v = v;
        c_lastCloud_->push_back(l_pdebug);
    }
    inline void debugCloudPushGrid_(const st_gridId& p_id, float h, float s, float v)
    {
        pcl::PointXYZHSV l_pdebug;
        l_pdebug.x = float(p_id.i / c_stCfg_.m_fInvLeafSize[0]) + 0.5 * c_stCfg_.m_fLeafSize[0];
        l_pdebug.y = float(p_id.j / c_stCfg_.m_fInvLeafSize[1]) + 0.5 * c_stCfg_.m_fLeafSize[1];
        l_pdebug.z = float(p_id.k / c_stCfg_.m_fInvLeafSize[2]) + 0.5 * c_stCfg_.m_fLeafSize[2];
        l_pdebug.h = h;
        l_pdebug.s = s;
        l_pdebug.v = v;
        c_lastCloud_->push_back(l_pdebug);
    }
    inline void eraseCloudPushGrid_(const st_gridId& p_id)
    {
        pcl::PointXYZ l_pdebug;
        l_pdebug.x = float(p_id.i / c_stCfg_.m_fInvLeafSize[0]) + 0.5 * c_stCfg_.m_fLeafSize[0];
        l_pdebug.y = float(p_id.j / c_stCfg_.m_fInvLeafSize[1]) + 0.5 * c_stCfg_.m_fLeafSize[1];
        l_pdebug.z = float(p_id.k / c_stCfg_.m_fInvLeafSize[2]) + 0.5 * c_stCfg_.m_fLeafSize[2];
        c_movingPoints_->push_back(l_pdebug);
    }
};
