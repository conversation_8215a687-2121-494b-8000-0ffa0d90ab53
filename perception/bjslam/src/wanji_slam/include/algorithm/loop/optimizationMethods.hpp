/*
 * @Author: your name
 * @Date: 2021-05-19 09:44:28
 * @LastEditTime: 2022-03-24 13:34:21
 * @Chen Junzhi: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /clouserLoop/src/optimizationMethods.hpp
 */
#pragma once

#include <Eigen/Eigen>
#include <pcl/registration/icp.h>

namespace wj_slam {
// 优化方法抽象类，提供优化函数接口
template <typename PointT> class optimization {
  private:
  public:
    typedef boost::shared_ptr<pcl::PointCloud<PointT>> PCRaw_PTR;

    virtual ~optimization(){};

    /**
     * @description: 匹配和估计两个点云之间的位姿转移
     * @param {PCRaw_PTR} p_src 匹配src
     * @param {PCRaw_PTR} p_trg 匹配trg
     * @param {s_POSE6D} &p_transform 计算得到的src->trg位姿转移
     * @param p_fVariance 计算误差
     * @return {*}
     */
    virtual bool optimize(PCRaw_PTR p_src,
                          PCRaw_PTR p_trg,
                          Eigen::Quaterniond& p_q,
                          Eigen::Vector3d& p_t,
                          float (&p_fVariance)[6]) = 0;
};

// 匹配优化方法icp
template <typename PointT> class icpOptimization : public optimization<PointT> {
  private:
    typedef typename optimization<PointT>::PCRaw_PTR PCRaw_PTR_;

    struct st_ICPCfg
    {
        float m_fMaxCorrespondenceDistance;  // icp最大匹配距离
        float m_fMaximumIterations;          // icp最大迭代次数
        float m_fTransformationEpsilon;      // icp单次迭代最大差异
        float m_fEuclideanFitnessEpsilon;    // icp迭代结束方差
        u_int m_uiRANSACIterations;          // icp-RANSAC最大次数
        float m_fMaxFitnessScore;            // 认为匹配成功的最差得分（越大越差）
    } c_stIcpCfg_;

    pcl::IterativeClosestPoint<PointT, PointT, double> icp;

  public:
    icpOptimization() : optimization<PointT>()
    {
        c_stIcpCfg_.m_fMaxCorrespondenceDistance = 0.5;
        c_stIcpCfg_.m_fMaximumIterations = 500;
        c_stIcpCfg_.m_fTransformationEpsilon = 1e-10;
        c_stIcpCfg_.m_fEuclideanFitnessEpsilon = 1e-6;
        c_stIcpCfg_.m_uiRANSACIterations = 10;
        c_stIcpCfg_.m_fMaxFitnessScore = 0.05;

        icp.setMaxCorrespondenceDistance(c_stIcpCfg_.m_fMaxCorrespondenceDistance);
        icp.setMaximumIterations(c_stIcpCfg_.m_fMaximumIterations);
        icp.setTransformationEpsilon(c_stIcpCfg_.m_fTransformationEpsilon);
        icp.setEuclideanFitnessEpsilon(c_stIcpCfg_.m_fEuclideanFitnessEpsilon);
        icp.setRANSACIterations(c_stIcpCfg_.m_uiRANSACIterations);
    }

    ~icpOptimization(){};

    void toPoseStruct_(Eigen::Quaterniond& p_q, Eigen::Vector3d& p_t, Eigen::Matrix4d p_Transform)
    {
        p_q = p_Transform.block<3, 3>(0, 0);
        p_t = p_Transform.block<3, 1>(0, 3);
    }

    void visualization(PCRaw_PTR_ unused_result)
    {
        // pcl::visualization::PCLVisualizer::Ptr viewer(
        //     new pcl::visualization::PCLVisualizer("2D Viewer"));
        // viewer->setBackgroundColor(0, 0, 0);
        // viewer->initCameraParameters();
        // viewer->setCameraPosition(0, 0, 10, 0, 0, 0);
        // viewer->addCoordinateSystem(1.0);
        // viewer->addPointCloud<PointT>(unused_result, "icp cloud");
        // while (!viewer->wasStopped())
        // {
        //     viewer->spinOnce();
        // }
    }

    /**
     * @description: 使用icp匹配和估计两个点云之间的位姿转移
     * @param {PCRaw_PTR} p_src 匹配src
     * @param {PCRaw_PTR} p_trg 匹配trg
     * @param {s_POSE6D} &p_transform 计算得到的src->trg位姿转移
     * @param p_fVariance 待填充误差
     * @return {*}
     */
    bool optimize(PCRaw_PTR_ p_src,
                  PCRaw_PTR_ p_trg,
                  Eigen::Quaterniond& p_q,
                  Eigen::Vector3d& p_t,
                  float (&p_fVariance)[6])
    {
        // Align clouds
        icp.setInputSource(p_src);
        icp.setInputTarget(p_trg);
        PCRaw_PTR_ unused_result(new pcl::PointCloud<PointT>());
        icp.align(*unused_result);

        // 可视化
        // *unused_result += p_trg;
        // visualization(unused_result);

        float l_fFitnessScore = icp.getFitnessScore();
        if (icp.hasConverged() == false || l_fFitnessScore > c_stIcpCfg_.m_fMaxFitnessScore)
        {
            //     fprintf(stderr, "[closureLoop]this loop is not converged with
            //     %f.\n",l_fFitnessScore);
            return false;
        }
        else
        {
            //     fprintf(stderr, "[closureLoop]this loop is converged with %f.\n",
            //     l_fFitnessScore);

            toPoseStruct_(p_q, p_t, icp.getFinalTransformation().matrix());  //.cast<double>()

            for (float& var : p_fVariance)
                var = 0.001;

            return true;
        }
    }
};

}  // namespace wj_slam
