/**
 * @file placeRecongnize.h
 * <AUTHOR> (chenjun<PERSON>@wanji.net.cn)
 * @brief 基于描述符的位置识别模块
 * @version 1.0
 * @date 2022-08-25
 *
 * @copyright Copyright (c) 2022 <PERSON><PERSON>
 */
#pragma once
#include <Eigen/Dense>
#include <boost/shared_ptr.hpp>

namespace wj_slam {
/**
 * @class PlaceRecongnize placeRecongnize.h "placeRecongnize.h"
 * @brief 计算点云的ScanContext特征描述子，计算不同点云的ScanContext描述子之间的相似度（1-dist）
 * \ingroup LoopCheck
 * @tparam P 输出到图管理器的帧的点类型,默认值为pcl::PointXYZ
 */
class PlaceRecongnize {
  public:
    typedef boost::shared_ptr<PlaceRecongnize> Ptr; /**< 类指针别名 */

  private:
    // loop thres
    static constexpr double SEARCH_RATIO =
        0.1;  // for fast comparison, no Brute-force, but search 10 % is okay.
              // // not was in the original conf paper, but improved ver.

  public:
    /**
     * @brief 计算Scan Context描述子之间相关性
     * @todo 请开发人员填写
     * @param[in] _sc1 描述子1
     * @param[in] _sc2 描述子2
     * @return [std::pair<double, int>] [输出得分]
     */
    std::pair<double, int> distanceBtnScanContext(Eigen::MatrixXd& _sc1, Eigen::MatrixXd& _sc2);

  private:
    /**
     * @brief 保存特征描述子的列均值，用于快速搜索
     * @todo 请开发人员填写
     * @param[in] _desc 描述子
     * @return [Eigen::MatrixXd] [特征向量]
     */
    Eigen::MatrixXd makeSectorkeyFromScancontext(Eigen::MatrixXd& _desc);

    /**
     * @brief 部分的特征描述子对存在相对偏移（前后移动），调整相对偏移量，差的范数作为评价误差，获得最小误差下的相对偏移量
     * @todo 请开发人员填写
     * @param[in] _vkey1
     * @param[in] _vkey2
     * @return [int] [未知]
     */
    int fastAlignUsingVkey(Eigen::MatrixXd& _vkey1, Eigen::MatrixXd& _vkey2);

    /**
     * @brief     将Scan Context特征描述子朝右侧平移
     * @todo 请开发人员填写
     * @param _mat
     * @param _num_shift
     * @return [Eigen::MatrixXd] [details]
     */
    Eigen::MatrixXd circshift(Eigen::MatrixXd& _mat, int _num_shift);

    /**
     * @brief 计算所有列的余弦相似度并求和，作为Scan Context的距离，其相似度为 （1-距离）
     * @todo 请开发人员填写
     * @param _sc1
     * @param _sc2
     * @return [double] [details]
     */
    double distDirectSC(Eigen::MatrixXd& _sc1, Eigen::MatrixXd& _sc2);
};
}  // namespace wj_slam