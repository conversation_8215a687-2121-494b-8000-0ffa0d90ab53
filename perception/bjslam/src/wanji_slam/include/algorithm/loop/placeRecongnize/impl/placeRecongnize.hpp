
#pragma once

#include "../placeRecongnize.h"

namespace wj_slam {

double PlaceRecongnize::distDirectSC(Eigen::MatrixXd& _sc1, Eigen::MatrixXd& _sc2)
{
    int num_eff_cols = 0;  // i.e., to exclude all-nonzero sector
    double sum_sector_similarity = 0;
    for (int col_idx = 0; col_idx < _sc1.cols(); col_idx++)
    {
        // 获取Scan Context的列
        Eigen::VectorXd col_sc1 = _sc1.col(col_idx);
        Eigen::VectorXd col_sc2 = _sc2.col(col_idx);

        if (col_sc1.norm() == 0 | col_sc2.norm() == 0)
            continue;  // don't count this sector pair.
        // 计算余弦相似度
        double sector_similarity = col_sc1.dot(col_sc2) / (col_sc1.norm() * col_sc2.norm());

        // 累计求和
        sum_sector_similarity = sum_sector_similarity + sector_similarity;
        num_eff_cols = num_eff_cols + 1;
    }

    double sc_sim = sum_sector_similarity / num_eff_cols;
    return 1.0 - sc_sim;
}

Eigen::MatrixXd PlaceRecongnize::makeSectorkeyFromScancontext(Eigen::MatrixXd& _desc)
{
    Eigen::MatrixXd variant_key(1, _desc.cols());
    for (int col_idx = 0; col_idx < _desc.cols(); col_idx++)
    {
        //  求列均值并保存
        Eigen::MatrixXd curr_col = _desc.col(col_idx);
        variant_key(0, col_idx) = curr_col.mean();
    }

    return variant_key;
}

int PlaceRecongnize::fastAlignUsingVkey(Eigen::MatrixXd& _vkey1, Eigen::MatrixXd& _vkey2)
{
    int argmin_vkey_shift = 0;
    double min_veky_diff_norm = 10000000;
    for (int shift_idx = 0; shift_idx < _vkey1.cols(); shift_idx++)
    {
        Eigen::MatrixXd vkey2_shifted = circshift(_vkey2, shift_idx);

        // 作差后，求F范数
        Eigen::MatrixXd vkey_diff = _vkey1 - vkey2_shifted;
        double cur_diff_norm = vkey_diff.norm();

        // 保留F范数最小时的偏移量
        if (cur_diff_norm < min_veky_diff_norm)
        {
            argmin_vkey_shift = shift_idx;
            min_veky_diff_norm = cur_diff_norm;
        }
    }

    return argmin_vkey_shift;
}

Eigen::MatrixXd PlaceRecongnize::circshift(Eigen::MatrixXd& _mat, int _num_shift)
{
    assert(_num_shift >= 0);

    if (_num_shift == 0)
    {
        Eigen::MatrixXd shifted_mat(_mat);
        return shifted_mat;  // Early return
    }

    Eigen::MatrixXd shifted_mat = Eigen::MatrixXd::Zero(_mat.rows(), _mat.cols());
    for (int col_idx = 0; col_idx < _mat.cols(); col_idx++)
    {
        // 将sc描述子按列添加偏移量重新保存
        int new_location = (col_idx + _num_shift) % _mat.cols();
        shifted_mat.col(new_location) = _mat.col(col_idx);
    }

    return shifted_mat;
}

std::pair<double, int> PlaceRecongnize::distanceBtnScanContext(Eigen::MatrixXd& _sc1,
                                                               Eigen::MatrixXd& _sc2)
{
    //  获取SC描述符列均值描述符
    Eigen::MatrixXd vkey_sc1 = makeSectorkeyFromScancontext(_sc1);
    Eigen::MatrixXd vkey_sc2 = makeSectorkeyFromScancontext(_sc2);
    //  通过SC列均值描述符确定最小偏移量的值
    int argmin_vkey_shift = fastAlignUsingVkey(vkey_sc1, vkey_sc2);

    const int SEARCH_RADIUS = round(0.5 * SEARCH_RATIO * _sc1.cols());  // a half of search range
    std::vector<int> shift_idx_search_space{argmin_vkey_shift};
    //  以最小偏移量构造多个范围内的可能偏移值
    for (int ii = 1; ii < SEARCH_RADIUS + 1; ii++)
    {
        shift_idx_search_space.push_back((argmin_vkey_shift + ii + _sc1.cols()) % _sc1.cols());
        shift_idx_search_space.push_back((argmin_vkey_shift - ii + _sc1.cols()) % _sc1.cols());
    }
    std::sort(shift_idx_search_space.begin(), shift_idx_search_space.end());

    // 根据前的可能的偏移值，调整SC描述符的相对偏移量。
    int argmin_shift = 0;
    double min_sc_dist = 10000000;
    for (int num_shift : shift_idx_search_space)
    {
        Eigen::MatrixXd sc2_shifted = circshift(_sc2, num_shift);
        // 调整后，计算距离，保存最小距离下的偏移量和距离
        double cur_sc_dist = distDirectSC(_sc1, sc2_shifted);
        if (cur_sc_dist < min_sc_dist)
        {
            argmin_shift = num_shift;
            min_sc_dist = cur_sc_dist;
        }
    }

    return std::make_pair(min_sc_dist, argmin_shift);
}
}  // namespace wj_slam