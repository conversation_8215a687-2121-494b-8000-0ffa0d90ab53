/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON>
 * @Date: 2021-05-17 11:02:01
 * @LastEditors: zushuang
 * @LastEditTime: 2021-10-25 13:34:44
 */

/***********************usecases******************
// 1.构造：
    clousureLoop<pcl::PointXYZ> cl;  //使用基本点类型进行构造
// 2.更新位姿图：
    // 每一个关键帧更新位姿图
    cl.renewPoseGraph(帧号,位姿,误差); //误差必须设置，且为(0,1)
// 3.闭环检测
    // 在submap或其他模块检测到位姿闭环（src帧<->trg帧）
// 4.闭环匹配
    // 设置src帧和trg帧
    cl.setInputSource(src帧号,src位姿，src特征点云1，src特征点云2);
    cl.setInputTarget(trg帧号,trg位姿，trg特征点云1，trg特征点云2);
    // 尝试匹配，如果成功返回true
    if(cl.align())
    {
        // 其他动作
    }
// 5.修正闭环
    // 重复使用函数，修正从align成功的src帧开始到最新帧（已经renewPoseGraph的）的全部点云
    // 新位姿为空结构体，新点云1，新点云2，新点云3为空指针，将返回填好的位姿和点云指针。
    // 原始点云3可填入空指针，将不矫正点云3
    // 必须同时使用
    cl.setOriKeyFrame(帧号，原始位姿q，原始位姿t，原始点云1，原始点云2，原始点云3);
    cl.getRenewKeyFrame(新位姿q，新位姿t，新点云1，新点云2，新点云3);
 ************************************************/
/***********************timetest******************
 0.测试条件：release编译，7楼办公室地图一共249帧，闭环匹配成功7次，修正点云约1500次
 1.renewPoseGraph平均耗时0.02ms
 2.align平均耗时35ms(最大初始误差约50cm)
 3.renewKeyFrame：使用单核平均耗时2.7ms，使用2核平均耗时1.4s
 4.内存消耗：测试程序总消耗内存12.7MB，其中外部地图消耗8.4MB,则闭环类实际消耗4.3MB
 ************************************************/
/***********************optimization******************
    // 当前使用icpOptimization类进行优化，若要添加新的优化方式：
    // 加载icp优化类
    opt = new icpOptimization<PT>();

 ************************************************/

#pragma once

// #include "common_ex.h"
#include "optimizationMethods.hpp"
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>

// based on gtsam
#include <gtsam/geometry/Pose3.h>
#include <gtsam/geometry/Rot3.h>
#include <gtsam/nonlinear/ISAM2.h>
#include <gtsam/nonlinear/Values.h>

namespace wj_slam {
template <typename PT = pcl::PointXYZ> class clousureLoop {
  private:
    // typedef pcl::PointXYZ PT;
    typedef pcl::PointCloud<PT> PointCloudRaw;
    typedef boost::shared_ptr<pcl::PointCloud<PT>> PCRaw_PTR;

    // debug模式
    bool c_bDebug_;
    // 设置参数
    struct st_Cfg
    {
    } c_stCfg_;

    typedef struct st_Pose
    {
        Eigen::Quaterniond m_quat;
        Eigen::Vector3d m_trans;
    } st_Pose;

    typedef struct st_input
    {
        PCRaw_PTR m_PCfirst;
        PCRaw_PTR m_PCsecond;
        PCRaw_PTR m_PCRaw;
        st_Pose m_stPose;
        int m_iID;
        void reset()
        {
            m_PCfirst.reset(new PointCloudRaw());
            m_PCsecond.reset(new PointCloudRaw());
            m_PCRaw.reset(new PointCloudRaw());
        }
    } st_input;

    // source 指当前新的帧（里程或时间意义上）
    st_input c_src_;
    // target 指过去旧的帧（里程或时间意义上）
    st_input c_trg_;

    // 当前要处理的帧
    st_input c_renewOri_;

    // isam bayes-tree
    gtsam::ISAM2* c_isam_;
    gtsam::Values c_isamEstimate_;

    optimization<PT>* opt;

  public:
    clousureLoop();
    ~clousureLoop();
    /**
     * @description: 更新位姿图
     * @param {int} p_iFrameID 帧号
     * @param {st_Pose} &p_newFramePose 位姿
     * @param {float} p_fVariance 该帧方差
     * @return {*}
     */
    void renewPoseGraph(int p_iFrameID,
                        Eigen::Quaterniond p_quat,
                        Eigen::Vector3d p_trans,
                        float p_fVariance[6]);

    /**
     * @description: 设置闭环匹配src点云（全局坐标系）和位姿
     * @param {int} p_iFrameID 帧号
     * @param {PCRaw_PTR} p_ori1 特征点云1
     * @param {PCRaw_PTR} p_ori2 特征点云2
     * @return {*}
     */
    void setInputSource(int p_iFrameID,
                        Eigen::Quaterniond p_quat,
                        Eigen::Vector3d p_trans,
                        PCRaw_PTR p_ori1,
                        PCRaw_PTR p_ori2);

    /**
     * @description: 设置闭环匹配目标点云（全局坐标系）和位姿
     * @param {int} p_iFrameID 帧号
     * @param {PCRaw_PTR} p_ori1 特征点云1
     * @param {PCRaw_PTR} p_ori2 特征点云2
     * @return {*}
     */
    void setInputTarget(int p_iFrameID,
                        Eigen::Quaterniond p_quat,
                        Eigen::Vector3d p_trans,
                        PCRaw_PTR p_ori1,
                        PCRaw_PTR p_ori2);

    /**
     * @description: 尝试匹配src和trg并返回匹配结果
     * @param {*}
     * @return true 匹配成功， false 匹配失败
     */
    bool align();

    /**
     * @description: 设置将要更新转移的帧号
     * @param {int} p_iFrameID 帧号
     * @param {Quaterniond} p_oriQuat 原始位姿q
     * @param {Vector3d} p_oriTrans 原始位姿t
     * @param {PCRaw_PTR} p_ori1 原始特征点云1
     * @param {PCRaw_PTR} p_ori2 原始特征点云2
     * @param {PCRaw_PTR} p_ori3 原始点云3（可以为空指针）
     * @return {*}
     */
    void setOriKeyFrame(int p_iFrameID,
                        Eigen::Quaterniond p_oriQuat,
                        Eigen::Vector3d p_oriTrans,
                        PCRaw_PTR p_ori1,
                        PCRaw_PTR p_ori2,
                        PCRaw_PTR p_ori3);
    /**
     * @description: 设置将要更新转移的帧号
     * @param {int} p_iFrameID 帧号
     * @param {Quaterniond} p_oriQuat 原始位姿q
     * @param {Vector3d} p_oriTrans 原始位姿t
     * @param {PCRaw_PTR} p_ori1 原始特征点云1
     * @param {PCRaw_PTR} p_ori2 原始特征点云2
     * @return {*}
     */
    void setOriKeyFrame(int p_iFrameID,
                        Eigen::Quaterniond p_oriQuat,
                        Eigen::Vector3d p_oriTrans,
                        PCRaw_PTR p_ori1,
                        PCRaw_PTR p_ori2);

    /**
     * @description: 更新setOriKeyFrame的帧
     * @param {Quaterniond} p_newQuat 空位姿q，返回新位姿q
     * @param {Vector3d} p_newTrans 空位姿t，返回新位姿t
     * @param {PCRaw_PTR} p_new1 转移的新特征点云1（空指针），返回堆指针
     * @param {PCRaw_PTR} p_new2 转移的新特征点云2（空指针） ，返回堆指针
     * @param {PCRaw_PTR} p_new3 转移的新点云3（空指针） ，返回堆指针
     * @return {*}
     */
    void getRenewKeyFrame(Eigen::Quaterniond& p_newQuat,
                          Eigen::Vector3d& p_newTrans,
                          PCRaw_PTR& p_new1,
                          PCRaw_PTR& p_new2,
                          PCRaw_PTR& p_new3);
    /**
     * @description: 更新setOriKeyFrame的帧
     * @param {Quaterniond} p_newQuat 空位姿q，返回新位姿q
     * @param {Vector3d} p_newTrans 空位姿t，返回新位姿t
     * @param {PCRaw_PTR} p_new1 转移的新特征点云1（空指针），返回堆指针
     * @param {PCRaw_PTR} p_new2 转移的新特征点云2（空指针） ，返回堆指针
     * @return {*}
     */
    void getRenewKeyFrame(Eigen::Quaterniond& p_newQuat,
                          Eigen::Vector3d& p_newTrans,
                          PCRaw_PTR& p_new1,
                          PCRaw_PTR& p_new2);

  private:
    inline Eigen::Quaterniond toQuater_(float roll, float pitch, float yaw)
    {
        const Eigen::AngleAxisd roll_angle(roll, Eigen::Vector3d::UnitX());
        const Eigen::AngleAxisd pitch_angle(pitch, Eigen::Vector3d::UnitY());
        const Eigen::AngleAxisd yaw_angle(yaw, Eigen::Vector3d::UnitZ());
        return (yaw_angle * pitch_angle * roll_angle).normalized();
    }
    inline void toEuler_(const Eigen::Quaterniond& q, float& roll, float& pitch, float& yaw)
    {
        // roll (x-axis rotation)
        double sinr_cosp = +2.0 * (q.w() * q.x() + q.y() * q.z());
        double cosr_cosp = +1.0 - 2.0 * (q.x() * q.x() + q.y() * q.y());
        roll = atan2(sinr_cosp, cosr_cosp);
        // pitch (y-axis rotation)
        double sinp = +2.0 * (q.w() * q.y() - q.z() * q.x());
        if (fabs(sinp) >= 1)
            pitch = copysign(M_PI / 2, sinp);  // use 90 degrees if out of range
        else
            pitch = asin(sinp);
        // yaw (z-axis rotation)
        double siny_cosp = +2.0 * (q.w() * q.z() + q.x() * q.y());
        double cosy_cosp = +1.0 - 2.0 * (q.y() * q.y() + q.z() * q.z());
        yaw = atan2(siny_cosp, cosy_cosp);
    }
    inline gtsam::Pose3 getPose3_(float (&pose6D)[6])
    {
        std::cout<<"find bug: closureLoopNew.h getPose3_" << std::endl;
        return gtsam::Pose3(gtsam::Rot3::RzRyRx(pose6D[3], pose6D[4], pose6D[5]),
                            gtsam::Point3(pose6D[0], pose6D[1], pose6D[2]));
    }
    inline gtsam::Pose3 getPose3_(Eigen::Quaterniond& p_quat, Eigen::Vector3d& p_trans)
    {
        float rpy[3];
        toEuler_(p_quat, rpy[0], rpy[1], rpy[2]);
        std::cout<<"find bug: closureLoopNew.h getPose3_2" << std::endl;
        return gtsam::Pose3(gtsam::Rot3::RzRyRx(rpy[0], rpy[1], rpy[2]),
                            gtsam::Point3(p_trans[0], p_trans[1], p_trans[2]));
    }
    inline void toPoseStruct_(st_Pose& p_sPose6D, float (&p_afPose6D)[6])
    {
        p_sPose6D.m_quat = toQuater_(p_afPose6D[3], p_afPose6D[4], p_afPose6D[5]);
        p_sPose6D.m_trans.x() = p_afPose6D[0];
        p_sPose6D.m_trans.y() = p_afPose6D[1];
        p_sPose6D.m_trans.z() = p_afPose6D[2];
    }
    inline void toPoseStruct_(st_Pose& p_sPose6D, Eigen::Matrix4d p_Transform)
    {
        p_sPose6D.m_quat = p_Transform.block<3, 3>(0, 0);
        p_sPose6D.m_trans = p_Transform.block<3, 1>(0, 3);
    }
    inline void toPoseArray_(st_Pose& p_sPose6D, float (&p_afPose6D)[6])
    {
        p_afPose6D[0] = p_sPose6D.m_trans.x();
        p_afPose6D[1] = p_sPose6D.m_trans.y();
        p_afPose6D[2] = p_sPose6D.m_trans.z();
        toEuler_(p_sPose6D.m_quat, p_afPose6D[3], p_afPose6D[4], p_afPose6D[5]);
    }

    /**
     * @description: 更新位姿图后可以略微优化位姿
     * @param {int} p_iFrameID
     * @param {st_Pose} &p_newFramePose
     * @return {*}
     */
    void calculateEstimate_(int p_iFrameID, st_Pose& p_newFramePose);

    /**
     * @description: 合并特征点云12
     * @param {PCRaw_PTR} p_src1 特征点云1
     * @param {PCRaw_PTR} p_src2 特征点云2
     * @param {PCRaw_PTR} p_trg 输出合并点云
     * @return {*}
     */
    void mergCloud_(PCRaw_PTR p_src1, PCRaw_PTR p_src2, PCRaw_PTR p_trg);

    /**
     * @description: 使用 p_transform 转移 p_stOldTf 到 p_stNewTf
     * @param {st_Pose} &p_stOldTf 旧位姿
     * @param {st_Pose} &p_transform 匹配结果的转移量
     * @param {st_Pose} &p_stNewTf 新位姿
     * @return {*}
     */
    void calcCorrectFramePose_(st_Pose& p_stOldTf, st_Pose& p_transform, st_Pose& p_stNewTf);

    /**
     * @description: 使用新计算的位姿添加约束
     * @param {int} p_iFromID src帧序号
     * @param {int} p_iToID trg帧序号
     * @param {st_Pose} &p_stFromTf src帧位姿（经过优化的）
     * @param {st_Pose} &p_stToTf trg帧位姿
     * @param {float} p_fVariance 携带的误差
     * @return {*}
     */
    void addConstraintToGraph_(int p_iFromID,
                               int p_iToID,
                               st_Pose& p_stFromTf,
                               st_Pose& p_stToTf,
                               float p_fVariance[6]);

    /**
     * @description: 使用转移量更新点云
     * @param {PCRaw_PTR} p_src 旧点云
     * @param {PCRaw_PTR} p_trg 新点云（输出）
     * @param {st_Pose} &p_stTransform 转移
     * @return {*}
     */
    void transFormCloud(PCRaw_PTR p_src, PCRaw_PTR p_trg, st_Pose& p_stTransform);
    void transFormCloud_MThread(PCRaw_PTR p_src, PCRaw_PTR p_trg, st_Pose& p_stTransform);
};

}  // namespace wj_slam
