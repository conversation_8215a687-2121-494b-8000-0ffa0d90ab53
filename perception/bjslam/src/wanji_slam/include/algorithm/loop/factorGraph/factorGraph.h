/**
 * @file factorGraph.h
 * <AUTHOR> (chenjun<PERSON>@wanji.net.cn)
 * @brief 位姿图管理器
 * @version 1.0
 * @date 2022-08-21
 *
 * @copyright Copyright (c) 2022 <PERSON><PERSON>
 */
#pragma once
#include "wj_log.h"
#include <boost/shared_ptr.hpp>
#include <gtsam/geometry/Pose3.h>
#include <gtsam/geometry/Rot3.h>
#include <gtsam/inference/Key.h>
#include <gtsam/inference/Symbol.h>
#include <gtsam/navigation/GPSFactor.h>
#include <gtsam/nonlinear/ISAM2.h>
#include <gtsam/nonlinear/NonlinearFactorGraph.h>
#include <gtsam/nonlinear/Values.h>
#include <gtsam/sam/BearingRangeFactor.h>
#include <gtsam/slam/BetweenFactor.h>
#include <gtsam/slam/PriorFactor.h>
#include <mutex>

namespace wj_slam {
/**
 * @class FactorGraph factorGraph.h "factorGraph.h"
 * @brief 位姿图管理器
 * \ingroup globalMapManager
 */
class FactorGraph {
  public:
    typedef boost::shared_ptr<FactorGraph> Ptr; /**< 类指针别名 */

    enum NodeType { Pose, GPS, Mark };

  private:
    std::mutex lockdata; /**< 锁,防止同时触发 */

    gtsam::ISAM2* c_isam_; /**< isam bayes-tree */
    gtsam::Values c_isamEstimate_;
    gtsam::NonlinearFactorGraph c_graphFactors_; /**< 位姿图 */
    gtsam::Values c_graphValues_; /**< initialEstimate是加到系统中的新变量的初始点 */

  public:
    /**
     * @brief Construct a new Pose Graph object
     *
     */
    FactorGraph();

    /**
     * @brief Destroy the Pose Graph object
     *
     */
    ~FactorGraph();

    /**
     * @brief 设置起点位姿
     *
     * @param[in] p_iFrameID 帧号
     * @param[in] p_quat 位姿
     * @param[in] p_trans 位姿
     * @param[in] p_fVariance 方差
     */
    void setInitPoseFactor(int p_iFrameID,
                           NodeType p_type,
                           Eigen::Quaterniond p_quat,
                           Eigen::Vector3d p_trans,
                           float (&p_fVariance)[6]);

    /**
     * @brief 添加新约束到位姿图
     * @attention 当前版本适用方法
     * @param[in] p_iFrameID1 From帧号
     * @param[in] p_iFrameID2 To帧号
     * @param[in] p_quat 位姿
     * @param[in] p_trans 位姿
     * @param[in] p_fVariance 方差
     */
    void addGraphConstraint(int p_iFrameID1,
                            NodeType p_type1,
                            Eigen::Quaterniond p_quat1,
                            Eigen::Vector3d p_trans1,
                            int p_iFrameID2,
                            NodeType p_type2,
                            Eigen::Quaterniond p_quat2,
                            Eigen::Vector3d p_trans2,
                            float (&p_fVariance)[6]);

    /**
     * @brief Set the Index Pose object
     *
     * @param[in] p_iFrameID 帧号
     * @param[in] p_quat 位姿
     * @param[in] p_trans 位姿
     */
    void setIndexPose(int p_iFrameID,
                      NodeType p_type,
                      Eigen::Quaterniond p_quat,
                      Eigen::Vector3d p_trans);

    /**
     * @brief Get the Index Pose object
     *
     * @param[in] p_iFrameID 帧号
     * @param[out] p_Quat 位姿
     * @param[out] p_Trans 位姿
     */
    void getIndexPose(int p_iFrameID,
                      NodeType p_type,
                      Eigen::Quaterniond& p_Quat,
                      Eigen::Vector3d& p_Trans);

    /**
     * @brief 更新位姿图
     *
     */
    void update();

    /**
     * @brief Get the Factor Graph object
     * 
     * @return [gtsam::VariableIndex] [Key&Factor]
     */
    gtsam::VariableIndex getFactorGraph();

    /**
     * @brief 保存因子图
     * 
     * @param filename
     */
    void saveGraph(const std::string filename);

    /**
     * @brief 载入因子图
     * 
     * @param filename
     */
    void loadGraph(const std::string filename);
    /**
     * @brief 从gtsam获取因子图相关数据
     * @param graph
     * @param estimate
     *
     */
    void getGraph(gtsam::NonlinearFactorGraph& graph, gtsam::Values& estimate)
    {
        c_isam_->update(c_graphFactors_,
                        c_graphValues_);  //可能重新更新，不确定优化增量大小。此处更新的优化增量没有
        c_isam_->update();
        c_graphFactors_.resize(0);
        c_graphValues_.clear();
        graph = c_isam_->getFactorsUnsafe();
        estimate = c_isam_->calculateEstimate();
    }

  private:
    /**
     * @brief 欧拉角转四元数
     *
     * @param[in] roll
     * @param[in] pitch
     * @param[in] yaw
     * @return [Eigen::Quaterniond] [四元数]
     */
    Eigen::Quaterniond toQuater_(float roll, float pitch, float yaw);

    /**
     * @brief 四元数转欧拉角
     *
     * @param[in] q 四元数
     * @param[out] roll
     * @param[out] pitch
     * @param[out] yaw
     */
    void toEuler_(const Eigen::Quaterniond& q, float& roll, float& pitch, float& yaw);

    /**
     * @brief Get the Pose3_ object
     *
     * @param p_quat 位姿
     * @param p_trans 位姿
     * @return [gtsam::Pose3] [Pose3形式位姿]
     */
    gtsam::Pose3 toPose3_(Eigen::Quaterniond& p_quat, Eigen::Vector3d& p_trans);

    /**
     * @brief SE3形式位姿转四元数形式位姿
     *
     * @param[out] p_quat 四元数
     * @param[out] p_trans 位置
     * @param[in] p_Transform SE3形式位姿
     */
    void toPoseStruct_(Eigen::Quaterniond& p_quat,
                       Eigen::Vector3d& p_trans,
                       Eigen::Matrix4d p_Transform);

    /**
     * @brief 生成Key
     * 
     * @param p_iFrameID
     * @param p_type
     * @return [gtsam::Key] [details]
     */
    gtsam::Key makeKey_(int p_iFrameID, NodeType p_type);

    /**
     * @brief 生成6维度噪声
     * 
     * @param na 前三维度：旋转分量
     * @param nb 后三维度：平移分量
     * @return [noiseD_Ptr] [details]
     */
    gtsam::noiseModel::Diagonal::shared_ptr makeSigmas6D_(float (&v)[6], int p_iUseD = 6);
};

}  // namespace wj_slam
