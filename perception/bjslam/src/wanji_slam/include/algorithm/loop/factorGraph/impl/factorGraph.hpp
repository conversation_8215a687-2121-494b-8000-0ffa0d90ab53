/**
 * @file factorGraph.hpp
 * <AUTHOR> (<EMAIL>)
 * @brief 位姿图管理器实现
 * @version 1.0
 * @date 2022-08-21
 *
 * @copyright Copyright (c) 2022 <PERSON><PERSON>
 */
#include "../factorGraph.h"
#include "./GraphIO.hpp"

#include <gtsam/inference/Symbol.h>
#include <fstream>
namespace wj_slam {

FactorGraph::FactorGraph()
{
    // 初始化isam
    gtsam::ISAM2Params isam_parameters;
    // 设置重新线性化阈值和跳过次数
    isam_parameters.relinearizeThreshold = 0.01;  // 示例阈值，请根据问题进行调整
    c_isam_ = new gtsam::ISAM2(isam_parameters);
}
FactorGraph::~FactorGraph()
{
    delete c_isam_;
    std::ofstream os("/media/wanji/data/autoDriving/HongQi2/autodriving0928/src/perception/bjslam/src/comm_tcp/outputMapping/slamfactor.dot");
    c_graphFactors_.saveGrap<PERSON>(os, c_graphValues_);
    std::string commandTrnas = "fdp /media/wanji/data/autoDriving/HongQi2/autodriving0928/src/perception/bjslam/src/comm_tcp/outputMapping/slamfactor.dot -Tpdf > /media/wanji/data/autoDriving/HongQi2/autodriving0928/src/perception/bjslam/src/comm_tcp/outputMapping/slamfactor.pdf";
    system(commandTrnas.c_str());
}

void FactorGraph::setInitPoseFactor(int p_iFrameID,
                                    NodeType p_type,
                                    Eigen::Quaterniond p_quat,
                                    Eigen::Vector3d p_trans,
                                    float (&p_fVariance)[6])
{
    std::lock_guard<std::mutex> lock(lockdata);
    switch (p_type)
    {
        case NodeType::Pose:
        {
            std::cout<<"find bug: factorGraph.hpp setInitPoseFactor" << std::endl;
            c_graphFactors_.add(gtsam::PriorFactor<gtsam::Pose3>(makeKey_(p_iFrameID, p_type),
                                                                 toPose3_(p_quat, p_trans),
                                                                 makeSigmas6D_(p_fVariance)));
            LOGM(WDEBUG,
                 "{} [factor graph] init PoseFactor at X{}",
                 WJLog::getWholeSysTime(),
                 p_iFrameID);
            break;
        }
        case NodeType::GPS:
        {
            std::cout <<"gps setInitPoseFactor: p_iFrameID = " << p_iFrameID << std::endl;
            gtsam::Key keytemp = makeKey_(p_iFrameID, p_type);
            gtsam::Symbol symbol(keytemp);
            std::cout << "Key: " << symbol << std::endl;
            // c_graphFactors_.add(
            //     gtsam::PriorFactor<gtsam::Point3>(makeKey_(p_iFrameID, p_type),
            //                      gtsam::Point3(p_trans.x(), p_trans.y(), p_trans.z()),
            //                      makeSigmas6D_(p_fVariance, 3)));

            // c_graphValues_.insert(makeKey_(p_iFrameID, p_type), toPose3_(p_quat, p_trans));
            // GPS因子是绑定到对应帧号上的
            c_graphFactors_.add(
                gtsam::GPSFactor(makeKey_(p_iFrameID, NodeType::Pose),
                                 gtsam::Point3(p_trans.x(), p_trans.y(), p_trans.z()),
                                makeSigmas6D_(p_fVariance, 3)));
            // c_graphValues_.insert(makeKey_(p_iFrameID, p_type), gtsam::Point3(p_trans.x(), p_trans.y(), p_trans.z()));

            LOGM(WDEBUG,
                 "{} [factor graph] init GPSFactor at G{}",
                 WJLog::getWholeSysTime(),
                 p_iFrameID);
            std::cout <<"gps setInitPoseFactor finished " << std::endl;
            break;
        }
        case NodeType::Mark: break;
        default: break;
    }
}

void FactorGraph::addGraphConstraint(int p_iFrameID1,
                                     NodeType p_type1,
                                     Eigen::Quaterniond p_quat1,
                                     Eigen::Vector3d p_trans1,
                                     int p_iFrameID2,
                                     NodeType p_type2,
                                     Eigen::Quaterniond p_quat2,
                                     Eigen::Vector3d p_trans2,
                                     float (&p_fVariance)[6])
{
    std::lock_guard<std::mutex> lock(lockdata);
    switch (p_type2)
    {
        case NodeType::Pose:
        {
            gtsam::Pose3 poseFrom = toPose3_(p_quat1, p_trans1);
            gtsam::Pose3 poseTo = toPose3_(p_quat2, p_trans2);
            c_graphFactors_.add(gtsam::BetweenFactor<gtsam::Pose3>(makeKey_(p_iFrameID1, p_type1),
                                                                   makeKey_(p_iFrameID2, p_type2),
                                                                   poseFrom.between(poseTo),
                                                                   makeSigmas6D_(p_fVariance)));
            break;
        }
        case NodeType::Mark:
        {
            // 帧位姿
            gtsam::Pose3 poseFrom = toPose3_(p_quat1, p_trans1);
            gtsam::Point3 pointTo = gtsam::Point3(p_trans2[0], p_trans2[1], p_trans2[2]);
            auto l_bearing = poseFrom.bearing(pointTo);
            auto l_range = poseFrom.range(pointTo);
            c_graphFactors_.add(gtsam::BearingRangeFactor<gtsam::Pose3, gtsam::Point3>(
                makeKey_(p_iFrameID1, p_type1),
                makeKey_(p_iFrameID2, p_type2),
                l_bearing,
                l_range,
                makeSigmas6D_(p_fVariance, 3)));
            break;
        }
        case NodeType::GPS:
        {
            std::cout <<"gps addGraphConstraint" << std::endl;
            // 不需要GPS-bundle因子，GPS因子已实现Pose-GPS绑定
            break;
        }
        default: break;
    }
}

void FactorGraph::setIndexPose(int p_iFrameID,
                               NodeType p_type,
                               Eigen::Quaterniond p_quat,
                               Eigen::Vector3d p_trans)
{
    std::lock_guard<std::mutex> lock(lockdata);
    switch (p_type)
    {
        case NodeType::Pose:
        {
            gtsam::Pose3 poseTo = toPose3_(p_quat, p_trans);
            c_graphValues_.insert(makeKey_(p_iFrameID, p_type), poseTo);
            LOGM(WDEBUG,
                 "{} [factor graph] add PoseNode X{} Estimate at XYZ=[{:.2f},{:.2f},{:.2f}]",
                 WJLog::getWholeSysTime(),
                 p_iFrameID,
                 p_trans[0],
                 p_trans[1],
                 p_trans[2]);

            break;
        }
        case NodeType::GPS:
        {
            gtsam::Point3 point = gtsam::Point3(p_trans.x(), p_trans.y(), p_trans.z());
            float l_vGPSPositionVar[6] = {0.05, 0.05, 0.1, 0, 0, 0}; 
            float (&p_fVariance)[6] = l_vGPSPositionVar;

            c_graphFactors_.add(
                gtsam::GPSFactor(makeKey_(p_iFrameID, NodeType::Pose),
                                 point,
                                makeSigmas6D_(p_fVariance, 3)));
            LOGM(WDEBUG,
                 "{} [factor graph] add GPSNode G{} Estimate at XYZ=[{:.2f},{:.2f},{:.2f}]",
                 WJLog::getWholeSysTime(),
                 p_iFrameID,
                 p_trans[0],
                 p_trans[1],
                 p_trans[2]);
            break;
        }
        case NodeType::Mark:
        {
            gtsam::Point3 poseTo = gtsam::Point3(p_trans.x(), p_trans.y(), p_trans.z());
            c_graphValues_.insert(makeKey_(p_iFrameID, p_type), poseTo);
            LOGM(WDEBUG,
                 "{} [factor graph] add MarkNode L{} Estimate at XYZ=[{:.2f},{:.2f},{:.2f}]",
                 WJLog::getWholeSysTime(),
                 p_iFrameID,
                 p_trans[0],
                 p_trans[1],
                 p_trans[2]);

            break;
        }
        default: break;
    }
}

void FactorGraph::getIndexPose(int p_iFrameID,
                               NodeType p_type,
                               Eigen::Quaterniond& p_Quat,
                               Eigen::Vector3d& p_Trans)
{
    std::lock_guard<std::mutex> lock(lockdata);
    switch (p_type)
    {
        case NodeType::Pose:
        {
            toPoseStruct_(p_Quat,
                          p_Trans,
                          c_isamEstimate_.at<gtsam::Pose3>(makeKey_(p_iFrameID, p_type)).matrix());
            break;
        }
        case NodeType::GPS:
        {
            std::cout <<"gps getIndexPose: p_iFrameID = " << p_iFrameID << std::endl;
            p_Trans = c_isamEstimate_.at<gtsam::Point3>(makeKey_(p_iFrameID, p_type));
            std::cout <<"gps getIndexPose finished" << std::endl;
            break;
        }
        case NodeType::Mark:
        {
            p_Trans = c_isamEstimate_.at<gtsam::Point3>(makeKey_(p_iFrameID, p_type));
            break;
        }
        default: break;
    }
}

void FactorGraph::update()
{
    std::lock_guard<std::mutex> lock(lockdata);
    // c_graphFactors_.print("GTSAM Graph:\n");
    // c_graphValues_.print("GTSAM values:\n");
    c_isam_->update(c_graphFactors_, c_graphValues_);
    for (size_t l_updateTimes = 0; l_updateTimes < 10; ++l_updateTimes)
        c_isam_->update();
    std::cout<<"finished update,,,,,,,,,,,,,,,,,,,," << std::endl;
    c_graphFactors_.resize(0);
    c_graphValues_.clear();
    c_isamEstimate_ = c_isam_->calculateEstimate();
}

gtsam::VariableIndex FactorGraph::getFactorGraph()
{
    std::lock_guard<std::mutex> lock(lockdata);
    return c_isam_->getVariableIndex();
}

void FactorGraph::saveGraph(const std::string filename)
{
    std::lock_guard<std::mutex> lock(lockdata);
    GraphIO io;
    io.saveGraph(c_isam_->getFactorsUnsafe(), c_isam_->calculateEstimate(), filename);
}
void FactorGraph::loadGraph(const std::string filename)
{
    GraphIO io;
    // gtSAMgraph是新加到系统中的因子
    gtsam::NonlinearFactorGraph gtSAMgraph;
    // initialEstimate是加到系统中的新变量的初始点
    gtsam::Values initialEstimate;
    io.loadGraph(gtSAMgraph, initialEstimate, filename);
    std::lock_guard<std::mutex> lock(lockdata);
    c_isam_->update(gtSAMgraph, initialEstimate);
    c_isam_->update();
    gtSAMgraph.resize(0);
    initialEstimate.clear();
}

Eigen::Quaterniond FactorGraph::toQuater_(float roll, float pitch, float yaw)
{
    const Eigen::AngleAxisd roll_angle(roll, Eigen::Vector3d::UnitX());
    const Eigen::AngleAxisd pitch_angle(pitch, Eigen::Vector3d::UnitY());
    const Eigen::AngleAxisd yaw_angle(yaw, Eigen::Vector3d::UnitZ());
    return (yaw_angle * pitch_angle * roll_angle).normalized();
}
void FactorGraph::toEuler_(const Eigen::Quaterniond& q, float& roll, float& pitch, float& yaw)
{
    // roll (x-axis rotation)
    double sinr_cosp = +2.0 * (q.w() * q.x() + q.y() * q.z());
    double cosr_cosp = +1.0 - 2.0 * (q.x() * q.x() + q.y() * q.y());
    roll = atan2(sinr_cosp, cosr_cosp);
    // pitch (y-axis rotation)
    double sinp = +2.0 * (q.w() * q.y() - q.z() * q.x());
    if (fabs(sinp) >= 1)
        pitch = copysign(M_PI / 2, sinp);  // use 90 degrees if out of range
    else
        pitch = asin(sinp);
    // yaw (z-axis rotation)
    double siny_cosp = +2.0 * (q.w() * q.z() + q.x() * q.y());
    double cosy_cosp = +1.0 - 2.0 * (q.y() * q.y() + q.z() * q.z());
    yaw = atan2(siny_cosp, cosy_cosp);
}

gtsam::Pose3 FactorGraph::toPose3_(Eigen::Quaterniond& p_quat, Eigen::Vector3d& p_trans)
{
    float rpy[3];
    toEuler_(p_quat, rpy[0], rpy[1], rpy[2]);
    return gtsam::Pose3(gtsam::Rot3::RzRyRx(rpy[0], rpy[1], rpy[2]),
                        gtsam::Point3(p_trans[0], p_trans[1], p_trans[2]));
}

void FactorGraph::toPoseStruct_(Eigen::Quaterniond& p_quat,
                                Eigen::Vector3d& p_trans,
                                Eigen::Matrix4d p_Transform)
{
    p_quat = p_Transform.block<3, 3>(0, 0);
    p_trans = p_Transform.block<3, 1>(0, 3);
}

gtsam::Key FactorGraph::makeKey_(int p_iFrameID, NodeType p_type)
{
    switch (p_type)
    {
        case NodeType::Pose: return X(p_iFrameID); break;
        case NodeType::GPS: return G(p_iFrameID); break;
        case NodeType::Mark: return L(p_iFrameID); break;
        default: break;
    }
    return E(0);
}

gtsam::noiseModel::Diagonal::shared_ptr FactorGraph::makeSigmas6D_(float (&v)[6], int p_iUseD)
{
    if (3 == p_iUseD)
        return gtsam::noiseModel::Diagonal::Variances(
            (gtsam::Vector(3) << v[0], v[1], v[2]).finished());
    else
        return gtsam::noiseModel::Diagonal::Variances(
            (gtsam::Vector(6) << v[0], v[1], v[2], v[3], v[4], v[5]).finished());
}

}  // namespace wj_slam