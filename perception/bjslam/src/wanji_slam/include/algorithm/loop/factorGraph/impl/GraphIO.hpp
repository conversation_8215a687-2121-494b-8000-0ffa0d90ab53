/*
 * @Description:
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2022-08-04 17:04:42
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-08-04 18:47:19
 */
#pragma once
#include <fstream>
#include <gtsam/geometry/Pose3.h>
#include <gtsam/geometry/Rot3.h>
#include <gtsam/inference/Key.h>
#include <gtsam/inference/Symbol.h>
#include <gtsam/navigation/GPSFactor.h>
#include <gtsam/nonlinear/ISAM2.h>
#include <gtsam/nonlinear/NonlinearFactorGraph.h>
#include <gtsam/nonlinear/Values.h>
#include <gtsam/sam/BearingRangeFactor.h>
#include <gtsam/slam/BetweenFactor.h>
#include <gtsam/slam/PriorFactor.h>
#include <vector>

using gtsam::symbol_shorthand::B;  // Bias  (ax,ay,az,gx,gy,gz)
using gtsam::symbol_shorthand::E;  // Inserted estimation gtsam::Pose3 (x,y,z,r,p,y)
using gtsam::symbol_shorthand::G;  // GPS pose
using gtsam::symbol_shorthand::L;  // Landmark gtsam::Point3 (x,y,z)
using gtsam::symbol_shorthand::U;  // Unknow
using gtsam::symbol_shorthand::V;  // Vel   (xdot,ydot,zdot)
using gtsam::symbol_shorthand::X;  // gtsam::Pose3 (x,y,z,r,p,y)

namespace wj_slam {

#pragma region friend_ios

// std::ostream& operator<<(std::ostream& is, const gtsam::Point3& t)
// {
//     is << t.x() << " " << t.y() << " " << t.z();
//     return is;
// }
std::ostream& operator<<(std::ostream& is, const gtsam::Quaternion& q)
{
    is << q.x() << " " << q.y() << " " << q.z() << " " << q.w();
    return is;
}
std::ostream& operator<<(std::ostream& is, const gtsam::Matrix3& m)
{
    for (size_t i = 0; i < 3; i++)
        for (size_t j = i; j < 3; j++)
            is << " " << m(i, j);
    return is;
}
std::ostream& operator<<(std::ostream& is, const gtsam::Matrix6& m)
{
    for (size_t i = 0; i < 6; i++)
        for (size_t j = i; j < 6; j++)
            is << " " << m(i, j);
    return is;
}
std::istream& operator>>(std::istream& is, gtsam::Point3& t)
{
    double x, y, z;
    is >> x >> y >> z;
    t = gtsam::Point3(x, y, z);
    return is;
}
std::istream& operator>>(std::istream& is, gtsam::Quaternion& q)
{
    double x, y, z, w;
    is >> x >> y >> z >> w;
    const double norm = sqrt(w * w + x * x + y * y + z * z), f = 1.0 / norm;
    q = gtsam::Quaternion(f * w, f * x, f * y, f * z);
    return is;
}
std::istream& operator>>(std::istream& is, gtsam::Matrix3& m)
{
    for (size_t i = 0; i < 3; i++)
        for (size_t j = i; j < 3; j++)
            is >> m(i, j);
    return is;
}
std::istream& operator>>(std::istream& is, gtsam::Matrix6& m)
{
    for (size_t i = 0; i < 6; i++)
        for (size_t j = i; j < 6; j++)
            is >> m(i, j);
    return is;
}
#pragma endregion

class GraphIO {
  public:
    /* ************************************************************************* */
    void saveGraph(const gtsam::NonlinearFactorGraph& graph,
                   const gtsam::Values& estimate,
                   const std::string& filename)
    {
        std::cout << "saveGraph values.size= " << estimate.size() << " ,factor.size= " << graph.size()
                  << std::endl;
        std::fstream stream(filename.c_str(), std::fstream::out | std::fstream::trunc);

        // Use a lambda here to more easily modify behavior.
        // auto index = [](gtsam::Key key) { return gtsam::Symbol(key).index(); };
        auto index = [](gtsam::Key key) { return gtsam::Symbol(key); };

        std::cout<<"find bug: GraphIO.hpp saveGraph" << std::endl;
        // save 3D poses / landmarks
        for (const auto key_value : estimate)
        {
            if (auto p = dynamic_cast<const gtsam::GenericValue<gtsam::Pose3>*>(&key_value.value))
            {
                const gtsam::Pose3& pose = p->value();
                const gtsam::Point3 t = pose.translation();
                const auto q = pose.rotation().toQuaternion();
                // stream << "VERTEX_SE3 " << index(key_value.key) << " " << t.x() << " " << t.y()
                //        << " " << t.z() << " " << q << std::endl;
                stream << "VERTEX_SE3:QUAT " << index(key_value.key).index() << " " << t.x() << " " << t.y()
                       << " " << t.z() << " " << q << std::endl;
            }
            else if (auto p =
                         dynamic_cast<const gtsam::GenericValue<gtsam::Point3>*>(&key_value.value))
            {
                const gtsam::Point3& point = p->value();
                // stream << "VERTEX_LM3 " << index(key_value.key) << " " << point.x() << " "
                //        << point.y() << " " << point.z() << std::endl;
                stream << "VERTEX_SE3:QUAT " << index(key_value.key).index() << " " << point.x() << " "
                       << point.y() << " " << point.z() << std::endl;
            }
        }
        std::cout<<"find bug: GraphIO.hpp saveGraph 2" << std::endl;

        // save edges (2D or 3D)
        for (const auto& factor_ : graph)
        {
            if (auto factorPR =
                    boost::dynamic_pointer_cast<gtsam::PriorFactor<gtsam::Pose3>>(factor_))
            {
                gtsam::Matrix6 Info = parseNoiseV6_(factorPR->noiseModel());

                stream << "EDGE_SE3:QUAT " << index(factorPR->key()).index() << Info << std::endl;
            }
            else if (auto factor3D =
                         boost::dynamic_pointer_cast<gtsam::BetweenFactor<gtsam::Pose3>>(factor_))
            {
                const gtsam::Pose3 pose3D = factor3D->measured();
                const gtsam::Point3 p = pose3D.translation();
                const auto q = pose3D.rotation().toQuaternion();
                gtsam::Matrix6 Info = parseNoiseV6_(factor3D->noiseModel());

                // stream << "EDGE_SE3 " << index(factor3D->key1()) << " " << index(factor3D->key2())
                //        << " " << p.x() << " " << p.y() << " " << p.z() << " " << q << Info
                //        << std::endl;
                stream << "EDGE_SE3:QUAT " << index(factor3D->key1()).index() << " " << index(factor3D->key2()).index()
                       << " " << p.x() << " " << p.y() << " " << p.z() << " " << q << Info
                       << std::endl;
            }
            else if (auto factorBR = boost::dynamic_pointer_cast<
                         gtsam::BearingRangeFactor<gtsam::Pose3, gtsam::Point3>>(factor_))
            {
                const gtsam::BearingRange<gtsam::Pose3, gtsam::Point3> br = factorBR->measured();
                const gtsam::Point3 bearing = br.bearing().point3();
                const double range = br.range();
                gtsam::Matrix3 Info = parseNoiseV3_(factorBR->noiseModel());

                stream << "EDGE_SE3:QUAT " << index(factorBR->keys()[0]).index() << " "
                       << index(factorBR->keys()[1]).index() << " " << range << " " << bearing.x() << " "
                       << bearing.y() << " " << bearing.z() << Info << std::endl;
            }
            else if (auto factorGPS = boost::dynamic_pointer_cast<gtsam::GPSFactor>(factor_))
            {
                const gtsam::Point3 p = factorGPS->measurementIn();
                gtsam::Matrix3 Info = parseNoiseV3_(factorGPS->noiseModel());

                stream << "EDGE_SE3:QUAT " << index(factorGPS->key()).index() << " " << p.x() << " " << p.y()
                       << " " << p.z() << Info << std::endl;
            }
        }
        std::cout<<"find bug: GraphIO.hpp saveGraph finished" << std::endl;
        stream.close();
    }

    void loadGraph(gtsam::NonlinearFactorGraph& graph,
                   gtsam::Values& estimate,
                   const std::string& filename)
    {
        std::fstream is(filename.c_str());
        std::string tags;
        std::cout<<"find bug: GraphIO.hpp loadGraph" << std::endl;
        while (getline(is, tags))
        {
            std::stringstream isl(tags);
            std::string tag;
            isl >> tag;

            if (auto indexedPose = parseVertexPose3_(isl, tag))
                estimate.insert<gtsam::Pose3>(indexedPose->first, indexedPose->second);
            else if (auto indexedLandmark = parseVertexPoint3_(isl, tag))
                estimate.insert<gtsam::Point3>(indexedLandmark->first, indexedLandmark->second);
            else if (auto factorPrior = parsePriorFactor_(isl, tag, estimate))
                graph.push_back(factorPrior);
            else if (auto factorBetween = parseBetweenFactor_(isl, tag))
                graph.push_back(factorBetween);
            else if (auto factorBearingRange = parseBearingRangeFactor_(isl, tag))
                graph.push_back(factorBearingRange);
            else if (auto factorGPS = parseGPSFactor_(isl, tag))
                graph.push_back(factorGPS);
        }
        std::cout<<"find bug: GraphIO.hpp loadGraph finished" << std::endl;
    }

  private:
    friend std::istream& operator>>(std::istream& is, gtsam::Point3& t);
    friend std::istream& operator>>(std::istream& is, gtsam::Quaternion& q);
    friend std::istream& operator>>(std::istream& is, gtsam::Matrix3& m);
    friend std::istream& operator>>(std::istream& is, gtsam::Matrix6& m);

    gtsam::Key isKey(std::istream& is)
    {
        std::string id;
        is >> id;
        size_t idx = atoi(id.substr(1).c_str());
        switch (id[0])
        {
            case 'x': return X(idx); break;
            case 'l': return L(idx); break;
            case 'e': return E(idx); break;
            default: break;
        }
        return U(0);
    }

    gtsam::Matrix6 parseNoiseV6_(const gtsam::SharedNoiseModel& model)
    {
        boost::shared_ptr<gtsam::noiseModel::Gaussian> gaussianModel =
            boost::dynamic_pointer_cast<gtsam::noiseModel::Gaussian>(model);
        return gaussianModel->R().transpose() * gaussianModel->R();
    }
    gtsam::Matrix3 parseNoiseV3_(const gtsam::SharedNoiseModel& model)
    {
        boost::shared_ptr<gtsam::noiseModel::Gaussian> gaussianModel =
            boost::dynamic_pointer_cast<gtsam::noiseModel::Gaussian>(model);
        return gaussianModel->R().transpose() * gaussianModel->R();
    }

    /* ************************************************************************* */
    boost::optional<std::pair<gtsam::Key, gtsam::Pose3>> parseVertexPose3_(std::istream& is,
                                                                           const std::string& tag)
    {
        if (tag == "VERTEX_SE3")
        {
            gtsam::Key id = isKey(is);
            gtsam::Point3 t;
            gtsam::Quaternion q;
            is >> t >> q;
            return std::make_pair(id, gtsam::Pose3(q, t));
        }
        else
            return boost::none;
    }

    /* ************************************************************************* */
    boost::optional<std::pair<gtsam::Key, gtsam::Point3>> parseVertexPoint3_(std::istream& is,
                                                                             const std::string& tag)
    {
        if (tag == "VERTEX_LM3")
        {
            gtsam::Key id = isKey(is);
            gtsam::Point3 t;
            is >> t;
            return std::make_pair(id, t);
        }
        else
            return boost::none;
    }
    /* ************************************************************************* */
    gtsam::PriorFactor<gtsam::Pose3>::shared_ptr
    parsePriorFactor_(std::istream& is, const std::string& tag, gtsam::Values& estimate)
    {
        if (tag == "EDGE_PRI")
        {
            gtsam::Key id = isKey(is);
            gtsam::Matrix6 m;
            is >> m;
            gtsam::SharedNoiseModel model = gtsam::noiseModel::Gaussian::Information(m);
            std::cout <<"find bug: factorGrah.hpp parsePriorFactor_" << std::endl;   
            gtsam::PriorFactor<gtsam::Pose3>::shared_ptr factor_(
                new gtsam::PriorFactor<gtsam::Pose3>(id,
                                                     estimate.at<gtsam::Pose3>(id),
                                                     gtsam::noiseModel::Gaussian::Information(m)));
            return factor_;
        }
        else
            return nullptr;
    }

    /* ************************************************************************* */
    gtsam::BetweenFactor<gtsam::Pose3>::shared_ptr parseBetweenFactor_(std::istream& is,
                                                                       const std::string& tag)
    {
        if (tag == "EDGE_SE3")
        {
            gtsam::Key id1 = isKey(is);
            gtsam::Key id2 = isKey(is);
            gtsam::Point3 t;
            gtsam::Quaternion q;
            gtsam::Matrix6 m;
            is >> t >> q >> m;
            gtsam::SharedNoiseModel model = gtsam::noiseModel::Gaussian::Information(m);

            gtsam::BetweenFactor<gtsam::Pose3>::shared_ptr factor_(
                new gtsam::BetweenFactor<gtsam::Pose3>(
                    id1, id2, gtsam::Pose3(q, t), gtsam::noiseModel::Gaussian::Information(m)));
            return factor_;
        }
        else
            return nullptr;
    }

    /* ************************************************************************* */
    gtsam::BearingRangeFactor<gtsam::Pose3, gtsam::Point3>::shared_ptr
    parseBearingRangeFactor_(std::istream& is, const std::string& tag)
    {
        if (tag == "EDGE_BR3")
        {
            gtsam::Key id1 = isKey(is);
            gtsam::Key id2 = isKey(is);
            double r;
            gtsam::Point3 t;
            gtsam::Matrix3 m;
            is >> r >> t >> m;
            gtsam::SharedNoiseModel model = gtsam::noiseModel::Gaussian::Information(m);
            gtsam::BearingRangeFactor<gtsam::Pose3, gtsam::Point3>::shared_ptr factor_(
                new gtsam::BearingRangeFactor<gtsam::Pose3, gtsam::Point3>(
                    id1, id2, gtsam::Unit3(t), r, gtsam::noiseModel::Gaussian::Information(m)));
            return factor_;
        }
        else
            return nullptr;
    }

    /* ************************************************************************* */
    gtsam::GPSFactor::shared_ptr parseGPSFactor_(std::istream& is, const std::string& tag)
    {
        if (tag == "EDGE_GP3")
        {
            gtsam::Key id = isKey(is);
            gtsam::Point3 t;
            gtsam::Matrix3 m;
            is >> t >> m;
            gtsam::SharedNoiseModel model = gtsam::noiseModel::Gaussian::Information(m);
            gtsam::GPSFactor::shared_ptr factor_(
                new gtsam::GPSFactor(id, t, gtsam::noiseModel::Gaussian::Information(m)));
            return factor_;
        }
        else
            return nullptr;
    }
};
}  // namespace wj_slam