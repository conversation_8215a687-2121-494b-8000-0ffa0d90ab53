/*
 * @Description: 优化建图保存中间建图过程数据
 * @Version: 1.0
 * @Autor: your name
 * @Date: 2023-04-21 14:38:07
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-04-23 19:40:30
 */
#pragma once
#include "../test/location/impl/debugSave.hpp"
#include "./KfSave.h"
#include "algorithm/loop/factorGraph/impl/GraphIO.hpp"
#include "algorithm/map/sub_map/KeyFrameMap.h"
#include "common/type/type_frame.h"
#include "tool/fileTool/fileTool.h"
#include <fstream>
#include <iostream>
#include <map>
#include <opencv2/highgui/highgui.hpp>
#include <opencv2/imgcodecs.hpp>
#include <pcl/io/pcd_io.h>
#include <pcl/point_cloud.h>
#include <sstream>
namespace wj_slam {
template <typename P = pcl::PointXYZHSV> class MappingStorage {
  public:
    /* data */
  private:
    typedef KEYFRAME<P> MapFrame;
    typedef typename MapFrame::Ptr MapFramePtr;
    typedef pcl::PointCloud<P> PointCloudPos;
    typedef typename PointCloudPos::Ptr PointCloudPosPtr;
#pragma region struct
    int c_iFieldSize_ = 1024 * 1024;
    int c_iVersionMap = 2;
    struct s_FeatureInfo
    {
        s_FeatureInfo()
        {
            m_iPntNum = 0;  //点数目
            // m_iFieldNum = 0;     //点的字段数，这个参数暂时可以不用
            m_iByteNum = 0;      //点的字节数
            m_iFeatureType = 0;  //存储类型
        }
        int m_iPntNum;
        // int m_iFieldNum;
        int m_iByteNum;
        int m_iFeatureType;
    };
    struct s_Header
    {
        int m_iTypeNum;
        std::vector<s_FeatureInfo> m_vFeatureInfo;
    };
    struct st_RtkMap  //假如要扩充其他数据，则直接在rtk类里面向下扩充，可以兼容。
    {
        double m_dLat;           //东北天纬度
        double m_dLon;           //东北天经度
        s_POSE6D m_stRtk2Lidar;  // RTK与lidar的外参
        s_POSE6D m_stAdjust;     //平差参数
        st_RtkMap() : m_dLat(0), m_dLon(0){};
    };
    struct st_Node
    {
        uint32_t m_uiNodeID;
        s_POSE6D m_Pose;
        s_POSE6D m_rtkPose;
        bool m_bIsGpsAdd;
        gtsam::Matrix3 m_vGpsNoise; /**< 初始位姿噪声 */
        // s_POSE6D m_ImuPose;
        // s_POSE6D m_WheelPose;
        st_Node() : m_uiNodeID(0), m_bIsGpsAdd(false){};
    };
    struct st_Line
    {
        uint32_t m_u32FromId;
        uint32_t m_u32ToId;
        bool m_bIsPrior;
        s_POSE6D m_SlamBetweenPose;
        gtsam::Matrix6 m_vNoise; /**< 初始位姿噪声 */
        // s_POSE6D m_ImuBetweenPose;
        // s_POSE6D m_WheelBetweenPose;
        st_Line() : m_u32FromId(0), m_u32ToId(0), m_bIsPrior(false){};
    };

#pragma endregion
    std::vector<st_Node> c_vNode_;      /**< node节点,建图过程数据*/
    std::vector<st_Line> c_vLine_;      /**< 边因子,建图过程数据*/
    std::vector<st_Line> c_vLoop_;      /**< 回环边,建图过程数据*/
    std::vector<st_Node> c_vGraphNode_; /**< node节点,Graph数据*/
    std::vector<st_Line> c_vGraphLine_; /**< 边因子,Graph数据*/
    std::vector<st_Line> c_vGraphLoop_; /**< 回环边,Graph数据*/
    // std::vector<MapFramePtr>& c_vMaps_; /**< 地图队列 */
    boost::shared_ptr<KfMapPair> c_pMapPair_ = nullptr; /**< 关键帧队列管理对象*/
    SYSPARAM* c_stSysParam_;                            /**< 系统参数 */
    st_RtkMap c_stRtkMap_;

  public:
    MappingStorage(boost::shared_ptr<KfMapPair> p_pMapPair) : c_pMapPair_(p_pMapPair)
    {
        c_stSysParam_ = SYSPARAM::getIn();
    }
    ~MappingStorage() {}
    /**
     * @brief 设置先验因子位姿,也是边设置
     * @param p_iFrameID 先验因子ID
     * @param p_CurrPose 先验因子pose
     * @param p_fVariance 噪声
     *
     */
    void setInitPose(int p_iFrameID, s_POSE6D p_CurrPose, float (&p_fVariance)[6])
    {
        st_Line l_temp;
        l_temp.m_u32FromId = p_iFrameID;
        l_temp.m_u32ToId = p_iFrameID;
        l_temp.m_bIsPrior = true;
        l_temp.m_SlamBetweenPose = p_CurrPose;
        l_temp.m_vNoise = makeSigmas6D_(p_fVariance);
        c_vLine_.push_back(l_temp);
    }
    /**
     * @brief 设置节点信息
     * @param p_iFrameID 节点ID
     * @param p_CurrPose 节点pose
     *
     */
    void setIndexPose(int p_iFrameID, s_POSE6D p_CurrPose)
    {
        st_Node l_temp;
        l_temp.m_uiNodeID = p_iFrameID;
        l_temp.m_Pose = p_CurrPose;
        // l_temp.m_rtkPose = c_vMaps_.at(p_iFrameID)->m_sRtkPose.m_Pose;
        // l_temp.m_rtkPose.m_bFlag = c_vMaps_.at(p_iFrameID)->m_sRtkPose.m_bFlag;
        c_vNode_.push_back(l_temp);
    }
    //设置between边,帧间边或者回环边
    /**
     * @brief 设置between边,帧间边或者回环边
     * @param p_u32FromId FromID
     * @param p_u32ToId ToID
     * @param p_betweenPose 帧间增量
     * @param p_fVariance 噪声
     *
     */
    void setGraphConstraint(uint32_t p_u32FromId,
                            uint32_t p_u32ToId,
                            s_POSE6D p_betweenPose,
                            float (&p_fVariance)[6])
    {
        st_Line l_temp;
        l_temp.m_u32FromId = p_u32FromId;
        l_temp.m_u32ToId = p_u32ToId;
        l_temp.m_SlamBetweenPose = p_betweenPose;
        l_temp.m_vNoise = makeSigmas6D_(p_fVariance);
        if (l_temp.m_u32ToId - l_temp.m_u32FromId > 1)
            c_vLoop_.push_back(l_temp);
        else
            c_vLine_.push_back(l_temp);
    }
    /**
     * @brief 设置GPS添加点
     * @param p_iFrameID 添加GPS点的关键帧绑定ID
     * @param p_fVariance Gps噪声
     *
     */
    void setGpsNode(int p_iFrameID, wj_slam::s_POSE6D& p_CurrPose, float (&p_fVariance)[6])
    {
        std::cout<<"mappingStorage.hpp setGpsNode1" << std::endl;
        // st_Node l_temp;
        // l_temp.m_uiNodeID = p_iFrameID;
        // l_temp.m_Pose = p_CurrPose;
        // // l_temp.m_rtkPose = c_vMaps_.at(p_iFrameID)->m_sRtkPose.m_Pose;
        // // l_temp.m_rtkPose.m_bFlag = c_vMaps_.at(p_iFrameID)->m_sRtkPose.m_bFlag;
        // c_vNode_.push_back(l_temp);
        // std::cout<<"mappingStorage.hpp setGpsNode2" << std::endl;
        c_vNode_[c_vNode_.size() - 1].m_bIsGpsAdd = true;
        std::cout<<"mappingStorage.hpp m_bIsGpsAdd = true" << std::endl;
        c_vNode_[c_vNode_.size() - 1].m_vGpsNoise = makeSigmas3D_(p_fVariance);
    }
    /**
     * @brief 保存建图过程数据
     * @param filename 路径
     *
     */
    void saveMapDate(const string& filename)
    {
        writeBinary_(filename, c_vNode_, c_vLine_, c_vLoop_);  //保存数据
    }
    /**
     * @brief 保存garph优化数据
     * @param graph gtsam优化器的量
     * @param estimate gtsam优化器的量
     * @param filename 路径
     *
     */
    void saveGraphDate(const gtsam::NonlinearFactorGraph& graph,
                       const gtsam::Values& estimate,
                       const string& filename)
    {
        graphTrans_(graph, estimate);
        writeBinary_(filename, c_vGraphNode_, c_vGraphLine_, c_vGraphLoop_);
    }
    /**
     * @brief 保存关键帧加密文件
     * @param filename 路径
     *
     */
    void saveKFBinary(const string& filename)
    {
        //合并点云
        MapFramePtr ALLKeyFrame_;
        ALLKeyFrame_.reset(new MapFrame());
        vector<st_KfPointNum> l_VKFnum;                           //单帧的KF点数
        PointCloudPosPtr l_pcKeyFramesPose(new PointCloudPos());  //将关键帧pose存在
        for (int i = 0; i < c_pMapPair_->getKfSize(); i++)
        {
            st_KfPointNum l_kfnum;
            MapPosePair l_mapPair = c_pMapPair_->getKfPairByInd(i);
            *ALLKeyFrame_->m_pFeature->allPC += *(l_mapPair.map->m_pFeature->allPC);
            *ALLKeyFrame_->m_pFeature->first += *(l_mapPair.map->m_pFeature->first);
            *ALLKeyFrame_->m_pFeature->second += *(l_mapPair.map->m_pFeature->second);
            *ALLKeyFrame_->m_pFeature->fourth += *(l_mapPair.map->m_pFeature->fourth);
            l_kfnum.set(l_mapPair.map->m_pFeature->allPC->points.size(),
                        l_mapPair.map->m_pFeature->first->points.size(),
                        l_mapPair.map->m_pFeature->second->points.size(),
                        l_mapPair.map->m_pFeature->fourth->points.size());
            l_VKFnum.push_back(l_kfnum);
            //关键帧位姿拷贝
            P l_pose;
            l_pose.x = l_mapPair.map->m_Pose.x();
            l_pose.y = l_mapPair.map->m_Pose.y();
            l_pose.z = l_mapPair.map->m_Pose.z();
            l_pose.h = l_mapPair.map->m_Pose.roll();
            l_pose.s = l_mapPair.map->m_Pose.pitch();
            l_pose.v = l_mapPair.map->m_Pose.yaw();
            l_pcKeyFramesPose->push_back(l_pose);
        }
        //加密点云数据
        std::vector<typename pcl::PointCloud<P>::Ptr> l_feature;
        l_feature.push_back(ALLKeyFrame_->m_pFeature->first);
        l_feature.push_back(ALLKeyFrame_->m_pFeature->second);
        l_feature.push_back(ALLKeyFrame_->m_pFeature->fourth);
        l_feature.push_back(ALLKeyFrame_->m_pFeature->third);
        std::vector<typename pcl::PointCloud<P>::Ptr> l_pcVisible;
        l_pcVisible.push_back(ALLKeyFrame_->m_pFeature->allPC);

        boost::shared_ptr<KfSave<P, P>> l_pReadWriteMap;
        l_pReadWriteMap.reset(new KfSave<P, P>());
        l_pReadWriteMap->writeBinary(filename, l_VKFnum, l_pcKeyFramesPose, l_feature, l_pcVisible);
    }
    //解密KF加密文件
    void readKFBinary(const std::string& filenameIn, const std::string& filenameOut)
    {
        MapFramePtr ALLKeyFrame_;
        ALLKeyFrame_.reset(new MapFrame());
        std::vector<MapFramePtr> l_vMapFrames_;  //新解密出来的地图关键帧集合
        vector<st_KfPointNum> l_KFnum_;          //单帧的KF点数
        PointCloudPosPtr l_pcKeyFramesPose_(new PointCloudPos());  //将关键帧pose存在
        std::vector<typename pcl::PointCloud<P>::Ptr> l_feature;
        std::vector<typename pcl::PointCloud<P>::Ptr> l_pcVisible;
        l_feature.push_back(ALLKeyFrame_->m_pFeature->first);
        l_feature.push_back(ALLKeyFrame_->m_pFeature->second);
        l_feature.push_back(ALLKeyFrame_->m_pFeature->fourth);
        l_feature.push_back(ALLKeyFrame_->m_pFeature->third);
        l_pcVisible.push_back(ALLKeyFrame_->m_pFeature->allPC);
        boost::shared_ptr<KfSave<P, P>> l_pReadWriteMap;
        l_pReadWriteMap.reset(new KfSave<P, P>());
        l_pReadWriteMap->readBinary(
            filenameIn, l_KFnum_, l_pcKeyFramesPose_, l_feature, l_pcVisible);
        //将点云解密成一帧一帧
        int finum = 0;
        int senum = 0;
        int curbnum = 0;
        int allnum = 0;
        for (int i = 0; i < l_KFnum_.size(); i++)
        {
            MapFramePtr lFrame_;
            lFrame_.reset(new MapFrame());
            st_KfPointNum l_kfnum = l_KFnum_[i];
            lFrame_->m_pFeature->first->width = l_kfnum.m_iFiNum;
            lFrame_->m_pFeature->first->height = 1;
            lFrame_->m_pFeature->first->points.resize(l_kfnum.m_iFiNum);
            lFrame_->m_pFeature->second->width = l_kfnum.m_iSeNum;
            lFrame_->m_pFeature->second->height = 1;
            lFrame_->m_pFeature->second->points.resize(l_kfnum.m_iSeNum);
            lFrame_->m_pFeature->fourth->width = l_kfnum.m_iCurbNum;
            lFrame_->m_pFeature->fourth->height = 1;
            lFrame_->m_pFeature->fourth->points.resize(l_kfnum.m_iCurbNum);
            lFrame_->m_pFeature->allPC->width = l_kfnum.m_iAllNum;
            lFrame_->m_pFeature->allPC->height = 1;
            lFrame_->m_pFeature->allPC->points.resize(l_kfnum.m_iAllNum);
            lFrame_->m_pFeature->m_uiScanFrame = i;
            memcpy(&lFrame_->m_pFeature->first->points[0],
                   &(l_feature[0]->points[finum]),
                   l_kfnum.m_iFiNum * sizeof(P));
            memcpy(&lFrame_->m_pFeature->second->points[0],
                   &(l_feature[1]->points[senum]),
                   l_kfnum.m_iSeNum * sizeof(P));
            memcpy(&lFrame_->m_pFeature->fourth->points[0],
                   &(l_feature[2]->points[curbnum]),
                   l_kfnum.m_iCurbNum * sizeof(P));
            memcpy(&lFrame_->m_pFeature->allPC->points[0],
                   &(l_pcVisible[0]->points[allnum]),
                   l_kfnum.m_iAllNum * sizeof(P));
            lFrame_->m_Pose.setXYZ(l_pcKeyFramesPose_->points[i].x,
                                   l_pcKeyFramesPose_->points[i].y,
                                   l_pcKeyFramesPose_->points[i].z);
            lFrame_->m_Pose.setRPY(l_pcKeyFramesPose_->points[i].h,
                                   l_pcKeyFramesPose_->points[i].s,
                                   l_pcKeyFramesPose_->points[i].v);
            finum += l_kfnum.m_iFiNum;
            senum += l_kfnum.m_iSeNum;
            curbnum += l_kfnum.m_iCurbNum;
            allnum += l_kfnum.m_iAllNum;
            l_vMapFrames_.push_back(lFrame_);
        }
        //保存关键帧
        oprateFile<P> wt(filenameOut);
        for (int i = 0; i < l_vMapFrames_.size(); i++)
            wt.saveOneFrame(l_vMapFrames_.at(i)->m_pFeature, l_vMapFrames_.at(i)->m_Pose);
    }
    /**
     * @brief 解密建图过程或Graph数据
     * @param filenameIn 输入加密文件路径
     * @param filenameOut 输出文档文件路径
     * @param p_isGraph 是否按graph解密数据
     *
     */
    void decryptBinary(const string& filenameIn, const string& filenameOut, bool p_isGraph = false)
    {
        std::vector<st_Node> l_NodeV; /**< 地图关键帧数据 */
        std::vector<st_Line> l_LineV; /**< 边因子 */
        std::vector<st_Line> l_loop;  /**< 回环边 */
        st_RtkMap l_RTKMap;           // RTK建图过程信息
        readBinary_(filenameIn.c_str(), l_NodeV, l_LineV, l_loop, l_RTKMap);
        std::fstream stream(filenameOut.c_str(), std::fstream::out | std::fstream::trunc);
        if (!p_isGraph)
        {
            stream.setf(ios::fixed, ios::floatfield);  // 设定为 fixed 模式，以小数点表示浮点数
            stream.precision(8);
            stream << l_RTKMap.m_dLat << std::endl;
            stream << l_RTKMap.m_dLon << std::endl;
            stream.precision(6);
            stream.unsetf(ios::floatfield);
            stream << l_RTKMap.m_stRtk2Lidar.x() << " " << l_RTKMap.m_stRtk2Lidar.y() << " "
                   << l_RTKMap.m_stRtk2Lidar.z() << " " << l_RTKMap.m_stRtk2Lidar.roll() << " "
                   << l_RTKMap.m_stRtk2Lidar.pitch() << " " << l_RTKMap.m_stRtk2Lidar.yaw()
                   << std::endl;
            stream << l_RTKMap.m_stAdjust.x() << " " << l_RTKMap.m_stAdjust.y() << " "
                   << l_RTKMap.m_stAdjust.z() << " " << l_RTKMap.m_stAdjust.roll() << " "
                   << l_RTKMap.m_stAdjust.pitch() << " " << l_RTKMap.m_stAdjust.yaw() << std::endl;
        }
        for (int i = 0; i < l_NodeV.size(); i++)
        {
            stream << "VERTEX_SE3 " << l_NodeV[i].m_uiNodeID << " " << l_NodeV[i].m_Pose.m_trans.x()
                   << " " << l_NodeV[i].m_Pose.m_trans.y() << " " << l_NodeV[i].m_Pose.m_trans.z()
                   << " " << l_NodeV[i].m_Pose.m_quat << std::endl;
        }
        for (int i = 0; i < l_NodeV.size(); i++)
        {
            if (!p_isGraph || (p_isGraph && l_NodeV[i].m_bIsGpsAdd))
                if (l_NodeV[i].m_rtkPose.m_bFlag == PoseStatus::ContinuePose
                    || l_NodeV[i].m_bIsGpsAdd)
                    stream << "EDGE_GP3 " << l_NodeV[i].m_uiNodeID << " "
                           << l_NodeV[i].m_rtkPose.m_trans.x() << " "
                           << l_NodeV[i].m_rtkPose.m_trans.y() << " "
                           << l_NodeV[i].m_rtkPose.m_trans.z() << " " << l_NodeV[i].m_rtkPose.m_quat
                           << " " << l_NodeV[i].m_vGpsNoise << " " << l_NodeV[i].m_rtkPose.m_bFlag
                           << " " << l_NodeV[i].m_bIsGpsAdd << std::endl;
        }
        for (int i = 0; i < l_LineV.size(); i++)
        {
            if (l_LineV[i].m_bIsPrior)
                stream << "EDGE_PRI " << l_LineV[i].m_u32FromId << l_LineV[i].m_vNoise << std::endl;
            else
                stream << "EDGE_SE3 " << l_LineV[i].m_u32FromId << " " << l_LineV[i].m_u32ToId
                       << " " << l_LineV[i].m_SlamBetweenPose.m_trans.x() << " "
                       << l_LineV[i].m_SlamBetweenPose.m_trans.y() << " "
                       << l_LineV[i].m_SlamBetweenPose.m_trans.z() << " "
                       << l_LineV[i].m_SlamBetweenPose.m_quat << l_LineV[i].m_vNoise << std::endl;
        }
        for (int i = 0; i < l_loop.size(); i++)
        {
            stream << "EDGE_SE3 " << l_loop[i].m_u32FromId << " " << l_loop[i].m_u32ToId << " "
                   << l_loop[i].m_SlamBetweenPose.m_trans.x() << " "
                   << l_loop[i].m_SlamBetweenPose.m_trans.y() << " "
                   << l_loop[i].m_SlamBetweenPose.m_trans.z() << " "
                   << l_loop[i].m_SlamBetweenPose.m_quat << l_loop[i].m_vNoise << std::endl;
        }
    }

  private:
    /**
     * @brief 写node和line,回环边等相关信息
     * @param p_strBinFile 路径
     * @param p_NodeV_ node
     * @param p_LineV_ line
     * @param p_loop_ 回环边
     * @code
     *
     * @endcode
     * @return [true] \n
     * [details wirte here]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [details wirte here]
     *
     */
    bool writeBinary_(std::string p_strBinFile,
                      vector<st_Node> p_NodeV_,
                      vector<st_Line> p_LineV_,
                      vector<st_Line> p_loop_)
    {
        //头文件操作
        s_Header l_sHeader;
        headerHandle_(l_sHeader, p_NodeV_, p_LineV_, p_loop_);

        std::ofstream l_outFile;
        l_outFile.open(p_strBinFile, std::ios::out | std::ios::binary);
        if (!l_outFile)
            return false;
        writeHandle_(l_outFile, l_sHeader);
        writeNodeBinary_(l_outFile, p_NodeV_, l_sHeader);
        writeLineBinary_(l_outFile, p_LineV_, l_sHeader);
        writeLoopBinary_(l_outFile, p_loop_, l_sHeader);
        l_outFile.close();
        return true;
    }
    /**
     * @brief 处理加密头文件
     * @param p_sHeader 加密文件头
     * @param p_NodeV_ 节点
     * @param p_LineV_ 边
     * @param p_LoopV_ 回环边
     * @code
     *
     * @endcode
     * @return [true] \n
     * [details wirte here]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [details wirte here]
     *
     */
    bool headerHandle_(s_Header& p_sHeader,
                       vector<st_Node> p_NodeV_,
                       vector<st_Line> p_LineV_,
                       vector<st_Line> p_LoopV_)
    {
        s_FeatureInfo l_stFeatureInfo;
        if (!p_NodeV_.empty())
        {
            LOGFAE(WTRACE, "{} 保存node节点数 | 个[{}]", WJLog::getWholeSysTime(), p_NodeV_.size());
            l_stFeatureInfo.m_iPntNum = p_NodeV_.size();
            l_stFeatureInfo.m_iByteNum = sizeof(p_NodeV_.at(0));
            l_stFeatureInfo.m_iFeatureType = 0;
            p_sHeader.m_vFeatureInfo.push_back(l_stFeatureInfo);
        }
        if (!p_LineV_.empty())
        {
            LOGFAE(WTRACE, "{} 保存line边数 | 个[{}]", WJLog::getWholeSysTime(), p_LineV_.size());
            l_stFeatureInfo.m_iPntNum = p_LineV_.size();
            l_stFeatureInfo.m_iByteNum = sizeof(p_LineV_.at(0));
            l_stFeatureInfo.m_iFeatureType = 0;
            p_sHeader.m_vFeatureInfo.push_back(l_stFeatureInfo);
        }
        if (!p_LoopV_.empty())
        {
            LOGFAE(WTRACE, "{} 保存loop边数 | 个[{}]", WJLog::getWholeSysTime(), p_LoopV_.size());
            l_stFeatureInfo.m_iPntNum = p_LoopV_.size();
            l_stFeatureInfo.m_iByteNum = sizeof(p_LoopV_.at(0));
            l_stFeatureInfo.m_iFeatureType = 0;
            p_sHeader.m_vFeatureInfo.push_back(l_stFeatureInfo);
        }
        copyRtkDate_();  //更新全局变量
        return true;
    }
    /**
     * @brief 写加密文件头
     * @param p_file 路径
     * @param p_sHeader 加密文件头
     *
     */
    void writeHandle_(std::ofstream& p_file, s_Header& p_sHeader)
    {
        //写头文件
        //写类型数量
        int l_iSize = p_sHeader.m_vFeatureInfo.size();
        p_file.write((char*)&l_iSize, sizeof(int));

        char l_cReserve[100];  // 100 - 8(版本占8字节)
        if (c_iVersionMap == 2)
        {
            int l_iVersion = c_iVersionMap;
            char ff[] = "ff";
            char aa[] = "aa";
            p_file.write((char*)&ff, 2);
            p_file.write((char*)&l_iVersion, 4);
            p_file.write((char*)&aa, 2);
            p_file.write((char*)&l_cReserve[0], 92);  //表示指针头，写多少个字节
        }
        else
        {
            //预留100字节
            p_file.write((char*)&l_cReserve[0], 100);
            if (c_iVersionMap != 1)
                printf("Error set map version | %d\n", c_iVersionMap);
        }
        for (int i = 0; i < (int)p_sHeader.m_vFeatureInfo.size(); i++)
        {
            p_file.write((char*)&(p_sHeader.m_vFeatureInfo[i].m_iFeatureType), sizeof(int));  //类型
            p_file.write((char*)&(p_sHeader.m_vFeatureInfo[i].m_iPntNum), sizeof(int));  //点数
            // p_file.write((char*)&(p_sHeader.m_vFeatureInfo[i].m_iFieldNum), sizeof(int));
            // //字段数
            p_file.write((char*)&(p_sHeader.m_vFeatureInfo[i].m_iByteNum), sizeof(int));  //字节数
            //预留100字节
            p_file.write((char*)&(l_cReserve[0]), 100);
        }
        //写RTK建图数据
        l_iSize = sizeof(st_RtkMap);
        p_file.write((char*)&l_iSize, sizeof(int));
        p_file.write((char*)&(c_stRtkMap_), sizeof(st_RtkMap));
    }
    /**
     * @brief 写node节点
     * @param p_file 流
     * @param p_NodeV_ 节点
     * @param p_sHeader 头信息
     *
     */
    void writeNodeBinary_(std::ofstream& p_file, vector<st_Node> p_NodeV_, s_Header& p_sHeader)
    {
        if (p_NodeV_.empty())
            return;
        //总点数
        int l_iFldSize = c_iFieldSize_;
        char* l_cFld = new char[l_iFldSize];
        int l_iFldIdx = 0;
        for (size_t j = 0; j < p_NodeV_.size(); j++)
        {
            memcpy(&l_cFld[l_iFldIdx],
                   reinterpret_cast<const char*>(&p_NodeV_[j]),
                   p_sHeader.m_vFeatureInfo[0].m_iByteNum
                       + (p_sHeader.m_vFeatureInfo[0].m_iFeatureType + 1));
            l_iFldIdx += (p_sHeader.m_vFeatureInfo[0].m_iByteNum
                          + (p_sHeader.m_vFeatureInfo[0].m_iFeatureType + 1));

            //该行剩余内存不够写一个点，跳入下一行
            if ((l_iFldIdx + p_sHeader.m_vFeatureInfo[0].m_iByteNum
                 + (p_sHeader.m_vFeatureInfo[0].m_iFeatureType + 1))
                > l_iFldSize)
            {
                p_file.write((char*)&l_cFld[0], l_iFldIdx);
                l_iFldIdx = 0;
            }
        }
        p_file.write((char*)&l_cFld[0], l_iFldIdx);
        delete[] l_cFld;
    }
    /**
     * @brief 写边节点
     * @param p_file 流
     * @param p_LineV_ 边
     * @param p_sHeader 头信息
     *
     */
    void writeLineBinary_(std::ofstream& p_file, vector<st_Line> p_LineV_, s_Header& p_sHeader)
    {
        if (p_LineV_.empty())
            return;
        //总点数
        int l_iFldSize = c_iFieldSize_;
        char* l_cFld = new char[l_iFldSize];
        int l_iFldIdx = 0;
        for (size_t j = 0; j < p_LineV_.size(); j++)
        {
            memcpy(&l_cFld[l_iFldIdx],
                   reinterpret_cast<const char*>(&p_LineV_[j]),
                   p_sHeader.m_vFeatureInfo[1].m_iByteNum
                       + (p_sHeader.m_vFeatureInfo[1].m_iFeatureType + 1));
            l_iFldIdx += (p_sHeader.m_vFeatureInfo[1].m_iByteNum
                          + (p_sHeader.m_vFeatureInfo[1].m_iFeatureType + 1));

            //该行剩余内存不够写一个点，跳入下一行
            if ((l_iFldIdx + p_sHeader.m_vFeatureInfo[1].m_iByteNum
                 + (p_sHeader.m_vFeatureInfo[1].m_iFeatureType + 1))
                > l_iFldSize)
            {
                p_file.write((char*)&l_cFld[0], l_iFldIdx);
                l_iFldIdx = 0;
            }
        }
        p_file.write((char*)&l_cFld[0], l_iFldIdx);
        delete[] l_cFld;
    }
    /**
     * @brief 写回环节点
     * @param p_file 流
     * @param p_vLoop_ 回环边
     * @param p_sHeader 头信息
     *
     */
    void writeLoopBinary_(std::ofstream& p_file, vector<st_Line> p_vLoop_, s_Header& p_sHeader)
    {
        if (p_vLoop_.empty())
            return;
        //总点数
        int l_iFldSize = c_iFieldSize_;
        char* l_cFld = new char[l_iFldSize];
        int l_iFldIdx = 0;
        for (size_t j = 0; j < p_vLoop_.size(); j++)
        {
            memcpy(&l_cFld[l_iFldIdx],
                   reinterpret_cast<const char*>(&p_vLoop_[j]),
                   p_sHeader.m_vFeatureInfo[2].m_iByteNum
                       + (p_sHeader.m_vFeatureInfo[2].m_iFeatureType + 1));
            l_iFldIdx += (p_sHeader.m_vFeatureInfo[2].m_iByteNum
                          + (p_sHeader.m_vFeatureInfo[2].m_iFeatureType + 1));

            //该行剩余内存不够写一个点，跳入下一行
            if ((l_iFldIdx + p_sHeader.m_vFeatureInfo[2].m_iByteNum
                 + (p_sHeader.m_vFeatureInfo[2].m_iFeatureType + 1))
                > l_iFldSize)
            {
                p_file.write((char*)&l_cFld[0], l_iFldIdx);
                l_iFldIdx = 0;
            }
        }
        p_file.write((char*)&l_cFld[0], l_iFldIdx);
        delete[] l_cFld;
    }
    /**
     * @brief 将Graph转换成节点和边
     * @param graph gtsam优化器的量
     * @param estimate gtsam优化器的量
     *
     */
    void graphTrans_(const gtsam::NonlinearFactorGraph& graph, const gtsam::Values& estimate)
    {
        c_vGraphLine_.clear();
        c_vGraphLoop_.clear();
        c_vGraphNode_.clear();
        
        

        // save 3D poses / landmarks
        for (const auto key_value : estimate)
        {
            st_Node l_temp;
            if (auto p = dynamic_cast<const gtsam::GenericValue<gtsam::Pose3>*>(&key_value.value))
            {
                const gtsam::Pose3& pose = p->value();
                const gtsam::Point3 t = pose.translation();
                const auto q = pose.rotation().toQuaternion();
                l_temp.m_uiNodeID = key_value.key;
                l_temp.m_Pose.m_trans = t;
                l_temp.m_Pose.m_quat = q;
                c_vGraphNode_.push_back(l_temp);
            }
            else if (auto p =
                         dynamic_cast<const gtsam::GenericValue<gtsam::Point3>*>(&key_value.value)){
                std::cout <<"graphTrans_--------------------------------hsq:  else p" << std::endl;
                const gtsam::Pose3 pose;
                const gtsam::Point3 t = p->value();// hsq raw: pose.translation();
                auto index = [](gtsam::Key key) { return gtsam::Symbol(key); };
                std::cout << "hsq  key_value.key = " << index(key_value.key) << " " << t.x() << " "
                       << t.y() << " " << t.z() << std::endl;
                const auto q = pose.rotation().toQuaternion();
                l_temp.m_uiNodeID = key_value.key;
                l_temp.m_Pose.m_trans = t;
                // l_temp.m_Pose.m_quat = q;
                c_vGraphNode_.push_back(l_temp);

            }
            else if(auto p = static_cast<const gtsam::GenericValue<gtsam::GPSFactor>*>(&key_value.value)){
                std::cout <<"graphTrans_--------------------------------hsq:  GPSFactor" << std::endl;
            }
        }

        std::cout<<"find bug: mappingStorage.hpp graphTrans_" << std::endl;
        // save edges (2D or 3D)
        for (const auto& factor_ : graph)
        {
            st_Line l_temp;
            if (auto factorPR =
                    boost::dynamic_pointer_cast<gtsam::PriorFactor<gtsam::Pose3>>(factor_))
            {
                gtsam::Matrix6 Info = parseNoiseV6_(factorPR->noiseModel());
                l_temp.m_bIsPrior = true;
                l_temp.m_u32FromId = factorPR->key();
                l_temp.m_u32ToId = factorPR->key();
                l_temp.m_vNoise = Info;
                c_vGraphLine_.push_back(l_temp);
            }
            else if (auto factor3D =
                         boost::dynamic_pointer_cast<gtsam::BetweenFactor<gtsam::Pose3>>(factor_))
            {
                const gtsam::Pose3 pose3D = factor3D->measured();
                const gtsam::Point3 t = pose3D.translation();
                const auto q = pose3D.rotation().toQuaternion();
                gtsam::Matrix6 Info = parseNoiseV6_(factor3D->noiseModel());
                l_temp.m_u32FromId = factor3D->key1();
                l_temp.m_u32ToId = factor3D->key2();
                l_temp.m_SlamBetweenPose.m_trans = t;
                l_temp.m_SlamBetweenPose.m_quat = q;
                l_temp.m_vNoise = Info;
                if (l_temp.m_u32ToId - l_temp.m_u32FromId > 1)
                    c_vGraphLoop_.push_back(l_temp);
                else
                    c_vGraphLine_.push_back(l_temp);
            }
            else if (auto factorGPS = boost::dynamic_pointer_cast<gtsam::GPSFactor>(factor_))
            {
                // std::cout<<"hsq:graphTrans_ save gtsam::GPSFactor" << std::endl;
                const gtsam::Point3 t = factorGPS->measurementIn();
                gtsam::Matrix3 Info = parseNoiseV3_(factorGPS->noiseModel());
                c_vGraphNode_[factorGPS->key()].m_bIsGpsAdd = true;
                c_vGraphNode_[factorGPS->key()].m_rtkPose.setXYZ(t[0], t[1], t[2]);
                c_vGraphNode_[factorGPS->key()].m_rtkPose.m_bFlag =
                    wj_slam::PoseStatus::ContinuePose;
                c_vGraphNode_[factorGPS->key()].m_vGpsNoise = Info;
            }
        }
        std::cout<<"find bug: mappingStorage.hpp graphTrans_ finished" << std::endl;
    }
    /**
     * @brief 拷贝RTK建图相关信息
     *
     */
    void copyRtkDate_()
    {
        // c_stRtkMap_.m_stAdjust = c_stSysParam_->m_rtk.m_stTrans;//目前暂时未加入rtk
        // c_stRtkMap_.m_stRtk2Lidar = c_stSysParam_->m_rtk.m_stLidarToAgv;
        // c_stRtkMap_.m_dLat = c_stSysParam_->m_rtk.m_dLat;
        // c_stRtkMap_.m_dLon = c_stSysParam_->m_rtk.m_dLon;
    }
    /**
     * @brief 解密函数
     * @param p_strBinFile 路径
     * @param p_NodeV_ 节点
     * @param p_LineV_ 边
     * @param p_loop_ 回环边
     * @param p_RTKMap RTK建图信息
     * @code
     *
     * @endcode
     * @return [true] \n
     * [details wirte here]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [details wirte here]
     *
     */
    bool readBinary_(const std::string p_strBinFile,
                     vector<st_Node>& p_NodeV_,
                     vector<st_Line>& p_LineV_,
                     vector<st_Line>& p_loop_,
                     st_RtkMap& p_RTKMap)
    {
        std::ifstream l_inFile(p_strBinFile, std::ios::in | std::ios::binary);
        if (!l_inFile)
            return false;

        int l_iFldSize = c_iFieldSize_;
        char* l_cFld = new char[l_iFldSize];
        s_Header l_sHeader;
        //读取头文件 获取总点数+ 类型数
        l_inFile.read((char*)&l_sHeader, 4);
        //读取版本
        int l_iVersion = 1;
        char l_cVersion[8];
        l_inFile.read((char*)&l_cVersion, 8);
        if (l_cVersion[0] == 'f' && l_cVersion[1] == 'f' && l_cVersion[7] == 'a'
            && l_cVersion[6] == 'a')
        {
            memcpy(&l_iVersion, &l_cVersion[2], 4);
            printf("当前读取建图数据版本为V%d\n", l_iVersion);
        }
        else
        {
            printf("当前读取建图数据版本为V%d\n", l_iVersion);
        }

        // if (l_iVersion != c_iVersionMap)
        // {
        //     printf("建图数据版本不匹配 | 设置： %d 读取：%d\n", c_iVersionMap, l_iVersion);
        //     l_inFile.close();
        //     return false;
        // }

        //预留100-8
        char l_cReserve[100];  //注意
        l_inFile.read((char*)&l_cReserve, 92);

        s_FeatureInfo l_sFeatureInfo;
        for (int m = 0; m < l_sHeader.m_iTypeNum; m++)
        {
            int l_iIndex = 0;
            l_inFile.read((char*)&l_cFld[0], (4 * 3));
            memcpy(&l_sFeatureInfo.m_iFeatureType, &l_cFld[l_iIndex], 4);
            memcpy(&l_sFeatureInfo.m_iPntNum, &l_cFld[l_iIndex + 4], 4);
            memcpy(&l_sFeatureInfo.m_iByteNum, &l_cFld[l_iIndex + 8], 4);
            l_sHeader.m_vFeatureInfo.push_back(l_sFeatureInfo);
            //预留100
            l_inFile.read((char*)&l_cReserve, 100);
        }

        //读取RTKmap信息 可以移植
        int l_iRTKSize;
        l_inFile.read((char*)&l_iRTKSize, sizeof(int));
        char* l_cRtk = new char[l_iRTKSize];
        l_inFile.read(l_cRtk, l_iRTKSize);
        int l_en = sizeof(st_RtkMap);
        if (l_en > l_iRTKSize)
        {
            l_en = l_iRTKSize;
        }
        memcpy(&p_RTKMap, &l_cRtk[0], l_en);

        // 判断第1个类型是不是Path
        // bool l_bHavePathMap = false;
        // if (!l_sHeader.m_vFeatureInfo[0].m_iFeatureType)
        //     l_bHavePathMap = true;
        for (int i = 0; i < l_sHeader.m_iTypeNum; i++)
        {
            int l_iOffset = 0;
            if (l_sHeader.m_vFeatureInfo[i].m_iFeatureType <= 200)  //**读取偏置
            {
                l_iOffset = l_sHeader.m_vFeatureInfo[i].m_iByteNum
                            + l_sHeader.m_vFeatureInfo[i].m_iFeatureType + 1;
                // if (l_sHeader.m_vFeatureInfo[i].m_iFeatureType > (int)p_feature.size())
                // {
                //     printf("读取地图不匹配\n");
                //     return false;
                // }
            }

            int l_iPntFldSize = l_sHeader.m_vFeatureInfo[i].m_iPntNum * l_iOffset;

            // 数据大小重定义
            if (i == 0)
            {
                p_NodeV_.resize(l_sHeader.m_vFeatureInfo[i].m_iPntNum);
            }
            else if (i == 1)
            {
                p_LineV_.resize(l_sHeader.m_vFeatureInfo[i].m_iPntNum);
            }
            else if (i == 2)
            {
                p_loop_.resize(l_sHeader.m_vFeatureInfo[i].m_iPntNum);
            }

            if (l_iPntFldSize <= l_iFldSize)
            {
                //读数据
                l_inFile.read((char*)&l_cFld[0], l_iPntFldSize);
                int l_iFldIdx = 0;
                for (int j = 0; j < l_sHeader.m_vFeatureInfo[i].m_iPntNum; j++)
                {
                    if (i == 0)
                    {
                        int l_iLen = sizeof(st_Node);
                        if (l_iLen > l_sHeader.m_vFeatureInfo[i].m_iByteNum)
                        {
                            l_iLen = l_sHeader.m_vFeatureInfo[i].m_iByteNum;
                        }
                        memcpy(&p_NodeV_[j], &l_cFld[l_iFldIdx], l_iLen);
                    }
                    else if (i == 1)
                    {
                        int l_iLen = sizeof(st_Line);
                        if (l_iLen > l_sHeader.m_vFeatureInfo[i].m_iByteNum)
                        {
                            l_iLen = l_sHeader.m_vFeatureInfo[i].m_iByteNum;
                        }
                        memcpy(&p_LineV_[j], &l_cFld[l_iFldIdx], l_iLen);
                    }
                    else if (i == 2)
                    {
                        int l_iLen = sizeof(st_Line);
                        if (l_iLen > l_sHeader.m_vFeatureInfo[i].m_iByteNum)
                        {
                            l_iLen = l_sHeader.m_vFeatureInfo[i].m_iByteNum;
                        }
                        memcpy(&p_loop_[j], &l_cFld[l_iFldIdx], l_iLen);
                    }
                    l_iFldIdx += l_iOffset;
                }
            }
            else
            {
                int l_iReadOffset = (l_iFldSize / l_iOffset) * l_iOffset;
                int l_iMult = l_iPntFldSize / l_iReadOffset;
                int l_iRem = l_iPntFldSize % l_iReadOffset;
                //读数据
                l_inFile.read((char*)&l_cFld[0], l_iReadOffset);
                int l_iFldIdx = 0;
                int l_iN = 1;
                for (int j = 0; j < l_sHeader.m_vFeatureInfo[i].m_iPntNum; j++)
                {
                    if (i == 0)
                    {
                        int l_iLen = sizeof(st_Node);
                        if (l_iLen > l_sHeader.m_vFeatureInfo[i].m_iByteNum)
                        {
                            l_iLen = l_sHeader.m_vFeatureInfo[i].m_iByteNum;
                        }
                        memcpy(&p_NodeV_[j], &l_cFld[l_iFldIdx], l_iLen);
                    }
                    else if (i == 1)
                    {
                        int l_iLen = sizeof(st_Line);
                        if (l_iLen > l_sHeader.m_vFeatureInfo[i].m_iByteNum)
                        {
                            l_iLen = l_sHeader.m_vFeatureInfo[i].m_iByteNum;
                        }
                        memcpy(&p_LineV_[j], &l_cFld[l_iFldIdx], l_iLen);
                    }
                    else if (i == 2)
                    {
                        int l_iLen = sizeof(st_Line);
                        if (l_iLen > l_sHeader.m_vFeatureInfo[i].m_iByteNum)
                        {
                            l_iLen = l_sHeader.m_vFeatureInfo[i].m_iByteNum;
                        }
                        memcpy(&p_loop_[j], &l_cFld[l_iFldIdx], l_iLen);
                    }
                    l_iFldIdx += l_iOffset;
                    if (l_iFldIdx == l_iReadOffset && l_iN != l_iMult)
                    {
                        l_inFile.read((char*)&l_cFld[0], l_iReadOffset);
                        l_iFldIdx = 0;
                        l_iN++;
                    }  // 非第一行
                    else if (l_iFldIdx == l_iReadOffset && l_iN == l_iMult)
                    {
                        l_inFile.read((char*)&l_cFld[0], l_iRem);
                        l_iFldIdx = 0;
                    }
                }
            }
        }
        l_inFile.close();
        delete[] l_cFld;
        return true;
    }
    gtsam::Matrix6 parseNoiseV6_(const gtsam::SharedNoiseModel& model)
    {
        boost::shared_ptr<gtsam::noiseModel::Gaussian> gaussianModel =
            boost::dynamic_pointer_cast<gtsam::noiseModel::Gaussian>(model);
        return gaussianModel->R().transpose() * gaussianModel->R();
    }
    gtsam::Matrix3 parseNoiseV3_(const gtsam::SharedNoiseModel& model)
    {
        boost::shared_ptr<gtsam::noiseModel::Gaussian> gaussianModel =
            boost::dynamic_pointer_cast<gtsam::noiseModel::Gaussian>(model);
        return gaussianModel->R().transpose() * gaussianModel->R();
    }
    gtsam::Matrix6 makeSigmas6D_(float (&v)[6])
    {
        gtsam::noiseModel::Diagonal::shared_ptr l_temp = gtsam::noiseModel::Diagonal::Variances(
            (gtsam::Vector(6) << v[0], v[1], v[2], v[3], v[4], v[5]).finished());
        boost::shared_ptr<gtsam::noiseModel::Gaussian> gaussianModel =
            boost::dynamic_pointer_cast<gtsam::noiseModel::Gaussian>(l_temp);
        return gaussianModel->R().transpose() * gaussianModel->R();
    }
    gtsam::Matrix3 makeSigmas3D_(float (&v)[6])
    {
        gtsam::noiseModel::Diagonal::shared_ptr l_temp = gtsam::noiseModel::Diagonal::Variances(
            (gtsam::Vector(3) << v[0], v[1], v[2]).finished());
        boost::shared_ptr<gtsam::noiseModel::Gaussian> gaussianModel =
            boost::dynamic_pointer_cast<gtsam::noiseModel::Gaussian>(l_temp);
        return gaussianModel->R().transpose() * gaussianModel->R();
    }
};
}  // namespace wj_slam