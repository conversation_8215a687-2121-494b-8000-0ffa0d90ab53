/**
 * @file loopCheck.hpp
 * <AUTHOR> Li
 * @brief 回环识别类
 * @version 1.0
 * @date 2023-09-04
 * @copyright Copyright (c)2023 Vanjee
 */

#pragma once
#include "../placeRecongnize/impl/placeRecongnize.hpp"
#include "./loop2map_align.hpp"
#include "algorithm/map/sub_map/KeyFrameMap.h"
#include "common/common_ex.h"
#include "common/type/type_thread.h"
#include <deque>

#ifndef _T_M_
#    define _T_M_ template <typename M>
#endif

// #define gtest
#ifdef gtest
#    define private public
#    define protected public
#endif
namespace wj_slam {
template <typename M = pcl::PointXYZHSV> class LoopCheck {
  public:
    /**
     * @brief 位置识别结果储存
     *
     */
    typedef struct st_RecongnizeResult
    {
        float m_fScore;             /**< 相似度得分*/
        int m_fShift;               /**< sc匹配偏移量*/
        int m_uiCurrFrameID;        /**< 当前帧ID*/
        int m_uiLoopFrameID;        /**< 回环子图ID*/
        s_POSE6D m_uiLoopFramePose; /**< 回环地图帧位姿*/
        s_POSE6D m_sEstimatedPose;  /**< 预估位姿*/

        st_RecongnizeResult() {}

        /**
         * @brief Construct a new st RecongnizeResult object
         *
         * @param[in] p_fScore 输入分数
         * @param[in] p_uiLID 输入地图ID
         * @param[in] p_sEPose 预估位姿
         */
        st_RecongnizeResult(float p_fScore,
                            int p_fShift,
                            int p_uiCID,
                            s_POSE6D p_sEPose,
                            int p_uiLID,
                            s_POSE6D p_sLPose)
            : m_fScore(p_fScore), m_fShift(p_fShift), m_uiCurrFrameID(p_uiCID),
              m_uiLoopFrameID(p_uiLID), m_uiLoopFramePose(p_sLPose), m_sEstimatedPose(p_sEPose)
        {
        }

        /**
         * @brief 重载比较符号
         *
         * @param[in] b 比较对象相似度得分,得分一致时比较当前ID
         * @return [true] [小于]
         * @return [false] [反之]
         */
        bool operator<(const st_RecongnizeResult& b) const
        {
            if (m_fScore != b.m_fScore)
                return m_fScore < b.m_fScore;
            return m_uiCurrFrameID < b.m_uiCurrFrameID;
        }
    } st_RecongnizeResult;

  private:
    using FrameFeature = FEATURE_PAIR<M>;                      /**< 特征组类型*/
    using FrameFeaturePtr = typename FrameFeature::Ptr;        /**< 特征组指针类型*/
    using Frame = KEYFRAME<M>;                                 /**< 关键帧类型*/
    using FramePtr = typename Frame::Ptr;                      /**< 关键帧指针*/
    boost::shared_ptr<KfMapPair> c_pMapPair_ = nullptr;        /**< 关键帧地图管理对象*/
    SYSPARAM* c_stSysParam_;                                   /**< 系统参数*/
    PlaceRecongnize c_PlaceRecongnize_;                        /**< 粗略位置识别方法*/
    L2M_Align<M, M> c_pMatcher_;                               /**< 精确配准方法*/
    typename wj_slam::thread::Ptr pThread;                     /**< 主线程控制器*/
    int c_iCurrIndex_;                                         /**< 待位置识别Src帧*/
    std::vector<int> c_viMayLoopIndex_;                        /**< 待位置识别Trg队列*/
    float c_fLoopScroeThr_;                                    /**< PR最低得分*/
    int c_iMapWindowSize_;                                     /**< 匹配地图窗口大小*/
    st_RecongnizeResult c_LoopCheckRes_;                       /**< 输出结果*/
    int c_iLastLoopIndex_;                                     /**< 上次的回环帧*/
    int c_iLoopKFIndex_;                                       /**< 检测到的回环帧*/
    std::deque<std::pair<int, std::vector<int>>> c_qLoopQueue; /**< 待求回环队列*/
    std::mutex c_mQueMutex_;                                   /**< 待求回环队列锁*/
    std::atomic<bool> c_bRun_;                                 /**< 线程控制标志*/
    int c_iRawMapSize;                                         /**< 旧地图(无有效SC)*/

  public:
    /**
     * @brief Construct a new Loop Check object
     *
     * @param p_vMaps 地图data
     * @param p_lock 地图指针
     */
    LoopCheck(boost::shared_ptr<KfMapPair> p_pMapPair);

    ~LoopCheck()
    {
        //检测线程停止标志
        c_bRun_ = false;
        //等待检测线程退出
        if (pThread)
            pThread->stop();
    }

    /**
     * @brief 设置一对待匹配帧-地图帧
     *
     * @param p_iCurrIndex 当前帧
     * @param p_viMayIndex 待检测可能回环地图帧队列
     */
    void setTryPair(int p_iCurrIndex, std::vector<int> p_viMayIndex)
    {
        //添加当前待检测帧至队列
        std::pair<int, std::vector<int>> l_currIndex(p_iCurrIndex, p_viMayIndex);
        c_mQueMutex_.lock();
        c_qLoopQueue.push_back(l_currIndex);
        c_mQueMutex_.unlock();
        LOGM(WDEBUG,
             "{} [LOOP CHECK] LoopCheck setTryPair curID:{} -> MayIndex size:{}.",
             WJLog::getWholeSysTime(),
             p_iCurrIndex,
             p_viMayIndex.size());
        if (thread::State::Stoped == getLoopCheckState())
        {
            //若没有线程则开启线程
            pThread.reset(new thread(boost::bind(&LoopCheck<M>::loopCheckThread_, this)));
            c_bRun_ = true;
            // 立即启动线程
            pThread->start();
        }
    }

    /**
     * @brief 设置粗配准最低分
     *
     */
    void setPR_ScoreThreshold(float p_fLoopScroeThr)
    {
        if (p_fLoopScroeThr > 0.0f && p_fLoopScroeThr < 1.0f)
            c_fLoopScroeThr_ = p_fLoopScroeThr;
    }

    /**
     * @brief 获取当前回环检测模块的状态
     *
     * @return [State::Stoped] 回环检测模块 未运行或上次无效
     * @return [State::Running] 回环检测模块 正在运行
     * @return [State::Paused] 回环检测模块 等待输出
     */
    thread::State getLoopCheckState()
    {
        if (pThread)
            return pThread->state();
        else
            return thread::State::Stoped;
    }

    /**
     * @brief 获取当前配准结果
     *
     */
    st_RecongnizeResult getLoopCheckResult()
    {
        // 恢复(终止完成的线程)
        pThread->resume();
        return c_LoopCheckRes_;
    }

  private:
    /**
     * @brief 回环匹配线程
     *
     */
    void loopCheckThread_();

    /**
     * @brief 基于scan context的场景识别
     * @note  通过当前子图中的位姿搜索之前子图,
     * 搜索过程采用scan context描述符来计算子图距离
     * 将搜索结果按相似度（1-distance）排序，加入中间缓存
     * @param[in] p_sPlace 识别区域缓存
     * @return [true] [存在识别区域]
     * @return [false] [不存在识别区域]
     */
    bool placeRecongnize_(Eigen::MatrixXd& p_CurrSC,
                          std::vector<int>& p_viMayLoopIndex,
                          std::vector<st_RecongnizeResult>& p_sPlace);
    /**
     * @brief 回环帧校验
     * @param p_curPose 当前帧原位姿
     * @param p_curNewPose 当前帧修正位姿
     * @code
     *
     * @endcode
     * @return [true] \n
     * [可以回环]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [错误回环]
     *
     */
    bool loopEdgeCheck_(s_POSE6D p_curPose, s_POSE6D p_curNewPose);
    /**
     * @brief 读取系统设置参数
     *
     */
    void paramReset_();
};

_T_M_ LoopCheck<M>::LoopCheck(boost::shared_ptr<KfMapPair> p_pMapPair)
    : c_pMapPair_(p_pMapPair), c_stSysParam_(SYSPARAM::getIn()), c_iCurrIndex_(-1),
      c_viMayLoopIndex_(std::vector<int>(0)), c_fLoopScroeThr_(c_stSysParam_->m_loop.m_fSCthr),
      c_iMapWindowSize_(10), c_iLastLoopIndex_(0), c_iLoopKFIndex_(-1), c_bRun_(false),
      c_iRawMapSize(c_pMapPair_->getKfSize())
{
    paramReset_();
}

_T_M_ void LoopCheck<M>::paramReset_()
{
    // setPR_ScoreThreshold(c_stSysParam_->m_loop.m_fSCthr);
    AMCL_param l_stParam;
    l_stParam.m_iParticleNums = c_stSysParam_->m_loop.m_iParticleNums;
    l_stParam.m_iFiltTimes = c_stSysParam_->m_loop.m_iParticleFilterTimes;
    l_stParam.m_fDeltaA = c_stSysParam_->m_loop.m_fParticleFilterRangeA;
    l_stParam.m_fDeltaX = c_stSysParam_->m_loop.m_fParticleFilterRangeX;
    l_stParam.m_fDeltaY = c_stSysParam_->m_loop.m_fParticleFilterRangeY;
    l_stParam.m_fDeltaZ = c_stSysParam_->m_loop.m_fParticleFilterRangeZ;
    c_pMatcher_.setAmclPara(l_stParam);
    c_pMatcher_.setOptTimes(c_stSysParam_->m_loop.m_iOptTimes);
    c_pMatcher_.setMatchPercentTHR(c_stSysParam_->m_loop.m_fMatchMinNumPercentThr);
    c_pMatcher_.setCheckTHR(c_stSysParam_->m_loop.m_fMatchAveDistanceThr);
    c_pMatcher_.setVerifyThreshold(c_stSysParam_->m_loop.m_fVerifyMatchPercentThr,
                                   c_stSysParam_->m_loop.m_fVerifyScoreThr);
}
_T_M_ void LoopCheck<M>::loopCheckThread_()
{
    try
    {
        while (!c_qLoopQueue.empty() && c_bRun_)
        {
            c_mQueMutex_.lock();
            int l_iCurID = c_qLoopQueue.size();
            c_iCurrIndex_ = c_qLoopQueue.back().first;
            c_viMayLoopIndex_ = c_qLoopQueue.back().second;
            c_qLoopQueue.pop_back();
            c_mQueMutex_.unlock();
            LOGM(WINFO,
                 "{} [LOOP CHECK] [id:{}]-> LoopCheck thread enter .",
                 WJLog::getWholeSysTime(),
                 c_iCurrIndex_);
            //若当前帧与上次回环帧数相差20以为不进行计算
            if (c_iCurrIndex_ - c_iLastLoopIndex_ < 20)
            {
                continue;
            }
            FramePtr l_KFCur(new Frame);
            FramePtr l_KFMap(new Frame);
            FramePtr l_KFMap_oneFrame(new Frame);
            std::vector<st_RecongnizeResult> l_vRecongRes;
            // 取出当前帧
            l_KFCur = c_pMapPair_->getKfMapByInd(c_iCurrIndex_);
            // 粗配准
            if (placeRecongnize_(*l_KFCur->m_pFeature->sc, c_viMayLoopIndex_, l_vRecongRes))
            {
                LOGM(WINFO,
                     "{} [LOOP CHECK] ID:[{}] SC Recongnize SUCCESS.",
                     WJLog::getWholeSysTime(),
                     c_iCurrIndex_);
                // 取出当前队列得分最高项
                st_RecongnizeResult& l_sBestRecong = l_vRecongRes.back();

                //设置当前位姿
                l_sBestRecong.m_sEstimatedPose = l_KFCur->m_Pose;
                // 构建地图
                std::vector<int> l_vMapId;
                for (int i = -c_iMapWindowSize_; i < c_iMapWindowSize_; ++i)
                {
                    int idx = l_sBestRecong.m_uiLoopFrameID + i;
                    if (idx >= c_iRawMapSize && idx < c_iCurrIndex_)
                    {
                        l_vMapId.push_back(idx);
                    }
                }
                l_KFMap = c_pMapPair_->getKfMapByInds(l_vMapId);
                // ID取子图ID
                l_KFMap->m_pFeature->m_uiScanFrame = l_sBestRecong.m_uiLoopFrameID;
                // 地图位姿与子图ID同步
                l_KFMap->m_Pose = l_sBestRecong.m_uiLoopFramePose;
                //获取回环帧单帧点云
                l_KFMap_oneFrame = c_pMapPair_->getKfMapByInd(l_sBestRecong.m_uiLoopFrameID);
                if (!c_bRun_)
                    break;
                // 开始配准
                s_POSE6D l_stSCEstimatePose;
                double l_dSCEstimateYaw =
                    l_sBestRecong.m_fShift / float(c_stSysParam_->m_loop.m_iSector) * 360.0;
                l_stSCEstimatePose.setRPY(0, 0, l_dSCEstimateYaw);
                LOGM(WDEBUG,
                     "{} [LOOP CHECK] ID:{} SC angle:[{}]",
                     WJLog::getWholeSysTime(),
                     c_iCurrIndex_,
                     l_dSCEstimateYaw);
                //输入SC预估位姿
                c_pMatcher_.setSCEstimate(l_stSCEstimatePose);
                //输入当前帧和当前位姿
                c_pMatcher_.setInputSource(l_KFCur->m_pFeature, l_KFCur->m_Pose);
                //输入回环地图,回环单帧和回环帧位姿
                c_pMatcher_.setInputTarget(
                    l_KFMap->m_pFeature, l_KFMap_oneFrame->m_pFeature, l_KFMap->m_Pose);
                bool res = c_pMatcher_.align();
                if (res)
                {
                    c_iLoopKFIndex_ = l_sBestRecong.m_uiCurrFrameID;
                    // 提取分数
                    l_sBestRecong.m_fScore = c_pMatcher_.getFitnessScore();
                    s_POSE6D l_Opt = c_pMatcher_.getFinalTransformation();
                    // 提取修正位姿
                    l_sBestRecong.m_sEstimatedPose = l_Opt * l_sBestRecong.m_sEstimatedPose;
                    LOGM(WDEBUG,
                         "{} [LOOP CHECK] {} :get l_Opt x:[{}],y:[{}],YAW:[{}]",
                         WJLog::getWholeSysTime(),
                         c_iCurrIndex_,
                         l_Opt.x(),
                         l_Opt.y(),
                         l_Opt.yaw());
                    if (loopEdgeCheck_(l_KFCur->m_Pose, l_sBestRecong.m_sEstimatedPose))
                    {
                        c_LoopCheckRes_ = l_sBestRecong;
                        LOGM(WINFO,
                             "{} [LOOP CHECK] LoopCheck success align from {} to {}, score:{}",
                             WJLog::getWholeSysTime(),
                             c_LoopCheckRes_.m_uiCurrFrameID,
                             c_LoopCheckRes_.m_uiLoopFrameID,
                             c_LoopCheckRes_.m_fScore);
                        c_iLastLoopIndex_ = c_iLoopKFIndex_;
                        // 若成功,线程暂停锁死，直至触发回环修正线程即继续
                        pThread->pause();
                        pThread->wait_pause();
                        c_mQueMutex_.lock();
                        c_qLoopQueue.erase(c_qLoopQueue.begin(),
                                           c_qLoopQueue.begin() + l_iCurID - 1);
                        c_mQueMutex_.unlock();
                    }
                    else
                    {
                        LOGM(WINFO,
                             "{} [LOOP CHECK] LoopCheck false from {} to {}.",
                             WJLog::getWholeSysTime(),
                             c_iCurrIndex_,
                             l_KFMap->m_pFeature->m_uiScanFrame);
                    }
                }
            }
        }
    }
    catch (const std::exception& e)
    {
        std::cerr << e.what() << '\n';
        c_mQueMutex_.unlock();
    }
    LOGM(WINFO, "{} [LOOP CHECK] LoopCheck thread exit.", WJLog::getWholeSysTime());
    // 失败则终止线程 || 成功则先暂停再终止
}
_T_M_ bool LoopCheck<M>::placeRecongnize_(Eigen::MatrixXd& p_CurrSC,
                                          std::vector<int>& p_viMayLoopIndex,
                                          std::vector<st_RecongnizeResult>& p_sPlace)
{
    // 检查所有KeyFrame-SubMap,注意每个SubMap可能含有不止一个SC
    for (auto l_uiMapID : p_viMayLoopIndex)
    {
        // 声明用来保存已有子图的指针
        FramePtr l_pSubMap = c_pMapPair_->getKfMapByInd(l_uiMapID);
        // 获取已有子图的所有帧<pose, sc>
        boost::shared_ptr<Eigen::MatrixXd>& l_scs = l_pSubMap->m_pFeature->sc;
        // 检查所有KeyFrame-SubMap->SC的重合度,全部加入容器进行重合度排序

        // 计算描述符匹配结果，<距离，偏移量>
        auto dist_shift = c_PlaceRecongnize_.distanceBtnScanContext(p_CurrSC, *l_scs);
        float l_fScore = 1 - dist_shift.first;
        int l_iShift = dist_shift.second;
        if (l_fScore > c_fLoopScroeThr_)
        {
            // 添加得分（1-距离）& SC-ID & 预估位姿(当前版本不知道) & 子图ID & 子图位姿
            p_sPlace.push_back(st_RecongnizeResult(l_fScore,
                                                   l_iShift,
                                                   c_iCurrIndex_,
                                                   l_pSubMap->m_Pose,
                                                   l_uiMapID,
                                                   l_pSubMap->m_Pose));
        }
        LOGM(WINFO,
             "{} [LOOP CHECK] LoopCheck find {} --> {} with score:{} - {}",
             WJLog::getWholeSysTime(),
             l_uiMapID,
             c_iCurrIndex_,
             l_fScore,
             c_fLoopScroeThr_);
    }
    // 如果存在识别成功,返回成功
    if (p_sPlace.size() > 0)
    {
        // 按得分给已选子图排序
        sort(p_sPlace.begin(), p_sPlace.end());
        LOGM(WINFO,
             "{} [LOOP CHECK] LoopCheck find:{} MAX id: {} with score:{}.",
             WJLog::getWholeSysTime(),
             p_sPlace.size(),
             p_sPlace.back().m_uiLoopFrameID,
             p_sPlace.back().m_fScore);
        return true;
    }
    LOGM(WINFO,
         "{} [LOOP CHECK] LoopCheck find: {}->NA:NA.",
         WJLog::getWholeSysTime(),
         c_iCurrIndex_);
    // 不存在则失败
    return false;
}

_T_M_ bool LoopCheck<M>::loopEdgeCheck_(s_POSE6D p_curPose, s_POSE6D p_curNewPose)
{
    FramePtr l_KFCheck(new Frame);
    //获取该可能回环帧的上一帧(深拷贝)
    l_KFCheck = c_pMapPair_->getKfMapByInd(c_iCurrIndex_ - 1);
    s_POSE6D l_stNewPose;
    s_POSE6D l_stDelta = l_KFCheck->m_Pose.inverse() * p_curPose;
    //计算当前帧上一帧修正后位姿
    l_stNewPose = p_curNewPose * l_stDelta.inverse();
    s_POSE6D l_opt = l_stNewPose * l_KFCheck->m_Pose.inverse();
    bool l_bRes = false;
    //计算修正后的点云
    c_pMatcher_.c_trans.transformCloudPoints(
        l_opt.m_quat, l_opt.m_trans, l_KFCheck->m_pFeature->first, l_KFCheck->m_pFeature->first);
    c_pMatcher_.c_trans.transformCloudPoints(
        l_opt.m_quat, l_opt.m_trans, l_KFCheck->m_pFeature->second, l_KFCheck->m_pFeature->second);
    //与回环检测的地图进行匹配,得到校验结果
    c_pMatcher_.setInputSource(l_KFCheck->m_pFeature, l_stNewPose);
    //概率校验+面点距离检测
    if (c_pMatcher_.checkDistance(true))
        l_bRes = true;
    else
        l_bRes = false;
    LOGM(WINFO,
         "{} [LOOP CHECK] ID:[{}] Edge Check:[{}].",
         WJLog::getWholeSysTime(),
         c_iCurrIndex_,
         l_bRes);
    return l_bRes;
}
}  // namespace wj_slam
