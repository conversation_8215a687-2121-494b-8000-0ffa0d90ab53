/**
 * @file loop2map_align.hpp
 * <AUTHOR>
 * @brief 回环帧匹配实现
 * @version 1.0
 * @date 2023-09-04
 * @copyright Copyright (c)2023 <PERSON><PERSON>
 */
#pragma once
#include "algorithm/loop/amcl_ParticleFileter/impl/amcl.hpp"
#include "algorithm/optimize/laserRegistration.h"
#include "algorithm/optimize/laserTransform.h"
#include "common/common_ex.h"
#include "common/type/type_frame.h"
#include "common/type/type_optimiz.h"
#include "tic_toc.h"
#include <boost/bind.hpp>

#ifndef _T_CM_
#    define _T_CM_ template <typename C, typename M>
#endif

// #define gtest
#ifdef gtest
#    define private public
#    define protected public
#endif

namespace wj_slam {

typedef struct AMCL_param
{
    float m_fDeltaX;
    float m_fDeltaY;
    float m_fDeltaZ;
    float m_fDeltaA;
    int m_iFiltTimes;
    int m_iParticleNums;
    AMCL_param()
        : m_fDeltaX(1.5), m_fDelta<PERSON>(1.5), m_fDeltaZ(0.02), m_fDeltaA(10), m_iFiltTimes(4),
          m_iParticleNums(1000)
    {
    }
} AMCL_param;
template <typename C = pcl::PointXYZHSV, typename M = pcl::PointXYZHSV> class L2M_Align {
  public:
    using Ptr = boost::shared_ptr<L2M_Align>;
    LaserTransform<C> c_trans; /**转移点云功能对象*/

  protected:
    using KeyFrameFeature = FEATURE_PAIR<C>;                  /**< 输入帧-特征组类型*/
    using KeyFrameFeaturePtr = typename KeyFrameFeature::Ptr; /**< 输入帧-特征组指针类型*/
    using KeyFrame = KEYFRAME<C>;                             /**< 输入帧-特征组指针类型*/
    using KeyFramePtr = typename KeyFrame::Ptr;               /**< 输入帧-特征组指针类型*/
    using MapFrameFeature = FEATURE_PAIR<M>;                  /**< 输出帧-特征组类型*/
    using MapFrameFeaturePtr = typename MapFrameFeature::Ptr; /**< 输出帧-特征组指针类型*/

    SYSPARAM* c_stSysParam_;            /**< 系统参数*/
    s_MappingConfig& c_stSlamParam_;    /**< 系统参数location部分 */
    s_LoctConfig& c_stLocateParam_;     /**< 系统参数mapping部分 */
    s_PoseCheckConfig& c_stCheckParam_; /**< 系统参数poseCheck部分 */

    KeyFramePtr c_pSrc_;           /**< 待匹配输入帧*/
    MapFrameFeaturePtr c_pTrg_Map; /**< 当前匹配地图*/
    KeyFramePtr c_pTrg_;           /**< 当前地图帧*/
    s_POSE6D c_stOptTrans_;        /**< 优化增量*/
    s_POSE6D c_stSCPose_;          /**< 局部点云sc预估位姿*/
    AMCL_param c_stAMCLPara_;      /**< AMCL参数*/
    bool c_bSampleSurfMatch_;      /**< 待匹配输入帧面点采样*/
    bool c_bXOYOpt_;               /**< 配准输出XOY结果*/
    float c_fXOYOptZ_;             /**< 配准输出XOY结果的高度*/
    float c_fEpsilon_[2];          /**< 优化终止梯度[xy,yaw]*/
    float c_fAlignScore_;          /**< 配准评分*/
    int c_iOptTimes_;              /**< 匹配次数*/
    float c_fNumPercentMap_;       /**< 校验的最小匹配比例 */
    float c_fOccupyScoreMap_;      /**< 校验的最小匹配分数 */
    bool c_bAMCLModle_;            /**< 是否AMCL模式*/
    float c_fDistanceThr_;         /**< 匹配评价距离检测阈值*/
    float c_fMatchPercentThr_;     /**< 匹配评价最低比例*/
    float c_fVerifyPercentThr_;    /**< 概率校验匹配比例阈值*/
    float c_fVerifyScoreThr_;      /**< 概率校验匹配得分阈值*/

    LaserRegistration<C, M> c_Matcher_;                        /**< 角面特征精确匹配器*/
    boost::shared_ptr<IVox<3, M>> c_pIvoxGrid_;                /**< ivox栅格地图 */
    boost::shared_ptr<LaserVerifyScore<C, M>> c_pLaserVerify_; /**< 栅格地图校验 */

    /**
     * @brief 匹配参数模式选择
     *
     * @param i 次数
     * @param max 总优化次数
     * @param p_bLocatMode
     * @param p_bKeyMod
     * @param p_TryMode
     */
    void switchSetMatch_(int i, bool p_TryMode);
    /**
     * @brief 校验函数
     *
     * @param p_keyFramePtr 校验帧
     * @return true 通过
     * @return false 不通过
     */
    bool checkOccupyVerify_();
    /**
     * @brief 设置匹配参数（通过标准模板）
     *
     * @param p_param
     */
    void setMatch_(s_MatcherConfig& p_param);
    /**
     * @brief 更新点云
     *
     * @param p_stOptOnce
     */
    void renewFrame_(s_POSE6D p_stOptOnce);
    /**
     * @brief 匹配优化
     *
     * @param p_pKeyFrame
     * @param p_stOptOnce
     * @param p_bTryMode
     * @return true
     * @return false
     */
    bool optimiz_(s_POSE6D& p_stOptOnce, bool p_bManualType);
    /**
     * @brief 执行amcl获取预估位姿
     * @code
     *
     * @endcode
     * @return [true] \n
     * [有正确预估位姿输出]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [无正确预估位姿输出]
     *
     */
    bool amclMode_();

    /**
     * @brief 转移点云至目标坐标系 全局-局部
     * @param p_stTrans 当前坐标系下位姿
     * @param p_pFeature 当前坐标系下点云
     *
     */
    void trans2Base_(s_POSE6D p_stTrans, MapFrameFeaturePtr p_pFeature)
    {
        p_stTrans = p_stTrans.inverse();
        c_trans.transformCloudPoints(
            p_stTrans.m_quat, p_stTrans.m_trans, p_pFeature->first, p_pFeature->first);
        c_trans.transformCloudPoints(
            p_stTrans.m_quat, p_stTrans.m_trans, p_pFeature->second, p_pFeature->second);
        c_trans.transformCloudPoints(
            p_stTrans.m_quat, p_stTrans.m_trans, p_pFeature->third, p_pFeature->third);
    }

  public:
    /**
     * @brief 设置匹配结果检测平均距离阈值
     * @param p_fDistanceThr
     *
     */
    void setCheckTHR(const float p_fDistanceThr)
    {
        c_fDistanceThr_ = p_fDistanceThr;
    }
    /**
     * @brief 设置最低匹配比例
     * @param p_fMatchPercentThr
     *
     */
    void setMatchPercentTHR(const float p_fMatchPercentThr)
    {
        c_fMatchPercentThr_ = p_fMatchPercentThr > 0 ? p_fMatchPercentThr : 0;
        c_Matcher_.setMatchScoreThr(c_fMatchPercentThr_);
        std::cout <<"hsq loop2map_align.hpp c_fMatchPercentThr_ = " << c_fMatchPercentThr_ << std::endl;
    }

    /**
     * @brief 设置AMCL参数
     * @param p_fAmclPara
     *
     */
    void setAmclPara(const AMCL_param p_fAmclPara)
    {
        c_stAMCLPara_ = p_fAmclPara;
    }

    /**
     * @brief 根据面点匹配平均距离评价优化是否正确
     * @code
     *
     * @endcode
     * @return [true] \n
     * [优化正确]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [优化错误]
     *
     */
    bool checkDistance(bool p_bIsCheck = false);
    /**
     * @brief 设置优化次数
     * @param p_iOptTimes
     *
     */
    void setOptTimes(const int p_iOptTimes);
    /**
     * @brief Set the Input Source object
     * @attention 此函数会拷贝当前帧内容到内部,以保证多个匹配器的并发能力
     * @param[in] p_pcIn 输入配准源(当前帧)
     */
    void setInputSource(KeyFrameFeaturePtr p_pcIn, s_POSE6D p_pPose);

    /**
     * @brief Set the Initial Transformation object
     *
     * @param[in] p_pEstamiteTrans 初始优化增量
     */
    void setEstimateTrans(s_POSE6D p_pEstamiteTrans)
    {
        //设置优化预估位姿
        c_Matcher_.setTransform(p_pEstamiteTrans.m_quat, p_pEstamiteTrans.m_trans);
    }

    /**
     * @brief Set the Transformation Epsilon
     * @note  迭代终止条件:当单次优化增量低于阈值,停止迭代
     * @param[in] p_fDxy 水平优化梯度阈值
     * @param[in] p_fDz 高程优化梯度阈值
     * @param[in] p_fDyaw 偏航角优化梯度阈值
     */
    void setTransformationEpsilon(float p_fDxy, float p_fDyaw)
    {
        c_fEpsilon_[0] = p_fDxy;
        c_fEpsilon_[1] = p_fDyaw;
    }

    /**
     * @brief 设置平面优化模式
     * @note 强行消除优化增量的Roll、Pitch和Z值
     *
     */
    void setXOYOptimize(const bool p_bXOYOpt, const float p_fXOYOptZ = 0.0)
    {
        c_bXOYOpt_ = p_bXOYOpt;
        c_fXOYOptZ_ = p_fXOYOptZ;
    }

    /**
     * @brief 获得匹配得分
     * @todo 当前匹配器没有可信赖得分回馈,目前使用面点匹配点平均距离
     * @return [float] [配准得分]
     */
    float getFitnessScore()
    {
        return c_fAlignScore_;
    }

    /**
     * @brief 获取全局点云与全局地图的优化增量
     *
     * @return [s_POSE6D] [获取配准结果:优化增量]
     */
    s_POSE6D getFinalTransformation()
    {
        return c_stOptTrans_;
    }
    /**
     * @brief 设置SC预估位置
     * @param p_stSCPose
     *
     */
    void setSCEstimate(s_POSE6D p_stSCPose)
    {
        c_stSCPose_ = p_stSCPose;
    }

    L2M_Align();
    /**
     * @brief 输入地图和当前地图帧
     * @param p_pcIn
     * @param p_pcIn_oneFrame
     * @param p_pPose
     *
     */
    void setInputTarget(MapFrameFeaturePtr& p_pcIn,
                        MapFrameFeaturePtr& p_pcIn_oneFrame,
                        s_POSE6D p_pPose);
    void setVerifyThreshold(float p_fMatchNumPercent, float p_fMatchOccupyScore);
    /**
     * @brief 执行优化
     * @code
     *
     * @endcode
     * @return [true] \n
     * [优化成功,有优化增量输出]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [优化失败,无优化增量输出]
     *
     */
    bool align();
};

_T_CM_ L2M_Align<C, M>::L2M_Align()
    : c_stSlamParam_(SYSPARAM::getIn()->m_slam), c_stLocateParam_(SYSPARAM::getIn()->m_loct),
      c_stCheckParam_(SYSPARAM::getIn()->m_posCheck), c_pSrc_(nullptr), c_pTrg_Map(nullptr),
      c_pTrg_(nullptr), c_stOptTrans_(s_POSE6D()), c_stSCPose_(s_POSE6D()),
      c_bSampleSurfMatch_(true), c_bXOYOpt_(false), c_fXOYOptZ_(0), c_fEpsilon_{0.001, 0.01},
      c_fAlignScore_(0), c_iOptTimes_(15), c_fNumPercentMap_(100), c_fOccupyScoreMap_(100),
      c_bAMCLModle_(false), c_fDistanceThr_(0.065), c_fMatchPercentThr_(0.35),
      c_fVerifyPercentThr_(0.6), c_fVerifyScoreThr_(0.7)
{
    c_stSysParam_ = SYSPARAM::getIn();
    // match
    c_Matcher_.setMaxIterations(1);  //设置迭代次数
    c_Matcher_.setMatch2D(true);     //设置角点2D匹配功能
    //设置点到线、面距离阈值
    c_Matcher_.setDistThreshold(c_stSysParam_->m_loct.m_match.m_fMaxDist);
    //设置角点2D搜索半径
    c_Matcher_.setCornerSearchR(c_stSysParam_->m_loct.m_match.m_fLine2DRadius);
    //设置靶标搜索半径
    c_Matcher_.setMarkSearchR(c_stSysParam_->m_loct.m_match.m_fLine2DMarkRadius);
    //设置角点2D匹配有效高差范围
    c_Matcher_.setZAxisThreshold(c_stSysParam_->m_loct.m_match.m_fLineMaxZDiff);
    //设置角点2D匹配最小点数
    c_Matcher_.setSearchNumThreshold(c_stSysParam_->m_loct.m_match.m_uiLineMinPoints);
    //设置面点k近邻搜索
    c_Matcher_.setSearchK(c_stSysParam_->m_loct.m_match.m_uiPlaneMaxPoints);
    //设置棚顶与地面高度阈值
    c_Matcher_.setwallhigh(c_stSysParam_->m_map.m_fGroundHigh, c_stSysParam_->m_map.m_fRoofHigh);
    //设置匹配与优化模块选择Kdtree、iVox
    c_Matcher_.setOptimizeModel(c_stSysParam_->m_map.m_iOptimizeModel);
    //设置双层墙过滤Z值高度阈值
    c_Matcher_.setFilterZValue(c_stSysParam_->m_map.m_fFilterZValue);
    //设置模式用来判定iVox需要计算的点数
    c_Matcher_.setWorkModel(c_stSysParam_->m_iWorkMode);
    //区分靶标导航还是slam导航
    c_Matcher_.setSlamModel(c_stSysParam_->m_iSlamMode);
    //设置靶标权重最小值与最大值
    c_Matcher_.setMarkWight(c_stSysParam_->m_bMarkWeightMin, c_stSysParam_->m_bMarkWeightMax);
    //设置面点搜索到的N点的最大距离差
    c_Matcher_.setRadiusThreshold(c_stSysParam_->m_loct.m_match.m_fPlaneMaxRadius);
    //匹配内部点数得分将为安全阈值0.1
    std::cout<<"loop2map_align.hpp setMatchScoreThr : "<<c_stSysParam_->m_posCheck.m_fKdMatchNumPercent << std::endl;
    c_Matcher_.setMatchScoreThr(
        c_stSysParam_->m_posCheck.m_fKdMatchNumPercent);
    //与location隔离参数,目前要求比例0.35以上
    // c_Matcher_.setMatchScoreThr(c_fMatchPercentThr_);

    // IVOX模式
    if (OptimizeMapType::IVOX_TYPE == c_stSysParam_->m_map.m_iOptimizeModel)
    {
        c_pIvoxGrid_.reset(new IVox<3, M>());
        c_pLaserVerify_.reset(new GridMapScore<C, M>(*c_stSysParam_));
    }
    else
    {
        LOGFAE(WERROR,
               "{} [loopAlign] 回环建图模式暂时不支持KD树匹配模式,请设置正确模式",
               WJLog::getWholeSysTime());
    }
    if (c_pIvoxGrid_)
    {
        //设置大栅格内部小栅格尺寸大小
        c_pIvoxGrid_->setInnerResolu(c_stSysParam_->m_map.m_fInGridResolu);
        //设置大栅格尺寸大小
        c_pIvoxGrid_->setOuterResolu(c_stSysParam_->m_map.m_fOutGridResolu);
        //设置搜索栅格内部点数
        c_pIvoxGrid_->setSearchPointNum(c_stSysParam_->m_map.m_iGridSearchNum);
        //设置iVox栅格容量大小
        c_pIvoxGrid_->setGridCapacity(c_stSysParam_->m_map.m_iGridSize);
        //设置栅格周围栅格环绕类型
        c_pIvoxGrid_->setGridNearbyType(c_stSysParam_->m_map.m_iGridMatchType);
        //栅格内部初始化
        c_pIvoxGrid_->gridInit();
        // 设置搜索函数
        c_Matcher_.setIVoxGrid(c_pIvoxGrid_);
        if (c_pLaserVerify_)
        {
            //设置高斯分布概率标准差(与栅格尺寸大小相关)
            c_pLaserVerify_->setGuassSize(c_stSysParam_->m_posCheck.m_fVerifyGauss);
            c_pLaserVerify_->setTargetGridMap(c_pIvoxGrid_);
            c_pLaserVerify_->setGroundAndRoofHigh(c_stSysParam_->m_map.m_fGroundHigh,
                                                  c_stSysParam_->m_map.m_fRoofHigh);
        }
    }
}
_T_CM_ void L2M_Align<C, M>::setOptTimes(const int p_iOptTimes)
{
    if (p_iOptTimes > 0)
    {
        c_iOptTimes_ = p_iOptTimes;
    }
    else
    {
        LOGFAE(WERROR,
               "{} [loopAlign] 设置优化次数小于0,设置失败,当前匹配次数:{}",
               WJLog::getWholeSysTime(),
               c_iOptTimes_);
    }
}

_T_CM_ void L2M_Align<C, M>::setVerifyThreshold(float p_fMatchNumPercent, float p_fMatchOccupyScore)
{
    if (p_fMatchNumPercent > 0.0)
        c_fVerifyPercentThr_ = p_fMatchNumPercent;
    if (p_fMatchOccupyScore > 0.0)
        c_fVerifyScoreThr_ = p_fMatchOccupyScore;
}
_T_CM_ void L2M_Align<C, M>::setInputTarget(MapFrameFeaturePtr& p_pcIn,
                                            MapFrameFeaturePtr& p_pcIn_oneFrame,
                                            s_POSE6D p_pPose)
{
    // 设置配准目标: 指针传递
    if (p_pcIn && c_pIvoxGrid_ && p_pcIn_oneFrame)
    {
        c_pTrg_.reset(new KeyFrame());
        c_pTrg_Map.reset(new MapFrameFeature());
        c_pTrg_->m_pFeature = p_pcIn_oneFrame;
        c_pTrg_->m_Pose = p_pPose;
        c_pTrg_Map = p_pcIn;
        // 新建地图
        c_Matcher_.setInputTargetCorner(c_pTrg_Map->first);
        c_pIvoxGrid_->buildGridMap(c_pTrg_Map->second->points, c_pTrg_Map->first->points, 3);
        //计算地图匹配得分作为概率基准
        c_pLaserVerify_->setInputSourceCloud(c_pTrg_->m_pFeature->second,
                                             c_pTrg_->m_pFeature->first);
        c_pLaserVerify_->setInputCurrentPose(c_pTrg_->m_Pose);
        c_pLaserVerify_->calcuLaserVerifyScore();
        c_fOccupyScoreMap_ = c_pLaserVerify_->getLaserVerifyScore();
        c_fNumPercentMap_ = c_pLaserVerify_->getLaserVerifyMatNum();
        // LOGM(WINFO, "MAP VERIFY SCORE{},MATCH PERCENT{}", c_fOccupyScoreMap_, c_fNumPercentMap_);
    }
    else
    {
        LOGFAE(WERROR,
               "{} [loopAlign] 回环建图模式暂时不支持KD树匹配模式,请设置正确模式后输入",
               WJLog::getWholeSysTime());
    }
}

_T_CM_ bool L2M_Align<C, M>::align()
{
    TicToc l_TicToc;
    if (nullptr == c_pSrc_)
    {
        return false;
    }
    //保留原始点云
    KeyFramePtr l_pSrc_;
    l_pSrc_.reset(new KeyFrame());
    *l_pSrc_->m_pFeature->first = *c_pSrc_->m_pFeature->first;
    *l_pSrc_->m_pFeature->second = *c_pSrc_->m_pFeature->second;
    *l_pSrc_->m_pFeature->third = *c_pSrc_->m_pFeature->third;
    l_pSrc_->m_pFeature->m_iSample2ndSize = c_pSrc_->m_pFeature->m_iSample2ndSize;
    l_pSrc_->m_pFeature->m_uiScanFrame = c_pSrc_->m_pFeature->m_uiScanFrame;
    l_pSrc_->m_Pose = c_pSrc_->m_Pose;

    KeyFramePtr l_pRawMap_;
    l_pRawMap_.reset(new KeyFrame());
    *l_pRawMap_->m_pFeature->first = *c_pTrg_Map->first;
    *l_pRawMap_->m_pFeature->second = *c_pTrg_Map->second;
    *l_pRawMap_->m_pFeature->third = *c_pTrg_Map->third;
    l_pRawMap_->m_Pose = c_pTrg_->m_Pose;
    // 初始状态
    bool l_bRes = false;
    // 手动模式
    bool l_bTryManual = false;
    // 原始Pose
    s_POSE6D l_rawPose = c_pSrc_->m_Pose;
    // 增量判定组
    s_POSE6D l_stFakePoseLast = c_pSrc_->m_Pose;
    s_POSE6D l_stFakePose = c_pSrc_->m_Pose;
    // amcl置位
    c_bAMCLModle_ = false;
    double l_normXY = 0;
    // 清空缓存
    c_stOptTrans_.reset();
    // 是否开启2D模式
    setXOYOptimize(c_stSlamParam_.optimiz_o3D);
    for (size_t i = 0; i <= c_iOptTimes_; ++i)
    {
        if (i == c_iOptTimes_)
        {
            if (!checkDistance())
            {
                if (c_bAMCLModle_)
                {
                    l_bRes = false;
                    break;
                }
                else
                {
                    c_bAMCLModle_ = true;
                    //恢复原始信息
                    *c_pSrc_->m_pFeature->first = *l_pSrc_->m_pFeature->first;
                    *c_pSrc_->m_pFeature->second = *l_pSrc_->m_pFeature->second;
                    *c_pSrc_->m_pFeature->third = *l_pSrc_->m_pFeature->third;
                    c_pSrc_->m_pFeature->m_iSample2ndSize = l_pSrc_->m_pFeature->m_iSample2ndSize;
                    c_pSrc_->m_pFeature->m_uiScanFrame = l_pSrc_->m_pFeature->m_uiScanFrame;
                    c_pSrc_->m_Pose = l_pSrc_->m_Pose;
                    amclMode_();
                    i = 0;
                }
                // std::cout << "hsq: lool2map_align.hpp align(), l_bRes = false" << std::endl;
            }
            else
            {
                l_bRes = true;
                // std::cout << "hsq: lool2map_align.hpp align(), l_bRes = true" << std::endl;
                break;
            }
        }
        if (!c_bAMCLModle_ && 0 == i)//回环匹配前先拉一下高度差
        {
            s_POSE6D l_stHeight;
            double l_dDeltaZ = c_pTrg_->m_Pose.z() - c_pSrc_->m_Pose.z();
            l_stHeight.setXYZ(0, 0, l_dDeltaZ);
            setEstimateTrans(l_stHeight);
        }
        else
            setEstimateTrans(s_POSE6D());
        // 根据匹配次数切换参数
        switchSetMatch_(i, l_bTryManual);
        // 匹配成功情况： 准备精确配准 || 验证优化增量 || 退出
        s_POSE6D l_stOptOnce;
        //计算单次优化增量
        l_bRes = optimiz_(l_stOptOnce, l_bTryManual);
        // std::cout << "hsq: lool2map_align.hpp optimiz_(), l_bRes = " << l_bRes<< std::endl;
#ifdef gtest
        if (!c_bAMCLModle_)
            l_bRes = false;
#endif
        // 根据单次优化增量转移结果
        if (l_bRes)
        {
            renewFrame_(l_stOptOnce);
            // 更新此次虚拟位置
            l_stFakePose = c_stOptTrans_ * l_rawPose;
            // 位姿增量
            Eigen::VectorXd l_delta = l_stFakePose.coeffs_6() - l_stFakePoseLast.coeffs_6();
            // XY增量
            double l_normXY = sqrt(pow(l_delta[0], 2) + pow(l_delta[1], 2));
            // 记录上次结果
            l_stFakePoseLast = l_stFakePose;
            // 如果不是第一次优化且优化增量极小,提前退出
            // if (i > 0 && l_normXY < c_fEpsilon_[0] && std::fabs(l_delta[5]) < c_fEpsilon_[1])
            // {
            //     if (checkDistance())
            //     {
            //         LOGM(WDEBUG, "[loopAlign] Minimal optimization increment, exit!");
            //         break;
            //     }
            // }

            if (l_bTryManual)
            {
                LOGM(WDEBUG,
                     "{} [loopAlign] [{}]:{} opti TryManual success, change accuracy mode,AMCL "
                     "mode:{}",
                     WJLog::getWholeSysTime(),
                     i,
                     c_iOptTimes_,
                     c_bAMCLModle_);
                l_bTryManual = false;
            }
        }
        else  // 匹配失败情况： 准备粗配准 || 继续粗配准 || 退出
        {
            if (i < 2)
            {
                // 精确配准失败：切换粗略匹配
                l_bTryManual = true;
            }
            else if (!c_bAMCLModle_)
            {
                c_bAMCLModle_ = true;
                // renewFrame_(c_stOptTrans_.inverse());
                // c_pSrc_ = l_pSrc_;
                *c_pSrc_->m_pFeature->first = *l_pSrc_->m_pFeature->first;
                *c_pSrc_->m_pFeature->second = *l_pSrc_->m_pFeature->second;
                *c_pSrc_->m_pFeature->third = *l_pSrc_->m_pFeature->third;
                c_pSrc_->m_pFeature->m_iSample2ndSize = l_pSrc_->m_pFeature->m_iSample2ndSize;
                c_pSrc_->m_pFeature->m_uiScanFrame = l_pSrc_->m_pFeature->m_uiScanFrame;
                c_pSrc_->m_Pose = l_pSrc_->m_Pose;
                amclMode_();
                i = 0;
            }
            else
            {
                LOGM(WDEBUG, "{} [loopAlign] Optimize too many times", WJLog::getWholeSysTime());
                break;
            }
        }
    }

    if (l_bRes)
    {
        if (c_bAMCLModle_)
        {
            c_stOptTrans_ = c_pTrg_->m_Pose * c_stOptTrans_ * (l_rawPose.inverse());
            //转回全局,准备校验
            //恢复原始点云以及标签HSV
            *c_pSrc_->m_pFeature->first = *l_pSrc_->m_pFeature->first;
            *c_pSrc_->m_pFeature->second = *l_pSrc_->m_pFeature->second;
            *c_pSrc_->m_pFeature->third = *l_pSrc_->m_pFeature->third;
            c_pSrc_->m_pFeature->m_iSample2ndSize = l_pSrc_->m_pFeature->m_iSample2ndSize;
            c_pSrc_->m_pFeature->m_uiScanFrame = l_pSrc_->m_pFeature->m_uiScanFrame;
            c_pSrc_->m_Pose = l_pSrc_->m_Pose;

            //转回原始地图,保留原始地图标签信息
            *c_pTrg_Map->first = *l_pRawMap_->m_pFeature->first;
            *c_pTrg_Map->second = *l_pRawMap_->m_pFeature->second;
            *c_pTrg_Map->third = *l_pRawMap_->m_pFeature->third;
            c_Matcher_.setInputTargetCorner(c_pTrg_Map->first);
            s_POSE6D l_stResult = c_stOptTrans_;
            renewFrame_(l_stResult);
            //防止renewFrame_函数中修改c_stOptTrans_
            c_stOptTrans_ = l_stResult;
        }
        // todo: 当前帧若经过amcl需要做概率校验吗,标签问题可能导致概率校验不通过
        if (!c_bAMCLModle_)
            l_bRes = checkOccupyVerify_();
    }
    if (!l_bRes)
    {
        //否则重置优化增量,防止错误输出
        c_stOptTrans_.reset();
        c_pSrc_ = nullptr;
    }

    LOGM(WINFO,
         "{} [loopAlign] check result:{},AMCL:{}  USE Time:{} ",
         WJLog::getWholeSysTime(),
         l_bRes,
         c_bAMCLModle_,
         l_TicToc.toc());
    return l_bRes;
}

_T_CM_ bool L2M_Align<C, M>::amclMode_()
{
    TicToc l_TicToc;
    boost::shared_ptr<amcl_loop::AMCL<C>> amcl_; /**< AMCL方法类*/
    trans2Base_(c_pTrg_->m_Pose, c_pTrg_Map);
    trans2Base_(c_pTrg_->m_Pose, c_pTrg_->m_pFeature);
    trans2Base_(c_pSrc_->m_Pose, c_pSrc_->m_pFeature);
    c_pSrc_->m_Pose.reset();
    c_Matcher_.setInputTargetCorner(c_pTrg_Map->first);
    c_pIvoxGrid_->buildGridMap(c_pTrg_Map->second->points, c_pTrg_Map->first->points, 3);
    amcl_.reset(new amcl_loop::AMCL<C>());
    amcl_->setTargetGridMap(c_pIvoxGrid_, c_pTrg_->m_pFeature);
    amcl_->setInputSourceCloud(c_pSrc_->m_pFeature->first, c_pSrc_->m_pFeature->second);
    //输入预估原始位姿
    amcl_->setInitialPose(c_stSCPose_);
    //输入采样范围
    amcl_->setFilterFile(c_stAMCLPara_.m_fDeltaX,
                         c_stAMCLPara_.m_fDeltaY,
                         c_stAMCLPara_.m_fDeltaZ,
                         c_stAMCLPara_.m_fDeltaA);
    amcl_->setFilterTimes(c_stAMCLPara_.m_iFiltTimes);
    amcl_->setParticleNum(c_stAMCLPara_.m_iParticleNums);
    amcl_->filter();
    s_POSE6D l_resultPose;
    amcl_->getOptimalPose(l_resultPose);
    c_stOptTrans_.reset();
    //根据AMCL结果更新局部点云以及局部匹配优化增量
    renewFrame_(l_resultPose);
    LOGM(WINFO,
         "{} [loopAlign] AMCL filter time:{},particle nums:{},USE Time:{}",
         WJLog::getWholeSysTime(),
         c_stAMCLPara_.m_iFiltTimes,
         c_stAMCLPara_.m_iParticleNums,
         l_TicToc.toc());

#ifdef gtest
    // s_POSE6D opt;
    // opt = l_resultPose * c_stSCPose_.inverse();
    // opt.printf("amcl delta");
    // static int iii = 0;
    // iii++;
    // pcl::io::savePCDFile<pcl::PointXYZHSV>(
    //     "/home/<USER>/data/lrz_ws/src/wanji_slam/data/Map/loop_gtest/amcl_"
    //         + std::to_string(c_pSrc_->m_pFeature->m_uiScanFrame) + ".pcd",
    //     *(c_pSrc_->m_pFeature->second));
    // pcl::io::savePCDFile<pcl::PointXYZHSV>(
    //     "/home/<USER>/data/lrz_ws/src/wanji_slam/data/Map/loop_gtest/amcl_"
    //         + std::to_string(c_pSrc_->m_pFeature->m_uiScanFrame) + "_map"
    //         + std::to_string(c_pTrg_->m_pFeature->m_uiScanFrame) + "map.pcd",
    //     *(c_pTrg_Map->second));
#endif
    return true;
}

_T_CM_ void L2M_Align<C, M>::switchSetMatch_(int i, bool p_TryMode)
{
    if (p_TryMode)
    {
        setMatch_(c_stLocateParam_.m_manual);
        LOGM(WTRACE,
             "{} [loopAlign] {}:{} opti TryManual.",
             WJLog::getWholeSysTime(),
             i,
             c_iOptTimes_);
    }
    else if (i < 3)
    {
        LOGM(WTRACE,
             "{} [loopAlign] {}:{} opti LocatMode.",
             WJLog::getWholeSysTime(),
             i,
             c_iOptTimes_);
        setMatch_(c_stLocateParam_.m_match);
    }
    else if (i < 8)
    {
        // 入此阶段需要注意i<8
        LOGM(WTRACE,
             "{} [loopAlign] {}:{} opti SLAMMode.",
             WJLog::getWholeSysTime(),
             i,
             c_iOptTimes_);
        setMatch_(c_stSlamParam_.m_match[i - 3]);
    }
    else
    {
        // todo: 增加另外的可能?
    }
}
_T_CM_ bool L2M_Align<C, M>::checkDistance(bool p_bIsCheck)
{
    c_fAlignScore_ = 0;
    // TODO:因为标签问题,经匹配后的局部点云无法参与概率校验
    s_POSE6D l_stOpttest;
    float l_fAveDistance = 10;
    //进优化起计算匹配点数与得分
    bool l_bCheckRes = false;
    if (optimiz_(l_stOpttest, 0))  ///*&& checkOccupyVerify_()*/
    {
        l_fAveDistance = c_Matcher_.getAveDistance();
        if (l_fAveDistance < c_fDistanceThr_)
        {
            l_bCheckRes = true;
            if (p_bIsCheck)
            {
                printf("回环帧校验:\n");
                l_bCheckRes = checkOccupyVerify_();
            }
        }
        else
        {
            l_bCheckRes = false;
        }
        float l_fNumberPercent = c_Matcher_.getMatchScore();
        std::cout<<"hsq:l2m.hpp checkDistance l_fNumberPercent = " << l_fNumberPercent << std::endl;
        LOGM(WINFO,
             "{} [loopAlign]  opt check [{}] with avedis:{} / {},and MatchPercent:{}",
             WJLog::getWholeSysTime(),
             l_bCheckRes,
             l_fAveDistance,
             c_fDistanceThr_,
             l_fNumberPercent);
    }
    else
        l_bCheckRes = false;

    c_fAlignScore_ = 1 - l_fAveDistance;
    std::cout<<"hsq: l2m.hpp checkDistance l_bCheckRes = " << l_bCheckRes <<", l_fAveDistance = " << l_fAveDistance 
        <<", c_fAlignScore_ = " << c_fAlignScore_<< std::endl;
    return l_bCheckRes;
}

_T_CM_ bool L2M_Align<C, M>::checkOccupyVerify_()
{
    //角点+面点实现位姿校验
    c_pLaserVerify_->setInputSourceCloud(c_pSrc_->m_pFeature->second, c_pSrc_->m_pFeature->first);
    //  对于IVOX校验此处只用于计算地面棚顶，对于KD校验需要注意坐标系
    c_pLaserVerify_->setInputCurrentPose(c_pSrc_->m_Pose);
    c_pLaserVerify_->calcuLaserVerifyScore();
    float l_fAlignScore = c_pLaserVerify_->getLaserVerifyScore();
    float l_fMatchNumPercent = c_pLaserVerify_->getLaserVerifyMatNum();

    {
        LOGM(WINFO,
             "verify score to Map:{}/{} , NumPercent TO map:{}/{}",
             l_fAlignScore / c_fOccupyScoreMap_,
             c_fVerifyScoreThr_,
             l_fMatchNumPercent / c_fNumPercentMap_,
             c_fVerifyPercentThr_);
        std::cout<<"hsq: l_fAlignScore = " << l_fAlignScore 
            <<", c_fOccupyScoreMap_ = " << c_fOccupyScoreMap_ 
            <<", c_fVerifyScoreThr_ = " << c_fVerifyScoreThr_ 
            <<", l_fMatchNumPercent = " << l_fMatchNumPercent 
            <<", c_fNumPercentMap_ = " << c_fNumPercentMap_
            <<", c_fVerifyPercentThr_ = " << c_fVerifyPercentThr_ << std::endl;
        if (l_fMatchNumPercent / c_fNumPercentMap_ < c_fVerifyPercentThr_
            || l_fAlignScore / c_fOccupyScoreMap_ < c_fVerifyScoreThr_){
            std::cout <<"hsq: checkOccupyVerify false" << std::endl;
            return false;
        }
        else{
            std::cout <<"hsq: checkOccupyVerify true" << std::endl;
            return true;
        }
    }
}

_T_CM_ void L2M_Align<C, M>::setInputSource(KeyFrameFeaturePtr p_pcIn, s_POSE6D p_pPose)
{
    if (nullptr != p_pcIn)
    {  //深拷贝
        c_pSrc_.reset(new KeyFrame());
        *c_pSrc_->m_pFeature->first = *p_pcIn->first;
        *c_pSrc_->m_pFeature->second = *p_pcIn->second;
        *c_pSrc_->m_pFeature->third = *p_pcIn->third;
        c_pSrc_->m_pFeature->m_iSample2ndSize = p_pcIn->m_iSample2ndSize;
        c_pSrc_->m_pFeature->m_uiScanFrame = p_pcIn->m_uiScanFrame;
        // 设置位姿，用于零点优化和墙面过滤
        c_pSrc_->m_Pose = p_pPose;

        c_Matcher_.setInputSourceCorner(c_pSrc_->m_pFeature->first);
        if (c_bSampleSurfMatch_)
            c_Matcher_.setInputSourceSurfaceSample(c_pSrc_->m_pFeature->second,
                                                   c_pSrc_->m_pFeature->m_iSample2ndSize);
        else
            c_Matcher_.setInputSourceSurface(c_pSrc_->m_pFeature->second);
        c_Matcher_.setInputSourceMark(c_pSrc_->m_pFeature->third);
    }
    else
    {
        LOGFAE(WERROR, "{} [loopAlign] 输入待配准点云指针为空!", WJLog::getWholeSysTime());
    }
}

_T_CM_ void L2M_Align<C, M>::setMatch_(s_MatcherConfig& p_param)
{
    //设置模式用来判定iVox需要计算的点数
    c_Matcher_.setWorkModel(c_stSysParam_->m_iWorkMode);
    // 设置点到线、面距离阈值
    c_Matcher_.setDistThreshold(p_param.m_fMaxDist);
    // 设置角点2D搜索半径
    c_Matcher_.setCornerSearchR(p_param.m_fLine2DRadius);
    // 设置角点2D匹配有效高差范围
    c_Matcher_.setZAxisThreshold(p_param.m_fLineMaxZDiff);
    // 设置角点2D匹配最小点数
    c_Matcher_.setSearchNumThreshold(p_param.m_uiLineMinPoints);
    // 设置面点k近邻搜索
    c_Matcher_.setSearchK(p_param.m_uiPlaneMaxPoints);
    // 设置面点搜索到的N点的最大距离差
    c_Matcher_.setRadiusThreshold(p_param.m_fPlaneMaxRadius);
    // 设置平面评价阈值
    c_Matcher_.setPlaneMeanDiffThr(p_param.m_fPlaneMeanDiff);
    // 设置平面有效阈值 PCA原理
    c_Matcher_.setPlanePcaThr((int)(p_param.m_vfPlanePCA[0]), p_param.m_vfPlanePCA[1]);
    // 设置靶标2D搜索半径
    // c_Matcher_.setMarkSearchR(p_param.m_fMarkLineRadius);
    // 设置靶标权重
    // c_Matcher_.setMarkWeights(p_param.m_iMarkWeight[0], p_param.m_iMarkWeight[1]);
    //设置平面有效阈值 PCA原理
    if (c_stSysParam_->m_loct.m_match.m_vfPlanePCA.size() == 2)
        c_Matcher_.setPlanePcaThr((int)(c_stSysParam_->m_loct.m_match.m_vfPlanePCA[0]),
                                  c_stSysParam_->m_loct.m_match.m_vfPlanePCA[1]);
}

_T_CM_ bool L2M_Align<C, M>::optimiz_(s_POSE6D& p_stOptOnce, bool p_bManualType)
{
    p_stOptOnce.reset();
    // 零点优化转移
    c_Matcher_.setMovePose(c_pSrc_->m_Pose.m_trans);
    //注意:这里的返回值不能代表真实情况,比例大于设定值则返回成功
    //输入分别为:待填充quat/trans type scanid 和是否忽略得分(即使忽略,也需要35%以上)
    c_Matcher_.setCurrentPose(c_pSrc_->m_Pose.m_trans);
    bool l_bRes = c_Matcher_.align(p_stOptOnce.m_quat, p_stOptOnce.m_trans, 1, 1, p_bManualType);
    std::cout << "hsq: l2m.hpp ceres optimise finished, l_bRes = " << l_bRes << std::endl;
    // 伪平面模式
    if (c_bXOYOpt_)
    {
        p_stOptOnce.m_trans.z() = c_fXOYOptZ_;
        p_stOptOnce.nomalize();
        p_stOptOnce.setRPY(0, 0, c_stOptTrans_.yaw());
    }
    return l_bRes;
}

_T_CM_ void L2M_Align<C, M>::renewFrame_(s_POSE6D p_stOptOnce)
{
    if (c_trans.isEffectiveTransform(p_stOptOnce.m_quat, p_stOptOnce.m_trans))
    {
        // 根据单次优化增量更新总优化增量
        c_stOptTrans_ = p_stOptOnce * c_stOptTrans_;
        // 根据单次优化增量更新预估位姿
        c_pSrc_->m_Pose = p_stOptOnce * c_pSrc_->m_Pose;
        // 根据单次优化增量更新点云
        c_trans.transformCloudPoints(p_stOptOnce.m_quat,
                                     p_stOptOnce.m_trans,
                                     c_pSrc_->m_pFeature->first,
                                     c_pSrc_->m_pFeature->first);
        c_trans.transformCloudPoints(p_stOptOnce.m_quat,
                                     p_stOptOnce.m_trans,
                                     c_pSrc_->m_pFeature->second,
                                     c_pSrc_->m_pFeature->second);
        c_trans.transformCloudPoints(p_stOptOnce.m_quat,
                                     p_stOptOnce.m_trans,
                                     c_pSrc_->m_pFeature->third,
                                     c_pSrc_->m_pFeature->third);
        //更新地图
        for (size_t i = 0; i < c_pTrg_Map->second->points.size(); i++)
        {
            c_pTrg_Map->second->points[i].h =
                c_Matcher_.getNormalAngle(c_pSrc_->m_Pose.x() - c_pTrg_Map->second->points[i].x,
                                          c_pSrc_->m_Pose.y() - c_pTrg_Map->second->points[i].y);
        }
        c_pIvoxGrid_->buildGridMap(c_pTrg_Map->second->points, c_pTrg_Map->first->points, 3);
#ifdef gtest
        // static int ii = 0;
        // ii++;
        // pcl::io::savePCDFile<pcl::PointXYZHSV>(
        //     "/home/<USER>/data/lrz_ws/src/wanji_slam/data/Map/loop_gtest/匹配测试文件/"
        //         + std::to_string(ii) + ".pcd",
        //     *(c_pSrc_->m_pFeature->second));
#endif
    }
}
}  // namespace wj_slam