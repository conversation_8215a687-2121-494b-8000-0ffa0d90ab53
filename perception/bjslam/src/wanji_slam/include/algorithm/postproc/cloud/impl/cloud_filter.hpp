/**
 * @file cloud_filter.hpp
 * <AUTHOR> Li
 * @brief 地图后处理实现
 * @version 0.1
 * @date 2023-07-10
 *
 * @copyright Copyright (c) 2023
 *
 */
#pragma once
#include "../cloud_filter.h"
#include <boost/bind.hpp>
#include <boost/function.hpp>
#include <pcl/io/pcd_io.h>

namespace wj_cfilter {
template <typename PointT> void CloudFilterBase<PointT>::setSampleMethod(const SampleMethod& method)
{
    c_sampleMethod_ = method;
    switch (c_sampleMethod_)
    {
        case SampleMethod::Octree:
            c_sample_.reset(new Sample_Octree<PointT>());
            c_sample_->setLeafSize(c_fGrid_);
            c_sample_->setOriginSample(false);
            break;
        case SampleMethod::Voxel:
            c_sample_.reset(new Sample_Voxel<PointT>());
            c_sample_->setLeafSize(c_fGrid_);
            c_sample_->setOriginSample(false);
            break;
        case SampleMethod::Octree_Ori:
            c_sample_.reset(new Sample_Octree<PointT>());
            c_sample_->setLeafSize(c_fGrid_);
            c_sample_->setOriginSample(true);
            break;
        case SampleMethod::Voxel_Ori:
            c_sample_.reset(new Sample_Voxel<PointT>());
            c_sample_->setLeafSize(c_fGrid_);
            c_sample_->setOriginSample(true);
            break;
        default:
            std::cerr << "setSampleMethod Outside : use default Voxel/" << c_fGrid_ << std::endl;
            c_sample_.reset(new Sample_Voxel<PointT>());
            c_sample_->setLeafSize(c_fGrid_);
            c_sample_->setOriginSample(false);
            break;
    }
}
template <typename PointT> void CloudFilterBase<PointT>::setFilterMethod(const FilterMethod& method)
{
    c_filterMethod_ = method;
}
template <typename PointT>
void CloudFilter_Linearity<PointT>::setLinarityMethod(const CheckLinarityMode& method)
{
    c_checkLineMode_ = method;
    switch (c_checkLineMode_)
    {
        case CheckLinarityMode::LinearMode_PCA:
            c_checkLine_.reset(new checkLine_PCA<PointT>());
            break;
        case CheckLinarityMode::LinearMode_Noraml:
            c_checkLine_.reset(new checkLine_Normal<PointT>());
            break;
        default: c_checkLine_.reset(new checkLine_PCA<PointT>()); break;
    }
}
template <typename PointT> void CloudFilter_Linearity<PointT>::segmentCloud_(void)
{
    /******对原始角点点云进行统计滤波******/
    /**
     * StatisticalOutlierRemoval方法去除离群点
     */
    // 创建滤波对象
    pcl::StatisticalOutlierRemoval<PointT> sor;
    sor.setInputCloud(c_pPc_);
    // 设置平均距离估计的最近邻居的数量K
    sor.setMeanK(10);
    // 设置标准差阈值系数，值越小，滤波效果越强
    sor.setStddevMulThresh(1.0);
    // 执行过滤
    sor.filter(*c_pPc_);

    /******对原始角点点云进行垂长方体采样加速（voxel可能出界）******/
    // z方向采样
    // c_sample_->setLeafSize(0.075, 0.075, 0.15);
    // c_sample_->setInputCloud(c_pPc_);
    // c_sample_->filter(*c_pPc_);
    // c_sample_->setLeafSize(c_fGrid_);

    /******使用2D搜索树进行平面距离聚类******/
    // 创建点表示方法为2D
    typename pcl::PointRepresentation2D<PointT>::ConstPtr pr2d(
        new pcl::PointRepresentation2D<PointT>);
    // 创建search方法
    typename pcl::search::KdTree<PointT>::Ptr tree(new pcl::search::KdTree<PointT>);
    // 临时分类序列
    pcl::IndicesClustersPtr l_clusters(new pcl::IndicesClusters);
    // 聚类方法
    pcl::CConditionalEuclideanClustering<PointT> cec;
    // 最终结果重置
    c_clusters_.reset(new pcl::IndicesClusters);

    // 设置搜索方法
    tree->setPointRepresentation(pr2d);
    tree->setInputCloud(c_pPc_);
    // 使用平面距离KD搜索!
    cec.setSearchMethod(tree);
    cec.setInputCloud(c_pPc_);
    // 设置额外条件：生长时z不可跳跃性变化
    cec.setConditionFunction(std::function<bool(const PointT&, const PointT&, float)>([](const PointT& a, const PointT& b, float c) {
        // return pow(a.x - b.x, 2) + pow(a.y - b.y, 2) < c;
        return std::abs(a.z - b.z) < 0.4;
    }));
    // 设置核函数参数_3(搜索半径)
    cec.setClusterTolerance(0.1f);
    // 角点的最小尺寸
    cec.setMinClusterSize(4);
    cec.segment(*c_clusters_);

    // /******使用2D搜索树进行z距离分类******/
    // // 使用z距离差作为核函数
    // cec.setConditionFunction(
    //     std::function([](const PointT& a, const PointT& b, float c) { return a.z - b.z < c; }));
    // // 设置核函数参数_3(搜索半径)
    // cec.setClusterTolerance(0.68);
    // // 临时分类序列
    // pcl::IndicesClusters l_cluZ;
    // // 临时搜索序列
    // pcl::IndicesPtr l_pClu(new pcl::Indices);
    // // PointCloud pccopy1,pccopy2,pccopy;
    // for (auto clu : l_clusters)
    // {
    //     l_pClu->swap(clu.indices);
    //     cec.setIndices(l_pClu);
    //     cec.segment(l_cluZ);
    //     c_clusters_->insert(c_clusters_->end(), l_cluZ.begin(), l_cluZ.end());
    // }
}
template <typename PointT> bool CloudFilter_Linearity<PointT>::checkLine_(PointCloudPtr& pc)
{
    return c_checkLine_->checkLinealize(pc);
}
template <typename PointT>
bool CloudFilter_Linearity<PointT>::isUniformDistributionInZ_(PointCloudPtr& pc,
                                                              Eigen::Vector4f& min,
                                                              Eigen::Vector4f& max)
{
    // 无法应对断裂垂线
    // 按高度划分格子数量
    int picSize = 5;
    // 格子高度
    float sampleZ = (max[2] - min[2]) / float(picSize);
    int hitCnt = 0;
    // 每个格子的命中次数
    std::vector<int> hit(picSize, 0);
    int hitDiv = 0;
    for (size_t i = 0; i < pc->size(); i++)
    {
        // 击中的格子id
        hitDiv = int(floor((pc->points[i].z - min[2]) / sampleZ));
        if (hitDiv > 0 && hitDiv < picSize)
        {
            // 击中次数++
            hit[hitDiv] += 1;
            if (hit[hitDiv] > hitCnt)
                hitCnt = hit[hitDiv];
        }
    }
    // 如果某个格子的占用大于0.4 表明割裂分布
    if (hitCnt > 0.4 * pc->points.size())
        return false;
    return true;
}
template <typename PointT> void CloudFilter_Linearity<PointT>::applyFilter_(PointCloud& p_pcOut)
{
    PointCloudPtr pccopy(new PointCloud());
    PointCloudPtr pcouttemp(new PointCloud());
    Eigen::Vector4f min, max, diagonal;
    for (size_t cluit = 0; cluit < c_clusters_->size(); cluit++)
    {
        pcl::copyPointCloud(*c_pPc_, (c_clusters_->at(cluit)), *pccopy);
        // 将这个clu单独进行采样
        c_sample_->setInputCloud(pccopy);
        c_sample_->filter(*pcouttemp);

        // 如果要使用线性过滤类进行处理则进入
        // if (FilterMethod::Filter_Seg == c_filterMethod_)
        {
            pcl::getMinMax3D(*pcouttemp, min, max);
            diagonal = max - min;
            // 如果 z长度小于0.4 或 z方向不是最长方向(通过 竖直向的长方体) 则抛弃
            if (diagonal[2] < c_fSegmentSize_ || 0.5 * diagonal[2] < diagonal[1] + diagonal[0])
                continue;
            // 如果 水平宽度的平方 大于 c_fFilterOutSizeSq_ 则判断是否线性
            // 实际上当水平宽度极大时,主要情况是:多个垂线的合并分类,可能带大量蔓延错误点（TODO）
            // 也有可能是一团杂散点云（这里过滤）
            if ((pow(diagonal[0], 2) + pow(diagonal[1], 2)) > c_fFilterOutSizeSq_)
            {
                // 非线性 抛弃
                if (!checkLine_(pcouttemp))
                {
                    continue;
                }
            }
        }
        p_pcOut += *pcouttemp;
    }
}
template <typename PointT> void CloudFilter_Linearity<PointT>::filter(PointCloud& p_pcOut)
{
    // TicToc tic;
    copyCloud_(p_pcOut);
    // 点云的条件聚类
    segmentCloud_();
    // 下采样+（若需要）线性过滤
    applyFilter_(p_pcOut);
    c_pPc_ = nullptr;  //释放无用点云
    // std::cout << "CloudFilter_Linearity cost time : " << tic.toc() << std::endl;
}

template <typename PointT>
void CloudFilter_Plane<PointT>::extractGround(PointCloudPtr p_pPc,
                                              PointCloud& p_pPcGnd,
                                              PointCloud& p_pPcOff)
{
    if (!c_GPE_)
        c_GPE_.reset(new csf::CSF());
    // 点云拷贝
    typename csf::PointCloudT::Ptr c_pcGPE_(new csf::PointCloudT());
    pcl::copyPointCloud(*p_pPc, *c_pcGPE_);
    // 是否进行边坡后处理。当有陡变地形是设置为ture
    c_GPE_->c_stParams.m_bSloopSmooth = true;
    // 表示布料网格大小，网格大则刚性大，弹性小
    c_GPE_->c_stParams.m_fClothResolution = 1;
    // 3平地;2缓坡;1陡坡
    c_GPE_->c_stParams.m_iRigidness = 1;
    // 每次迭代下降,默认是0.65
    c_GPE_->c_stParams.m_fTimeStep = 0.65;
    // 将点云划分为模拟地形和非地面部分的阈值
    c_GPE_->c_stParams.m_fClassThreshold = 0.2;
    // 最大迭代次数
    c_GPE_->c_stParams.m_iInterations = 100;
    std::vector<int> c_vGroundList_;     // 地面点云索引
    std::vector<int> c_vOffGroundList_;  // 非地面点云索引
    // csf滤波输入点云
    c_GPE_->setPointCloud(c_pcGPE_);
    // 输出地面点和非地面点索引
    c_GPE_->do_filtering(c_vGroundList_, c_vOffGroundList_, false);
    pcl::copyPointCloud(*p_pPc, c_vGroundList_, p_pPcGnd);
    pcl::copyPointCloud(*p_pPc, c_vOffGroundList_, p_pPcOff);
}

template <typename PointT> void CloudFilter_Plane<PointT>::segmentCloud_(PointCloudPtr p_pPcOff)
{
    //todo：该函数实现实测比较耗时，暂时未启用
    p_pPcOff->height = 1;
    p_pPcOff->width = p_pPcOff->points.size();
    // 创建search方法
    typename pcl::search::KdTree<PointT>::Ptr tree(new pcl::search::KdTree<PointT>);
    tree->setInputCloud(p_pPcOff);

    std::vector<int> reserve_indices;                // 保留云团
    std::vector<pcl::PointIndices> cluster_indices;  // 临近云团结果
    pcl::EuclideanClusterExtraction<PointT> ec;      // 欧式聚类方法
    ec.setClusterTolerance(5 * c_fGrid_);            // 临近指标为5边长
    ec.setMinClusterSize(10);                        // 最小点数为10
    ec.setMaxClusterSize(p_pPcOff->points.size());   // 最大为全部
    ec.setSearchMethod(tree);                        // 搜索
    ec.setInputCloud(p_pPcOff);
    ec.extract(cluster_indices);  // 聚类
    for (auto it = cluster_indices.begin(); it != cluster_indices.end(); ++it)
        reserve_indices.insert(reserve_indices.end(), it->indices.begin(), it->indices.end());
    std::sort(reserve_indices.begin(), reserve_indices.end());
    pcl::copyPointCloud(*p_pPcOff, reserve_indices, *p_pPcOff);
}

template <typename PointT>
void CloudFilter_Plane<PointT>::octreeRemovePoints(PointCloudPtr p_pCloud, PointCloudPtr p_pOutput)
{
    //过滤条件 5米的子叶栅格内小于5个点
    pcl::octree::OctreePointCloud<pcl::PointXYZHSV> octree(5);

    octree.setInputCloud(p_pCloud);

    octree.addPointsFromInputCloud();
    // 构建Octree
    std::vector<int> vec_point_index, vec_total_index;  //  体素内点的索引，   要删除的点的索引
    // vector<pcl::octree::OctreeKey> vec_key;

    for (auto iter = octree.leaf_begin(); iter != octree.leaf_end(); ++iter)
    {
        auto key = iter.getCurrentOctreeKey();  //获取当前叶节点
        // vec_key.emplace_back(key);

        auto it_key = octree.findLeaf(key.x, key.y, key.z);  //根据叶节点坐标获取
        if (it_key != nullptr)
        {
            vec_point_index =
                iter.getLeafContainer().getPointIndicesVector();  //获取叶节点的点数索引容器
            if (vec_point_index.size() < 5)                       // 体素内点小于5时删除
            {
                for (size_t i = 0; i < vec_point_index.size(); i++)
                {
                    vec_total_index.push_back(vec_point_index[i]);
                }
            }
        }
    }

    // 使用pcl index 滤波
    pcl::PointIndices::Ptr outliners(new pcl::PointIndices());

    outliners->indices.resize(vec_total_index.size());

    for (size_t i = 0; i < vec_total_index.size(); i++)
    {
        outliners->indices[i] = vec_total_index[i];
    }
    pcl::ExtractIndices<pcl::PointXYZHSV> extract;
    extract.setInputCloud(p_pCloud);
    extract.setIndices(outliners);
    extract.setNegative(true);
    extract.filter(*p_pOutput);  // 输入输出可以是一个点云吗
}

template <typename PointT> void CloudFilter_Plane<PointT>::filter(PointCloud& p_pcOut)
{
    // TicToc tic;
    copyCloud_(p_pcOut);
    // c_pPcGnd_.reset(new PointCloud());
    // c_pPcOff_.reset(new PointCloud());
    // 单倍采样
    PointCloudPtr l_pfilterPc(new PointCloud());
    c_sample_->setLeafSize(c_fGrid_);
    c_sample_->setInputCloud(c_pPc_);
    c_sample_->filter(*l_pfilterPc);

    // 只有下采样
    // extractGround(c_pPc_, *c_pPcGnd_, *c_pPcOff_);
    // 地面上点云的欧式聚类过滤小体积点

    PointCloudPtr l_pSegPc(new PointCloud());
    octreeRemovePoints(l_pfilterPc, l_pSegPc);  //使用八叉树按点数过滤
    // segmentCloud_(c_pPc_);
    // 对地面使用更大采样率
    // c_sample_->setLeafSize(2 * c_fGrid_);
    // c_sample_->setInputCloud(c_pPcGnd_);
    // c_sample_->filter(p_pcOut);
    // p_pcOut += *c_pPcOff_;

    pcl::copyPointCloud(*l_pSegPc, p_pcOut);
    // p_pcOut = *c_pPc_;

    // std::cout << "CloudFilter_Plane cost time : " << tic.toc() << std::endl;
}

template <typename PointT> void CloudFilter_Plane<PointT>::filter2D(PointCloud& p_pcOut)
{
    if (c_pPc_ && !c_pPc_->empty())
    {
        p_pcOut.clear();
        c_sample_->setLeafSize(c_fGrid_);
        c_sample_->setInputCloud(c_pPc_);
        c_sample_->filter(*c_pPc_);
        for (PointT& p : c_pPc_->points)
            p.z = 0;
        pcl::copyPointCloud(*c_pPc_, p_pcOut);
    }
    else
    {
        std::cout << "[CloudFilter_Plane]:无输入点云,无法产生2D点云" << std::endl;
    }
    // for (PointT& p : c_pPcOff_->points)
    //     p.z = 0;
    // // // 单倍采样
    // c_sample_->setLeafSize(c_fGrid_);
    // c_sample_->setInputCloud(c_pPcOff_);
    // c_sample_->filter(p_pcOut);
    // std::cout << "CloudFilter_2D cost time : " << tic.toc() << std::endl;
};

}  // namespace wj_cfilter
