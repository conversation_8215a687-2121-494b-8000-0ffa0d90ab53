/**
 * @file cloud_filter.h
 * <AUTHOR> Li
 * @brief 地图后处理流程实现,同文件下包含基类和两个子类
 * @version 0.1
 * @date 2023-06-29
 *
 * @copyright Copyright (c) 2023
 *
 */
#pragma once
#include "algorithm/calibration/horizonAlign/CSF.h"
#include "algorithm/optimize/kdtree_flann2d.h"
#include "cloud_sample.h"
#include "conditional_euclidean_clustering.h"
#include "impl/checkLine.hpp"
#include <pcl/filters/radius_outlier_removal.h>
#include <pcl/filters/statistical_outlier_removal.h>
#include <pcl/point_cloud.h>
#include <pcl/segmentation/extract_clusters.h>
namespace wj_cfilter {
using namespace wj_sample;
enum FilterMethod { NoFilter = 0, Filter_Seg };
enum SampleMethod { Octree = 0, Octree_Ori, Voxel, Voxel_Ori };
/**
 * @brief 地图处理实体基类
 *
 * @tparam PointT
 */
template <typename PointT> class CloudFilterBase {
  protected:
    using PointCloud = pcl::PointCloud<PointT>;
    using PointCloudPtr = typename PointCloud::Ptr;

  public:
    using Ptr = boost::shared_ptr<CloudFilterBase<PointT>>;

  protected:
    PointCloudPtr c_pPc_;                       /**< 内部使用点云指针*/
    typename SampleBase<PointT>::Ptr c_sample_; /**< 采样方法实例*/
    SampleMethod c_sampleMethod_;               /**< 采样方法*/
    FilterMethod c_filterMethod_;               /**< 过滤方法*/
    float c_fGrid_;                             /**< 栅格大小*/

  protected:
    /**
     * @brief 深拷贝点云，防止清空输入点云
     *
     * @param data 输出点云
     */
    inline void copyCloud_(PointCloud& data)
    {
        //判断是否为相同指针,如果是需要深拷贝
        if (c_pPc_.get() == &data)
        {
            c_pPc_.reset(new PointCloud());
            *c_pPc_ = data;
        }
        data.clear();
    }

  public:
    /**
     * @brief 基类构造函数
     *
     */
    CloudFilterBase()
        : c_pPc_(nullptr), c_sample_(nullptr), c_sampleMethod_(SampleMethod::Octree),
          c_filterMethod_(FilterMethod::Filter_Seg), c_fGrid_(0.1)
    {
    }
    /**
     * @brief 基类析构函数
     *
     */
    virtual ~CloudFilterBase() {}
    /**
     * @brief 设置输入点云
     *
     * @param p_pc 输入点云指针
     */
    inline void setInputCloud(const PointCloudPtr& p_pc)
    {
        c_pPc_ = p_pc;
    }
    /**
     * @brief 设置采样方法
     *
     * @param method { Octree , Octree_Ori, Voxel, Voxel_Ori };
     */
    void setSampleMethod(const SampleMethod& method);
    /**
     * @brief 设置滤波方法
     *
     * @param method {NoFilter = 0, Filter_Seg }
     */
    void setFilterMethod(const FilterMethod& method);
    /**
     * @brief 设置栅格尺寸
     *
     * @param p_fLeafsize
     */
    inline void setLeafSize(float p_fLeafsize)
    {
        if (p_fLeafsize > 0)
            c_fGrid_ = p_fLeafsize;
    }
    /**
     * @brief 处理流程，纯虚函数
     *
     * @param p_pcOut
     */
    virtual void filter(PointCloud& p_pcOut) = 0;
    /**
     * @brief 2D点云生成流程，纯虚函数
     *
     * @param p_pcOut
     */
    virtual void filter2D(PointCloud& p_pcOut) = 0;
};
/**
 * @brief 线型点云(角点点云)处理实体类
 *
 * @tparam PointT
 */
template <typename PointT> class CloudFilter_Linearity : public CloudFilterBase<PointT> {
  private:
    using PointCloud = typename CloudFilterBase<PointT>::PointCloud;
    using PointCloudPtr = typename PointCloud::Ptr;
    using CheckLinarityMode = typename checkLine<PointT>::CheckLinarityMode;

  public:
    using Ptr = boost::shared_ptr<CloudFilter_Linearity<PointT>>;

  private:
    using CloudFilterBase<PointT>::c_pPc_;
    using CloudFilterBase<PointT>::c_sample_;
    using CloudFilterBase<PointT>::c_fGrid_;
    using CloudFilterBase<PointT>::copyCloud_;
    using CloudFilterBase<PointT>::c_filterMethod_;
    pcl::IndicesClustersPtr c_clusters_; /**< 聚类点云*/
    float c_fSegmentSize_;     /**< 垂线空间最小对角线长度的平方 默认值0.4*/
    float c_fFilterOutSizeSq_; /**< 垂线空间水平宽度平方的最大值*/
    CheckLinarityMode c_checkLineMode_;                /**< 垂线线性检测*/
    boost::shared_ptr<checkLine<PointT>> c_checkLine_; /**< 垂线线性检测实例*/

  public:
    /**
     * @brief 构造函数
     *
     */
    CloudFilter_Linearity()
        : c_clusters_(nullptr), c_fSegmentSize_(0.4f), c_fFilterOutSizeSq_(0.17f),
          c_checkLineMode_(CheckLinarityMode::LinearMode_PCA), c_checkLine_(nullptr)
    {
        // 初始化默认线性检测方法
        setLinarityMethod(c_checkLineMode_);
    }
    ~CloudFilter_Linearity() {}
    /**
     * @brief 设置线性检查方法
     *
     * @param method
     */
    void setLinarityMethod(const CheckLinarityMode& method);
    /**
     * @brief 角点点云处理流程
     *
     * @param p_pcOut 输出处理后的点云
     */
    virtual void filter(PointCloud& p_pcOut);

    /**
     * @brief 目前是空函数
     *
     * @param p_pcOut
     */
    virtual void filter2D(PointCloud& p_pcOut){};

  private:
    /**
     * @brief 区域分割流程,依据水平距离差的条件欧式聚类
     *
     */
    void segmentCloud_();
    /**
     * @brief 线性检测
     *
     * @param pc 聚类后的点云集,全部点云的子集之一
     * @return true 线性
     * @return false 非线性
     */
    bool checkLine_(PointCloudPtr& pc);
    // todo:补充下面函数的描述
    /**
     * @brief
     *
     * @param pc
     * @param min
     * @param max
     * @return true
     * @return false
     */
    bool isUniformDistributionInZ_(PointCloudPtr& pc, Eigen::Vector4f& min, Eigen::Vector4f& max);
    /**
     * @brief 过滤实现函数,填充输出点云
     *
     * @param p_pcOut
     */
    void applyFilter_(PointCloud& p_pcOut);
};
/**
 * @brief 面点点云处理实体类
 *
 * @tparam PointT 点云类型
 */
template <typename PointT> class CloudFilter_Plane : public CloudFilterBase<PointT> {
  private:
    using PointCloud = typename CloudFilterBase<PointT>::PointCloud;
    using PointCloudPtr = typename PointCloud::Ptr;

  public:
    using Ptr = boost::shared_ptr<CloudFilter_Plane<PointT>>;

  private:
    using CloudFilterBase<PointT>::c_pPc_;
    using CloudFilterBase<PointT>::c_fGrid_;
    using CloudFilterBase<PointT>::c_sample_;
    using CloudFilterBase<PointT>::copyCloud_;

    PointCloudPtr c_pPcGnd_;            /**< 内部使用点云指针-地面*/
    PointCloudPtr c_pPcOff_;            /**< 内部使用点云指针-非地面*/
    boost::shared_ptr<csf::CSF> c_GPE_; /**< 地面提取对象布料模型*/

    /**
     * @brief 区域分割算法流程,应用了欧式聚类
     *
     * @param p_pPcOff
     */
    void segmentCloud_(PointCloudPtr p_pPcOff);

    /**
 * @brief 使用八叉树过滤点数过少的点云
 * 
 * @param p_pCloud 
 * @param p_pOutput 
 */
    void octreeRemovePoints(PointCloudPtr p_pCloud, PointCloudPtr p_pOutput);

  public:
    /**
     * @brief 构造函数
     *
     */
    CloudFilter_Plane() : c_pPcGnd_(nullptr), c_pPcOff_(nullptr), c_GPE_(nullptr) {}
    /**
     * @brief 析构函数
     *
     */
    ~CloudFilter_Plane() {}

    /**
     * @brief 提取地面
     *
     * @param p_pPc
     * @param p_pPcGnd
     * @param p_pPcOff
     */
    void extractGround(PointCloudPtr p_pPc, PointCloud& p_pPcGnd, PointCloud& p_pPcOff);
    /**
     * @brief 面点点云处理实体
     *
     * @param p_pcOut
     */
    virtual void filter(PointCloud& p_pcOut);

    /**
     * @brief 2D处理方法流程
     *
     * @param p_pcOut
     */
    virtual void filter2D(PointCloud& p_pcOut);
};

}  // namespace wj_cfilter
#include "impl/cloud_filter.hpp"
