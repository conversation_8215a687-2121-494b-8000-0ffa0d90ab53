/**
 * @file cloud_sample.h
 * <AUTHOR> Li
 * @brief 降采样实现
 * @version 0.1
 * @date 2023-07-10
 *
 * @copyright Copyright (c) 2023
 *
 */
#pragma once
#include <Eigen/Dense>
#include <pcl/common/common.h>
#include <pcl/filters/extract_indices.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/octree/octree.h>
#include <pcl/octree/octree_pointcloud_voxelcentroid.h>
#include <pcl/octree/octree_search.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
namespace wj_sample {
template <typename PointT> class SampleBase {
  protected:
    using PointCloud = pcl::PointCloud<PointT>;
    using PointCloudPtr = typename PointCloud::Ptr;

  public:
    using Ptr = boost::shared_ptr<SampleBase<PointT>>;

  protected:
    PointCloudPtr c_pc_;       /**< 内部使用点云指针*/
    float c_fLeafSize_;        /**< 栅格大小*/
    bool c_bSampleFromOrigin_; /**< 采样栅格起点是否从最低点开始*/

  protected:
    void copyCloud_(PointCloud& data)
    {
        //判断是否位相同指针,如果是需要深拷贝
        if (c_pc_.get() == &data)
        {
            c_pc_.reset(new PointCloud());
            c_pc_->swap(data);
        }
        else
        {
            data.clear();
        }
    }

  public:
    SampleBase() : c_pc_(nullptr), c_fLeafSize_(0.1), c_bSampleFromOrigin_(false)
    {
        c_pc_.reset(new PointCloud());
    }
    virtual ~SampleBase() {}
    virtual void setInputCloud(const PointCloudPtr& l_pcIn)
    {
        c_pc_ = l_pcIn;
    }
    /**
     * @brief 控制采样网格起点为原点还是最低点
     *
     * @param p_bMode
     */
    virtual void setOriginSample(bool p_bMode)
    {
        c_bSampleFromOrigin_ = p_bMode;
    }
    /**
     * @brief 纯虚函数,过滤实现函数
     *
     * @param p_pcOut 输入点云
     */
    virtual void filter(PointCloud& p_pcOut) = 0;
    /**
     * @brief 纯虚函数,设置网格边长
     *
     * @param p_fLeafsize
     */
    virtual void setLeafSize(float p_fLeafsize) = 0;
    /**
     * @brief 纯虚函数,设置网格边长
     *
     * @param p_fLeafsize_x
     * @param p_fLeafsize_y
     * @param p_fLeafsize_z
     */
    virtual void setLeafSize(float p_fLeafsize_x, float p_fLeafsize_y, float p_fLeafsize_z) = 0;
};
template <typename PointT> class Sample_Octree : public SampleBase<PointT> {
  private:
    using PointCloud = typename SampleBase<PointT>::PointCloud;
    using PointCloudPtr = typename PointCloud::Ptr;

  public:
    using Ptr = boost::shared_ptr<Sample_Octree<PointT>>;

  private:
    typename pcl::octree::OctreePointCloud<PointT>::Ptr c_pTree_;

  protected:
    using SampleBase<PointT>::c_fLeafSize_;
    using SampleBase<PointT>::c_bSampleFromOrigin_;
    using SampleBase<PointT>::c_pc_;
    using SampleBase<PointT>::copyCloud_;

  public:
    /**
     * @brief 使用 PCL OctreePointCloud 栅格化点云
     *
     * @param p_pcOut
     */
    virtual void filter(PointCloud& p_pcOut);
    /**
     * @brief 设置栅格边长
     *
     * @param p_fLeafsize
     */
    virtual void setLeafSize(float p_fLeafsize);
    /**
     * @brief 设置栅格边长
     *
     * @param p_fLeafsize_x
     * @param p_fLeafsize_y
     * @param p_fLeafsize_z
     */
    virtual void setLeafSize(float p_fLeafsize_x, float p_fLeafsize_y, float p_fLeafsize_z);
    Sample_Octree()
    {
        // 使用默认值初始化八叉树
        c_pTree_.reset(new pcl::octree::OctreePointCloud<PointT>(c_fLeafSize_));
    }
    ~Sample_Octree() {}
};
template <typename PointT> class Sample_Voxel : public SampleBase<PointT> {
  private:
    using PointCloud = typename SampleBase<PointT>::PointCloud;
    using PointCloudPtr = typename PointCloud::Ptr;

  public:
    using Ptr = boost::shared_ptr<Sample_Voxel<PointT>>;

  private:
    boost::shared_ptr<pcl::VoxelGrid<PointT>> c_pTree_;
    using SampleBase<PointT>::c_fLeafSize_;
    using SampleBase<PointT>::c_bSampleFromOrigin_;
    using SampleBase<PointT>::c_pc_;
    using SampleBase<PointT>::copyCloud_;

  public:
    /**
     * @brief 过滤实现函数
     *
     * @param p_pcOut
     */
    virtual void filter(PointCloud& p_pcOut);
    virtual void setLeafSize(float p_fLeafsize);
    virtual void setLeafSize(float p_fLeafsize_x, float p_fLeafsize_y, float p_fLeafsize_z);
    Sample_Voxel()
    {
        c_pTree_.reset(new pcl::VoxelGrid<PointT>());
        c_pTree_->setLeafSize(c_fLeafSize_, c_fLeafSize_, c_fLeafSize_);
    }
    ~Sample_Voxel()
    {
        // c_pTree_ = nullptr;
    }
};

}  // namespace wj_sample

#include "impl/cloud_sample.hpp"
