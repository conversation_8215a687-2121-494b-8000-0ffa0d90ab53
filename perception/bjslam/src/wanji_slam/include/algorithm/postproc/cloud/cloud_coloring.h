/**
 * @file cloud_coloring.h
 * <AUTHOR> (<EMAIL>)
 * @brief 点云上色
 * @version 0.1
 * @date 2023-07-12
 *
 * @copyright Copyright (c) 2023
 *
 */
#pragma once

#include <boost/bind.hpp>
#include <boost/function.hpp>
#include <pcl/io/pcd_io.h>
#include <pcl/io/png_io.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
namespace wj_cColor {
template <typename PointT = pcl::PointXYZ> class CloudColorBase {
  protected:
    using PointCloud = pcl::PointCloud<PointT>;
    using PointCloudPtr = boost::shared_ptr<PointCloud>;
    using PointCloudConstPtr = boost::shared_ptr<const PointCloud>;
    using RGB = pcl::RGB;
    using RGBCloud = pcl::PointCloud<RGB>;
    using RGBCloudPtr = boost::shared_ptr<RGBCloud>;
    using RGBCloudConstPtr = boost::shared_ptr<const RGBCloud>;

  public:
    typedef boost::shared_ptr<CloudColorBase<PointT>> Ptr;

  protected:
    PointCloudPtr c_pPc_; /**< 内部使用点云指针*/
    RGBCloudPtr c_pRGBc_; /**< 内部使用颜色*/
    float c_fGrid_ = 0.2; /**< 栅格大小*/
    float c_greyRange_[2] = {0.49999, 1.0};
    float c_greySize_;

  public:
    CloudColorBase(/* args */) {}
    virtual ~CloudColorBase() {}
    void setInputCloud(const PointCloudPtr& p_pc)
    {
        c_pPc_ = p_pc;
        c_pRGBc_.reset(new RGBCloud());
    }
    /**
     * @description: 设置栅格尺寸
     * @param {float} 栅格尺寸
     * @return {*}
     * @other: 设置应早于 setSampleMethod()
     */
    void setLeafSize(float p_fLeafsize)
    {
        c_fGrid_ = p_fLeafsize;
    }
    /**
     * @description: 设置灰度转换的范围
     * @param {float} 栅格尺寸
     * @return {*}
     * @other: 设置应早于 setSampleMethod()
     */
    void setGreyRange(float min, float max)
    {
        c_greyRange_[0] = min;
        c_greyRange_[1] = max;
        c_greySize_ = max - min;
    }
    virtual void GreyToRGB(float p_fLabel, RGB& rgb);
    virtual void color(RGBCloud& p_pcOut) = 0;
    virtual void image(const std::string& filename);
};
template <typename PointT = pcl::PointXYZ> class CloudColor_PCA : public CloudColorBase<PointT> {
  private:
    using PointCloud = typename CloudColorBase<PointT>::PointCloud;
    using PointCloudPtr = typename PointCloud::Ptr;
    using RGB = typename CloudColorBase<PointT>::RGB;
    using RGBCloud = typename CloudColorBase<PointT>::RGBCloud;
    using RGBCloudPtr = typename CloudColorBase<PointT>::RGBCloudPtr;

  public:
    typedef boost::shared_ptr<CloudColor_PCA<PointT>> Ptr;

  private:
    using CloudColorBase<PointT>::c_pPc_;
    using CloudColorBase<PointT>::c_pRGBc_;
    using CloudColorBase<PointT>::c_fGrid_;
    using CloudColorBase<PointT>::c_greyRange_;

    void pcaToRgb(PointCloudPtr p_pPc, RGBCloud& p_pRGBc, double p_fRadius);
    float calcPrincipal(PointCloud& p_pc, std::vector<int> p_idx);

  public:
    CloudColor_PCA(/* args */) {}
    ~CloudColor_PCA() {}
    virtual void color(RGBCloud& p_pcOut);
};

}  // namespace wj_cColor
#include "impl/cloud_coloring.hpp"