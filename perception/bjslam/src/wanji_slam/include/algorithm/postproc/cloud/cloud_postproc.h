/**
 * @file cloud_postproc.h
 * <AUTHOR> Li
 * @brief 地图后处理
 * @version 0.1
 * @date 2023-06-28
 *
 * @copyright Copyright (c) 2023
 *
 */
#pragma once

#include "cloud_filter.h"
namespace wj_cpostproc {
using namespace wj_cfilter;
/**
 * @brief 点云后处理方法,配置此方法后不用配置别的参数
 *  Plane 平面
 *  VerticalLine 垂直线
 *  HorizontalLine 水平线
 *
 */
enum PostProcMethod { Plane = 0, VerticalLine, HorizontalLine };
/**
 * @brief filter马甲类，感觉可以去掉，可能有巧思？
 *
 * @tparam PointT 模板类型
 */
template <typename PointT> class CloudPostProc {
  public:
    using Ptr = boost::shared_ptr<CloudPostProc<PointT>>;

  private:
    using PointCloud = pcl::PointCloud<PointT>;
    using PointCloudPtr = typename PointCloud::Ptr;

    FilterMethod c_filterMode_; /**< 过滤方法*/
    SampleMethod c_sampleMode_; /**< 采样方法*/
    float c_fGrid_;             /**< 采样栅格大小*/

  private:
    boost::shared_ptr<CloudFilterBase<PointT>> c_filter_; /**< 处理实体*/

  public:
    /**
     * @brief 构造函数
     *
     */
    CloudPostProc() : c_fGrid_(0.1), c_filter_(nullptr) {}
    /**
     * @brief 析构函数，将处理实体对象置空
     *
     */
    ~CloudPostProc() {}
    /**
     * @brief 设置输入点云
     *
     * @param p_pc
     */
    void setInputCloud(const PointCloudPtr& p_pc);

    /**
     * @brief 处理函数的马甲，真正的处理函数在里面
     *
     * @param p_pcOut 输出点云
     */
    void filter(PointCloud& p_pcOut);

    /**
     * @brief 2D处理函数的马甲，真正的处理函数在里面
     *
     * @param p_pcOut 输出点云
     */
    void filter2D(PointCloud& p_pcOut);

    /**
     * @brief 设置处理方法,包含了setFilterMethod()\setSampleMethod()
     *
     * @param 处理方法
     */
    void setProcMethod(const PostProcMethod& method);
    /**
     * @brief 设置过滤方法
     *
     * @param method 方法
     */
    void setFilterMethod(const FilterMethod& method);

    /**
     * @brief 设置采样方法
     *
     * @param method 采样方法
     */
    void setSampleMethod(const SampleMethod& method);

    /**
     * @brief 设置栅格大小
     *
     * @param p_fLeafSize
     */
    void setLeafSize(float p_fLeafSize);
};

}  // namespace wj_cpostproc
#include "impl/cloud_postproc.hpp"
