/**
 * @file cloud_postproc.hpp
 * <AUTHOR> Li
 * @brief
 * @version 0.1
 * @date 2023-07-07
 *
 * @copyright Copyright (c) 2023
 *
 */
#pragma once

#include "../cloud_postproc.h"
namespace wj_cpostproc {

template <typename PointT> void CloudPostProc<PointT>::setLeafSize(float p_fLeafSize)

{
    c_fGrid_ = p_fLeafSize;
    if (c_filter_)
        c_filter_->setLeafSize(c_fGrid_);
}
template <typename PointT> void CloudPostProc<PointT>::setSampleMethod(const SampleMethod& method)
{
    c_sampleMode_ = method;
    if (c_filter_)
        c_filter_->setSampleMethod(c_sampleMode_);
}

template <typename PointT> void CloudPostProc<PointT>::setFilterMethod(const FilterMethod& method)
{
    c_filterMode_ = method;
    if (c_filter_)
        c_filter_->setFilterMethod(c_filterMode_);
}

template <typename PointT> void CloudPostProc<PointT>::setProcMethod(const PostProcMethod& method)
{
    c_filter_ = nullptr;
    switch (method)
    {
        case PostProcMethod::Plane:
            c_filterMode_ = FilterMethod::NoFilter;
            c_sampleMode_ = SampleMethod::Octree;
            c_filter_.reset(new CloudFilter_Plane<PointT>());
            break;
        case PostProcMethod::VerticalLine:
            c_filterMode_ = FilterMethod::Filter_Seg;
            c_sampleMode_ = SampleMethod::Voxel_Ori;
            c_filter_.reset(new CloudFilter_Linearity<PointT>());
            break;
        // 无法使用
        // case PostProcMethod::HorizontalLine:
        //     c_stConfig_.m_filterMode = FilterMethod::NoFilter;
        //     c_stConfig_.m_sampleMode = SampleMethod::Voxel_Ori;
        //     c_filter_.reset(new CloudFilter_Plane<PointT>());
        //     break;
        default:
            std::cerr << "setProcMethod Outside : use default PostProcMethod::Plane" << std::endl;
            c_filter_.reset(new CloudFilter_Plane<PointT>());
            break;
    }
    c_filter_->setLeafSize(c_fGrid_);
    c_filter_->setSampleMethod(c_sampleMode_);
    c_filter_->setFilterMethod(c_filterMode_);
}

template <typename PointT> void CloudPostProc<PointT>::filter2D(PointCloud& p_pcOut)
{
    c_filter_->filter2D(p_pcOut);
}

template <typename PointT> void CloudPostProc<PointT>::filter(PointCloud& p_pcOut)

{
    c_filter_->filter(p_pcOut);
}
template <typename PointT> void CloudPostProc<PointT>::setInputCloud(const PointCloudPtr& p_pc)
{
    c_filter_->setInputCloud(p_pc);
}

}  // namespace wj_cpostproc
