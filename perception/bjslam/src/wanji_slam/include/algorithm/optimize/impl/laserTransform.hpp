/*******************************
 * @Descripttion:
 * @version: 1.0
 * @Author: wang<PERSON><PERSON>
 * @Date: 2021-05-14 10:10:59
 * @LastEditors: zushuang
 * @LastEditTime: 2021-06-29 10:41:48
 *******************************/
#pragma once
#include "algorithm/optimize/laserTransform.h"

template <typename PointT>
void LaserTransform<PointT>::transformSlerp(const PointT* p_pPntIn, PointT& p_pPntOut)
{
    memcpy(&c_fDT_, reinterpret_cast<const uint8_t*>(&(*p_pPntIn)) + TIME_FIELDS_OFFSET, 4UL);
    c_fDT_ /= c_fUdpTime_;

    //使用匀速运动假设下的姿势变化的四元数线性插值修正
    c_qSlp_ = c_q0_.slerp(c_fDT_, c_q1_);
    c_tSlp_ = c_t0_ + c_fDT_ * (c_t1_ - c_t0_);

    p_pPntOut = *p_pPntIn;
    p_pPntOut.getVector3fMap() = c_qSlp_ * p_pPntIn->getVector3fMap() + c_tSlp_;
}

template <typename PointT>
void LaserTransform<PointT>::transformSlerpToEnd(const PointT* p_pPntIn, PointT& p_pPntOut)
{
    memcpy(&c_fDT_, reinterpret_cast<const uint8_t*>(&(*p_pPntIn)) + TIME_FIELDS_OFFSET, 4UL);
    c_fDT_ = 1 - c_fDT_ / c_fUdpTime_;

    //使用匀速运动假设下的姿势变化的四元数线性插值修正
    c_qSlp_ = c_q0_.slerp(c_fDT_, c_q1_);
    c_tSlp_ = c_t0_ + c_fDT_ * (c_t1_ - c_t0_);

    p_pPntOut = *p_pPntIn;
    p_pPntOut.getVector3fMap() = c_qSlp_ * p_pPntIn->getVector3fMap() + c_tSlp_;
}

template <typename PointT>
void LaserTransform<PointT>::transformCloudSlerp(Eigen::Quaterniond p_q0,
                                                 Eigen::Vector3d p_t0,
                                                 Eigen::Quaterniond p_q1,
                                                 Eigen::Vector3d p_t1,
                                                 const PointCloudTypePtr p_pPcIn,
                                                 PointCloudTypePtr p_pPcOut,
                                                 const float p_iTimespan,
                                                 bool p_bSlerpToEnd)
{
    int l_iCldSize = p_pPcIn->size();
    if (p_pPcOut != p_pPcIn)
        p_pPcOut->resize(l_iCldSize);

    c_fUdpTime_ = p_iTimespan;
    c_q0_ = p_q0.cast<float>();
    c_t0_ = p_t0.cast<float>();
    c_q1_ = p_q1.cast<float>();
    c_t1_ = p_t1.cast<float>();
    if (!p_bSlerpToEnd)
    {
#pragma omp parallel for nowait num_threads(MAX_NUM_THREAD_LTF) private(c_fDT_, c_qSlp_, c_tSlp_)
        for (int j = 0; j < l_iCldSize; ++j)
        {
            transformSlerp(&p_pPcIn->points[j], p_pPcOut->points[j]);
        }
    }
    else
    {
#pragma omp parallel for nowait num_threads(MAX_NUM_THREAD_LTF) private(c_fDT_, c_qSlp_, c_tSlp_)
        for (int j = 0; j < l_iCldSize; ++j)
        {
            transformSlerpToEnd(&p_pPcIn->points[j], p_pPcOut->points[j]);
        }
    }
}

template <typename PointT>
void LaserTransform<PointT>::transformPoint(Eigen::Quaterniond p_q0,
                                            Eigen::Vector3d p_t0,
                                            const PointT* p_pPntIn,
                                            PointT& p_pPntOut)
{
    p_pPntOut = *p_pPntIn;
    p_pPntOut.getVector3fMap() =
        p_q0.cast<float>() * p_pPntIn->getVector3fMap() + p_t0.cast<float>();
}

template <typename PointT>
void LaserTransform<PointT>::transformPoint(const PointT* p_pPntIn, PointT& p_pPntOut)
{
    p_pPntOut = *p_pPntIn;
    p_pPntOut.getVector3fMap() = c_q0_ * p_pPntIn->getVector3fMap() + c_t0_;
}

template <typename PointT>
void LaserTransform<PointT>::transformCloudPoints(Eigen::Quaterniond p_q0,
                                                  Eigen::Vector3d p_t0,
                                                  const PointCloudTypePtr p_pPcIn,
                                                  PointCloudTypePtr p_pPcOut)
{
    // PointCloudTypePtr l_pPcTmp(new PointCloudType);
    int l_iCldSize = p_pPcIn->size();
    if (p_pPcIn != p_pPcOut)
        p_pPcOut->resize(l_iCldSize);

    c_q0_ = p_q0.cast<float>();
    c_t0_ = p_t0.cast<float>();
#pragma omp parallel for nowait num_threads(MAX_NUM_THREAD_LTF)
    for (int j = 0; j < l_iCldSize; ++j)
    {
        transformPoint(&p_pPcIn->points[j], p_pPcOut->points[j]);
    }
    // *p_pPcOut = *l_pPcTmp;
}
