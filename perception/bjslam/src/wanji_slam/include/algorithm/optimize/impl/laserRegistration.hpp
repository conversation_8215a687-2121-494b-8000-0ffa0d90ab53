/*
 * @Author: wang<PERSON>ang-dev
 * @Date: 2021-04-12 16:55:32
 * @LastEditTime: 2023-11-29 09:30:25
 * @LastEditors: shuangquan han
 * @Description: In User Settings Edit
 * @FilePath: /catkin_ws/src/wanji_slam/wslam/src/LaserRegistration.cpp
 */
#pragma once
#include "algorithm/optimize/laserRegistration.h"
template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setInputFrameCloud(PointCloudSourcePtr p_pPc)
{
    if (p_pPc->points.empty())
    {
        std::cout << "FrameCloud is empty." << std::endl;
        return;
    }

    //*c_pSrcFrmPc_ = *p_pPc;
    c_pSrcFrmPc_ = p_pPc;
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setInputSourceCorner(PointCloudSourcePtr p_pPc)
{
    if (p_pPc->points.empty())
    {
        c_iSrcCorNum_ = 0;
        c_pSrcCorPc_ = nullptr;
        return;
    }

    //*c_pSrcCorPc_ = *p_pPc;
    c_pSrcCorPc_ = p_pPc;
    c_iSrcCorNum_ = p_pPc->size();
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setInputSourceMark(PointCloudSourcePtr p_pPc)
{
    if (p_pPc->points.empty())
    {
        c_iSrcMarkNum_ = 0;
        c_pSrcMarkCorPc_ = nullptr;
        return;
    }

    //*c_pSrcCorPc_ = *p_pPc;
    c_pSrcMarkCorPc_ = p_pPc;
    c_iSrcMarkNum_ = p_pPc->size();
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setInputSourceSurface(PointCloudSourcePtr p_pPc)
{
    if (p_pPc->points.empty())
    {
        c_iSrcSurNum_ = 0;
        c_pSrcSufPc_ = nullptr;
        return;
    }

    // *c_pSrcSufPc_ = *p_pPc;
    c_pSrcSufPc_ = p_pPc;
    c_iSrcSurNum_ = p_pPc->size();
}
template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setInputSourceSurfaceSample(
    PointCloudSourcePtr p_pPc,
    int p_iSampleSize)
{
    if (p_pPc->points.empty())
    {
        c_iSrcSurNum_ = 0;
        c_pSrcSufPc_ = nullptr;
        return;
    }
    c_iSampleNum_ = p_iSampleSize;
    c_pSrcSufPc_ = p_pPc;
    c_iSrcSurNum_ = p_iSampleSize < (int)p_pPc->size() ? p_iSampleSize : p_pPc->size();
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setInputTargetCorner(PointCloudTargetPtr p_pPc)
{
    if (!p_pPc || p_pPc->points.empty())
    {
        c_iTagCorNum_ = 0;
        c_pTgtCorPc_ = nullptr;
        return;
    }
    c_iTagCorNum_ = p_pPc->size();
    c_pTgtCorPc_ = p_pPc;

    if (c_b2dMatch_)
    {
        // *c_pTgtCorPc2D_ = *p_pPc;
        c_pKdtFln2dCor_->setInputCloud(c_pTgtCorPc_);
    }
    else
    {
        c_pKdtCor_->setInputCloud(c_pTgtCorPc_);
    }
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setInputTargetMark(PointCloudTargetPtr p_pPc)
{
    if (p_pPc->points.empty())
    {
        c_iTagMarkNum_ = 0;
        c_pTgtMarkCorPc_ = nullptr;
        return;
    }
    c_iTagMarkNum_ = p_pPc->size();
    c_pTgtMarkCorPc_ = p_pPc;

    if (c_b2dMatch_)
    {
        // *c_pTgtCorPc2D_ = *p_pPc;
        c_pKdtFln2dMarkCor_->setInputCloud(c_pTgtMarkCorPc_);
    }
    else
    {
        c_pKdtMarkCor_->setInputCloud(c_pTgtMarkCorPc_);
    }
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setInputTargetSurface(PointCloudTargetPtr p_pPc)
{
    if (p_pPc->points.empty())
    {
        c_iTagSurNum_ = 0;
        c_pTgtSurPc_ = nullptr;
        return;
    }

    c_pTgtSurPc_ = p_pPc;

    c_iTagSurNum_ = p_pPc->size();
    c_pKdtSur_->setInputCloud(c_pTgtSurPc_);
    // c_pKdtFlnSur_->setInputCloud(c_pTgtSurPc_);
}
template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setIVoxGrid(
    boost::shared_ptr<IVox<3, PointTarget>> p_pIvox)
{
    if (!p_pIvox)
    {
        return;
    }
    c_pIvox_ = p_pIvox;
    c_iTagSurNum_ = 1;
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setOptimizeModel(int p_iModel)
{
    // std::cout << "hsq: laserRegistration.hpp setOptimizeModel() p_iModel = " << p_iModel
    //           << std::endl;
    c_iOptimizeModel_ = p_iModel;
}
template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setFilterZValue(float p_fZValue)
{
    c_fFilterZValue_ = p_fZValue;
}
template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setWorkModel(int p_iModel)
{
    c_iWorkModel_ = p_iModel;
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setSlamModel(int p_iModel)
{
    c_iSlamModel_ = p_iModel;
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setMarkWight(int p_iMin, int p_iMax)
{
    c_iMarkWeightMin_ = p_iMin;
    c_iMarkWeightMax_ = p_iMax;
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::choseIVoxMapchParam_(int p_iModel)
{
    if (!c_pSrcSufPc_)
        return;
    switch (p_iModel)
    {
        case WorkMode::InitMapMode: c_iSrcSurNum_ = c_pSrcSufPc_->size(); break;
        case WorkMode::ContMapMode: c_iSrcSurNum_ = c_pSrcSufPc_->size(); break;
        case WorkMode::UpdateMapMode: c_iSrcSurNum_ = c_pSrcSufPc_->size(); break;
        case WorkMode::LocatMode: c_iSrcSurNum_ = c_pSrcSufPc_->size(); break;
        default: break;
    }
}
template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setTransform(const Eigen::Quaterniond p_qEst,
                                                               const Eigen::Vector3d p_tEst)
{
    c_qEst_ = p_qEst;
    c_tEst_ = p_tEst;
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setCurrentPose(const Eigen::Vector3d p_tEst)
{
    c_trans_ = p_tEst;
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setDistThreshold(const float p_fDis)
{
    c_fDisThd_ = p_fDis * p_fDis;
    // c_fDisThd_ = dis;
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setZAxisThreshold(const float p_fZThd)
{
    c_fZThd_ = p_fZThd;
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setAngleSearch(bool p_bAngleSrh)
{
    c_bAngleSrh_ = p_bAngleSrh;
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setAngle(const float p_fAngle)
{
    c_fAngle_ = p_fAngle;
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setMatch2D(bool p_b2D)
{
    c_b2dMatch_ = p_b2D;
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setRadiusThreshold(const float p_fRThd)
{
    c_fRThd_ = p_fRThd * p_fRThd;
    // c_fRThd_ = r;
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setCornerSearchR(const float p_fCorSrhR)
{
    c_fCorSrhR_ = p_fCorSrhR;
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setMarkSearchR(const float p_fCorSrhR)
{
    c_fMarkSrhR_ = p_fCorSrhR;
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setSurfaceSearchR(const float p_fSurSrhR)
{
    c_fSurSrhR_ = p_fSurSrhR;
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setSearchK(const int p_iKdNum)
{
    c_iKdNum_ = p_iKdNum;
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setwallhigh(const float p_fgdHigh,
                                                              const float p_frfHigh)
{
    c_fGroundHigh_ = p_fgdHigh;
    c_fRoofHigh_ = p_frfHigh;
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setSearchNumThreshold(const int p_iSrhNum)
{
    c_iSrhNum_ = p_iSrhNum;
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setMaxIterations(const int p_iIter)
{
    c_iIte_ = p_iIter;
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setMatchScoreThr(const float p_fMatchThr)
{
    c_fMatchPrecentThr_ = p_fMatchThr < 0.1 ? 0.1 : p_fMatchThr;
    std::cout <<"hsq LaserRegistration.hpp c_fMatchPrecentThr_ = " << c_fMatchPrecentThr_ << std::endl;
}

/**
 * @description: 设置PCA方法 校验平面的特征向量方向及阈值
 * @param {const int} p_iDirection 特征向量方向
 * @param {const float} p_fThr 对应方向 特征值阈值
 * @return {*}
 */
template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setPlanePcaThr(const int p_iDirection,
                                                                 const float p_fThr)
{
    if (p_iDirection > 2 || p_iDirection < 0)
    {
        LOGM(WWARN, "direction error, ignore set | {}", p_iDirection);
        return;
    }
    c_iPlanePcaDirection = p_iDirection;
    c_fPlanePcaValue = p_fThr;
}

/**
 * @description: 设置平面拟合误差阈值
 * @param {const float} p_fThr 平面拟合误差阈值
 * @return {*}
 */
template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setPlaneMeanDiffThr(const float p_fThr)
{
    c_fPlaneMeanDiff = p_fThr;
}

template <typename PointSource, typename PointTarget>
float LaserRegistration<PointSource, PointTarget>::getMatchScore()
{
    return c_fMatchPrecent_;
}
template <typename PointSource, typename PointTarget>
float LaserRegistration<PointSource, PointTarget>::getResultScore()
{
    return getMatchScore();
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::getResultFramePoints(PointCloudSourcePtr p_pPc)
{
    *p_pPc = *c_pSrcFrmPc_;
}
template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::getResultCornerPoints(PointCloudSourcePtr p_pPc)
{
    *p_pPc = *c_pSrcCorPc_;
}
template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::getResultSurfacePoints(PointCloudSourcePtr p_pPc)
{
    *p_pPc = *c_pSrcSufPc_;
}

template <typename PointSource, typename PointTarget>
float LaserRegistration<PointSource, PointTarget>::getValidSearchRadius_(
    const PointSource* p_pntSrh,
    float p_fSrhR)
{
    // if (c_bAngleSrh_)
    // {
    //     float l_fSrhPntDis = sqrt(pow(p_pntSrh->x, 2) + pow(p_pntSrh->y, 2) + pow(p_pntSrh->z,
    //     2)); float l_fSrhThr = sin(c_fAngle_ / 180.0 * M_PI) * l_fSrhPntDis; if (l_fSrhThr <
    //     p_fSrhR)
    //     {
    //         return l_fSrhThr;
    //     }
    //     else
    //     {
    //         return p_fSrhR;
    //     }
    // }
    // else
    {
        return p_fSrhR;
    }
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::radiusMatchLine2D(
    ceres::Problem& p_pbm,
    ceres::LossFunction* p_pLossFunc,
    double* p_pdParaQ,
    double* p_pdParaT)
{
    if (!c_pSrcCorPc_ || !c_pTgtCorPc_)
        return;

#ifdef MATCHDEBUG
    pcl::PCDWriter writer;
    writer.writeBinary("./subFi.pcd", *c_pTgtCorPc_);
    writer.writeBinary("./rawFi.pcd", *c_pSrcCorPc_);
    pcl::PointCloud<pcl::PointXYZI>::Ptr l_curPtr(new pcl::PointCloud<pcl::PointXYZI>());
    pcl::PointXYZI l_curPoint;
    PointCloudTargetPtr l_matchPtr(new PointCloudTarget());
#endif
    // debugmatch计数
    int l_iDebugUsep[4] = {0, 0, 0, 0};

    std::vector<int> l_viPntSrhIdx;
    std::vector<float> l_vfPntSrhSqDis;
    PointSource l_pntO, l_pntWldO;

    // 点云高差排序容器
    s_SmoothZDiff* l_sCloudSmoothZdiff;

    // 方向向量
    Eigen::Vector3d l_unitDirection(0, 0, 0);
    Eigen::Vector3d l_prePntA, l_prePntB, l_currPnt;
    float l_fDisCurr2AB = 0;

#pragma omp parallel for nowait num_threads(MAX_NUM_THREAD) private(l_pntO,                        \
                                                                        l_pntWldO,                 \
                                                                        l_viPntSrhIdx,             \
                                                                        l_vfPntSrhSqDis,           \
                                                                        l_sCloudSmoothZdiff,       \
                                                                        l_unitDirection,           \
                                                                        l_prePntA,                 \
                                                                        l_prePntB,                 \
                                                                        l_currPnt,                 \
                                                                        l_fDisCurr2AB, )           \
    reduction(+ : c_iCorMatchNum_, l_iDebugUse)
    for (int i = 0; i < c_iSrcCorNum_; i++)
    {
        l_pntO = c_pSrcCorPc_->points[i];
        // 搜索半径
        float l_fKdR = getValidSearchRadius_(&l_pntO, c_fCorSrhR_);
        // 转雷达坐标点为预估全局坐标点
        LaserTransform<PointSource>::transformPoint(c_qEst_, c_tEst_, &l_pntO, l_pntWldO);

#ifdef MATCHDEBUG
        if (i)
            l_curPtr->points.push_back(l_curPoint);
        l_iDebugUse[3] = 0;
        l_curPoint.intensity = l_iDebugUse[3];
        l_curPoint.x = l_pntWldO.x;
        l_curPoint.y = l_pntWldO.y;
        l_curPoint.z = l_pntWldO.z;
#endif

        PointTarget l_pntSrh;
        l_pntSrh.x = l_pntWldO.x;
        l_pntSrh.y = l_pntWldO.y;
        l_pntSrh.z = l_pntWldO.z;
        if (c_pKdtFln2dCor_->radiusSearch(l_pntSrh, l_fKdR, l_viPntSrhIdx, l_vfPntSrhSqDis)
            < c_iSrhNum_)
        {
            continue;
        }

#ifdef MATCHDEBUG
        l_iDebugUse[0]++;
        l_iDebugUse[3]++;
        l_curPoint.intensity = l_iDebugUse[3];
#endif

        // 进行z轴排序
        l_sCloudSmoothZdiff = new s_SmoothZDiff[l_viPntSrhIdx.size()];

        for (int j = 0; j < (int)l_viPntSrhIdx.size(); j++)
        {
            l_sCloudSmoothZdiff[j].m_ZDiff =
                std::fabs(c_pTgtCorPc_->points[l_viPntSrhIdx[j]].z - l_pntWldO.z);
            l_sCloudSmoothZdiff[j].m_idx = l_viPntSrhIdx[j];
        }
        std::sort(l_sCloudSmoothZdiff, l_sCloudSmoothZdiff + l_viPntSrhIdx.size(), s_SortValue());

        // 提取有效高差范围的点，计算中心点
        // 匹配点容器
        std::vector<Eigen::Vector3d> l_vNearCorners;
        Eigen::Vector3d l_center(0, 0, 0);
        for (int j = 0; j < (int)l_viPntSrhIdx.size(); j++)
        {
            // z轴差值过大，舍弃点
            if (l_sCloudSmoothZdiff[j].m_ZDiff > c_fZThd_)
            {
                continue;
            }
            Eigen::Vector3d l_tmpCor(c_pTgtCorPc_->points[l_sCloudSmoothZdiff[j].m_idx].x,
                                     c_pTgtCorPc_->points[l_sCloudSmoothZdiff[j].m_idx].y,
                                     c_pTgtCorPc_->points[l_sCloudSmoothZdiff[j].m_idx].z);
            l_center = l_center + l_tmpCor;
            l_vNearCorners.push_back(l_tmpCor);
        }
        delete[] l_sCloudSmoothZdiff;
        if ((int)l_vNearCorners.size() < c_iSrhNum_)
        {
            continue;
        }

#ifdef MATCHDEBUG
        l_iDebugUse[1]++;
        l_iDebugUse[3]++;
        l_curPoint.intensity = l_iDebugUse[3];
#endif

        // 计算最近邻点的中心
        l_center = l_center / float(l_vNearCorners.size());

        // 协方差矩阵
        Eigen::Matrix3d l_covMat = Eigen::Matrix3d::Zero();
        for (int j = 0; j < (int)l_vNearCorners.size(); j++)
        {
            Eigen::Matrix<double, 3, 1> l_tmpZeroMean = l_vNearCorners[j] - l_center;
            l_covMat = l_covMat + l_tmpZeroMean * l_tmpZeroMean.transpose();
        }
        l_center -= c_tMovePose_;

        // 计算协方差矩阵的特征值和特征向量，用于判断这5个点是不是呈线状分布，此为PCA的原理
        Eigen::SelfAdjointEigenSolver<Eigen::Matrix3d> l_saes(l_covMat);
        // 判断非直线
        if ((l_saes.eigenvalues().normalized())[2] < 0.99)
        {
            continue;
        }

#ifdef MATCHDEBUG
        l_iDebugUse[2]++;
        l_iDebugUse[3]++;
        l_curPoint.intensity = l_iDebugUse[3];
#endif

        l_unitDirection = l_saes.eigenvectors().col(2);
        // 如果临近点呈线状分布，最大的特征值对应的特征向量就是该线的方向向量
        // 从中心点沿着方向向量向两端移动0.1m，构造线上的两个点
        l_prePntA = 0.1 * l_unitDirection + l_center;
        l_prePntB = -0.1 * l_unitDirection + l_center;
        l_currPnt << l_pntO.x, l_pntO.y, l_pntO.z;
        l_currPnt -= c_tMovePose_;
        ceres::CostFunction* l_pCostFunc = LidarEdgeFactor::Create(l_currPnt, l_prePntA, l_prePntB);
        p_pbm.AddResidualBlock(l_pCostFunc, p_pLossFunc, p_pdParaQ, p_pdParaT);
        c_iCorMatchNum_++;

#ifdef MATCHDEBUG
        PointTarget l_tmp = PointTarget(l_prePntA[0] + c_tMovePose_.x(),
                                        l_prePntA[1] + c_tMovePose_.y(),
                                        l_prePntA[2] + c_tMovePose_.z());
        l_matchPtr->points.push_back(l_tmp);
        l_tmp = PointTarget(l_prePntB[0] + c_tMovePose_.x(),
                            l_prePntB[1] + c_tMovePose_.y(),
                            l_prePntB[2] + c_tMovePose_.z());
        l_matchPtr->points.push_back(l_tmp);
#endif
    }

#ifdef MATCHDEBUG
    l_curPtr->points.push_back(l_curPoint);

    if (l_matchPtr->points.size() != 0)
    {
        printf("save match corn\n");
        writer.writeBinary("./matchFi.pcd", *l_matchPtr);
    }
    writer.writeBinary("./curQuanjuFi.pcd", *l_curPtr);
    printf("match corn: %d - %d - [ %d - %d - %d]\n",
           c_iSrcCorNum_,
           c_iCorMatchNum_,
           l_iDebugUse[0],
           l_iDebugUse[1],
           l_iDebugUse[2]);
#endif
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::radiusMatchMarkLine2D(
    ceres::Problem& p_pbm,
    ceres::LossFunction* p_pLossFunc,
    double* p_pdParaQ,
    double* p_pdParaT,
    const int p_iType)
{
    static int s_iMarkId = 100;
    if (!c_pSrcMarkCorPc_ || !c_pTgtMarkCorPc_)
    {
        if (p_iType == Mapping)
        {
            if (c_pSrcMarkCorPc_)
            {
                for (int i = 0; i < c_iSrcMarkNum_; i++)
                {
                    s_iMarkId++;
                    c_pSrcMarkCorPc_->points[i].h = s_iMarkId;
                }
            }
        }
        return;
    }

#ifdef MATCHDEBUG
    pcl::PCDWriter writer;
    writer.writeBinary("./MarksubFi.pcd", *c_pTgtMarkCorPc_);
    writer.writeBinary("./MarkrawFi.pcd", *c_pSrcMarkCorPc_);
    pcl::PointCloud<pcl::PointXYZI>::Ptr l_curPtr(new pcl::PointCloud<pcl::PointXYZI>());
    pcl::PointXYZI l_curPoint;
    PointCloudTargetPtr l_matchPtr(new PointCloudTarget());
#endif
    // debugmatch计数
    int l_iDebugUsep[4] = {0, 0, 0, 0};

    std::vector<int> l_viPntSrhIdx;
    std::vector<float> l_vfPntSrhSqDis;
    PointSource l_pntO, l_pntWldO;

    // 点云高差排序容器
    s_SmoothZDiff* l_sCloudSmoothZdiff = nullptr;

    // 方向向量
    Eigen::Vector3d l_unitDirection(0, 0, 0);
    Eigen::Vector3d l_prePntA, l_prePntB, l_currPnt;
    float l_fDisCurr2AB = 0;

#pragma omp parallel for nowait num_threads(MAX_NUM_THREAD) private(l_pntO,                        \
                                                                        l_pntWldO,                 \
                                                                        l_viPntSrhIdx,             \
                                                                        l_vfPntSrhSqDis,           \
                                                                        l_sCloudSmoothZdiff,       \
                                                                        l_unitDirection,           \
                                                                        l_prePntA,                 \
                                                                        l_prePntB,                 \
                                                                        l_currPnt,                 \
                                                                        l_fDisCurr2AB, )           \
    reduction(+ : c_iCorMatchNum_, l_iDebugUse)

    std::vector<ceres::CostFunction*> l_vecCosts;

    for (int i = 0; i < c_iSrcMarkNum_; i++)
    {
        l_pntO = c_pSrcMarkCorPc_->points[i];
        // 搜索半径
        float l_fKdR = c_fMarkSrhR_;
        // 转雷达坐标点为预估全局坐标点
        LaserTransform<PointSource>::transformPoint(c_qEst_, c_tEst_, &l_pntO, l_pntWldO);

#ifdef MATCHDEBUG
        if (i)
            l_curPtr->points.push_back(l_curPoint);
        l_iDebugUse[3] = 0;
        l_curPoint.intensity = l_iDebugUse[3];
        l_curPoint.x = l_pntWldO.x;
        l_curPoint.y = l_pntWldO.y;
        l_curPoint.z = l_pntWldO.z;
#endif

        PointTarget l_pntSrh;
        l_pntSrh.x = l_pntWldO.x;
        l_pntSrh.y = l_pntWldO.y;
        l_pntSrh.z = l_pntWldO.z;

        int l_iMatchNum =
            c_pKdtFln2dMarkCor_->radiusSearch(l_pntSrh, l_fKdR, l_viPntSrhIdx, l_vfPntSrhSqDis);
        if (l_iMatchNum < 1)
        {
            if (p_iType == Mapping)
            {
                s_iMarkId++;
                c_pSrcMarkCorPc_->points[i].h = s_iMarkId;
            }
            continue;
        }

        if (p_iType == Mapping)
        {
            c_pSrcMarkCorPc_->points[i].h = c_pTgtMarkCorPc_->points[l_viPntSrhIdx[0]].h;
        }

#ifdef MATCHDEBUG
        l_iDebugUse[0]++;
        l_iDebugUse[3]++;
        l_curPoint.intensity = l_iDebugUse[3];
#endif

        // 提取有效高差范围的点，计算中心点
        // 匹配点容器
        std::vector<Eigen::Vector3d> l_vNearCorners;
        Eigen::Vector3d l_center(0, 0, 0);
        for (int j = 0; j < (int)l_viPntSrhIdx.size(); j++)
        {
            Eigen::Vector3d l_tmpCor(c_pTgtMarkCorPc_->points[l_viPntSrhIdx[j]].x,
                                     c_pTgtMarkCorPc_->points[l_viPntSrhIdx[j]].y,
                                     c_pTgtMarkCorPc_->points[l_viPntSrhIdx[j]].z);
            l_center = l_center + l_tmpCor;
            l_vNearCorners.push_back(l_tmpCor);
        }
        delete[] l_sCloudSmoothZdiff;

#ifdef MATCHDEBUG
        l_iDebugUse[1]++;
        l_iDebugUse[3]++;
        l_curPoint.intensity = l_iDebugUse[3];
#endif
        // 计算最近邻点的中心
        l_center = l_center / float(l_vNearCorners.size());

        // 协方差矩阵
        Eigen::Matrix3d l_covMat = Eigen::Matrix3d::Zero();
        for (int j = 0; j < (int)l_vNearCorners.size(); j++)
        {
            Eigen::Matrix<double, 3, 1> l_tmpZeroMean = l_vNearCorners[j] - l_center;
            l_covMat = l_covMat + l_tmpZeroMean * l_tmpZeroMean.transpose();
        }
        l_center -= c_tMovePose_;

#ifdef MATCHDEBUG
        l_iDebugUse[2]++;
        l_iDebugUse[3]++;
        l_curPoint.intensity = l_iDebugUse[3];
#endif

        // 如果临近点呈线状分布，最大的特征值对应的特征向量就是该线的方向向量
        // 从中心点沿着方向向量向两端移动0.1m，构造线上的两个点
        l_prePntA = 0.1 * Eigen::Vector3d(0, 0, 1) + l_center;
        l_prePntB = -0.1 * Eigen::Vector3d(0, 0, 1) + l_center;
        l_currPnt << l_pntO.x, l_pntO.y, l_pntO.z;
        l_currPnt -= c_tMovePose_;
        ceres::CostFunction* l_pCostFunc = LidarEdgeFactor::Create(l_currPnt, l_prePntA, l_prePntB);

        l_vecCosts.push_back(l_pCostFunc);
        c_iMarkMatchNum_++;

#ifdef MATCHDEBUG
        PointTarget l_tmp = PointTarget(l_prePntA[0] + c_tMovePose_.x(),
                                        l_prePntA[1] + c_tMovePose_.y(),
                                        l_prePntA[2] + c_tMovePose_.z());
        l_matchPtr->points.push_back(l_tmp);
        l_tmp = PointTarget(l_prePntB[0] + c_tMovePose_.x(),
                            l_prePntB[1] + c_tMovePose_.y(),
                            l_prePntB[2] + c_tMovePose_.z());
        l_matchPtr->points.push_back(l_tmp);
#endif
    }

    int l_iNumLoop = 0;
    if (c_iMarkMatchNum_ >= 3)
    {
        l_iNumLoop = c_iMarkWeightMax_;
    }
    else
    {
        l_iNumLoop = c_iMarkWeightMin_;
    }

    // if (l_vecCosts.size() * l_iNumLoop > 500)
    // {
    //     l_iNumLoop = 500 / l_vecCosts.size();
    //     if (l_iNumLoop < 1)
    //     {
    //         l_iNumLoop = 1;
    //     }
    // }

    for (int i = 0; i < (int)l_vecCosts.size(); i++)
    {
        for (int j = 0; j < l_iNumLoop; j++)
        {
            p_pbm.AddResidualBlock(l_vecCosts[i], p_pLossFunc, p_pdParaQ, p_pdParaT);
        }
    }

#ifdef MATCHDEBUG
    l_curPtr->points.push_back(l_curPoint);

    if (l_matchPtr->points.size() != 0)
    {
        printf("save match corn\n");
        writer.writeBinary("./MarkmatchFi.pcd", *l_matchPtr);
    }
    writer.writeBinary("./MarkcurQuanjuFi.pcd", *l_curPtr);
    printf("match corn: %d - %d - [ %d - %d - %d]\n",
           c_iSrcCorNum_,
           c_iCorMatchNum_,
           l_iDebugUse[0],
           l_iDebugUse[1],
           l_iDebugUse[2]);
#endif
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::radiusMatchLine(ceres::Problem& p_pbm,
                                                                  ceres::LossFunction* p_pLossFunc,
                                                                  double* p_pdParaQ,
                                                                  double* p_pdParaT)
{
    if (!c_pSrcCorPc_ || !c_pTgtCorPc_)
        return;
    float l_fCrossLineMatchMaxDist = sqrt(c_fDisThd_);
    float l_fCornerMatchMaxDist = sqrt(c_fDisThd_);
    std::vector<int> l_viPntSrhIdx;
    std::vector<float> l_vfPntSrhSqDis;
    PointSource l_pntO, l_pntWldO;

    // 点云高差排序容器
    s_SmoothZDiff* l_sCloudSmoothZdiff;

    // 方向向量
    Eigen::Vector3d l_unitDirection(0, 0, 0);
    Eigen::Vector3d l_prePntA, l_prePntB, l_currPnt;
    float l_fDisCurr2AB = 0;

#pragma omp parallel for nowait num_threads(MAX_NUM_THREAD) private(l_pntO,                        \
                                                                        l_pntWldO,                 \
                                                                        l_viPntSrhIdx,             \
                                                                        l_vfPntSrhSqDis,           \
                                                                        l_sCloudSmoothZdiff,       \
                                                                        l_unitDirection,           \
                                                                        l_prePntA,                 \
                                                                        l_prePntB,                 \
                                                                        l_currPnt,                 \
                                                                        l_fDisCurr2AB, )           \
    reduction(+ \ : c_iCorMatchNum_)
    for (int i = 0; i < c_iSrcCorNum_; i++)
    {
        l_pntO = c_pSrcCorPc_->points[i];
        // 搜索半径
        float l_fKdR = getValidSearchRadius_(&l_pntO, c_fCorSrhR_);
        // 转雷达坐标点为预估全局坐标点
        LaserTransform<PointSource>::transformPoint(c_qEst_, c_tEst_, &l_pntO, l_pntWldO);

        PointTarget l_pntSrh;
        l_pntSrh.x = l_pntWldO.x;
        l_pntSrh.y = l_pntWldO.y;
        l_pntSrh.z = l_pntWldO.z;

        if (c_pKdtCor_->radiusSearch(l_pntSrh, l_fKdR, l_viPntSrhIdx, l_vfPntSrhSqDis) <= 0)
        {
            // std::cout << "kdtree search failed!" << std::endl;
            continue;
        }
        if (l_viPntSrhIdx.size() < c_iSrhNum_)
        {
            continue;
        }

        // 进行z轴排序
        l_sCloudSmoothZdiff = new s_SmoothZDiff[l_viPntSrhIdx.size()];

        for (int j = 0; j < l_viPntSrhIdx.size(); j++)
        {
            l_sCloudSmoothZdiff[j].m_ZDiff =
                std::fabs(c_pTgtCorPc_->points[l_viPntSrhIdx[j]].z - l_pntWldO.z);
            l_sCloudSmoothZdiff[j].m_idx = l_viPntSrhIdx[j];
        }
        std::sort(l_sCloudSmoothZdiff, l_sCloudSmoothZdiff + l_viPntSrhIdx.size(), s_SortValue());

        // 提取有效高差范围的点，计算中心点
        // 匹配点容器
        std::vector<Eigen::Vector3d> l_vNearCorners;
        Eigen::Vector3d l_center(0, 0, 0);
        for (int j = 0; j < l_viPntSrhIdx.size(); j++)
        {
            // z轴插值过大，舍弃点
            // if (l_sCloudSmoothZdiff[j].m_ZDiff > c_fZThd_)
            // {
            //     continue;
            // }
            Eigen::Vector3d l_tmpCor(c_pTgtCorPc_->points[l_sCloudSmoothZdiff[j].m_idx].x,
                                     c_pTgtCorPc_->points[l_sCloudSmoothZdiff[j].m_idx].y,
                                     c_pTgtCorPc_->points[l_sCloudSmoothZdiff[j].m_idx].z);
            l_center = l_center + l_tmpCor;
            l_vNearCorners.push_back(l_tmpCor);
        }
        delete[] l_sCloudSmoothZdiff;
        if (l_vNearCorners.size() < 2)
        {
            continue;
        }

        // 计算最近邻点的中心
        l_center = l_center / float(l_vNearCorners.size());

        // 协方差矩阵
        Eigen::Matrix3d l_covMat = Eigen::Matrix3d::Zero();
        for (int j = 0; j < l_vNearCorners.size(); j++)
        {
            Eigen::Matrix<double, 3, 1> l_tmpZeroMean = l_vNearCorners[j] - l_center;
            l_covMat = l_covMat + l_tmpZeroMean * l_tmpZeroMean.transpose();
        }

        // 计算协方差矩阵的特征值和特征向量，用于判断这5个点是不是呈线状分布，此为PCA的原理
        Eigen::SelfAdjointEigenSolver<Eigen::Matrix3d> l_saes(l_covMat);
        // 判断非直线
        if (!(l_saes.eigenvalues()[2] > 5 * l_saes.eigenvalues()[1]))
        {
            continue;
        }
        l_unitDirection = l_saes.eigenvectors().col(2);
        // 如果临近点呈线状分布，最大的特征值对应的特征向量就是该线的方向向量
        // 从中心点沿着方向向量向两端移动0.1m，构造线上的两个点
        l_prePntA = 0.1 * l_unitDirection + l_center;
        l_prePntB = -0.1 * l_unitDirection + l_center;
        l_currPnt << l_pntO.x, l_pntO.y, l_pntO.z;
        l_fDisCurr2AB = distFromPointToLine_(
            Eigen::Vector3d(l_pntWldO.x, l_pntWldO.y, l_pntWldO.z), l_prePntA, l_prePntB);

        if (l_sCloudSmoothZdiff[5].m_ZDiff < c_fZThd_ && l_fDisCurr2AB < l_fCrossLineMatchMaxDist)
        {
            ceres::CostFunction* l_pCostFunc =
                LidarEdgeFactor::Create(l_currPnt, l_prePntA, l_prePntB);
            // p_pLossFunc = new ceres::HuberLoss(0.2);
            p_pbm.AddResidualBlock(l_pCostFunc, p_pLossFunc, p_pdParaQ, p_pdParaT);
            c_iCorMatchNum_++;
        }  // 非横线
        else if (l_fDisCurr2AB < l_fCornerMatchMaxDist)
        {
            ceres::CostFunction* l_pCostFunc =
                LidarEdgeFactor::Create(l_currPnt, l_prePntA, l_prePntB);
            p_pbm.AddResidualBlock(l_pCostFunc, p_pLossFunc, p_pdParaQ, p_pdParaT);
            c_iCorMatchNum_++;
        }
    }
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::radiusMatchPlane(ceres::Problem& p_pbm,
                                                                   ceres::LossFunction* p_pLossFunc,
                                                                   double* p_pdParaQ,
                                                                   double* p_pdParaT)
{
    if (!c_pSrcSufPc_ || !c_pTgtSurPc_)
        return;
    // 搜索的最大距离差
    //  float l_fVerticalSearchMaxDiff = c_fRThd_;
    //  面点最大匹配距离
    float l_SurfMatchMaxDist = sqrt(c_fDisThd_);
    if (c_bLooseMatch_)
    {
        l_SurfMatchMaxDist *= 2.0;
    }

    std::vector<int> l_viPntSrhIdx;
    std::vector<float> l_vfPntSrhSqDis;
    PointSource l_pntO;
    PointSource l_pntWldO;

    const int l_num = 5;

    Eigen::Vector3d l_norm(0, 0, 0);
    double l_dNegativeOADotNorm = 0;
    bool l_bPlaneValid = true;

    float l_fDisPnt2Plane = 0;
    Eigen::Vector3d l_currPnt(0, 0, 0);

#pragma omp parallel for nowait num_threads(MAX_NUM_THREAD) private(l_pntO,                        \
                                                                        l_pntWldO,                 \
                                                                        l_viPntSrhIdx,             \
                                                                        l_vfPntSrhSqDis,           \
                                                                        l_norm,                    \
                                                                        l_dNegativeOADotNorm,      \
                                                                        l_bPlaneValid,             \
                                                                        l_fDisPnt2Plane,           \
                                                                        l_currPnt)                 \
    reduction(+ \ : c_iSurMatchNum_)
    for (int i = 0; i < c_iSrcSurNum_; i++)
    {
        l_bPlaneValid = true;
        l_pntO = c_pSrcSufPc_->points[i];
        float l_fKdR = getValidSearchRadius_(&l_pntO, c_fSurSrhR_);
        // 转雷达坐标点为预估全局坐标点
        LaserTransform<PointSource>::transformPoint(c_qEst_, c_tEst_, &l_pntO, l_pntWldO);
        PointTarget l_pntSrh;
        l_pntSrh.x = l_pntWldO.x;
        l_pntSrh.y = l_pntWldO.y;
        l_pntSrh.z = l_pntWldO.z;

        if (c_pKdtSur_->radiusSearch(l_pntSrh, l_fKdR, l_viPntSrhIdx, l_vfPntSrhSqDis) <= 0)
        {
            continue;
        }

        // if (l_viPntSrhIdx.size() < c_iSrhNum_)
        // {
        //     continue;
        // }

        if (l_viPntSrhIdx.size() < 6)
        {
            continue;
        }

        Eigen::Matrix<double, l_num, 3> l_matA0;
        Eigen::Matrix<double, l_num, 1> l_matB0 = -1 * Eigen::Matrix<double, l_num, 1>::Ones();
        // //如果临近点最大距离大于阈值，不进行拟合
        // if (l_vfPntSrhSqDis[l_num - 1] > l_fVerticalSearchMaxDiff)
        // {
        //     continue;
        // }
        for (int j = 0; j < l_num; j++)
        {
            l_matA0(j, 0) = c_pTgtSurPc_->points[l_viPntSrhIdx[j]].x;
            l_matA0(j, 1) = c_pTgtSurPc_->points[l_viPntSrhIdx[j]].y;
            l_matA0(j, 2) = c_pTgtSurPc_->points[l_viPntSrhIdx[j]].z;
        }

        // 求解这个最小二乘问题，可得平面的法向量，find the l_norm of plane
        l_norm = l_matA0.colPivHouseholderQr().solve(l_matB0);
        // Ax + By + Cz + 1 = 0，全部除以法向量的模长，方程依旧成立，而且使得法向量归一化了
        l_dNegativeOADotNorm = 1 / l_norm.norm();
        l_norm.normalize();

        // Here n(pa, pb, pc) is unit l_norm of plane
        for (int j = 0; j < l_num; j++)
        {
            // 点(x0, y0, z0)到平面Ax + By + Cz + D = 0 的距离公式 = fabs(Ax0 + By0 +
            // Cz0 + D) / sqrt(A^2 + B^2 + C^2)
            if (fabs(l_norm(0) * c_pTgtSurPc_->points[l_viPntSrhIdx[j]].x
                     + l_norm(1) * c_pTgtSurPc_->points[l_viPntSrhIdx[j]].y
                     + l_norm(2) * c_pTgtSurPc_->points[l_viPntSrhIdx[j]].z + l_dNegativeOADotNorm)
                > 0.15)
            {
                l_bPlaneValid = false;  // 平面没有拟合好，平面“不够平”
                break;
            }
        }

        l_currPnt << l_pntO.x, l_pntO.y, l_pntO.z;

        if (l_bPlaneValid)
        {
            l_fDisPnt2Plane =
                distFromPointToPlane_(Eigen::Vector3d(l_pntWldO.x, l_pntWldO.y, l_pntWldO.z),
                                      l_norm,
                                      l_dNegativeOADotNorm);

            // 距离约束
            if (l_SurfMatchMaxDist > l_fDisPnt2Plane)
            {
                ceres::CostFunction* l_pCostFunc =
                    LidarPlaneNormFactor::Create(l_currPnt, l_norm, l_dNegativeOADotNorm);
                p_pbm.AddResidualBlock(l_pCostFunc, p_pLossFunc, p_pdParaQ, p_pdParaT);
                c_iSurMatchNum_++;
            }
        }
    }
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::nearestKMatchLine(
    ceres::Problem& p_pbm,
    ceres::LossFunction* p_pLossFunc,
    double* p_pdParaQ,
    double* p_pdParaT)
{
    if (!c_pSrcCorPc_ || !c_pTgtCorPc_)
        return;

#ifdef MATCHDEBUG
    pcl::PCDWriter writer;
    writer.writeBinary("./subFi.pcd", *c_pTgtCorPc_);
    writer.writeBinary("./rawFi.pcd", *c_pSrcCorPc_);
    pcl::PointCloud<pcl::PointXYZI>::Ptr l_curPtr(new pcl::PointCloud<pcl::PointXYZI>());
    pcl::PointXYZI l_curPoint;
    PointCloudTargetPtr l_matchPtr(new PointCloudTarget());
#endif
    // debugmatch计数
    int l_iDebugUse[4] = {0, 0, 0, 0};

    float l_fCrossLineMatchMaxDist = sqrt(c_fDisThd_);
    float l_fCornerMatchMaxDist = sqrt(c_fDisThd_);
    // 搜索的最大距离差
    float l_fVerticalSearchMaxDiff = c_fRThd_;

    if (c_bLooseMatch_)
    {
        l_fCrossLineMatchMaxDist *= 2.0;
        l_fCornerMatchMaxDist *= 2.0;
    }

    std::vector<int> l_viPntSrhIdx;
    std::vector<float> l_vfPntSrhSqDis;
    PointSource l_pntO, l_pntWldO;

    // 匹配点容器
    std::vector<Eigen::Vector3d> l_vNearCorners(5);
    // Eigen::Vector3d              l_center(0, 0, 0);
    // 点云高差排序容器
    s_SmoothZDiff l_sCloudSmoothZdiff[c_iKdNum_];

    // 协方差矩阵
    // Eigen::Matrix3d             l_covMat = Eigen::Matrix3d::Zero();
    // Eigen::Matrix<double, 3, 1> l_tmpZeroMean(0, 0, 0);

    // 方向向量
    Eigen::Vector3d l_unitDirection(0, 0, 0);
    Eigen::Vector3d l_prePntA, l_prePntB, l_currPnt;
    float l_fDisCurr2AB = 0;

#pragma omp parallel for nowait num_threads(MAX_NUM_THREAD) private(l_pntO,                        \
                                                                        l_pntWldO,                 \
                                                                        l_viPntSrhIdx,             \
                                                                        l_vfPntSrhSqDis,           \
                                                                        l_vNearCorners,            \
                                                                        l_sCloudSmoothZdiff,       \
                                                                        l_unitDirection,           \
                                                                        l_prePntA,                 \
                                                                        l_prePntB,                 \
                                                                        l_currPnt,                 \
                                                                        l_fDisCurr2AB)             \
    reduction(+ : c_iCorMatchNum_, l_iDebugUse)
    for (int i = 0; i < c_iSrcCorNum_; i++)
    {
        l_pntO = c_pSrcCorPc_->points[i];
        // 转雷达坐标点为预估全局坐标点
        LaserTransform<PointSource>::transformPoint(c_qEst_, c_tEst_, &l_pntO, l_pntWldO);
        PointTarget l_pntSrh;
        l_pntSrh.x = l_pntWldO.x;
        l_pntSrh.y = l_pntWldO.y;
        l_pntSrh.z = l_pntWldO.z;

#ifdef MATCHDEBUG
        if (i)
            l_curPtr->points.push_back(l_curPoint);
        l_iDebugUse[3] = 0;
        l_curPoint.intensity = l_use;
        l_curPoint.x = l_pntWldO.x;
        l_curPoint.y = l_pntWldO.y;
        l_curPoint.z = l_pntWldO.z;
#endif

        // 在submap的corner特征点（target）中，寻找距离当前帧corner特征点（source）最近的5个点
        if (c_pKdtCor_->nearestKSearch(l_pntSrh, c_iKdNum_, l_viPntSrhIdx, l_vfPntSrhSqDis) <= 0)
        {
            continue;
        }

#ifdef MATCHDEBUG
        l_iDebugUse[0]++;
        l_iDebugUse[3]++;
        l_curPoint.intensity = l_iDebugUse[3];
#endif

        // 进行z轴排序
        for (int j = 0; j < (int)l_viPntSrhIdx.size(); j++)
        {
            l_sCloudSmoothZdiff[j].m_ZDiff =
                std::fabs(c_pTgtCorPc_->points[l_viPntSrhIdx[j]].z - l_pntWldO.z);
            l_sCloudSmoothZdiff[j].m_idx = l_viPntSrhIdx[j];
        }
        std::sort(l_sCloudSmoothZdiff, l_sCloudSmoothZdiff + c_iKdNum_, s_SortValue());
        Eigen::Vector3d l_center(0, 0, 0);
        for (int j = 0; j < 5; j++)
        {
            l_vNearCorners[j](0) = c_pTgtCorPc_->points[l_sCloudSmoothZdiff[j].m_idx].x;
            l_vNearCorners[j](1) = c_pTgtCorPc_->points[l_sCloudSmoothZdiff[j].m_idx].y;
            l_vNearCorners[j](2) = c_pTgtCorPc_->points[l_sCloudSmoothZdiff[j].m_idx].z;

            l_center = l_center + l_vNearCorners[j];
        }
        // 计算这个5个最近邻点的中心
        l_center = l_center / float(l_vNearCorners.size());

        // 协方差矩阵
        Eigen::Matrix3d l_covMat = Eigen::Matrix3d::Zero();
        for (int j = 0; j < (int)l_vNearCorners.size(); j++)
        {
            Eigen::Matrix<double, 3, 1> l_tmpZeroMean = l_vNearCorners[j] - l_center;
            l_covMat = l_covMat + l_tmpZeroMean * l_tmpZeroMean.transpose();
        }
        l_center -= c_tMovePose_;

        // 计算协方差矩阵的特征值和特征向量，用于判断这5个点是不是呈线状分布，此为PCA的原理
        Eigen::SelfAdjointEigenSolver<Eigen::Matrix3d> l_saes(l_covMat);
        // 判断非直线
        if (!(l_saes.eigenvalues()[2] > 5 * l_saes.eigenvalues()[1]))
        {
            continue;
        }

#ifdef MATCHDEBUG
        l_iDebugUse[1]++;
        l_iDebugUse[3]++;
        l_curPoint.intensity = l_iDebugUse[3];
#endif

        l_unitDirection = l_saes.eigenvectors().col(2);
        // 如果5个点呈线状分布，最大的特征值对应的特征向量就是该线的方向向量
        // 从中心点沿着方向向量向两端移动0.1m，构造线上的两个点
        l_prePntA = 0.1 * l_unitDirection + l_center;
        l_prePntB = -0.1 * l_unitDirection + l_center;
        l_currPnt << l_pntO.x, l_pntO.y, l_pntO.z;
        l_currPnt -= c_tMovePose_;
        l_fDisCurr2AB = distFromPointToLine_(l_currPnt, l_prePntA, l_prePntB);

        // l_fDisCurr2AB = geo.DistFromPointToLine(curr_point, l_prePntA, l_prePntB);
        // 搜索横线
        // if (l_sCloudSmoothZdiff[5].m_ZDiff < c_fZThd_ && l_bHorizionLineEnableFlag &&
        //     l_fDisCurr2AB < l_fCrossLineMatchMaxDist)
        if (l_sCloudSmoothZdiff[5].m_ZDiff < c_fZThd_ && l_fDisCurr2AB < l_fCrossLineMatchMaxDist)
        {
            ceres::CostFunction* l_pCostFunc =
                LidarEdgeFactor::Create(l_currPnt, l_prePntA, l_prePntB);
            // p_pLossFunc = new ceres::HuberLoss(0.2);
            p_pbm.AddResidualBlock(l_pCostFunc, p_pLossFunc, p_pdParaQ, p_pdParaT);
            c_iCorMatchNum_++;

#ifdef MATCHDEBUG
            l_iDebugUse[2]++;
            l_iDebugUse[3]++;
            l_curPoint.intensity = l_iDebugUse[3];

            PointTarget l_tmp = PointTarget(l_prePntA[0] + c_tMovePose_.x(),
                                            l_prePntA[1] + c_tMovePose_.y(),
                                            l_prePntA[2] + c_tMovePose_.z());
            l_matchPtr->points.push_back(l_tmp);
            l_tmp = PointTarget(l_prePntB[0] + c_tMovePose_.x(),
                                l_prePntB[1] + c_tMovePose_.y(),
                                l_prePntB[2] + c_tMovePose_.z());
            l_matchPtr->points.push_back(l_tmp);
#endif
        }
        // 非横线
        else if (l_vfPntSrhSqDis[4] < l_fVerticalSearchMaxDiff
                 && l_fDisCurr2AB < l_fCornerMatchMaxDist)
        {
            ceres::CostFunction* l_pCostFunc =
                LidarEdgeFactor::Create(l_currPnt, l_prePntA, l_prePntB);
            p_pbm.AddResidualBlock(l_pCostFunc, p_pLossFunc, p_pdParaQ, p_pdParaT);
            c_iCorMatchNum_++;

#ifdef MATCHDEBUG
            l_iDebugUse[2]++;
            l_iDebugUse[3]++;
            l_curPoint.intensity = l_iDebugUse[3];

            PointTarget l_tmp = PointTarget(l_prePntA[0] + c_tMovePose_.x(),
                                            l_prePntA[1] + c_tMovePose_.y(),
                                            l_prePntA[2] + c_tMovePose_.z());
            l_matchPtr->points.push_back(l_tmp);
            l_tmp = PointTarget(l_prePntB[0] + c_tMovePose_.x(),
                                l_prePntB[1] + c_tMovePose_.y(),
                                l_prePntB[2] + c_tMovePose_.z());
            l_matchPtr->points.push_back(l_tmp);
#endif
        }
    }
#ifdef MATCHDEBUG
    l_curPtr->points.push_back(l_curPoint);

    if (l_matchPtr->points.size() != 0)
    {
        printf("save match corn\n");
        writer.writeBinary("./matchFi.pcd", *l_matchPtr);
    }
    writer.writeBinary("./curQuanjuFi.pcd", *l_curPtr);
    printf("match corn: %d - %d - [ %d - %d - %d]\n",
           c_iSrcCorNum_,
           c_iCorMatchNum_,
           l_iDebugUse[0],
           l_iDebugUse[1],
           l_iDebugUse[2]);
#endif
}

// 双层墙
template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::nearestKMatchPlaneiVox(
    ceres::Problem& p_pbm,
    ceres::LossFunction* p_pLossFunc,
    double* p_pdParaQ,
    double* p_pdParaT)
{
    if (!c_pSrcSufPc_ || !c_pIvox_)
        return;

#ifdef MATCHDEBUG
    pcl::PCDWriter writer;
    pcl::PointCloud<pcl::PointXYZI>::Ptr l_matchPtr(new pcl::PointCloud<pcl::PointXYZI>());
    pcl::PointCloud<pcl::PointXYZINormal>::Ptr l_curPtr(
        new pcl::PointCloud<pcl::PointXYZINormal>());
    pcl::PointXYZI l_matchPoint;
    pcl::PointXYZINormal l_curPoint;
    writer.writeBinary("./subSe.pcd", *c_pTgtSurPc_);
    writer.writeBinary("./curRawSe.pcd", *c_pSrcSufPc_);
#endif
    int l_iDebugUse[4] = {0, 0, 0, 0};

    // 搜索的最大距离差
    float l_fVerticalSearchMaxDiff = c_fRThd_;
    // 面点最大匹配距离
    float l_SurfMatchMaxDist = sqrt(c_fDisThd_);
    if (c_bLooseMatch_)
    {
        l_SurfMatchMaxDist *= 2.0;
    }

    std::vector<PointTarget> l_viPntSrhIdx;
    std::vector<float> l_vfPntSrhSqDis;
    PointSource l_pntO;
    PointSource l_pntWldO;

    const int l_num = 5;

    Eigen::Vector3d l_norm(0, 0, 0);
    double l_dNegativeOADotNorm = 0;
    bool l_bPlaneValid = true;

    float l_fDisPnt2Plane = 0;
    Eigen::Vector3d l_currPnt(0, 0, 0);

#pragma omp parallel for nowait num_threads(MAX_NUM_THREAD) private(l_pntO,                        \
                                                                        l_pntWldO,                 \
                                                                        l_viPntSrhIdx,             \
                                                                        l_vfPntSrhSqDis,           \
                                                                        l_norm,                    \
                                                                        l_dNegativeOADotNorm,      \
                                                                        l_bPlaneValid,             \
                                                                        l_fDisPnt2Plane,           \
                                                                        l_currPnt)                 \
    reduction(+ : c_iSurMatchNum_, l_iDebugUse)
    // 回环检测通过面点匹配平均距离作为评价标准
    double l_dSumDis = 0;  // 统计总距离
    int l_inum = 0;        // 统计匹配次数
    c_fDistance_ = 10;     // 输出平均距离

    for (int i = 0; i < c_iSrcSurNum_; i++)
    {
#ifdef MATCHDEBUG
        if (i)
            l_curPtr->points.push_back(l_curPoint);
        l_iDebugUse[3] = 0;

        l_curPoint.intensity = 0;
        l_curPoint.normal_x = 0;
        l_curPoint.normal_y = 0;
        l_curPoint.normal_z = 0;
        l_curPoint.curvature = 0;
#endif
        l_viPntSrhIdx.clear();
        l_bPlaneValid = false;
        l_pntO = c_pSrcSufPc_->points[i];
        // 转雷达坐标点为预估全局坐标点
        LaserTransform<PointSource>::transformPoint(c_qEst_, c_tEst_, &l_pntO, l_pntWldO);

#ifdef MATCHDEBUG
        l_curPoint.x = l_pntWldO.x;
        l_curPoint.y = l_pntWldO.y;
        l_curPoint.z = l_pntWldO.z;
#endif
        // 区分房顶与地面点，不参与双层墙计算
        changePointLabel_(c_pSrcSufPc_->points[i], true);

        PointTarget l_pntSrh;
        PointCloudTargetPtr l_pntnear(new PointCloudTarget());
        l_pntSrh.x = l_pntWldO.x;
        l_pntSrh.y = l_pntWldO.y;
        l_pntSrh.z = l_pntWldO.z;
        l_pntSrh.v = c_pSrcSufPc_->points[i].v;

        c_pSrcSufPc_->points[i].h =
            getNormalAngle(c_trans_.x() - l_pntSrh.x, c_trans_.y() - l_pntSrh.y);
        c_pSrcSufPc_->points[i].s = 0;

        if (!c_pIvox_->getClosestGridPoint(l_pntSrh, l_pntnear, c_iKdNum_, 1))
        {
            continue;
        }

        if (l_pntnear->points.size() < 3)
        {
            continue;
        }

        int l_iPointNum = -1;
        if (c_pSrcSufPc_->points[i].v == 0)
        {
            float l_dMedWallAng;  // 搜到点中墙的中位数角度
            std::vector<double> l_vdTgtAngle;
            std::vector<double> l_vdTgtEdgeAngle;  // 存储边界角度
            for (std::size_t j = 0; j < l_pntnear->points.size(); ++j)
            {
                // 当前点的标签不为0及高度在范围内
                if (filterZValue(c_pSrcSufPc_->points[i], l_pntnear->points[j]))
                {
                    // [0,30)对应原始角度[135,165)，排序时应该排在后面
                    if (l_pntnear->points[j].s < 30.0)
                        l_vdTgtEdgeAngle.push_back(l_pntnear->points[j].s);
                    else
                        l_vdTgtAngle.push_back(l_pntnear->points[j].s);
                }
            }

#ifdef MATCHDEBUG
            l_iDebugUse[0]++;
            l_iDebugUse[3]++;
            l_curPoint.intensity = l_iDebugUse[3];
#endif

#ifdef MATCHDEBUG
            l_iDebugUse[1]++;
            l_iDebugUse[3]++;
            l_curPoint.intensity = l_iDebugUse[3];
#endif

            // 删除距离远的点
            // l_viPntSrhIdx.resize(j);
            // int l_iPointNum = l_viPntSrhIdx.size();
            std::vector<std::vector<PointTarget>> l_viLabel(2);      // 用于平面拟合
            std::vector<std::vector<PointTarget>> l_viLabelLine(2);  // 用于寻找直线

            if (l_vdTgtAngle.size() == 0)
            {
                continue;
            }
            else
            {
                l_dMedWallAng =
                    getMedWallAngle(l_vdTgtAngle, l_vdTgtEdgeAngle);  // 求墙的中位数角度
            }

            // 0 1分割
            for (std::size_t id = 0; id < l_pntnear->points.size(); id++)
            {
                edgeAngleHandle(l_pntnear->points[id].h, l_dMedWallAng);   // 处理边界角度
                if (angleFormula(l_pntnear->points[id].h, l_dMedWallAng))  // 判断点的标签
                {
                    l_viLabel[0].push_back(l_pntnear->points[id]);
                    if (filterZValue(c_pSrcSufPc_->points[i], l_pntnear->points[id]))  // 高度过滤
                        l_viLabelLine[0].push_back(l_pntnear->points[id]);
                }
                else
                {
                    l_viLabel[1].push_back(l_pntnear->points[id]);
                    if (filterZValue(c_pSrcSufPc_->points[i], l_pntnear->points[id]))
                        l_viLabelLine[1].push_back(l_pntnear->points[id]);
                }
            }

            // 在点数较多的类别中找最长直线
            if (l_viLabelLine[0].size() > l_viLabelLine[1].size())
            {
                if (l_viLabelLine[0].size() < 3)
                {
                    continue;
                }
                c_pSrcSufPc_->points[i].s = getWallLine(l_viLabelLine[0]);
            }
            else
            {
                if (l_viLabelLine[1].size() < 3)
                {
                    continue;
                }
                c_pSrcSufPc_->points[i].s = getWallLine(l_viLabelLine[1]);
            }

            edgeAngleHandle(c_pSrcSufPc_->points[i].h, c_pSrcSufPc_->points[i].s);
            // 判断当前点的标签
            if (angleFormula(c_pSrcSufPc_->points[i].h, c_pSrcSufPc_->points[i].s))
            {
                if (l_viLabel[0].size() < 3)
                {
                    continue;
                }
                c_pSrcSufPc_->points[i].v = 1.0;
                l_viPntSrhIdx = l_viLabel[0];
            }
            else
            {
                if (l_viLabel[1].size() < 3)
                {
                    continue;
                }
                c_pSrcSufPc_->points[i].v = -1.0;
                l_viPntSrhIdx = l_viLabel[1];
            }
            l_iPointNum = l_viPntSrhIdx.size();
        }
        else
        {
            l_iPointNum = l_pntnear->points.size();
        }

        if (i > c_iSampleNum_)
        {
            continue;
        }

        // 计算平面方程
        //  int l_iPointNum = l_viPntSrhIdx.size();
        Eigen::Matrix<double, 1, 3> l_center;
        l_center << 0, 0, 0;
        Eigen::MatrixXd l_matA0;
        l_matA0.resize(l_iPointNum, 3);
        Eigen::MatrixXd l_matB0;
        l_matB0.resize(l_iPointNum, 1);
        for (int j = 0; j < l_iPointNum; j++)
            l_matB0(j, 0) = -1;
        for (int j = 0; j < l_iPointNum; j++)
        {
            if (c_pSrcSufPc_->points[i].v == 0)
            {
                l_matA0(j, 0) = l_viPntSrhIdx[j].x - c_tMovePose_[0];
                l_matA0(j, 1) = l_viPntSrhIdx[j].y - c_tMovePose_[1];
                l_matA0(j, 2) = l_viPntSrhIdx[j].z - c_tMovePose_[2];
            }
            else
            {
                l_matA0(j, 0) = l_pntnear->points[j].x - c_tMovePose_[0];
                l_matA0(j, 1) = l_pntnear->points[j].y - c_tMovePose_[1];
                l_matA0(j, 2) = l_pntnear->points[j].z - c_tMovePose_[2];
            }
            l_center += l_matA0.row(j);
        }

        // 计算这个5个最近邻点的中心
        l_center = l_center / float(l_iPointNum);
        // 协方差矩阵
        Eigen::Matrix3d l_covMat = Eigen::Matrix3d::Zero();
        for (int j = 0; j < l_iPointNum; j++)
        {
            Eigen::Matrix<double, 3, 1> l_tmpZeroMean;
            l_tmpZeroMean = (l_matA0.row(j) - l_center).transpose();
            l_covMat = l_covMat + l_tmpZeroMean * l_tmpZeroMean.transpose();
        }
        // 计算协方差矩阵的特征值和特征向量，用于判断这5个点是不是呈线状分布，此为PCA的原理
        Eigen::SelfAdjointEigenSolver<Eigen::Matrix3d> l_saes(l_covMat);
        // 判断非平面
        //  if ((l_saes.eigenvalues().normalized())[2] > 0.995)
        if ((l_saes.eigenvalues().normalized())[c_iPlanePcaDirection] > c_fPlanePcaValue)
        {
            // 拟合未成功的需要将标签回退
            changePointLabel_(c_pSrcSufPc_->points[i], false);
            continue;
        }
        // l_matA0.rowwise() -= c_tMovePose_.transpose();

        // // 求解这个最小二乘问题，可得平面的法向量，find the l_norm of plane
        // l_norm = l_matA0.colPivHouseholderQr().solve(l_matB0);
        // // Ax + By + Cz + 1 = 0，全部除以法向量的模长，方程依旧成立，而且使得法向量归一化了
        // l_dNegativeOADotNorm = 1 / l_norm.norm();
        // l_norm.normalize();
        l_norm = l_saes.eigenvectors().leftCols<1>().eval();
        l_dNegativeOADotNorm = -l_center * l_norm;

#ifdef MATCHDEBUG
        l_iDebugUse[2]++;
        l_iDebugUse[3]++;
        l_curPoint.intensity = l_iDebugUse[3];
        l_curPoint.normal_x = l_norm(0);
        l_curPoint.normal_y = l_norm(1);
        l_curPoint.normal_z = l_norm(2);
#endif

        // Here n(pa, pb, pc) is unit l_norm of plane
        // 使用平均误差判定平面拟合好坏
        float l_fMeanDiff = 0;
        for (int j = 0; j < l_iPointNum; j++)
        {
            // 点(x0, y0, z0)到平面Ax + By + Cz + D = 0 的距离公式 = fabs(Ax0 + By0 +
            // Cz0 + D) / sqrt(A^2 + B^2 + C^2)
            l_fMeanDiff += fabs(l_norm(0) * l_matA0(j, 0) + l_norm(1) * l_matA0(j, 1)
                                + l_norm(2) * l_matA0(j, 2) + l_dNegativeOADotNorm);
#ifdef MATCHDEBUG
            l_matchPoint.x = c_pTgtSurPc_->points[l_viPntSrhIdx[j]].x;
            l_matchPoint.y = c_pTgtSurPc_->points[l_viPntSrhIdx[j]].y;
            l_matchPoint.z = c_pTgtSurPc_->points[l_viPntSrhIdx[j]].z;
            l_matchPoint.intensity = -2;
            l_matchPtr->points.push_back(l_matchPoint);
#endif
        }
        l_fMeanDiff /= static_cast<float>(l_iPointNum);
        // if (l_fMeanDiff < 0.15)
        if (l_fMeanDiff < c_fPlaneMeanDiff)
            l_bPlaneValid = true;
        else
        {
            // 拟合未成功的需要将标签回退
            changePointLabel_(c_pSrcSufPc_->points[i], false);
        }

        l_currPnt << l_pntSrh.x, l_pntSrh.y, l_pntSrh.z;
        l_currPnt -= c_tMovePose_;

#ifdef MATCHDEBUG
        l_iDebugUse[3]++;
        l_curPoint.intensity = l_iDebugUse[3];
        int l_sizeMatchPtr = l_matchPtr->points.size();
        for (int o = l_sizeMatchPtr - l_iPointNum; o < l_sizeMatchPtr; o++)
            l_matchPtr->points[o].intensity = -1;
#endif

        if (l_bPlaneValid)
        {
            l_fDisPnt2Plane = distFromPointToPlane_(l_currPnt, l_norm, l_dNegativeOADotNorm);

            l_dSumDis += l_fDisPnt2Plane;
            l_inum++;
            // 距离约束
            if (l_SurfMatchMaxDist > l_fDisPnt2Plane)
            {
                l_currPnt << l_pntO.x, l_pntO.y, l_pntO.z;
                l_currPnt -= c_tMovePose_;
                ceres::CostFunction* l_pCostFunc =
                    LidarPlaneNormFactor::Create(l_currPnt, l_norm, l_dNegativeOADotNorm);
                p_pbm.AddResidualBlock(l_pCostFunc, p_pLossFunc, p_pdParaQ, p_pdParaT);
                c_iSurMatchNum_++;

#ifdef MATCHDEBUG
                l_iDebugUse[3]++;
                l_curPoint.intensity = l_iDebugUse[3];
                for (int o = l_sizeMatchPtr - l_iPointNum; o < l_sizeMatchPtr; o++)
                    l_matchPtr->points[o].intensity = i;
#endif
            }
            else
            {
                // 拟合未成功的需要将标签回退
                changePointLabel_(c_pSrcSufPc_->points[i], false);
            }
        }
    }
    c_fDistance_ = l_dSumDis / l_inum;
#ifdef MATCHDEBUG
    l_curPtr->points.push_back(l_curPoint);
    if (l_matchPtr->points.size() != 0)
    {
        printf("save match surf\n");
        writer.writeBinary("./matchSe.pcd", *l_matchPtr);
    }
    writer.writeBinary("./curQuanSe.pcd", *l_curPtr);

    printf("match surf %d - %d [ %d - %d - %d ]\n",
           c_iSrcSurNum_,
           c_iSurMatchNum_,
           l_iDebugUse[0],
           l_iDebugUse[1],
           l_iDebugUse[2]);
#endif
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::nearestKMatchPlane(
    ceres::Problem& p_pbm,
    ceres::LossFunction* p_pLossFunc,
    double* p_pdParaQ,
    double* p_pdParaT)
{
    if (!c_pSrcSufPc_ || !c_pTgtSurPc_)
        return;

#ifdef MATCHDEBUG
    pcl::PCDWriter writer;
    pcl::PointCloud<pcl::PointXYZI>::Ptr l_matchPtr(new pcl::PointCloud<pcl::PointXYZI>());
    pcl::PointCloud<pcl::PointXYZINormal>::Ptr l_curPtr(
        new pcl::PointCloud<pcl::PointXYZINormal>());
    pcl::PointXYZI l_matchPoint;
    pcl::PointXYZINormal l_curPoint;
    writer.writeBinary("./subSe.pcd", *c_pTgtSurPc_);
    writer.writeBinary("./curRawSe.pcd", *c_pSrcSufPc_);
#endif
    int l_iDebugUse[4] = {0, 0, 0, 0};

    // 搜索的最大距离差
    float l_fVerticalSearchMaxDiff = c_fRThd_;
    // 面点最大匹配距离
    float l_SurfMatchMaxDist = sqrt(c_fDisThd_);
    if (c_bLooseMatch_)
    {
        l_SurfMatchMaxDist *= 2.0;
    }

    std::vector<int> l_viPntSrhIdx;
    std::vector<float> l_vfPntSrhSqDis;
    PointSource l_pntO;
    PointSource l_pntWldO;

    const int l_num = 5;

    Eigen::Vector3d l_norm(0, 0, 0);
    double l_dNegativeOADotNorm = 0;
    bool l_bPlaneValid = true;

    float l_fDisPnt2Plane = 0;
    Eigen::Vector3d l_currPnt(0, 0, 0);
#pragma omp parallel for nowait num_threads(MAX_NUM_THREAD) private(l_pntO,                        \
                                                                        l_pntWldO,                 \
                                                                        l_viPntSrhIdx,             \
                                                                        l_vfPntSrhSqDis,           \
                                                                        l_norm,                    \
                                                                        l_dNegativeOADotNorm,      \
                                                                        l_bPlaneValid,             \
                                                                        l_fDisPnt2Plane,           \
                                                                        l_currPnt)                 \
    reduction(+ : c_iSurMatchNum_, l_iDebugUse)
    for (int i = 0; i < c_iSrcSurNum_; i++)
    {
#ifdef MATCHDEBUG
        if (i)
            l_curPtr->points.push_back(l_curPoint);
        l_iDebugUse[3] = 0;

        l_curPoint.intensity = 0;
        l_curPoint.normal_x = 0;
        l_curPoint.normal_y = 0;
        l_curPoint.normal_z = 0;
        l_curPoint.curvature = 0;
#endif

        l_bPlaneValid = false;
        l_pntO = c_pSrcSufPc_->points[i];
        // 转雷达坐标点为预估全局坐标点
        LaserTransform<PointSource>::transformPoint(c_qEst_, c_tEst_, &l_pntO, l_pntWldO);

#ifdef MATCHDEBUG
        l_curPoint.x = l_pntWldO.x;
        l_curPoint.y = l_pntWldO.y;
        l_curPoint.z = l_pntWldO.z;
#endif

        PointTarget l_pntSrh;
        l_pntSrh.x = l_pntWldO.x;
        l_pntSrh.y = l_pntWldO.y;
        l_pntSrh.z = l_pntWldO.z;

        if (c_pKdtSur_->nearestKSearch(l_pntSrh, c_iKdNum_, l_viPntSrhIdx, l_vfPntSrhSqDis) < 4)
        {
            continue;
        }

#ifdef MATCHDEBUG
        l_iDebugUse[0]++;
        l_iDebugUse[3]++;
        l_curPoint.intensity = l_iDebugUse[3];
#endif

        // 计算范围内点的数量
        int j = 0;
        for (; j < (int)l_vfPntSrhSqDis.size(); ++j)
        {
            if (l_vfPntSrhSqDis[j] > l_fVerticalSearchMaxDiff)
                break;
        }
        // 如果点数不足，退出
        if (j < 3)
        {
            continue;
        }

#ifdef MATCHDEBUG
        l_iDebugUse[1]++;
        l_iDebugUse[3]++;
        l_curPoint.intensity = l_iDebugUse[3];
#endif

        // 删除距离远的点
        l_viPntSrhIdx.resize(j);
        int l_iPointNum = l_viPntSrhIdx.size();
        // 计算平面方程
        Eigen::Matrix<double, 1, 3> l_center;
        l_center << 0, 0, 0;
        Eigen::MatrixXd l_matA0;
        l_matA0.resize(l_iPointNum, 3);
        Eigen::MatrixXd l_matB0;
        l_matB0.resize(l_iPointNum, 1);
        for (int j = 0; j < l_iPointNum; j++)
            l_matB0(j, 0) = -1;
        for (int j = 0; j < l_iPointNum; j++)
        {
            l_matA0(j, 0) = c_pTgtSurPc_->points[l_viPntSrhIdx[j]].x - c_tMovePose_[0];
            l_matA0(j, 1) = c_pTgtSurPc_->points[l_viPntSrhIdx[j]].y - c_tMovePose_[1];
            l_matA0(j, 2) = c_pTgtSurPc_->points[l_viPntSrhIdx[j]].z - c_tMovePose_[2];
            l_center += l_matA0.row(j);
        }
        // 计算这个5个最近邻点的中心
        l_center = l_center / float(l_iPointNum);
        // 协方差矩阵
        Eigen::Matrix3d l_covMat = Eigen::Matrix3d::Zero();
        for (int j = 0; j < l_iPointNum; j++)
        {
            Eigen::Matrix<double, 3, 1> l_tmpZeroMean;
            l_tmpZeroMean = (l_matA0.row(j) - l_center).transpose();
            l_covMat = l_covMat + l_tmpZeroMean * l_tmpZeroMean.transpose();
        }
        // 计算协方差矩阵的特征值和特征向量，用于判断这5个点是不是呈线状分布，此为PCA的原理
        Eigen::SelfAdjointEigenSolver<Eigen::Matrix3d> l_saes(l_covMat);
        // 判断非平面
        //  if ((l_saes.eigenvalues().normalized())[2] > 0.995)
        if ((l_saes.eigenvalues().normalized())[c_iPlanePcaDirection] > c_fPlanePcaValue)
        {
            continue;
        }

        // l_matA0.rowwise() -= c_tMovePose_.transpose();
        // // 求解这个最小二乘问题，可得平面的法向量，find the l_norm of plane
        // l_norm = l_matA0.colPivHouseholderQr().solve(l_matB0);
        // // Ax + By + Cz + 1 = 0，全部除以法向量的模长，方程依旧成立，而且使得法向量归一化了
        // l_dNegativeOADotNorm = 1 / l_norm.norm();
        // l_norm.normalize();
        l_norm = l_saes.eigenvectors().leftCols<1>().eval();
        l_dNegativeOADotNorm = -l_center * l_norm;

#ifdef MATCHDEBUG
        l_iDebugUse[2]++;
        l_iDebugUse[3]++;
        l_curPoint.intensity = l_iDebugUse[3];
        l_curPoint.normal_x = l_norm(0);
        l_curPoint.normal_y = l_norm(1);
        l_curPoint.normal_z = l_norm(2);
#endif

        // Here n(pa, pb, pc) is unit l_norm of plane
        // 使用平均误差判定平面拟合好坏
        float l_fMeanDiff = 0;
        for (int j = 0; j < l_iPointNum; j++)
        {
            // 点(x0, y0, z0)到平面Ax + By + Cz + D = 0 的距离公式 = fabs(Ax0 + By0 +
            // Cz0 + D) / sqrt(A^2 + B^2 + C^2)
            l_fMeanDiff += fabs(l_norm(0) * l_matA0(j, 0) + l_norm(1) * l_matA0(j, 1)
                                + l_norm(2) * l_matA0(j, 2) + l_dNegativeOADotNorm);
#ifdef MATCHDEBUG
            l_matchPoint.x = c_pTgtSurPc_->points[l_viPntSrhIdx[j]].x;
            l_matchPoint.y = c_pTgtSurPc_->points[l_viPntSrhIdx[j]].y;
            l_matchPoint.z = c_pTgtSurPc_->points[l_viPntSrhIdx[j]].z;
            l_matchPoint.intensity = -2;
            l_matchPtr->points.push_back(l_matchPoint);
#endif
        }
        l_fMeanDiff /= static_cast<float>(l_iPointNum);
        // if (l_fMeanDiff < 0.15)
        if (l_fMeanDiff < c_fPlaneMeanDiff)
            l_bPlaneValid = true;

        l_currPnt << l_pntSrh.x, l_pntSrh.y, l_pntSrh.z;
        l_currPnt -= c_tMovePose_;

#ifdef MATCHDEBUG
        l_iDebugUse[3]++;
        l_curPoint.intensity = l_iDebugUse[3];
        int l_sizeMatchPtr = l_matchPtr->points.size();
        for (int o = l_sizeMatchPtr - l_iPointNum; o < l_sizeMatchPtr; o++)
            l_matchPtr->points[o].intensity = -1;
#endif

        if (l_bPlaneValid)
        {
            l_fDisPnt2Plane = distFromPointToPlane_(l_currPnt, l_norm, l_dNegativeOADotNorm);

            // 距离约束
            if (l_SurfMatchMaxDist > l_fDisPnt2Plane)
            {
                l_currPnt << l_pntO.x, l_pntO.y, l_pntO.z;
                l_currPnt -= c_tMovePose_;
                ceres::CostFunction* l_pCostFunc =
                    LidarPlaneNormFactor::Create(l_currPnt, l_norm, l_dNegativeOADotNorm);
                p_pbm.AddResidualBlock(l_pCostFunc, p_pLossFunc, p_pdParaQ, p_pdParaT);
                c_iSurMatchNum_++;

#ifdef MATCHDEBUG
                l_iDebugUse[3]++;
                l_curPoint.intensity = l_iDebugUse[3];
                for (int o = l_sizeMatchPtr - l_iPointNum; o < l_sizeMatchPtr; o++)
                    l_matchPtr->points[o].intensity = i;
#endif
            }
        }
    }

#ifdef MATCHDEBUG
    l_curPtr->points.push_back(l_curPoint);
    if (l_matchPtr->points.size() != 0)
    {
        printf("save match surf\n");
        writer.writeBinary("./matchSe.pcd", *l_matchPtr);
    }
    writer.writeBinary("./curQuanSe.pcd", *l_curPtr);

    printf("match surf %d - %d [ %d - %d - %d ]\n",
           c_iSrcSurNum_,
           c_iSurMatchNum_,
           l_iDebugUse[0],
           l_iDebugUse[1],
           l_iDebugUse[2]);
#endif
}

template <typename PointSource, typename PointTarget>
bool LaserRegistration<PointSource, PointTarget>::filterZValue(PointSource& p_pSrcPt,
                                                               PointTarget& p_pTgtPt)
{
    // B5 6楼的数据注意高度阈值，目前最好的参数为0.3-0.5
    if (fabs(p_pSrcPt.z - p_pTgtPt.z) < c_fFilterZValue_)
        return true;
    return false;
}

template <typename PointSource, typename PointTarget>
double
LaserRegistration<PointSource, PointTarget>::getMedWallAngle(std::vector<double>& p_vdTgtAngle,
                                                             std::vector<double>& p_vdTgtEdgeAngle)
{
    double l_dMedWallAng;
    sort(p_vdTgtAngle.begin(), p_vdTgtAngle.end());
    // 处理原始角度135的边界问题，<25度的是原始角度>170，考虑排序问题
    if (p_vdTgtEdgeAngle.size() > 0)
    {
        sort(p_vdTgtEdgeAngle.begin(), p_vdTgtEdgeAngle.end());
        p_vdTgtAngle.insert(p_vdTgtAngle.end(), p_vdTgtEdgeAngle.begin(), p_vdTgtEdgeAngle.end());
    }
    l_dMedWallAng = p_vdTgtAngle[int(p_vdTgtAngle.size() / 2)];
    return l_dMedWallAng;
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::edgeAngleHandle(float& p_dNorAngle,
                                                                  float& p_dWallAngle)
{
    if (p_dWallAngle < 10)
        p_dWallAngle += 135;
    else if (p_dWallAngle >= 170)
        p_dWallAngle -= 45;
    else
    {
        p_dNorAngle += 45;
        if (p_dNorAngle >= 360)
            p_dNorAngle -= 360;
    }
}

template <typename PointSource, typename PointTarget>
bool LaserRegistration<PointSource, PointTarget>::angleFormula(float& p_dNorAngle,
                                                               float& p_dWallAngle)
{
    if (p_dNorAngle >= p_dWallAngle && p_dNorAngle < p_dWallAngle + 180)
        return true;
    return false;
}

template <typename PointSource, typename PointTarget>
double LaserRegistration<PointSource, PointTarget>::getWallLine(std::vector<PointTarget>& p_pTgtPt)
{
    float l_fMaxDis = -1;
    int l_vMin_pt = -1, l_vMax_pt = -1;

    // 找到距离最远的两个点
    for (std::size_t i = 0; i < p_pTgtPt.size(); i++)
    {
        for (std::size_t j = 0; j < p_pTgtPt.size(); j++)
        {
            if (j == i)
                continue;
            float x = p_pTgtPt[i].x - p_pTgtPt[j].x;
            float y = p_pTgtPt[i].y - p_pTgtPt[j].y;
            float d = sqrt(pow(x, 2) + pow(y, 2));
            if (d > l_fMaxDis)
            {
                l_fMaxDis = d;
                l_vMax_pt = i;
                l_vMin_pt = j;
            }
        }
    }
    // 求这两个点的连线在坐标系下的角度，代表墙的角度
    return getWallAngle(p_pTgtPt[l_vMax_pt].x - p_pTgtPt[l_vMin_pt].x,
                        p_pTgtPt[l_vMax_pt].y - p_pTgtPt[l_vMin_pt].y);
}

template <typename PointSource, typename PointTarget>
double LaserRegistration<PointSource, PointTarget>::getWallAngle(float x, float y)
{
    double l_dRad = atan2(y, x);            // 求arctan角度
    double l_dAngle = l_dRad * 180 / M_PI;  // 弧度转角度
    // 归一化到[0,180)
    if (l_dAngle < 0)
        l_dAngle += 180;

    l_dAngle += 45;  // 点云整体旋转45度，避免0和180度边界问题
    if (l_dAngle >= 180)
        l_dAngle -= 180;
    return l_dAngle;
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setLabel(int p_iNum, float p_iLabel)
{
    c_pSrcSufPc_->points[p_iNum].v = p_iLabel;
}

template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::setFrameNormal(int p_iNum, PointTarget p_pPnt)
{
    c_pSrcSufPc_->points[p_iNum].h =
        getNormalAngle(c_trans_.x() - p_pPnt.x, c_trans_.y() - p_pPnt.y);
    c_pSrcSufPc_->points[p_iNum].s = 0;
    c_pSrcSufPc_->points[p_iNum].v = 0;
}

template <typename PointSource, typename PointTarget>
double LaserRegistration<PointSource, PointTarget>::getNormalAngle(float x, float y)
{
    double l_dRad = atan2(y, x);            // 求arctan角度
    double l_dAngle = l_dRad * 180 / M_PI;  // 弧度转角度
    // 归一化到[0,360)
    if (l_dAngle < 0)
        l_dAngle += 360;
    return l_dAngle;
}
template <typename PointSource, typename PointTarget>
void LaserRegistration<PointSource, PointTarget>::changePointLabel_(PointSource& p_pPoint,
                                                                    bool p_bSign)
{
    if (((p_pPoint.z - c_trans_.z()) < -c_fGroundHigh_)
        || ((p_pPoint.z - c_trans_.z()) > c_fRoofHigh_))
        p_pPoint.v = 2.0;
    else
    {
        if (p_bSign)
            p_pPoint.v = 0;
        else
            p_pPoint.v = 3.0;
    }
}

template <typename PointSource, typename PointTarget>
bool LaserRegistration<PointSource, PointTarget>::align(Eigen::Quaterniond& p_qCorr,
                                                        Eigen::Vector3d& p_tCorr,
                                                        const int p_iType,
                                                        int p_iScanId,
                                                        bool p_bIgnoreScore)
{
    if (p_iType != Odometry && p_iType != Mapping)
    {
        std::cout << "please input type = 0 or 1.(0：Odometry，1：Mapping)" << std::endl;
        return false;
    }

    // 点云特征匹配时的优化变量
    double l_daParaQ[4] = {c_qEst_.x(), c_qEst_.y(), c_qEst_.z(), c_qEst_.w()};
    double l_daParaT[3] = {c_tEst_.x(), c_tEst_.y(), c_tEst_.z()};

    p_qCorr = Eigen::Quaterniond::Identity();
    p_tCorr = Eigen::Vector3d::Zero();

    // std::cout << "hsq: laserRegistration.hpp align() p_iType(0：Odometry，1：Mapping) = " <<
    // p_iType
    //           << ", c_iSlamModel_ = " << c_iSlamModel_ << ", c_b2dMatch_ = " << c_b2dMatch_
    //           << ", c_iOptimizeModel_ = " << c_iOptimizeModel_ << std::endl;

    for (int i = 0; i < c_iIte_; ++i)
    {
        ceres::LossFunction* l_lossFunPlane = nullptr;
        ceres::LossFunction* l_lossFunLine = nullptr;
        ceres::LocalParameterization* l_qParam = new ceres::EigenQuaternionParameterization();
        ceres::Problem::Options l_pbmOpt;
        ceres::Problem l_pbm(l_pbmOpt);
        l_pbm.AddParameterBlock(l_daParaQ, 4, l_qParam);
        l_pbm.AddParameterBlock(l_daParaT, 3);

        c_iMarkMatchNum_ = 0;
        c_iCorMatchNum_ = 0;
        c_iSurMatchNum_ = 0;
        c_bLooseMatch_ = false;

        if (p_iType == Odometry)
        {
            switch (c_iSlamModel_)
            {
                case 0: {
                    // 纯slam模式
                    if (c_b2dMatch_)
                    {
                        radiusMatchLine2D(l_pbm, l_lossFunLine, l_daParaQ, l_daParaT);
                    }
                    else
                    {
                        nearestKMatchLine(l_pbm, l_lossFunLine, l_daParaQ, l_daParaT);
                    }
                    nearestKMatchPlane(l_pbm, l_lossFunPlane, l_daParaQ, l_daParaT);
                    // 匹配点数过小会出现秩亏的现象，导致矩阵无解
                    c_fMatchPrecent_ = (float)(c_iCorMatchNum_ + c_iSurMatchNum_)
                                       / (float)(c_iSrcCorNum_ + c_iSrcSurNum_);
                    // std::cout << "hsq: laserRegistration.hpp align() Odometry-纯slam模式 c_fMatchPrecent_ = " << c_fMatchPrecent_ 
                    //     << std::endl;
                    break;
                }
                case 1: {
                    // 纯靶标模式
                    radiusMatchMarkLine2D(l_pbm, l_lossFunLine, l_daParaQ, l_daParaT, p_iType);
                    c_fMatchPrecent_ = (float)(c_iMarkMatchNum_) / (float)(c_iSrcMarkNum_);
                    // std::cout << "hsq: laserRegistration.hpp align() Odometry-纯靶标模式 c_fMatchPrecent_ = " << c_fMatchPrecent_ 
                    //     << std::endl;
                    break;
                }
                case 2: {
                    // 混合模式
                    if (c_b2dMatch_)
                    {
                        radiusMatchLine2D(l_pbm, l_lossFunLine, l_daParaQ, l_daParaT);
                    }
                    else
                    {
                        nearestKMatchLine(l_pbm, l_lossFunLine, l_daParaQ, l_daParaT);
                    }
                    nearestKMatchPlane(l_pbm, l_lossFunPlane, l_daParaQ, l_daParaT);
                    radiusMatchMarkLine2D(l_pbm, l_lossFunLine, l_daParaQ, l_daParaT, p_iType);
                    // 匹配点数过小会出现秩亏的现象，导致矩阵无解
                    c_fMatchPrecent_ = (float)(c_iCorMatchNum_ + c_iSurMatchNum_
                                               + c_iMarkWeightMax_ * c_iMarkMatchNum_)
                                       / (float)(c_iSrcCorNum_ + c_iSrcSurNum_
                                                 + c_iMarkWeightMax_ * c_iSrcMarkNum_);
                    // std::cout << "hsq: laserRegistration.hpp align() Odometry-混合模式 c_fMatchPrecent_ = " << c_fMatchPrecent_ 
                    //     << std::endl;
                    break;
                }
                case 3: {
                    // 混合模式 无角点
                    nearestKMatchPlane(l_pbm, l_lossFunPlane, l_daParaQ, l_daParaT);
                    radiusMatchMarkLine2D(l_pbm, l_lossFunLine, l_daParaQ, l_daParaT, p_iType);
                    // 匹配点数过小会出现秩亏的现象，导致矩阵无解
                    c_fMatchPrecent_ =
                        (float)(c_iSurMatchNum_ + c_iMarkWeightMax_ * c_iMarkMatchNum_)
                        / (float)(c_iSrcSurNum_ + c_iMarkWeightMax_ * c_iSrcMarkNum_);
                    // std::cout << "hsq: laserRegistration.hpp align() Odometry-混合模式 无角点 c_fMatchPrecent_ = " << c_fMatchPrecent_ 
                    //     << std::endl;
                }
                default: break;
            }

            // 匹配点数过小会出现秩亏的现象，导致矩阵无解
            if (c_iSrcCorNum_ + c_iSrcSurNum_ == 0)
            {
                // std::cout << "hsq: laserRegistration.hpp align() c_iSrcCorNum_ + c_iSrcSurNum_ ==
                // 0"
                //          << std::endl;
                std::cout << "hsq:  定位异常 | 里程计置信度过低, 请检查.laserRegistration.hpp align()c_iSrcCorNum_ + c_iSrcSurNum_ == 0"
                              << ", c_iSrcCorNum_ = " << c_iSrcCorNum_
                              << ", c_iSrcSurNum_ = " << c_iSrcSurNum_
                              << ", c_fMatchPrecent_ = " << c_fMatchPrecent_ 
                              << ", c_b2dMatch_ = " << c_b2dMatch_<< std::endl;
                LOGO(WWARN,
                     "{} 帧[{}] 定位异常 | 里程计置信度过低, 请检查!",
                     WJLog::getWholeSysTime(),
                     p_iScanId);
                LOGO(WWARN,
                     "{} 帧[{}] S2S-{:.3}: [{}<--{}]:{}, [{}<--{}]:{} | {}",
                     WJLog::getWholeSysTime(),
                     p_iScanId,
                     c_fMatchPrecent_,
                     c_iSrcCorNum_,
                     c_iTagCorNum_,
                     c_iCorMatchNum_,
                     c_iSrcSurNum_,
                     c_iTagSurNum_,
                     c_iSurMatchNum_,
                     p_bIgnoreScore);
                return false;
            }
            // std::cout << "hsq: laserRegistration.hpp align() c_iSrcCorNum_ = " << c_iSrcCorNum_ 
            //                 <<", c_iSampleNum_ = " << c_iSampleNum_<< std::endl;
            if (c_fMatchPrecent_ < c_fMatchPrecentThr_)
            {
                if (!p_bIgnoreScore || (p_bIgnoreScore && c_fMatchPrecent_ < 0.1))
                {
                    std::cout << "hsq:  定位异常 | 里程计置信度过低, 请检查.laserRegistration.hpp align() c_fMatchPrecent_ < "
                                 "c_fMatchPrecentThr"
                              << ", c_fMatchPrecentThr_ = " << c_fMatchPrecentThr_
                              << ", p_bIgnoreScore = " << p_bIgnoreScore
                              << ", c_fMatchPrecent_ = " << c_fMatchPrecent_ << std::endl;
                    LOGO(WWARN,
                         "{} 帧[{}] 定位异常 | 里程计置信度过低, 请检查!",
                         WJLog::getWholeSysTime(),
                         p_iScanId);
                    LOGO(WWARN,
                         "{} 帧[{}] S2S-{:.3}: [{}<--{}]:{}, [{}<--{}]:{} | {}",
                         WJLog::getWholeSysTime(),
                         p_iScanId,
                         c_fMatchPrecent_,
                         c_iSrcCorNum_,
                         c_iTagCorNum_,
                         c_iCorMatchNum_,
                         c_iSrcSurNum_,
                         c_iTagSurNum_,
                         c_iSurMatchNum_,
                         p_bIgnoreScore);
                    return false;
                }
            }
        }
        if (p_iType == Mapping)
        {
            switch (c_iSlamModel_)
            {
                case 0: {
                    // 纯slam模式
                    if (c_b2dMatch_)
                    {
                        radiusMatchLine2D(l_pbm, l_lossFunLine, l_daParaQ, l_daParaT);
                    }
                    else
                    {
                        nearestKMatchLine(l_pbm, l_lossFunLine, l_daParaQ, l_daParaT);
                    }
                    switch (c_iOptimizeModel_)
                    {
                        case OptimizeMapType::KDTREE_TYPE:
                            nearestKMatchPlane(l_pbm, l_lossFunPlane, l_daParaQ, l_daParaT);
                            break;
                        case OptimizeMapType::IVOX_TYPE:
                            choseIVoxMapchParam_(c_iWorkModel_);
                            nearestKMatchPlaneiVox(l_pbm, l_lossFunPlane, l_daParaQ, l_daParaT);
                            break;
                        default: break;
                    }

                    c_fMatchPrecent_ = (float)(c_iCorMatchNum_ + c_iSurMatchNum_)
                                       / (float)(c_iSrcCorNum_ + c_iSampleNum_);
                    // std::cout << "hsq: laserRegistration.hpp align() Mapping-纯slam模式 c_fMatchPrecent_ = " << c_fMatchPrecent_ 
                    //     << std::endl;
                    // std::cout << "hsq: c_fMatchPrecent_ = " << c_fMatchPrecent_
                    //           << ", c_iCorMatchNum_ = " << c_iCorMatchNum_
                    //           << ", c_iSurMatchNum_ = " << c_iSurMatchNum_
                    //           << ", c_iSrcCorNum_ = " << c_iSrcCorNum_
                    //           << ", c_iSampleNum_ = " << c_iSampleNum_ << std::endl;
                    break;
                }
                case 1: {
                    // 纯靶标模式
                    radiusMatchMarkLine2D(l_pbm, l_lossFunLine, l_daParaQ, l_daParaT, Mapping);
                    c_fMatchPrecent_ = (float)(c_iMarkMatchNum_) / (float)(c_iSrcMarkNum_);
                    // std::cout << "hsq: laserRegistration.hpp align() Mapping-纯靶标模式 c_fMatchPrecent_ = " << c_fMatchPrecent_ 
                    //     << std::endl;
                    break;
                }
                case 2: {
                    // 混合模式
                    if (c_b2dMatch_)
                    {
                        radiusMatchLine2D(l_pbm, l_lossFunLine, l_daParaQ, l_daParaT);
                    }
                    else
                    {
                        nearestKMatchLine(l_pbm, l_lossFunLine, l_daParaQ, l_daParaT);
                    }
                    switch (c_iOptimizeModel_)
                    {
                        case OptimizeMapType::KDTREE_TYPE:
                            nearestKMatchPlane(l_pbm, l_lossFunPlane, l_daParaQ, l_daParaT);
                            break;
                        case OptimizeMapType::IVOX_TYPE:
                            choseIVoxMapchParam_(c_iWorkModel_);
                            nearestKMatchPlaneiVox(l_pbm, l_lossFunPlane, l_daParaQ, l_daParaT);
                            break;
                        default: break;
                    }

                    radiusMatchMarkLine2D(l_pbm, l_lossFunLine, l_daParaQ, l_daParaT, Mapping);

                    c_fMatchPrecent_ = (float)(c_iCorMatchNum_ + c_iSurMatchNum_
                                               + c_iMarkWeightMax_ * c_iMarkMatchNum_)
                                       / (float)(c_iSrcCorNum_ + c_iSampleNum_
                                                 + c_iMarkWeightMax_ * c_iSrcMarkNum_);
                    // std::cout << "hsq: laserRegistration.hpp align() Mapping-混合模式 c_fMatchPrecent_ = " << c_fMatchPrecent_ 
                    //     << std::endl;
                    break;
                }
                case 3: {
                    // 混合模式 无角点
                    switch (c_iOptimizeModel_)
                    {
                        case OptimizeMapType::KDTREE_TYPE:
                            nearestKMatchPlane(l_pbm, l_lossFunPlane, l_daParaQ, l_daParaT);
                            break;
                        case OptimizeMapType::IVOX_TYPE:
                            choseIVoxMapchParam_(c_iWorkModel_);
                            nearestKMatchPlaneiVox(l_pbm, l_lossFunPlane, l_daParaQ, l_daParaT);
                            break;
                        default: break;
                    }

                    radiusMatchMarkLine2D(l_pbm, l_lossFunLine, l_daParaQ, l_daParaT, p_iType);
                    // 匹配点数过小会出现秩亏的现象，导致矩阵无解
                    c_fMatchPrecent_ =
                        (float)(c_iSurMatchNum_ + c_iMarkWeightMax_ * c_iMarkMatchNum_)
                        / (float)(c_iSampleNum_ + c_iMarkWeightMax_ * c_iSrcMarkNum_);
                    // std::cout << "hsq: laserRegistration.hpp align() Mapping-混合模式 无角点 c_fMatchPrecent_ = " << c_fMatchPrecent_ 
                    //     << std::endl;
                }
                default: break;
            }

            if (c_iSrcCorNum_ + c_iSampleNum_ == 0)
            {
                std::cout << "hsq: 定位异常 | 里程计置信度过低, 请检查 laserRegistration.hpp align() c_iSrcCorNum_ = " << c_iSrcCorNum_ 
                            <<", c_iSampleNum_ = " << c_iSampleNum_<< std::endl;
                LOGM(WWARN, "{} 定位异常 | 里程计置信度过低, 请检查!", WJLog::getWholeSysTime());
                LOGM(WWARN,
                     "{} 帧[{}] S2M-{:.3}: [{}<--{}]:{}, [{}<--{}]:{}, [{}<--{}]:{} | {}",
                     WJLog::getWholeSysTime(),
                     p_iScanId,
                     c_fMatchPrecent_,
                     c_iSrcCorNum_,
                     c_iTagCorNum_,
                     c_iCorMatchNum_,
                     c_iSampleNum_,
                     c_iTagSurNum_,
                     c_iSurMatchNum_,
                     c_iSrcMarkNum_,
                     c_iTagMarkNum_,
                     c_iMarkMatchNum_,
                     p_bIgnoreScore);
                return false;
            }
            if (c_fMatchPrecent_ < c_fMatchPrecentThr_)
            {
                if (!p_bIgnoreScore || (p_bIgnoreScore && c_fMatchPrecent_ < 0.1))
                {
                    std::cout << "hsq: 定位异常 | 里程计置信度过低, 请检查 laserRegistration.hpp align() c_fMatchPrecent_ < "
                                 "c_fMatchPrecentThr: "
                              << ", c_fMatchPrecent_ = " << c_fMatchPrecent_ 
                              << ", c_fMatchPrecentThr_ = " << c_fMatchPrecentThr_
                              << ", p_bIgnoreScore = " << p_bIgnoreScore
                              << std::endl;

                    LOGM(
                        WINFO, "{} 定位异常 | 里程计置信度过低, 请检查!", WJLog::getWholeSysTime());
                    LOGM(WINFO,
                         "{} 帧[{}] S2M-{:.3}:{} [{}<--{}]:{}, [{}<--{}]:{}, [{}<--{}]:{} | {}",
                         WJLog::getWholeSysTime(),
                         p_iScanId,
                         c_fMatchPrecent_,
                         c_fMatchPrecentThr_,
                         c_iSrcCorNum_,
                         c_iTagCorNum_,
                         c_iCorMatchNum_,
                         c_iSampleNum_,
                         c_iTagSurNum_,
                         c_iSurMatchNum_,
                         c_iSrcMarkNum_,
                         c_iTagMarkNum_,
                         c_iMarkMatchNum_,
                         p_bIgnoreScore);
                    return false;
                }
            }
            
        }

        ceres::Solver::Options l_opt;
        l_opt.linear_solver_type = ceres::DENSE_NORMAL_CHOLESKY;
        l_opt.num_threads = MAX_NUM_THREAD;
        l_opt.max_num_iterations = 100;
        if (p_iType == Mapping)
        {
            l_opt.max_num_iterations = 100;
        }

        // Δcost/meanDev <= function_tolerance时停止求解
        l_opt.function_tolerance = 1e-8;  // 1e-6
        // 信任域步长(trust region step)相对减少的最小值。
        l_opt.min_relative_decrease = 1e-6;  // 1e-3

        l_opt.minimizer_progress_to_stdout = false;
        l_opt.logging_type = ceres::SILENT;

        // TicToc t_sol;
        ceres::Solver::Summary l_summary;
        // 基于构建的所有残差项，求解最优的当前帧位姿与上一帧位姿的位姿增量：para_q和l_daParaT
        ceres::Solve(l_opt, &l_pbm, &l_summary);

        // std::cout << l_summary.FullReport() << std::endl;
        // 输出优化的简要信息

        c_qEst_.x() = l_daParaQ[0];
        c_qEst_.y() = l_daParaQ[1];
        c_qEst_.z() = l_daParaQ[2];
        c_qEst_.w() = l_daParaQ[3];
        c_tEst_.x() = l_daParaT[0];
        c_tEst_.y() = l_daParaT[1];
        c_tEst_.z() = l_daParaT[2];
        c_qEst_.normalize();

        p_tCorr = c_tEst_ + c_tMovePose_ - c_qEst_ * c_tMovePose_;
        p_qCorr = c_qEst_;
    }
    // std::cout << "hsq: laserRegistration.hpp ceres optimise finished, c_fMatchPrecent_ = " << c_fMatchPrecent_ << std::endl;
    return true;
}
