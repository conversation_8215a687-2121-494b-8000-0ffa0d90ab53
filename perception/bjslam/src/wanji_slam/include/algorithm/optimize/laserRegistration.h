/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-12 16:56:06
 * @LastEditTime: 2023-06-29 10:03:50
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /catkin_ws/src/wanji_slam/wslam/src/LaserRegistration.h
 */

#ifndef LASER_REGISTRATION
#define LASER_REGISTRATION

#include "../../tic_toc.h"
#include "../include/common/common_ex.h"
#include "../map/iVox_map/impl/iVox_map.hpp"
#include "algorithm/optimize/impl/lidarFactor.hpp"
#include "algorithm/optimize/kdtree_flann2d.h"
#include "algorithm/optimize/laserTransform.h"
#include "omp.h"
#include "wj_log.h"
#include <map>
#include <pcl/kdtree/impl/kdtree_flann.hpp>
#include <pcl/search/impl/kdtree.hpp>

#define MAX_NUM_THREAD 4
#define MID_NUM_THREAD 3
#define MIN_NUM_THREAD 2
using namespace wj_slam;
//最远匹配线束差
#define BUFF_SCAN 2.5
// 匹配输出debug信息
// #define MATCHDEBUG
template <typename PointSource, typename PointTarget>
class LaserRegistration : public LaserTransform<PointSource> {
  private:
    int RING_FIELDS_OFFSET = 0;
    // int TIME_FIELDS_OFFSET = 0;

  public:
    enum { Odometry, Mapping };
    struct s_SmoothZDiff
    {
        float m_ZDiff;
        int m_idx;
    };

    //点云高差排序核
    struct s_SortValue
    {
        bool operator()(s_SmoothZDiff const& p_sLeft, s_SmoothZDiff const& p_sRight)
        {
            return p_sLeft.m_ZDiff < p_sRight.m_ZDiff;
        }
    };

  public:
    typedef boost::shared_ptr<LaserRegistration<PointSource, PointTarget>> Ptr;

    typedef pcl::PointCloud<PointSource> PointCloudSource;
    typedef typename PointCloudSource::Ptr PointCloudSourcePtr;

    typedef pcl::PointCloud<PointTarget> PointCloudTarget;
    typedef typename PointCloudTarget::Ptr PointCloudTargetPtr;

    typedef pcl::search::KdTree<PointTarget> KdTree;
    typedef typename pcl::search::KdTree<PointTarget>::Ptr KdTreePtr;

    typedef pcl::KdTreeFLANN2D<PointTarget> KdTreeFlann2D;
    typedef typename KdTreeFlann2D::Ptr KdTreeFlann2DPtr;

    // typedef pcl::KdTreeFLANN<PointTarget> KdTreeFlann;
    // typedef typename KdTreeFlann::Ptr KdTreeFlannPtr;

  public:
    // test
    float getAveDistance()
    {
        return c_fDistance_;
    }
    void setRingOffset(int p_iRingOffset)
    {
        RING_FIELDS_OFFSET = p_iRingOffset;
    }
    LaserRegistration()
        : c_pKdtCor_(new KdTree), c_pKdtSur_(new KdTree), c_pKdtFln2dCor_(new KdTreeFlann2D),
          c_pKdtMarkCor_(new KdTree), c_pKdtFln2dMarkCor_(new KdTreeFlann2D), c_fDistance_(10)
    {
        c_qEst_ = Eigen::Quaterniond::Identity();
        c_tEst_ = Eigen::Vector3d::Zero();
        c_tMovePose_ = Eigen::Vector3d::Zero();
        c_qMovePose_ = Eigen::Quaterniond::Identity();

        c_pTgtCorPc2D_.reset(new PointCloudTarget());

        c_iIte_ = 0;
        c_fDisThd_ = 0;
        c_fZThd_ = 0;
        c_fRThd_ = 0;
        c_iKdNum_ = 10;
        c_dMeanDev_ = 1.0;

        c_iCorMatchNum_ = 0;
        c_iSurMatchNum_ = 0;
        c_fMatchPrecentThr_ = 0.1;
        c_fMatchPrecent_ = 0.0;
        c_fCorSrhR_ = 0;
        c_fSurSrhR_ = 0;

        c_bLooseMatch_ = false;
        c_b2dMatch_ = false;

        c_iSrhNum_ = 0;
        c_bAngleSrh_ = false;
        c_fAngle_ = 0.0;

        c_fPlaneMeanDiff = 0.05;
        c_iPlanePcaDirection = 0;
        c_fPlanePcaValue = 0.01;

        c_iOptimizeModel_ = -1;
        c_fFilterZValue_ = 0.5;

        c_iSlamModel_ = 0;
        c_iMarkWeightMax_ = 20;
        c_iMarkWeightMin_ = 10;
        c_iWorkModel_ = 0;
    }
    ~LaserRegistration() {}

    bool tooFewMatchMapCor()
    {
        return c_pTgtCorPc_->points.size() < 100 ? true : false;
    }

    bool tooFewMatchMapSurf()
    {
        return c_pTgtSurPc_->points.size() < 100 ? true : false;
    }

    /**
     * @description: 输入当前帧点云
     * @param {PointCloudSourcePtr} p_pPc
     * @return {*}
     */
    void setInputFrameCloud(PointCloudSourcePtr p_pPc);
    /**
     * @description: 输入当前帧角点
     * @param {PointCloudSourcePtr} p_pPc
     * @return {*}
     */
    void setInputSourceCorner(PointCloudSourcePtr p_pPc);
    /**
     * @description: 输入当前帧靶标角点
     * @param {PointCloudSourcePtr} p_pPc
     * @return {*}
     */
    void setInputSourceMark(PointCloudSourcePtr p_pPc);
    /**
     * @description: 输入当前帧面点
     * @param {PointCloudSourcePtr} p_pPc
     * @return {*}
     */
    void setInputSourceSurface(PointCloudSourcePtr p_pPc);
    /**
     * @description: 输入当前帧面点
     * @param {PointCloudSourcePtr} p_pPc
     * @return {*}
     */
    void setInputSourceSurfaceSample(PointCloudSourcePtr p_pPc, int p_iSampleSize);
    /**
     * @description: 输入匹配目标角点
     * @param {PointCloudTargetPtr} p_pPc
     * @return {*}
     */
    void setInputTargetCorner(PointCloudTargetPtr p_pPc);
    // void setInputTargetCorner(PointCloudTargetPtr p_pPc, PointCloudTargetPtr p_pPc2D = NULL);

    /**
     * @description: 输入匹配目标靶标角点
     * @param {PointCloudTargetPtr} p_pPc
     * @return {*}
     */
    void setInputTargetMark(PointCloudTargetPtr p_pPc);
    /**
     * @description: 输入匹配目标面点
     * @param {PointCloudTargetPtr} p_pPc
     * @return {*}
     */
    void setInputTargetSurface(PointCloudTargetPtr p_pPc);
    /**
     * @description: 设置匹配对象的姿态估值
     * @param {const} Eigen     预估的旋转四元数
     * @param {const} Eigen     预估的平移量
     * @return {*}
     */
    void setTransform(const Eigen::Quaterniond p_qEst, const Eigen::Vector3d p_tEst);
    /**
     * @description: 设置最邻近匹配点筛选的距离阈值（雷达里程计参数）
     * @param {const float} p_fDis
     * @return {*}
     */
    void setDistThreshold(const float p_fDis);
    /**
     * @description: 设置高程z值，判断是否为横线（建图参数）
     * @param {const float} p_fZThd
     * @return {*}
     */
    void setZAxisThreshold(const float p_fZThd);

    /**
     * @description: 设置匹配点半径筛选阈值（雷达里程计参数）
     * @param {const float} p_fRThd
     * @return {*}
     */
    void setRadiusThreshold(const float p_fRThd);
    /**
     * @description: 设置角点搜素半径
     * @param {const float} p_fRThd
     * @return {*}
     */
    void setCornerSearchR(const float p_fCorSrhR);
    /**
     * @description: 设置靶标搜素半径
     * @param {const float} p_fRThd
     * @return {*}
     */
    void setMarkSearchR(const float p_fCorSrhR);
    /**
     * @description: 设置面点搜索半径
     * @param {const float} p_fRThd
     * @return {*}
     */
    void setSurfaceSearchR(const float p_fSurSrhR);
    /**
     * @description: 设置kdtree临近搜索的点个数
     * @param {const int} p_iKdNum
     * @return {*}
     */
    void setSearchK(const int p_iKdNum);

    /**
     * @brief 设置双层墙阈值高度
     *
     * @param p_fgdHigh
     * @param p_frfHigh
     *
     */
    void setwallhigh(const float p_fgdHigh, const float p_frfHigh);

    /**
     * @description: 设置半径搜索的点个数阈值
     * @param {const int} p_iSrhNum
     * @return {*}
     */

    void setSearchNumThreshold(const int p_iSrhNum);

    /**
     * @description: 设置最大迭代次数
     * @param {const int} p_iIter
     * @return {*}
     */
    void setMaxIterations(const int p_iIter);
    /**
     * @description: 设置是否进行2D方式匹配
     * @param {bool} p_b2D
     * @return {*}false：不使用     true：使用
     */
    void setMatch2D(bool p_b2D);

    /**
     * @description: 设置是否进行角度计算半径筛选阈值
     * @param {bool} p_bAngleSrh
     * @return {*}false：不使用     true：使用
     */
    void setAngleSearch(bool p_bAngleSrh);
    /**
     * @description: 设置角度值
     * @param {const int} p_iAngle
     * @return {*}
     */
    void setAngle(const float p_fAngle);
    /**
     * @description: 设置最低优化得分
     * @param {PointCloudSourcePtr} p_pPc
     * @return {*}
     */
    void setMatchScoreThr(const float p_fMatchThr);

    /**
     * @description: 设置PCA方法 校验平面的特征向量方向及阈值
     * @param {const int} p_iDirection 特征向量方向
     * @param {const float} p_fThr 对应方向 特征值阈值
     * @return {*}
     */
    void setPlanePcaThr(const int p_iDirection, const float p_fThr);

    /**
     * @description: 设置平面拟合误差阈值
     * @param {const float} p_fThr 平面拟合误差阈值
     * @return {*}
     */
    void setPlaneMeanDiffThr(const float p_fThr);

    /**
     * @description: 匹配优化函数
     * @param {Quaterniond} &p_qCorr        输出优化增量四元数
     * @param {Vector3d} &p_tCorr       输出优化增量平移量
     * @param {const int} p_iType       输入（0：雷达里程计，1：建图）
     * @return {*}
     */
    bool align(Eigen::Quaterniond& p_qCorr,
               Eigen::Vector3d& p_tCorr,
               const int p_iType,
               int p_iScanId = 0,
               bool p_bIgnoreScore = false);
    /**
     * @description: 获取优化得分
     * @param {PointCloudSourcePtr} p_pPc
     * @return {*}
     */
    float getMatchScore();
    /**
     * @description: 获取得分
     * @param {PointCloudSourcePtr} p_pPc
     * @return {*}
     */
    float getResultScore();
    /**
     * @description: 获取优化后的当前帧的角点
     * @param {PointCloudSourcePtr} p_pPc
     * @return {*}
     */
    void getResultCornerPoints(PointCloudSourcePtr p_pPc);
    /**
     * @description: 获取优化后的当前帧的面点
     * @param {PointCloudSourcePtr} p_pPc
     * @return {*}
     */
    void getResultSurfacePoints(PointCloudSourcePtr p_pPc);
    /**
     * @description: 获取优化后的当前帧点云
     * @param {PointCloudSourcePtr} p_pPc
     * @return {*}
     */
    void getResultFramePoints(PointCloudSourcePtr p_pPc);

    /**
     * @description: 2D角点匹配
     * @param {Problem} &p_pbm
     * @param {LossFunction} *p_pLossFunc
     * @param {double} *p_pdParaQ
     * @param {double} *p_pdParaT
     * @return {*}
     */
    void radiusMatchLine2D(ceres::Problem& p_pbm,
                           ceres::LossFunction* p_pLossFunc,
                           double* p_pdParaQ,
                           double* p_pdParaT);

    /**
     * @description: 2D靶标角点匹配
     * @param {Problem} &p_pbm
     * @param {LossFunction} *p_pLossFunc
     * @param {double} *p_pdParaQ
     * @param {double} *p_pdParaT
     * @return {*}
     */
    void radiusMatchMarkLine2D(ceres::Problem& p_pbm,
                               ceres::LossFunction* p_pLossFunc,
                               double* p_pdParaQ,
                               double* p_pdParaT,
                               const int p_iType);

    /**
     * @description:3D角点匹配
     * @param {Problem} &p_pbm
     * @param {LossFunction} *p_pLossFunc
     * @param {double} *p_pdParaQ
     * @param {double} *p_pdParaT
     * @return {*}
     */
    void radiusMatchLine(ceres::Problem& p_pbm,
                         ceres::LossFunction* p_pLossFunc,
                         double* p_pdParaQ,
                         double* p_pdParaT);

    /**
     * @description: 3D面点匹配
     * @param {Problem} &p_pbm
     * @param {LossFunction} *p_pLossFunc
     * @param {double} *p_pdParaQ
     * @param {double} *p_pdParaT
     * @return {*}
     */
    void radiusMatchPlane(ceres::Problem& p_pbm,
                          ceres::LossFunction* p_pLossFunc,
                          double* p_pdParaQ,
                          double* p_pdParaT);

    /**
     * @description: 建图角点匹配
     * @param {Problem} &p_pbm
     * @param {LossFunction} *p_pLossFunc
     * @param {double} *p_pdParaQ
     * @param {double} *p_pdParaT
     * @return {*}
     */
    void nearestKMatchLine(ceres::Problem& p_pbm,
                           ceres::LossFunction* p_pLossFunc,
                           double* p_pdParaQ,
                           double* p_pdParaT);

    /**
     * @description: 建图面点匹配
     * @param {Problem} &p_pbm
     * @param {LossFunction} *p_pLossFunc
     * @param {double} *p_pdParaQ
     * @param {double} *p_pdParaT
     * @return {*}
     */
    void nearestKMatchPlaneiVox(ceres::Problem& p_pbm,
                                ceres::LossFunction* p_pLossFunc,
                                double* p_pdParaQ,
                                double* p_pdParaT);
    void nearestKMatchPlaneDoubleWall(ceres::Problem& p_pbm,
                                      ceres::LossFunction* p_pLossFunc,
                                      double* p_pdParaQ,
                                      double* p_pdParaT);
    void nearestKMatchPlane(ceres::Problem& p_pbm,
                            ceres::LossFunction* p_pLossFunc,
                            double* p_pdParaQ,
                            double* p_pdParaT);

    void setMovePose(Eigen::Vector3d pose_t = Eigen::Vector3d::Zero(),
                     Eigen::Quaterniond pose_q = Eigen::Quaterniond::Identity())
    {
        c_tMovePose_ = pose_t;
        c_qMovePose_ = pose_q;
    }
    /**
     * @brief 高度Z值过滤
     *
     * @param p_iSrcSufPcNum
     * @param p_iTgtSurPcNum
     * @code
     *
     * @endcode
     * @return [true] \n
     * [details wirte here]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [details wirte here]
     *
     */
    bool filterZValue(PointSource& p_pSrcPt, PointTarget& p_pTgtPt);

    /**
     * @brief 返回墙的角度的中位数
     *
     * @param p_vdTgtAngle
     * @param p_vdTgtEdgeAngle
     * @code
     *
     * @endcode
     * @return [double] \n
     * [details wirte here]
     *
     */
    double getMedWallAngle(std::vector<double>& p_vdTgtAngle,
                           std::vector<double>& p_vdTgtEdgeAngle);

    /**
     * @brief 对边界角度135的特殊处理
     *
     * @param p_dNorAngle
     * @param p_dWallAngle
     *
     */
    void edgeAngleHandle(float& p_dNorAngle, float& p_dWallAngle);
    /**
     * @brief 根据法线角度、墙的角度判断点的标签
     *
     * @param p_dNorAngle
     * @param p_dWallAngle
     * @code
     *
     * @endcode
     * @return [true] \n
     * [details wirte here]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [details wirte here]
     *
     */
    bool angleFormula(float& p_dNorAngle, float& p_dWallAngle);

    /**
     * @brief 找最长直线表示墙的方向
     *
     * @param p_pTgtPt
     * @code
     *
     * @endcode
     * @return [double] \n
     * [details wirte here]
     *
     */
    double getWallLine(std::vector<PointTarget>& p_pTgtPt);

    /**
     * @brief 计算当前点所在墙在全局坐标系下的角度
     *
     * @param x
     * @param y
     * @code
     *
     * @endcode
     * @return [double] \n
     * [details wirte here]
     *
     */
    double getWallAngle(float x, float y);

    /**
     * @brief 设置当前点的标签
     *
     * @param p_iNum
     * @param p_iLabel
     *
     */
    void setLabel(int p_iNum, float p_iLabel);

    /**
     * @brief 计算当前帧的hsv
     *
     * @param p_iNum
     * @param p_pPnt
     *
     */
    void setFrameNormal(int p_iNum, PointTarget p_pPnt);

    /**
     * @brief 计算当帧面点中各点法线在全局坐标系下的角度
     *
     * @param x
     * @param y
     * @code
     *
     * @endcode
     * @return [double] \n
     * [details wirte here]
     *
     */
    double getNormalAngle(float x, float y);

    /**
     * @brief 设定位姿用于计算房顶与地面
     *
     * @param p_tEst
     *
     */
    void setCurrentPose(const Eigen::Vector3d p_tEst);

    /**
     * @brief 获取iVox栅格地图
     *
     * @param p_pIvox
     * @param p_pPc
     *
     */
    void setIVoxGrid(boost::shared_ptr<IVox<3, PointTarget>> p_pIvox);

    /**
     * @brief 设置匹配模块是kdtree还是iVox
     *
     * @param p_iModel
     *
     */
    void setOptimizeModel(int p_iModel);
    /**
     * @brief 设置过滤Z值的阈值
     *
     * @param p_fZValue
     *
     */
    void setFilterZValue(float p_fZValue);
    /**
     * @brief 设置工作模式，用于iVox选择匹配的点数
     *
     * @param p_iModel
     *
     */
    void setWorkModel(int p_iModel);
    /**
     * @brief 设置导航模式
     *
     * @param p_iSlamModel
     *
     */
    void setSlamModel(int p_iSlamModel);
    /**
     * @brief 设置导靶标权重
     *
     * @param p_iMin
     * @param p_iMax
     */
    void setMarkWight(int p_iMin, int p_iMax);
    /**
     * @brief 根据模式选择参与计算的点数是全部点云还是下采样点云
     *
     * @param p_iModel
     *
     */
    void choseIVoxMapchParam_(int p_iModel);
    /**
     * @brief 用于初始化每个当前帧的V值，回退当前帧的V值
     *
     * @param p_pPoint
     * @param p_bSign
     *
     */
    void changePointLabel_(PointSource& p_pPoint, bool p_bSign);

  private:
    /**
     * @description: 通过比较角度计算半径和设置半径，选取合适的半径
     * @param {const} PointSource     种子点
     * @param {float} p_fSrhR     设置的半径
     * @return {*}    有效的半径
     */
    float getValidSearchRadius_(const PointSource* p_pntSrh, float p_fSrhR);

    /**
     * @description: 计算点到直线的距离
     * @param {Vector3d} p_pntCur      目标点点
     * @param {Vector3d} &p_LineA        直线上点a
     * @param {Vector3d} &p_LineB        直线上点b
     * @return {*}
     */
    static float distFromPointToLine_(Eigen::Vector3d p_pntCur,
                                      Eigen::Vector3d& p_LineA,
                                      Eigen::Vector3d& p_LineB)
    {
        return fabs(((p_pntCur - p_LineA).cross(p_pntCur - p_LineB)).norm()
                    / (p_LineA - p_LineB).norm());
    }
    /**
     * @description: 计算点到面的距离
     * @param {Vector3d} p_pntCur       目标点
     * @param {Vector3d} &p_planeNorm         平面法向量
     * @param {double} p_dNegativeOADotNorm         法向量模长的倒数
     * @return {*}
     */
    static float distFromPointToPlane_(Eigen::Vector3d p_pntCur,
                                       Eigen::Vector3d& p_planeNorm,
                                       double p_dNegativeOADotNorm)
    {
        return fabs(p_planeNorm.dot(p_pntCur) + p_dNegativeOADotNorm);
    }

  private:
    PointCloudSourcePtr c_pSrcFrmPc_;
    PointCloudSourcePtr c_pSrcCorPc_;
    PointCloudSourcePtr c_pSrcMarkCorPc_;
    PointCloudSourcePtr c_pSrcSufPc_;

    PointCloudTargetPtr c_pTgtFrmPc_;
    PointCloudTargetPtr c_pTgtCorPc_;
    PointCloudTargetPtr c_pTgtMarkCorPc_;
    PointCloudTargetPtr c_pTgtSurPc_;

    PointCloudTargetPtr c_pTgtCorPc2D_;
    //输入的估计(estimate)值，q，t，
    Eigen::Quaterniond c_qEst_;
    Eigen::Vector3d c_tEst_;

    Eigen::Quaterniond c_quat_;
    Eigen::Vector3d c_trans_;
    // 转移到零点
    Eigen::Vector3d c_tMovePose_;
    Eigen::Quaterniond c_qMovePose_;

    //雷达里程计有效点筛选阈值
    float c_fDisThd_;
    //建图z轴阈值
    float c_fZThd_;
    //建图半径阈值
    float c_fRThd_;
    //角点搜索半径
    float c_fCorSrhR_;
    //角点搜索半径
    float c_fMarkSrhR_;
    //面点搜索半径
    float c_fSurSrhR_;

    //里程计z值约束
    float c_afMinMaxZ_[2];
    // 2d匹配的开关
    bool c_b2dMatch_;
    // KdTreePtr m_tree;
    KdTreePtr c_pKdtCor_;
    KdTreePtr c_pKdtMarkCor_;
    KdTreePtr c_pKdtSur_;

    KdTreeFlann2DPtr c_pKdtFln2dCor_;
    KdTreeFlann2DPtr c_pKdtFln2dMarkCor_;

    // KdTreeFlannPtr c_pKdtFlnSur_;
    // KdTreeFlannPtr c_pKdtFlnCor_;

    //匹配数量
    int c_iCorMatchNum_;
    int c_iMarkMatchNum_;
    int c_iSurMatchNum_;
    float c_fMatchPrecentThr_; /**< 最小匹配率 */
    float c_fMatchPrecent_;    /**< 匹配率 */
    // kdtree临近搜索的点数
    int c_iKdNum_;
    //最大迭代次数
    int c_iIte_;

    //残差
    double c_dMeanDev_;
    double c_daVarXyz_[9];
    double c_daVarRpy_[9];

    //点数
    int c_iSrcCorNum_;
    int c_iSrcMarkNum_;
    int c_iSrcSurNum_;
    int c_iTagCorNum_;
    int c_iTagMarkNum_;
    int c_iTagSurNum_;

    bool c_bLooseMatch_;

    //半径搜索的点的个数阈值
    int c_iSrhNum_;

    //设置是否进行角度限制
    bool c_bAngleSrh_;

    //设置角度
    float c_fAngle_;

    float time0, time1;

    // 平面评价阈值
    float c_fPlaneMeanDiff;
    // 平面有效阈值 PCA
    float c_fPlanePcaValue;
    int c_iPlanePcaDirection;
    // ivox
    boost::shared_ptr<IVox<3, PointTarget>> c_pIvox_ = nullptr;
    // kdtree、iVox选择开关
    int c_iOptimizeModel_;
    //双层墙Z值过滤阈值
    float c_fFilterZValue_;
    int c_iWorkModel_;
    int c_iSampleNum_;
    float c_fGroundHigh_;
    float c_fRoofHigh_;

    int c_iSlamModel_;
    // 靶标权重
    int c_iMarkWeightMax_;
    int c_iMarkWeightMin_;
    std::map<int, int> c_mapMarkId;

    // 回环检测临时方法
    float c_fDistance_;
};

#include "impl/laserRegistration.hpp"

#endif
