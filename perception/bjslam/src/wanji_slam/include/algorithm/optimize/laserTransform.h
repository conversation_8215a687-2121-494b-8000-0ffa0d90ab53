
/*
 * @Author: wa<PERSON><PERSON><PERSON>
 * @Date: 2021-04-12 16:56:06
 * @LastEditTime: 2023-07-04 15:05:16
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @Description: In User Settings Edit
 * @FilePath: /catkin_ws/src/wanji_slam/wslam/src/laserTransform.h
 */
#ifndef LASER_TRANSFORM
#define LASER_TRANSFORM

#include "common/config/conf_lidar.h"
#include "common/config/conf_timer.h"
#include "omp.h"
#include <Eigen/Dense>
#include <Eigen/Geometry>
#include <boost/shared_ptr.hpp>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>

template <typename PointT> class LaserTransform {
  public:
    typedef boost::shared_ptr<LaserTransform<PointT>> Ptr;
    typedef pcl::PointCloud<PointT> PointCloudType;
    typedef typename PointCloudType::Ptr PointCloudTypePtr;

  private:
    // int RING_FIELDS_OFFSET = 0;
    int TIME_FIELDS_OFFSET = 0;
    const static int MAX_NUM_THREAD_LTF = 1;

  public:
    void setTimeOffset(int p_iTimeOffset)
    {
        TIME_FIELDS_OFFSET = p_iTimeOffset;
    }

    LaserTransform()
    {
        c_q0_ = Eigen::Quaternionf::Identity();
        c_t0_ = Eigen::Vector3f::Zero();
        c_q1_ = Eigen::Quaternionf::Identity();
        c_t1_ = Eigen::Vector3f::Zero();
        c_qSlp_ = Eigen::Quaternionf::Identity();
        c_tSlp_ = Eigen::Vector3f::Zero();
    }
    ~LaserTransform() {}
    /**
     * @description:已知两帧与全局（某一帧）的转换关系，插值两帧之间某一时刻与全局（某一帧）的转换关系,并转换相应点
     * @param {Quaterniond} p_q0      前一帧与全局的转换关系，四元数
     * @param {Vector3d} p_t0         前一帧与全局的平移量
     * @param {Quaterniond} p_q1      当前帧与全局的转换关系，四元数
     * @param {Vector3d} p_t1         当前帧与全局的平移关系
     * @param {PointT} p_pPntIn          输入点云单个点
     * @param {PointT} &p_pntOut        输出转换后的点
     * @return {*}
     */
    void transformSlerp(const PointT* p_pPntIn, PointT& p_pntOut);
    /**
     * @description:已知两帧与全局（某一帧）的转换关系，插值两帧之间某一时刻与全局（某一帧）的转换关系,并转换相应点
     * @param {Quaterniond} p_q0      前一帧与全局的转换关系，四元数
     * @param {Vector3d} p_t0         前一帧与全局的平移量
     * @param {Quaterniond} p_q1      当前帧与全局的转换关系，四元数
     * @param {Vector3d} p_t1         当前帧与全局的平移关系
     * @param {PointT} p_pPntIn          输入点云单个点
     * @param {PointT} &p_pntOut        输出转换后的点
     * @return {*}
     */
    void transformSlerpToEnd(const PointT* p_pPntIn, PointT& p_pntOut);

    /**
     * @description:
     * 已知两帧与全局（某一帧）的转换关系，插值两帧之间某一时刻与全局（某一帧）的转换关系,并转换相应帧点云
     * @param {Quaterniond} p_q0    前一帧与全局的转换关系，四元数
     * @param {Vector3d} p_t0   前一帧与全局的平移量
     * @param {Quaterniond} p_q1    当前帧与全局的转换关系，四元数
     * @param {Vector3d} p_t1   当前帧与全局的平移关系
     * @param {const PointCloudTypePtr} p_pPcIn     输入点云
     * @param {PointCloudTypePtr} p_pPcOut      输出点云
     * @param {bool} p_bSlerpToEnd
     * 是否进行尾端补齐，默认是false。（false：不尾端补偿，true尾端补偿）
     * @param {const int} p_bSlerpToEnd
     * 是否进行尾端补齐，默认是false。（false：不尾端补偿，true尾端补偿）
     * @return {*}
     */
    void transformCloudSlerp(Eigen::Quaterniond p_q0,
                             Eigen::Vector3d p_t0,
                             Eigen::Quaterniond p_q1,
                             Eigen::Vector3d p_t1,
                             const PointCloudTypePtr p_pPcIn,
                             PointCloudTypePtr p_pPcOut,
                             const float p_iTimespan = SCAN_TIME_MS,
                             bool p_bSlerpToEnd = false);

    /**
     * @description: 点坐标转换A=p_q0*B+p_t0
     * @param {Quaterniond} p_q0         A和B之间的转换关系，四元数
     * @param {Vector3d} p_t0        A和B之间的平移量
     * @param {const PointT} *p_pPntIn        输入点云单个点
     * @param {PointT} &p_pntOut        输出转换后的点
     * @return {*}
     */
    void transformPoint(Eigen::Quaterniond p_q0,
                        Eigen::Vector3d p_t0,
                        const PointT* p_pPntIn,
                        PointT& p_pPntOut);
    /**
     * @description: 点坐标转换A=p_q0*B+p_t0
     * @param {const PointT} *p_pPntIn        输入点云单个点
     * @param {PointT} &p_pntOut        输出转换后的点
     * @return {*}
     */
    void transformPoint(const PointT* p_pPntIn, PointT& p_pntOut);
    /**
     * @description: 点云坐标转换，A=p_q0*B+p_t0
     * @param {Quaterniond} p_q0    A和B之间的转换关系，四元数
     * @param {Vector3d} p_t0       A和B之间的平移量
     * @param {const PointCloudTypePtr} p_pPcIn     输入点云
     * @param {PointCloudTypePtr} p_pPcOut      输出点云
     * @return {*}
     */
    void transformCloudPoints(Eigen::Quaterniond p_q0,
                              Eigen::Vector3d p_t0,
                              const PointCloudTypePtr p_pPcIn,
                              PointCloudTypePtr p_pPcOut);
    bool isEffectiveTransform(Eigen::Quaterniond& p_q0, Eigen::Vector3d& p_t0)
    {
        return (p_t0.norm() > 1e-3 || std::abs(p_q0.w()) < 0.99999);
    }

  private:
    //传参
    Eigen::Quaternionf c_q0_;
    Eigen::Vector3f c_t0_;
    Eigen::Quaternionf c_q1_;
    Eigen::Vector3f c_t1_;
    //求插值
    Eigen::Quaternionf c_qSlp_;
    Eigen::Vector3f c_tSlp_;
    // 矫正时间
    float c_fUdpTime_;
    float c_fDT_;
};

#include "impl/laserTransform.hpp"
#endif