/**
 * @file poseCheck.hpp
 * <AUTHOR> (<EMAIL>)
 * @brief 位姿校验模块
 * @version 1.0
 * @date 2022-08-19
 *
 * @copyright Copyright (c) 2022 <PERSON><PERSON>
 */
#pragma once
#include "common/common_ex.h"
#include "ekf.hpp"
#include <Eigen/Dense>
#include <algorithm>
#include <limits>
#include <map>
#include <string>
#include <utility>
#include <vector>

namespace RL = filter;
namespace wj_slam {
/**
 * @class PoseCheck poseCheck.h "poseCheck.h"
 * @brief 位姿校验模块
 * \ingroup poseOpt
 * @details 使用EKF滤波器收集两个定位模块的位置数据,连续定位效果类似odom数据,全局类似gps数据
 * @attention 目前无法在建图模式使用,因为帧间隔过于大,变速中无法维持匀速假设!
 * @attention 目前没有输出校验符号(~_~)所以这是个虚空模块,但肯定是可以用的
 */
class PoseCheck {
  public:
    typedef boost::shared_ptr<PoseCheck> Ptr; /**< 类指针别名 */

  private:
    using uid = int;                       /**< 子图ID类型 */
    using Time = double;                   /**< 时间类型 */
    using Pose = wj_slam::s_POSE6D;        /**< 6D位姿别名 */
    using Twist = wj_slam::s_POSE6D;       /**< 6D位姿别名 */
    using Odom = wj_slam::s_PoseWithTwist; /**< 6D位姿&速度别名 */
    using MeasurementQueue = std::priority_queue<RL::MeasurementPtr,
                                                 std::vector<RL::MeasurementPtr>,
                                                 RL::Measurement>; /**< 观测量zR优先队列定义 */

    RL::Ekf filter_; /**< 滤波器 */

    MeasurementQueue measurementQueue_; /**< 观测量zR优先队列,小顶堆 */

    // 1/180.0*M_PI
    static constexpr double D2R = 0.017453292519943;

    // 1*180.0/M_PI
    static constexpr double R2D = 57.************;

    double pose_check_threshold = 1;   /**< 认为位姿观测量异常的距离 */
    double angle_check_threshold = 90; /**< 认为位姿观测量异常的距离 */

    double pose_rejection_threshold = 2; /**< 认为位姿观测量异常的马氏距离(拒绝) */
    double twist_rejection_threshold = 0.2; /**< 认为速度观测量异常的马氏距离 */
    std::vector<int> odom_update = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1}; /**< 位姿&速度观测量表示 */
    std::vector<int> pose_update = {1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0}; /**< 位姿观测量表示 */

  public:
    PoseCheck(double p_pose_rejection_threshold, double p_angle_check_threshold);

    /**
     * @brief 行进距离检查
     *
     */
    bool checkVirtualDistOverFar(s_POSE6D& p_stPredict, s_POSE6D& p_stMeasure);

    /**
     * @brief 加入观测队列
     *
     * @param[in] topicName - 观测员名称(debug使用)
     * @param[in] measurement - 需要加入队列的观测矩阵
     * @param[in] measurementCovariance - 观测误差矩阵
     * @param[in] updateVector - 更新对应的状态空间部分状态
     * @param[in] mahalanobisThresh - 认为数据异常的马氏距离
     * @param[in] time - 测量时间(秒)
     */
    void enqueueMeasurement(const std::string name,
                            const Eigen::VectorXd& measurement,
                            const Eigen::MatrixXd& measurementCovariance,
                            const std::vector<int>& updateVector,
                            const double mahalanobisThresh,
                            const Time& time);
    /**
     * @brief 获取滤波器预估的状态
     *
     * @param[out] message 预估状态(位姿&速度)
     * @return [true] [滤波器已初始化,数据有效]
     * @return [false] [滤波器未初始化]
     */
    bool getPredictOdometryMessage(Odom& message, const Time& time);
    /**
     * @brief 获取滤波器当前的状态
     *
     * @param[out] message 当前状态(位姿&速度)
     * @return [true] [滤波器已初始化,数据有效]
     * @return [false] [滤波器未初始化]
     */
    bool getFilteredOdometryMessage(Odom& message);

    /**
     * @brief 处理观测队列中的全部观测
     *
     * @param[in] currentTime 处理队列到当前时间
     */
    void integrateMeasurements(const Time& currentTime);

    /**
     * @brief 接受观测量并输入队列
     * @attention 回调函数/普通函数均可,目前为普通调用方式
     * @todo 目前没有传递误差信息
     * @param[in] msg 输入观测量(位姿&速度)
     * @param[in] time 输入观测时间
     */
    void odometryCallback(Pose& msg, Twist& msgT, const Time& time);

    /**
     * @brief 接受观测量并输入队列
     * @attention 回调函数/普通函数均可,目前为普通调用方式
     * @todo 目前没有传递误差信息
     * @param[in] msg 输入观测量(位姿)
     * @param[in] time 输入观测时间
     */
    void slamCallBack(Pose& msg, Twist& msgT, const Time& time);
};

PoseCheck::PoseCheck(double p_pose_rejection_threshold, double p_angle_check_threshold)
    : pose_check_threshold(p_pose_rejection_threshold),
      angle_check_threshold(p_angle_check_threshold),
      pose_rejection_threshold(pose_check_threshold * 2)
{
    // LOGFAE(WINFO,
    //        "定位校验 | 位姿行进距离&角度 不得超过 {:.1f} m, {:.0f} deg",
    //        pose_check_threshold,
    //        angle_check_threshold);
}

bool PoseCheck::checkVirtualDistOverFar(s_POSE6D& p_stPredict, s_POSE6D& p_stMeasure)
{
    s_POSE6D l_poseDev = p_stPredict.inverse() * p_stMeasure;
    double l_dPosDis = l_poseDev.normXY();
    double l_dAngDev = std::fabs(l_poseDev.yaw());
    if (l_dPosDis > pose_check_threshold)
    {
        LOGFAE(WERROR,
               "定位异常 | 位姿行进距离 {:.3f} 超过 {:.3f} m",
               l_dPosDis,
               pose_check_threshold);
        return false;
    }
    if (l_dAngDev > angle_check_threshold)
    {
        LOGFAE(WERROR,
               "定位异常 | 位姿行进角度 {:.3f} 超过 {:.3f} deg",
               l_dAngDev,
               angle_check_threshold);
        return false;
    }
    return true;
}

void PoseCheck::integrateMeasurements(const Time& currentTime)
{
    // If we have any measurements in the queue, process them
    if (!measurementQueue_.empty())
    {
        while (!measurementQueue_.empty())
        {
            RL::MeasurementPtr measurement = measurementQueue_.top();
            measurementQueue_.pop();

            // This will call predict and, if necessary, correct
            filter_.processMeasurement(*(measurement.get()));
        }
        filter_.setLastUpdateTime(currentTime);
    }
    else if (filter_.getInitializedStatus())
    {
        // In the event that we don't get any measurements for a long time,
        // we still need to continue to estimate our state. Therefore, we
        // should project the state forward here.
        double lastUpdateDelta = currentTime - filter_.getLastUpdateTime();

        // If we get a large delta, then continuously predict until
        // if (lastUpdateDelta >= filter_.getSensorTimeout())
        {
            LOGFAE(WERROR,
                   "定位异常 | 本次定位 {:.1f} sec,上次定位 {:.1f} sec",
                   currentTime,
                   filter_.getLastUpdateTime());
            filter_.predict(lastUpdateDelta);

            // Update the last measurement time and last update time
            filter_.setLastMeasurementTime(filter_.getLastMeasurementTime() + lastUpdateDelta);
            filter_.setLastUpdateTime(filter_.getLastUpdateTime() + lastUpdateDelta);
        }
    }
    else
    {
        std::cout << "Filter not yet initialized." << std::endl;
    }
}

bool PoseCheck::getPredictOdometryMessage(Odom& message, const Time& time)
{
    // If the filter has received a measurement at some point...
    if (filter_.getInitializedStatus())
    {
        double lastUpdateDelta = time - filter_.getLastUpdateTime();
        filter_.predict(lastUpdateDelta);
        // Grab our current state and covariance estimates
        const Eigen::VectorXd& state = filter_.getState();
        const Eigen::MatrixXd& estimateErrorCovariance = filter_.getEstimateErrorCovariance();

        message.m_Pose.setXYZ(
            state(RL::StateMemberX), state(RL::StateMemberY), state(RL::StateMemberZ));
        message.m_Pose.setRPY(state(RL::StateMemberRoll) * R2D,
                              state(RL::StateMemberPitch) * R2D,
                              state(RL::StateMemberYaw) * R2D);
        message.m_Twist.setXYZ(
            state(RL::StateMemberVx), state(RL::StateMemberVy), state(RL::StateMemberVz));
        message.m_Twist.setRPY(state(RL::StateMemberVroll) * R2D,
                               state(RL::StateMemberVpitch) * R2D,
                               state(RL::StateMemberVyaw) * R2D);
        message.m_tsSyncTime = filter_.getLastMeasurementTime() * 1000.0;

        //   for (size_t i = 0; i < POSE_SIZE; i++)
        //   {
        //     for (size_t j = 0; j < POSE_SIZE; j++)
        //     {
        //       message.m_Pose.covariance[POSE_SIZE * i + j] = estimateErrorCovariance(i, j);
        //     }
        //   }
        //   for (size_t i = 0; i < TWIST_SIZE; i++)
        //   {
        //     for (size_t j = 0; j < TWIST_SIZE; j++)
        //     {
        //       message.m_Twist.covariance[TWIST_SIZE * i + j] =
        //           estimateErrorCovariance(i + POSITION_V_OFFSET, j + POSITION_V_OFFSET);
        //     }
        //   }
    }
    return filter_.getInitializedStatus();
}

bool PoseCheck::getFilteredOdometryMessage(Odom& message)
{
    // If the filter has received a measurement at some point...
    if (filter_.getInitializedStatus())
    {
        // Grab our current state and covariance estimates
        const Eigen::VectorXd& state = filter_.getState();
        const Eigen::MatrixXd& estimateErrorCovariance = filter_.getEstimateErrorCovariance();

        message.m_Pose.setXYZ(
            state(RL::StateMemberX), state(RL::StateMemberY), state(RL::StateMemberZ));
        message.m_Pose.setRPY(state(RL::StateMemberRoll) * R2D,
                              state(RL::StateMemberPitch) * R2D,
                              state(RL::StateMemberYaw) * R2D);
        message.m_Twist.setXYZ(
            state(RL::StateMemberVx), state(RL::StateMemberVy), state(RL::StateMemberVz));
        message.m_Twist.setRPY(state(RL::StateMemberVroll) * R2D,
                               state(RL::StateMemberVpitch) * R2D,
                               state(RL::StateMemberVyaw) * R2D);
        message.m_tsSyncTime = filter_.getLastMeasurementTime() * 1000.0;

        //   for (size_t i = 0; i < POSE_SIZE; i++)
        //   {
        //     for (size_t j = 0; j < POSE_SIZE; j++)
        //     {
        //       message.m_Pose.covariance[POSE_SIZE * i + j] = estimateErrorCovariance(i, j);
        //     }
        //   }
        //   for (size_t i = 0; i < TWIST_SIZE; i++)
        //   {
        //     for (size_t j = 0; j < TWIST_SIZE; j++)
        //     {
        //       message.m_Twist.covariance[TWIST_SIZE * i + j] =
        //           estimateErrorCovariance(i + POSITION_V_OFFSET, j + POSITION_V_OFFSET);
        //     }
        //   }
    }
    return filter_.getInitializedStatus();
}
void PoseCheck::odometryCallback(Pose& msg, Twist& msgT, const Time& time)
{
    Eigen::VectorXd measurement(RL::STATE_SIZE);
    Eigen::MatrixXd measurementCovariance(RL::STATE_SIZE, RL::STATE_SIZE);
    Eigen::VectorXd m6D = msg.coeffs_6();
    m6D.tail(3) = m6D.tail(3) * D2R;
    Eigen::VectorXd mV6D = msgT.coeffs_6();
    mV6D.tail(3) = mV6D.tail(3) * D2R;
    measurement.setZero();
    measurement.head(6) = m6D;
    measurement.block(6, 0, 6, 1) = mV6D;
    measurementCovariance.setZero();
    measurementCovariance(0, 0) = 0.001;
    measurementCovariance(1, 1) = 0.001;
    measurementCovariance(2, 2) = 0.003;
    measurementCovariance(3, 3) = 0.006;
    measurementCovariance(4, 4) = 0.006;
    measurementCovariance(5, 5) = 0.01;
    measurementCovariance(6, 6) = 0.0005;
    measurementCovariance(7, 7) = 0.0005;
    measurementCovariance(8, 8) = 0.002;
    measurementCovariance(9, 9) = 0.003;
    measurementCovariance(10, 10) = 0.003;
    measurementCovariance(11, 11) = 0.006;
    // Store the measurement. Add a "pose" suffix so we know what kind of measurement
    // we're dealing with when we debug the core filter logic.
    enqueueMeasurement("local-Locat",
                       measurement,
                       measurementCovariance,
                       odom_update,
                       pose_rejection_threshold,
                       time);
}
void PoseCheck::slamCallBack(Pose& msg, Twist& msgT, const Time& time)
{
    Eigen::VectorXd measurement(RL::STATE_SIZE);
    Eigen::MatrixXd measurementCovariance(RL::STATE_SIZE, RL::STATE_SIZE);
    Eigen::VectorXd m6D = msg.coeffs_6();
    m6D.tail(3) = m6D.tail(3) * D2R;
    Eigen::VectorXd mV6D = msgT.coeffs_6();
    mV6D.tail(3) = mV6D.tail(3) * D2R;
    measurement.setZero();
    measurement.head(6) = m6D;
    measurement.block(6, 0, 6, 1) = mV6D;
    measurementCovariance.setZero();
    measurementCovariance(0, 0) = 0.0004;
    measurementCovariance(1, 1) = 0.0004;
    measurementCovariance(2, 2) = 0.0005;
    measurementCovariance(3, 3) = 0.003;
    measurementCovariance(4, 4) = 0.003;
    measurementCovariance(5, 5) = 0.006;
    measurementCovariance(6, 6) = 0.0002;
    measurementCovariance(7, 7) = 0.0002;
    measurementCovariance(8, 8) = 0.004;
    measurementCovariance(9, 9) = 0.001;
    measurementCovariance(10, 10) = 0.001;
    measurementCovariance(11, 11) = 0.002;
    // Store the measurement. Add a "pose" suffix so we know what kind of measurement
    // we're dealing with when we debug the core filter logic.
    enqueueMeasurement("global-Locat",
                       measurement,
                       measurementCovariance,
                       pose_update,
                       pose_rejection_threshold,
                       time);
}
void PoseCheck::enqueueMeasurement(const std::string name,
                                   const Eigen::VectorXd& measurement,
                                   const Eigen::MatrixXd& measurementCovariance,
                                   const std::vector<int>& updateVector,
                                   const double mahalanobisThresh,
                                   const Time& time)
{
    RL::MeasurementPtr meas = RL::MeasurementPtr(new RL::Measurement());
    meas->topicName_ = name;
    meas->measurement_ = measurement;
    meas->covariance_ = measurementCovariance;
    meas->updateVector_ = updateVector;
    meas->time_ = time;
    meas->mahalanobisThresh_ = mahalanobisThresh;
    measurementQueue_.push(meas);
}
}  // namespace wj_slam