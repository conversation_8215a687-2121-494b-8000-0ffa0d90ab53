/**
 * @file filter.h
 * <AUTHOR> (ch<PERSON><PERSON><PERSON>@wanji.net.cn)
 * @brief 滤波器模板
 * @version 1.0
 * @date 2022-08-19
 * @see http://docs.ros.org/en/noetic/api/robot_localization/html/index.html
 * @copyright Copyright (c) 2022 <PERSON><PERSON>
 */
#pragma once

#include <Eigen/Dense>
#include <boost/shared_ptr.hpp>
#include <iomanip>
#include <iostream>
#include <limits>
#include <map>
#include <ostream>
#include <queue>
#include <string>
#include <vector>

namespace filter {
typedef unsigned long size_t;

//! @brief Enumeration that defines the state vector
//!
enum StateMembers {
    StateMemberX = 0,
    StateMemberY,
    StateMemberZ,
    StateMemberRoll,
    StateMemberPitch,
    StateMemberYaw,
    StateMemberVx,
    StateMemberVy,
    StateMemberVz,
    StateMemberVroll,
    StateMemberVpitch,
    StateMemberVyaw,
};

//! @brief Global constants that define our state
//! vector size and offsets to groups of values
//! within that state.
const int STATE_SIZE = 12;
const int POSITION_OFFSET = StateMemberX;
const int ORIENTATION_OFFSET = StateMemberRoll;
const int POSITION_V_OFFSET = StateMemberVx;
const int ORIENTATION_V_OFFSET = StateMemberVroll;

//! @brief Pose and twist messages each
//! contain six variables
const int POSE_SIZE = 6;
const int TWIST_SIZE = 6;
const int POSITION_SIZE = 3;
const int ORIENTATION_SIZE = 3;

//! @brief Common variables
const double PI = 3.141592653589793;
const double TAU = 6.283185307179587;

// Handy methods for debug output
// #define FB_DEBUG(msg) std::cout << msg;
#define FB_DEBUG(msg)
std::ostream& operator<<(std::ostream& os, const Eigen::MatrixXd& mat);
std::ostream& operator<<(std::ostream& os, const Eigen::VectorXd& vec);
std::ostream& operator<<(std::ostream& os, const std::vector<size_t>& vec);
std::ostream& operator<<(std::ostream& os, const std::vector<int>& vec);

std::ostream& operator<<(std::ostream& os, const Eigen::MatrixXd& mat)
{
    os << "[";

    int rowCount = static_cast<int>(mat.rows());

    for (int row = 0; row < rowCount; ++row)
    {
        if (row > 0)
        {
            os << " ";
        }

        for (int col = 0; col < mat.cols(); ++col)
        {
            os << std::setiosflags(std::ios::left) << std::setw(12) << std::setprecision(5)
               << mat(row, col);
        }

        if (row < rowCount - 1)
        {
            os << "\n";
        }
    }

    os << "]\n";

    return os;
}

std::ostream& operator<<(std::ostream& os, const Eigen::VectorXd& vec)
{
    os << "[";
    for (int dim = 0; dim < vec.rows(); ++dim)
    {
        os << std::setiosflags(std::ios::left) << std::setw(12) << std::setprecision(5) << vec(dim);
    }
    os << "]\n";

    return os;
}

std::ostream& operator<<(std::ostream& os, const std::vector<size_t>& vec)
{
    os << "[";
    for (size_t dim = 0; dim < vec.size(); ++dim)
    {
        os << std::setiosflags(std::ios::left) << std::setw(12) << std::setprecision(5) << vec[dim];
    }
    os << "]\n";

    return os;
}

std::ostream& operator<<(std::ostream& os, const std::vector<int>& vec)
{
    os << "[";
    for (size_t dim = 0; dim < vec.size(); ++dim)
    {
        os << std::setiosflags(std::ios::left) << std::setw(3) << (vec[dim] ? "t" : "f");
    }
    os << "]\n";

    return os;
}

//! @brief Structure used for storing and comparing measurements
//! (for priority queues)
//!
//! Measurement units are assumed to be in meters and radians.
//! Times are real-valued and measured in seconds.
//!
struct Measurement
{
    // The topic name for this measurement. Needed
    // for capturing previous state values for new
    // measurements.
    std::string topicName_;

    // The measurement and its associated covariance
    Eigen::VectorXd measurement_;
    Eigen::MatrixXd covariance_;

    // This defines which variables within this measurement
    // actually get passed into the filter. std::vector<bool>
    // is generally frowned upon, so we use ints.
    std::vector<int> updateVector_;

    // The real-valued time, in seconds, since some epoch
    // (presumably the start of execution, but any will do)
    double time_;

    // The Mahalanobis distance threshold in number of sigmas
    double mahalanobisThresh_;

    // We want earlier times to have greater priority
    bool operator()(const boost::shared_ptr<Measurement>& a,
                    const boost::shared_ptr<Measurement>& b)
    {
        return (*this)(*(a.get()), *(b.get()));
    }

    bool operator()(const Measurement& a, const Measurement& b)
    {
        return a.time_ > b.time_;
    }

    Measurement()
        : topicName_(""), time_(0.0), mahalanobisThresh_(std::numeric_limits<double>::max())
    {
    }
};
typedef boost::shared_ptr<Measurement> MeasurementPtr;

//! @brief Structure used for storing and comparing filter states
//!
//! This structure is useful when higher-level classes need to remember filter
//! history. Measurement units are assumed to be in meters and radians. Times
//! are real-valued and measured in seconds.
//!
struct FilterState
{
    // The filter state vector
    Eigen::VectorXd state_;

    // The filter error covariance matrix
    Eigen::MatrixXd estimateErrorCovariance_;

    // The time stamp of the most recent measurement for the filter
    double lastMeasurementTime_;

    // We want the queue to be sorted from latest to earliest timestamps.
    bool operator()(const FilterState& a, const FilterState& b)
    {
        return a.lastMeasurementTime_ < b.lastMeasurementTime_;
    }

    FilterState() : state_(), estimateErrorCovariance_(), lastMeasurementTime_(0.0) {}
};
typedef boost::shared_ptr<FilterState> FilterStatePtr;

class FilterBase {
  public:
    //! @brief Constructor for the FilterBase class
    //!
    FilterBase();

    //! @brief Destructor for the FilterBase class
    //!
    virtual ~FilterBase();

    //! @brief Carries out the correct step in the predict/update cycle. This
    //! method must be implemented by subclasses.
    //!
    //! @param[in] measurement - The measurement to fuse with the state estimate
    //!
    virtual void correct(const Measurement& measurement) = 0;

    //! @brief Gets the estimate error covariance
    //!
    //! @return A copy of the estimate error covariance matrix
    //!
    const Eigen::MatrixXd& getEstimateErrorCovariance();

    //! @brief Gets the filter's initialized status
    //!
    //! @return True if we've received our first measurement, false otherwise
    //!
    bool getInitializedStatus();

    //! @brief Gets the most recent measurement time
    //!
    //! @return The time at which we last received a measurement
    //!
    double getLastMeasurementTime();

    //! @brief Gets the filter's last update time
    //!
    //! @return The time at which we last updated the filter,
    //! which can occur even when we don't receive measurements
    //!
    double getLastUpdateTime();

    //! @brief Gets the filter's predicted state, i.e., the
    //! state estimate before correct() is called.
    //!
    //! @return A constant reference to the predicted state
    //!
    const Eigen::VectorXd& getPredictedState();

    //! @brief Gets the filter's process noise covariance
    //!
    //! @return A constant reference to the process noise covariance
    //!
    const Eigen::MatrixXd& getProcessNoiseCovariance();

    //! @brief Gets the filter state
    //!
    //! @return A constant reference to the current state
    //!
    const Eigen::VectorXd& getState();

    //! @brief Carries out the predict step in the predict/update cycle.
    //! Projects the state and error matrices forward using a model of
    //! the vehicle's motion. This method must be implemented by subclasses.
    //!
    //! @param[in] delta - The time step over which to predict.
    //!
    virtual void predict(const double delta) = 0;

    //! @brief Does some final preprocessing, carries out the predict/update cycle
    //!
    //! @param[in] measurement - The measurement object to fuse into the filter
    //!
    virtual void processMeasurement(const Measurement& measurement);

    //! @brief Manually sets the filter's estimate error covariance
    //!
    //! @param[in] estimateErrorCovariance - The state to set as the filter's
    //! current state
    //!
    void setEstimateErrorCovariance(const Eigen::MatrixXd& estimateErrorCovariance);

    //! @brief Sets the filter's last measurement time.
    //!
    //! @param[in] lastMeasurementTime - The last measurement time of the filter
    //!
    void setLastMeasurementTime(const double lastMeasurementTime);

    //! @brief Sets the filter's last update time.
    //!
    //! This is used mostly for initialization purposes, as the
    //! integrateMeasurements() function will update the filter's last update time
    //! as well.
    //!
    //! @param[in] lastUpdateTime - The last update time of the filter
    //!
    void setLastUpdateTime(const double lastUpdateTime);

    //! @brief Sets the process noise covariance for the filter.
    //!
    //! This enables external initialization, which is important, as this
    //! matrix can be difficult to tune for a given implementation.
    //!
    //! @param[in] processNoiseCovariance - The STATE_SIZExSTATE_SIZE process
    //! noise covariance matrix to use for the filter
    //!
    void setProcessNoiseCovariance(const Eigen::MatrixXd& processNoiseCovariance);

    //! @brief Manually sets the filter's state
    //!
    //! @param[in] state - The state to set as the filter's current state
    //!
    void setState(const Eigen::VectorXd& state);

    //! @brief Utility method keeping RPY angles in the range [-pi, pi]
    //! @param[in] rotation - The rotation to bind
    //! @return the bounded value
    //!
    double clampRotation(double rotation);

    //! @brief Keeps the state Euler angles in the range [-pi, pi]
    //!
    virtual void wrapStateAngles();

    //! @brief Tests if innovation is within N-sigmas of covariance. Returns true if passed the
    //! test.
    //! @param[in] innovation - The difference between the measurement and the state
    //! @param[in] invCovariance - The innovation error
    //! @param[in] nsigmas - Number of standard deviations that are considered acceptable
    //!
    virtual bool checkMahalanobisThreshold(const Eigen::VectorXd& innovation,
                                           const Eigen::MatrixXd& invCovariance,
                                           const double nsigmas);

    //! @brief Covariance matrices can be incredibly unstable. We can
    //! add a small value to it at each iteration to help maintain its
    //! positive-definite property.
    //!
    Eigen::MatrixXd covarianceEpsilon_;

    //! @brief This matrix stores the total error in our position
    //! estimate (the state_ variable).
    //!
    Eigen::MatrixXd estimateErrorCovariance_;

    //! @brief We need the identity for a few operations. Better to store it.
    //!
    Eigen::MatrixXd identity_;

    //! @brief Whether or not we've received any measurements
    //!
    bool initialized_;

    //! @brief Tracks the time the filter was last updated using a measurement.
    //!
    //! This value is used to monitor sensor readings with respect to the
    //! sensorTimeout_. We also use it to compute the time delta values for our
    //! prediction step.
    //!
    double lastMeasurementTime_;

    //! @brief Used for tracking the latest update time as determined
    //! by this class.
    //!
    //! We assume that this class may receive measurements that occurred in the
    //! past, as may happen with sensors distributed on different machines on a
    //! network. This variable tracks when the filter was updated with respect to
    //! the executable in which this class was instantiated. We use this to
    //! determine if we have experienced a sensor timeout, i.e., if we haven't
    //! received any sensor data in a long time.
    //!
    double lastUpdateTime_;

    //! @brief Holds the last predicted state of the filter
    //!
    Eigen::VectorXd predictedState_;

    //! @brief As we move through the world, we follow a predict/update
    //! cycle. If one were to imagine a scenario where all we did was make
    //! predictions without correcting, the error in our position estimate
    //! would grow without bound. This error is stored in the
    //! stateEstimateCovariance_ matrix. However, this matrix doesn't answer
    //! the question of *how much* our error should grow for each time step.
    //! That's where the processNoiseCovariance matrix comes in. When we
    //! make a prediction using the transfer function, we add this matrix
    //! (times deltaT) to the state estimate covariance matrix.
    //!
    Eigen::MatrixXd processNoiseCovariance_;

    //! @brief This is the robot's state vector, which is what we are trying to
    //! filter. The values in this vector are what get reported by the node.
    //!
    Eigen::VectorXd state_;

    //! @brief The Kalman filter transfer function
    //!
    //! Kalman filters and extended Kalman filters project the current
    //! state forward in time. This is the "predict" part of the predict/correct
    //! cycle. A Kalman filter has a (typically constant) matrix A that defines
    //! how to turn the current state, x, into the predicted next state. For an
    //! EKF, this matrix becomes a function f(x). However, this function can still
    //! be expressed as a matrix to make the math a little cleaner, which is what
    //! we do here. Technically, each row in the matrix is actually a function.
    //! Some rows will contain many trigonometric functions, which are of course
    //! non-linear. In any case, you can think of this as the 'A' matrix in the
    //! Kalman filter formulation.
    //!
    Eigen::MatrixXd transferFunction_;

    //! @brief The Kalman filter transfer function Jacobian
    //!
    //! The transfer function is allowed to be non-linear in an EKF, but
    //! for propagating (predicting) the covariance matrix, we need to linearize
    //! it about the current mean (i.e., state). This is done via a Jacobian,
    //! which calculates partial derivatives of each row of the transfer function
    //! matrix with respect to each state variable.
    //!
    Eigen::MatrixXd transferFunctionJacobian_;

  private:
    //! @brief Whether or not the filter is in debug mode
    //!
    bool debug_;
};

FilterBase::FilterBase()
    : covarianceEpsilon_(STATE_SIZE, STATE_SIZE), estimateErrorCovariance_(STATE_SIZE, STATE_SIZE),
      identity_(STATE_SIZE, STATE_SIZE), predictedState_(STATE_SIZE),
      processNoiseCovariance_(STATE_SIZE, STATE_SIZE), state_(STATE_SIZE),
      transferFunction_(STATE_SIZE, STATE_SIZE), transferFunctionJacobian_(STATE_SIZE, STATE_SIZE),
      debug_(false)
{
    initialized_ = false;

    // Clear the state and predicted state
    state_.setZero();
    predictedState_.setZero();

    // Prepare the invariant parts of the transfer
    // function
    transferFunction_.setIdentity();

    // Clear the Jacobian
    transferFunctionJacobian_.setZero();

    // Set the estimate error covariance. We want our measurements
    // to be accepted rapidly when the filter starts, so we should
    // initialize the state's covariance with large values.
    estimateErrorCovariance_.setIdentity();
    estimateErrorCovariance_ *= 1e-9;

    // We need the identity for the update equations
    identity_.setIdentity();

    // Set the epsilon matrix to be a matrix with small values on the diagonal
    // It is used to maintain the positive-definite property of the covariance
    covarianceEpsilon_.setIdentity();
    covarianceEpsilon_ *= 0.001;

    // Initialize our last update and measurement times
    lastUpdateTime_ = 0;
    lastMeasurementTime_ = 0;

    // These can be overridden via the launch parameters,
    // but we need default values.
    processNoiseCovariance_.setZero();
    processNoiseCovariance_(StateMemberX, StateMemberX) = 0.05;
    processNoiseCovariance_(StateMemberY, StateMemberY) = 0.05;
    processNoiseCovariance_(StateMemberZ, StateMemberZ) = 0.06;
    processNoiseCovariance_(StateMemberRoll, StateMemberRoll) = 0.03;
    processNoiseCovariance_(StateMemberPitch, StateMemberPitch) = 0.03;
    processNoiseCovariance_(StateMemberYaw, StateMemberYaw) = 0.06;
    processNoiseCovariance_(StateMemberVx, StateMemberVx) = 0.025;
    processNoiseCovariance_(StateMemberVy, StateMemberVy) = 0.025;
    processNoiseCovariance_(StateMemberVz, StateMemberVz) = 0.04;
    processNoiseCovariance_(StateMemberVroll, StateMemberVroll) = 0.01;
    processNoiseCovariance_(StateMemberVpitch, StateMemberVpitch) = 0.01;
    processNoiseCovariance_(StateMemberVyaw, StateMemberVyaw) = 0.02;
}

FilterBase::~FilterBase() {}

const Eigen::MatrixXd& FilterBase::getEstimateErrorCovariance()
{
    return estimateErrorCovariance_;
}

bool FilterBase::getInitializedStatus()
{
    return initialized_;
}

double FilterBase::getLastMeasurementTime()
{
    return lastMeasurementTime_;
}

double FilterBase::getLastUpdateTime()
{
    return lastUpdateTime_;
}

const Eigen::VectorXd& FilterBase::getPredictedState()
{
    return predictedState_;
}

const Eigen::MatrixXd& FilterBase::getProcessNoiseCovariance()
{
    return processNoiseCovariance_;
}

const Eigen::VectorXd& FilterBase::getState()
{
    return state_;
}

void FilterBase::processMeasurement(const Measurement& measurement)
{
    FB_DEBUG("------ FilterBase::processMeasurement (" << measurement.topicName_ << ") ------\n");

    double delta = 0.0;

    // If we've had a previous reading, then go through the predict/update
    // cycle. Otherwise, set our state and covariance to whatever we get
    // from this measurement.
    if (initialized_)
    {
        // Determine how much time has passed since our last measurement
        delta = measurement.time_ - lastMeasurementTime_;

        FB_DEBUG("Filter is already initialized. Carrying out predict/correct loop...\n"
                 "Measurement time is "
                 << std::setprecision(20) << measurement.time_ << ", last measurement time is "
                 << lastMeasurementTime_ << ", delta is " << delta << "\n");

        // Only want to carry out a prediction if it's
        // forward in time. Otherwise, just correct.
        if (delta > 0)
        {
            predict(delta);

            // Return this to the user
            predictedState_ = state_;
        }

        correct(measurement);
    }
    else
    {
        FB_DEBUG("First measurement. Initializing filter.\n");

        // Initialize the filter, but only with the values we're using
        size_t measurementLength = measurement.updateVector_.size();
        for (size_t i = 0; i < measurementLength; ++i)
        {
            state_[i] = (measurement.updateVector_[i] ? measurement.measurement_[i] : state_[i]);
        }

        // Same for covariance
        for (size_t i = 0; i < measurementLength; ++i)
        {
            for (size_t j = 0; j < measurementLength; ++j)
            {
                estimateErrorCovariance_(i, j) =
                    (measurement.updateVector_[i] && measurement.updateVector_[j]
                         ? measurement.covariance_(i, j)
                         : estimateErrorCovariance_(i, j));
            }
        }

        initialized_ = true;
    }

    if (delta >= 0.0)
    {
        // Update the last measurement and update time.
        // The measurement time is based on the time stamp of the
        // measurement, whereas the update time is based on this
        // node's current ROS time. The update time is used to
        // determine if we have a sensor timeout, whereas the
        // measurement time is used to calculate time deltas for
        // prediction and correction.
        lastMeasurementTime_ = measurement.time_;
    }

    FB_DEBUG("------ /FilterBase::processMeasurement (" << measurement.topicName_ << ") ------\n");
}

void FilterBase::setEstimateErrorCovariance(const Eigen::MatrixXd& estimateErrorCovariance)
{
    estimateErrorCovariance_ = estimateErrorCovariance;
}

void FilterBase::setLastMeasurementTime(const double lastMeasurementTime)
{
    lastMeasurementTime_ = lastMeasurementTime;
}

void FilterBase::setLastUpdateTime(const double lastUpdateTime)
{
    lastUpdateTime_ = lastUpdateTime;
}

void FilterBase::setProcessNoiseCovariance(const Eigen::MatrixXd& processNoiseCovariance)
{
    processNoiseCovariance_ = processNoiseCovariance;
}

void FilterBase::setState(const Eigen::VectorXd& state)
{
    state_ = state;
}

double FilterBase::clampRotation(double rotation)
{
    while (rotation > PI)
    {
        rotation -= TAU;
    }

    while (rotation < -PI)
    {
        rotation += TAU;
    }

    return rotation;
}

void FilterBase::wrapStateAngles()
{
    state_(StateMemberRoll) = clampRotation(state_(StateMemberRoll));
    state_(StateMemberPitch) = clampRotation(state_(StateMemberPitch));
    state_(StateMemberYaw) = clampRotation(state_(StateMemberYaw));
}

bool FilterBase::checkMahalanobisThreshold(const Eigen::VectorXd& innovation,
                                           const Eigen::MatrixXd& invCovariance,
                                           const double nsigmas)
{
    double sqMahalanobis = innovation.dot(invCovariance * innovation);
    double threshold = nsigmas * nsigmas;

    if (sqMahalanobis >= threshold)
    {
        FB_DEBUG("Innovation mahalanobis distance test failed. Squared Mahalanobis is: "
                 << sqMahalanobis << "\n"
                 << "Threshold is: " << threshold << "\n"
                 << "Innovation is: " << innovation << "\n"
                 << "Innovation covariance is:\n"
                 << invCovariance << "\n");

        return false;
    }

    return true;
}
}  // namespace filter