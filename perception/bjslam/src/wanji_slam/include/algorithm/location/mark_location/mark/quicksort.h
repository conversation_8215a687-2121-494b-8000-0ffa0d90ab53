/*
 * @Author: your name
 * @Date: 2021-06-02 13:56:28
 * @LastEditTime: 2021-06-02 14:33:36
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: /720slam/src/wanji_slam/include/algorithm/location/mark_location/mark/quicksort.h
 */
#pragma once
#ifndef __QUICKSORT_H__
#    define __QUICKSORT_H__

#    include "marktype.h"
// #ifdef __QUICKSORT_C
#    define QUICKSORT_EXT
// #else
// #define QUICKSORT_EXT extern
// #endif

QUICKSORT_EXT int partition(int* number, int left, int right);
QUICKSORT_EXT void quicksort(int* number, int left, int right);
QUICKSORT_EXT void SWAP(int* x, int* y);

QUICKSORT_EXT int Return_AxisData(u8 axis, MARK_XY* p_Mark);
QUICKSORT_EXT void SWAP_MARK(MARK_XY** x, MARK_XY** y);
QUICKSORT_EXT int
partition_MARK(MARK_XY* p_MarkSet, int left, int right, char axis, MARK_XY** p_MarkAddr);
QUICKSORT_EXT void
quicksort_Marks(MARK_XY* p_MarkSet, int left, int right, char axis, MARK_XY** p_MarkAddr);
#endif
