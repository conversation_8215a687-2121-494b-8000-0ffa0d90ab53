#pragma once
#ifndef _MARKSLOCATION_COMMON_H_
#    define _MARKSLOCATION_COMMON_H_

// #include <ros/ros.h>

// #    include <geometry_msgs/PoseStamped.h>
// #    include <geometry_msgs/Quaternion.h>
// #    include <nav_msgs/Odometry.h>
// #    include <nav_msgs/Path.h>
#    include <pcl/kdtree/kdtree_flann.h>
#    include <pcl/point_cloud.h>
#    include <pcl/point_types.h>
// #    include <pcl_conversions/pcl_conversions.h>
// #    include <pcl_ros/point_cloud.h>
// #    include <sensor_msgs/PointCloud2.h>

// #    include <tf/transform_broadcaster.h>
// #    include <tf/transform_datatypes.h>

#    include "tic_toc.h"
#    include <algorithm>
#    include <array>
#    include <cmath>
#    include <fstream>
#    include <iostream>
#    include <iterator>
#    include <sstream>
#    include <string>
#    include <termios.h>

//#define TIME_TEST

/***********************************************************************/
// chen
//新定义的点云类，包含强度，置信度，线号，时间差
/** Euclidean Wanji coordinate, including intensity and ring number. */
struct PointXYZICRT
{
    PCL_ADD_POINT4D;                 // quad-word XYZ
    PCL_ADD_INTENSITY;               ///< laser intensity reading
    float confidence;                ///< Degree of confidence
    float ring;                      ///< laser ring number
    float deltaT;                    ///< Time compare to fisrt point
    EIGEN_MAKE_ALIGNED_OPERATOR_NEW  // ensure proper alignment
} EIGEN_ALIGN16;

POINT_CLOUD_REGISTER_POINT_STRUCT(PointXYZICRT,
                                  (float, x, x)(float, y, y)(float, z, z)(float,
                                                                          intensity,
                                                                          intensity)(
                                      float,
                                      confidence,
                                      confidence)(float, ring, ring)(float, deltaT, deltaT))

typedef PointXYZICRT PointType;
typedef pcl::PointCloud<PointType> VPointCloud2;

/***********************************************************************/
inline double rad2deg(double radians)
{
    return radians * 180.0 / M_PI;
}

inline double deg2rad(double degrees)
{
    return degrees * M_PI / 180.0;
}

static void toEulerAngle(const Eigen::Quaterniond& q, double& roll, double& pitch, double& yaw)
{
    // roll (x-axis rotation)
    double sinr_cosp = +2.0 * (q.w() * q.x() + q.y() * q.z());
    double cosr_cosp = +1.0 - 2.0 * (q.x() * q.x() + q.y() * q.y());
    roll = atan2(sinr_cosp, cosr_cosp);

    // pitch (y-axis rotation)
    double sinp = +2.0 * (q.w() * q.y() - q.z() * q.x());
    if (fabs(sinp) >= 1)
        pitch = copysign(M_PI / 2, sinp);  // use 90 degrees if out of range
    else
        pitch = asin(sinp);

    // yaw (z-axis rotation)
    double siny_cosp = +2.0 * (q.w() * q.z() + q.x() * q.y());
    double cosy_cosp = +1.0 - 2.0 * (q.y() * q.y() + q.z() * q.z());
    yaw = atan2(siny_cosp, cosy_cosp);
}

Eigen::Quaterniond RPY2Quat(Eigen::Vector3d RPY)
{
    const Eigen::AngleAxisd roll_angle(RPY(0), Eigen::Vector3d::UnitX());
    const Eigen::AngleAxisd pitch_angle(RPY(1), Eigen::Vector3d::UnitY());
    const Eigen::AngleAxisd yaw_angle(RPY(2), Eigen::Vector3d::UnitZ());
    return yaw_angle * pitch_angle * roll_angle;
}

/**
 * @brief GetKeyFromTermino()
 * 非阻塞形式，获取键值
 */
static inline int GetKeyEnterTermino(void)
{
    fd_set rfds;
    struct timeval tv;
    int retval;

    /* Watch stdin (fd 0) to see when it has input. */
    FD_ZERO(&rfds);
    FD_SET(0, &rfds);
    /* Wait up to five seconds. */
    tv.tv_sec = 0;
    tv.tv_usec = 0;

    retval = select(1, &rfds, NULL, NULL, &tv);
    /* Don't rely on the value of tv now! */

    if (retval == -1)
    {
        perror("select()");
        return 0;
    }
    else if (retval)
        return 1;
    /* FD_ISSET(0, &rfds) will be true. */
    else
        return 0;
    return 0;
}

/**
 * @brief GetKeyFromTermino()
 * 阻塞形式，获取键值
 */
static inline int GetKeyFromTermino()
{
    int key_value;
    struct termios new_config;
    struct termios old_config;

    tcgetattr(0, &old_config);
    new_config = old_config;
    new_config.c_lflag &= ~(ICANON | ECHO);
    tcsetattr(0, TCSANOW, &new_config);

    key_value = getchar();

    tcsetattr(0, TCSANOW, &old_config);

    // ROS_INFO("key_value: %d\n", key_value);
    if (key_value == 115)
        return 1;
    else
        return 0;
}

#endif
