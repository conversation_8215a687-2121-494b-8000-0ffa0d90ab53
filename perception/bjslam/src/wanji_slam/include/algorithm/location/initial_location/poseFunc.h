#pragma once
#ifndef POSEFUNC_H_
#    define POSEFUNC_H_

#    include "common_func.h"
#    include "header.h"
#    include <chrono>
#    include <iostream>
#    include <random>
#    include <string>

using namespace std;
using namespace std::chrono;

//初始定位0,继续定位1,预测定位2,停止定位3
#    define POS_Initial 0
#    define POS_Continue 1
#    define POS_Predict 2
#    define POS_Stop 3

#    define POSMode_Mark 0
#    define POSMode_Loam 1

#    define POSOUT_No 0
#    define POSOUT_Now 1
#    define POSOUT_Curr 2

typedef struct _ROBOTPOS
{
    Eigen::Vector3d t;
    uint32_t timeTamp;
    int flag;  // POS_Continue\POS_Initial\POS_Predict\POS_Stop 0 1 2 3
    int mode;  // POSMode_Mark\POSMode_Loam 0 1
    int meandev;
} ROBOTPOS;

#    define STRUCT_ROBOTPOS_LEN ((sizeof(ROBOTPOS) >> 1))

typedef struct _LOCALPOSE
{
    uint32_t timeTamp;           // 时钟时间 0-1h内的us
    Eigen::Vector3d local_t;     //最近帧位置xyz
    Eigen::Quaterniond local_q;  //位置四元数
    Eigen::Vector3d incre_t;     //最近帧位置增量，即线速度
    Eigen::Quaterniond incre_q;  //最近帧角度增量，即角速度
} LOCALPOSE;
#    define STRUCT_LOCALPOSE_LEN ((sizeof(LOCALPOSE) >> 1))

// long long     8个字节，64位，取值范围 -2^63 ～ 2^63-1
// unsigned long 4个字节，32位，取值范围 -2^32 ～ 2^32-1
uint32_t rosTime2HourUSec(ros::Time p_time);  // long long 8个字节，64位，取值范围 -2^63 ～ 2^63-1

uint32_t getCurrHourUSec();

double secRound(double timeOffset);

long long gedCurrNSec();

void getDate(long long time);

void correctPoseIncre(double deltaT,
                      Eigen::Vector3d t_in,
                      Eigen::Quaterniond q_in,
                      Eigen::Vector3d& t_out,
                      Eigen::Quaterniond& q_out);

void updateProbPose(Eigen::Vector3d t_old,
                    Eigen::Quaterniond q_old,
                    Eigen::Vector3d t_incre,
                    Eigen::Quaterniond q_incre,
                    Eigen::Vector3d& t_new,
                    Eigen::Quaterniond& q_new);

void transformPoseStruct(int l_mode,
                         int l_flag,
                         uint32_t l_timeStamp,
                         Eigen::Vector3d& l_t,
                         Eigen::Quaterniond& l_q,
                         ROBOTPOS& l_out);
/**
 * l_localPose：输入（上一帧位姿/时间戳/增量），l_strutPoseout：输出 结构体未来预估位姿
 */
void predictPose(LOCALPOSE l_localPose,
                 uint32_t l_nowTime,
                 Eigen::Vector3d& l_prob_t,
                 Eigen::Quaterniond& l_prob_q);

/**
 * deltaT: 100ms的倍数
 */
void correctPoseIncre(double deltaT,
                      Eigen::Vector3d t_in,
                      Eigen::Quaterniond q_in,
                      Eigen::Vector3d& t_out,
                      Eigen::Quaterniond& q_out);

/**
 * 更新预测位姿
 */
void updateProbPose(Eigen::Vector3d t_old,
                    Eigen::Quaterniond q_old,
                    Eigen::Vector3d t_incre,
                    Eigen::Quaterniond q_incre,
                    Eigen::Vector3d& t_new,
                    Eigen::Quaterniond& q_new);

/**
 * 位姿 eigen 转 ROBOTPOS
 * ROBOTPOS.t.x() y()为位置，单位m，z()为方向0-360deg
 */
void transformPoseStruct(int l_mode,
                         int l_flag,
                         uint32_t l_timeStamp,
                         Eigen::Vector3d& l_t,
                         Eigen::Quaterniond& l_q,
                         ROBOTPOS& l_out);

/**
 * 针对double型time，对0.1进行四舍五入， 0.099 / 0.11 约等 0.1
 */
double secTimeRound(double timeOffset);

/**
 * 保证位姿增量为连续两帧之间，即100ms内
 * deltaT：100ms的倍数
 */
void correctIncLidarPose(double deltaT, Eigen::Vector3d& t_out, Eigen::Quaterniond& q_out);

/**
 * 两次全局位姿,求局部增量
 */
void computeIncLidarPose(Eigen::Vector3d l_now_t,
                         Eigen::Quaterniond l_now_q,
                         Eigen::Vector3d l_last_t,
                         Eigen::Quaterniond l_last_q,
                         double l_time_now,
                         double l_time_last,
                         Eigen::Vector3d& l_incre_t,
                         Eigen::Quaterniond& l_incre_q);

/**
 * 限制两帧之间的位姿变化
 * */
void limitIncLidarPose(Eigen::Vector3d& l_t, Eigen::Quaterniond& l_q);

double tPoseEigenDiff(Eigen::Vector3d p1, Eigen::Vector3d p2);

double tSumEigenToDist(Eigen::Vector3d p1);

#endif