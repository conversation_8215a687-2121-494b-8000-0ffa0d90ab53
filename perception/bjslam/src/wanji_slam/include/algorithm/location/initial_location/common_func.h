/*
 * @Description:
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2022-03-22 10:00:12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-03-22 11:06:02
 */
#pragma once

#include <Eigen/Dense>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pwd.h>
#include <queue>
#include <vector>

static const double M_2PI = 6.2831853071796;

// 1/180.0*M_PI
static const double D2R = 0.017453292519943;

// 1*180.0/M_PI
static const double R2D = 57.295779513082;

// 点到点的距离pcl
template <typename T1, typename T2> inline double calcDistP2P_point(T1& pcur, T2& ptra)
{
    return sqrt(pow(pcur.x - ptra.x, 2) + pow(pcur.y - ptra.y, 2) + pow(pcur.z - ptra.z, 2));
}

// 点到点的距离pcl
template <typename T1, typename T2> inline double calcXYDistP2P_point(T1& pcur, T2& ptra)
{
    return sqrt(pow(pcur.x - ptra.x, 2) + pow(pcur.y - ptra.y, 2));
}

// 清理点云的内存占用
template <typename T> inline void clearPointCloud(pcl::PointCloud<T>& pc)
{
    pcl::PointCloud<T>().swap(pc);
}

template <typename T> inline void clearQueue(std::queue<T>& msgQueue)
{
    std::queue<T>().swap(msgQueue);
}

double rad2deg(double radians)
{
    return radians * 180.0 / M_PI;
}

double deg2rad(double degrees)
{
    return degrees * M_PI / 180.0;
}

//返回弧度值
inline float _AngToRad(float Ang)
{
    return Ang / 180.0 * M_PI;
}
//返回角度值
inline float _RadToAng(float Rad)
{
    return Rad * 180.0 / M_PI;
}

//返回360度内的角度
inline void degI360(float& angle)
{
    while (angle > 360.0f)
        angle -= 360.0f;
    while (angle < 0.0f)
        angle += 360.0f;
}

//返回360度内的角度
inline float _AngIn360(float Ang)
{
    if (Ang >= 360.0)
    {
        Ang -= 360.0;
        return Ang;
    }
    else if (Ang < 0.0)
    {
        Ang += 360.0;
        return Ang;
    }
    else
        return Ang;
}
//返回2pi内的弧度
inline float _RadIn2PI(float Rad)
{
    if (Rad >= 2 * M_PI)
    {
        Rad -= 2 * M_PI;
        return Rad;
    }
    else if (Rad < 0.0)
    {
        Rad += 2 * M_PI;
        return Rad;
    }
    else
        return Rad;
}

//四元数转欧拉角，先绕Z轴旋转再绕新的y轴旋转，再绕新的x轴旋转。
template <typename T>
inline void toEulerAngle(const Eigen::Quaterniond& q, T& roll, T& pitch, T& yaw)
{
    // roll (x-axis rotation)
    double sinr_cosp = +2.0 * (q.w() * q.x() + q.y() * q.z());
    double cosr_cosp = +1.0 - 2.0 * (q.x() * q.x() + q.y() * q.y());
    roll = atan2(sinr_cosp, cosr_cosp);

    // pitch (y-axis rotation)
    double sinp = +2.0 * (q.w() * q.y() - q.z() * q.x());
    if (fabs(sinp) >= 1)
        pitch = copysign(M_PI / 2, sinp);  // use 90 degrees if out of range
    else
        pitch = asin(sinp);

    // yaw (z-axis rotation)
    double siny_cosp = +2.0 * (q.w() * q.z() + q.x() * q.y());
    double cosy_cosp = +1.0 - 2.0 * (q.y() * q.y() + q.z() * q.z());
    yaw = atan2(siny_cosp, cosy_cosp);
}

//欧拉角转四元数，先绕Z轴旋转再绕新的y轴旋转，再绕新的x轴旋转。
inline Eigen::Quaterniond RPY2Quat(Eigen::Vector3d RPY)
{
    const Eigen::AngleAxisd roll_angle(RPY(0), Eigen::Vector3d::UnitX());
    const Eigen::AngleAxisd pitch_angle(RPY(1), Eigen::Vector3d::UnitY());
    const Eigen::AngleAxisd yaw_angle(RPY(2), Eigen::Vector3d::UnitZ());
    return yaw_angle * pitch_angle * roll_angle;
}

inline Eigen::Vector3d quatToEuler(Eigen::Quaterniond& p_quat)
{
    Eigen::Vector3d l_vEuler = p_quat.matrix().eulerAngles(0, 1, 2);
    return l_vEuler;
}

Eigen::Quaterniond YAW2Quat(double Yaw)
{
    const Eigen::AngleAxisd roll_angle(0.0, Eigen::Vector3d::UnitX());
    const Eigen::AngleAxisd pitch_angle(0.0, Eigen::Vector3d::UnitY());
    const Eigen::AngleAxisd yaw_angle(Yaw, Eigen::Vector3d::UnitZ());
    return yaw_angle * pitch_angle * roll_angle;
}

double toYaw(const Eigen::Quaterniond& q)
{
    // yaw (z-axis rotation)
    double siny_cosp = +2.0 * (q.w() * q.z() + q.x() * q.y());
    double cosy_cosp = +1.0 - 2.0 * (q.y() * q.y() + q.z() * q.z());
    double l_yaw = atan2(siny_cosp, cosy_cosp);

    if (l_yaw > 2 * M_PI)
        printf("l_yaw >2PI");
    return l_yaw;
}

double toYawIn2PI(const Eigen::Quaterniond& q)
{
    // yaw (z-axis rotation)
    double siny_cosp = +2.0 * (q.w() * q.z() + q.x() * q.y());
    double cosy_cosp = +1.0 - 2.0 * (q.y() * q.y() + q.z() * q.z());
    double l_yaw = atan2(siny_cosp, cosy_cosp);

    if (l_yaw < 0.0)
        return l_yaw + 2 * M_PI;
    else if (l_yaw > 2 * M_PI)
        return l_yaw - 2 * M_PI;
    else
        return l_yaw;
}

//获取当前用户名
inline std::string getUserName()
{
    uid_t userid;
    struct passwd* pwd;
    userid = getuid();
    pwd = getpwuid(userid);
    return pwd->pw_name;
}

//获取键盘的触发
/**
 * @brief GetKeyFromTermino()
 * 非阻塞形式，获取键值
 */
inline int GetKeyEnterTermino(void)
{
    fd_set rfds;
    struct timeval tv;
    int retval;

    /* Watch stdin (fd 0) to see when it has input. */
    FD_ZERO(&rfds);
    FD_SET(0, &rfds);
    /* Wait up to five seconds. */
    tv.tv_sec = 0;
    tv.tv_usec = 0;

    retval = select(1, &rfds, NULL, NULL, &tv);
    /* Don't rely on the value of tv now! */

    if (retval == -1)
    {
        perror("select()");
        return 0;
    }
    else if (retval)
        return 1;
    /* FD_ISSET(0, &rfds) will be true. */
    else
        return 0;
    return 0;
}

//获取当前的系统时间
inline std::string getCurrentTimeStr()
{
    time_t t = time(NULL);
    char ch[64] = {0};
    char result[100] = {0};
    strftime(ch, sizeof(ch) - 1, "%Y_%m_%d_%H:%M", localtime(&t));  //%Y%m%d%H%M%S
    sprintf(result, "%s", ch);
    return std::string(result);
}

/**
 * p_bef为定位位姿时间戳，p_now为当前时刻时间戳，p_now 恒> p_bef
 * 经过整小时时，出现p_now < p_bef  p_now += 3600*1000000
 */
int getTimeOffset(uint32_t p_now, uint32_t p_bef)
{
    if (p_now < p_bef)
        p_now += 3600000000;
    return (p_now - p_bef);
}