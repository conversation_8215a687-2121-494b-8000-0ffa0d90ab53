/*
 * @Author: your name
 * @Date: 2021-04-21 17:00:17
 * @LastEditTime: 2022-03-23 19:13:20
 * @LastEditors: <PERSON><PERSON>
 * @Description: In User Settings Edit
 * @FilePath: /catkin_ws/src/wanji_amcl3d/src/amcl/main.cpp
 */

#include "../amcl.h"

#define MULTI_AMCL 1

namespace wanjilocal {

template <typename PointMap, typename PointScan>
GlobalAmcl<PointMap, PointScan>::GlobalAmcl() : c_bLoadMap_(false)
{
    c_pAmcl_.reset(new wanjilocal::Amcl2D<PointMap, PointScan>());
    c_pAmclLocal_.reset(new wanjilocal::Amcl2D<PointMap, PointScan>());
}

template <typename PointMap, typename PointScan> GlobalAmcl<PointMap, PointScan>::~GlobalAmcl() {}

template <typename PointMap, typename PointScan> void GlobalAmcl<PointMap, PointScan>::initPara()
{
    c_pAmcl_->setCloudHeightRange(-0.2, 0.2);
    c_pAmcl_->setMapType(MAP_DIMENSION::MAP_2D);
    c_pAmcl_->setOverlapThresh(0.45);
    c_pAmcl_->setGridmapResolution(1);
    c_pAmcl_->setScoreThresh(0.33);

#ifdef MULTI_AMCL
    c_pAmclLocal_->setCloudHeightRange(-0.2, 0.2);
    c_pAmclLocal_->setMapType(MAP_DIMENSION::MAP_2D);
    c_pAmclLocal_->setOverlapThresh(0.45);
    c_pAmclLocal_->setGridmapResolution(0.3);
    c_pAmclLocal_->setScoreThresh(0.9);
#endif
}

template <typename PointMap, typename PointScan>
void GlobalAmcl<PointMap, PointScan>::setCloudMap(const cloudMapPtr& p_pcCorner,
                                                  const cloudMapPtr& p_pcSurface,
                                                  const cloudMapPtr& p_pPath)
{
    c_pcMapMerge_.reset(new typename pcl::PointCloud<PointMap>);
    *c_pcMapMerge_ = *p_pcCorner + *p_pcSurface;
    c_pcPath_ = p_pPath;

    //生成全量栅格地图
    c_pAmcl_->setCloudMap(c_pcMapMerge_);

    //获取全量栅格地图
    c_pAmcl_->getGridMapInfo(c_gridMapEdge, c_totalGridInfo_);

    //降采样路径点
    downSamplePath(c_pcPath_, c_pcPathDS_);

    //将栅格地图分割为子地图
    divideGridMap(c_totalGridInfo_, c_gridMapEdge, c_vSubGridMap_);

    c_pAmcl_->sampleInitPoseFromPath(p_pPath, c_vInitPoses, 2, 0.5, 1.5, M_PI);

#pragma endregion 第二次amcl

#ifdef MULTI_AMCL

    c_pAmclLocal_->setCloudMap(c_pcMapMerge_);
    // c_pAmclLocal_->setCloudMap(l_pcSubMap);
    Grid3dInfo::Ptr l_totalGridInfo;  //栅格地图，全图

    c_pAmclLocal_->getGridMapInfo(c_gridMapEdge2_, l_totalGridInfo);
    //将栅格地图分割为子地图
    divideGridMap(l_totalGridInfo, c_gridMapEdge2_, c_vSubGridMap2_);

#endif

#pragma region

    c_bLoadMap_ = true;
    c_pcMapMerge_.reset();
    c_pcMapMerge_ = nullptr;
}

// 路径+前缀：/home/<USER>/catkin_ws/src/wanji_amcl/map/lowpark
//自己读取文件，调试用
template <typename PointMap, typename PointScan>
bool GlobalAmcl<PointMap, PointScan>::loadMap(const std::string& p_sMapFile)
{
    //输入地图，创建栅格概率地图
    cloudMapPtr l_pcPath(new pcl::PointCloud<PointMap>());

    cloudMapPtr l_pcMapSurf(new pcl::PointCloud<PointMap>());
    cloudMapPtr l_pcMapCorner(new pcl::PointCloud<PointMap>());
    cloudMapPtr l_pcMapMerge(new pcl::PointCloud<PointMap>());
    cloudMapPtr l_pcSubMap(new pcl::PointCloud<PointMap>());
    std::string l_path = p_sMapFile;  // ros::package::getPath("wanji_amcl") + c_para_.map_dir;

    if (pcl::io::loadPCDFile<PointMap>(l_path + "_2.pcd", *l_pcMapSurf) == -1
        || pcl::io::loadPCDFile<PointMap>(l_path + "_1.pcd", *l_pcMapCorner) == -1
        || pcl::io::loadPCDFile<PointMap>(l_path + "_3.pcd", *l_pcPath) == -1)
    {
        PCL_ERROR("Couldn't read file map.pcd\n");
        return false;
    }

    initPara();

    setCloudMap(l_pcMapCorner, l_pcMapSurf, l_pcPath);

    return true;
}

//降采样路径点
template <typename PointMap, typename PointScan>
void GlobalAmcl<PointMap, PointScan>::downSamplePath(
    typename pcl::PointCloud<PointMap>::Ptr p_cloudIn,
    typename pcl::PointCloud<PointMap>::Ptr& p_cloudOut)
{
    pcl::VoxelGrid<PointMap> l_voxel;
    l_voxel.setLeafSize(5, 5, 5);
    l_voxel.setInputCloud(p_cloudIn);
    p_cloudOut.reset(new pcl::PointCloud<PointMap>);
    l_voxel.filter(*p_cloudOut);
    c_kdtreePathDS.reset(new pcl::KdTreeFLANN<PointMap>);
    c_kdtreePathDS->setInputCloud(p_cloudOut);
}

// 合并多个子栅格地图
template <typename PointMap, typename PointScan>
void GlobalAmcl<PointMap, PointScan>::spliceSubGridMap(
    const std::vector<Grid3dInfo::Ptr>& p_vSubGridMap,
    const wj_slam::s_InitPose& p_pose,
    Grid3dInfo::Ptr& p_subGrid)
{
    if (c_pcPathDS_->size() != p_vSubGridMap.size())
    {
        // ROS_ERROR("sub gird map error!\n");
        p_subGrid->ceil.reset(new pcl::PointCloud<pcl::PointXYZI>());

        return;
    }

    std::vector<int> pointIndexs;
    std::vector<float> pointDists;
    PointMap l_pt(p_pose.x, p_pose.y, p_pose.z);
    c_kdtreePathDS->radiusSearch(l_pt, 30, pointIndexs, pointDists);

    pcl::PointCloud<pcl::PointXYZI>::Ptr l_ceilTmp(new pcl::PointCloud<pcl::PointXYZI>());
    p_subGrid->ceil.reset(new pcl::PointCloud<pcl::PointXYZI>());

    for (int i = 0; i < (int)pointIndexs.size(); ++i)
    {
        p_subGrid->sensor_dev = p_vSubGridMap[pointIndexs[i]]->sensor_dev;
        *(l_ceilTmp) += *(p_vSubGridMap[pointIndexs[i]]->ceil);
    }

    pcl::VoxelGrid<pcl::PointXYZI> l_voxel;
    l_voxel.setLeafSize(1, 1, 1);
    l_voxel.setInputCloud(l_ceilTmp);
    l_voxel.filter(*(p_subGrid->ceil));

    l_ceilTmp.reset();
}

template <typename PointMap, typename PointScan>
void GlobalAmcl<PointMap, PointScan>::divideGridMap(Grid3dInfo::Ptr p_gridMap,
                                                    const PointCloudInfoS& p_gridMapEdge,
                                                    std::vector<Grid3dInfo::Ptr>& p_vSubGridMap)
{
    pcl::KdTreeFLANN<pcl::PointXYZI>::Ptr l_kdtreeGridMap(new pcl::KdTreeFLANN<pcl::PointXYZI>);

    l_kdtreeGridMap->setInputCloud(p_gridMap->ceil);

    std::vector<int> pointIndexs;
    std::vector<float> pointDists;
    pcl::PointXYZI posetmp;
    pcl::PointXYZI* pointtmp;

    for (uint32_t i = 0; i < c_pcPathDS_->size(); i++)
    {
        //路径点与左下角距离 / 分辨率 = 路径点所在栅格
        posetmp.x = (c_pcPathDS_->points[i].x - p_gridMapEdge.m_min_x) / p_gridMapEdge.m_resol;
        posetmp.y = (c_pcPathDS_->points[i].y - p_gridMapEdge.m_min_y) / p_gridMapEdge.m_resol;
        // posetmp.z = (c_pcPathDS_->points[i].z - p_gridMapEdge.m_min_z) /
        // p_gridMapEdge.m_resol;
        posetmp.z = 0;
        posetmp.intensity = 0;
        //半径搜索，10m/0.3分辨率，得到栅格下标及距离

        //路径点附近多少范围点 与 scanToMap 应该一致
        l_kdtreeGridMap->radiusSearch(
            posetmp, 30.0 / p_gridMapEdge.m_resol, pointIndexs, pointDists);
        Grid3dInfo::Ptr grid_info;
        grid_info.reset(new Grid3dInfo());
        grid_info->sensor_dev = p_gridMapEdge.m_resol;
        grid_info->ceil.reset(new pcl::PointCloud<pcl::PointXYZI>());
        grid_info->ceil->resize(pointIndexs.size());
        int cnt = 0;

        for (std::vector<int>::iterator it = pointIndexs.begin(); it != pointIndexs.end(); it++)
        {
            pointtmp = &p_gridMap->ceil->points[*it];
            grid_info->ceil->points[cnt] = *pointtmp;
            cnt++;
        }
        p_vSubGridMap.push_back(grid_info);
    }
}

template <typename PointMap, typename PointScan>
bool GlobalAmcl<PointMap, PointScan>::beginLocation(const cloudScanPtr& p_pcScan,
                                                    double& p_dTx,
                                                    double& p_dTy,
                                                    double& p_dTheta)
{
    printf("\n begin global localizaiton\n");
    TicToc l_timeGlobal;
    l_timeGlobal.tic();

    if (!c_bLoadMap_)
    {
        printf("not load map\n");
        return false;
    }

    c_pcFeatureLast_ = p_pcScan;

    //保存返回定位结果
    double l_dTx = -1;
    double l_dTy = -1;
    double l_dTheta = 0;
    double l_dScore = 0;
    bool l_bLocated = false;

    TicToc l_time;
    l_time.tic();

    for (size_t i = 0; i < c_vInitPoses.size(); i++)
    {
        // c_pAmcl_->setMultiInitPose(c_vInitPoses);
        c_pAmcl_->setInitialPose(c_vInitPoses[i].x,
                                 c_vInitPoses[i].y,
                                 c_vInitPoses[i].a,
                                 c_vInitPoses[i].devXY,
                                 c_vInitPoses[i].devA);

        c_pAmcl_->setProcessMode(PROCESS_MODE::PARTICLE_MODE);
        c_pAmcl_->setGlobalType(GLOBAL_METHOD::GLOBAL_ALL_ONCE);

        Grid3dInfo::Ptr l_subGrid(new Grid3dInfo());
        spliceSubGridMap(c_vSubGridMap_, c_vInitPoses[i], l_subGrid);
        c_pAmcl_->setGridMapInfo(c_gridMapEdge, l_subGrid);

        double l_dTxTmp = -1;
        double l_dTyTmp = -1;
        double l_dThetaTmp = 0;

        //是否需要根据定位条件调整评价标准
        bool l_bLocatedTmp =
            c_pAmcl_->amclCallBack(c_pcFeatureLast_, l_dTxTmp, l_dTyTmp, l_dThetaTmp);
        double l_dScoreTmp = c_pAmcl_->getScore();

        if (l_dScoreTmp > l_dScore)
        {
            l_dTx = l_dTxTmp;
            l_dTy = l_dTyTmp;
            l_dTheta = l_dThetaTmp;
            l_dScore = l_dScoreTmp;
            l_bLocated = l_bLocatedTmp;
        }
    }

    // std::string l_sRe = l_bLocated ? "success" : "failed";
    // std::cout << "\nGlobal AMCL result: " << l_sRe.c_str() << endl;

    double l_dTime = l_time.toc();
    printf("time of first amcl: %.3f\n", l_dTime);

    //第一层就严重失败，直接退出
    if (l_dScore < 0.1)
    {
        p_dTx = 0;
        p_dTy = 0;
        p_dTheta = 0;
        printf("First amcl failed.\n");
        return false;
    }

#pragma region 第二次amcl

#ifdef MULTI_AMCL

    printf("\n amcl Located result: x=%.3f; y=%.3f; a=%.3f; score=%.3f\n",
           l_dTx,
           l_dTy,
           l_dTheta,
           l_dScore);
    printf("Begin second amcl\n");
    l_time.tic();

    TicToc l_timeMap;
    l_timeMap.tic();

    Grid3dInfo::Ptr l_subGrid2(new Grid3dInfo());
    wj_slam::s_InitPose l_pose;
    l_pose.x = l_dTx;
    l_pose.y = l_dTy;
    l_pose.z = 0;

    spliceSubGridMap(c_vSubGridMap2_, l_pose, l_subGrid2);
    c_pAmcl_->setGridMapInfo(c_gridMapEdge2_, l_subGrid2);

    printf("second amcl: aplice grid map time cost, %.2f", l_timeMap.toc());

    c_pAmclLocal_->setInitialPose(l_dTx, l_dTy, l_dTheta, 0.5, 0.1);
    c_pAmclLocal_->setYawResolution(0.005);
    c_pAmclLocal_->setProcessMode(PROCESS_MODE::PARTICLE_MODE);
    c_pAmclLocal_->setGlobalType(GLOBAL_METHOD::GLOBAL_ALL_ONCE);

    //是否需要根据定位条件调整评价标准
    l_bLocated = c_pAmclLocal_->amclCallBack(c_pcFeatureLast_, l_dTx, l_dTy, l_dTheta);
    l_dScore = c_pAmclLocal_->getScore();

    std::string l_sRe = l_bLocated ? "success" : "failed";
    std::cout << "\nGlobal Amcl result: " << l_sRe.c_str() << endl;

    l_dTime = l_time.toc();
    printf("time of second amcl location: %.3f\n", l_dTime);
#endif

#pragma endregion

    printf("\nFinal Located result: x=%.3f; y=%.3f; a=%.3f; score=%.3f\n",
           l_dTx,
           l_dTy,
           l_dTheta,
           l_dScore);

    std::cout << "global amcl time cost: " << l_timeGlobal.toc() << std::endl;

    p_dTx = l_dTx;
    p_dTy = l_dTy;
    p_dTheta = l_dTheta;

    return l_bLocated;
}

template <typename PointMap, typename PointScan>
void GlobalAmcl<PointMap, PointScan>::getSubCloud(
    typename pcl::PointCloud<PointMap>::Ptr f_cloudIn,
    typename pcl::PointCloud<PointMap>::Ptr f_cloudOut,
    float f_min,
    float f_max)
{
    f_cloudOut->reserve(f_cloudIn->size());
    for (int i = 0; i < (int)f_cloudIn->size(); ++i)
    {
        const float l_height = f_cloudIn->points[i].z;
        if (l_height > f_min && l_height < f_max)
        {
            f_cloudOut->push_back(f_cloudIn->points[i]);
        }
    }

    f_cloudOut->points.shrink_to_fit();
    f_cloudOut->width = f_cloudOut->points.size();
}

template <typename PointMap, typename PointScan>
void GlobalAmcl<PointMap, PointScan>::getSubMap(
    const typename pcl::PointCloud<PointMap>::Ptr p_wholeMap,
    const PointMap& f_pt,
    float p_radious,
    typename pcl::PointCloud<PointMap>::Ptr p_subMap)
{
    typename pcl::KdTreeFLANN<PointMap>::Ptr kdtreeFeatureMap(new pcl::KdTreeFLANN<PointMap>);
    std::vector<int> indexs;
    std::vector<float> dists;
    PointMap* pointtmp;

    kdtreeFeatureMap->setInputCloud(p_wholeMap);

    kdtreeFeatureMap->radiusSearch(f_pt, p_radious, indexs, dists);

    int cnt = 0;

    p_subMap->resize(indexs.size());
    for (std::vector<int>::iterator it = indexs.begin(); it != indexs.end(); it++)
    {
        pointtmp = &p_wholeMap->points[*it];
        p_subMap->points[cnt] = *pointtmp;
        cnt++;
    }

    p_subMap->width = cnt;
}

template <typename PointMap, typename PointScan> void GlobalAmcl<PointMap, PointScan>::initValue()
{
}
}  // namespace wanjilocal
#define WJSLAM_GlobalAmcl(T, P) template class wanjilocal::GlobalAmcl<T, P>;