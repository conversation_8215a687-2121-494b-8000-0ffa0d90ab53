/*
 * @Author: your name
 * @Date: 2021-04-21 17:00:31
 * @LastEditTime: 2021-10-25 13:33:45
 * @LastEditors: zushuang
 * @Description: In User Settings Edit
 * @FilePath: /catkin_ws/src/wanji_amcl3d/include/amcl/PointCloudTools.h
 */
/*!
 * @file PointCloudTools.h
 * @copyright Copyright (c) 2019, FADA-CATEC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once

// #include <octomap/OcTree.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>

namespace wanjilocal {

class pathPointMapInfo {
  public:
    typedef boost::shared_ptr<pathPointMapInfo> Ptr; /*!< Grid information shared point */
    typedef boost::shared_ptr<const pathPointMapInfo> ConstPtr; /*!< Grid information shared point
                                                                   (const)*/
    pcl::PointCloud<pcl::PointXYZ>::Ptr ceil;                   /*3D栅格信息*/
};

class Grid3dCell {
  public:
    float dist{-1}; /*!< Distance */
    float prob{0};  /*!< Probability */
};
typedef struct PointCloudInfoS
{
    float m_min_x{0}; /*!< Minimum x-axis value of octomap */
    float m_min_y{0}; /*!< Minimum y-axis value of octomap */
    float m_min_z{0}; /*!< Minimum z-axis value of octomap */
    float m_max_x{0}; /*!< Maximum x-axis value of octomap */
    float m_max_y{0}; /*!< Maximum y-axis value of octomap */
    float m_max_z{0}; /*!< Maximum z-axis value of octomap */
    float m_resol{0}; /*!< Octomap Resolution */
} PointCloudInfoS;

class Grid3dInfo {
  public:
    typedef boost::shared_ptr<Grid3dInfo> Ptr;            /*!< Grid information shared point */
    typedef boost::shared_ptr<const Grid3dInfo> ConstPtr; /*!< Grid information shared point
                                                             (const)*/
    pcl::PointCloud<pcl::PointXYZI>::Ptr ceil;            /*3D栅格信息*/
    // PointCloudInfoS                             pcInfo;        /*地图边界信息*/
    float sensor_dev{0}; /*!< 三维点云传感器的设计 */
};

class PointCloudInfo {
  public:
    typedef boost::shared_ptr<PointCloudInfo> Ptr; /*!< Point cloud information shared point */
    typedef boost::shared_ptr<const PointCloudInfo> ConstPtr; /*!< Point cloud information shared
                                                                 point (const)*/
    pcl::PointCloud<pcl::PointXYZ>::Ptr cloud;                /*!< Pcl point cloud */
    double octo_min_x{0}; /*!< Minimum x-axis value of octomap */
    double octo_min_y{0}; /*!< Minimum y-axis value of octomap */
    double octo_min_z{0}; /*!< Minimum z-axis value of octomap */
    double octo_max_x{0}; /*!< Maximum x-axis value of octomap */
    double octo_max_y{0}; /*!< Maximum y-axis value of octomap */
    double octo_max_z{0}; /*!< Maximum z-axis value of octomap */
    double octo_resol{0}; /*!< Octomap Resolution */
};

}  // namespace wanjilocal
