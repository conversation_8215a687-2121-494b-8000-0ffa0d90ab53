#pragma once
#include "../amcl.h"

#ifdef TEST
extern std::ofstream g_out_file;
#endif

namespace wanjilocal {
// template <typename T>
// void clearPointCloud(pcl::PointCloud<T> &pc)
// {
//     pcl::PointCloud<T>().swap(pc);
//     // std::vector<T, Eigen::aligned_allocator<T>>().swap(pc.points);
// }

template <typename PointMap, typename PointScan>
Amcl2D<PointMap, PointScan>::Amcl2D()
    : c_processMode_(PARTICLE_MODE), c_bLoadMap_(false), c_mapGrid3d_(), c_pfPF_(),
      c_fMinHeight_(1.8), c_fMaxHeight_(1.91), c_dSensorDev_(0.3), c_fXYDev_(0.1), c_fADev_(M_PI_2),
      c_fYawReso_(0.3), c_dMatchCore_(0), c_mapType_(MAP_2D), c_dLocationThresh_(1.1),
      c_globalType_(GLOBAL_ALL_ONCE)
{
    //当前帧定位
    c_qMapCurr_ = Eigen::Quaterniond::Identity();
    c_tMapCurr_ = Eigen::Vector3d(0, 0, 0);

    // 上一帧定位
    c_qMapLast_ = Eigen::Quaterniond::Identity();
    c_tMapLast_ = Eigen::Vector3d(0, 0, 0);

    c_pcMapMerge_ = nullptr;
}

template <typename PointMap, typename PointScan> Amcl2D<PointMap, PointScan>::~Amcl2D() {}

template <typename PointMap, typename PointScan>
void Amcl2D<PointMap, PointScan>::setCloudHeightRange(float p_fMin, float p_fMax)
{
    c_fMinHeight_ = p_fMin;
    c_fMaxHeight_ = p_fMax;
}

template <typename PointMap, typename PointScan>
bool Amcl2D<PointMap, PointScan>::setCloudMap(const cloudMapPtr& p_pcMap)
{
    TicToc l_time;
    l_time.tic();

    if (p_pcMap == nullptr || p_pcMap->empty())
        return false;

    initValue_();

    *c_pcMapMerge_ = *p_pcMap;
    filterByheight_<PointMap>(c_fMinHeight_, c_fMaxHeight_, c_pcMapMerge_);

    if (c_mapType_ == MAP_2D)
    {
        projectZcoor_<PointMap>(0, c_pcMapMerge_);
    }

#ifdef TEST
    g_out_file << l_time.toc();
#endif

    l_time.tic();

    c_bLoadMap_ = true;

    amclInitFunc_();

#ifdef TEST
    g_out_file << "," << l_time.toc() << std::endl << std::endl;
#endif

    printf("\033[1;32m---->\033[0m Start Wanji_Amcl.\n");
    return true;
}

template <typename PointMap, typename PointScan>
void Amcl2D<PointMap, PointScan>::setInitialPose(double p_dInitX,
                                                 double p_dInitY,
                                                 double p_dInitYarm,
                                                 const float p_fXYdev,
                                                 const float p_fAdev)
{
    c_tMapLast_ = Eigen::Vector3d(p_dInitX, p_dInitY, 0);
    c_qMapLast_ = YAW2Quat(p_dInitYarm);

    c_vInitPoses_.clear();

    wj_slam::s_InitPose l_tmpPose(p_dInitX, p_dInitY, 0.0f, p_dInitYarm, p_fXYdev, p_fAdev);
    c_vInitPoses_.push_back(l_tmpPose);

    // lin
    // c_vecPathPointInd_ = getNewPathInd_(c_tMapLast_);

    c_fXYDev_ = p_fXYdev;
    c_fADev_ = p_fAdev;
}

template <typename PointMap, typename PointScan>
void Amcl2D<PointMap, PointScan>::setMultiInitPose(
    const std::vector<wj_slam::s_InitPose>& p_vInitPoses)
{
    c_vInitPoses_ = p_vInitPoses;
}

template <typename PointMap, typename PointScan>
void Amcl2D<PointMap, PointScan>::setGlobalType(int p_iType)
{
    c_globalType_ = p_iType;
}

template <typename PointMap, typename PointScan>
void Amcl2D<PointMap, PointScan>::setYawResolution(float p_fYawReso)
{
    c_fYawReso_ = p_fYawReso;
}

template <typename PointMap, typename PointScan>
void Amcl2D<PointMap, PointScan>::sampleInitPoseFromPath(
    const cloudMapPtr& p_pcPath,
    std::vector<wj_slam::s_InitPose>& p_vInitPoses,
    float p_fSampleSize,
    float p_fDevXY,
    float p_fA,
    float p_fDevA)
{
    p_vInitPoses.clear();
    p_vInitPoses.shrink_to_fit();

    cloudMapPtr l_downSamplePath(new pcl::PointCloud<PointMap>());
    typename pcl::VoxelGrid<PointMap> l_pathVoxel;  //路径点素滤波
    l_pathVoxel.setLeafSize(p_fSampleSize, p_fSampleSize, p_fSampleSize);
    l_pathVoxel.setInputCloud(p_pcPath);
    l_pathVoxel.filter(*l_downSamplePath);

    p_vInitPoses.resize(l_downSamplePath->size());

    for (int i = 0; i < (int)l_downSamplePath->size(); ++i)
    {
        p_vInitPoses[i].x = l_downSamplePath->points[i].x;  //+ c_pfPF_.ranGaussian_(0, p_fDevXY);
                                                            //// 增加一定的误差，模拟测试
        p_vInitPoses[i].y = l_downSamplePath->points[i].y;  //+ c_pfPF_.ranGaussian_(0, p_fDevXY);
        p_vInitPoses[i].z = 0;                              //高程方向赋值零
        p_vInitPoses[i].devXY = p_fDevXY;
        p_vInitPoses[i].a = p_fA;
        p_vInitPoses[i].devA = p_fDevA;
    }

    l_downSamplePath.reset();
}

template <typename PointMap, typename PointScan>
bool Amcl2D<PointMap, PointScan>::amclCallBack(const cloudScanPtr& p_pcScan,
                                               double& p_dTx,
                                               double& p_dTy,
                                               double& p_dTheta)
{
    c_dMatchCore_ = 0;
    // lin_test
    *c_pcFeatureLast_ = *p_pcScan;  //点云硬拷贝

    filterByheight_<PointScan>(c_fMinHeight_, c_fMaxHeight_, c_pcFeatureLast_);

    if (c_mapType_ == MAP_DIMENSION::MAP_2D)
    {
        projectZcoor_<PointScan>(0, c_pcFeatureLast_);  // lin_test
    }

    if (GLOBAL_ALL_ONCE == c_globalType_)  //所有初始值一次性处理
    {
        std::cout << std::endl << "all init poses processing..." << std::endl;
        c_vInitPosesDoing_ = c_vInitPoses_;
        amclFunc_();
    }
    else
    {
        std::cout << "\n process one by one." << std::endl;
        s_Particle l_bestPose;
        l_bestPose.wp = -100;
        for (int i = 0; i < (int)c_vInitPoses_.size(); ++i)
        {
            c_vInitPosesDoing_.clear();
            c_vInitPosesDoing_.push_back(c_vInitPoses_[i]);

            amclFunc_();

            if (c_meanP_.wp > l_bestPose.wp)
            {
                l_bestPose = c_meanP_;
            }
        }

        c_meanP_ = l_bestPose;
    }

    p_dTx = c_meanP_.x;
    p_dTy = c_meanP_.y;
    p_dTheta = c_meanP_.a;

    c_dMatchCore_ = c_meanP_.wp;

    printf("Score Thresh: %.3f\n", c_dLocationThresh_);

    return c_dMatchCore_ > c_dLocationThresh_;
}

template <typename PointMap, typename PointScan>
bool Amcl2D<PointMap, PointScan>::setGridMapInfo(const PointCloudInfoS& pcInfo,
                                                 Grid3dInfo::Ptr p_pGrid3dInfo)
{
    if (p_pGrid3dInfo == nullptr || p_pGrid3dInfo->ceil == nullptr)
    {
        return false;
    }

    c_pfPF_.setPointEdgeInfo(pcInfo);         //设置边界
    c_pfPF_.updateSubGridMap(p_pGrid3dInfo);  // lin_note 传递栅格概率地图

    return true;
}

template <typename PointMap, typename PointScan>
void Amcl2D<PointMap, PointScan>::setOverlapThresh(float p_fOverlap)
{
    c_pfPF_.setOverlapThresh(p_fOverlap);
}

template <typename PointMap, typename PointScan>
void Amcl2D<PointMap, PointScan>::setScoreThresh(double p_dScore)
{
    c_dLocationThresh_ = p_dScore;
}

template <typename PointMap, typename PointScan>
void Amcl2D<PointMap, PointScan>::setGridmapResolution(double f_dGridReso)
{
    c_mapGrid3d_.setResolution(f_dGridReso);
    c_dSensorDev_ = f_dGridReso;

    // const float l_gauss_const1 = static_cast<float>(1. / (c_dSensorDev_ * sqrt(2 * M_PI)));
    // // 1 / (2*0.3*0.3)         = 1 / (0.3 * 0.6)
    // const float l_gauss_const2 = static_cast<float>(1. / (2. * c_dSensorDev_ * c_dSensorDev_));

    // float l_fThreshDis = 0.6 * f_dGridReso;
    // c_dLocationThresh_ = l_gauss_const1 * expf(-l_fThreshDis * l_fThreshDis * l_gauss_const2);
}

template <typename PointMap, typename PointScan>
void Amcl2D<PointMap, PointScan>::setProcessMode(const PROCESS_MODE& p_mode)
{
    c_processMode_ = p_mode;
}

template <typename PointMap, typename PointScan>
void Amcl2D<PointMap, PointScan>::setMapType(const MAP_DIMENSION& p_mapType)
{
    c_mapType_ = p_mapType;
}

template <typename PointMap, typename PointScan>
void Amcl2D<PointMap, PointScan>::getGridMapInfo(PointCloudInfoS& pcInfo,
                                                 Grid3dInfo::Ptr& p_pGrid3dInfo)
{
    pcInfo = c_mapGrid3d_.getPointEdgeInfo();
    p_pGrid3dInfo = c_mapGrid3d_.getGridInfo();
}

template <typename PointMap, typename PointScan> void Amcl2D<PointMap, PointScan>::initValue_()
{
    /*----------------------------------------当前帧角点面点初始化-------------------------------------------------------------*/
    c_pcFeatureLast_.reset(new pcl::PointCloud<PointScan>());
    c_cpFeatureLastDS_.reset(new pcl::PointCloud<PointScan>());
    /*----------------------------------------点云地图初始化-------------------------------------------------------------*/
    c_pcMapMerge_.reset(new pcl::PointCloud<PointMap>());

    c_subGridInfo_.reset(new Grid3dInfo());
    c_subGridInfo_->ceil.reset(new pcl::PointCloud<pcl::PointXYZI>());
}

//高程坐标投影为同一个值
template <typename PointMap, typename PointScan>
template <typename T>
void Amcl2D<PointMap, PointScan>::projectZcoor_(float p_fZ,
                                                typename pcl::PointCloud<T>::Ptr p_pcCloud)
{
    for (size_t i = 0; i < p_pcCloud->size(); i++)
    {
        (*p_pcCloud)[i].z = p_fZ;
    }
}
// TODO:
// FIXME:
//过滤高度范围之外的点
template <typename PointMap, typename PointScan>
template <typename T>
void Amcl2D<PointMap, PointScan>::filterByheight_(float p_fMinH,
                                                  float p_fMaxH,
                                                  typename pcl::PointCloud<T>::Ptr p_pcCloud)
{
    typename pcl::PointCloud<T>::Ptr l_pcTmpCloud(new pcl::PointCloud<T>());
    l_pcTmpCloud->reserve(p_pcCloud->size());

    for (int i = 0; i < (int)p_pcCloud->size(); ++i)
    {
        if (p_pcCloud->points[i].z > p_fMinH && p_pcCloud->points[i].z < p_fMaxH)
        {
            l_pcTmpCloud->push_back(p_pcCloud->points[i]);
        }
    }

    p_pcCloud->points = l_pcTmpCloud->points;
    p_pcCloud->points.shrink_to_fit();
    p_pcCloud->width = p_pcCloud->points.size();
    l_pcTmpCloud.reset();
}

template <typename PointMap, typename PointScan> bool Amcl2D<PointMap, PointScan>::amclInitFunc_()
{
    std::cout << ("\033[1;32m---->\033[0m Wait Amcl initialization ...") << std::endl;

    TicToc l_tAmclInitTime;
    l_tAmclInitTime.tic();

    c_mapGrid3d_.setMapType(c_mapType_);
    if (!c_mapGrid3d_.open(c_pcMapMerge_, c_dSensorDev_))  // parameters_.sensor_dev_
        return false;

    c_subGridInfo_ = c_mapGrid3d_.getGridInfo();
    c_pcMapMergeDS_ = c_mapGrid3d_.getMapDS();

    printf("grid size: %ld, resolution: %.2f\n",
           c_subGridInfo_->ceil->size(),
           c_subGridInfo_->sensor_dev);

    //得到下采样地图指针
    // c_pcMapMergeDS_ = c_mapGrid3d_.getMapDS(); //硬拷贝

    c_pfPF_.setPointEdgeInfo(c_mapGrid3d_.getPointEdgeInfo());  //设置边界

    c_pfPF_.updateSubGridMap(c_subGridInfo_);  // lin_note 传递栅格概率地图
    // m_bPingPang = true;

    printf("amcl init time: %f ms\n", l_tAmclInitTime.toc());
    return true;
}

template <typename PointMap, typename PointScan> void Amcl2D<PointMap, PointScan>::amclFunc_()
{
    // fillAmclPose_(c_tMapLast_, c_qMapLast_);
    fillMultiPose_();

    c_pfPF_.setMapType(c_mapType_);
    if (!c_pfPF_.isInitialized())
    {
        c_subMapBuf_.lock();
        c_pfPF_.initPf(c_vecAmclPose_);
        c_subMapBuf_.unlock();
    }

    pcl::VoxelGrid<PointScan> l_cloudVG;
    l_cloudVG.setLeafSize(c_dSensorDev_, c_dSensorDev_, c_dSensorDev_);
    l_cloudVG.setInputCloud(c_pcFeatureLast_);
    l_cloudVG.filter(*c_cpFeatureLastDS_);

    if (c_processMode_ == PARTICLE_MODE)
    {
        std::cout << ("\n localization type : particle filter\n");
        c_pfPF_.predict(c_fXYDev_, c_fXYDev_, c_fXYDev_, c_fYawReso_, c_cpFeatureLastDS_);
        c_meanP_ = c_pfPF_.getMean();
    }
    else if (c_processMode_ == CARTOGRAPHER_MODE)
        predictByCartoOptimize_();
    else
    {
        return;
    }

    if (c_meanP_.a > (2 * M_PI))
        c_meanP_.a -= 2 * M_PI;

    updateAmclPose_();  //传递pose至t_map_curr q_map_curr

    // outPutBadScore();

    c_pfPF_.resetEnableInit();
}

template <typename PointMap, typename PointScan>
void Amcl2D<PointMap, PointScan>::outPutBadScore()  //输出评分不准确的文件
{
    double l_yarmDeg = rad2deg(c_meanP_.a);
    //转换到-180~180区间
    if (l_yarmDeg > 180)
        l_yarmDeg -= 360;

    //保存异常值
    double l_scoreThresh = 1.1;
    static int counter = 0;
    if (true /* c_meanP_.wp < 0.01 || (std::abs(l_yarmDeg) > 5 && c_meanP_.wp > l_scoreThresh) || (std::abs(l_yarmDeg) < 5 && c_meanP_.wp < l_scoreThresh )*/)
    {
        std::stringstream l_ss;
        l_ss.precision(2);
        l_ss.setf(std::ios::fixed);
        counter++;

        l_ss << "scan";
        l_ss << counter;
        l_ss << "-A" << l_yarmDeg;
        l_ss << "-score" << c_meanP_.wp << ".pcd";

        std::string file_name = c_sTestOutDir + "badScore/" + l_ss.str();
        std::string file_name_s = c_sTestOutDir + "badScore/s_" + l_ss.str();

        cloudScanPtr l_transedCloud(new pcl::PointCloud<PointScan>());
        pcl::transformPointCloud(*c_pcFeatureLast_, *l_transedCloud, c_tMapCurr_, c_qMapCurr_);
        for (int i = 0; i < (int)l_transedCloud->size(); ++i)
        {
            pcl::PointXYZI l_pt;
            l_pt.x = l_transedCloud->points[i].x;
            l_pt.y = l_transedCloud->points[i].y;
            l_pt.z = l_transedCloud->points[i].z;
            c_pfPF_.calcGridValue(l_pt, l_pt);

            l_transedCloud->points[i].x = l_pt.x;
            l_transedCloud->points[i].y = l_pt.y;
            l_transedCloud->points[i].z = l_pt.z;
        }

        if (!l_transedCloud->empty())
        {
            pcl::io::savePCDFileBinary(file_name, *l_transedCloud);
            pcl::io::savePCDFileBinary(file_name_s, *c_pcFeatureLast_);
        }

        std::string l_subGridMapFile = c_sTestOutDir + "badScore/subGridMap.pcd";
        pcl::io::savePCDFileBinary(l_subGridMapFile, *(c_subGridInfo_->ceil));

        // ROS_WARN("A bad score matching saved!\n");
    }
}

template <typename PointMap, typename PointScan>
void Amcl2D<PointMap, PointScan>::predictByCartoOptimize_()
{
    if (c_vInitPosesDoing_.empty())
        return;

    s_Particle l_initPtc;
    l_initPtc.x = c_vInitPosesDoing_[0].x;
    l_initPtc.y = c_vInitPosesDoing_[0].y;
    l_initPtc.z = 0;
    l_initPtc.a = c_vInitPosesDoing_[0].a;

    l_initPtc.wp = c_pfPF_.computeWeight(c_cpFeatureLastDS_, l_initPtc, c_subGridInfo_);
    double l_dScore = c_pfPF_.optimizeCarto(l_initPtc, c_subGridInfo_, c_cpFeatureLastDS_);
    printf("carto optimize score: %.4f\n", l_dScore);
    c_dMatchCore_ = l_dScore;
    c_meanP_ = l_initPtc;
}

template <typename PointMap, typename PointScan>
void Amcl2D<PointMap, PointScan>::fillAmclPose_(Eigen::Vector3d p_t, Eigen::Quaterniond p_q)
{
    if (c_fADev_ > c_fYawReso_)
        c_fYawReso_ = c_fADev_ / 10.0f;

    c_vecAmclPose_.clear();
    uint32_t l_iRadSize = ceil(abs(2 * c_fADev_) / c_fYawReso_);  // 3.1415 / 0.3 = 10.4
    // TODO: 转角只考虑180度范围，是否会存在过小的问题？
    AMCLPOSE l_posetmp;
    l_posetmp.x = p_t.x();
    l_posetmp.y = p_t.y();
    l_posetmp.z = 0;

    double l_yaw = toYawIn2PI(p_q);
    l_yaw -= c_fADev_;
    // if(yaw < -M_PI)
    //     yaw += M_PI*2;

    for (uint32_t i = 0; i < l_iRadSize; i++)
    {
        l_posetmp.a = l_yaw + i * c_fYawReso_;  // parameters_.odom_a_mod_
        // if( posetmp.a > M_PI)
        //     posetmp.a -= M_PI*2;

        if (l_posetmp.a < 0)
            l_posetmp.a = M_PI * 2 + l_posetmp.a;
        else if (l_posetmp.a > M_PI * 2)
            l_posetmp.a -= M_PI * 2;

        c_vecAmclPose_.push_back(l_posetmp);
    }
}

template <typename PointMap, typename PointScan> void Amcl2D<PointMap, PointScan>::fillMultiPose_()
{
    c_vecAmclPose_.clear();

    for (int k = 0; k < (int)c_vInitPosesDoing_.size(); ++k)
    {
        c_fADev_ = c_vInitPosesDoing_[k].devA;
        c_fXYDev_ = c_vInitPosesDoing_[k].devXY;

        if (c_fYawReso_ > c_fADev_)
            c_fYawReso_ = c_fADev_ / 10.0f;

        uint32_t l_iRadSize = ceil(abs(2 * c_fADev_) / c_fYawReso_);  // 3.1415 / 0.3 = 10.4
        // TODO: 转角只考虑180度范围，是否会存在过小的问题？
        AMCLPOSE l_posetmp;
        l_posetmp.x = c_vInitPosesDoing_[k].x;
        l_posetmp.y = c_vInitPosesDoing_[k].y;
        l_posetmp.z = 0;

        double l_yaw = c_vInitPosesDoing_[k].a;
        l_yaw -= c_fADev_;
        // if(yaw < -M_PI)
        //     yaw += M_PI*2;

        for (uint32_t i = 0; i < l_iRadSize; i++)
        {
            l_posetmp.a = l_yaw + i * c_fYawReso_;  // parameters_.odom_a_mod_
            // if( posetmp.a > M_PI)
            //     posetmp.a -= M_PI*2;

            if (l_posetmp.a < 0)
                l_posetmp.a = M_PI * 2 + l_posetmp.a;
            else if (l_posetmp.a > M_PI * 2)
                l_posetmp.a -= M_PI * 2;

            c_vecAmclPose_.push_back(l_posetmp);
        }
    }
}

template <typename PointMap, typename PointScan> void Amcl2D<PointMap, PointScan>::updateAmclPose_()
{
    c_tMapCurr_.x() = c_meanP_.x;
    c_tMapCurr_.y() = c_meanP_.y;
    c_tMapCurr_.z() = 0.0;  // mean_p_.z

    c_qMapCurr_ = YAW2Quat(c_meanP_.a).normalized();
}

}  // namespace wanjilocal

#define WJSLAM_Amcl2D(T, P) template class wanjilocal::Amcl2D<T, P>;