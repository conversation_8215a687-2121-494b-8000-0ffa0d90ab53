/**
 * @file iVox_node.hpp
 * <AUTHOR>
 * @brief iVox Grid Node
 * @version 1.0
 * @date 2022-08-26
 */

#include <algorithm>
#include <cmath>
#include <list>
#include <pcl/common/centroid.h>
#include <vector>

#include "../include/tic_toc.h"
#include "eigen_types.h"
#include "hilbert.hpp"

namespace wj_slam {

/**
 * @brief 两点之间的平方距离
 *
 * @tparam <PointT>
 * @param pt1
 * @param pt2
 * @code
 *
 * @endcode
 * @return [double] \n
 * [squared distance of two pcl points]
 *
 */
template <typename PointT> inline double distance2(const PointT& pt1, const PointT& pt2)
{
    Eigen::Vector3f d = pt1.getVector3fMap() - pt2.getVector3fMap();
    return d.squaredNorm();
}

/**
 * @brief 将点云点转换为Eigen::Matrix<Type, Dim, 1>
 *
 * @tparam <Type>
 * @tparam <Dim>
 * @tparam <PointType>
 * @param p_pt
 * @code
 *
 * @endcode
 * @return [Eigen::Matrix<Type, Dim, 1>] \n
 * [convert from pcl point to eigen]
 *
 */
template <typename Type, int Dim, typename PointType>
inline Eigen::Matrix<Type, Dim, 1> ToEigen(const PointType& p_pt)
{
    return Eigen::Matrix<Type, Dim, 1>(p_pt.x, p_pt.y, p_pt.z);
}

/**
 * @brief 点云点的xyz转换为Eigen::Matrix<float, 3, 1>
 *
 * @tparam <>
 * @param p_pt
 * @code
 *
 * @endcode
 * @return [Eigen::Matrix<float, 3, 1>] \n
 * [details wirte here]
 *
 */
template <>
inline Eigen::Matrix<float, 3, 1> ToEigen<float, 3, pcl::PointXYZ>(const pcl::PointXYZ& p_pt)
{
    return p_pt.getVector3fMap();
}

/**
 * @brief
 *
 * @tparam <>
 * @param p_pt
 * @code
 *
 * @endcode
 * @return [Eigen::Matrix<float, 3, 1>] \n
 * [details wirte here]
 *
 */
template <>
inline Eigen::Matrix<float, 3, 1> ToEigen<float, 3, pcl::PointXYZI>(const pcl::PointXYZI& p_pt)
{
    return p_pt.getVector3fMap();
}

/**
 * @brief
 *
 * @tparam <>
 * @param p_pt
 * @code
 *
 * @endcode
 * @return [Eigen::Matrix<float, 3, 1>] \n
 * [details wirte here]
 *
 */
template <>
inline Eigen::Matrix<float, 3, 1>
ToEigen<float, 3, pcl::PointXYZINormal>(const pcl::PointXYZINormal& p_pt)
{
    return p_pt.getVector3fMap();
}

/**
 * @class
 * @brief 栅格
 *
 * @tparam <PointT>
 * @tparam <Dim>
 *
 */
template <typename PointT, int Dim = 3> class IVoxNode {
  public:
    // EIGEN_MAKE_ALIGNED_OPERATOR_NEW;
    using PointGrid = typename pcl::PointCloud<PointT>::Ptr;

    /***********************
     * An struct type.
     * 小栅格参数
     */
    struct s_StructInnerGridParam
    {
        int m_iInnerGridEdge;        /**< struct value 1 */
        int m_iGridMaxIndex;         /**< struct value 2 */
        int m_iGridMaxSize;          /**< struct value 3 */
        float m_fInnerResolu;        /**< struct value 4 */
        float m_fInv_InnerResolu;    /**< struct value 5 */
        float m_fOuterGridRel;       /**< struct value 6 */
        Eigen::Vector3i m_eDivb_mul; /**< struct value 7 */
    };

    enum SearchMode {
        GETMAPPOINT = 1,
        GETZREOPOINT,
    };

    struct DistPoint;
    /**
     * @brief Construct a new IVoxNode object
     *
     *
     */
    IVoxNode(const float& p_fOuterRes, const float& p_fInnerRes)
    {
        initGridParam(p_fOuterRes, p_fInnerRes);
        // c_vfrontPts_.reserve(40);
        c_cfrontLocal_ = new char[c_sInnerGrid.m_iGridMaxIndex];
        c_cbackLocal_ = new char[c_sInnerGrid.m_iGridMaxIndex];
        c_czeroLocal_ = new char[c_sInnerGrid.m_iGridMaxIndex];
        c_cotherLocal_ = new char[c_sInnerGrid.m_iGridMaxIndex];
        c_chighLocal_ = new char[c_sInnerGrid.m_iGridMaxIndex];
        c_cmatchLocal_ = new char[c_sInnerGrid.m_iGridMaxIndex];
        c_cconnerLocal_ = new char[c_sInnerGrid.m_iGridMaxIndex];

        memset(c_cfrontLocal_, 0, sizeof(char) * c_sInnerGrid.m_iGridMaxIndex);
        memset(c_cbackLocal_, 0, sizeof(char) * c_sInnerGrid.m_iGridMaxIndex);
        memset(c_czeroLocal_, 0, sizeof(char) * c_sInnerGrid.m_iGridMaxIndex);
        memset(c_cotherLocal_, 0, sizeof(char) * c_sInnerGrid.m_iGridMaxIndex);
        memset(c_chighLocal_, 0, sizeof(char) * c_sInnerGrid.m_iGridMaxIndex);
        memset(c_cmatchLocal_, 0, sizeof(char) * c_sInnerGrid.m_iGridMaxIndex);
        memset(c_cconnerLocal_, 0, sizeof(char) * c_sInnerGrid.m_iGridMaxIndex);
    }
    /**
     * @brief Construct a new IVoxNode object
     *
     * @param p_pt
     * @param p_fOuterRes
     *
     */
    IVoxNode(const PointT& p_pt, const float& p_fOuterRes, const float& p_fInnerRes);

    ~IVoxNode()
    {
        if (c_cfrontLocal_)
            delete[] c_cfrontLocal_;

        if (c_cbackLocal_)
            delete[] c_cbackLocal_;

        if (c_czeroLocal_)
            delete[] c_czeroLocal_;

        if (c_cotherLocal_)
            delete[] c_cotherLocal_;

        if (c_chighLocal_)
            delete[] c_chighLocal_;

        if (c_cmatchLocal_)
            delete[] c_cmatchLocal_;

        if (c_cconnerLocal_)
            delete[] c_cconnerLocal_;
    }

    /**
     * @brief 初始化内部小栅格的参数
     *
     * @tparam <PointT>
     * @tparam <Dim>
     * @param p_fOuterResolu
     *
     */
    void initGridParam(const float& p_fOuterResolu, const float& p_fInnerResolu);

    /**
     * @brief 获取当前点所在栅格的边界
     *
     * @param p_pt
     * @param p_fGridWidth
     *
     */
    void calcuGridEdge(const PointT& p_pt, const float& p_fGridWidth);

    /**
     * @brief 当前点云点所在的栅格已经存在，则直接将点保存至栅格内部
     *
     * @param p_pt
     * @param p_iIdx
     *
     */
    bool insertOldPoint(const PointT& p_pt, int p_iIdx);
    /**
     * @brief 当前点云点对应的栅格为新创建的，将点云保存至栅格内部
     *
     * @param p_pt
     *
     */
    bool insertNewPoint(const PointT& p_pt);

    /**
     * @brief 获取栅格内部是否存储有点云点
     *
     * @code
     *
     * @endcode
     * @return [true] \n
     * [栅格内部存在点云]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [栅格内部不存在点云]
     *
     */
    inline bool Empty() const;

    /**
     * @brief 获取当前栅格内部存储点云点的Vector大小
     *
     * @code
     *
     * @endcode
     * @return [std::size_t] \n
     * [details wirte here]
     *
     */
    inline std::size_t Size() const;

    /**
     * @brief 获取当前栅格中心的点云点
     *
     * @param p_ptCenter
     *
     */
    void getCentroidGrid(PointGrid p_ptCenter);

    /**
     * @brief 获取栅格内部随机一个点云点
     *
     * @param p_ptCenter
     *
     */
    void getRandomPoint(PointGrid p_ptCenter);

    /**
     * @brief 获取当前栅格内部包含点云点集的质心
     *
     * @param p_ptCenter
     *
     */
    void getBaryCenterGrid(PointGrid p_ptCenter);

    /**
     * @brief 采用KNN的搜索方式获取栅格点云点
     *
     * @param p_gridPoint
     * @param point
     * @param K
     * @param max_range
     * @code
     *
     * @endcode
     * @return [int] \n
     * [details wirte here]
     *
     */
    int KNNPointByCondition(std::vector<DistPoint>& p_gridPoint,
                            const PointT& point,
                            const int& K,
                            const double& max_range);

    /**
     * @brief 获取当前栅格内部所存储的点云点
     *
     * @param p_pt
     * @param p_gridPoint
     * @code
     *
     * @endcode
     * @return [int] \n
     * [details wirte here]
     *
     */
    int getGridPoint(const PointT& p_pt, PointGrid& p_gridPoint, int p_isearchModel);

    /**
     * @brief 获取当前栅格内部所有的点云
     *
     * @param p_pt
     * @param p_gridPoint
     * @code
     *
     * @endcode
     * @return [int] \n
     * [details wirte here]
     *
     */
    void getGridAllPoint(const PointT& p_pt, PointGrid& p_gridPoint, bool p_bMode);

    /**
     * @brief 获取当前栅格内部存储点云点的Vector地址
     *
     * @param p_lVPoint
     * @code
     *
     * @endcode
     * @return [int] \n
     * [details wirte here]
     *
     */
    int getPointFromGrid(std::list<typename std::vector<PointT>*>& p_lVPoint);

    /**
     * @brief 计算当前点云点所在Vector下标
     *
     * @param p_pt
     * @code
     *
     * @endcode
     * @return [char] \n
     * [details wirte here]
     *
     */
    int calcuGridIndex(const PointT& p_pt);

    /**
     * @brief 获取当前一个点云点的左边界值
     *
     * @param p_fPt
     * @param p_fRel
     * @code
     *
     * @endcode
     * @return [float] \n
     * [details wirte here]
     *
     */
    float getGridEdge(float p_fPt, float p_fRel);

    /**
     * @brief 清除旧栅格内部参数，用于下次新添加栅格
     *
     *
     */
    void clearIVoxNode();

    /**
     * @brief 判断当前点是否属于当前栅格
     *
     * @param p_pt
     * @param p_iIdx
     * @code
     *
     * @endcode
     * @return [true] \n
     * [details wirte here]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [details wirte here]
     *
     */
    bool checkGrid(const PointT& p_pt, int& p_iIdx);

    /**
     * @brief 计算当前点对应栅格内部的index
     *
     * @param p_iIdx
     * @param p_iM
     * @param p_IN
     *
     */
    void calcuPosition(int& p_iIdx, int& p_iM, int& p_IN);

    /**
     * @brief 清理标签为0的Vector
     *
     *
     */
    void clearZeroVector();

    /**
     * @brief 获取当前前后墙标签内部所存储的点云
     *
     * @param map_pt
     *
     */
    void getMapGrid(PointGrid& map_pt);

    /**
     * @brief 将点云添加至对比标签的vector中
     *
     * @param p_vPtVector
     * @param p_pt
     * @param p_cSign
     * @param p_im
     * @param p_in
     * @code
     *
     * @endcode
     * @return [true] \n
     * [添加成功]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [添加失败]
     *
     */
    bool addPointVector(std::vector<PointT>& p_vPtVector,
                        const PointT& p_pt,
                        char* p_cSign,
                        int p_im,
                        int p_in);

    /*********************************
     *  c_sInnerGrid.
     *  小栅格参数结构体
     */
    s_StructInnerGridParam c_sInnerGrid;

  private:
    // PointT c_centerPoint_;
    // float c_fsideLength_;
    // bool c_gridState_;
    /*********************************
     *  c_min_.
     */
    Eigen::Vector3f c_min_;
    /*********************************
     *  c_min_b_.
     */
    Eigen::Vector3f c_min_b_;
    /*********************************
     *  c_vfrontPts_.
     *  存储标签为-1的点云
     */
    std::vector<PointT> c_vfrontPts_;
    /*********************************
     *  c_vbackPts_.
     *  存储标签为1的点云
     */
    std::vector<PointT> c_vbackPts_;
    /*********************************
     *  c_vzeroPts_.
     *  存储标签为0的点云(点云初始标签为0)
     */
    std::vector<PointT> c_vzeroPts_;
    /*********************************
     *  c_votherPts_.
     *  储存标签为-2的点云(初始为0标签，后续没有被赋予任何标签)
     */
    std::vector<PointT> c_votherPts_;
    /*********************************
     *  c_vhighPts_.
     *  存储标签为2的点云(房顶、地面点云点)
     */
    std::vector<PointT> c_vhighPts_;
    /*********************************
     *  c_vmatchPts_.
     *  存储拟合平面未成功时，-1和1暂时被修改3标签的点云
     */
    std::vector<PointT> c_vmatchPts_;
    /*********************************
     *  c_vconnerPts_.
     *  存储标签为-3的角点，用于校验
     */
    std::vector<PointT> c_vconnerPts_;

    /*********************************
     *  c_cfrontLocal_.
     *  管理标签为-1的点在每个小栅格内部是否存在
     */
    char* c_cfrontLocal_;
    /*********************************
     *  c_cbackLocal_.
     *  管理标签为1的点在每个小栅格内部是否存在
     */
    char* c_cbackLocal_;
    /*********************************
     *  c_czeroLocal_.
     *  管理标签为0的点在每个小栅格内部是否存在
     */
    char* c_czeroLocal_;
    /*********************************
     *  c_cotherLocal_.
     *  管理标签为-2的点在每个小栅格内部是否存在
     */
    char* c_cotherLocal_;
    /*********************************
     *  c_chighLocal_.
     *  管理标签为2的点在每个小栅格内部是否存在
     */
    char* c_chighLocal_;
    /*********************************
     *  c_cmatchLocal_.
     *  管理标签为2的点在每个小栅格内部是否存在
     */
    char* c_cmatchLocal_;
    /*********************************
     *  c_cconnerLocal_.
     *  管理标签为-3的点在每个小栅格内部是否存在
     */
    char* c_cconnerLocal_;
};

template <typename PointT, int Dim> struct IVoxNode<PointT, Dim>::DistPoint
{
    double dist = 0;
    IVoxNode* node = nullptr;
    int idx = 0;

    DistPoint() = default;
    DistPoint(const double d, IVoxNode* n, const int i) : dist(d), node(n), idx(i) {}

    // PointT Get() { return node->GetPoint(idx); }

    inline bool operator()(const DistPoint& p1, const DistPoint& p2)
    {
        return p1.dist < p2.dist;
    }

    inline bool operator<(const DistPoint& rhs)
    {
        return dist < rhs.dist;
    }
};

template <typename PointT, int Dim>
IVoxNode<PointT, Dim>::IVoxNode(const PointT& p_pt,
                                const float& p_fOuterRes,
                                const float& p_fInnerRes)
{
    // c_gridState_ = false;
    initGridParam(p_fOuterRes, p_fInnerRes);

    c_min_[0] = getGridEdge(p_pt.x, p_fOuterRes);
    c_min_[1] = getGridEdge(p_pt.y, p_fOuterRes);
    c_min_[2] = getGridEdge(p_pt.z, p_fOuterRes);

    c_min_b_ = (c_min_ * c_sInnerGrid.m_fInv_InnerResolu).array();
}

template <typename PointT, int Dim>
void IVoxNode<PointT, Dim>::initGridParam(const float& p_fOuterResolu, const float& p_fInnerResolu)
{
    c_sInnerGrid.m_fInnerResolu = p_fInnerResolu;
    c_sInnerGrid.m_fOuterGridRel = p_fOuterResolu;
    c_sInnerGrid.m_iInnerGridEdge =
        std::ceil(c_sInnerGrid.m_fOuterGridRel / c_sInnerGrid.m_fInnerResolu);
    c_sInnerGrid.m_iGridMaxIndex = (std::pow(c_sInnerGrid.m_iInnerGridEdge, 3) / 8) + 1;
    c_sInnerGrid.m_iGridMaxSize = std::pow(c_sInnerGrid.m_iInnerGridEdge, 3);
    c_sInnerGrid.m_fInv_InnerResolu = 1 / c_sInnerGrid.m_fInnerResolu;
    c_sInnerGrid.m_eDivb_mul << 1, c_sInnerGrid.m_iInnerGridEdge,
        std::pow(c_sInnerGrid.m_iInnerGridEdge, 2);
}

template <typename PointT, int Dim>
void IVoxNode<PointT, Dim>::calcuGridEdge(const PointT& p_pt, const float& p_fGridWidth)
{
    c_min_[0] = getGridEdge(p_pt.x, p_fGridWidth);
    c_min_[1] = getGridEdge(p_pt.y, p_fGridWidth);
    c_min_[2] = getGridEdge(p_pt.z, p_fGridWidth);

    c_min_b_ = (c_min_ * c_sInnerGrid.m_fInv_InnerResolu).array();
}

template <typename PointT, int Dim>
bool IVoxNode<PointT, Dim>::insertOldPoint(const PointT& p_pt, int p_iIdx)
{
    int l_im = -1, l_in = -1;
    calcuPosition(p_iIdx, l_im, l_in);
    if (p_pt.v == -1)
    {
        return (addPointVector(c_vfrontPts_, p_pt, c_cfrontLocal_, l_im, l_in));
    }
    else if (p_pt.v == 1)
    {
        return (addPointVector(c_vbackPts_, p_pt, c_cbackLocal_, l_im, l_in));
    }
    else if (p_pt.v == 2)
    {
        return (addPointVector(c_vhighPts_, p_pt, c_chighLocal_, l_im, l_in));
    }
    else if (p_pt.v == 3)
    {
        return (addPointVector(c_vmatchPts_, p_pt, c_cmatchLocal_, l_im, l_in));
    }
    else if (p_pt.v == 0)
    {
        if (!(c_czeroLocal_[l_im] & (1 << l_in)))
        {
            c_czeroLocal_[l_im] |= (1 << l_in);
            c_vzeroPts_.emplace_back(p_pt);
            return true;
        }
    }
    else if (p_pt.v == -3)
    {
        if (!(c_cconnerLocal_[l_im] & (1 << l_in)))
        {
            c_cconnerLocal_[l_im] |= (1 << l_in);
            c_vconnerPts_.push_back(p_pt);
            return true;
        }
    }

    // else
    // {
    //     if (!(c_cotherLocal_[l_im] & (1 << l_in)))
    //     {
    //         c_cotherLocal_[l_im] |= (1 << l_in);
    //         c_votherPts_.push_back(p_pt);
    //     }
    // }
    return false;
}

template <typename PointT, int Dim> bool IVoxNode<PointT, Dim>::insertNewPoint(const PointT& p_pt)
{
    int l_im = -1, l_in = -1;
    int key = calcuGridIndex(p_pt);
    calcuPosition(key, l_im, l_in);
    if (p_pt.v == -1)
    {
        return (addPointVector(c_vfrontPts_, p_pt, c_cfrontLocal_, l_im, l_in));
    }
    else if (p_pt.v == 1)
    {
        return (addPointVector(c_vbackPts_, p_pt, c_cbackLocal_, l_im, l_in));
    }
    else if (p_pt.v == 2)
    {
        return (addPointVector(c_vhighPts_, p_pt, c_chighLocal_, l_im, l_in));
    }
    else if (p_pt.v == 3)
    {
        return (addPointVector(c_vmatchPts_, p_pt, c_cmatchLocal_, l_im, l_in));
    }
    else if (p_pt.v == 0)
    {
        if (!(c_czeroLocal_[l_im] & (1 << l_in)))
        {
            c_czeroLocal_[l_im] |= (1 << l_in);
            c_vzeroPts_.push_back(p_pt);
            return true;
        }
    }
    else if (p_pt.v == -3)
    {
        if (!(c_cconnerLocal_[l_im] & (1 << l_in)))
        {
            c_cconnerLocal_[l_im] |= (1 << l_in);
            c_vconnerPts_.push_back(p_pt);
            return true;
        }
    }

    // else
    // {
    //     if (!(c_cotherLocal_[l_im] & (1 << l_in)))
    //     {
    //         c_cotherLocal_[l_im] |= (1 << l_in);
    //         c_votherPts_.push_back(p_pt);
    //     }
    // }
    return false;
}

// template <typename PointT, int Dim>
// bool IVoxNode<PointT, Dim>::Empty() const
// {
//     return (c_vfrontPts_.empty() && c_vbackPts_.empty());
// }

template <typename PointT, int Dim> std::size_t IVoxNode<PointT, Dim>::Size() const
{
    return c_vfrontPts_.size();
}

template <typename PointT, int Dim>
void IVoxNode<PointT, Dim>::getCentroidGrid(PointGrid p_ptCenter)
{
    PointT centroid;
    centroid.getVector4fMap() = Eigen::Vector4f((c_min_[0] + c_sInnerGrid.m_fOuterGridRel / 2),
                                                (c_min_[1] + c_sInnerGrid.m_fOuterGridRel / 2),
                                                (c_min_[2] + c_sInnerGrid.m_fOuterGridRel / 2),
                                                0.0);
    p_ptCenter->points.emplace_back(centroid);
}

template <typename PointT, int Dim> void IVoxNode<PointT, Dim>::getRandomPoint(PointGrid p_ptCenter)
{
    p_ptCenter->points.emplace_back(c_vfrontPts_.back());
}

template <typename PointT, int Dim>
void IVoxNode<PointT, Dim>::getBaryCenterGrid(PointGrid p_ptCenter)
{
    Eigen::Vector3f xyz(0.0, 0.0, 0.0);
    for (int i = 0; i < c_vfrontPts_.size(); i++)
    {
        xyz[0] += c_vfrontPts_[i].x;
        xyz[1] += c_vfrontPts_[i].y;
        xyz[2] += c_vfrontPts_[i].z;
    }
    PointT baryCenter;
    baryCenter.getVector4fMap() = Eigen::Vector4f((xyz[0] / c_vfrontPts_.size()),
                                                  (xyz[1] / c_vfrontPts_.size()),
                                                  (xyz[2] / c_vfrontPts_.size()),
                                                  0.0);
    p_ptCenter->points.emplace_back(baryCenter);
}

template <typename PointT, int Dim>
int IVoxNode<PointT, Dim>::KNNPointByCondition(std::vector<DistPoint>& p_gridPoint,
                                               const PointT& point,
                                               const int& K,
                                               const double& max_range)
{
    std::size_t old_size = p_gridPoint.size();
// #define INNER_TIMER
#ifdef INNER_TIMER
    static std::unordered_map<std::string, std::vector<int64_t>> stats;
    if (stats.empty())
    {
        stats["dis"] = std::vector<int64_t>();
        stats["put"] = std::vector<int64_t>();
        stats["nth"] = std::vector<int64_t>();
    }
#endif
    for (const auto& p_pt : c_vfrontPts_)
    {
#ifdef INNER_TIMER
        auto t0 = std::chrono::high_resolution_clock::now();
#endif
        double d = distance2(p_pt, point);
#ifdef INNER_TIMER
        auto t1 = std::chrono::high_resolution_clock::now();
#endif
        if (d < max_range * max_range)
        {
            p_gridPoint.template emplace_back(DistPoint(d, this, &p_pt - c_vfrontPts_.data()));
        }
#ifdef INNER_TIMER
        auto t2 = std::chrono::high_resolution_clock::now();

        auto dis = std::chrono::duration_cast<std::chrono::nanoseconds>(t1 - t0).count();
        stats["dis"].emplace_back(dis);
        auto put = std::chrono::duration_cast<std::chrono::nanoseconds>(t2 - t1).count();
        stats["put"].emplace_back(put);
#endif
    }

#ifdef INNER_TIMER
    auto t1 = std::chrono::high_resolution_clock::now();
#endif
    // sort by distance
    if (old_size + K >= p_gridPoint.size()) {}
    else
    {
        std::nth_element(p_gridPoint.begin() + old_size,
                         p_gridPoint.begin() + old_size + K - 1,
                         p_gridPoint.end());
        p_gridPoint.resize(old_size + K);
    }

#ifdef INNER_TIMER
    auto t2 = std::chrono::high_resolution_clock::now();
    auto nth = std::chrono::duration_cast<std::chrono::nanoseconds>(t2 - t1).count();
    stats["nth"].emplace_back(nth);

    constexpr int STAT_PERIOD = 100000;
    if (!stats["nth"].empty() && stats["nth"].size() % STAT_PERIOD == 0)
    {
        for (auto& it : stats)
        {
            const std::string& key = it.first;
            std::vector<int64_t>& stat = it.second;
            int64_t sum_ = std::accumulate(stat.begin(), stat.end(), 0);
            int64_t num_ = stat.size();
            stat.clear();
            std::cout << "inner_" << key << "(ns): sum=" << sum_ << " num=" << num_
                      << " ave=" << 1.0 * sum_ / num_ << " ave*n=" << 1.0 * sum_ / STAT_PERIOD
                      << std::endl;
        }
    }
#endif
    return p_gridPoint.size();
}

template <typename PointT, int Dim>
int IVoxNode<PointT, Dim>::getGridPoint(const PointT& p_pt,
                                        PointGrid& p_gridPoint,
                                        int p_isearchModel)
{
    if (p_isearchModel == SearchMode::GETMAPPOINT)
    {
        if (p_pt.v == 0)
        {
            if (c_vfrontPts_.size() >= c_vbackPts_.size())
            {
                for (const auto& pt : c_vfrontPts_)
                {
                    p_gridPoint->push_back(pt);
                }
            }
            else
            {
                for (const auto& pt : c_vbackPts_)
                {
                    p_gridPoint->push_back(pt);
                }
            }
        }
        else if (p_pt.v == 2)
        {
            for (const auto& pt : c_vhighPts_)
            {
                p_gridPoint->push_back(pt);
            }
        }
    }
    else if (p_isearchModel == SearchMode::GETZREOPOINT)
    {
        for (const auto& pt : c_vzeroPts_)
        {
            p_gridPoint->push_back(pt);
        }
    }

    return p_gridPoint->size();
}

template <typename PointT, int Dim>
void IVoxNode<PointT, Dim>::getGridAllPoint(const PointT& p_pt,
                                            PointGrid& p_gridPoint,
                                            bool p_bMode)
{
    if (p_bMode)
    {
        if (p_pt.v == -1)
        {
            if (!c_vfrontPts_.empty())
            {
                for (const auto& pt : c_vfrontPts_)
                {
                    p_gridPoint->push_back(pt);
                }
            }
            if (!c_vconnerPts_.empty())
            {
                for (const auto& pt : c_vconnerPts_)
                {
                    p_gridPoint->push_back(pt);
                }
            }
        }
        else if (p_pt.v == 1)
        {
            if (!c_vbackPts_.empty())
            {
                for (const auto& pt : c_vbackPts_)
                {
                    p_gridPoint->push_back(pt);
                }
            }
            if (!c_vconnerPts_.empty())
            {
                for (const auto& pt : c_vconnerPts_)
                {
                    p_gridPoint->push_back(pt);
                }
            }
        }
        else if (p_pt.v == -3)
        {
            if (!c_vconnerPts_.empty())
            {
                for (const auto& pt : c_vconnerPts_)
                {
                    p_gridPoint->push_back(pt);
                }
            }
        }
    }
    else
    {
        if (p_pt.v == -3)
        {
            if (!c_vconnerPts_.empty())
            {
                for (const auto& pt : c_vconnerPts_)
                {
                    p_gridPoint->push_back(pt);
                }
            }
        }
        else
        {
            if (!c_vconnerPts_.empty())
            {
                for (const auto& pt : c_vconnerPts_)
                {
                    p_gridPoint->push_back(pt);
                }
            }
            if (!c_vhighPts_.empty())
            {
                for (const auto& pt : c_vhighPts_)
                {
                    p_gridPoint->push_back(pt);
                }
            }
        }
    }

    // return c_vfrontPts_.size();
}

template <typename PointT, int Dim>
int IVoxNode<PointT, Dim>::getPointFromGrid(std::list<typename std::vector<PointT>*>& p_lVPoint)
{
    p_lVPoint.emplace_back(&c_vfrontPts_);
    return c_vfrontPts_.size();
}

template <typename PointT, int Dim> int IVoxNode<PointT, Dim>::calcuGridIndex(const PointT& p_pt)
{
    int ijk0 =
        static_cast<int>(std::floor((p_pt.x * c_sInnerGrid.m_fInv_InnerResolu) - c_min_b_[0]));
    int ijk1 =
        static_cast<int>(std::floor((p_pt.y * c_sInnerGrid.m_fInv_InnerResolu) - c_min_b_[1]));
    int ijk2 =
        static_cast<int>(std::floor((p_pt.z * c_sInnerGrid.m_fInv_InnerResolu) - c_min_b_[2]));
    int idx = ijk0 * c_sInnerGrid.m_eDivb_mul[0] + ijk1 * c_sInnerGrid.m_eDivb_mul[1]
              + ijk2 * c_sInnerGrid.m_eDivb_mul[2];

    return idx;
}

template <typename PointT, int Dim>
float IVoxNode<PointT, Dim>::getGridEdge(float p_fPt, float p_fRel)
{
    float l_edge;
    if (p_fPt > 0)
    {
        if (p_fPt - (int)p_fPt > p_fRel)
            l_edge = (int)p_fPt + p_fRel;
        else
            l_edge = (int)p_fPt;
    }
    else
    {
        if (p_fPt - (int)p_fPt > -p_fRel)
            l_edge = (int)p_fPt + (-p_fRel);
        else
            l_edge = (int)p_fPt + 2 * (-p_fRel);
    }
    return l_edge;
}

template <typename PointT, int Dim> void IVoxNode<PointT, Dim>::clearIVoxNode()
{
    // c_gridState_ = true;
    c_vfrontPts_.clear();
    c_vbackPts_.clear();
    c_vzeroPts_.clear();
    c_votherPts_.clear();
    c_vhighPts_.clear();
    c_vmatchPts_.clear();
    c_vconnerPts_.clear();

    memset(c_cfrontLocal_, 0, sizeof(char) * c_sInnerGrid.m_iGridMaxIndex);
    memset(c_cbackLocal_, 0, sizeof(char) * c_sInnerGrid.m_iGridMaxIndex);
    memset(c_czeroLocal_, 0, sizeof(char) * c_sInnerGrid.m_iGridMaxIndex);
    memset(c_cotherLocal_, 0, sizeof(char) * c_sInnerGrid.m_iGridMaxIndex);
    memset(c_chighLocal_, 0, sizeof(char) * c_sInnerGrid.m_iGridMaxIndex);
    memset(c_cmatchLocal_, 0, sizeof(char) * c_sInnerGrid.m_iGridMaxIndex);
    memset(c_cconnerLocal_, 0, sizeof(char) * c_sInnerGrid.m_iGridMaxIndex);
}
template <typename PointT, int Dim>
bool IVoxNode<PointT, Dim>::checkGrid(const PointT& p_pt, int& p_iIdx)
{
    p_iIdx = calcuGridIndex(p_pt);
    if (p_iIdx < c_sInnerGrid.m_iGridMaxSize && p_iIdx >= 0)
        return true;

    return false;
}

template <typename PointT, int Dim>
void IVoxNode<PointT, Dim>::calcuPosition(int& p_iIdx, int& p_iM, int& p_IN)
{
    p_iM = p_iIdx / 8;
    p_IN = p_iIdx % 8;
}

template <typename PointT, int Dim> void IVoxNode<PointT, Dim>::clearZeroVector()
{
    c_vzeroPts_.clear();
}

template <typename PointT, int Dim> void IVoxNode<PointT, Dim>::getMapGrid(PointGrid& map_pt)
{
    if (!c_vfrontPts_.empty())
    {
        for (const auto& pt : c_vfrontPts_)
        {
            map_pt->push_back(pt);
        }
    }
    if (!c_vbackPts_.empty())
    {
        for (const auto& pt : c_vbackPts_)
        {
            map_pt->push_back(pt);
        }
    }
    if (!c_vhighPts_.empty())
    {
        for (const auto& pt : c_vhighPts_)
        {
            map_pt->push_back(pt);
        }
    }
    if (!c_vconnerPts_.empty())
    {
        for (const auto& pt : c_vconnerPts_)
        {
            map_pt->push_back(pt);
        }
    }
    // if (!c_vzeroPts_.empty())
    // {
    //     for (const auto &pt : c_vzeroPts_)
    //     {
    //         map_pt->emplace_back(pt);
    //     }
    // }
}

template <typename PointT, int Dim>
bool IVoxNode<PointT, Dim>::addPointVector(std::vector<PointT>& p_vPtVector,
                                           const PointT& p_pt,
                                           char* p_cSign,
                                           int p_im,
                                           int p_in)
{
    if (!(p_cSign[p_im] & (1 << p_in)))
    {
        p_cSign[p_im] |= (1 << p_in);
        p_vPtVector.emplace_back(p_pt);
        if (!(c_czeroLocal_[p_im] & (1 << p_in)))
        {
            c_czeroLocal_[p_im] |= (1 << p_in);
            c_vzeroPts_.emplace_back(p_pt);
        }
        return true;
    }
    return false;
}

}  // namespace wj_slam
