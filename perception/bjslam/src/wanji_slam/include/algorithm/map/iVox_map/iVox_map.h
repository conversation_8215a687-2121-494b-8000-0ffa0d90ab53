/**
 * @file ivox3d.h
 * <AUTHOR>
 * @brief Generate a grid map from a point cloud map
 * @version 1.0
 * @date 2022-08-15
 * @copyright Copyright (c)2022 Vanjee
 */

#ifndef WJ_SLAM_IVOX3D_H
#define WJ_SLAM_IVOX3D_H

// #include <execution>
// #include <glog/logging.h>
#include <list>
#include <thread>

#include "eigen_types.h"
#include "hilbert.hpp"
#include "iVox_node.hpp"

namespace wj_slam {

/**
 * @class
 * @brief 栅格地图
 *
 * @tparam <Dim>
 * @tparam <PointType>
 *
 */
template <int Dim, typename PointType> class IVox {
  public:
    using KeyType = Eigen::Matrix<int, Dim, 1>;
    using PtType = Eigen::Matrix<float, Dim, 1>;
    // using NodeType = typename IVoxNodeTypeTraits<PointType, Dim>::NodeType;
    using NodeType = IVoxNode<PointType, Dim>;
    // using NodeType = typename NodeTypeTraits<PointType, Dim>;
    using PointVector = std::vector<PointType, Eigen::aligned_allocator<PointType>>;
    using PointGrid = typename pcl::PointCloud<PointType>::Ptr;
    using GridItera = typename std::list<std::pair<KeyType, NodeType>>::iterator;

    /********************
     * An enum type
     */
    enum NearbyType {
        CENTER = 0, /**< enum value 1 */
        NEARBY6,    /**< enum value 2 */
        NEARBY18,   /**< enum value 3 */
        NEARBY26,   /**< enum value 4 */
    };

    enum MapModel {
        INITBUILDMAP = 1, /**< enum value 1 */
        CONTBUILDMAP,     /**< enum value 2 */
        LOCATMAP,         /**< enum value 3 */
        UPDATEMAP,        /**< enum value 4 */
    };

    /********************
     * An struct type
     * 栅格参数
     */
    struct s_StructOuterGridParam
    {
        float m_fInnerResolu;             /**< struct value 1 */
        float m_fOuterResolu;             /**< struct value 2 */
        float m_fInv_OuterResolu;         /**< struct value 3 */
        int m_iGridNearby_Type;           /**< struct value 4 */
        std::size_t m_iCapacity = 100000; /**< struct value 5 */
        int m_iMatchPoints;               /**< struct value 6 */
        int m_iMinMatchPoints = 10;       /**< struct value 7 */
    };

    /**
     * @brief Construct a new IVox object
     *
     * @param p_gridParam
     *
     */
    IVox();
    ~IVox();
    /**
     * @brief 切换工作模式的时候，iVox内部需要重置的参数
     *
     *
     */
    void start();
    /**
     * @brief 初始化栅格内部状态
     *
     *
     */
    void gridInit();
    /**
     * @brief 实现每次建立新的栅格地图
     *
     * @param p_pointToAdd
     * @param p_iMode
     *
     */
    void buildGridMap(PointVector& p_surPoint, PointVector& p_corPoint, int p_iMode);

    /**
     * @brief 增量式连续建立栅格地图
     *
     * @param p_surPoint 面点
     * @param p_corPoint 角点
     * @param p_iMode
     *
     */
    void addGridMap(PointVector& p_surPoint, PointVector& p_corPoint, int p_iMode);

    /**
     * @brief 清理cache用于动态管理栅格
     *
     *
     */
    void clearCache();

    /**
     * @brief 获取栅格内部中心点
     *
     * @param p_pCentPt
     *
     */
    void getGridCentoidPoints(PointGrid p_pCentPt);

    /**
     * @brief 获取栅格内部随机一点
     *
     * @param p_pRandPt
     *
     */
    void getGridRandomPoints(PointGrid p_pRandPt);

    /**
     * @brief 获取栅格内部点云的质心
     *
     * @param p_pBaryPt
     *
     */
    void getGridBaryCenterPoints(PointGrid p_pBaryPt);

    /**
     * @brief 取出栅格内部相应数量的点云
     *
     * @param p_pt
     * @param closest_pt
     * @param max_num
     * @code
     *
     * @endcode
     * @return [true] \n
     * [取得栅格内部点云]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [未取得栅格内部点云]
     *
     */
    bool
    getClosestGridPoint(const PointType& p_pt, PointGrid& closest_pt, int max_num, int p_igetModel);

    /**
     * @brief 获取栅格内部点云时，优化取得的点云呈平面
     *
     * @param p_pt
     * @param closest_pt
     * @param max_num
     * @param p_bLabel
     * @code
     *
     * @endcode
     * @return [true] \n
     * [取得栅格内部点云]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [未取得栅格内部点云]
     *
     */
    bool getOptimizePoint(const PointType& p_pt, PointGrid& closest_pt, bool p_bLabel);

    /**
     * @brief 取出栅格内部相应数量的点云
     *
     * @param p_pt
     * @param p_lVPoint
     * @param max_num
     * @code
     *
     * @endcode
     * @return [true] \n
     * [取得栅格内部点云]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [未取得栅格内部点云]
     *
     */
    bool getClosestGridPoint(const PointType& p_pt,
                             std::list<typename std::vector<PointType>*>& p_lVPoint,
                             int max_num);

    /**
     * @brief 获取一个栅格内部存储的所有点云，用于显示
     *
     * @param p_pt
     * @param p_lVPoint
     * @code
     *
     * @endcode
     * @return [true] \n
     * [details wirte here]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [details wirte here]
     *
     */
    bool getGridAllPoint(const PointType& p_pt, PointGrid& closest_pt, bool p_bMode);

    /**
     * @brief
     *
     * @code
     *
     * @endcode
     * @return [std::vector<float>] \n
     * [details wirte here]
     *
     */
    std::vector<float> StatGridPoints() const;

    /**
     * @brief 清理标签为0的栅格
     *
     *
     */
    void clearGrid();

    /**
     * @brief 获取当前栅格所建立的地图(目前只取前后墙标签)
     *
     * @param map_pt
     *
     */
    void getiVoxMap(PointGrid& map_pt);

    /**
     * @brief 将重新赋予标签的点移动到对应的栅格标签内部
     *
     * @param p_pMovePt
     *
     */
    void movePoint(std::pair<KeyType, PointType>& p_pMovePt);

    /**
     * @brief 设置大栅格内部小栅格尺寸
     *
     * @param p_fInnerRes
     *
     */
    void setInnerResolu(float p_fInnerRes);
    /**
     * @brief 设置大栅格尺寸
     *
     * @param p_fOuterRes
     *
     */
    void setOuterResolu(float p_fOuterRes);
    /**
     * @brief 设置搜索点数
     *
     * @param p_iSearchNum
     *
     */
    void setSearchPointNum(int p_iSearchNum);
    /**
     * @brief 设置栅格容量大小
     *
     * @param p_iCapacity
     *
     */
    void setGridCapacity(std::size_t p_iCapacity);
    /**
     * @brief 设置栅格周围环绕类型
     *
     * @param p_iType
     *
     */
    void setGridNearbyType(int p_iType);

    /*********************************
     *  c_lNoLabel.
     *  存储初始标签为0，后续需要将其修改标签
     */
    std::list<std::pair<KeyType, PointType>> c_lNoLabel;
    /*********************************
     *  c_vNoSign.
     *  用于将初始标签为0，后续计算正确标签之后，将其原始点云中的标签修改为对应的标签
     */
    std::vector<std::pair<int, float>> c_vNoSign;
    /*********************************
     *  c_lAddZero.
     *  用于记录每次addGridMap新添加的点云点
     */
    std::list<KeyType> c_lAddZero;
    /*********************************
     *  c_iNum.
     */
    int c_iNum = -1;
    /*********************************
     *  c_vaddPoint.
     *  用于记录每次addGridMap新添加的点云点标签为-1或1的点云点，用于rviz显示
     */
    std::vector<PointType> c_vaddPoint;

  private:
    /**
     * @brief 初始化栅格相邻栅格数目
     *
     *
     */
    void generateNearbyGrids_();

    /**
     * @brief 将点云点转换为keyType
     *
     * @param p_pt
     * @code
     *
     * @endcode
     * @return [KeyType] \n
     * [details wirte here]
     *
     */
    KeyType Pos2Grid_(const PtType& p_pt) const;

    /**
     * @brief 清理栅格，作为下次新建
     *
     *
     */
    void clearForNewGrid_();

    /**
     * @brief 将面点、角点添加至栅格内部
     *
     * @param p_pointToAdd
     *
     */
    void addPointToGrid_(PointVector& p_pointToAdd, bool p_bCorInit);

    /**
     * @brief 新建一个栅格
     *
     * @param p_key
     * @param p_pt
     *
     */
    void creatNewGrid_(KeyType p_key, const PointType& p_pt);

    /*********************************
     *  c_sGridparam_.
     *  大栅格参数
     */
    s_StructOuterGridParam c_sGridparam_;
    /*********************************
     *  c_mGridmap_.
     *  栅格地图容器
     */
    std::unordered_map<KeyType,
                       typename std::list<std::pair<KeyType, NodeType*>>::iterator,
                       hash_vec<Dim>>
        c_mGridmap_;  // voxel hash map
    /*********************************
     *  c_lGridcache_.
     *  栅格地图长度容器
     */
    std::list<std::pair<KeyType, NodeType*>> c_lGridcache_;  // voxel cache
    /*********************************
     *  c_vNearbygrid_.
     *  栅格周围类型
     */
    std::vector<KeyType> c_vNearbygrid_;  // nearbys
    std::vector<KeyType> c_vVerify_;
    /*********************************
     *  c_hash_.
     */
    hash_vec<Dim> c_hash_;
    // std::vector<keyAndNodeType*> c_vGridAddr_;
    int c_iMode_;
};

}  // namespace wj_slam
#ifdef WJSLAM_NO_PRECOMPILE
#    include "impl/iVox_map.hpp"
#else
#    define WJSLAM_IVox(PointT, Dim) template class wj_slam::IVox<PointT, Dim>;
#endif
#endif
