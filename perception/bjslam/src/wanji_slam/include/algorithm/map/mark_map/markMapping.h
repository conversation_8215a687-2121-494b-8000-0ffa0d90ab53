#pragma once

// #include "common_ex.h"
#include "markType.h"
// 点云
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>

// 位姿
#include <Eigen/Dense>
#include <Eigen/Geometry>

namespace wj_slam {

class markMapping {
  private:
    // h = size s = shape v = time
    typedef pcl::PointXYZHSV mark;
    typedef pcl::PointCloud<mark> markCloud;
    typedef boost::shared_ptr<pcl::PointCloud<mark>> markCloudPtr;

    // debug 模式
    bool c_bDebug_;

    // 靶标转移到不同容器的判断条件
    typedef struct st_cfg
    {
        u_int m_nmaxLatestSecDiff;  // 最新靶标时间距离当前时间的限制
        u_int m_nmaxAvgSecDiff;     // 平均靶标时间距离当前时间的限制
        u_int m_nminNumToMaybe;     // 允许加入临时靶标地图的点数限制
        u_int m_nminNumToMap;       // 允许加入永久靶标地图的段数限制
    } st_cfg;
    // 靶标转移到不同容器的判断条件
    st_cfg c_stCfg_;

    typedef struct st_Pose
    {
        Eigen::Quaterniond m_quat;
        Eigen::Vector3d m_trans;
        float timeStamp;
    } st_Pose;

    // 新靶标和位姿
    markCloudPtr c_inputMark_;
    st_Pose c_inputPose_;
    // 废弃靶标点云
    markCloudPtr c_abnormMarkCloud_;
    // 临时靶标地图
    markCloudPtr c_tempMarkMap_;
    // 永久靶标地图
    markCloudPtr c_permtMarkMap_;

  public:
    /**
     * @brief: 默认构造函数，从默认位置加载参数
     * @param {*}
     * @return {*}
     */
    markMapping();
    markMapping(bool debug);

    /**
     * @description: 设置输入的新靶标列表,深拷贝
     * @param {Quaterniond} p_quat 位姿旋转量
     * @param {Vector3d} p_trans 位姿平移量
     * @param {double} p_fTimestamp 时间戳
     * @return {*}
     */
    void setInputMarkList(MARK_LIST& p_newMarkList,
                          Eigen::Quaterniond p_quat,
                          Eigen::Vector3d p_trans,
                          double p_fTimestamp);

    /**
     * @description: 更新临时靶标地图和永久靶标地图
     * @param {*}
     * @return 是否成功更新，不成功时无需更换地图
     */
    bool updateMarkMap();

    /**
     * @description: 获取临时靶标地图，用于定位
     * @param p_list 输入靶标序列的引用
     * @return {*}
     */
    void getTempMarkMapList(MARK_LIST& p_list);

    /**
     * @description: 获取永久靶标地图，用于储存
     * @param p_list 输入靶标序列的引用
     * @return {*}
     */
    void getPermtMarkMapList(MARK_LIST& p_list);

  private:
    void allocateMemery_();
    void setParamDefalut_();

    /**
     * @brief: 主函数，输入被拷贝
     * @param p_inputMark 传入当前扫描靶标
     * @param p_pose 传入当前里程计
     * @return 0-更新完成
     */
    bool renewMarkMap_(markCloudPtr p_inputMark, st_Pose& p_pose);

    /**
     * @brief: 转移输入靶标点云
     * @param p_inputMark 当前的点云
     * @param p_pose 当前里程计
     * @return 完成，失败
     */
    bool inputProcess_(markCloud& p_inputMark, st_Pose& p_pose);

    /**
     * @brief: 当前靶标点云反匹配地图并删除匹配上的靶标
     * @param p_inputMark 当前点云（转移后的）
     * @param p_permtMarkMap 反匹配对象
     * @param p_tempMarkCloud 临时靶标点云
     * @return 完成，失败
     */
    bool dematchMarkMap_(markCloudPtr p_inputMark, markCloudPtr p_permtMarkMap);

    /**
     * @brief:  分割临时靶标点云并抽取临时靶标和永久靶标
     * @param p_tempMarkCloud 临时靶标点云
     * @param p_fTimestamp 当前时间
     * @return 完成，失败
     */
    bool analysisTempMarkCloud_(markCloudPtr p_tempMarkCloud, float p_fTimestamp);

    /**
     * @description: 分析这个点云团，复制点云团中心到三种容器或者不转移
     * @param p_markCluster 当前分析的点云团
     * @param p_ftimeCurr 当前时间
     * @param p_abnormMark 转移到废弃点云
     * @param p_tempMarkMap 转移到临时地图
     * @param p_permtMarkMap 转移到永久地图
     * @return 是否被复制
     */
    bool shiftMarkCluster_(markCloud& p_markCluster,
                           float p_ftimeCurr,
                           markCloudPtr p_abnormMark,
                           markCloudPtr p_tempMarkMap,
                           markCloudPtr p_permtMarkMap);

    /**
     * @description: 获取点云中最新点时间
     * @param {markCloud} &p_tempMarkCloud
     * @return {*}
     */
    float getLatestMarkTime_(markCloud& p_markCloud);

    /**
     * @description: 获取点云中点的平均时间
     * @param {markCloud} &p_tempMarkCloud
     * @return {*}
     */
    float getAverageMarkTime_(markCloud& p_markCloud);

    /**
     * @description: 获取点云中点的重心
     * @note: 同时会平均点云的其他字段
     * @param {markCloud} &p_tempMarkCloud
     * @return {*}
     */
    mark getCenteroid_(markCloud& p_markCloud);

    void transformListToCloud_(markCloud& p_cloud, MARK_LIST& p_list);
    void transformCloudToList_(markCloud& p_cloud, MARK_LIST& p_list);
};
// end of class markMapping

}  // namespace wj_slam
// namespace wj_slam
