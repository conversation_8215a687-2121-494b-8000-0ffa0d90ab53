// local
#include "algorithm/map/sub_map/SubmapBox.h"

SubmapBox::SubmapBox()
    : c_bEnableLoop_(false), c_iContinuousIndThr_(1),
      c_fNearbyBoxRadius_(10), c_fScanBoxMaxRadius_(30)
{
    init_();
}

SubmapBox::~SubmapBox() {}

bool SubmapBox::update(const type::ConstPosePcPtr p_kfPose, const Pose& p_curPose)
{
    //! 首帧时上一次位姿为(0,0,0)，是否影响?（似无影响）
    Pose l_lasePose = c_curPose_;
    // 更新当前位姿
    c_curPose_ = p_curPose;
    // 填充子图框
    c_stSubmapBox_ = fillSubmapBox_(c_curPose_);
    // 子图关键帧索引集合、回环关键帧集合
    std::vector<int> l_vSubmapInds;
    std::vector<int> l_vLoopInds;
    // 清空子图及回环帧关键帧索引
    c_vSubmapKfInds_.clear();
    c_vLoopKfInds_.clear();
    // 直通滤波获取[子图框]关键帧索引集合及点云
    std::vector<int> l_vBoxInds;
    if (!straightFilter_(p_kfPose, c_stSubmapBox_, l_vBoxInds))
    {
        return false;
    }
    
    // 回环使能，以子图框历史帧作为回环帧候选索引
    if (c_bEnableLoop_)
    {
        // 获取子图框中[最新]且帧号连续的关键帧索引作为子图关键帧候选索引
        l_vSubmapInds = getLatestContinuousKfInds_(l_vBoxInds);
        // 框内索引与子图索引求差集，结果作为回环帧索引
        l_vLoopInds = vectorDifference(l_vBoxInds, l_vSubmapInds);
    }
    else
    {
        l_vSubmapInds = l_vBoxInds;
    }
    // 设置子图关键帧索引
    setSubmapKfInds_(l_vSubmapInds);
    // 设置回环帧索引
    setLoopKfInds_(l_vLoopInds);
    return true;
}

void SubmapBox::setScanRange(float p_fMinX, float p_fMinY, float p_fMaxX, float p_fMaxY)
{
    c_stScanBox_ = st_Box(p_fMinX, p_fMaxX, p_fMinY, p_fMaxY);
}

void SubmapBox::setEnableLoop(bool p_bFlag)
{
    c_bEnableLoop_ = p_bFlag;
}

std::vector<int> SubmapBox::getSubmapKfInds()
{
    std::lock_guard <std::mutex>l_lck(c_kfMutex_);
    return c_vSubmapKfInds_;
}

std::vector<int> SubmapBox::getLoopKfInds()
{
    std::lock_guard <std::mutex>l_lck(c_kfMutex_);
    return c_vLoopKfInds_;
}

void SubmapBox::setSubmapKfInds_(const std::vector<int>& p_vInds)
{
    std::lock_guard <std::mutex>l_lck(c_kfMutex_);
    c_vSubmapKfInds_ = p_vInds;
}

void SubmapBox::init_()
{
    wj_slam::SYSPARAM* l_stSysParam = wj_slam::SYSPARAM::getIn();
    c_fNearbyBoxRadius_ = l_stSysParam->m_map.m_fMapRange;
    c_fScanBoxMaxRadius_ = l_stSysParam->m_map.m_fMapMaxRange;
    c_curPose_ = Eigen::Vector3d::Zero();
}

void SubmapBox::setLoopKfInds_(const std::vector<int>& p_vInds)
{
    std::lock_guard <std::mutex>l_lck(c_kfMutex_);
    c_vLoopKfInds_ = p_vInds;
}

SubmapBox::st_Box SubmapBox::fillNearbyBox_(const Pose& p_currPose)
{
    st_Box l_stNearbyBox_;
    l_stNearbyBox_.m_fXMin = p_currPose[0] - c_fNearbyBoxRadius_;
    l_stNearbyBox_.m_fYMin = p_currPose[1] - c_fNearbyBoxRadius_;
    l_stNearbyBox_.m_fXMax = p_currPose[0] + c_fNearbyBoxRadius_;
    l_stNearbyBox_.m_fYMax = p_currPose[1] + c_fNearbyBoxRadius_;

    return l_stNearbyBox_;
}

SubmapBox::st_Box SubmapBox::fillScanBox_(const Pose& p_currPose)
{
    st_Box l_stScanBox_;
    // 约束雷达扫描最小距离
    l_stScanBox_.m_fXMax = (fabs(c_stScanBox_.m_fXMax) < 0.0001) ? (c_stScanBox_.m_fXMax + 0.01)
                                                                 : c_stScanBox_.m_fXMax;
    l_stScanBox_.m_fXMin = (fabs(c_stScanBox_.m_fXMin) < 0.0001) ? (c_stScanBox_.m_fXMin - 0.01)
                                                                 : c_stScanBox_.m_fXMin;
    l_stScanBox_.m_fYMax = (fabs(c_stScanBox_.m_fYMax) < 0.0001) ? (c_stScanBox_.m_fYMax + 0.01)
                                                                 : c_stScanBox_.m_fYMax;
    l_stScanBox_.m_fYMin = (fabs(c_stScanBox_.m_fYMin) < 0.0001) ? (c_stScanBox_.m_fYMin - 0.01)
                                                                 : c_stScanBox_.m_fYMin;

    // 约束雷达扫描最远距离
    if ((l_stScanBox_.m_fXMax - l_stScanBox_.m_fXMin) > 2 * c_fScanBoxMaxRadius_)
    {
        l_stScanBox_.m_fXMax = p_currPose[0] + c_fScanBoxMaxRadius_;
        l_stScanBox_.m_fXMin = p_currPose[0] - c_fScanBoxMaxRadius_;
    }
    if ((l_stScanBox_.m_fYMax - l_stScanBox_.m_fYMin) > 2 * c_fScanBoxMaxRadius_)
    {
        l_stScanBox_.m_fYMax = p_currPose[1] + c_fScanBoxMaxRadius_;
        l_stScanBox_.m_fYMin = p_currPose[1] - c_fScanBoxMaxRadius_;
    }

    return l_stScanBox_;
}

SubmapBox::st_Box SubmapBox::fillSubmapBox_(const Pose& p_currPose)
{
    // 根据回环是否使能决定使用附近框或扫描框作为子图框
    return c_bEnableLoop_ ? fillNearbyBox_(p_currPose) : fillScanBox_(p_currPose);
}

bool SubmapBox::straightFilter_(const type::ConstPosePcPtr p_pcKfPose,
                                   const st_Box& p_stBox,
                                   std::vector<int>& p_vBoxInds)
{
    for (std::size_t i = 0; i < p_pcKfPose->size(); i++)
    {
        if (isInBox(p_stBox, p_pcKfPose->at(i)))
        {
            p_vBoxInds.push_back(i);
        }
    }

    return !p_vBoxInds.empty();
}

std::vector<int> SubmapBox::getOldestContinuousKfInds_(std::vector<int> p_vKfInds)
{
    // sort(p_vKfInds.begin(), p_vKfInds.end());
    std::vector<int> v;
    int i = 0;
    for (i = p_vKfInds.size() - 2; i >= 0; i--)
    {
        if (p_vKfInds[i + 1] - p_vKfInds[i] > c_iContinuousIndThr_)
        {
            break;
        }
    }
    p_vKfInds.erase(p_vKfInds.begin() + i + 1, p_vKfInds.end());
    v.swap(p_vKfInds);
    return v;
}

std::vector<int> SubmapBox::getLatestContinuousKfInds_(std::vector<int> p_vKfInds)
{
    // sort(p_vKfInds.begin(), p_vKfInds.end());
    std::vector<int> v;
    for (int i = p_vKfInds.size() - 2; i >= 0; i--)
    {
        if (p_vKfInds[i + 1] - p_vKfInds[i] > c_iContinuousIndThr_)
        {
            p_vKfInds.erase(p_vKfInds.begin(), p_vKfInds.begin() + i + 1);
            break;
        }
    }
    v.swap(p_vKfInds);
    return v;
}

bool SubmapBox::isInBox(const st_Box& p_stBox, const type::POSE& p_pose)
{
    return (p_pose.x < p_stBox.m_fXMax && p_pose.x > p_stBox.m_fXMin && p_pose.y < p_stBox.m_fYMax
            && p_pose.y > p_stBox.m_fYMin);
}