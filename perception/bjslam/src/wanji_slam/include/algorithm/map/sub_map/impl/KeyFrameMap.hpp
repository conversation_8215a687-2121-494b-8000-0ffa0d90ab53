// local
#include "algorithm/map/sub_map/KeyFrameMap.h"

KeyFrameMap::KeyFrameMap() : c_bEnableIdModel_(true), c_uiEnableFeatrueDs_(0xFF)
{
    c_srcMap_.reset(new KeyFrame());
    wj_slam::SYSPARAM* l_stSysParam = wj_slam::SYSPARAM::getIn();
    if (l_stSysParam->m_map.m_iOptimizeModel == wj_slam::OptimizeMapType::KDTREE_TYPE)
    {
        // 注意：定位模式-子图内部暂时限制必须采样，否则影响KD搜点
        setFeatureDsEnabled(true, true, true, true);
    }
    else
    {
        setFeatureDsEnabled(true, false, true, true);
    }
}

KeyFrameMap::~KeyFrameMap() {}

void KeyFrameMap::generateKfMap(type::PosePcPtr p_pose,
                                type::ConstKeyFramePtr p_srcMap,
                                double p_dKfRange)
{
    if (p_srcMap == nullptr || p_pose == nullptr)
    {
        printf("Error: nullptr\n");
        return;
    }

    // 拷贝原始地图
    *c_srcMap_ = *p_srcMap;

    // 清空关键帧特征索引队列
    c_vKfCornIdInds_.clear();
    c_vKfSurfIdInds_.clear();
    c_vKfMarkIdInds_.clear();
    c_vKfCurbIdInds_.clear();

    std::lock_guard<std::mutex> lck(c_kfMutex_);
    // 拷贝位姿点云
    *c_pcKfPoses_ = *p_pose;
    // 清空关键帧队列
    c_vpairKFMaps_.clear();

    // 非ID模式(目前仅[连续建图]模式下进入)
    if (!c_bEnableIdModel_)
    {
        // 获取关键帧特征索引
        fillKfIndsFromWholeMap_(c_pcKfPoses_, p_srcMap, p_dKfRange);
        // 填充关键帧队列
        fillKfIndsToKFMaps_(p_srcMap);
    }
    else
    {
        // 位姿采样
        FilterFactory* l_filterFactor = FilterFactory::getInstance();
        l_filterFactor->filterKfPose<type::POSE>(c_pcKfPoses_, c_pcKfPoses_);
        // 获取关键帧特征索引
        fillKfIndsFromWholeMap_(c_pcKfPoses_, p_srcMap, p_dKfRange);
    }
}

void KeyFrameMap::getKfMaps(const std::vector<int>& p_vKfInds, type::KeyFramePtr& p_localMap)
{
    if (p_vKfInds.empty())
        return;

    if (c_bEnableIdModel_)
    {
        if (c_vKfSurfIdInds_.empty() || (c_vKfCornIdInds_.empty() && c_vKfMarkIdInds_.empty()))
        {
            return;
        }

        // ID模式下从关键帧索引中分别获取局部特征点云
        getCornPcByInds_(p_vKfInds, p_localMap->m_pFeature->first);
        getSurfPcByInds_(p_vKfInds, p_localMap->m_pFeature->second);
        getMarkPcByInds_(p_vKfInds, p_localMap->m_pFeature->third);
        getCurbPcByInds_(p_vKfInds, p_localMap->m_pFeature->fourth);
    }
    else
    {
        // 非ID模式下从关键帧队列中直接获取局部点云
        p_localMap = getKfMapByInds(p_vKfInds);
    }
}

bool KeyFrameMap::getIdModel()
{
    return c_bEnableIdModel_;
}

void KeyFrameMap::setIdModel(bool p_bFlag)
{
    c_bEnableIdModel_ = p_bFlag;
}

uint8_t KeyFrameMap::getFeatureDsEnabled()
{
    return c_uiEnableFeatrueDs_;
}

void KeyFrameMap::setFeatureDsEnabled(bool p_bCorn, bool p_bSurf, bool p_bMark, bool p_bCurb)
{
    c_uiEnableFeatrueDs_ =
        ((!!p_bCorn) | ((!!p_bSurf) << 1) | ((!!p_bMark) << 2) | ((!!p_bCurb) << 3));
}

void KeyFrameMap::filterKfMap(const type::ConstKeyFramePtr p_pairSubMap,
                              const type::KeyFramePtr p_pcOut)
{
    bool l_bCornDs = (c_uiEnableFeatrueDs_ & 0x01);
    bool l_bSurfDs = (c_uiEnableFeatrueDs_ & 0x02);
    bool l_bMarkDs = (c_uiEnableFeatrueDs_ & 0x04);
    bool l_bCurbDs = (c_uiEnableFeatrueDs_ & 0x08);

    FilterFactory* l_filterFactory = FilterFactory::getInstance();
    if (l_bCornDs)
    {
        l_filterFactory->filterCorner<type::PointMap>(p_pairSubMap->m_pFeature->first,
                                                      p_pcOut->m_pFeature->first);
    }
    else
    {
        *p_pcOut->m_pFeature->first = *p_pairSubMap->m_pFeature->first;
    }

    if (l_bSurfDs)
    {
        l_filterFactory->filterSurf<type::PointMap>(p_pairSubMap->m_pFeature->second,
                                                    p_pcOut->m_pFeature->second);
    }
    else
    {
        *p_pcOut->m_pFeature->second = *p_pairSubMap->m_pFeature->second;
    }

    if (l_bMarkDs)
    {
        l_filterFactory->filterMark<type::PointMap>(p_pairSubMap->m_pFeature->third,
                                                    p_pcOut->m_pFeature->third);
    }
    else
    {
        *p_pcOut->m_pFeature->third = *p_pairSubMap->m_pFeature->third;
    }

    if (l_bCurbDs)
    {
        l_filterFactory->filterCurb<type::PointMap>(p_pairSubMap->m_pFeature->fourth,
                                                    p_pcOut->m_pFeature->fourth);
    }
    else
    {
        *p_pcOut->m_pFeature->fourth = *p_pairSubMap->m_pFeature->fourth;
    }
}

void KeyFrameMap::fillKfIndsFromWholeMap_(const type::ConstPosePcPtr p_pcKfPose,
                                          const type::ConstKeyFramePtr p_srcMap,
                                          double p_dKfRange)
{
    pcl::KdTreeFLANN<type::PointMap> l_kdTreeCorn;
    pcl::KdTreeFLANN<type::PointMap> l_kdTreeSurf;
    pcl::KdTreeFLANN<type::PointMap> l_kdTreeMark;
    pcl::KdTreeFLANN<type::PointMap> l_kdTreeCurb;

    std::vector<int> l_vIndsCorn, l_vIndsSurf, l_vIndsMark, l_vIndsCurb;
    std::vector<float> l_vDistsCorn, l_vDistsSurf, l_vDistsMark, l_vDistsCurb;

    l_kdTreeCorn.setInputCloud(p_srcMap->m_pFeature->first);
    l_kdTreeSurf.setInputCloud(p_srcMap->m_pFeature->second);

    bool l_bEnableCurb = p_srcMap->m_pFeature->curbSize();
    bool l_bEnableMark = p_srcMap->m_pFeature->markSize();
    if (l_bEnableMark)
    {
        l_kdTreeMark.setInputCloud(p_srcMap->m_pFeature->third);
    }
    if (l_bEnableCurb)
    {
        l_kdTreeCurb.setInputCloud(p_srcMap->m_pFeature->fourth);
    }

    // 根据位姿点云，邻近搜索获取每个位姿点对应的特征点索引
    for (uint32_t i = 0; i < p_pcKfPose->size(); i++)
    {
        type::PointMap l_path;
        l_path.x = p_pcKfPose->at(i).x;
        l_path.y = p_pcKfPose->at(i).y;
        l_path.z = p_pcKfPose->at(i).z;
        l_kdTreeCorn.radiusSearch(l_path, p_dKfRange, l_vIndsCorn, l_vDistsCorn);
        l_kdTreeSurf.radiusSearch(l_path, p_dKfRange, l_vIndsSurf, l_vDistsSurf);

        if (l_bEnableMark)
        {
            l_kdTreeMark.radiusSearch(l_path, p_dKfRange, l_vIndsMark, l_vDistsMark);
        }
        else
        {
            l_vIndsMark.clear();
        }

        if (l_bEnableCurb)
        {
            l_kdTreeCurb.radiusSearch(l_path, p_dKfRange, l_vIndsCurb, l_vDistsCurb);
        }
        else
        {
            l_vIndsCurb.clear();
        }

        c_vKfCornIdInds_.push_back(l_vIndsCorn);
        c_vKfSurfIdInds_.push_back(l_vIndsSurf);
        c_vKfMarkIdInds_.push_back(l_vIndsMark);
        c_vKfCurbIdInds_.push_back(l_vIndsCurb);
    }
}

void KeyFrameMap::fillKfIndsToKFMaps_(const type::ConstKeyFramePtr p_srcMap)
{
    int l_kfCornSize = c_vKfCornIdInds_.size();
    int l_kfSurfSize = c_vKfSurfIdInds_.size();
    int l_kfMarkSize = c_vKfMarkIdInds_.size();
    int l_kfCurbSize = c_vKfCurbIdInds_.size();
    bool l_bSameSize =
        (l_kfCornSize + l_kfSurfSize + l_kfMarkSize + l_kfCurbSize) == 4 * l_kfCornSize;

    if (!l_bSameSize)
    {
        printf("Error: sMap[%d][%d][%d][%d]\n",
               l_kfCornSize,
               l_kfSurfSize,
               l_kfMarkSize,
               l_kfCurbSize);
        return;
    }

    // 根据关键帧特征索引生成对应关键帧点云
    std::vector<type::KeyFramePtr> l_vMaps;
    for (int i = 0; i < (int)c_vKfCornIdInds_.size(); i++)
    {
        type::KeyFramePtr l_pairKFInfo = boost::make_shared<KeyFrame>();
        pcl::copyPointCloud(*(p_srcMap->m_pFeature->first),
                            c_vKfCornIdInds_[i],
                            *(l_pairKFInfo->m_pFeature->first));
        pcl::copyPointCloud(*(p_srcMap->m_pFeature->second),
                            c_vKfSurfIdInds_[i],
                            *(l_pairKFInfo->m_pFeature->second));
        if (c_vKfMarkIdInds_[i].size())
        {
            pcl::copyPointCloud(*(p_srcMap->m_pFeature->third),
                                c_vKfMarkIdInds_[i],
                                *(l_pairKFInfo->m_pFeature->third));
        }

        if (c_vKfCurbIdInds_[i].size())
        {
            pcl::copyPointCloud(*(p_srcMap->m_pFeature->fourth),
                                c_vKfCurbIdInds_[i],
                                *(l_pairKFInfo->m_pFeature->fourth));
        }
        l_vMaps.push_back(l_pairKFInfo);
    }
    c_vpairKFMaps_.swap(l_vMaps);
}

void KeyFrameMap::getCornPcByInds_(const std::vector<int>& p_vKfInds, type::FeaturePcPtr p_pcCorn)
{
    bool l_bCornDs = c_bEnableIdModel_ ? true : (c_uiEnableFeatrueDs_ & 0x01);

    if (!l_bCornDs)
    {
        std::vector<int> l_vLocalCornIds;
        for (int k = 0; k < (int)p_vKfInds.size(); k++)
        {
            for (int n = 0; n < (int)c_vKfCornIdInds_[p_vKfInds[k]].size(); n++)
            {
                l_vLocalCornIds.push_back(c_vKfCornIdInds_[p_vKfInds[k]][n]);
            }
        }
        std::sort(l_vLocalCornIds.begin(), l_vLocalCornIds.end());
        pcl::copyPointCloud(*(c_srcMap_->m_pFeature->first), l_vLocalCornIds, *p_pcCorn);
    }
    else
    {
        int l_iCornSize = c_srcMap_->m_pFeature->cornerSize();
        std::vector<int> l_vCornTable(l_iCornSize, 0);
        int l_iCornCnt = 0;
        for (int k = 0; k < (int)p_vKfInds.size(); k++)
        {
            for (int n = 0; n < (int)c_vKfCornIdInds_[p_vKfInds[k]].size(); n++)
            {
                if (!l_vCornTable[c_vKfCornIdInds_[p_vKfInds[k]][n]])
                {
                    l_vCornTable[c_vKfCornIdInds_[p_vKfInds[k]][n]] = 1;
                    l_iCornCnt++;
                }
            }
        }
        p_pcCorn->points.resize(l_iCornCnt);
        p_pcCorn->header = c_srcMap_->m_pFeature->first->header;
        p_pcCorn->width = static_cast<std::uint32_t>(l_iCornCnt);
        p_pcCorn->height = 1;
        p_pcCorn->is_dense = c_srcMap_->m_pFeature->first->is_dense;
        p_pcCorn->sensor_orientation_ = c_srcMap_->m_pFeature->first->sensor_orientation_;
        p_pcCorn->sensor_origin_ = c_srcMap_->m_pFeature->first->sensor_origin_;
        for (std::size_t i = 0, j = 0; i < l_vCornTable.size(); i++)
        {
            if (l_vCornTable[i])
            {
                p_pcCorn->points[j++] = c_srcMap_->m_pFeature->first->at(i);
            }
        }
    }
}

void KeyFrameMap::getSurfPcByInds_(const std::vector<int>& p_vKfInds, type::FeaturePcPtr p_pcSurf)
{
    bool l_bSurfDs = c_bEnableIdModel_ ? true : (c_uiEnableFeatrueDs_ & 0x02);

    if (!l_bSurfDs)
    {
        std::vector<int> l_vLocalSurfIds;
        for (int k = 0; k < (int)p_vKfInds.size(); k++)
        {
            for (int n = 0; n < (int)c_vKfSurfIdInds_[p_vKfInds[k]].size(); n++)
            {
                l_vLocalSurfIds.push_back(c_vKfSurfIdInds_[p_vKfInds[k]][n]);
            }
        }
        std::sort(l_vLocalSurfIds.begin(), l_vLocalSurfIds.end());
        pcl::copyPointCloud(*(c_srcMap_->m_pFeature->second), l_vLocalSurfIds, *p_pcSurf);
    }
    else
    {
        int l_iSurfSize = c_srcMap_->m_pFeature->surfSize();
        std::vector<int> l_vSurfTable(l_iSurfSize, 0);
        int l_iSurfCnt = 0;
        for (int k = 0; k < (int)p_vKfInds.size(); k++)
        {
            for (int n = 0; n < (int)c_vKfSurfIdInds_[p_vKfInds[k]].size(); n++)
            {
                if (!l_vSurfTable[c_vKfSurfIdInds_[p_vKfInds[k]][n]])
                {
                    l_vSurfTable[c_vKfSurfIdInds_[p_vKfInds[k]][n]] = 1;
                    l_iSurfCnt++;
                }
            }
        }

        p_pcSurf->points.resize(l_iSurfCnt);
        p_pcSurf->header = c_srcMap_->m_pFeature->second->header;
        p_pcSurf->width = static_cast<std::uint32_t>(l_iSurfCnt);
        p_pcSurf->height = 1;
        p_pcSurf->is_dense = c_srcMap_->m_pFeature->second->is_dense;
        p_pcSurf->sensor_orientation_ = c_srcMap_->m_pFeature->second->sensor_orientation_;
        p_pcSurf->sensor_origin_ = c_srcMap_->m_pFeature->second->sensor_origin_;
        for (std::size_t i = 0, j = 0; i < l_vSurfTable.size(); i++)
        {
            if (l_vSurfTable[i])
            {
                p_pcSurf->points[j++] = c_srcMap_->m_pFeature->second->at(i);
            }
        }
    }
}

void KeyFrameMap::getMarkPcByInds_(const std::vector<int>& p_vKfInds, type::FeaturePcPtr p_pcMark)
{
    bool l_bMarkDs = c_bEnableIdModel_ ? true : (c_uiEnableFeatrueDs_ & 0x04);

    if (!l_bMarkDs)
    {
        std::vector<int> l_vLocalMarkIds;
        for (int k = 0; k < (int)p_vKfInds.size(); k++)
        {
            for (int n = 0; n < (int)c_vKfMarkIdInds_[p_vKfInds[k]].size(); n++)
            {
                l_vLocalMarkIds.push_back(c_vKfMarkIdInds_[p_vKfInds[k]][n]);
            }
        }
        std::sort(l_vLocalMarkIds.begin(), l_vLocalMarkIds.end());
        pcl::copyPointCloud(*(c_srcMap_->m_pFeature->third), l_vLocalMarkIds, *p_pcMark);
    }
    else
    {
        int l_iMarkSize = c_srcMap_->m_pFeature->markSize();
        std::vector<int> l_vMarkTable(l_iMarkSize, 0);
        int l_iMarkCnt = 0;
        for (int k = 0; k < (int)p_vKfInds.size(); k++)
        {
            for (int n = 0; n < (int)c_vKfMarkIdInds_[p_vKfInds[k]].size(); n++)
            {
                if (!l_vMarkTable[c_vKfMarkIdInds_[p_vKfInds[k]][n]])
                {
                    l_vMarkTable[c_vKfMarkIdInds_[p_vKfInds[k]][n]] = 1;
                    l_iMarkCnt++;
                }
            }
        }

        p_pcMark->points.resize(l_iMarkCnt);
        p_pcMark->header = c_srcMap_->m_pFeature->third->header;
        p_pcMark->width = static_cast<std::uint32_t>(l_iMarkCnt);
        p_pcMark->height = 1;
        p_pcMark->is_dense = c_srcMap_->m_pFeature->third->is_dense;
        p_pcMark->sensor_orientation_ = c_srcMap_->m_pFeature->third->sensor_orientation_;
        p_pcMark->sensor_origin_ = c_srcMap_->m_pFeature->third->sensor_origin_;
        for (std::size_t i = 0, j = 0; i < l_vMarkTable.size(); i++)
        {
            if (l_vMarkTable[i])
            {
                p_pcMark->points[j++] = c_srcMap_->m_pFeature->third->at(i);
            }
        }
    }
}

void KeyFrameMap::getCurbPcByInds_(const std::vector<int>& p_vKfInds, type::FeaturePcPtr p_pcCurb)
{
    bool l_bCurbDs = c_bEnableIdModel_ ? true : (c_uiEnableFeatrueDs_ & 0x08);

    if (!l_bCurbDs)
    {
        std::vector<int> l_vLocalCurbIds;
        for (int k = 0; k < (int)p_vKfInds.size(); k++)
        {
            for (int n = 0; n < (int)c_vKfCurbIdInds_[p_vKfInds[k]].size(); n++)
            {
                l_vLocalCurbIds.push_back(c_vKfCurbIdInds_[p_vKfInds[k]][n]);
            }
        }
        std::sort(l_vLocalCurbIds.begin(), l_vLocalCurbIds.end());
        pcl::copyPointCloud(*(c_srcMap_->m_pFeature->fourth), l_vLocalCurbIds, *p_pcCurb);
    }
    else
    {
        int l_iCurbSize = c_srcMap_->m_pFeature->curbSize();
        std::vector<int> l_vCurbTable(l_iCurbSize, 0);
        int l_iCurbCnt = 0;
        for (int k = 0; k < (int)p_vKfInds.size(); k++)
        {
            for (int n = 0; n < (int)c_vKfCurbIdInds_[p_vKfInds[k]].size(); n++)
            {
                if (!l_vCurbTable[c_vKfCurbIdInds_[p_vKfInds[k]][n]])
                {
                    l_vCurbTable[c_vKfCurbIdInds_[p_vKfInds[k]][n]] = 1;
                    l_iCurbCnt++;
                }
            }
        }

        p_pcCurb->points.resize(l_iCurbCnt);
        p_pcCurb->header = c_srcMap_->m_pFeature->fourth->header;
        p_pcCurb->width = static_cast<std::uint32_t>(l_iCurbCnt);
        p_pcCurb->height = 1;
        p_pcCurb->is_dense = c_srcMap_->m_pFeature->fourth->is_dense;
        p_pcCurb->sensor_orientation_ = c_srcMap_->m_pFeature->fourth->sensor_orientation_;
        p_pcCurb->sensor_origin_ = c_srcMap_->m_pFeature->fourth->sensor_origin_;
        for (std::size_t i = 0, j = 0; i < l_vCurbTable.size(); i++)
        {
            if (l_vCurbTable[i])
            {
                p_pcCurb->points[j++] = c_srcMap_->m_pFeature->fourth->at(i);
            }
        }
    }
}