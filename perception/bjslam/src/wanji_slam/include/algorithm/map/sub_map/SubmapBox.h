/**
 * @file SubmapBox.hpp
 * <AUTHOR>
 * @brief 子图框构建类
 * @version 1.0
 * @date 2023-07-25
 * @copyright Copyright (c)2023 Vanjee
 */

#pragma once
// local
#include "common/common_ex.h"
#include "common/type/type_template.h"
#include "impl/op_vector.hpp"
// pcl
#include <pcl/point_cloud.h>

/**
 * >> 子图框使用说明：
 * 1. 该类依赖于子图类，并为子图类提供候选子图关键帧索引及回环帧索引;
 * 2. 子图关键帧索引通过扫描框（附近框）划定后获取，回环帧索引通过附近框划定后获取;
 *
 * >> 子图框使用步骤:
 * 1. 使用setScanRange函数设定当前扫描框范围;
 * 2. 使用update函数更新子图关键帧索引及回环帧索引，该函数返回当前更新状态；
 * 3. 使用getSubmapKfInds函数、getLoopKfInds函数获取当前子图框更新索引的结果。
 *
 */

class SubmapBox : public TypePose<type::POSE> {
  public:
    using Pose = Eigen::Vector3d;

    /**
     * @brief 子图框
     *
     */
    struct st_Box
    {
        st_Box() : m_fXMin(0), m_fXMax(0), m_fYMin(0), m_fYMax(0) {}
        st_Box(float p_xMin, float p_xMax, float p_yMin, float p_yMax)
            : m_fXMin(p_xMin), m_fXMax(p_xMax), m_fYMin(p_yMin), m_fYMax(p_yMax)
        {
        }
        float m_fXMin;
        float m_fXMax;
        float m_fYMin;
        float m_fYMax;
    };

    /**
     * @brief 构造函数
     * @param p_pcKfPose 关键帧位姿点云
     *
     */
    SubmapBox();

    /**
     * @brief 析构函数
     *
     */
    ~SubmapBox();

    /**
     * @brief 根据当前位姿更新子图框及框中关键帧索引
     * @param p_fkPose  关键帧位姿点云
     * @param p_curPose 当前位姿
     * @code
     *
     * @endcode
     * @return [true] \n
     * [更新成功]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [更新失败，关键帧为空]
     *
     */
    bool update(const type::ConstPosePcPtr p_fkPose, const Pose& p_curPose);

    /**
     * @brief 设置雷达扫描框范围
     * @param p_fMinX   扫描框之X最小值
     * @param p_fMinY   扫描框之Y最小值
     * @param p_fMaxX   扫描框之X最大值
     * @param p_fMaxY   扫描框之Y最大值
     *
     */
    void setScanRange(float p_fMinX, float p_fMinY, float p_fMaxX, float p_fMaxY);

    /**
     * @brief 设置回环使能
     * @param p_flag 使能标志
     *
     */
    void setEnableLoop(bool p_flag);

    /**
     * @brief 获取子图关键帧索引
     * @code
     *
     * @endcode
     * @return [std::vector<int>] \n
     * [子图关键帧索引集合]
     *
     */
    std::vector<int> getSubmapKfInds();

    /**
     * @brief 获取回环关键帧索引
     * @code
     *
     * @endcode
     * @return [std::vector<int>] \n
     * [回环关键帧索引集合]
     *
     */
    std::vector<int> getLoopKfInds();

  private:
    /**
     * @brief 初始化函数
     *
     */
    void init_();

    /**
     * @brief 设置子图关键帧索引集合
     * @param p_vInds 输入关键帧索引
     *
     */
    void setSubmapKfInds_(const std::vector<int>& p_vInds);

    /**
     * @brief 设置回环关键帧索引集合
     * @param p_vInds 输入关键帧索引
     *
     */
    void setLoopKfInds_(const std::vector<int>& p_vInds);

    /**
     * @brief 填充附近框
     * @param p_currPose 当前位姿
     * @code
     *
     * @endcode
     * @return [st_Box] \n
     * [附近框]
     *
     */
    st_Box fillNearbyBox_(const Pose& p_currPose);

    /**
     * @brief 填充扫描框
     * @param p_currPose 当前位姿
     * @code
     *
     * @endcode
     * @return [st_Box] \n
     * [扫描框]
     *
     */
    st_Box fillScanBox_(const Pose& p_currPose);

    /**
     * @brief 填充子图框
     * @param p_currPose 当前位姿
     * @code
     *
     * @endcode
     * @return [st_Box] \n
     * [子图框]
     *
     */
    st_Box fillSubmapBox_(const Pose& p_currPose);

    /**
     * @brief 直通滤波
     * @param p_pcKfPose    原始关键帧点云
     * @param p_stBox       滤波框
     * @param p_vBoxInds    滤波后的关键帧索引
     * @param p_pcBoxPose   滤波后的关键帧点云
     * @code
     *
     * @endcode
     * @return [true] \n
     * [滤波后的关键帧索引集合不为空]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [滤波后的关键帧索引集合为空]
     *
     */
    bool straightFilter_(const type::ConstPosePcPtr p_pcKfPose,
                         const st_Box& p_stBox,
                         std::vector<int>& p_vBoxInds);

    /**
     * @brief 从<源索引集合>获取[最旧]且连续的关键帧索引集合，若<源索引集合>连续，则获取索引为空
     * @param p_kfInds 源索引集合
     * @code
     *
     * @endcode
     * @return [std::vector<int>] \n
     * [目标索引集合]
     *
     */
    std::vector<int> getOldestContinuousKfInds_(std::vector<int> p_kfInds);

    /**
     * @brief 从<源索引集合>获取[最新]且连续的关键帧索引集合，若<源索引集合>连续，则获取全部索引
     * @param p_kfInds 源索引集合
     * @code
     *
     * @endcode
     * @return [std::vector<int>] \n
     * [目标索引集合]
     *
     */
    std::vector<int> getLatestContinuousKfInds_(std::vector<int> p_kfInds);

    /**
     * @brief 判断某一点是否位于目标框内
     * @param p_stBox 目标框
     * @param p_pose  目标点
     * @code
     *
     * @endcode
     * @return [true] \n
     * [位于目标框中]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [不位于目标框中]
     *
     */
    bool isInBox(const st_Box& p_stBox, const type::POSE& p_pose);

  private:
    bool c_bEnableLoop_;        /**< 回环使能 */
    int c_iContinuousIndThr_;   /**< 连续索引判断阈值，大于该值认为索引不连续 */
    float c_fNearbyBoxRadius_;  /**< 附近框半径，即边长之一半 */
    float c_fScanBoxMaxRadius_; /**< 扫描框半径，即边长之一半 */
    Pose c_curPose_;            /**< 当前位姿 */
    st_Box c_stScanBox_;        /**< 扫描框约束 */
    st_Box c_stSubmapBox_;      /**< 子图框 */
    std::mutex c_kfMutex_;      /**< 关键帧锁 */
    std::vector<int> c_vSubmapKfInds_; /**< 子图关键帧索引集合 */
    std::vector<int> c_vLoopKfInds_;   /**< 运动框关键帧索引集合 */
};

#include "impl/SubmapBox.hpp"