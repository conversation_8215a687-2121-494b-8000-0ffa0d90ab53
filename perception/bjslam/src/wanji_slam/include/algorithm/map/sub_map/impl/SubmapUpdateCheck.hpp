/**
 * @file SubmapUpdateCheck.hpp
 * <AUTHOR>
 * @brief  子图更新检验类
 * @version 1.0
 * @date 2023-09-14
 * @copyright Copyright (c)2023 Vanjee
 */

#pragma once

/**
 * 子图更新条件：
 * 1. 首帧；
 * 2. 新增关键帧个数达到指定阈值；
 * 3. 新增里程到达指定阈值；
 *
 */

#include <Eigen/Core>

class SubmapUpdateCheck {
  public:
    /**
     * @brief 构造函数
     * @param p_iKfMapSizeThd   关键帧个数判定阈值
     * @param p_fDistThd        里程判定阈值
     *
     */
    SubmapUpdateCheck(int p_iKfMapSizeThd, float p_fDistThd);

    /**
     * @brief 析构函数
     *
     */
    ~SubmapUpdateCheck();

    /**
     * @brief 初始化判定条件
     *
     */
    void init();

    /**
     * @brief 检验判定条件
     * @param p_kfMapSize   当前关键帧个数
     * @param p_kfPose      当前位姿
     * @code
     *
     * @endcode
     * @return [true] \n
     * [检验通过]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [检验未通过]
     *
     */
    bool check(int p_kfMapSize, const Eigen::Vector3d& p_kfPose);

  private:
    /**
     * @brief 关键帧个数条件判定
     * @param p_kfMapSize
     * @code
     *
     * @endcode
     * @return [true] \n
     * [通过]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [未通过]
     *
     */
    bool kfMapSizeCheck(int p_kfMapSize);

    /**
     * @brief 里程条件判定
     * @param p_kfPose
     * @code
     *
     * @endcode
     * @return [true] \n
     * [通过]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [未通过]
     *
     */
    bool kfPoseCheck(const Eigen::Vector3d& p_kfPose);

  private:
    bool c_bIsFirstFrame_;     /**< 是否为首帧数据 */
    float c_fDistThd_;         /**< 里程判定阈值 */
    int c_iKfMapSizeThd_;      /**< 关键帧个数判定阈值 */
    int c_iKfMapSize_;         /**< 关键帧个数 */
    Eigen::Vector3d c_kfPose_; /**< 关键帧位姿 */
};

SubmapUpdateCheck::SubmapUpdateCheck(int p_iKfMapSizeThd, float p_fDistThd)
    : c_bIsFirstFrame_(true), c_fDistThd_(p_fDistThd), c_iKfMapSizeThd_(p_iKfMapSizeThd),
      c_iKfMapSize_(-1), c_kfPose_(Eigen::Vector3d::Zero())
{
}

SubmapUpdateCheck::~SubmapUpdateCheck() {}

void SubmapUpdateCheck::init()
{
    c_bIsFirstFrame_ = true;
}

bool SubmapUpdateCheck::check(int p_kfMapSize, const Eigen::Vector3d& p_kfPose)
{
    bool res = false;
    if (c_bIsFirstFrame_)
    {
        c_bIsFirstFrame_ = false;
        res = true;
    }
    else
    {
        res = kfMapSizeCheck(p_kfMapSize) || kfPoseCheck(p_kfPose);
    }

    if (res)
    {
        c_iKfMapSize_ = p_kfMapSize;
        c_kfPose_ = p_kfPose;
    }
    return res;
}

bool SubmapUpdateCheck::kfMapSizeCheck(int p_kfMapSize)
{
    if (c_iKfMapSize_ < 0 || p_kfMapSize < 0)
    {
        return false;
    }
    return p_kfMapSize - c_iKfMapSize_ >= c_iKfMapSizeThd_;
}

bool SubmapUpdateCheck::kfPoseCheck(const Eigen::Vector3d& p_kfPose)
{
    float l_fOdomDist = pow(p_kfPose[0] - c_kfPose_[0], 2) + pow(p_kfPose[1] - c_kfPose_[1], 2);
    return l_fOdomDist > c_fDistThd_ * c_fDistThd_;
}
