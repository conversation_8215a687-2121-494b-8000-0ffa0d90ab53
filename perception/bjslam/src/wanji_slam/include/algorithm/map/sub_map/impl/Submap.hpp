// local
#include "algorithm/map/sub_map/Submap.h"
#include "tic_toc.h"
#include "common/time_consuming.hpp"

Submap::Submap(boost::shared_ptr<KeyFrameMap> p_keyFrameMap)
    : c_bEnable2d_(false), c_bIsUpdateSubMap_(false), c_tMapCurr_(Eigen::Vector3d::Zero()),
      c_qMapCurr_(Eigen::Quaterniond::Identity()),
      c_pairSubMap_(boost::make_shared<type::KeyFrame>()),
      c_pairSubMap2d_(boost::make_shared<type::KeyFrame>()),
      c_pairAddSubMap_(boost::make_shared<type::KeyFrame>()),
      c_pairAddSubMap2d_(boost::make_shared<type::KeyFrame>()), c_submapUpdateCheck_(1, 2.0),
      c_keyFrameMap_(p_keyFrameMap), c_bIsRun_(true)
{
    wj_slam::SYSPARAM* l_stSysParam = wj_slam::SYSPARAM::getIn();
    c_iOptModel_ = l_stSysParam->m_map.m_iOptimizeModel;
    c_thread_ = boost::make_shared<std::thread>(&Submap::run_, this);
}

Submap::~Submap()
{
    c_bIsRun_ = false;
    c_thrCond_.notify_all();
    if (c_thread_ && c_thread_->joinable())
    {
        c_thread_->join();
    }
}

void Submap::updateSubmap(bool p_bIsInit)
{
    if (p_bIsInit)
    {
        c_submapUpdateCheck_.init();
    }

    // 检验是否满足子图更新条件
    if (c_submapUpdateCheck_.check(c_keyFrameMap_->getKfSize(), c_tMapCurr_))
    {
        if (p_bIsInit)
        {
            // 初始化条件下，清空子图关键帧队列，更新子图
            std::unique_lock<std::mutex> locker(c_thrMutex_);
            c_vSubmapKfInds_.clear();
            c_vSubmapDsKfInds_.clear();
            // c_thrCond_.notify_one();
            TicToc tt;
            if (c_bIsRun_)
            {
                getSubmap_();
            }
            LOGSP(WDEBUG,
                  "{} [SUBMAP] Submap Cost Time : {:.3f}ms",
                  WJLog::getWholeSysTime(),
                  tt.toc());
        }
        else
        {
            // 尝试获取线程锁，若线程未在运行中，则唤醒，否则跳过本次更新
            if (c_thrMutex_.try_lock())
            {
                c_thrCond_.notify_one();
                c_thrMutex_.unlock();
            }
        }
    }
}

bool Submap::transformPointer(type::KeyFramePtr& p_pairSubMap,
                              std::vector<int>& p_vKFId,
                              bool p_bIstrans2d)
{
    // 获取当前子图指针(拷贝方式/指针方式)
    p_pairSubMap = getSubmapPtr_(SubmapPcType::SUBMAP_3D, false);
    // 获取运动框关键帧索引
    p_vKFId = c_submapBox_.getLoopKfInds();
    // 获取子图更新标志
    bool l_bIsUdpate = c_bIsUpdateSubMap_;
    // 重置子图更新标志
    if (!p_bIstrans2d)
    {
        c_bIsUpdateSubMap_ = false;
    }
    return l_bIsUdpate;
}

bool Submap::transform2dPointer(type::KeyFramePtr& p_pairSubMap2d)
{
    // 若未开启2D模式则强制开启，并即时计算2D子图
    if (!isEnable2d())
    {
        setEnable2d(true);
        generateSubmap2d_(getSubmapPtr_(SubmapPcType::SUBMAP_3D, false));
    }

    // 获取2D子图(拷贝方式)
    p_pairSubMap2d = getSubmapPtr_(SubmapPcType::SUBMAP_2D);
    // 重置子图更新标志
    c_bIsUpdateSubMap_ = false;

    return true;
}

bool Submap::transformAddPointer(type::KeyFramePtr& p_pairWholeSubMap,
                                 type::KeyFramePtr& p_pairAddSubMap,
                                 std::vector<int>& p_vKFId,
                                 bool p_bIstrans2d)
{
    // 获取当前子图指针(拷贝方式/指针方式)
    p_pairWholeSubMap = getSubmapPtr_(SubmapPcType::SUBMAP_3D, false);
    // 获取当前增量子图指针(拷贝方式/指针方式)
    p_pairAddSubMap = getSubmapPtr_(SubmapPcType::INCREMENT_SUBMAP_3D, false);
    // 获取运动框关键帧索引
    p_vKFId = c_submapBox_.getLoopKfInds();
    // 获取子图更新标志
    bool l_bIsUdpate = c_bIsUpdateSubMap_;
    // 重置子图更新标志
    if (!p_bIstrans2d)
    {
        c_bIsUpdateSubMap_ = false;
    }
    return l_bIsUdpate;
}

bool Submap::transformAdd2dPointer(type::KeyFramePtr& l_pairSubMap2d)
{
    // 若未开启2D模式则强制开启，并即时生成2D子图
    if (!isEnable2d())
    {
        setEnable2d(true);
        generateSubmap2d_(getSubmapPtr_(SubmapPcType::INCREMENT_SUBMAP_3D, false));
    }
    // 获取2D子图(拷贝方式)
    l_pairSubMap2d = getSubmapPtr_(SubmapPcType::INCREMENT_SUBMAP_2D);
    // 重置子图更新标志
    c_bIsUpdateSubMap_ = false;

    return true;
}

void Submap::setEnableLoop(bool p_bIsopen)
{
    c_submapBox_.setEnableLoop(p_bIsopen);
}

void Submap::setScanRange(float p_f32MinX, float p_f32MinY, float p_f32MaxX, float p_f32MaxY)
{
    c_submapBox_.setScanRange(p_f32MinX, p_f32MinY, p_f32MaxX, p_f32MaxY);
}

void Submap::setPose(Eigen::Vector3d p_tCurrPose, Eigen::Quaterniond p_qCurrPose)
{
    c_tMapCurr_ = p_tCurrPose;
    c_qMapCurr_ = p_qCurrPose;
}

void Submap::setEnable2d(bool p_bFlag)
{
    c_bEnable2d_ = p_bFlag;
}

bool Submap::isEnable2d()
{
    return c_bEnable2d_;
}

void Submap::run_()
{
    while (c_bIsRun_)
    {
        std::unique_lock<std::mutex> locker(c_thrMutex_);
        c_thrCond_.wait(locker);
        TicToc tt;
        if (c_bIsRun_)
        {
            getSubmap_();
        }
        COST_TIME_SUBMAP(tt.toc());
        LOGSP(
            WDEBUG, "{} [SUBMAP] Submap Cost Time : {:.3f}ms", WJLog::getWholeSysTime(), tt.toc());
    }
}

bool Submap::getSubmap_()
{
    // 根据当前位姿更新子图框
    if (!c_submapBox_.update(c_keyFrameMap_->getKfPose(), c_tMapCurr_))
    {
        LOGSP(WDEBUG, "{} [SUBMAP] SubmapBox empty.", WJLog::getWholeSysTime());
        return false;
    }

    // 获取子图关键帧索引及位姿点云
    std::vector<int> l_vSubmapKfInds = c_submapBox_.getSubmapKfInds();
    type::PosePcPtr l_pcSubmapKfPose = c_keyFrameMap_->getKfPoseByInds(l_vSubmapKfInds);

    // 判断子图关键帧索引是否改变
    if (!l_pcSubmapKfPose->empty() && isChangeKFInd_(c_vSubmapKfInds_, l_vSubmapKfInds))
    {
        // 更新子图关键帧位姿
        c_vSubmapKfInds_ = l_vSubmapKfInds;
        // 关键帧位姿下采样
        downSizeFilterPose_(l_pcSubmapKfPose, l_vSubmapKfInds);
        // 计算新增Ind(iVox只需地图新增部分，实现增量式地图)
        std::vector<int> l_vNewAddSubMapKfInds =
            vectorDifference<int>(l_vSubmapKfInds, c_vSubmapDsKfInds_, false);
        // 更新采样后的子图关键帧位姿
        c_vSubmapDsKfInds_ = l_vSubmapKfInds;
        if (l_vNewAddSubMapKfInds.empty() && c_iOptModel_ == wj_slam::OptimizeMapType::IVOX_TYPE)
        {
            return false;
        }

        LOGSP(WDEBUG,
              "{} [SUBMAP] Update submap success, KF size: {}",
              WJLog::getWholeSysTime(),
              l_vSubmapKfInds.size());

        // 获取子图及增量子图
        type::KeyFramePtr l_pairSubmap = boost::make_shared<type::KeyFrame>();
        type::KeyFramePtr l_pairAddSubmap = boost::make_shared<type::KeyFrame>();
        c_keyFrameMap_->getKfMaps(l_vSubmapKfInds, l_pairSubmap);
        c_keyFrameMap_->getKfMaps(l_vNewAddSubMapKfInds, l_pairAddSubmap);
        // 增加插帧
        // ...
        // 子图下采样
        if (!c_keyFrameMap_->getIdModel())
        {
            c_keyFrameMap_->filterKfMap(l_pairSubmap, l_pairSubmap);
            c_keyFrameMap_->filterKfMap(l_pairAddSubmap, l_pairAddSubmap);
        }
        // 设置当前3D子图
        setSubmapPc_(l_pairSubmap, SubmapPcType::SUBMAP_3D);
        setSubmapPc_(l_pairAddSubmap, SubmapPcType::INCREMENT_SUBMAP_3D);
        // 生成2D地图
        if (isEnable2d())
        {
            generateSubmap2d_(l_pairSubmap);
            generateSubmap2d_(l_pairAddSubmap);
        }
        // 子图更新标志置位
        c_bIsUpdateSubMap_ = true;
        return true;
    }
    return false;
}

bool Submap::isChangeKFInd_(const std::vector<int>& p_vRawInds, const std::vector<int>& p_vNewInds)
{
    return p_vNewInds != p_vRawInds;
}

void Submap::downSizeFilterPose_(const type::ConstPosePcPtr p_pcIn, std::vector<int>& p_vPcInds)
{
    FilterFactory* l_filterFactor = FilterFactory::getInstance();
    type::PosePcPtr l_pcPose = c_keyFrameMap_->getKfPose();
    p_vPcInds = l_filterFactor->filterKfPoseToNearestInds<type::POSE>(p_pcIn, l_pcPose);
}

void Submap::generateSubmap2d_(const type::ConstKeyFramePtr p_pairSubMap3d)
{
    type::KeyFramePtr l_submap2d = boost::make_shared<type::KeyFrame>();

    {
        std::lock_guard <std::mutex>l_lck(c_pairSubmapMutex_);
        *l_submap2d = *p_pairSubMap3d;
    }

    for (int j = 0; j < (int)l_submap2d->m_pFeature->cornerSize(); j++)
    {
        l_submap2d->m_pFeature->first->at(j).z = 0;
    }
    for (int j = 0; j < (int)l_submap2d->m_pFeature->surfSize(); j++)
    {
        l_submap2d->m_pFeature->second->at(j).z = 0;
    }
    for (int j = 0; j < (int)l_submap2d->m_pFeature->markSize(); j++)
    {
        l_submap2d->m_pFeature->third->at(j).z = 0;
    }
    for (int j = 0; j < (int)l_submap2d->m_pFeature->curbSize(); j++)
    {
        l_submap2d->m_pFeature->fourth->at(j).z = 0;
    }

    setSubmapPc_(l_submap2d, SubmapPcType::SUBMAP_2D);
}

void Submap::generateAddSubmap2d_(const type::ConstKeyFramePtr p_pairAddSubMap3d)
{
    type::KeyFramePtr l_addSubmap2d = boost::make_shared<type::KeyFrame>();

    {
        std::lock_guard <std::mutex>l_lck(c_pairSubmapMutex_);
        *l_addSubmap2d = *p_pairAddSubMap3d;
    }

    for (int j = 0; j < (int)l_addSubmap2d->m_pFeature->cornerSize(); j++)
    {
        l_addSubmap2d->m_pFeature->first->at(j).z = 0;
    }
    for (int j = 0; j < (int)l_addSubmap2d->m_pFeature->surfSize(); j++)
    {
        l_addSubmap2d->m_pFeature->second->at(j).z = 0;
    }
    for (int j = 0; j < (int)l_addSubmap2d->m_pFeature->markSize(); j++)
    {
        l_addSubmap2d->m_pFeature->third->at(j).z = 0;
    }
    for (int j = 0; j < (int)l_addSubmap2d->m_pFeature->curbSize(); j++)
    {
        l_addSubmap2d->m_pFeature->fourth->at(j).z = 0;
    }

    setSubmapPc_(l_addSubmap2d, SubmapPcType::INCREMENT_SUBMAP_2D);
}

type::KeyFramePtr Submap::getSubmapPtr_(SubmapPcType l_ptrType, bool p_bCopy)
{
    std::lock_guard <std::mutex>l_lck(c_pairSubmapMutex_);
    type::KeyFramePtr l_submap = nullptr;
    switch (l_ptrType)
    {
        case SubmapPcType::SUBMAP_3D: l_submap = c_pairSubMap_; break;
        case SubmapPcType::SUBMAP_2D: l_submap = c_pairSubMap2d_; break;
        case SubmapPcType::INCREMENT_SUBMAP_3D: l_submap = c_pairAddSubMap_; break;
        case SubmapPcType::INCREMENT_SUBMAP_2D: l_submap = c_pairAddSubMap2d_; break;
        default: l_submap = c_pairSubMap_; break;
    }

    if (p_bCopy)
    {
        type::KeyFramePtr l_submapPtr = boost::make_shared<type::KeyFrame>();
        *l_submapPtr = *l_submap;
        return l_submapPtr;
    }
    return l_submap;
}

void Submap::setSubmapPc_(const type::KeyFramePtr p_pairSubmap,
                          SubmapPcType l_ptrType,
                          bool p_bCopy)
{
    type::KeyFramePtr l_submapPtr = boost::make_shared<type::KeyFrame>();
    std::lock_guard <std::mutex>l_lck(c_pairSubmapMutex_);
    if (p_bCopy)
    {
        *l_submapPtr = *p_pairSubmap;
        switch (l_ptrType)
        {
            case SubmapPcType::SUBMAP_3D: c_pairSubMap_ = l_submapPtr; break;
            case SubmapPcType::SUBMAP_2D: c_pairSubMap2d_ = l_submapPtr; break;
            case SubmapPcType::INCREMENT_SUBMAP_3D: c_pairAddSubMap_ = l_submapPtr; break;
            case SubmapPcType::INCREMENT_SUBMAP_2D: c_pairAddSubMap2d_ = l_submapPtr; break;
            default: c_pairSubMap_ = l_submapPtr; break;
        }
    }
    else
    {
        switch (l_ptrType)
        {
            case SubmapPcType::SUBMAP_3D: c_pairSubMap_ = p_pairSubmap; break;
            case SubmapPcType::SUBMAP_2D: c_pairSubMap2d_ = p_pairSubmap; break;
            case SubmapPcType::INCREMENT_SUBMAP_3D: c_pairAddSubMap_ = p_pairSubmap; break;
            case SubmapPcType::INCREMENT_SUBMAP_2D: c_pairAddSubMap2d_ = p_pairSubmap; break;
            default: c_pairSubMap_ = p_pairSubmap; break;
        }
    }
}