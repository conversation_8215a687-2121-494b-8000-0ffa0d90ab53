/**
 * @file input.h
 * <AUTHOR> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
 * @brief 雷达驱动类
 * @version 1.0
 * @date 2023-06-20
 * @copyright Copyright (c)2023 Vanjee
 */

#ifndef __LSLIDAR_INPUT_H_
#define __LSLIDAR_INPUT_H_

// local
#include "common/common_ex.h"
#include "common/type/circleQueue.h"
#include "tool/fileTool/fileTool.h"
// sys
#include <netinet/in.h>

namespace wanji_driver {
/********************** 720 Protocol **************************/

#define GET_SN 0x0510          /**< 获取设备SN号 0x0401 */
#define GET_BTN_TEMP 0x0402    /**< 获取底板温度 */
#define GET_VERTICAL_AG 0x0514 /**< 获取雷达垂直角度分辨率 */
#define GET_LEVEL_AG 0x051A    /**< 获取雷偏心修正表 */
#define GET_DEV_PARAM 0x0501   /**< 获取设备基本参数 */
#define GET_FIRE_STATUS 0x0507 /**< 获取发光通道使能情况 */

#define MIN_PROTOCOL_SIZE 32      /**< 720协议最小长度 */
#define RECV_BUFFER_SIZE 15120000 /**< 接收缓存大小 1260*120*100 = 14.4M */
#define TEMP_BUFFER_SIZE 40960    /**< 临时缓存大小 40k */
#define SCAN_SIZE_720_FE 1260     /**< 720扫描数据大小 FFEE格式 */
#define SCAN_SIZE_720_FD_1 1145 /**< 720扫描数据大小 FFDD格式 单回波19线(5+1188+60）*/
#define SCAN_SIZE_720_FD_2 1001 /**< 720扫描数据大小 FFDD格式 双回波16线(5+936+60) */

/*********************** 720 Protocol *************************/
#pragma region 结构体
typedef struct
{
    int fd;
    int port;
    int err_no;
    struct sockaddr_in local;
    struct sockaddr_in remote;
    struct timeval time_kernel;
    struct timeval time_user;
    int64_t prev_serialnum;
} socket_info;

/**
 * @brief 扫描数据缓存
 *
 */
struct st_ScanBuffer
{
    bool m_bIsFull;           /**< 是否已收集整圈数据 */
    int32_t m_iCircleNum;     /**< 本圈圈号 */
    uint16_t m_frameCnt;      /**< 已收集帧计数 */
    uint16_t m_startIdx;      /**< 本圈起始帧序号 */
    s_LIDAR_RAW_DATAS m_data; /**< 本圈数据 */

    st_ScanBuffer() : m_bIsFull(false), m_iCircleNum(-1), m_frameCnt(0), m_startIdx(0) {}
};
//离线仿真数据包头
struct offlinePcap_pkthdr
{
    u_int16_t m_header;
    u_int16_t m_headerLen;
    int len;              /* length this packet (off wire) */
    wj_slam::sTimeval ts; /* time stamp */
};
#pragma endregion

#pragma region 通信基类
class Input {
  public:
    typedef boost::function<void(st_ScanBuffer&)> ScanDataCb;

    /**
     * @brief 构造函数
     * @param p_uiLidarId   雷达ID
     * @param scandataCb    扫描数据输出回调
     *
     */
    Input(uint32_t p_uiLidarId, ScanDataCb scandataCb);

    /**
     * @brief 析构函数
     *
     */
    virtual ~Input();

    /**
     * @brief 雷达驱动是否启动
     * @code
     *
     * @endcode
     * @return [true] \n
     * [已启动]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [未启动]
     *
     */
    virtual bool isStart();

    /**
     * @brief 发送网络数据
     * @param p_cBuf    当前发送数据
     * @param p_iSize   实际发送长度
     * @code
     *
     * @endcode
     * @return [int] \n
     * [-1 失败; 0 成功]
     *
     */
    virtual int sendPacket(uint8_t* p_cBuf, const int p_iSize);

    /**
     * @brief 查询设备基本参数
     *
     */
    virtual void requestBaseParam() = 0;

    int getDevMac(int p_iSocket, std::string& p_sMac);

  protected:
    /**
     * @brief 设备初始化 设备可用
     * @code
     *
     * @endcode
     * @return [true] \n
     * [初始化成功]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [失败]
     *
     */
    virtual bool inputInit_();

    /**
     * @brief 网络初始化 建立套接字 设置端口复用 绑定端口 默认UDP
     * @param inf         套接字信息
     * @param p_sMyAddr   设备通信地址
     * @code
     *
     * @endcode
     * @return [true] \n
     * [初始化成功]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [失败]
     *
     */
    virtual bool onlineInit_(socket_info& inf, sockaddr_in& p_sMyAddr);

    /**
     * @brief 接收socket数据
     *
     */
    virtual void recvSocketData_();

    /**
     * @brief 处理socket数据
     *
     */
    virtual void parseSocketData_();

    /**
     * @brief 参数查询相关解析
     * @param data 数据首地址
     * @param len  数据长度
     *
     */
    virtual void parseLidarParam_(const uint8_t* data, uint16_t len);

    /**
     * @brief 扫描数据相关解析
     * @param data  数据首地址
     * @param len   数据长度
     *
     */
    virtual void parseScanData_(const uint8_t* data, uint16_t len);

    /**
     * @brief 初始化扫描数据缓存
     * @param npackets 扫描数据包数
     *
     */
    void initScanBuffer_(uint16_t npackets);

    /**
     * @brief 重置ScanBuffer
     * @param buffer      待重置buffer之引用
     * @param circleNum   圈号
     * @param startIdx    起始序号
     * @param npackets    包数
     *
     */
    void resetScanBuffer_(st_ScanBuffer& buffer,
                          int32_t circleNum,
                          uint16_t startIdx,
                          uint16_t npackets);

    /**
     * @brief 异或校验
     * @param p_vBuf    待校验数据首地址
     * @param p_iSize   待校验数据长度
     *
     */
    virtual void checkXOR_(uint8_t p_vBuf[], int p_iSize);
    virtual wj_slam::sTimeval getSyncTime_(const offlinePcap_pkthdr* pcapHeader,
                                           const wj_slam::sTimeval& timenow);

    /**
     * @brief 检查连接
     * @code 
     *    
     * @endcode 
     * @return [true] \n 
     * [新设备连接]
     * @code 
     *    
     * @endcode 
     * @return [false] \n 
     * [并非新设备]
     * 
     */
    virtual bool checkConnect_();

    int getDevMac_(int p_iSocket, std::string& p_sMac);

  protected:
    wj_slam::SYSPARAM* c_stSysParam_;      /**< 系统参数指针 */
    wj_slam::s_LidarConfig& c_stLaserCfg_; /**< 雷达配置参数引用 */
    ScanDataCb c_outScanDataCb_;           /**< 扫描数据输出回调 */
    sockaddr_in c_myAddr_;                 /**< 本机地址 */
    sockaddr_in c_remoteAddr_;             /**< 雷达地址 */
    socket_info c_sockinf_;                /**< socket 信息 */
    ip_mreq c_sockmreq_;                   /**< 组播参数 */
    bool c_bRun_;                          /**< 运行标志 */
    bool c_bScanStartFlag_;                /**< 扫描数据起始标志 */
    bool c_bInitSuecss_;                   /**< 初始化成功标志 */
    uint32_t c_uiLidarId_;                 /**< 雷达ID */
    uint16_t c_iLevevAgPkgStatus_;         /**< 偏心表包已获取状态 初值为0x0001 */
    uint16_t c_uiExpPktIdx_;               /**< 当前期望包号，用以判断包是否乱序 */
    uint8_t c_iCurBufferFlag_;             /**< 当前使用buffer标志 1/2 */
    int c_iFirstCycleNum_;                 /**< 预处理圈号  初值-1 */
    int c_iFirstPktIdx_;                   /**< 预处理圈首包包号  初值-1 */
	  int c_iPcapHeaderLen_;                 /**< 数据包中包含的Pcapheader长度 */
    std::thread* c_recvThr_;               /**< 接收线程 */
    std::thread* c_parseThr_;              /**< 解析线程 */
    std::string c_sDevMac_;                /**< 设备MAC地址 */
    st_ScanBuffer c_stScanBuffer1_;        /**< 扫描包接收缓存1（一圈）*/
    st_ScanBuffer c_stScanBuffer2_;        /**< 扫描包接收缓存2（一圈）*/
    CircleQueue<uint8_t> c_recvBuffer_;    /**< 雷达UDP数据接收缓存 */
    uint16_t c_uiFirstCirclePkgNum_;       /**< 首圈（预处理圈）帧计数 */
    std::vector<uint16_t> c_FirstCirclIdx_; /**< 首圈帧序记录，用以在确定首包帧序后排序 */
    std::vector<s_LIDAR_RAW_DATA> c_FirstCircleData_; /**< 首圈数据记录，用以在确定首包帧序后排序 */
};
#pragma endregion

#pragma region 720类
class Input720 : public Input {
  public:
    /**
     * @brief 构造函数
     * @param p_uiLidarId   雷达ID
     * @param scandataCb    扫描数据回调
     *
     */
    Input720(uint32_t p_uiLidarId, ScanDataCb scandataCb);

    /**
     * @brief 析构函数
     *
     */
    virtual ~Input720();

    /**
     * @brief 查询设备基本参数
     *
     */
    void requestBaseParam();

  protected:
    using Array = s_LIDAR_RAW_DATA::DArray;

#pragma region 变量

    std::string c_sVerAnglePath_;    /**< 垂直分辨率表路径 */
    std::string c_sCalibrationPath_; /**< 水平偏心表路径 */
    int c_iLevelAngleData_[7200];    /**< 偏心数据 */
    bool c_bIsGetSn_;                /**< 是否获取到SN号 */
    bool c_bIsGetBaseParam_;         /**< 是否获取到基本参数 */
    bool c_bIsGetVerAngleData_;      /**< 是否获取到垂直角度 */
    bool c_bIsGetLevelAngleData_;    /**< 是否获取到偏心角 */

#pragma endregion

#pragma region 函数

    /**
     * @brief 参数查询相关解析
     * @param data 数据首地址
     * @param len  数据长度
     *
     */
    void parseLidarParam_(const uint8_t* data, uint16_t len) override;

    /**
     * @brief 查询替换SN
     * @code
     *
     * @endcode
     * @return [int] \n
     * [查询是否成功]
     *
     */
    int requestSN_();

    /**
     * @brief 解析SN
     * @param p_buf     待解析数据
     * @param p_iSize   待解析数据长度
     * @code
     *
     * @endcode
     * @return [true] \n
     * [解析成功]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [解析失败]
     *
     */
    bool analyseSN_(const uint8_t* p_buf, const int p_iSize);

    /**
     * @brief 比较SN 并改写配置SN
     * @param p_sSetSN    待配置SN
     * @param p_sReadSN   待比较SN
     *
     */
    void compareSN_(std::string& p_sSetSN, std::string p_sReadSN);

    /**
     * @brief 只有1个分割符c, 将s 按照分割符c 切割写入v  同时保留c
     * @param s   s 字符串
     * @param v   v 输出列表
     * @param c   c 分割符
     * WLR720F_NP 分割符：_NP  结果将为[0] WLR720F  [1] _NP
     */
    void splitStr_(const std::string& s, std::vector<std::string>& v, const std::string& c);

    /**
     * @brief 查询基本运行情况
     * @code
     *
     * @endcode
     * @return [int] \n
     * [查询是否成功]
     *
     */
    int requestBaseParam_();

    /**
     * @brief 解析基本运行情况
     * @param p_buf     待解析数据
     * @param p_iSize   待解析数据长度
     * @code
     *
     * @endcode
     * @return [true] \n
     * [解析成功]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [解析失败]
     *
     */
    bool analyseBaseParam_(const uint8_t* p_buf, const int p_iSize);

    /**
     * @brief 查询保存垂直表
     * @code
     *
     * @endcode
     * @return [int] \n
     * [-1 读取错误 0 读取成功]
     *
     */
    int requestVerAngleData_();

    /**
     * @brief 解析保存垂直表
     * @param p_buf     待解析数据
     * @param p_size    待解析数据长度
     * @code
     *
     * @endcode
     * @return [true] \n
     * [解析保存成功]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [解析保存失败]
     *
     */
    bool analyseVerAngleData_(const uint8_t* p_buf, const int p_size);

    /**
     * @brief 保存垂直表
     * @param p_VerAngleData 垂直表数据首地址
     * @param p_uiSize       垂直表数据长度
     * @param p_sFilePath    保存路径
     *
     */
    void saveVerAngleData_(float* p_VerAngleData, uint p_uiSize, std::string p_sFilePath);

    /**
     * @brief 查询保存偏心表
     * @code
     *
     * @endcode
     * @return [int] \n
     * [-1 读取错误 0 读取成功]
     *
     */
    int requestLevelAngleData_();

    /**
     * @brief 解析保存偏心表
     * @param p_buf     待解析数据
     * @param p_size    待解析数据长度
     * @code
     *
     * @endcode
     * @return [int] \n
     * [-1 读取错误 0~14 读取成功]
     *
     */
    int analyseLevelAngleData_(const uint8_t* p_buf, const int p_size);

    /**
     * @brief 保存偏心表 14400 5个1行
     * @param p_VerAngleData  偏心表数据首地址
     * @param p_uiSize        偏心表数据长度
     * @param p_sFilePath     保存路径
     * @code
     *
     * @endcode
     * @return [true] \n
     * [保存成功]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [保存失败]
     *
     */
    bool saveLevelAngleData_(int* p_VerAngleData, uint p_uiSize, std::string p_sFilePath);
#pragma endregion
};
#pragma endregion

}  // namespace wanji_driver

#endif