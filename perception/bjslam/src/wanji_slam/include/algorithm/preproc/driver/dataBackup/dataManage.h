/**
 * @file dataManage.h
 * <AUTHOR>
 * @brief 文件夹管理器
 * @version 1.1
 * @date 2023-06-25
 * @copyright Copyright (c)2023 <PERSON><PERSON>
 */

#pragma once
#include <boost/filesystem.hpp>
#include <boost/regex.hpp>

using namespace std;

class DataManage {
  public:
    /**
     * @brief 创建一个文件夹管理器
     * @param p_p       文件路径
     * @param p_iNum    最多保留的文件夹数目
     *
     */
    DataManage(std::string p_p, int p_iNum)
    {
        c_vsFolderNameList.clear();
        c_iFileMaxNum_ = p_iNum;
        std::string p_sPath = p_p;
        if (p_sPath[p_sPath.length() - 1] != '/')
            p_sPath += "/";
        c_pDataPath_ = boost::filesystem::path(p_sPath);
        funBrowseFile_(c_vsFolderNameList, c_pDataPath_, c_iFileMaxNum_);
    }

    /**
     * @brief 判断一个文件是否存在
     * @param p_path 文件路径
     * @code
     *
     * @endcode
     * @return [true] \n
     * [存在]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [不存在]
     *
     */
    bool isFileExistent(const boost::filesystem::path& p_path)
    {
        boost::system::error_code error;
        return boost::filesystem::exists(p_path, error);
    }

    /**
     * @brief 添加新的文件夹 删除旧的
     * @param l_sFolderName 文件夹名
     * @code
     *
     * @endcode
     * @return [true] \n
     * [添加成功]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [添加失败]
     *
     */
    bool deleteOldFolder(std::string l_sFolderName)
    {
        c_vsFolderNameList.push_back(l_sFolderName);
        if ((int)c_vsFolderNameList.size() > (c_iFileMaxNum_ - 1))
        {
            std::string l_file_path = c_pDataPath_.string() + c_vsFolderNameList.front();
            if (removeFolder_(l_file_path))
            {
                std::cout << "删除成功:" << c_vsFolderNameList.front() << std::endl;
                c_vsFolderNameList.erase(c_vsFolderNameList.begin());
            }
            if (boost::filesystem::exists(boost::filesystem::path(l_file_path)))
                printf("文件[%s]删除失败\n", c_vsFolderNameList.front().c_str());
        }
        sort(c_vsFolderNameList.begin(), c_vsFolderNameList.end());
        return true;
    }

    std::vector<std::string> c_vsFolderNameList; /**< 文件夹下所有文件夹名称+后缀 */

  private:
    int c_iFileMaxNum_;                   /**< 保留的最多文件夹数目*/
    boost::filesystem::path c_pDataPath_; /**< 文件路径*/

    /**
     * @brief 将string转为int
     * @param p_sText 待转的string文本
     * @code
     *
     * @endcode
     * @return [int] \n
     * [为数字，string转换成功。否则为0]
     *
     */
    static int funSt2Num_(const std::string& p_sText) /**string 2 num*/
    {
        std::stringstream l_ss(p_sText);
        int l_result;
        return l_ss >> l_result ? l_result : 0;
    }

    /**
     * @brief 文件名排序
     *
     */
    struct funMysort
    {
        bool operator()(const std::string& p_sa, const std::string& p_sb)
        {
            boost::regex re("(\\d+)");
            boost::match_results<std::string::const_iterator> what1, what2;
            boost::regex_search(p_sa.cbegin(), p_sa.cend(), what1, re, boost::match_default);
            boost::regex_search(p_sb.cbegin(), p_sb.cend(), what2, re, boost::match_default);

            return funSt2Num_(what1[1]) > funSt2Num_(what2[1]);
        }
    };

    /**
     * @brief 删除文件夹
     * @param l_path 文件夹路径
     * @code
     *
     * @endcode
     * @return [true] \n
     * [删除成功]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [删除失败]
     *
     */
    bool removeFolder_(std::string l_path)
    {
        try
        {
            boost::filesystem::remove_all(boost::filesystem::path(l_path));
            return true;
        }
        catch (const std::exception& e)
        {
            printf("删除失败，请检查 | %s\n", e.what());
        }
        return false;
    }

    /**
     * @brief 遍历目录下所有文件，添加至l_vfiles中
     * @param l_vfiles  保存的所有文件
     * @param p         路径
     * @param p_iMaxNum 最多保留的pcap文件数目
     *
     */
    void funBrowseFile_(std::vector<std::string>& l_vfiles,
                        boost::filesystem::path p,
                        int p_iMaxNum = 10)
    {
        if (!boost::filesystem::exists(p))
            return;
        for (auto i = boost::filesystem::directory_iterator(p);
             i != boost::filesystem::directory_iterator();
             i++)
        {
            // 目录则添加
            if (boost::filesystem::is_directory(i->path()))
            {
                // cout << i->path().filename().string() << endl;
                l_vfiles.push_back(i->path().filename().string());
            }
        }
        // 从小到大排序
        if (!l_vfiles.empty())
            sort(l_vfiles.begin(), l_vfiles.end());
        int l_Size = l_vfiles.size();
        while (1)
        {
            if ((int)l_vfiles.size() <= p_iMaxNum)
                break;
            std::string l_file_path = c_pDataPath_.string() + l_vfiles.front();
            l_vfiles.erase(l_vfiles.begin());
            if (!removeFolder_(l_file_path))
                break;
        }
        for (int i = 0; i < (int)l_vfiles.size(); i++)
        {
            std::cout << "folder[" << i << "]" << l_vfiles[i] << std::endl;
        }
    }
};