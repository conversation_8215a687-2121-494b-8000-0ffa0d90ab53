/**
 * @file convert.h
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @brief 接收 deta_X * 1600 网络原始数据 判断数据是否异常 解算点云
 * @version 1.1
 * @date 2023-06-25
 * @copyright Copyright (c)2023 <PERSON>jee
 */

#ifndef _WANJI_CONVERT_H_
#define _WANJI_CONVERT_H_

// local
#include "../driver/wanji_driver.h"
#include "common/common_ex.h"
#include "common/type/type_imu.h"
#include "common_WLR720.h"
#include "tic_toc.h"
// std
#include <queue>
// boost
#include <boost/function.hpp>
// pcl
#include <pcl_ros/point_cloud.h>

using namespace std;

namespace wanji_driver {
using TimeSource = wj_slam::TimeSource;
struct st_TWithTS
{
    boost::shared_ptr<s_LIDAR_RAW_DATAS> m_data;
    int m_iFrameId;
    timeMs m_tsSyncTime;
    timeMs m_tsSyncTimeEnd;
    timeMs m_tsWallTime;
    bool m_bIsReConnect;
    st_TWithTS()
    {
        m_data = NULL;
        m_iFrameId = 0;
        m_tsSyncTime = 0;
        m_tsSyncTimeEnd = 0;
        m_tsWallTime = 0;
        m_bIsReConnect = false;
    }
    st_TWithTS& operator=(st_TWithTS& in)
    {
        this->m_data = in.m_data;
        this->m_iFrameId = in.m_iFrameId;
        this->m_tsSyncTime = in.m_tsSyncTime;
        this->m_tsSyncTimeEnd = in.m_tsSyncTimeEnd;
        this->m_tsWallTime = in.m_tsWallTime;
        this->m_bIsReConnect = in.m_bIsReConnect;
        return *this;
    }
};

#pragma region 基类
class Convert {
  public:
    /**
     * @brief 构造函数
     * @param p_uiId    雷达ID
     *
     */
    Convert(uint32_t p_uiId);

    /**
     * @brief 析构函数
     *
     */
    ~Convert();

    /**
     * @brief 设置到base坐标系的转移
     *
     */
    void setTransToBase();

    /**
     * @brief 启动解析线程
     *
     */
    virtual void start();

    /**
     * @brief 停止解析线程
     *
     */
    virtual void stop();

    /**
     * @brief 判断线程是否停止
     * @code
     *
     * @endcode
     * @return [true] \n
     * [已停止]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [未停止]
     *
     */
    virtual bool isStop();

    /**
     * @brief 关闭线程
     *
     */
    virtual void shutdown();

    virtual void setIMUCallback(boost::function<void(uint32_t, const wj_slam::IMUPerScan&)> p_cb)
    {
        c_imuCb_ = p_cb;
    }

    /**
     * @brief 判断线程是否关闭
     * @code
     *
     * @endcode
     * @return [true] \n
     * [已关闭]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [未关闭]
     *
     */
    virtual bool isShutdown();

    /**
     * @brief 线程执行函数
     *
     */
    void run();

    /**
     * @brief 接收不同雷达网络原始数据 转化为该类格式 记录系统时间
     * @param p_pScanMsg        原始数据
     * @param p_iFrameId        帧ID
     * @param isReNewConnect    是否重连
     *
     */
    virtual void processScan(boost::shared_ptr<s_LIDAR_RAW_DATAS> p_pScanMsg,
                             int p_iFrameId,
                             bool isReNewConnect);

  protected:
    /**
     * @brief 根据不同型号雷达 提取包内部时间信息
     * @param p_rawData 当前包数据
     * @code
     *
     * @endcode
     * @return [timeval] \n
     * [包内部时间信息]
     *
     */
    virtual wj_slam::sTimeval getPktDataTime_(const s_LIDAR_RAW_DATA& p_rawData) = 0;

    virtual wj_slam::sTimeval getSyncTime_(const s_LIDAR_RAW_DATA& p_rawData) = 0;

    virtual bool getSyncTime_(const s_LIDAR_RAW_DATA& p_rawData, wj_slam::sTimeval& p_timeval) = 0;

    /**
     * @brief 获取原始数据
     * @code
     *
     * @endcode
     * @return [st_TWithTS] \n
     * [数据缓存]
     *
     */
    st_TWithTS getRawData_();

    /**
     * @brief 是否存在点云
     * @code
     *
     * @endcode
     * @return [true] \n
     * [存在点云]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [不存在点云]
     *
     */
    bool hasPointCloud_();

    /**
     * @brief 点云当前帧异常 相关信息保存
     * @param p_sPath       存储路径
     * @param p_sLaserName  雷达名
     * @param p_iLastTime   上一帧点云时间
     * @param p_iCurTime    当前点云时间
     * @param p_iCurID      帧ID
     *
     */
    void saveErrorLogFile_(std::string p_sPath,
                           std::string p_sLaserName,
                           int p_iLastTime,
                           int p_iCurTime,
                           int p_iCurID);

    /**
     * @brief 根据距离/角度盲区/最小强度 过滤点云
     * @param p_uiIntensity     当前点云强度
     * @param p_fDist           当前点云距离
     * @param p_fAzimuthAngle   当前点云方位角
     * @code
     *
     * @endcode
     * @return [true] \n
     * [点云有效]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [点云无效]
     *
     */
    bool isValidData_(u_int& p_uiIntensity, float& p_fDist, float& p_fAzimuthAngle);

    /**
     * @brief 设置雷达盲区零度偏移 并约束起始/终止角
     *
     */
    void setBlindZeroAngOffset_();

    /**
     * @brief 改变雷达角度 适合逆时针旋转方式
     * @param p_fAzimuthAngle 雷达输出角度
     *
     */
    virtual void angAntiRotate_(float& p_fAzimuthAngle) = 0;

    /**
     * @brief 根据不同型号雷达 点云解算
     * @param p_pScanMsg    当前点云
     * @param p_iId         当前帧id
     *
     */
    virtual void unpackData_(st_TWithTS& p_pScanMsg, int p_iId) = 0;

    /**
     * @brief 检查原始数据头角度范围是否异常
     * @param p_pScanMsg    原始数据
     * @param p_iCurId      帧id
     * @code
     *
     * @endcode
     * @return [true] \n
     * [正常]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [异常]
     *
     */
    virtual bool checkDataHeader_(st_TWithTS& p_pScanMsg, int& p_iCurId) = 0;

  protected:
    typedef pcl::PointXYZHSV POINTTYPEOUT;
    typedef pcl::PointCloud<POINTTYPEOUT> PCOut;
    typedef pcl::PointCloud<POINTTYPEOUT>::Ptr PCOutPtr;

#pragma region 常量 / 结构体定义
    constexpr static double D2R = 0.017453292519943;     /**< 角度转弧度 1/180.0*M_PI */
    constexpr static double R2D = 57.295779513082;       /**< 弧度转角度 1*180.0/M_PI */
    constexpr static float ROTATION_RESOLUTION = 0.001f; /**< [deg] sin/cos查表最小角度 */
    constexpr static float ROTATION_RESOLUTION_INV = 1.0f / ROTATION_RESOLUTION; /**< [1/deg] */
    constexpr static u_int ROTATION_MAX_UNITS = 360 * ROTATION_RESOLUTION_INV; /**< [deg/100]长度 */

    /**
     * @brief 联合体-成员共用地址 uint的值 为 byte[1]byte[0]合起来 实现byte转换uint16/int16
     *
     */
    union two_bytes
    {
        uint16_t uint;
        int16_t sint;
        uint8_t bytes[2];
    };

#pragma endregion

    uint32_t c_uiLidarID_;                               /**< 雷达ID */
    wj_slam::SYSPARAM* c_stSysParam_;                    /**< 系统参数指针 */
    wj_slam::s_LidarConfig& c_stLaserCfg_;               /**< 雷达参数引用 */
    float c_afSinRotTable_[ROTATION_MAX_UNITS];          /**< sin表 */
    float c_afCosRotTable_[ROTATION_MAX_UNITS];          /**< cos表 */
    std::vector<wj_slam::s_BlindConfig> c_vstBlindInfo_; /**< 角度盲区 */
    std::queue<st_TWithTS> c_rawDataBuffer_;             /**< 接收原始数据队列 */
    bool c_bUnpcakRun_;                                  /**< 解析线程运行标志 */
    std::shared_ptr<std::thread> c_unpackThr_;           /**< 解析线程 */
    bool c_bImuFirstPkg_;
    wj_slam::sTimeval c_tvLastIMUPktTime_;
    boost::function<void(uint32_t)> c_netErrorCb_;
    boost::function<void(uint32_t, const wj_slam::IMUPerScan&)> c_imuCb_;
};
#pragma endregion

#pragma region 720类
class Convert720 : public Convert {
  protected:
    /**< 占用字节 （距离2 脉宽1 置信度1） */
    constexpr static int RAW_SCAN_SIZE = 4;
    /**< 发光通道19线 */
    constexpr static int SCANS_PER_BLOCK = 19;
    /**< 19*4 每块数据长度 */
    constexpr static int BLOCK_DATA_SIZE = (SCANS_PER_BLOCK * RAW_SCAN_SIZE);
    /**< 120包每包15块 每块80字节 */
    constexpr static int BLOCKS_PER_PACKET = 15;
    /**< 19*15 */
    constexpr static int SCANS_PER_PACKET = (SCANS_PER_BLOCK * BLOCKS_PER_PACKET);
    /**< 中间线点数 */
    constexpr static int MIDLINE_POINT_SIZE = 7200;
    /**< 720最小精度4mm */
    constexpr static float DISTANCE_RESOLUTION = 0.004f;

    // IMU转换参数
    constexpr static double c_fAngScalar = 1.0 * 8.75 / 1000.0 / 180.0 * M_PI; /**< rad/s */
    constexpr static double c_fAccScalar = 1.0 * 0.061 / 1000.0 * 9.80665;     /**< m/s^2 */
    typedef struct RawBlock
    {
        uint16_t header;
        uint16_t rotation;
        uint8_t data[BLOCK_DATA_SIZE];
    } st_RawBlock;

    typedef struct RawAddtionMsg
    {
        uint16_t header;
        uint8_t rads[2];
        uint8_t time[6];
        uint8_t nsec[4];
        uint8_t angVel[6];
        uint8_t accel[6];
    } st_RawAddtionMsg;

    typedef struct RawPacket
    {
        st_RawBlock blocks[BLOCKS_PER_PACKET];
        st_RawAddtionMsg addmsg;
    } st_RawPacket;

  public:
    typedef boost::function<void(uint32_t,
                                 s_PCloud (&)[WLR720_SCANS_PER_FIRING],
                                 s_PCloud&,
                                 PCOutPtr&,
                                 timeMs,
                                 timeMs,
                                 u_int32_t)>
        FeatureCb;
    typedef pcl::PointCloud<pcl::PointXYZI> PointCloud;

#pragma region 公有函数
    Convert720(uint32_t p_uiLidarID, FeatureCb p_fECb);

    virtual ~Convert720();

#pragma endregion

  protected:
    FeatureCb c_fECb_;                             /**< 特征提取回调函数 */
    s_PCloud c_pcRawOut_[WLR720_SCANS_PER_FIRING]; /**< 原始点云 */
    float c_fAngleResolutionVal_;                  /**< 水平角分辨率 */
    float c_fLineAngleGap_;                        /**< 4条线发光通道 间隔角度 */
    float c_fGroupAngleVal_;                       /**< 中间线水平角分辨率 */
    float c_afVertAngle_[SCANS_PER_BLOCK];         /**< 默认垂直表 */
    float c_afAzimuthDiff_[SCANS_PER_BLOCK];       /**< 方位角修正表? */
    float c_afVsinRotTable_[SCANS_PER_BLOCK];      /**< 垂直表修正sin */
    float c_afVcosRotTable_[SCANS_PER_BLOCK];      /**< 垂直表修正cos */
    int c_aiEccentricity_[MIDLINE_POINT_SIZE];     /**< 偏心修正表 离心 */

#pragma region 保护函数
    /**
     * @brief 检查原始数据头角度范围是否异常
     * @param p_pScanMsg    原始数据
     * @param p_iCurId      帧id
     * @code
     *
     * @endcode
     * @return [true] \n
     * [正常]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [异常]
     *
     */
    virtual bool checkDataHeader_(st_TWithTS& p_pScanMsg, int& p_iCurId);

    /**
     * @brief 变雷达角度，并适合逆时针旋转方式
     * @param p_fAzimuthAngle 雷达输出角度
     *
     */
    virtual void angAntiRotate_(float& p_fAzimuthAngle);

    /**
     * @brief 根据不同型号雷达，点云解算
     * @param p_pScanMsg    当前点云
     * @param p_iId         当前帧id
     *
     */
    virtual void unpackData_(st_TWithTS& p_pScanMsg, int p_iId);

    /**
     * @brief 检查线束的点数是否异常
     * @param p_iCurId 当前点云
     * @code
     *
     * @endcode
     * @return [true] \n
     * [正常]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [异常]
     *
     */
    virtual bool checkLines_(s_PCloud (&p_rawOut)[WLR720_SCANS_PER_FIRING], int p_iCurId);

    /**
     * @brief 根据不同型号雷达，提取包内部时间信息
     * @param p_rawData 当前包数据
     * @code
     *
     * @endcode
     * @return [timeval] \n
     * [包内部时间信息]
     *
     */
    virtual wj_slam::sTimeval getPktDataTime_(const s_LIDAR_RAW_DATA& p_rawData);
    virtual wj_slam::sTimeval getSyncTime_(const s_LIDAR_RAW_DATA& p_rawData);
    virtual bool getSyncTime_(const s_LIDAR_RAW_DATA& p_rawData, wj_slam::sTimeval& p_timeval);
    /**
     * @brief 加载雷达配置
     * @code
     *
     * @endcode
     * @return [true] \n
     * [配置成功]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [配置失败]
     *
     */
    virtual bool setup_();

    /**
     * @brief 读取垂直表
     * @param p_cVerAngData 待修改垂直表数据 需要有默认值 用于无垂直表时使用
     * @param p_cDiffData   待填充修正数据?
     * @param p_uiSize      数组最大长度
     * @param p_sFilePath   垂直表路径
     * @code
     *
     * @endcode
     * @return [true] \n
     * [读取成功]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [读取失败]
     *
     */
    virtual bool loadVelAngleData_(float* p_cVerAngData,
                                   float* p_cDiffData,
                                   uint32_t p_uiSize,
                                   std::string p_sFilePath);

    /**
     * @brief 读取偏心表数据 默认以“，”分割
     * @param p_cData       代填充数组
     * @param p_uiSize      数组最大长度
     * @param p_sFilePath   偏心表文件路径
     * @param p_sSplit      分割符
     * @code
     *
     * @endcode
     * @return [true] \n
     * [读取成功]
     * @code
     *
     * @endcode
     * @return [false] \n
     * [读取失败]
     *
     */
    virtual bool loadLevelAngleData_(int* p_cData,
                                     uint32_t p_uiSize,
                                     std::string p_sFilePath,
                                     std::string p_sSplit = ",");

    /**
     * @brief 计算用于垂直修正的sin/cos
     * @param p_aVerAngData 垂直角度表
     * @param p_aSinData    sin表待填充
     * @param p_aCosData    cos表待填充
     * @param p_uiSize      表长度
     *
     */
    virtual void
    setVelAngleData_(float* p_aVerAngData, float* p_aSinData, float* p_aCosData, uint32_t p_uiSize);

    /**
     * @brief 逐包解算雷达数据
     * @param p_rawData 雷达原始网络数据
     * @param p_f64Time 每个点的时间
     * @param s_PCloud  原始点云
     * @param pmid      中间线点云
     * @param pcOut     输出点云
     *
     */
    virtual void unpack_(const s_LIDAR_RAW_DATA::DArray& p_rawData,
                         double& p_f64Time,
                         s_PCloud (&pc)[WLR720_SCANS_PER_FIRING],
                         s_PCloud& pmid,
                         PCOutPtr& pcOut);
    virtual void unpackIMU_(int offset,
                            const std::vector<s_LIDAR_RAW_DATA>& p_rawData,
                            wj_slam::IMUData& imuData);

    /**
     * @brief 逐包解算雷达数据中的附加数据
     * @param p_rawData 雷达原始网络数据
     * @param padd      附加消息 GNSS+IMU
     *
     */
    virtual void unpackAddMsg_(const s_LIDAR_RAW_DATA::DArray& p_rawData, AddtionMsgPtr& padd);
#pragma endregion
};
#pragma endregion

}  // namespace wanji_driver
#include "impl/convert.hpp"
#endif