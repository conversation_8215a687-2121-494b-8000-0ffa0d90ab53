// local
#include "../convert.h"
#include "common/config/conf_timer.h"
#include "common/time_consuming.hpp"
namespace wanji_driver {

#pragma region 基类
Convert::Convert(uint32_t p_uiId)
    : c_uiLidarID_(p_uiId), c_stSysParam_(wj_slam::SYSPARAM::getIn()),
      c_stLaserCfg_(c_stSysParam_->m_lidar[p_uiId]), c_bUnpcakRun_(false), c_unpackThr_(nullptr),
      c_bImuFirstPkg_(false)
{
    std::string l_sLaserType = c_stLaserCfg_.m_dev.m_sDevType;
    // 初始化雷达盲区
    setBlindZeroAngOffset_();
    // sin/cos 查表初始化
    int sign = (l_sLaserType == "WLR720FCW" || l_sLaserType == "WLR720FCW_NP") ? 1 : -1;
    for (uint32_t rot_index = 0; rot_index < ROTATION_MAX_UNITS; ++rot_index)
    {
        float rotation = ROTATION_RESOLUTION * rot_index * D2R;
        c_afCosRotTable_[rot_index] = sign * cosf(rotation);
        c_afSinRotTable_[rot_index] = sinf(rotation);
    }
    c_imuCb_.clear();
}

Convert::~Convert()
{
    stop();
}

void Convert::setTransToBase()
{
    std::string l_sStr = "lidar[" + c_stLaserCfg_.m_sLaserName + "] transBase";
    c_stLaserCfg_.m_transToBase.printf(l_sStr);
    // 蹭这个接口设置雷达盲区
    setBlindZeroAngOffset_();
}

void Convert::start()
{
    stop();
    c_bUnpcakRun_ = true;
    c_bImuFirstPkg_ = false;
    c_unpackThr_.reset(new std::thread(&Convert720::run, this));
}

void Convert::stop()
{
    c_bUnpcakRun_ = false;
    if (c_unpackThr_ && c_unpackThr_->joinable())
    {
        c_unpackThr_->join();
    }
    c_unpackThr_ = nullptr;
}

bool Convert::isStop()
{
    return !c_bUnpcakRun_;
}

void Convert::shutdown()
{
    stop();
}

bool Convert::isShutdown()
{
    return isStop();
}

void Convert::run()
{
    int l_iCurrID = -1;
    uint32_t l_uiPkgNum = 0;
    wj_slam::sTimeval l_PktFirstTime;
    while (c_bUnpcakRun_)
    {
        if (hasPointCloud_())
        {
            st_TWithTS l_pRawData = getRawData_();
            l_iCurrID = l_pRawData.m_iFrameId;
            l_uiPkgNum = l_pRawData.m_data->m_vPackets.size();
            //获取雷达时间
            if (getSyncTime_(l_pRawData.m_data->m_vPackets[0], l_PktFirstTime))
            {
                if (!c_stSysParam_->m_time.isSensorTimeSet())
                {
                    c_stSysParam_->m_time.setSensorBaseTime(l_PktFirstTime);
                    LOGFAE(WINFO,
                           "{} 雷达 [{}] 帧 {} 授时, syncTime {}",
                           WJLog::getWholeSysTime(),
                           c_stLaserCfg_.m_sLaserName,
                           l_iCurrID,
                           c_stSysParam_->m_time.getSensorBaseTime().data());
                }
                if (c_stSysParam_->m_time.isSensorTimeSet())
                {
                    // 获取此帧首包雷达内部时间戳
                    l_pRawData.m_tsSyncTime =
                        l_PktFirstTime.getDiffMs(c_stSysParam_->m_time.getSensorBaseTime());
                    // 获取此帧首包系统接收时间戳
                    l_pRawData.m_tsWallTime =
                        l_pRawData.m_data->m_vPackets[0].m_stRecvTimeval.getDiffMs(
                            c_stSysParam_->m_time.getSystemBaseTime());
                    unpackData_(l_pRawData, l_iCurrID);
                }
            }
        }
        else
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    }

    while (!c_rawDataBuffer_.empty())
    {
        printf("lidar[%d] pop\n", c_uiLidarID_);
        c_rawDataBuffer_.pop();
    }
}

void Convert::processScan(boost::shared_ptr<s_LIDAR_RAW_DATAS> p_pScanMsg,
                          int p_iFrameId,
                          bool isReNewConnect)
{
    // 只放入队列，不处理，节省时间
    if (c_bUnpcakRun_)
    {
        st_TWithTS l_stTwithTs;
        l_stTwithTs.m_iFrameId = p_iFrameId;
        l_stTwithTs.m_data = p_pScanMsg;
        l_stTwithTs.m_bIsReConnect = isReNewConnect;
        c_rawDataBuffer_.push(l_stTwithTs);
    }
}

bool Convert::hasPointCloud_()
{
    return !c_rawDataBuffer_.empty();
}

st_TWithTS Convert::getRawData_()
{
    st_TWithTS l_pRawData;
    l_pRawData = c_rawDataBuffer_.front();
    c_rawDataBuffer_.pop();
    return l_pRawData;
}

void Convert::saveErrorLogFile_(std::string p_sPath,
                                std::string p_sLaserName,
                                int p_iLastTime,
                                int p_iCurTime,
                                int p_iCurID)
{
    time_t t = time(NULL);
    char ch[64] = {0};
    char result[100] = {0};
    strftime(ch, sizeof(ch) - 1, "%Y_%m_%d_%H:%M_%S", localtime(&t));  //%Y%m%d%H%M%S
    sprintf(result, "%s", ch);
    std::string l_sNow = result;

    std::fstream l_filePoseWR;
    l_filePoseWR.open(p_sPath.c_str(), std::ios::app);
    if (l_filePoseWR.is_open())
    {
        l_filePoseWR << p_sLaserName << "," << c_stLaserCfg_.m_uiFrameTimeMax << "," << l_sNow
                     << "," << p_iCurTime - p_iLastTime << "," << p_iCurID << std::endl;
    }
    l_filePoseWR.close();
}

bool Convert::isValidData_(u_int& p_uiIntensity, float& p_fDist, float& p_fAzimuthAngle)
{
    if (p_fDist < c_stLaserCfg_.m_fMinDist || p_fDist > c_stLaserCfg_.m_fMaxDist)
        return false;
    // if (p_uiIntensity < c_stLaserCfg_.m_uiMinIntensity)
    //     return false;
    // 顺时针角度逆时针化
    angAntiRotate_(p_fAzimuthAngle);
    for (int i = 0; i < (int)c_vstBlindInfo_.size(); i++)
    {
        if (p_fAzimuthAngle < c_vstBlindInfo_[i].m_fEndAng
            && p_fAzimuthAngle > c_vstBlindInfo_[i].m_fStartAng)
        {
            return false;
        }
    }
    return true;
}

void Convert::setBlindZeroAngOffset_()
{
    /*
    假设雷达线头朝下，盲区角度设定均为从720FCW 0 deg逆时针开始
      720FCW                               720F
        90                                  90
    180     0                           180     0
        270                                 270
        |                                   |
        |                                   |
    但720FCW 雷达角度从Y+顺时针旋转     720F 雷达角度从Y+ 逆时针旋转
      720FCW                               720F
        0                                   180
    270     90                          270     90
        180                                 0
        |                                   |
        |                                   |
    此处先不考虑旋转方向，认为雷达和盲区方向一致，为逆时针
      720FCW                               720F
        0
    90      270
        180
    将盲区设定 旋转 满足 雷达坐标系
      720FCW                               720F
      +270deg                             +270deg
    */
    using BlindConfigVec = std::vector<wj_slam::s_BlindConfig>;

    BlindConfigVec l_vstBlindInfo = c_stLaserCfg_.m_vBlindSector;
    BlindConfigVec l_vstAddBlindInfo;

    for (int i = 0; i < (int)l_vstBlindInfo.size(); i++)
    {
        l_vstBlindInfo[i].m_fStartAng += c_stLaserCfg_.m_uiBlindZeroDegOffset;
        l_vstBlindInfo[i].m_fEndAng += c_stLaserCfg_.m_uiBlindZeroDegOffset;
        // 此处为了保证(0,360]
        if (l_vstBlindInfo[i].m_fStartAng >= 360.0)
            l_vstBlindInfo[i].m_fStartAng -= 360.0;
        if (l_vstBlindInfo[i].m_fEndAng > 360.0)
            l_vstBlindInfo[i].m_fEndAng -= 360.0;
        // eg: [set]-[90,300] [align]-[270,120] == [270,0] [120];
        if (l_vstBlindInfo[i].m_fStartAng > l_vstBlindInfo[i].m_fEndAng)
        {
            //此情况一般发生与engAng>180,此时分割盲区
            wj_slam::s_BlindConfig l_addBlind;
            l_addBlind.m_fStartAng = 0;
            l_addBlind.m_fEndAng = l_vstBlindInfo[i].m_fEndAng;
            l_vstBlindInfo[i].m_fEndAng = 360.0;
            l_vstAddBlindInfo.push_back(l_addBlind);
        }
    }

    // 新增盲区
    if (l_vstAddBlindInfo.size())
        l_vstBlindInfo.insert(
            l_vstBlindInfo.end(), l_vstAddBlindInfo.begin(), l_vstAddBlindInfo.end());

    c_vstBlindInfo_ = l_vstBlindInfo;
}

#pragma endregion

#pragma region 720类
Convert720::Convert720(uint32_t p_uiLidarID, FeatureCb p_fECb)
    : Convert(p_uiLidarID), c_fECb_(p_fECb)
{
    memset(c_afVertAngle_, 0, sizeof(c_afVertAngle_));
    memset(c_afAzimuthDiff_, 0, sizeof(c_afAzimuthDiff_));
    if (!setup_())
    {
        c_stLaserCfg_.m_dev.setStatus(wj_slam::DevStatus::LOADPARAMERROR);
        LOGFAE(WERROR, "SLAM模块驱动初始化失败，无法运行 | 请按照以下步骤进行检查：");
        LOGFAE(WERROR, " *********************************************** ");
        LOGFAE(WERROR, " * 1. 在线模式下请确认雷达是否正确连接 ");
        LOGFAE(WERROR,
               " * 2. "
               "2022年以前的机器徐手动拷贝对应雷达的修正表1/修正表2,并按照以下命名规则更改名称： ");
        LOGFAE(WERROR, " *       修正表1：雷达类型_雷达名.csv(eg:WLR720FCW_front.csv)");
        LOGFAE(WERROR, " *       修正表2：雷达类型_雷达名.txt_PX(eg: WLR720FCW_front.txt_PX) ");
        LOGFAE(WERROR,
               " * 3. 更改后将文件存入/wanjislam/src/wanji_slam /data/Wanji_lidar_16文件夹下");
        LOGFAE(WERROR, " * **********************************************");
        c_stSysParam_->m_fae.setErrorCode("C17");
        return;
    }
    start();
}

Convert720::~Convert720() {}

#pragma region 基类纯虚函数

void Convert720::angAntiRotate_(float& p_fAzimuthAngle)
{
    if (!c_stLaserCfg_.m_bIsAntiRotate)
    {
        p_fAzimuthAngle = 360.0 - p_fAzimuthAngle;
        // 角度约束[0,360)
        if (p_fAzimuthAngle >= 360.0)
            p_fAzimuthAngle -= 360.0;
        else if (p_fAzimuthAngle < 0.0)
            p_fAzimuthAngle += 360.0;
    }
}

void Convert720::unpackData_(st_TWithTS& p_pScanMsg, int p_iCurId)
{
    TicToc tt;
    for (int i = 0; i < WLR720_SCANS_PER_FIRING; i++)
        c_pcRawOut_[i].reset();

    s_PCloud l_pcMidOut(true);
    PCOutPtr l_pcAll(new PCOut());
    wj_slam::IMUPerScan l_vImuData;

    double l_f64Time = 0.0f;  // 各个点的time 用于偏心修正
    for (size_t i = 0; i < p_pScanMsg.m_data->m_vPackets.size(); i = i + 12)
    {
        wj_slam::IMUData l_imuPack;
        unpackIMU_(i, p_pScanMsg.m_data->m_vPackets, l_imuPack);
        l_imuPack.scanId() = p_iCurId;
        l_vImuData.push_back(l_imuPack);
    }
    for (size_t i = 0; i < p_pScanMsg.m_data->m_vPackets.size(); ++i)
    {
        s_LIDAR_RAW_DATA& l_stData = p_pScanMsg.m_data->m_vPackets[i];
        unpack_(l_stData.m_data,
                l_f64Time,
                c_pcRawOut_,
                l_pcMidOut,
                l_pcAll);  // hsq：解析数据存入c_pcRawOut_、l_pcMidOut、l_pcAll（未使用）
    }
    if (c_imuCb_)
        c_imuCb_(c_uiLidarID_, l_vImuData);
    // 首包取出附加数据
    unpackAddMsg_(p_pScanMsg.m_data->m_vPackets[0].m_data, l_pcMidOut.m_pinfo);
    
    COST_TIME_UNPACK(tt.toc());
    LOGCONV(WDEBUG,
            "{} [UNPACK] 帧[{}] Unapck Cost Time {:.3f}",
            WJLog::getWholeSysTime(),
            p_iCurId,
            tt.toc());

    // 确认点数非空
    // if (checkLines_(c_pcRawOut_, p_iCurId))
        // c_fECb_(c_uiLidarID_,
        //         c_pcRawOut_,
        //         l_pcMidOut,
        //         l_pcAll,
        //         p_pScanMsg.m_tsSyncTime,
        //         p_pScanMsg.m_tsWallTime,
        //         p_iCurId);
}

bool Convert720::checkLines_(s_PCloud (&p_rawOut)[WLR720_SCANS_PER_FIRING], int p_iCurId)
{
    std::vector<int> l_viZeroLineList;
    int l_iPointSum_ = 0;
    for (int i = 0; i < WLR720_SCANS_PER_FIRING; i++)
    {
        if (p_rawOut[i].m_praw->size() == 0)
            l_viZeroLineList.push_back(i);
        l_iPointSum_ += p_rawOut[i].m_praw->size();
    }
    if (l_iPointSum_ < c_stLaserCfg_.m_uiPointMinNum
        || l_viZeroLineList.size() == WLR720_SCANS_PER_FIRING)
    {
        c_stLaserCfg_.m_dev.setStatus(wj_slam::DevStatus::DATADATAERROR);
        // c_stSysParam_->m_fae.setErrorCode("C18");
        LOGFAE(WERROR,
               "{} 雷达 [{}] 扫描异常 | 帧[{}] 点数过少 {}/{},已丢弃",
               WJLog::getWholeSysTime(),
               c_stLaserCfg_.m_sLaserName,
               p_iCurId,
               l_iPointSum_,
               c_stLaserCfg_.m_uiPointMinNum);
        LOGFAE(WERROR, " *********************************************** ");
        LOGFAE(WERROR, " * 1. 确认雷达遮光罩没有被障碍物遮挡 ");
        LOGFAE(WERROR, " * 2. 连接web客户端查看[雷达参数配置]中雷达距离盲区是否在合理值范围");
        LOGFAE(WERROR, " *       WLR720FCW：MIN:1.5m---->MAX:70m");
        LOGFAE(WERROR, " *       WLR720F：MIN:0.5m---->MAX:70m ");
        LOGFAE(WERROR,
               " * 3. 如果上述操作还未解决，请打开wireshark软件录制数据包，联系万集开发人员");
        LOGFAE(WERROR, " * **********************************************");
        return false;
    }
    else
    {
        if (c_stLaserCfg_.m_dev.m_status == wj_slam::DevStatus::DATADATAERROR)
            c_stLaserCfg_.m_dev.setStatus(wj_slam::DevStatus::DEVCONNECT);
        if (l_viZeroLineList.size() > (int)(WLR720_SCANS_PER_FIRING * 0.2))
        {
            // 16线 超过3线有线点数为0，则提醒
            for (int i = 0; i < (int)l_viZeroLineList.size(); i++)
                LOGFAE(WWARN,
                       "{} 雷达 [{}] 扫描异常 | 帧[{}] 线[{}] 点数为空",
                       WJLog::getWholeSysTime(),
                       c_stLaserCfg_.m_sLaserName,
                       p_iCurId,
                       l_viZeroLineList[i]);
            LOGFAE(WWARN,
                   "{} 雷达 [{}] 扫描异常 | 若存在遮挡雷达,可忽略此问题,否则请检查!",
                   WJLog::getWholeSysTime(),
                   c_stLaserCfg_.m_sLaserName);
        }
    }
    return true;
}

bool Convert720::checkDataHeader_(st_TWithTS& p_pScanMsg, int& p_iCurId)
{
    return true;
}

wj_slam::sTimeval Convert720::getPktDataTime_(const s_LIDAR_RAW_DATA& p_rawData)
{
    wj_slam::sTimeval l_DataTime;
    const st_RawPacket* raw = (const st_RawPacket*)&p_rawData.m_data[0];
    // gnss/lidar
    // 由雷达设备决定：有GNS则GNS，否则雷达内部计
    tm l_time = {
        .tm_sec = raw->addmsg.time[0],         //秒
        .tm_min = raw->addmsg.time[1],         //分
        .tm_hour = raw->addmsg.time[2],        //时
        .tm_mday = raw->addmsg.time[3],        //日
        .tm_mon = raw->addmsg.time[4] - 1,     //月
        .tm_year = raw->addmsg.time[5] + 100,  //年
    };
    l_DataTime.set(mktime(&l_time),
                   (((raw->addmsg.nsec[3] & 0x0F) << 24) + (raw->addmsg.nsec[2] << 16)
                    + (raw->addmsg.nsec[1] << 8) + raw->addmsg.nsec[0])
                       / 100);
    return l_DataTime;
}

wj_slam::sTimeval Convert720::getSyncTime_(const s_LIDAR_RAW_DATA& p_rawData)
{
    wj_slam::sTimeval l_DataTime;
    switch (c_stSysParam_->m_time.timeSource())
    {
        case TimeSource::Local: l_DataTime = p_rawData.m_stSyncTimeval; break;
        case TimeSource::Sensor: l_DataTime = getPktDataTime_(p_rawData); break;

        default: break;
    }
    return l_DataTime;
}

bool Convert720::getSyncTime_(const s_LIDAR_RAW_DATA& p_rawData, wj_slam::sTimeval& p_timeval)
{
    p_timeval = getSyncTime_(p_rawData);
    if (p_timeval.second() || p_timeval.subsecond())
        return true;
    p_timeval.reset();
    std::cerr << "[Error] Lidar [" << c_stSysParam_->m_lidar[c_uiLidarID_].m_sLaserName
              << "] is not Timing" << std::endl;
    return false;
}
#pragma endregion

#pragma region 保护函数
bool Convert720::setup_()
{
    bool l_bRes_ = true;
    // 根据雷达转速更改值
    switch (c_stLaserCfg_.m_uiRPM)
    {
        case 300:  // 5hz
            c_fAngleResolutionVal_ = 0.1;
            c_fLineAngleGap_ = 0.0025;
            break;
        case 600:  // 10hz
            c_fAngleResolutionVal_ = 0.2;
            c_fLineAngleGap_ = 0.005;
            break;
        case 900:  // 15hz
            c_fAngleResolutionVal_ = 0.3;
            c_fLineAngleGap_ = 0.0075;
            break;
        case 1200:  // 20hz
            c_fAngleResolutionVal_ = 0.4;
            c_fLineAngleGap_ = 0.01;
            break;
        default:
            c_fAngleResolutionVal_ = 0;
            c_fLineAngleGap_ = 0;
            break;
    }
    c_fGroupAngleVal_ = c_fAngleResolutionVal_ / 4;

    std::string l_sFilePath = "_" + c_stLaserCfg_.m_sLaserName;

    float l_finitVerAg[SCANS_PER_BLOCK] = {
        0, 14, 12, 10, 8, 0, 6, 4, 2, 0, -2, -4, -6, -8, 0, -10, -12, -14, -16};
    memcpy(c_afVertAngle_, l_finitVerAg, sizeof(c_afVertAngle_));

    // 读取垂直表: 垂直角 航向角
    l_bRes_ = loadVelAngleData_(c_afVertAngle_,
                                c_afAzimuthDiff_,
                                SCANS_PER_BLOCK,
                                c_stLaserCfg_.m_sCalibrationFile + l_sFilePath + ".csv")
              && l_bRes_;

    // 计算垂直sin/cos
    setVelAngleData_(c_afVertAngle_, c_afVsinRotTable_, c_afVcosRotTable_, SCANS_PER_BLOCK);

    // 读取偏心表,不存在则设置默认偏心
    l_bRes_ = loadLevelAngleData_(c_aiEccentricity_,
                                  MIDLINE_POINT_SIZE,
                                  c_stLaserCfg_.m_sCalibrationFile + l_sFilePath + ".txt_PX")
              && l_bRes_;
    return l_bRes_;
}

bool Convert720::loadVelAngleData_(float* p_cVerAngData,
                                   float* p_cDiffData,
                                   uint32_t p_uiSize,
                                   std::string p_sFilePath)
{
    ifstream l_file;
    l_file.open(p_sFilePath.c_str());
    uint32_t l_uiRowId = 0;

    if (!l_file)
    {
        LOGFAE(WERROR,
               "{} 雷达 [{}]  加载修正表2异常 | 文件[{}]损坏/不存在,请按照以下步骤检查：",
               WJLog::getWholeSysTime(),
               c_stLaserCfg_.m_sLaserName,
               p_sFilePath.c_str());
        LOGFAE(WERROR, " *********************************************** ");
        LOGFAE(WERROR,
               " * 1. 在线模式下为自动获取修正表，如果文件不存在请优先检查雷达是否正确连接 ");
        LOGFAE(WERROR,
               " * 2. "
               "2022年以前的机器徐手动拷贝对应雷达的修正表1/修正表2,并按照以下命名规则更改名称： ");
        LOGFAE(WERROR, " *       修正表1：雷达类型_雷达名.csv(eg:WLR720FCW_front.csv)");
        LOGFAE(WERROR, " *       修正表2：雷达类型_雷达名.txt_PX(eg: WLR720FCW_front.txt_PX) ");
        LOGFAE(WERROR,
               " * 3. 如果上述操作还未解决，请打开wireshark软件录制数据包，联系万集开发人员");
        LOGFAE(WERROR, " * **********************************************");
        c_stSysParam_->m_fae.setErrorCode("C19");
        return false;
    }
    else
    {
        string l_sReadLine, mem, key;
        unsigned int l_uiInd = 0;
        while (true)
        {
            getline(l_file, l_sReadLine);
            if (l_file.fail())
                break;
            // 遍历该行字符串 分割',' key ，mem
            while (l_uiInd < l_sReadLine.length())
            {
                if (l_sReadLine[l_uiInd] == ',')
                {
                    key = mem;
                    mem.clear();
                    l_uiInd++;
                }
                else
                    mem += l_sReadLine[l_uiInd++];
            }
            istringstream isAng(key);
            isAng >> *(p_cVerAngData + l_uiRowId);
            istringstream isAzimuth(mem);
            isAzimuth >> *(p_cDiffData + l_uiRowId);
            l_uiRowId++;
            mem.clear();
            key.clear();
            l_uiInd = 0;
        }
        l_file.close();
    }
    return true;
}

void Convert720::setVelAngleData_(float* p_aVerAngData,
                                  float* p_aSinData,
                                  float* p_aCosData,
                                  uint32_t p_uiSize)
{
    for (uint32_t i = 0; i < p_uiSize; i++)
    {
        *(p_aSinData + i) = sin(*(p_aVerAngData + i) * M_PI / 180.0);
        *(p_aCosData + i) = cos(*(p_aVerAngData + i) * M_PI / 180.0);
    }
}

bool Convert720::loadLevelAngleData_(int* p_cData,
                                     uint32_t p_uiSize,
                                     std::string p_sFilePath,
                                     std::string p_sSplit)
{
    std::ifstream l_file;
    l_file.open(p_sFilePath.c_str());
    if (!l_file)
    {
        LOGFAE(WERROR,
               "{} 雷达 [{}]  加载修正表1异常 | 文件[{}]损坏/不存在,请按照以下步骤检查：",
               WJLog::getWholeSysTime(),
               c_stLaserCfg_.m_sLaserName,
               p_sFilePath.c_str());
        c_stSysParam_->m_fae.setErrorCode("C20");
        LOGFAE(WERROR, " *********************************************** ");
        LOGFAE(WERROR,
               " * 1. 在线模式下为自动获取修正表，如果文件不存在请优先检查雷达是否正确连接 ");
        LOGFAE(WERROR,
               " * 2. "
               "2022年以前的机器徐手动拷贝对应雷达的修正表1/修正表2,并按照以下命名规则更改名称： ");
        LOGFAE(WERROR, " *       修正表1：雷达类型_雷达名.csv(eg:WLR720FCW_front.csv)");
        LOGFAE(WERROR, " *       修正表2：雷达类型_雷达名.txt_PX(eg: WLR720FCW_front.txt_PX) ");
        LOGFAE(WERROR,
               " * 3. 如果上述操作还未解决，请打开wireshark软件录制数据包，联系万集开发人员");
        LOGFAE(WERROR, " * **********************************************");
        memset(p_cData, 0, p_uiSize);
        return false;
    }
    else
    {
        std::string str;
        unsigned int ie = 0;
        while (true)
        {
            getline(l_file, str);

            if (l_file.fail())
                break;
            char* p = strtok((char*)str.data(), p_sSplit.c_str());  //逗号分隔依次取出
            while (p != NULL)
            {
                if (ie >= p_uiSize)
                {
                    LOGFAE(WERROR,
                           "{} 雷达 [{}] 加载修正表1异常 | 文件[{}]格式异常,请检查修正表1文件！",
                           WJLog::getWholeSysTime(),
                           c_stLaserCfg_.m_sLaserName,
                           p_sFilePath.c_str());
                    //用0 替换 p_uiSsze个p_cData
                    memset(p_cData, 0, p_uiSize);
                    l_file.close();
                    return false;
                }
                sscanf(p, "%d", p_cData + ie);  // char ---> int
                ie++;
                p = strtok(NULL, p_sSplit.c_str());
            }
        }
        l_file.close();
    }
    return true;
}

void Convert720::unpackIMU_(int offset,
                            const std::vector<s_LIDAR_RAW_DATA>& p_rawData,
                            wj_slam::IMUData& imuData)
{
    const st_RawPacket* raw = (const st_RawPacket*)&p_rawData[offset].m_data[0];
    float l_fTime;
    l_fTime = ((raw->blocks[0].rotation % 36000) / 300) * BLOCKS_PER_PACKET * SCANS_PER_BLOCK
              * WLR720_POINT_TDURATION;
    const uint8_t* pbuf = nullptr;
    pbuf = (const uint8_t*)&raw->addmsg;
    float groyX = 0, groyY = 0, groyZ = 0, accX = 0, accY = 0, accZ = 0;
    groyX =
        8.75 * ((short)(pbuf[14] | (((pbuf[15]) << 8) & 0xff00))) / 1000 * 0.0174533;  // * 8.75;
    groyY =
        8.75 * ((short)(pbuf[16] | (((pbuf[17]) << 8) & 0xff00))) / 1000 * 0.0174533;  // * 8.75;
    groyZ =
        8.75 * ((short)(pbuf[18] | (((pbuf[19]) << 8) & 0xff00))) / 1000 * 0.0174533;  // * 8.75;
    accX = 0.061 * ((short)(pbuf[20] | (((pbuf[21] << 8) & 0xff00)))) / 1000 * 9.81;   // * 0.061;
    accY = 0.061 * ((short)(pbuf[22] | (((pbuf[23] << 8) & 0xff00)))) / 1000 * 9.81;   // * 0.061;
    accZ = 0.061 * ((short)(pbuf[24] | (((pbuf[25] << 8) & 0xff00)))) / 1000 * 9.81;   // * 0.061;
    imuData.linearAcceleration() << accX, accY, accZ;
    imuData.angularVelocity() << groyX, groyY, groyZ;
    if (c_bImuFirstPkg_)
        imuData.imuTime() = getPktDataTime_(p_rawData[offset]).getDiffMs(c_tvLastIMUPktTime_);
    else
    {
        imuData.imuTime() = 0;
        c_tvLastIMUPktTime_ = getPktDataTime_(p_rawData[offset]);
        c_bImuFirstPkg_ = true;
    }
    imuData.imuRecvTime() =
        p_rawData[offset].m_stRecvTimeval.getDiffMs(c_stSysParam_->m_time.getSystemBaseTime());
    imuData.syncTime() =
        getSyncTime_(p_rawData[offset]).getDiffMs(c_stSysParam_->m_time.getSensorBaseTime());
    wj_slam::sTimeval tmp = p_rawData[offset].m_stSyncTimeval;
}

// hsq: pcOut未使用？
void Convert720::unpack_(const s_LIDAR_RAW_DATA::DArray& p_rawData,
                         double& p_f64Time,
                         s_PCloud (&pc)[WLR720_SCANS_PER_FIRING],
                         s_PCloud& pmid,
                         PCOutPtr& pcOut)
{
    const st_RawPacket* raw = (const st_RawPacket*)&p_rawData[0];
    POINTTYPEOUT l_pointOut; // hsq: 未使用
    PRaw pointRaw;  // pcl::PointXYZ
    PAdd pointAdd;

    float l_fDist_xyz = 0, l_fDist_xy = 0;
    Eigen::Vector3d l_Dist_xyz(0, 0, 0);
    uint32_t l_fIntensity = 0;
    float l_fAzimuthAngle = 0;
    // 乱序包 首点更新时间
    p_f64Time = ((raw->blocks[0].rotation % 36000) / 300) * BLOCKS_PER_PACKET * SCANS_PER_BLOCK
                * WLR720_POINT_TDURATION;
    // 15包
    for (int i = 0; i < BLOCKS_PER_PACKET; i++)
    {
        int scanID = 0;
        // 19块
        for (int j = 0, k = 0; j < SCANS_PER_BLOCK; j++, k += RAW_SCAN_SIZE)
        {
            // 解析获取距离信息 单位4mm -> m
            union two_bytes tmp;
            tmp.bytes[0] = raw->blocks[i].data[k];
            tmp.bytes[1] = raw->blocks[i].data[k + 1];
            l_fDist_xyz = tmp.uint * DISTANCE_RESOLUTION;
            l_fDist_xyz += c_stLaserCfg_.m_fDistComp;
            // 解析强度值(脉宽)
            l_fIntensity = raw->blocks[i].data[k + 3];

            // 角度 单位deg
            float first_Angazimuth = (raw->blocks[i].rotation) / 100.0 - c_fAngleResolutionVal_;
            float second_Angazimuth = first_Angazimuth + c_fGroupAngleVal_;
            float third_Angazimuth = first_Angazimuth + 2 * c_fGroupAngleVal_;
            float fourth_Angazimuth = first_Angazimuth + 3 * c_fGroupAngleVal_;

            switch (j)
            {
                case 0: l_fAzimuthAngle = (first_Angazimuth); break;
                case 1: l_fAzimuthAngle = (first_Angazimuth + c_fLineAngleGap_); break;
                case 2: l_fAzimuthAngle = (first_Angazimuth + 2 * c_fLineAngleGap_); break;
                case 3: l_fAzimuthAngle = (first_Angazimuth + 3 * c_fLineAngleGap_); break;
                case 4: l_fAzimuthAngle = (first_Angazimuth + 4 * c_fLineAngleGap_); break;
                case 5: l_fAzimuthAngle = (second_Angazimuth); break;
                case 6: l_fAzimuthAngle = (second_Angazimuth + c_fLineAngleGap_); break;
                case 7: l_fAzimuthAngle = (second_Angazimuth + 2 * c_fLineAngleGap_); break;
                case 8: l_fAzimuthAngle = (second_Angazimuth + 3 * c_fLineAngleGap_); break;
                case 9: l_fAzimuthAngle = (third_Angazimuth); break;
                case 10: l_fAzimuthAngle = (third_Angazimuth + c_fLineAngleGap_); break;
                case 11: l_fAzimuthAngle = (third_Angazimuth + 2 * c_fLineAngleGap_); break;
                case 12: l_fAzimuthAngle = (third_Angazimuth + 3 * c_fLineAngleGap_); break;
                case 13: l_fAzimuthAngle = (third_Angazimuth + 4 * c_fLineAngleGap_); break;
                case 14: l_fAzimuthAngle = (fourth_Angazimuth); break;
                case 15: l_fAzimuthAngle = (fourth_Angazimuth + c_fLineAngleGap_); break;
                case 16: l_fAzimuthAngle = (fourth_Angazimuth + 2 * c_fLineAngleGap_); break;
                case 17: l_fAzimuthAngle = (fourth_Angazimuth + 3 * c_fLineAngleGap_); break;
                case 18: l_fAzimuthAngle = (fourth_Angazimuth + 4 * c_fLineAngleGap_); break;
                default: break;
            }

            // 真实角度计算
            l_fAzimuthAngle += 0.05 * c_aiEccentricity_[int(l_fAzimuthAngle * 20 + 7200) % 7200];
            // 角度约束[0,360)
            l_fAzimuthAngle = l_fAzimuthAngle < 360.0 ? l_fAzimuthAngle : l_fAzimuthAngle - 360.0;
            l_fAzimuthAngle = l_fAzimuthAngle < 0.0 ? l_fAzimuthAngle + 360.0 : l_fAzimuthAngle;

            // 扫描线束计算
            if (j == 0 || j == 5 || j == 9 || j == 14)
                scanID = 8;
            if (j > 0 && j < 5)
                scanID = 16 - j;
            if (j > 5 && j < 14)
                scanID = 17 - j;
            if (j > 14 && j <= 18)
                scanID = 18 - j;

            int l_iRotIdx = int(l_fAzimuthAngle * ROTATION_RESOLUTION_INV) % ROTATION_MAX_UNITS;
            l_fDist_xy = l_fDist_xyz * c_afVcosRotTable_[j];
            l_Dist_xyz.z() = l_fDist_xyz * c_afVsinRotTable_[j];
            l_Dist_xyz.x() = l_fDist_xy * c_afSinRotTable_[l_iRotIdx];
            l_Dist_xyz.y() = l_fDist_xy * c_afCosRotTable_[l_iRotIdx];

            // 仿射变换矩阵
            l_Dist_xyz = c_stLaserCfg_.m_transToBase * l_Dist_xyz;
            p_f64Time += WLR720_POINT_TDURATION;

            pointRaw.x = l_Dist_xyz.x();
            pointRaw.y = l_Dist_xyz.y();
            pointRaw.z = l_Dist_xyz.z();
            pointAdd.intensity = l_fIntensity;
            pointAdd.xydist = l_fDist_xy;
            pointAdd.depth = l_fDist_xyz;
            pointAdd.ang = l_fAzimuthAngle;
            pointAdd.time = p_f64Time;

            // 中间线点云
            if (scanID == 8)
            {
                pmid.m_praw->points.push_back(pointRaw);
                pmid.m_padd->push_back(pointAdd);
            }
            if ((j == 5) || (j == 9) || (j == 14))
            {
                continue;
            }

            if (!isValidData_(l_fIntensity, l_fDist_xyz, l_fAzimuthAngle))
                continue;
            if (!c_stLaserCfg_.m_bUseFloor && l_Dist_xyz.z() < -c_stLaserCfg_.m_fFeatureHeight)
                continue;

            pc[scanID].m_praw->points.push_back(pointRaw);
            pc[scanID].m_padd->push_back(pointAdd);
        }
    }
}

void Convert720::unpackAddMsg_(const s_LIDAR_RAW_DATA::DArray& p_rawData, AddtionMsgPtr& padd)
{
    const st_RawPacket* raw = (const st_RawPacket*)&p_rawData[0];

    // gnss
    tm l_time = {
        .tm_sec = raw->addmsg.time[0],         //秒
        .tm_min = raw->addmsg.time[1],         //分
        .tm_hour = raw->addmsg.time[2],        //时
        .tm_mday = raw->addmsg.time[3],        //日
        .tm_mon = raw->addmsg.time[4] - 1,     //月
        .tm_year = raw->addmsg.time[5] + 100,  //年
    };
    padd->m_gnssTime.tv_sec = mktime(&l_time);
    padd->m_gnssTime.tv_usec = (((raw->addmsg.nsec[3] & 0x0F) << 24) + (raw->addmsg.nsec[2] << 16)
                                + (raw->addmsg.nsec[1] << 8) + raw->addmsg.nsec[0])
                               / 100;
    padd->m_state[0] = (raw->addmsg.nsec[3] & 0x80) >> 7;
    padd->m_state[1] = (raw->addmsg.nsec[3] & 0x40) >> 6;
    padd->m_state[2] = (raw->addmsg.nsec[3] & 0x30) >> 4;
    /* imu
    角速度数据[rad/s] = 原始数据 * 灵敏度 / 1000 /180 * pi
    加速度数据[m/s^2] = 原始数据 * 灵敏度 / 1000 * gravity 其中gravity = 9.80665
    */
    union two_bytes l_tmpData;
    for (int j = 0; j < 3; ++j)
    {
        l_tmpData.bytes[0] = raw->addmsg.angVel[2 * j];
        l_tmpData.bytes[1] = raw->addmsg.angVel[2 * j + 1];
        padd->m_gyro[j] = l_tmpData.sint * c_fAngScalar;
        l_tmpData.bytes[0] = raw->addmsg.accel[2 * j];
        l_tmpData.bytes[1] = raw->addmsg.accel[2 * j + 1];
        padd->m_accel[j] = l_tmpData.sint * c_fAccScalar;
    }
}
#pragma endregion

#pragma endregion

}  // namespace wanji_driver