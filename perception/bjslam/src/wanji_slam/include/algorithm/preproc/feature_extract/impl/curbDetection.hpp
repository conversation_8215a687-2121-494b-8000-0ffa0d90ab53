/*
 * @Author: qiu
 * @Date: 2021-10-20 14:20:55
 * @LastEditTime: 2022-03-23 15:51:56
 * @LastEditors: <PERSON><PERSON> Chen
 * @Description: 识别提取路沿点
 * @FilePath: /catkin_ws4/src/curb_detection/src/curb_Detection.hpp
 */

#include <Eigen/Dense>
#include <cmath>
#include <iostream>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <vector>

template <typename PointType> class CurbDetector {
  public:
    CurbDetector()
    {
        int ring_idx[16] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15};
        memcpy(ring_idx_, ring_idx, sizeof(ring_idx));
    }

    /**
     * @description: 设置路沿高度
     * @param {float} p_fh 路沿高度
     * @return {*}
     */
    void setHighValue(const float p_fh)
    {
        c_fcurbHigh = p_fh;
    }

    /**
     * @description: 设置雷达高度
     * @param {float} p_fl 雷达高度
     * @return {*}
     */
    void setLidarHigh(const float p_fl)
    {
        c_flidarHigh = p_fl;
    }
    /**
     * @description: 设置车行进方向与雷达X轴逆时针夹角
     * @param {float} p_fangle 逆时针角度大小
     * @return {*}
     */
    void setAngleNum(const float p_fangle)
    {
        c_idetecNum = (p_fangle / 0.2);
    }

    /**
     * @description: 接收点云，并保存一份
     * @param {shared_ptr<pcl::PointCloud<PointType>>} p_cloudIn 点云输入
     * @return {*}
     */
    void setInputCloud(const boost::shared_ptr<pcl::PointCloud<PointType>> p_cloudIn)
    {
        if (nullptr == p_cloudIn)
        {
            std::cout << "Cloud is Empty" << std::endl;
        }
        else
        {
            c_pointIn = p_cloudIn;
        }
    }

    /**
     * @description: 地址加偏移量取值
     * @param {int} p_fn
     * @return {*}
     */
    void setOffset(const int p_fn)
    {
        iOffset = p_fn;
    }

    /**
     * @description: 提取路沿
     * @param {pcl::PointCloud<PointType>} &p_curbOut 输出提取的路沿点云
     * @param {Eigen::MatrixXf} &p_arrCloud 输出拟合直线向量与坐标
     * @return {*}
     */
    void curbDetector(pcl::PointCloud<PointType>& p_curbOut)
    {
        c_cloudCleaned = cleanPoints(c_pointIn);
        c_cloudC = RangePoints(c_pointIn);
        p_curbOut.clear();
        p_curbOutf.clear();
        p_curbOuts.clear();

        rightCloud.clear();
        leftCloud.clear();
        Eigen::MatrixXf p_arrCloud;
        p_arrCloud.resize(4, 2);

        l_roadWidef = 0;
        l_roadWides = 0;

        p_lineOut.clear();

        std::vector<int> l_allInd;
        //计算前三条线
        for (int i = 0; i < 3; i++)
        {
            pcl::PointCloud<PointType> pointsInTheRing = c_cloudCleaned[i];
            pcl::PointCloud<PointType> pointsOutput = c_cloudC[i];

            if (pointsInTheRing.empty() || pointsOutput.empty())
            {
                std::cout << "pointsInTheRing no data" << std::endl;
                continue;
            }
            systemInit();

            getRoadLineId(pointsInTheRing, c_vLinePointsNum[i]);
            getRoadWide(pointsInTheRing);
            // std::cout<<l_roadWidef<<"     "<<l_roadWides<<std::endl;

            corner_rightDown = calcuCornerRightDown(pointsInTheRing, c_vLinePointsNum[i]);
            corner_rightUp = calcuCornerRightUp(pointsInTheRing, c_vLinePointsNum[i]);
            corner_leftUp = calcuCornerLeftUp(pointsInTheRing, c_vLinePointsNum[i]);
            corner_leftDown = calcuCornerLeftDown(pointsInTheRing, c_vLinePointsNum[i]);

            getCurbRightDown(pointsInTheRing, corner_rightDown);
            getCurbRightUp(pointsInTheRing, corner_rightUp);
            getCurbLeftUp(pointsInTheRing, corner_leftUp);
            getCurbLeftDown(pointsInTheRing, corner_leftDown);

            getRoadLineFront(pointsInTheRing, curb_rightup, curb_leftup, line_rightUp, line_leftUp);
            getRoadLineBack(
                pointsInTheRing, curb_rightdown, curb_leftdown, line_rightDown, line_leftDown);

            if (line_front.size() != 0)
            {
                for (int j = 0; j < (int)line_front.size(); j++)
                {
                    p_lineOut.push_back(pointsOutput.points[line_front[j]]);
                    l_allInd.push_back(line_front[j]);
                }
            }
            if (line_back.size() != 0)
            {
                for (int k = 0; k < (int)line_back.size(); k++)
                {
                    p_lineOut.push_back(pointsOutput.points[line_back[k]]);
                    l_allInd.push_back(line_back[k]);
                }
            }

            if (curb_rightdown.size() != 0)
            {
                l_allInd.push_back(curb_rightdown[0]);
                p_curbOutf.push_back(pointsOutput.points[curb_rightdown[0]]);
            }

            if (curb_rightup.size() != 0)
            {
                l_allInd.push_back(curb_rightup[0]);
                p_curbOutf.push_back(pointsOutput.points[curb_rightup[0]]);
            }

            if (curb_leftup.size() != 0)
            {
                l_allInd.push_back(curb_leftup[0]);
                p_curbOutf.push_back(pointsOutput.points[curb_leftup[0]]);
            }

            if (curb_leftdown.size() != 0)
            {
                l_allInd.push_back(curb_leftdown[0]);
                p_curbOutf.push_back(pointsOutput.points[curb_leftdown[0]]);
            }
        }
        //计算第四条线
        for (int i = 3; i < 4; i++)
        {
            pcl::PointCloud<PointType> pointsInTheRing = c_cloudCleaned[i];
            pcl::PointCloud<PointType> pointsOutput = c_cloudC[i];
            systemInit();
            if (pointsInTheRing.empty() || pointsOutput.empty())
                continue;

            corner_rightDown = calcuCornerRightDownFar(pointsInTheRing, c_vLinePointsNum[i]);
            corner_rightUp = calcuCornerRightUpFar(pointsInTheRing, c_vLinePointsNum[i]);
            corner_leftUp = calcuCornerLeftUpFar(pointsInTheRing, c_vLinePointsNum[i]);
            corner_leftDown = calcuCornerLeftDownFar(pointsInTheRing, c_vLinePointsNum[i]);

            getCurbRightDown(pointsInTheRing, corner_rightDown);
            getCurbRightUp(pointsInTheRing, corner_rightUp);
            getCurbLeftUp(pointsInTheRing, corner_leftUp);
            getCurbLeftDown(pointsInTheRing, corner_leftDown);

            if (curb_rightdown.size() != 0)
            {
                l_allInd.push_back(curb_rightdown[0]);
                p_curbOuts.push_back(pointsOutput.points[curb_rightdown[0]]);
            }
            if (curb_rightup.size() != 0)
            {
                l_allInd.push_back(curb_rightup[0]);
                p_curbOuts.push_back(pointsOutput.points[curb_rightup[0]]);
            }
            if (curb_leftup.size() != 0)
            {
                l_allInd.push_back(curb_leftup[0]);
                p_curbOuts.push_back(pointsOutput.points[curb_leftup[0]]);
            }
            if (curb_leftdown.size() != 0)
            {
                l_allInd.push_back(curb_leftdown[0]);
                p_curbOuts.push_back(pointsOutput.points[curb_leftdown[0]]);
            }
        }

        fitCurbLine(rightCloud, leftCloud);
        p_arrCloud = p_arrPoint;

        p_curbOut = p_curbOutf + p_curbOuts;
        // printf("curb size: %d | road size: %d\n", p_curbOut.size(), p_lineOut.size());
        p_curbOut += p_lineOut;
    }

  private:
    boost::shared_ptr<pcl::PointCloud<PointType>> c_pointIn;
    std::vector<pcl::PointCloud<PointType>> c_cloudCleaned;
    std::vector<pcl::PointCloud<PointType>> c_cloudC;
    int c_idetecNum;     //车行进方向的点云分区开始id
    float c_fcurbHigh;   //路沿高度
    float c_flidarHigh;  //雷达高度
    int ring_idx_[16];

    std::vector<int> curb_rightdown;
    std::vector<int> curb_rightup;
    std::vector<int> curb_leftup;
    std::vector<int> curb_leftdown;

    std::vector<int> corner_rightDown;
    std::vector<int> corner_rightUp;
    std::vector<int> corner_leftUp;
    std::vector<int> corner_leftDown;

    std::vector<int> line_rightDown;
    std::vector<int> line_rightUp;
    std::vector<int> line_leftUp;
    std::vector<int> line_leftDown;

    std::vector<int> line_front;
    std::vector<int> line_back;

    pcl::PointCloud<PointType> pcs_in;
    pcl::PointCloud<PointType> p_curbOutf;
    pcl::PointCloud<PointType> p_curbOuts;
    pcl::PointCloud<PointType> p_lineOut;

    Eigen::Vector3d v1_1, v2_1;
    Eigen::Vector3d v1_2, v2_2;
    Eigen::Vector3d v1_3, v2_3;
    Eigen::Vector3d v1_4, v2_4;
    Eigen::Vector3d v3, v4;
    Eigen::Vector3d v5, v6;
    Eigen::Vector3d v7, v8;
    Eigen::Vector3d v9, v10;

    pcl::PointCloud<PointType> rightCloud;
    pcl::PointCloud<PointType> leftCloud;

    Eigen::MatrixXf p_arrPoint;
    float l_roadWidef, l_roadWides;
    int iOffset;

    std::vector<int> c_vLinePointsNum;
    //初始化
    void systemInit()
    {
        curb_rightdown.clear();
        curb_rightup.clear();
        curb_leftup.clear();
        curb_leftdown.clear();

        corner_rightDown.clear();
        corner_rightUp.clear();
        corner_leftUp.clear();
        corner_leftDown.clear();

        line_rightDown.clear();
        line_rightUp.clear();
        line_leftUp.clear();
        line_leftDown.clear();

        line_front.clear();
        line_back.clear();
    }

    /**
     * @description: 计算点云的线号
     * @param {PointType} &p_Line
     * @return {*}
     */
    int whichLinePoints(PointType p_Line)
    {
        double angleVerticl;
        int scanID;

        angleVerticl = atan(p_Line.z / sqrt(p_Line.x * p_Line.x + p_Line.y * p_Line.y));
        scanID = round(angleVerticl / M_PI * 180.0 / 2) + 8;
        return scanID;
    }

    /**
     * @description: 计算点云点的序号
     * @param {PointXYZI} &p_Location
     * @return {*}
     */
    int whichPoints(PointType p_Location)
    {
        double angleHorizon;
        int pointID;

        angleHorizon = atan2(p_Location.y, p_Location.x);
        if (angleHorizon < 0)
            angleHorizon += 2 * M_PI;

        pointID = (angleHorizon / M_PI * 180) / 0.2;

        return pointID;
    }
    /**
     * @description: 点云排序
     * @param {shared_ptr<pcl::PointCloud<PointType>>} p_In 排序前点云
     * @return {std::vector<pcl::PointCloud<PointType>>} l_CloudOutPut 排序后点云
     */
    std::vector<pcl::PointCloud<PointType>>
    RangePoints(const boost::shared_ptr<pcl::PointCloud<PointType>> p_In)
    {
        double angleVerticl;
        int scanID;

        pcl::PointCloud<PointType> l_point;
        std::vector<pcl::PointCloud<PointType>> l_CloudOutPut(16);
        l_point.resize(p_In->points.size());

        for (int i = 0; i < (int)p_In->points.size(); i++)
        {
            angleVerticl = atan(p_In->points[i].z
                                / sqrt(p_In->points[i].x * p_In->points[i].x
                                       + p_In->points[i].y * p_In->points[i].y));
            scanID = round(angleVerticl / M_PI * 180.0 / 2) + 8;

            for (int ring_num = 0; ring_num < 16; ring_num++)
            {
                if (scanID == ring_idx_[ring_num])
                // if (p_In->points[i].ring == ring_idx_[ring_num])
                {
                    l_CloudOutPut[ring_num].push_back(p_In->points[i]);
                }
            }
        }

        for (int j = 0; j < 16; j++)
        {
            c_vLinePointsNum.push_back(l_CloudOutPut[j].points.size());
        }
        return l_CloudOutPut;
    }

    /**
     * @description: 去除临界点、拖尾点
     * @param {shared_ptr<pcl::PointCloud<PointType>>} p_Input
     * @return {std::vector<pcl::PointCloud<PointType>>} l_CloudScans 标记临界点、拖尾点后的点云
     */
    std::vector<pcl::PointCloud<PointType>>
    cleanPoints(const boost::shared_ptr<pcl::PointCloud<PointType>> p_Input)
    {
        int scanID_;

        pcl::PointCloud<PointType> l_point;
        std::vector<pcl::PointCloud<PointType>> l_CloudScans(16);
        if (p_Input->points.size() > 28800)
            std::cout << "The input point size over 16x1800" << std::endl;
        l_point.resize(p_Input->points.size());
        float l_fAngleToDelete = sin(0.2 / 180.0 * M_PI) / sin(10.0 /*可调*/ / 180.0 * M_PI);
        // for (int i = 0; i < 16; i++)
        // {
        //     // if (i == 8)
        //     //     l_CloudScans[i].resize(7200);
        //     // else
        //         l_CloudScans[i].resize(1800);
        // }
        for (int i = 0; i < (int)p_Input->points.size(); i++)
        {
            // const float* l_iaddr = reinterpret_cast<const float*>(&(p_Input->points[i]));

            // l_point.points[i].x = p_Input->points[i].x;
            // l_point.points[i].y = p_Input->points[i].y;
            // l_point.points[i].z = p_Input->points[i].z;

            // memcpy(&l_point.points[i].h, l_iaddr+4, sizeof(float));

            l_point.points[i] = p_Input->points[i];

            // l_point.points[i].s = 0;
            scanID_ = whichLinePoints(l_point.points[i]);
            // int pointID_ = whichPoints(l_point.points[i]);
            // l_CloudScans[scanID_].points[pointID_] = l_point.points[i];
            for (int ring_num = 0; ring_num < 16; ring_num++)
            {
                if (scanID_ == ring_idx_[ring_num])
                {
                    // l_CloudScans[ring_num].points[pointID_]=l_point.points[i];
                    l_CloudScans[ring_num].push_back(l_point.points[i]);
                }
            }
        }

        for (int j = 0; j < 16; j++)
        {
            for (int k = 0; k < (int)l_CloudScans[j].points.size(); k++)
            {
                l_CloudScans[j].points[k].v = 0;
                float r1 =
                    sqrt(pow(l_CloudScans[j].points[k].x, 2) + pow(l_CloudScans[j].points[k].y, 2));
                float r2;

                if (r1 <= 1.0)
                {
                    l_CloudScans[j].points[k].v = 1;
                }

                if (k == 0)
                {
                    r2 = r1;
                }
                else
                {
                    r2 = sqrt(pow(l_CloudScans[j].points[k - 1].x, 2)
                              + pow(l_CloudScans[j].points[k - 1].y, 2));
                }

                float dist = 0;
                float diff = 0;
                float diff2 = std::fabs(r1 - r2);
                dist = std::min(r1, r2);
                diff = l_fAngleToDelete * dist * 1.0;

                if (diff2 > diff)
                {
                    l_CloudScans[j].points[k].v = 1;
                }
            }
        }
        return l_CloudScans;
    }

    //将点云投影yoz平面求角点
    /**
     * @description: 计算右下区域角点并提取角点序号
     * @param {pcl::PointCloud<PointType>} &p_laserScan1 右下角区域450个点
     * @return {std::vector<int>} CornerId1 右下区域角点序号
     */
    std::vector<int> calcuCornerRightDown(const pcl::PointCloud<PointType>& p_laserScan1,
                                          int p_iLineNum)
    {
        pcs_in = p_laserScan1;
        std::vector<int> CornerId1;
        CornerId1.clear();

        for (int idnum = (c_idetecNum + 900); idnum < (c_idetecNum + 1349); idnum++)
        {
            int id = idnum;
            if (idnum >= 1800)
                id = idnum % 1800;
            if (id >= p_iLineNum)
                continue;
            if (pcs_in.points[id].v == 0 && pcs_in.points[id + 2].v == 0
                && pcs_in.points[id + 4].v == 0)
            {
                float juli1 =
                    fabs(sqrt(pow(pcs_in.points[id + 2].x, 2) + pow(pcs_in.points[id + 2].y, 2)
                              + pow(pcs_in.points[id + 2].z, 2))
                         - sqrt(pow(pcs_in.points[id].x, 2) + pow(pcs_in.points[id].y, 2)
                                + pow(pcs_in.points[id].z, 2)));
                float juli2 =
                    fabs(sqrt(pow(pcs_in.points[id + 4].x, 2) + pow(pcs_in.points[id + 4].y, 2)
                              + pow(pcs_in.points[id + 4].z, 2))
                         - sqrt(pow(pcs_in.points[id + 2].x, 2) + pow(pcs_in.points[id + 2].y, 2)
                                + pow(pcs_in.points[id + 2].z, 2)));

                if (juli1 < 0.2 && juli2 < 0.2)
                {
                    v1_1[0] = 0;  //(pcs_in[id].x - pcs_in[id+2].x);
                    v1_1[1] = (pcs_in.points[id].y - pcs_in.points[id + 2].y);
                    v1_1[2] = (pcs_in.points[id].z - pcs_in.points[id + 2].z);

                    v2_1[0] = 0;  //(pcs_in[id+4].x - pcs_in[id+2].x);
                    v2_1[1] = (pcs_in.points[id + 4].y - pcs_in.points[id + 2].y);
                    v2_1[2] = (pcs_in.points[id + 4].z - pcs_in.points[id + 2].z);

                    float cosValue = ((v1_1 + v2_1).norm()) / ((v1_1 - v2_1).norm());
                    //设置识别为角点的角度数值阈值
                    if (cosValue >= 0.36 && cosValue <= 1.2)
                    {
                        CornerId1.push_back(idnum + 2);
                    }
                }
            }
        }
        return CornerId1;
    }

    /**
     * @description: 计算右上区域角点并提取角点序号
     * @param {pcl::PointCloud<PointType>} &p_laserScan2 右上区域450个点
     * @return {std::vector<int>} CornerId2 右上区域角点序号
     */
    std::vector<int> calcuCornerRightUp(const pcl::PointCloud<PointType>& p_laserScan2,
                                        int p_iLineNum)
    {
        pcs_in = p_laserScan2;
        std::vector<int> CornerId2;
        CornerId2.clear();

        for (int idnum = (c_idetecNum + 1749); idnum > (c_idetecNum + 1350); idnum--)
        {
            int id = idnum;
            if (idnum >= 1800)
                id = idnum % 1800;
            if (id >= p_iLineNum)
                continue;
            if (pcs_in.points[id].v == 0 && pcs_in.points[id - 2].v == 0
                && pcs_in.points[id - 4].v == 0)
            {
                float juli1 =
                    fabs(sqrt(pow(pcs_in.points[id - 2].x, 2) + pow(pcs_in.points[id - 2].y, 2)
                              + pow(pcs_in.points[id - 2].z, 2))
                         - sqrt(pow(pcs_in.points[id].x, 2) + pow(pcs_in.points[id].y, 2)
                                + pow(pcs_in.points[id].z, 2)));
                float juli2 =
                    fabs(sqrt(pow(pcs_in.points[id - 4].x, 2) + pow(pcs_in.points[id - 4].y, 2)
                              + pow(pcs_in.points[id - 4].z, 2))
                         - sqrt(pow(pcs_in.points[id - 2].x, 2) + pow(pcs_in.points[id - 2].y, 2)
                                + pow(pcs_in.points[id - 2].z, 2)));

                if (juli1 < 0.3 && juli2 < 0.3)
                {
                    v1_2[0] = 0;  //(pcs_in[id].x - pcs_in[id-2].x);
                    v1_2[1] = (pcs_in.points[id].y - pcs_in.points[id - 2].y);
                    v1_2[2] = (pcs_in.points[id].z - pcs_in.points[id - 2].z);

                    v2_2[0] = 0;  //(pcs_in[id-4].x - pcs_in[id-2].x);
                    v2_2[1] = (pcs_in.points[id - 4].y - pcs_in.points[id - 2].y);
                    v2_2[2] = (pcs_in.points[id - 4].z - pcs_in.points[id - 2].z);

                    float cosValue = ((v1_2 + v2_2).norm()) / ((v1_2 - v2_2).norm());
                    if (cosValue >= 0.57 && cosValue <= 1.2)
                    {
                        CornerId2.push_back(idnum - 2);
                    }
                }
            }
        }
        return CornerId2;
    }

    /**
     * @description: 计算左上区域角点并提取序号
     * @param {pcl::PointCloud<PointType>} &p_laserScan3 左上区域450个点
     * @return {std::vector<int>} CornerId3 左上区域角点序号
     */
    std::vector<int> calcuCornerLeftUp(const pcl::PointCloud<PointType>& p_laserScan3,
                                       int p_iLineNum)
    {
        pcs_in = p_laserScan3;
        std::vector<int> CornerId3;
        CornerId3.clear();

        for (int idnum = c_idetecNum; idnum < (c_idetecNum + 449); idnum++)
        {
            int id = idnum;
            if (idnum >= 1800)
                id = (idnum % 1800);
            if (id >= p_iLineNum)
                continue;
            if (pcs_in.points[id].v == 0 && pcs_in.points[id + 2].v == 0
                && pcs_in.points[id + 4].v == 0)
            {
                float juli1 =
                    fabs(sqrt(pow(pcs_in.points[id + 2].x, 2) + pow(pcs_in.points[id + 2].y, 2)
                              + pow(pcs_in.points[id + 2].z, 2))
                         - sqrt(pow(pcs_in.points[id].x, 2) + pow(pcs_in.points[id].y, 2)
                                + pow(pcs_in.points[id].z, 2)));
                float juli2 =
                    fabs(sqrt(pow(pcs_in.points[id + 4].x, 2) + pow(pcs_in.points[id + 4].y, 2)
                              + pow(pcs_in.points[id + 4].z, 2))
                         - sqrt(pow(pcs_in.points[id + 2].x, 2) + pow(pcs_in.points[id + 2].y, 2)
                                + pow(pcs_in.points[id + 2].z, 2)));
                float juli3 =
                    fabs(sqrt(pow(pcs_in.points[id + 3].x, 2) + pow(pcs_in.points[id + 3].y, 2)
                              + pow(pcs_in.points[id + 3].z, 2))
                         - sqrt(pow(pcs_in.points[id].x, 2) + pow(pcs_in.points[id].y, 2)
                                + pow(pcs_in.points[id].z, 2)));
                float juli4 =
                    fabs(sqrt(pow(pcs_in.points[id + 6].x, 2) + pow(pcs_in.points[id + 6].y, 2)
                              + pow(pcs_in.points[id + 6].z, 2))
                         - sqrt(pow(pcs_in.points[id + 3].x, 2) + pow(pcs_in.points[id + 3].y, 2)
                                + pow(pcs_in.points[id + 3].z, 2)));

                if (juli1 < 0.18 && juli2 < 0.18 && juli3 < 0.25 && juli4 < 0.25)
                {
                    v1_3[0] = 0;  //(pcs_in[id].x - pcs_in[id+2].x);
                    v1_3[1] = (pcs_in.points[id].y - pcs_in.points[id + 2].y);
                    v1_3[2] = (pcs_in.points[id].z - pcs_in.points[id + 2].z);

                    v2_3[0] = 0;  //(pcs_in[id+4].x - pcs_in[id+2].x);
                    v2_3[1] = (pcs_in.points[id + 4].y - pcs_in.points[id + 2].y);
                    v2_3[2] = (pcs_in.points[id + 4].z - pcs_in.points[id + 2].z);

                    float cosValue = ((v1_3 + v2_3).norm()) / ((v1_3 - v2_3).norm());
                    if (cosValue >= 0.46 && cosValue <= 1.2)
                    {
                        CornerId3.push_back(idnum + 2);
                    }
                }
            }
        }
        return CornerId3;
    }

    /**
     * @description: 计算左下区域角点并提取序号
     * @param {pcl::PointCloud<PointType>} &p_laserScan4 左下区域450个点
     * @return {std::vector<int>} CornerId4 左下区域角点序号
     */
    std::vector<int> calcuCornerLeftDown(const pcl::PointCloud<PointType>& p_laserScan4,
                                         int p_iLineNum)
    {
        pcs_in = p_laserScan4;
        std::vector<int> CornerId4;
        CornerId4.clear();

        for (int idnum = (c_idetecNum + 899); idnum > (c_idetecNum + 450); idnum--)
        {
            int id = idnum;
            if (idnum >= 1800)
                id = idnum % 1800;
            if (id >= p_iLineNum)
                continue;
            if (pcs_in.points[id].v == 0 && pcs_in.points[id - 2].v == 0
                && pcs_in.points[id - 4].v == 0)
            {
                float juli1 =
                    fabs(sqrt(pow(pcs_in.points[id - 2].x, 2) + pow(pcs_in.points[id - 2].y, 2)
                              + pow(pcs_in.points[id - 2].z, 2))
                         - sqrt(pow(pcs_in.points[id].x, 2) + pow(pcs_in.points[id].y, 2)
                                + pow(pcs_in.points[id].z, 2)));
                float juli2 =
                    fabs(sqrt(pow(pcs_in.points[id - 4].x, 2) + pow(pcs_in.points[id - 4].y, 2)
                              + pow(pcs_in.points[id - 4].z, 2))
                         - sqrt(pow(pcs_in.points[id - 2].x, 2) + pow(pcs_in.points[id - 2].y, 2)
                                + pow(pcs_in.points[id - 2].z, 2)));
                float juli3 =
                    fabs(sqrt(pow(pcs_in.points[id - 3].x, 2) + pow(pcs_in.points[id - 3].y, 2)
                              + pow(pcs_in.points[id - 3].z, 2))
                         - sqrt(pow(pcs_in.points[id].x, 2) + pow(pcs_in.points[id].y, 2)
                                + pow(pcs_in.points[id].z, 2)));
                float juli4 =
                    fabs(sqrt(pow(pcs_in.points[id - 6].x, 2) + pow(pcs_in.points[id - 6].y, 2)
                              + pow(pcs_in.points[id - 6].z, 2))
                         - sqrt(pow(pcs_in.points[id - 3].x, 2) + pow(pcs_in.points[id - 3].y, 2)
                                + pow(pcs_in.points[id - 3].z, 2)));

                if (juli1 < 0.18 && juli2 < 0.18 && juli3 < 0.25 && juli4 < 0.25)
                {
                    v1_4[0] = 0;  //(pcs_in[id].x - pcs_in[id-2].x);
                    v1_4[1] = (pcs_in.points[id].y - pcs_in.points[id - 2].y);
                    v1_4[2] = (pcs_in.points[id].z - pcs_in.points[id - 2].z);

                    v2_4[0] = 0;  //(pcs_in[id-4].x - pcs_in[id-2].x);
                    v2_4[1] = (pcs_in.points[id - 4].y - pcs_in.points[id - 2].y);
                    v2_4[2] = (pcs_in.points[id - 4].z - pcs_in.points[id - 2].z);

                    float cosValue = ((v1_4 + v2_4).norm()) / ((v1_4 - v2_4).norm());
                    if (cosValue >= 0.46 && cosValue <= 1.2)
                    {
                        CornerId4.push_back(idnum - 2);
                    }
                }
            }
        }
        return CornerId4;
    }

    // xyz三维求角点
    /**
     * @description: 计算右下区域角点并提取序号
     * @param {pcl::PointCloud<PointType>} &p_laserScan1 右下区域450个点
     * @return {std::vector<int>} CornerId1 右下区域角点序号
     */
    std::vector<int> calcuCornerRightDownFar(const pcl::PointCloud<PointType>& p_laserScan1,
                                             int p_iLineNum)
    {
        pcs_in = p_laserScan1;
        std::vector<int> CornerId1;
        CornerId1.clear();

        for (int idnum = (c_idetecNum + 900); idnum < (c_idetecNum + 1349); idnum++)
        {
            int id = idnum;
            if (idnum >= 1800)
                id = idnum % 1800;
            if (id >= p_iLineNum)
                continue;
            if (pcs_in.points[id].v == 0 && pcs_in.points[id + 2].v == 0
                && pcs_in.points[id + 4].v == 0)
            {
                float juli1 =
                    fabs(sqrt(pow(pcs_in.points[id + 2].x, 2) + pow(pcs_in.points[id + 2].y, 2)
                              + pow(pcs_in.points[id + 2].z, 2))
                         - sqrt(pow(pcs_in.points[id].x, 2) + pow(pcs_in.points[id].y, 2)
                                + pow(pcs_in.points[id].z, 2)));
                float juli2 =
                    fabs(sqrt(pow(pcs_in.points[id + 4].x, 2) + pow(pcs_in.points[id + 4].y, 2)
                              + pow(pcs_in.points[id + 4].z, 2))
                         - sqrt(pow(pcs_in.points[id + 2].x, 2) + pow(pcs_in.points[id + 2].y, 2)
                                + pow(pcs_in.points[id + 2].z, 2)));

                if (juli1 < 0.2 && juli2 < 0.2)
                {
                    v1_1[0] = (pcs_in.points[id].x - pcs_in.points[id + 2].x);
                    v1_1[1] = (pcs_in.points[id].y - pcs_in.points[id + 2].y);
                    v1_1[2] = (pcs_in.points[id].z - pcs_in.points[id + 2].z);

                    v2_1[0] = (pcs_in.points[id + 4].x - pcs_in.points[id + 2].x);
                    v2_1[1] = (pcs_in.points[id + 4].y - pcs_in.points[id + 2].y);
                    v2_1[2] = (pcs_in.points[id + 4].z - pcs_in.points[id + 2].z);

                    float cosValue = ((v1_1 + v2_1).norm()) / ((v1_1 - v2_1).norm());
                    if (cosValue >= 0.46 && cosValue <= 1.2)
                    {
                        CornerId1.push_back(idnum + 2);
                    }
                }
            }
        }
        return CornerId1;
    }

    /**
     * @description: 计算右上区域角点并提取序号
     * @param {pcl::PointCloud<PointType>} &p_laserScan2 右上区域450个点
     * @return {std::vector<int>} CornerId2 右下区域角点序号
     */
    std::vector<int> calcuCornerRightUpFar(const pcl::PointCloud<PointType>& p_laserScan2,
                                           int p_iLineNum)
    {
        pcs_in = p_laserScan2;
        std::vector<int> CornerId2;
        CornerId2.clear();
        for (int idnum = (c_idetecNum + 1749); idnum > (c_idetecNum + 1350); idnum--)
        {
            int id = idnum;
            if (idnum >= 1800)
                id = idnum % 1800;
            if (id >= p_iLineNum)
                continue;
            if (pcs_in.points[id].v == 0 && pcs_in.points[id - 2].v == 0
                && pcs_in.points[id - 4].v == 0)
            {
                float juli1 =
                    fabs(sqrt(pow(pcs_in.points[id - 2].x, 2) + pow(pcs_in.points[id - 2].y, 2)
                              + pow(pcs_in.points[id - 2].z, 2))
                         - sqrt(pow(pcs_in.points[id].x, 2) + pow(pcs_in.points[id].y, 2)
                                + pow(pcs_in.points[id].z, 2)));
                float juli2 =
                    fabs(sqrt(pow(pcs_in.points[id - 4].x, 2) + pow(pcs_in.points[id - 4].y, 2)
                              + pow(pcs_in.points[id - 4].z, 2))
                         - sqrt(pow(pcs_in.points[id - 2].x, 2) + pow(pcs_in.points[id - 2].y, 2)
                                + pow(pcs_in.points[id - 2].z, 2)));

                if (juli1 < 0.3 && juli2 < 0.3)
                {
                    v1_2[0] = (pcs_in.points[id].x - pcs_in.points[id - 2].x);
                    v1_2[1] = (pcs_in.points[id].y - pcs_in.points[id - 2].y);
                    v1_2[2] = (pcs_in.points[id].z - pcs_in.points[id - 2].z);

                    v2_2[0] = (pcs_in.points[id - 4].x - pcs_in.points[id - 2].x);
                    v2_2[1] = (pcs_in.points[id - 4].y - pcs_in.points[id - 2].y);
                    v2_2[2] = (pcs_in.points[id - 4].z - pcs_in.points[id - 2].z);

                    float cosValue = ((v1_2 + v2_2).norm()) / ((v1_2 - v2_2).norm());
                    if (cosValue >= 0.46 && cosValue <= 1.2)
                    {
                        CornerId2.push_back(idnum - 2);
                    }
                }
            }
        }
        return CornerId2;
    }

    /**
     * @description: 计算左上区域角点并提取序号
     * @param {pcl::PointCloud<PointType>} &p_laserScan3 左上区域450个点
     * @return {std::vector<int>} CornerId3 左上区域点云序号
     */
    std::vector<int> calcuCornerLeftUpFar(const pcl::PointCloud<PointType>& p_laserScan3,
                                          int p_iLineNum)
    {
        pcs_in = p_laserScan3;
        std::vector<int> CornerId3;
        CornerId3.clear();
        std::vector<int> id_num;
        id_num.clear();

        for (int idnum = c_idetecNum; idnum < (c_idetecNum + 449); idnum++)
        {
            int id = idnum;
            if (idnum >= 1800)
                id = (idnum % 1800);
            if (id >= p_iLineNum)
                continue;
            if (pcs_in.points[id].v == 0 && pcs_in.points[id + 2].v == 0
                && pcs_in.points[id + 4].v == 0)
            {
                float juli1 =
                    fabs(sqrt(pow(pcs_in.points[id + 2].x, 2) + pow(pcs_in.points[id + 2].y, 2)
                              + pow(pcs_in.points[id + 2].z, 2))
                         - sqrt(pow(pcs_in.points[id].x, 2) + pow(pcs_in.points[id].y, 2)
                                + pow(pcs_in.points[id].z, 2)));
                float juli2 =
                    fabs(sqrt(pow(pcs_in.points[id + 4].x, 2) + pow(pcs_in.points[id + 4].y, 2)
                              + pow(pcs_in.points[id + 4].z, 2))
                         - sqrt(pow(pcs_in.points[id + 2].x, 2) + pow(pcs_in.points[id + 2].y, 2)
                                + pow(pcs_in.points[id + 2].z, 2)));
                float juli3 =
                    fabs(sqrt(pow(pcs_in.points[id + 3].x, 2) + pow(pcs_in.points[id + 3].y, 2)
                              + pow(pcs_in.points[id + 3].z, 2))
                         - sqrt(pow(pcs_in.points[id].x, 2) + pow(pcs_in.points[id].y, 2)
                                + pow(pcs_in.points[id].z, 2)));
                float juli4 =
                    fabs(sqrt(pow(pcs_in.points[id + 6].x, 2) + pow(pcs_in.points[id + 6].y, 2)
                              + pow(pcs_in.points[id + 6].z, 2))
                         - sqrt(pow(pcs_in.points[id + 3].x, 2) + pow(pcs_in.points[id + 3].y, 2)
                                + pow(pcs_in.points[id + 3].z, 2)));

                if (juli1 < 0.18 && juli2 < 0.18 && juli3 < 0.25 && juli4 < 0.25)
                {
                    v1_3[0] = (pcs_in.points[id].x - pcs_in.points[id + 2].x);
                    v1_3[1] = (pcs_in.points[id].y - pcs_in.points[id + 2].y);
                    v1_3[2] = (pcs_in.points[id].z - pcs_in.points[id + 2].z);

                    v2_3[0] = (pcs_in.points[id + 4].x - pcs_in.points[id + 2].x);
                    v2_3[1] = (pcs_in.points[id + 4].y - pcs_in.points[id + 2].y);
                    v2_3[2] = (pcs_in.points[id + 4].z - pcs_in.points[id + 2].z);

                    float cosValue = ((v1_3 + v2_3).norm()) / ((v1_3 - v2_3).norm());
                    if (cosValue >= 0.17 && cosValue <= 1.2)
                    {
                        id_num.push_back(idnum + 2);
                    }
                    else
                    {
                        if (id_num.size() >= 2)
                        {
                            int num = (id_num.size() / 2);
                            CornerId3.push_back(id_num[num]);
                            id_num.clear();
                        }
                        // CornerId3.push_back(id+2);
                    }
                }
            }
        }
        return CornerId3;
    }

    /**
     * @description: 计算左下区域角点并提取序号
     * @param {pcl::PointCloud<PointType>} &p_laserScan4 左下区域450个点
     * @return {std::vector<int>} CornerId4 左下区域点云序号
     */
    std::vector<int> calcuCornerLeftDownFar(const pcl::PointCloud<PointType>& p_laserScan4,
                                            int p_iLineNum)
    {
        pcs_in = p_laserScan4;
        std::vector<int> CornerId4;
        CornerId4.clear();
        std::vector<int> id_num;
        id_num.clear();

        for (int idnum = (c_idetecNum + 899); idnum > (c_idetecNum + 450); idnum--)
        {
            int id = idnum;
            if (idnum >= 1800)
                id = idnum % 1800;
            if (id >= p_iLineNum)
                continue;
            if (pcs_in.points[id].v == 0 && pcs_in.points[id - 2].v == 0
                && pcs_in.points[id - 4].v == 0)
            {
                float juli1 =
                    fabs(sqrt(pow(pcs_in.points[id - 2].x, 2) + pow(pcs_in.points[id - 2].y, 2)
                              + pow(pcs_in.points[id - 2].z, 2))
                         - sqrt(pow(pcs_in.points[id].x, 2) + pow(pcs_in.points[id].y, 2)
                                + pow(pcs_in.points[id].z, 2)));
                float juli2 =
                    fabs(sqrt(pow(pcs_in.points[id - 4].x, 2) + pow(pcs_in.points[id - 4].y, 2)
                              + pow(pcs_in.points[id - 4].z, 2))
                         - sqrt(pow(pcs_in.points[id - 2].x, 2) + pow(pcs_in.points[id - 2].y, 2)
                                + pow(pcs_in.points[id - 2].z, 2)));
                float juli3 =
                    fabs(sqrt(pow(pcs_in.points[id - 3].x, 2) + pow(pcs_in.points[id - 3].y, 2)
                              + pow(pcs_in.points[id - 3].z, 2))
                         - sqrt(pow(pcs_in.points[id].x, 2) + pow(pcs_in.points[id].y, 2)
                                + pow(pcs_in.points[id].z, 2)));
                float juli4 =
                    fabs(sqrt(pow(pcs_in.points[id - 6].x, 2) + pow(pcs_in.points[id - 6].y, 2)
                              + pow(pcs_in.points[id - 6].z, 2))
                         - sqrt(pow(pcs_in.points[id - 3].x, 2) + pow(pcs_in.points[id - 3].y, 2)
                                + pow(pcs_in.points[id - 3].z, 2)));

                if (juli1 < 0.18 && juli2 < 0.18 && juli3 < 0.25 && juli4 < 0.25)
                {
                    v1_4[0] = (pcs_in.points[id].x - pcs_in.points[id - 2].x);
                    v1_4[1] = (pcs_in.points[id].y - pcs_in.points[id - 2].y);
                    v1_4[2] = (pcs_in.points[id].z - pcs_in.points[id - 2].z);

                    v2_4[0] = (pcs_in.points[id - 4].x - pcs_in.points[id - 2].x);
                    v2_4[1] = (pcs_in.points[id - 4].y - pcs_in.points[id - 2].y);
                    v2_4[2] = (pcs_in.points[id - 4].z - pcs_in.points[id - 2].z);

                    float cosValue = ((v1_4 + v2_4).norm()) / ((v1_4 - v2_4).norm());

                    if (cosValue >= 0.17 && cosValue <= 1.2)
                    {
                        id_num.push_back(idnum - 2);
                    }
                    else
                    {
                        if (id_num.size() >= 2)
                        {
                            int num = (id_num.size() / 2);
                            CornerId4.push_back(id_num[num]);
                            id_num.clear();
                        }
                        // CornerId4.push_back(id-2);
                    }
                }
            }
        }
        return CornerId4;
    }

    /**
     * @description: 判定路沿计算区域内的点是连续存在
     * @param {pcl::PointCloud<PointType>} &p_a 输入点云
     * @param {int} p_b 点云序号
     * @return {bool} true 连续 flase 不连续
     */
    bool keZhiXing(const pcl::PointCloud<PointType>& p_a, const int p_b)
    {
        if (p_b > (c_idetecNum + 900) && p_b <= (c_idetecNum + 1349))
        {
            int n;
            if (p_b > 1800)
                n = p_b % 1800;
            else
                n = p_b;

            for (int m = n; m < n + 10; m++)
            {
                float juli1 = fabs(sqrt(pow(p_a.points[m + 2].x, 2) + pow(p_a.points[m + 2].y, 2)
                                        + pow(p_a.points[m + 2].z, 2))
                                   - sqrt(pow(p_a.points[m].x, 2) + pow(p_a.points[m].y, 2)
                                          + pow(p_a.points[m].z, 2)));
                float juli2 = fabs(sqrt(pow(p_a.points[m].x, 2) + pow(p_a.points[m].y, 2)
                                        + pow(p_a.points[m].z, 2))
                                   - sqrt(pow(p_a.points[m - 2].x, 2) + pow(p_a.points[m - 2].y, 2)
                                          + pow(p_a.points[m - 2].z, 2)));
                if (juli1 < 0.4 && juli2 < 0.4)
                {
                    continue;
                }
                else
                {
                    return false;
                }
            }
            return true;
        }
        else if (p_b > (c_idetecNum + 1350) && p_b <= (c_idetecNum + 1799))
        {
            int n;
            if (p_b > 1800)
                n = p_b % 1800;
            else
                n = p_b;

            for (int m = n; m > n - 10; m--)
            {
                float juli1 = fabs(sqrt(pow(p_a.points[m - 2].x, 2) + pow(p_a.points[m - 2].y, 2)
                                        + pow(p_a.points[m - 2].z, 2))
                                   - sqrt(pow(p_a.points[m].x, 2) + pow(p_a.points[m].y, 2)
                                          + pow(p_a.points[m].z, 2)));
                float juli2 = fabs(sqrt(pow(p_a.points[m].x, 2) + pow(p_a.points[m].y, 2)
                                        + pow(p_a.points[m].z, 2))
                                   - sqrt(pow(p_a.points[m + 2].x, 2) + pow(p_a.points[m + 2].y, 2)
                                          + pow(p_a.points[m + 2].z, 2)));
                if (juli1 < 0.4 && juli2 < 0.4)
                {
                    continue;
                }
                else
                {
                    return false;
                }
            }
            return true;
        }
        else if (p_b > c_idetecNum && p_b <= (c_idetecNum + 449))
        {
            int n;
            if (p_b > 1800)
                n = p_b % 1800;
            else
                n = p_b;

            for (int m = n; m < n + 30; m++)
            {
                float juli1 = fabs(sqrt(pow(p_a.points[m + 2].x, 2) + pow(p_a.points[m + 2].y, 2)
                                        + pow(p_a.points[m + 2].z, 2))
                                   - sqrt(pow(p_a.points[m].x, 2) + pow(p_a.points[m].y, 2)
                                          + pow(p_a.points[m].z, 2)));
                float juli2 = fabs(sqrt(pow(p_a.points[m].x, 2) + pow(p_a.points[m].y, 2)
                                        + pow(p_a.points[m].z, 2))
                                   - sqrt(pow(p_a.points[m - 2].x, 2) + pow(p_a.points[m - 2].y, 2)
                                          + pow(p_a.points[m - 2].z, 2)));
                if (juli1 < 0.4 && juli2 < 0.4)
                {
                    continue;
                }
                else
                {
                    return false;
                }
            }
            return true;
        }
        else if (p_b > (c_idetecNum + 450) && p_b <= (c_idetecNum + 899))
        {
            int n;
            if (p_b > 1800)
                n = p_b % 1800;
            else
                n = p_b;

            for (int m = n; m > n - 40; m--)
            {
                float juli1 = fabs(sqrt(pow(p_a.points[m - 2].x, 2) + pow(p_a.points[m - 2].y, 2)
                                        + pow(p_a.points[m - 2].z, 2))
                                   - sqrt(pow(p_a.points[m].x, 2) + pow(p_a.points[m].y, 2)
                                          + pow(p_a.points[m].z, 2)));
                float juli2 = fabs(sqrt(pow(p_a.points[m].x, 2) + pow(p_a.points[m].y, 2)
                                        + pow(p_a.points[m].z, 2))
                                   - sqrt(pow(p_a.points[m + 2].x, 2) + pow(p_a.points[m + 2].y, 2)
                                          + pow(p_a.points[m + 2].z, 2)));

                if (juli1 < 0.4 && juli2 < 0.4)
                {
                    continue;
                }
                else
                {
                    return false;
                }
            }
            return true;
        }
        else
        {
            printf("keZhiXing Error\n");
            return false;
        }
    }

    /**
     * @description: 判定车道线计算区域内的点是连续存在
     * @param {pcl::PointCloud<PointType>} &p_a 输入点云
     * @param {int} p_b 点云序号
     * @return {bool} true 连续 flase 不连续
     */
    bool ZhiXing(const pcl::PointCloud<PointType>& p_a, const int p_b)
    {
        if (p_b > (c_idetecNum + 900) && p_b <= (c_idetecNum + 1349))
        {
            int n;
            if (p_b > 1800)
                n = p_b % 1800;
            else
                n = p_b;

            for (int m = n; m < n + 5; m++)
            {
                float juli1 = fabs(sqrt(pow(p_a.points[m + 2].x, 2) + pow(p_a.points[m + 2].y, 2)
                                        + pow(p_a.points[m + 2].z, 2))
                                   - sqrt(pow(p_a.points[m].x, 2) + pow(p_a.points[m].y, 2)
                                          + pow(p_a.points[m].z, 2)));
                float juli2 = fabs(sqrt(pow(p_a.points[m].x, 2) + pow(p_a.points[m].y, 2)
                                        + pow(p_a.points[m].z, 2))
                                   - sqrt(pow(p_a.points[m - 2].x, 2) + pow(p_a.points[m - 2].y, 2)
                                          + pow(p_a.points[m - 2].z, 2)));
                if (juli1 < 0.4 && juli2 < 0.4)
                {
                    continue;
                }
                else
                {
                    return false;
                }
            }
            return true;
        }
        else if (p_b > (c_idetecNum + 1350) && p_b <= (c_idetecNum + 1799))
        {
            int n;
            if (p_b > 1800)
                n = p_b % 1800;
            else
                n = p_b;

            for (int m = n; m > n - 5; m--)
            {
                float juli1 = fabs(sqrt(pow(p_a.points[m - 2].x, 2) + pow(p_a.points[m - 2].y, 2)
                                        + pow(p_a.points[m - 2].z, 2))
                                   - sqrt(pow(p_a.points[m].x, 2) + pow(p_a.points[m].y, 2)
                                          + pow(p_a.points[m].z, 2)));
                float juli2 = fabs(sqrt(pow(p_a.points[m].x, 2) + pow(p_a.points[m].y, 2)
                                        + pow(p_a.points[m].z, 2))
                                   - sqrt(pow(p_a.points[m + 2].x, 2) + pow(p_a.points[m + 2].y, 2)
                                          + pow(p_a.points[m + 2].z, 2)));
                if (juli1 < 0.4 && juli2 < 0.4)
                {
                    continue;
                }
                else
                {
                    return false;
                }
            }
            return true;
        }
        else if (p_b > c_idetecNum && p_b <= (c_idetecNum + 449))
        {
            int n;
            if (p_b > 1800)
                n = p_b % 1800;
            else
                n = p_b;

            for (int m = n; m < n + 5; m++)
            {
                float juli1 = fabs(sqrt(pow(p_a.points[m + 2].x, 2) + pow(p_a.points[m + 2].y, 2)
                                        + pow(p_a.points[m + 2].z, 2))
                                   - sqrt(pow(p_a.points[m].x, 2) + pow(p_a.points[m].y, 2)
                                          + pow(p_a.points[m].z, 2)));
                float juli2 = fabs(sqrt(pow(p_a.points[m].x, 2) + pow(p_a.points[m].y, 2)
                                        + pow(p_a.points[m].z, 2))
                                   - sqrt(pow(p_a.points[m - 2].x, 2) + pow(p_a.points[m - 2].y, 2)
                                          + pow(p_a.points[m - 2].z, 2)));
                if (juli1 < 0.4 && juli2 < 0.4)
                {
                    continue;
                }
                else
                {
                    return false;
                }
            }
            return true;
        }
        else if (p_b > (c_idetecNum + 450) && p_b <= (c_idetecNum + 899))
        {
            int n;
            if (p_b > 1800)
                n = p_b % 1800;
            else
                n = p_b;

            for (int m = n; m > n - 5; m--)
            {
                float juli1 = fabs(sqrt(pow(p_a.points[m - 2].x, 2) + pow(p_a.points[m - 2].y, 2)
                                        + pow(p_a.points[m - 2].z, 2))
                                   - sqrt(pow(p_a.points[m].x, 2) + pow(p_a.points[m].y, 2)
                                          + pow(p_a.points[m].z, 2)));
                float juli2 = fabs(sqrt(pow(p_a.points[m].x, 2) + pow(p_a.points[m].y, 2)
                                        + pow(p_a.points[m].z, 2))
                                   - sqrt(pow(p_a.points[m + 2].x, 2) + pow(p_a.points[m + 2].y, 2)
                                          + pow(p_a.points[m + 2].z, 2)));

                if (juli1 < 0.4 && juli2 < 0.4)
                {
                    continue;
                }
                else
                {
                    return false;
                }
            }
            return true;
        }
        else
        {
            printf("ZhiXing Error\n");
            return false;
        }
    }

    /**
     * @description: 识别右下区域路沿点
     * @param {pcl::PointCloud<PointType>} p_pointsIn 输入点云
     * @param {std::vector<int>} p_cornerId1 右下区域角点序号
     * @return {*}
     */
    void getCurbRightDown(pcl::PointCloud<PointType> p_pointsIn, std::vector<int> p_cornerId1)
    {
        int idp = 0;
        for (int i = 0; i < (int)p_cornerId1.size(); i++)
        {
            int j = p_cornerId1[i];
            if (j > (c_idetecNum + 900) && j <= (c_idetecNum + 1349))
            {
                if (j > 1800)
                    idp = j % 1800;
                else
                    idp = j;

                if (p_pointsIn.points[idp].v == 0 && p_pointsIn.points[idp + 2].v == 0
                    && p_pointsIn.points[idp + 3].v == 0 && p_pointsIn.points[idp + 4].v == 0
                    && p_pointsIn.points[idp + 5].v == 0 && p_pointsIn.points[idp + 6].v == 0
                    && p_pointsIn.points[idp + 10].v == 0)
                // if (pointzhixing(p_pointsIn, j))
                {
                    if (keZhiXing(p_pointsIn, j))
                    {
                        float z_dis = p_pointsIn.points[idp + 2].z - p_pointsIn.points[idp].z;
                        float z_dis1 = p_pointsIn.points[idp + 3].z - p_pointsIn.points[idp].z;
                        float z_dis2 = p_pointsIn.points[idp + 4].z - p_pointsIn.points[idp].z;
                        float z_dis3 = p_pointsIn.points[idp + 5].z - p_pointsIn.points[idp].z;
                        float z_dis4 = p_pointsIn.points[idp + 6].z - p_pointsIn.points[idp].z;
                        float z_dis5 = p_pointsIn.points[idp + 10].z - p_pointsIn.points[idp].z;

                        if (z_dis > 0 && z_dis1 > 0 && z_dis2 > 0 && z_dis3 > 0 && z_dis4 > 0)
                        {
                            if (z_dis5 >= (0.8 * c_fcurbHigh))
                            {
                                // if (fabs(y_dis) > 0.02)
                                // {
                                for (int l = (idp + 6); l < (idp + 13); l++)
                                {
                                    float z_dis_h =
                                        p_pointsIn.points[l].z - p_pointsIn.points[idp].z;
                                    if (z_dis_h > (1.1 * c_fcurbHigh))
                                    {
                                        // float p_dist = sqrt((p_pointsIn.points[(j+6)].x -
                                        // p_pointsIn.points[j].x) * (p_pointsIn.points[(j+6)].x -
                                        // p_pointsIn.points[j].x)
                                        // + (p_pointsIn.points[(j+6)].y - p_pointsIn.points[j].y) *
                                        // (p_pointsIn.points[(j+6)].y - p_pointsIn.points[j].y));
                                        // if (p_dist >= xy_thresh)
                                        // {
                                        for (int n = idp; n < l; n++)
                                        {
                                            if ((n + 2) <= l)
                                            {
                                                v3[0] = 0;  //(p_pointsIn.points[n-2].x -
                                                            // p_pointsIn.points[n].x);
                                                v3[1] = (p_pointsIn.points[n - 2].y
                                                         - p_pointsIn.points[n].y);
                                                v3[2] = (p_pointsIn.points[n - 2].z
                                                         - p_pointsIn.points[n].z);

                                                v4[0] = 0;  //(p_pointsIn.points[n+2].x -
                                                            // p_pointsIn.points[n].x);
                                                v4[1] = (p_pointsIn.points[n + 2].y
                                                         - p_pointsIn.points[n].y);
                                                v4[2] = (p_pointsIn.points[n + 2].z
                                                         - p_pointsIn.points[n].z);

                                                float cosVal_ =
                                                    ((v3 + v4).norm()) / ((v3 - v4).norm());
                                                if (cosVal_ >= 0.17 && cosVal_ <= 1.25)
                                                {
                                                    curb_rightdown.push_back(n);
                                                    return;
                                                }
                                            }
                                        }
                                        // }
                                    }
                                }
                                // }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * @description: 识别右上区域路沿点
     * @param {pcl::PointCloud<PointType>} p_pointsIn 输入点云
     * @param {std::vector<int>} p_cornerId2 右上区域角点序号
     * @return {*}
     */
    void getCurbRightUp(pcl::PointCloud<PointType> p_pointsIn, std::vector<int> p_cornerId2)
    {
        int idp = 0;
        for (int i = 0; i < (int)p_cornerId2.size(); i++)
        {
            int j = p_cornerId2[i];
            if (j > (c_idetecNum + 1350) && j <= (c_idetecNum + 1799))
            {
                if (j > 1800)
                    idp = j % 1800;
                else
                    idp = j;

                if (p_pointsIn.points[idp].v == 0 && p_pointsIn.points[idp - 3].v == 0
                    && p_pointsIn.points[idp - 4].v == 0 && p_pointsIn.points[idp - 5].v == 0
                    && p_pointsIn.points[idp - 6].v == 0 && p_pointsIn.points[idp - 7].v == 0
                    && p_pointsIn.points[idp - 10].v == 0)
                // if (pointzhixing(p_pointsIn, j))
                {
                    if (keZhiXing(p_pointsIn, j))
                    {
                        float z_dis = p_pointsIn.points[idp - 3].z - p_pointsIn.points[idp].z;
                        float z_dis1 = p_pointsIn.points[idp - 4].z - p_pointsIn.points[idp].z;
                        float z_dis2 = p_pointsIn.points[idp - 5].z - p_pointsIn.points[idp].z;
                        float z_dis3 = p_pointsIn.points[idp - 6].z - p_pointsIn.points[idp].z;
                        float z_dis4 = p_pointsIn.points[idp - 7].z - p_pointsIn.points[idp].z;
                        float z_dis5 = p_pointsIn.points[idp - 10].z - p_pointsIn.points[idp].z;

                        if (z_dis >= 0 && z_dis1 > 0 && z_dis2 > 0 && z_dis3 > 0 && z_dis4 > 0)
                        {
                            if (z_dis5 >= (0.8 * c_fcurbHigh))
                            {
                                // float y_dis = p_pointsIn.points[(j+6)+10].y -
                                // p_pointsIn.points[j+6].y; if (fabs(y_dis) > 0.02)
                                // {
                                for (int l = (idp - 7); l > (idp - 15); l--)
                                {
                                    float z_dis_h =
                                        p_pointsIn.points[l].z - p_pointsIn.points[idp].z;
                                    if (z_dis_h > (1.05 * c_fcurbHigh))
                                    {
                                        // float p_dist = sqrt((p_pointsIn.points[(j+6)].x -
                                        // p_pointsIn.points[j].x) * (p_pointsIn.points[(j+6)].x -
                                        // p_pointsIn.points[j].x)
                                        // + (p_pointsIn.points[(j+6)].y - p_pointsIn.points[j].y) *
                                        // (p_pointsIn.points[(j+6)].y - p_pointsIn.points[j].y));
                                        // if (p_dist >= xy_thresh)
                                        // {
                                        for (int n = idp; n > l; n--)
                                        {
                                            float max_val = 0;
                                            int id;
                                            if ((n - 2) >= l)
                                            {
                                                v5[0] = 0;  //(p_pointsIn.points[n+2].x -
                                                            // p_pointsIn.points[n].x);
                                                v5[1] = (p_pointsIn.points[n + 2].y
                                                         - p_pointsIn.points[n].y);
                                                v5[2] = (p_pointsIn.points[n + 2].z
                                                         - p_pointsIn.points[n].z);

                                                v6[0] = 0;  //(p_pointsIn.points[n-2].x -
                                                            // p_pointsIn.points[n].x);
                                                v6[1] = (p_pointsIn.points[n - 2].y
                                                         - p_pointsIn.points[n].y);
                                                v6[2] = (p_pointsIn.points[n - 2].z
                                                         - p_pointsIn.points[n].z);

                                                float cosVal_ =
                                                    ((v5 + v6).norm()) / ((v5 - v6).norm());
                                                if (cosVal_ >= 0.17 && cosVal_ <= 1.25)
                                                {
                                                    if (cosVal_ > max_val)
                                                    {
                                                        max_val = cosVal_;
                                                        id = n;
                                                    }
                                                    else
                                                    {
                                                        continue;
                                                    }
                                                    curb_rightup.push_back(id);
                                                    rightCloud.push_back(p_pointsIn.points[id]);
                                                    return;
                                                }
                                            }
                                        }
                                        // }
                                    }
                                }
                                // }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * @description: 识别左上区域路沿点
     * @param {pcl::PointCloud<PointType>} p_pointsIn 输入点云
     * @param {std::vector<int>} p_cornerId3 左上区域角点序号
     * @return {*}
     */
    void getCurbLeftUp(pcl::PointCloud<PointType> p_pointsIn, std::vector<int> p_cornerId3)
    {
        int idp = 0;
        for (int i = 0; i < (int)p_cornerId3.size(); i++)
        {
            int j = p_cornerId3[i];
            if (j > c_idetecNum && j <= (c_idetecNum + 449))
            {
                if (j > 1800)
                    idp = j % 1800;
                else
                    idp = j;

                if (p_pointsIn.points[idp].v == 0 && p_pointsIn.points[idp + 2].v == 0
                    && p_pointsIn.points[idp + 3].v == 0 && p_pointsIn.points[idp + 4].v == 0
                    && p_pointsIn.points[idp + 5].v == 0 && p_pointsIn.points[idp + 6].v == 0
                    && p_pointsIn.points[idp + 30].v == 0)
                // if (pointzhixing(p_pointsIn, j))
                {
                    if (keZhiXing(p_pointsIn, j))
                    {
                        float z_dis = p_pointsIn.points[idp + 2].z - p_pointsIn.points[idp].z;
                        float z_dis1 = p_pointsIn.points[idp + 3].z - p_pointsIn.points[idp].z;
                        float z_dis2 = p_pointsIn.points[idp + 4].z - p_pointsIn.points[idp].z;
                        float z_dis3 = p_pointsIn.points[idp + 5].z - p_pointsIn.points[idp].z;
                        float z_dis4 = p_pointsIn.points[idp + 6].z - p_pointsIn.points[idp].z;
                        float z_dis5 = p_pointsIn.points[idp + 30].z - p_pointsIn.points[idp].z;

                        float y_dis = p_pointsIn.points[(idp + 10)].y - p_pointsIn.points[idp].y;
                        if (z_dis > 0 && z_dis1 > 0 && z_dis2 > 0 && z_dis3 > 0 && z_dis4 > 0)
                        {
                            if (z_dis5 >= (0.8 * c_fcurbHigh))
                            {
                                if (fabs(y_dis) < 0.1)
                                {
                                    for (int l = (idp + 30); l < (idp + 50); l++)
                                    {
                                        float z_dis_h =
                                            p_pointsIn.points[l].z - p_pointsIn.points[idp].z;
                                        if (z_dis_h > (1.1 * c_fcurbHigh))
                                        {
                                            // float p_dist = sqrt((p_pointsIn.points[(j+6)].x -
                                            // p_pointsIn.points[j].x) * (p_pointsIn.points[(j+6)].x
                                            // - p_pointsIn.points[j].x)
                                            // + (p_pointsIn.points[(j+6)].y -
                                            // p_pointsIn.points[j].y) * (p_pointsIn.points[(j+6)].y
                                            // - p_pointsIn.points[j].y));
                                            // if (p_dist >= xy_thresh)
                                            // {
                                            for (int n = idp; n < (idp + 6); n++)
                                            {
                                                if ((n + 2) <= (idp + 6))
                                                {
                                                    v7[0] = 0;  //(p_pointsIn.points[n-2].x -
                                                                // p_pointsIn.points[n].x);
                                                    v7[1] = (p_pointsIn.points[n - 2].y
                                                             - p_pointsIn.points[n].y);
                                                    v7[2] = (p_pointsIn.points[n - 2].z
                                                             - p_pointsIn.points[n].z);

                                                    v8[0] = 0;  //(p_pointsIn.points[n+2].x -
                                                                // p_pointsIn.points[n].x);
                                                    v8[1] = (p_pointsIn.points[n + 2].y
                                                             - p_pointsIn.points[n].y);
                                                    v8[2] = (p_pointsIn.points[n + 2].z
                                                             - p_pointsIn.points[n].z);

                                                    float cosVal_ =
                                                        ((v7 + v8).norm()) / ((v7 - v8).norm());
                                                    if (cosVal_ >= 0.17 && cosVal_ <= 1.25)
                                                    {
                                                        curb_leftup.push_back(n);
                                                        leftCloud.push_back(p_pointsIn.points[n]);
                                                        return;
                                                    }
                                                }
                                            }
                                            // }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * @description: 识别左下区域路沿点
     * @param {pcl::PointCloud<PointType>} p_pointsIn 输入点云
     * @param {std::vector<int>} p_cornerId4 左下区域角点序号
     * @return {*}
     */
    void getCurbLeftDown(pcl::PointCloud<PointType> p_pointsIn, std::vector<int> p_cornerId4)
    {
        int idp = 0;
        for (int i = 0; i < (int)p_cornerId4.size(); i++)
        {
            int j = p_cornerId4[i];
            if (j > (c_idetecNum + 450) && j <= (c_idetecNum + 899))
            {
                if (j > 1800)
                    idp = j % 1800;
                else
                    idp = j;

                if (p_pointsIn.points[idp].v == 0 && p_pointsIn.points[idp - 2].v == 0
                    && p_pointsIn.points[idp - 3].v == 0 && p_pointsIn.points[idp - 4].v == 0
                    && p_pointsIn.points[idp - 5].v == 0 && p_pointsIn.points[idp - 6].v == 0
                    && p_pointsIn.points[idp - 40].v == 0)
                // if (pointzhixing(p_pointsIn, j))
                {
                    if (keZhiXing(p_pointsIn, j))
                    {
                        float z_dis = p_pointsIn.points[idp - 2].z - p_pointsIn.points[idp].z;
                        float z_dis1 = p_pointsIn.points[idp - 3].z - p_pointsIn.points[idp].z;
                        float z_dis2 = p_pointsIn.points[idp - 4].z - p_pointsIn.points[idp].z;
                        float z_dis3 = p_pointsIn.points[idp - 5].z - p_pointsIn.points[idp].z;
                        float z_dis4 = p_pointsIn.points[idp - 6].z - p_pointsIn.points[idp].z;
                        float z_dis5 = p_pointsIn.points[idp - 40].z - p_pointsIn.points[idp].z;

                        float y_dis = p_pointsIn.points[(idp - 10)].y - p_pointsIn.points[idp].y;
                        if (z_dis > 0 && z_dis1 > 0 && z_dis2 > 0 && z_dis3 > 0 && z_dis4 > 0)
                        {
                            if (z_dis5 >= (0.8 * c_fcurbHigh))
                            {
                                if (fabs(y_dis) < 0.1)
                                {
                                    for (int l = (idp - 40); l > (idp - 60); l--)
                                    {
                                        float z_dis_h =
                                            p_pointsIn.points[l].z - p_pointsIn.points[idp].z;
                                        if (z_dis_h > (1.1 * c_fcurbHigh))
                                        {
                                            // float p_dist = sqrt((p_pointsIn.points[(j+6)].x -
                                            // p_pointsIn.points[j].x) * (p_pointsIn.points[(j+6)].x
                                            // - p_pointsIn.points[j].x)
                                            // + (p_pointsIn.points[(j+6)].y -
                                            // p_pointsIn.points[j].y) * (p_pointsIn.points[(j+6)].y
                                            // - p_pointsIn.points[j].y));
                                            // if (p_dist >= xy_thresh)
                                            // {
                                            for (int n = idp; n > (idp - 6); n--)
                                            {
                                                if ((n - 2) >= (idp - 6))
                                                {
                                                    v9[0] = (p_pointsIn.points[n + 2].x
                                                             - p_pointsIn.points[n].x);
                                                    v9[1] = (p_pointsIn.points[n + 2].y
                                                             - p_pointsIn.points[n].y);
                                                    v9[2] = (p_pointsIn.points[n + 2].z
                                                             - p_pointsIn.points[n].z);

                                                    v10[0] = (p_pointsIn.points[n - 2].x
                                                              - p_pointsIn.points[n].x);
                                                    v10[1] = (p_pointsIn.points[n - 2].y
                                                              - p_pointsIn.points[n].y);
                                                    v10[2] = (p_pointsIn.points[n - 2].z
                                                              - p_pointsIn.points[n].z);

                                                    float cosVal_ =
                                                        ((v9 + v10).norm()) / ((v9 - v10).norm());
                                                    if (cosVal_ >= 0.17 && cosVal_ <= 1.25)
                                                    {
                                                        curb_leftdown.push_back(n);
                                                        return;
                                                    }
                                                }
                                            }
                                            // }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * @description: 将左右路沿点拟合直线
     * @param {pcl::PointCloud<PointType>} p_arrRight 右边路沿点
     * @param {pcl::PointCloud<PointType>} p_arrLeft 左边路沿点
     * @return {*}
     */
    void fitCurbLine(pcl::PointCloud<PointType> p_arrRight, pcl::PointCloud<PointType> p_arrLeft)
    {
        p_arrPoint.resize(4, 2);
        p_arrPoint << 0, 0, 0, 0, 0, 0, 0, 0;
        if (p_arrRight.size() >= 2)
        {
            int n = p_arrRight.size();
            float sumX = 0;
            float sumY = 0;
            Eigen::MatrixXf l_arrAX;
            l_arrAX.resize(n, 2);

            Eigen::MatrixXf l_arrAY;
            l_arrAY.resize(n, 1);

            Eigen::MatrixXf l_arrMean;
            l_arrMean.resize(1, 2);

            for (int i = 0; i < n; i++)
            {
                l_arrAX(i, 0) = p_arrRight.points[i].x;
                l_arrAX(i, 1) = p_arrRight.points[i].y;
                // l_arrAX(i, 2) = 0;
                // l_arrAX(i, 3) = 1;
                sumX = sumX + p_arrRight.points[i].x;
                sumY = sumY + p_arrRight.points[i].y;

                l_arrAY(i) = 1;
            }
            l_arrMean(0, 0) = sumX / n;
            l_arrMean(0, 1) = sumY / n;

            Eigen::MatrixXf l_arrA =
                l_arrAX.jacobiSvd(Eigen::ComputeThinU | Eigen::ComputeThinV).solve(l_arrAY);
            Eigen::MatrixXf l_arrANorm = (l_arrA / l_arrA.norm());

            if (fabs(l_arrANorm(1)) >= 0.99)
            {
                Eigen::MatrixXf l_arrB;
                l_arrB.resize(2, 2);
                float l_arrBR = l_arrA(1) * l_arrMean(0, 0) + (-l_arrA(0)) * l_arrMean(0, 1);
                Eigen::MatrixXf l_arrBY;
                l_arrBY.resize(2, 1);
                l_arrBY << 1, l_arrBR;
                l_arrB << l_arrA(0), l_arrA(1), l_arrA(1), -l_arrA(0);

                Eigen::MatrixXf l_arrBX =
                    l_arrB.jacobiSvd(Eigen::ComputeThinU | Eigen::ComputeThinV).solve(l_arrBY);
                // Eigen::Map<Eigen::RowVectorXf> l_arrATran(l_arrA.data(), l_arrA.size());

                p_arrPoint(0, 0) = l_arrA(1);
                p_arrPoint(0, 1) = -l_arrA(0);
                p_arrPoint(1, 0) = l_arrBX(0);
                p_arrPoint(1, 1) = l_arrBX(1);
            }
        }

        if (p_arrLeft.size() >= 2)
        {
            int n = p_arrLeft.size();
            float sumX_ = 0;
            float sumY_ = 0;
            Eigen::MatrixXf l_arrAX_;
            l_arrAX_.resize(n, 2);

            Eigen::MatrixXf l_arrAY_;
            l_arrAY_.resize(n, 1);

            Eigen::MatrixXf l_arrMean_;
            l_arrMean_.resize(1, 2);

            for (int i = 0; i < n; i++)
            {
                l_arrAX_(i, 0) = p_arrLeft.points[i].x;
                l_arrAX_(i, 1) = p_arrLeft.points[i].y;
                // l_arrAX(i, 2) = 0;
                // l_arrAX(i, 3) = 1;
                sumX_ = sumX_ + p_arrLeft.points[i].x;
                sumY_ = sumY_ + p_arrLeft.points[i].y;

                l_arrAY_(i) = 1;
            }

            l_arrMean_(0, 0) = sumX_ / n;
            l_arrMean_(0, 1) = sumY_ / n;

            Eigen::MatrixXf l_arrA_ =
                l_arrAX_.jacobiSvd(Eigen::ComputeThinU | Eigen::ComputeThinV).solve(l_arrAY_);
            Eigen::MatrixXf l_arrANorm_ = (l_arrA_ / l_arrA_.norm());

            if (fabs(l_arrANorm_(1)) >= 0.99)
            {
                Eigen::MatrixXf l_arrB_;
                l_arrB_.resize(2, 2);
                float l_arrBR_ = l_arrA_(1) * l_arrMean_(0, 0) + (-l_arrA_(0)) * l_arrMean_(0, 1);
                Eigen::MatrixXf l_arrBY_;
                l_arrBY_.resize(2, 1);
                l_arrBY_ << 1, l_arrBR_;
                l_arrB_ << l_arrA_(0), l_arrA_(1), l_arrA_(1), -l_arrA_(0);

                Eigen::MatrixXf l_arrBX_ =
                    l_arrB_.jacobiSvd(Eigen::ComputeThinU | Eigen::ComputeThinV).solve(l_arrBY_);
                // Eigen::Map<Eigen::RowVectorXf> l_arrATran(l_arrA.data(), l_arrA.size());

                p_arrPoint(2, 0) = l_arrA_(1);
                p_arrPoint(2, 1) = -l_arrA_(0);
                p_arrPoint(3, 0) = l_arrBX_(0);
                p_arrPoint(3, 1) = l_arrBX_(1);
            }
        }
    }

    /**
     * @description: 提取车道线点云序号
     * @param {pcl::PointCloud<PointType>} p_inLine
     * @return {*}
     */
    void getRoadLineId(pcl::PointCloud<PointType> p_inLine, int p_iLineNum)
    {
        std::vector<int> LineIDf, LineIDs;
        for (int idnum = (c_idetecNum + 900); idnum < (c_idetecNum + 1349); idnum++)
        {
            int id = idnum;
            if (ZhiXing(p_inLine, idnum))
            {
                if (idnum >= 1800)
                    id = (idnum % 1800);
                if (id >= p_iLineNum)
                    continue;

                float value = (p_inLine.points[id + 1].h / p_inLine.points[id].h);
                // int idline = whichLinePoints(p_inLine.points[id]);

                if (value >= 1.2)  // 1.4
                {
                    if (LineIDf.size() != 0)
                    {
                        int num_f = LineIDf.front();
                        if (num_f >= 1800)
                            num_f = num_f % 1800;
                        float dis_f = p_inLine.points[num_f].y - p_inLine.points[id + 1].y;
                        if (dis_f > 0.3)
                            LineIDf.insert(LineIDf.begin(), idnum + 1);
                    }
                    else
                    {
                        LineIDf.push_back(idnum + 1);
                    }
                }
                if (value < 1)
                {
                    value = 1 / value;
                    if (value >= 1.2 && LineIDf.size() != 0)  // 1.4
                    {
                        if (LineIDs.size() != 0)
                        {
                            int num_s = LineIDs.back();
                            if (num_s >= 1800)
                                num_s = num_s % 1800;
                            float dis_s = p_inLine.points[num_s].y - p_inLine.points[id + 1].y;
                            if (dis_s > 0.3)
                                LineIDs.push_back(idnum + 1);
                        }
                        else
                        {
                            int num_ss = LineIDf.front();
                            if (num_ss >= 1800)
                                num_ss = num_ss % 1800;
                            float dis_test = p_inLine.points[num_ss].y - p_inLine.points[id + 1].y;
                            if (dis_test > 0.13)
                                LineIDs.push_back(idnum + 1);
                        }
                    }
                }
                if (LineIDf.size() != 0 && LineIDs.size() != 0
                    && LineIDf.front() > (c_idetecNum + 900)
                    && LineIDf.front() <= (c_idetecNum + 1349)
                    && LineIDs.back() > (c_idetecNum + 900)
                    && LineIDs.back() <= (c_idetecNum + 1349))
                {
                    if (LineIDf.front() >= 1800)
                        LineIDf.front() = (LineIDf.front() % 1800);
                    if (LineIDs.back() >= 1800)
                        LineIDs.back() = (LineIDs.back() % 1800);
                    float dis =
                        p_inLine.points[LineIDf.front()].y - p_inLine.points[LineIDs.back()].y;
                    if (LineIDs.back() > LineIDf.front() && dis > 0.12 && dis < 0.23)
                    {
                        std::vector<int> maxValue;
                        std::vector<int> maxID;
                        for (int i = LineIDf.front(); i < LineIDs.back(); i++)
                        {
                            maxValue.push_back(p_inLine.points[i].h);
                            maxID.push_back(i);
                        }
                        std::vector<int>::iterator max =
                            std::max_element(maxValue.begin(), maxValue.end());
                        int num = std::distance(maxValue.begin(), max);
                        if (maxID.size() != 0)
                        {
                            line_rightDown.push_back(maxID[num]);
                        }
                        LineIDf.clear();
                        LineIDs.clear();
                    }
                }
            }
        }

        LineIDf.clear();
        LineIDs.clear();
        for (int idnum = (c_idetecNum + 1749); idnum > (c_idetecNum + 1350); idnum--)
        {
            int id = idnum;

            if (ZhiXing(p_inLine, idnum))
            {
                if (idnum >= 1800)
                    id = idnum % 1800;
                if (id >= p_iLineNum)
                    continue;
                float value = (p_inLine.points[id - 1].h / p_inLine.points[id].h);
                // int idline = whichLinePoints(p_inLine.points[id]);

                if (value >= 1.2)  // 1.4
                {
                    if (LineIDf.size() != 0)
                    {
                        int num_f = LineIDf.front();
                        if (num_f >= 1800)
                            num_f = num_f % 1800;
                        float dis_f = p_inLine.points[num_f].y - p_inLine.points[id - 1].y;
                        if (dis_f > 0.3)
                            LineIDf.insert(LineIDf.begin(), idnum - 1);
                    }
                    else
                    {
                        LineIDf.push_back(idnum - 1);
                    }
                }
                if (value < 1)
                {
                    value = 1 / value;
                    if (value >= 1.2 && LineIDf.size() != 0)  // 1.4
                    {
                        if (LineIDs.size() != 0)
                        {
                            int num_s = LineIDs.back();
                            if (num_s >= 1800)
                                num_s = num_s % 1800;
                            float dis_s = p_inLine.points[num_s].y - p_inLine.points[id - 1].y;
                            if (dis_s > 0.3)
                                LineIDs.push_back(idnum - 1);
                        }
                        else
                        {
                            int num_ss = LineIDf.front();
                            if (num_ss >= 1800)
                                num_ss = num_ss % 1800;
                            float dis_test = p_inLine.points[num_ss].y - p_inLine.points[id - 1].y;
                            if (dis_test > 0.13)
                                LineIDs.push_back(idnum - 1);
                        }
                    }
                }
                if (LineIDf.size() != 0 && LineIDs.size() != 0
                    && LineIDf.front() >= (c_idetecNum + 1350)
                    && LineIDf.front() <= (c_idetecNum + 1749)
                    && LineIDs.back() >= (c_idetecNum + 1350)
                    && LineIDs.back() <= (c_idetecNum + 1749))
                {
                    if (LineIDf.front() >= 1800)
                        LineIDf.front() = (LineIDf.front() % 1800);
                    if (LineIDs.back() >= 1800)
                        LineIDs.back() = (LineIDs.back() % 1800);
                    float dis =
                        p_inLine.points[LineIDf.front()].y - p_inLine.points[LineIDs.back()].y;
                    if (LineIDf.front() > LineIDs.back() && dis > 0.12 && dis < 0.23)
                    {
                        std::vector<int> maxValue;
                        std::vector<int> maxID;
                        for (int i = LineIDs.back(); i < LineIDf.front(); i++)
                        {
                            maxValue.push_back(p_inLine.points[i].h);
                            maxID.push_back(i);
                        }
                        std::vector<int>::iterator max =
                            std::max_element(maxValue.begin(), maxValue.end());
                        int num = std::distance(maxValue.begin(), max);
                        if (maxID.size() != 0)
                        {
                            line_rightUp.push_back(maxID[num]);
                        }
                        LineIDf.clear();
                        LineIDs.clear();
                    }
                }
            }
        }

        LineIDf.clear();
        LineIDs.clear();
        for (int idnum = c_idetecNum; idnum < (c_idetecNum + 449); idnum++)
        {
            int id = idnum;
            if (ZhiXing(p_inLine, idnum))
            {
                if (idnum >= 1800)
                    id = (idnum % 1800);
                if (id >= p_iLineNum)
                    continue;
                float value = (p_inLine.points[id + 1].h / p_inLine.points[id].h);
                int idline = whichLinePoints(p_inLine.points[id]);

                if (value >= 1.2 - idline * 0.02)  // 1.3
                {
                    if (LineIDf.size() != 0)
                    {
                        int num_f = LineIDf.front();
                        if (num_f >= 1800)
                            num_f = num_f % 1800;
                        float dis_f = p_inLine.points[id + 1].y - p_inLine.points[num_f].y;
                        if (dis_f > 0.3)
                            LineIDf.insert(LineIDf.begin(), idnum + 1);
                    }
                    else
                    {
                        LineIDf.push_back(idnum + 1);
                    }
                }
                if (value < 1)
                {
                    value = 1 / value;
                    if (value >= 1.2 && LineIDf.size() != 0)  // 1.3
                    {
                        if (LineIDs.size() != 0)
                        {
                            int num_s = LineIDs.back();
                            if (num_s >= 1800)
                                num_s = num_s % 1800;
                            float dis_s = p_inLine.points[id + 1].y - p_inLine.points[num_s].y;
                            if (dis_s > 0.3)
                                LineIDs.push_back(idnum + 1);
                        }
                        else
                        {
                            int num_ss = LineIDf.front();
                            if (num_ss >= 1800)
                                num_ss = num_ss % 1800;
                            float dis_test = p_inLine.points[id + 1].y - p_inLine.points[num_ss].y;
                            if (dis_test > 0.13)
                                LineIDs.push_back(idnum + 1);
                        }
                    }
                }
                if (LineIDf.size() != 0 && LineIDs.size() != 0 && LineIDf.front() >= c_idetecNum
                    && LineIDf.front() <= (c_idetecNum + 449) && LineIDs.back() >= c_idetecNum
                    && LineIDs.back() <= (c_idetecNum + 449))
                {
                    if (LineIDf.front() >= 1800)
                        LineIDf.front() = (LineIDf.front() % 1800);
                    if (LineIDs.back() >= 1800)
                        LineIDs.back() = (LineIDs.back() % 1800);
                    float dis =
                        p_inLine.points[LineIDs.back()].y - p_inLine.points[LineIDf.front()].y;
                    if (LineIDs.back() > LineIDf.front() && dis > 0.12 && dis < 0.23)
                    {
                        std::vector<int> maxValue;
                        std::vector<int> maxID;
                        for (int i = LineIDf.front(); i < LineIDs.back(); i++)
                        {
                            maxValue.push_back(p_inLine.points[i].h);
                            maxID.push_back(i);
                        }
                        std::vector<int>::iterator max =
                            std::max_element(maxValue.begin(), maxValue.end());
                        int num = std::distance(maxValue.begin(), max);
                        if (maxID.size() != 0)
                        {
                            line_leftUp.push_back(maxID[num]);
                        }
                        LineIDf.clear();
                        LineIDs.clear();
                    }
                }
            }
        }

        LineIDf.clear();
        LineIDs.clear();
        for (int idnum = (c_idetecNum + 899); idnum > (c_idetecNum + 450); idnum--)
        {
            int id = idnum;
            if (ZhiXing(p_inLine, idnum))
            {
                if (idnum >= 1800)
                    id = idnum % 1800;
                if (id >= p_iLineNum)
                    continue;
                float value = (p_inLine.points[id - 1].h / p_inLine.points[id].h);
                // int idline = whichLinePoints(p_inLine.points[id]);

                if (value >= 1.2)  // 1.3
                {
                    if (LineIDf.size() != 0)
                    {
                        int num_f = LineIDf.front();
                        if (num_f >= 1800)
                            num_f = num_f % 1800;
                        float dis_f = p_inLine.points[id - 1].y - p_inLine.points[num_f].y;
                        if (dis_f > 0.3)
                            LineIDf.insert(LineIDf.begin(), idnum + 1);
                    }
                    else
                    {
                        LineIDf.push_back(idnum - 1);
                    }
                }
                if (value < 1)
                {
                    value = 1 / value;
                    if (value >= 1.2 && LineIDf.size() != 0)  // 1.3
                    {
                        if (LineIDs.size() != 0)
                        {
                            int num_s = LineIDs.back();
                            if (num_s >= 1800)
                                num_s = num_s % 1800;
                            float dis_s = p_inLine.points[id - 1].y - p_inLine.points[num_s].y;
                            if (dis_s > 0.3)
                                LineIDs.push_back(idnum - 1);
                        }
                        else
                        {
                            int num_ss = LineIDf.front();
                            if (num_ss >= 1800)
                                num_ss = num_ss % 1800;
                            float dis_test = p_inLine.points[id - 1].y - p_inLine.points[num_ss].y;
                            if (dis_test > 0.13)
                                LineIDs.push_back(idnum - 1);
                        }
                    }
                }
                if (LineIDf.size() != 0 && LineIDs.size() != 0
                    && LineIDf.front() >= (c_idetecNum + 450)
                    && LineIDf.front() <= (c_idetecNum + 899)
                    && LineIDs.back() >= (c_idetecNum + 450)
                    && LineIDs.back() <= (c_idetecNum + 899))
                {
                    if (LineIDf.front() >= 1800)
                        LineIDf.front() = (LineIDf.front() % 1800);
                    if (LineIDs.back() >= 1800)
                        LineIDs.back() = (LineIDs.back() % 1800);
                    float dis =
                        p_inLine.points[LineIDs.back()].y - p_inLine.points[LineIDf.front()].y;
                    if (LineIDf.front() > LineIDs.back() && dis > 0.12 && dis < 0.23)
                    {
                        std::vector<int> maxValue;
                        std::vector<int> maxID;
                        for (int i = LineIDs.back(); i < LineIDf.front(); i++)
                        {
                            maxValue.push_back(p_inLine.points[i].h);
                            maxID.push_back(i);
                        }
                        std::vector<int>::iterator max =
                            std::max_element(maxValue.begin(), maxValue.end());
                        int num = std::distance(maxValue.begin(), max);
                        if (maxID.size() != 0)
                        {
                            line_leftDown.push_back(maxID[num]);
                        }
                        LineIDf.clear();
                        LineIDs.clear();
                    }
                }
            }
        }
    }

    /**
     * @description: 计算道路宽度
     * @param {pcl::PointCloud<PointType>} p_lineWide
     * @return {*}
     */
    void getRoadWide(pcl::PointCloud<PointType> p_lineWide)
    {
        std::vector<int> startId;
        pcl::PointCloud<PointType> roadline;
        pcl::PointCloud<PointType> l_testPoint;

        startId.clear();
        roadline.clear();
        l_testPoint.clear();

        for (int i = 0; i < (int)p_lineWide.size(); i++)
        {
            float juli = sqrt(pow(p_lineWide.points[i].x, 2) + pow(p_lineWide.points[i].y, 2)
                              + pow(p_lineWide.points[i].z, 2));
            int Idnum = whichLinePoints(p_lineWide.points[i]);
            if (juli > (c_flidarHigh / cos(78.0 * M_PI / 180.0) - 0.3)
                && juli < (c_flidarHigh / cos(78.0 * M_PI / 180.0) + 0.8) && Idnum == 2
                && p_lineWide.points[i].v == 0)
            {
                l_testPoint.push_back(p_lineWide.points[i]);
                startId.push_back(i);
            }
        }
        if (startId.size() != 0 && l_testPoint.size() != 0)
        {
            // pcl::io::savePCDFileASCII("paixut.pcd", l_testPoint);
            for (int j = 0; j < (int)startId.size(); j++)
            {
                int numf = startId[j + 1] - startId[j];
                // int numb = startId[startId.back()] - startId[startId.back() - j];
                if (numf > 300 && numf < 1750)
                {
                    roadline.push_back(l_testPoint.points[j]);
                    roadline.push_back(l_testPoint.points[j + 1]);
                }
                if (roadline.size() != 0 && roadline.size() == 4)
                {
                    // pcl::io::savePCDFileASCII("roadline.pcd", roadline);
                    l_roadWidef = fabs(roadline.points[1].y) + fabs(roadline.points[2].y);
                    l_roadWides = fabs(roadline.points[0].y) + fabs(roadline.points[3].y);
                }
            }
        }
    }

    /**
     * @description: 提取车前方车道线
     * @param {pcl::PointCloud<PointType> p_inRoadFront   点云
     * @param {std::vector<int> p_curbRightf    右上区域路沿
     * @param {std::vector<int> p_curbLeftf    左上区域路沿
     * @param {std::vector<int> p_lineRightf    右上区域车道线
     * @param {std::vector<int>} p_lineLeftf    左上区域车道线
     * @return {*}
     */
    void getRoadLineFront(pcl::PointCloud<PointType> p_inRoadFront,
                          std::vector<int> p_curbRightf,
                          std::vector<int> p_curbLeftf,
                          std::vector<int> p_lineRightf,
                          std::vector<int> p_lineLeftf)
    {
        if (p_curbRightf.size() != 0)
        {
            if (p_lineRightf.size() != 0)
            {
                for (int i = 0; i < (int)p_lineRightf.size(); i++)
                {
                    float dis_r = fabs(p_inRoadFront.points[p_curbRightf[0]].y
                                       - p_inRoadFront.points[p_lineRightf[i]].y);
                    if (dis_r > 1.5 && dis_r < 2.0)
                    {
                        int index = whichLinePoints(p_inRoadFront.points[p_lineRightf[i]]);
                        float dis_a = sqrt(pow(p_inRoadFront.points[p_lineRightf[i]].x, 2)
                                           + pow(p_inRoadFront.points[p_lineRightf[i]].y, 2)
                                           + pow(p_inRoadFront.points[p_lineRightf[i]].z, 2));
                        if (dis_a > (c_flidarHigh / cos(74 * M_PI / 180.0) - 0.3)
                            && dis_a < (c_flidarHigh / cos(74 * M_PI / 180.0) + 0.3) && index == 0)
                            line_front.push_back(p_lineRightf[i]);
                        if (dis_a > (c_flidarHigh / cos(76 * M_PI / 180.0) - 0.3)
                            && dis_a < (c_flidarHigh / cos(76 * M_PI / 180.0) + 0.3) && index == 1)
                            line_front.push_back(p_lineRightf[i]);
                        if (dis_a > (c_flidarHigh / cos(78 * M_PI / 180.0) - 0.3)
                            && dis_a < (c_flidarHigh / cos(78 * M_PI / 180.0) + 0.3) && index == 2)
                            line_front.push_back(p_lineRightf[i]);
                    }
                }
            }
        }

        if (p_curbLeftf.size() != 0)
        {
            if (p_lineLeftf.size() != 0)
            {
                for (int j = 0; j < (int)p_lineLeftf.size(); j++)
                {
                    float dis_l = fabs(p_inRoadFront.points[p_curbLeftf[0]].y
                                       - p_inRoadFront.points[p_lineLeftf[j]].y);
                    if (dis_l > 1.5 && dis_l < 2.0)
                    {
                        int index = whichLinePoints(p_inRoadFront.points[p_lineLeftf[j]]);
                        float dis_a = sqrt(pow(p_inRoadFront.points[p_lineLeftf[j]].x, 2)
                                           + pow(p_inRoadFront.points[p_lineLeftf[j]].y, 2)
                                           + pow(p_inRoadFront.points[p_lineLeftf[j]].z, 2));
                        if (dis_a > (c_flidarHigh / cos(74 * M_PI / 180.0) - 0.3)
                            && dis_a < (c_flidarHigh / cos(74 * M_PI / 180.0) + 0.4) && index == 0)
                            line_front.push_back(p_lineLeftf[j]);
                        if (dis_a > (c_flidarHigh / cos(76 * M_PI / 180.0) - 0.3)
                            && dis_a < (c_flidarHigh / cos(76 * M_PI / 180.0) + 0.4) && index == 1)
                            line_front.push_back(p_lineLeftf[j]);
                        if (dis_a > (c_flidarHigh / cos(78 * M_PI / 180.0) - 0.3)
                            && dis_a < (c_flidarHigh / cos(78 * M_PI / 180.0) + 0.4) && index == 2)
                            line_front.push_back(p_lineLeftf[j]);
                    }
                    if (dis_l > 5.5 && dis_l < 6.3)
                    {
                        int index = whichLinePoints(p_inRoadFront.points[p_lineLeftf[j]]);
                        float dis_a = sqrt(pow(p_inRoadFront.points[p_lineLeftf[j]].x, 2)
                                           + pow(p_inRoadFront.points[p_lineLeftf[j]].y, 2)
                                           + pow(p_inRoadFront.points[p_lineLeftf[j]].z, 2));
                        if (dis_a > (c_flidarHigh / cos(74 * M_PI / 180.0) - 0.3)
                            && dis_a < (c_flidarHigh / cos(74 * M_PI / 180.0) + 0.4) && index == 0)
                            line_front.push_back(p_lineLeftf[j]);
                        if (dis_a > (c_flidarHigh / cos(76 * M_PI / 180.0) - 0.3)
                            && dis_a < (c_flidarHigh / cos(76 * M_PI / 180.0) + 0.4) && index == 1)
                            line_front.push_back(p_lineLeftf[j]);
                        if (dis_a > (c_flidarHigh / cos(78 * M_PI / 180.0) - 0.3)
                            && dis_a < (c_flidarHigh / cos(78 * M_PI / 180.0) + 0.4) && index == 2)
                            line_front.push_back(p_lineLeftf[j]);
                    }
                }
            }
        }

        if (p_curbRightf.empty() || p_curbLeftf.empty())
        {
            p_lineRightf.insert(p_lineRightf.end(), p_lineLeftf.begin(), p_lineLeftf.end());
            for (int k = 0; k < (int)p_lineRightf.size(); k++)
            {
                int index = whichLinePoints(p_inRoadFront.points[p_lineRightf[k]]);
                float dis_a = sqrt(pow(p_inRoadFront.points[p_lineRightf[k]].x, 2)
                                   + pow(p_inRoadFront.points[p_lineRightf[k]].y, 2)
                                   + pow(p_inRoadFront.points[p_lineRightf[k]].z, 2));
                if (dis_a > (c_flidarHigh / cos(74 * M_PI / 180.0) - 0.3)
                    && dis_a < (c_flidarHigh / cos(74 * M_PI / 180.0) + 0.4) && index == 0)
                    line_front.push_back(p_lineRightf[k]);
                if (dis_a > (c_flidarHigh / cos(76 * M_PI / 180.0) - 0.3)
                    && dis_a < (c_flidarHigh / cos(76 * M_PI / 180.0) + 0.4) && index == 1)
                    line_front.push_back(p_lineRightf[k]);
                if (dis_a > (c_flidarHigh / cos(78 * M_PI / 180.0) - 0.3)
                    && dis_a < (c_flidarHigh / cos(78 * M_PI / 180.0) + 0.4) && index == 2)
                    line_front.push_back(p_lineRightf[k]);
            }
            if (line_front.size() >= 8)
                line_front.clear();
        }
    }

    /**
     * @description: 提取车后方车道线
     * @param {PointCloud<PointType> p_inRoadBack    点云
     * @param {std::vector<int> p_curbRightb    右下区域路沿
     * @param {std::vector<int> p_curbLeftb    左下区域路沿
     * @param {std::vector<int> p_lineRightb   右下区域车道线
     * @param {std::vector<int>} p_lineLeftb    左下区域车道线
     * @return {*}
     */
    void getRoadLineBack(pcl::PointCloud<PointType> p_inRoadBack,
                         std::vector<int> p_curbRightb,
                         std::vector<int> p_curbLeftb,
                         std::vector<int> p_lineRightb,
                         std::vector<int> p_lineLeftb)
    {
        if (p_curbRightb.size() != 0)
        {
            if (p_lineRightb.size() != 0)
            {
                for (int i = 0; i < (int)p_lineRightb.size(); i++)
                {
                    float dis_r = fabs(p_inRoadBack.points[p_curbRightb[0]].y
                                       - p_inRoadBack.points[p_lineRightb[i]].y);
                    if (dis_r > 1.5 && dis_r < 2.0)
                    {
                        int index = whichLinePoints(p_inRoadBack.points[p_lineRightb[i]]);
                        float dis_a = sqrt(pow(p_inRoadBack.points[p_lineRightb[i]].x, 2)
                                           + pow(p_inRoadBack.points[p_lineRightb[i]].y, 2)
                                           + pow(p_inRoadBack.points[p_lineRightb[i]].z, 2));
                        if (dis_a > (c_flidarHigh / cos(74 * M_PI / 180.0) - 0.3)
                            && dis_a < (c_flidarHigh / cos(74 * M_PI / 180.0) + 0.4) && index == 0)
                            line_back.push_back(p_lineRightb[i]);
                        if (dis_a > (c_flidarHigh / cos(76 * M_PI / 180.0) - 0.3)
                            && dis_a < (c_flidarHigh / cos(76 * M_PI / 180.0) + 0.4) && index == 1)
                            line_back.push_back(p_lineRightb[i]);
                        if (dis_a > (c_flidarHigh / cos(78 * M_PI / 180.0) - 0.3)
                            && dis_a < (c_flidarHigh / cos(78 * M_PI / 180.0) + 0.4) && index == 2)
                            line_back.push_back(p_lineRightb[i]);
                    }
                }
            }
        }

        if (p_curbLeftb.size() != 0)
        {
            if (p_lineLeftb.size() != 0)
            {
                for (int j = 0; j < (int)p_lineLeftb.size(); j++)
                {
                    float dis_l = fabs(p_inRoadBack.points[p_curbLeftb[0]].y
                                       - p_inRoadBack.points[p_lineLeftb[j]].y);
                    if (dis_l > 1.5 && dis_l < 2.0)
                    {
                        int index = whichLinePoints(p_inRoadBack.points[p_lineLeftb[j]]);
                        float dis_a = sqrt(pow(p_inRoadBack.points[p_lineLeftb[j]].x, 2)
                                           + pow(p_inRoadBack.points[p_lineLeftb[j]].y, 2)
                                           + pow(p_inRoadBack.points[p_lineLeftb[j]].z, 2));
                        if (dis_a > (c_flidarHigh / cos(74 * M_PI / 180.0) - 0.3)
                            && dis_a < (c_flidarHigh / cos(74 * M_PI / 180.0) + 0.4) && index == 0)
                            line_back.push_back(p_lineLeftb[j]);
                        if (dis_a > (c_flidarHigh / cos(76 * M_PI / 180.0) - 0.3)
                            && dis_a < (c_flidarHigh / cos(76 * M_PI / 180.0) + 0.4) && index == 1)
                            line_back.push_back(p_lineLeftb[j]);
                        if (dis_a > (c_flidarHigh / cos(78 * M_PI / 180.0) - 0.3)
                            && dis_a < (c_flidarHigh / cos(78 * M_PI / 180.0) + 0.4) && index == 2)
                            line_back.push_back(p_lineLeftb[j]);
                    }
                    if (dis_l > 5.5 && dis_l < 6.3)
                    {
                        int index = whichLinePoints(p_inRoadBack.points[p_lineLeftb[j]]);
                        float dis_a = sqrt(pow(p_inRoadBack.points[p_lineLeftb[j]].x, 2)
                                           + pow(p_inRoadBack.points[p_lineLeftb[j]].y, 2)
                                           + pow(p_inRoadBack.points[p_lineLeftb[j]].z, 2));
                        if (dis_a > (c_flidarHigh / cos(74 * M_PI / 180.0) - 0.3)
                            && dis_a < (c_flidarHigh / cos(74 * M_PI / 180.0) + 0.4) && index == 0)
                            line_back.push_back(p_lineLeftb[j]);
                        if (dis_a > (c_flidarHigh / cos(76 * M_PI / 180.0) - 0.3)
                            && dis_a < (c_flidarHigh / cos(76 * M_PI / 180.0) + 0.4) && index == 1)
                            line_back.push_back(p_lineLeftb[j]);
                        if (dis_a > (c_flidarHigh / cos(78 * M_PI / 180.0) - 0.3)
                            && dis_a < (c_flidarHigh / cos(78 * M_PI / 180.0) + 0.4) && index == 2)
                            line_back.push_back(p_lineLeftb[j]);
                    }
                }
            }
        }

        if (p_curbRightb.empty() || p_curbLeftb.empty())
        {
            p_lineRightb.insert(p_lineRightb.end(), p_lineLeftb.begin(), p_lineLeftb.end());
            {
                for (int k = 0; k < (int)p_lineRightb.size(); k++)
                {
                    int index = whichLinePoints(p_inRoadBack.points[p_lineRightb[k]]);
                    float dis_a = sqrt(pow(p_inRoadBack.points[p_lineRightb[k]].x, 2)
                                       + pow(p_inRoadBack.points[p_lineRightb[k]].y, 2)
                                       + pow(p_inRoadBack.points[p_lineRightb[k]].z, 2));
                    if (dis_a > (c_flidarHigh / cos(74 * M_PI / 180.0) - 0.3)
                        && dis_a < (c_flidarHigh / cos(74 * M_PI / 180.0) + 0.4) && index == 0)
                        line_back.push_back(p_lineRightb[k]);
                    if (dis_a > (c_flidarHigh / cos(76 * M_PI / 180.0) - 0.3)
                        && dis_a < (c_flidarHigh / cos(76 * M_PI / 180.0) + 0.4) && index == 1)
                        line_back.push_back(p_lineRightb[k]);
                    if (dis_a > (c_flidarHigh / cos(78 * M_PI / 180.0) - 0.3)
                        && dis_a < (c_flidarHigh / cos(78 * M_PI / 180.0) + 0.4) && index == 2)
                        line_back.push_back(p_lineRightb[k]);
                }
                if (line_back.size() >= 8)
                    line_back.clear();
            }
        }
    }
};