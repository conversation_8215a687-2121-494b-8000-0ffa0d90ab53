/*
 * @Author: your name
 * @Date: 2021-04-21 14:38:14
 * @LastEditTime: 2023-07-04 15:14:04
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @Description: In User Settings Edit
 * @FilePath: /test_ws/src/wanji_ros/wslam_pretreat/include/featureExtract.h
 */
#pragma once
#ifndef __wslam_pretreat_FEATUREEXTRACT_H
#    define __wslam_pretreat_FEATUREEXTRACT_H 1

#    include "common_WLR720.h"
#    include "omp.h"
#    include "wj_log.h"
#    include <Eigen/Dense>
#    include <float.h>
namespace wj_FE {

/*///////////////////////////////usecase///////////////////////////////
1. 构造：
    fe_ = new FeatureExtract(false); // 不启动debug
2. 设置参数：
    fe_->setParamOutDoor(std::string()); //室外默认参数
    或 fe_->setParamInDoor(std::string());  //室内默认参数
    fe_->setFeatureZSpace(z_min,z_max);  //设置z范围,必须在setParamXXX之后 否则被覆盖
    //也可以修改std::string()从外部加载参数
    //还可以手动设置使用特征类型(默认不使用SPA和BSM)：
    例如： fe_->c_stCfg_.m_use.m_bUseSPA = false; //禁止使用SPA分段特征
    fe_->c_stCfg_.m_use.m_bUseGND = true;
    fe_->setLidarHeight(lidarHeight);
    classificateCloud_() 中 l_iMidLineId控制地面可能出现在哪些线
3. 使用：
    // 将完整点云inputCloud深拷贝
    fe_->setInputCloud(inputCloud);
    // 提取特征到outputcloud1和outputcloud2
    fe_->extractFeatureOneBlock_(outputcloud1,outputcloud2);
    // 获取debug点云
    fe_->getSegmentCloud(); // 全部分类成功的点云段
    fe_->getDownSizeCloud(); // 下采样点云

4. 耗时：
    四线程3.8GHz：平均6ms，峰值14ms

/////////////////////////////////usecase/////////////////////////////*/

class FeatureExtract {
  public:
    enum state { DONE = 0, FAIL };
    enum SCENARIO { DEFAULT = -1, INDOOR, OUTDOOR, VACROUS, LABOR };
    // 点分类描述
    enum FuTyp {
        UNK_F = 0,  // 未定义类型点(不输出) 0
        BSM_F,      // 背景小物体面点      1
        GND_F,      // 地面面点           2
        SPA_F,      // 稀疏大物体面点      3
        RND_F,      // 类圆大物体面点      4
        SHELT,      // 遮挡角点           5
        FSM_F,      // 前景小物体角点      6
        NML_F,      // 一般大物体角面点     7
    };

  private:
    typedef pcl::PointXYZHSV PFeature;
    typedef pcl::PointCloud<PFeature> pcFeature;
    typedef pcFeature::Ptr pcFeaturePtr;

    typedef pcl::PointXYZI pDs;
    typedef pcl::PointCloud<pcl::PointXYZI> pcDs;
    typedef pcl::PointCloud<pcl::PointXYZI>::Ptr pcDsPtr;

#    pragma region Inner - Config
    /////////////** configuration parameters *//////////////
    typedef struct st_UseConfig
    {
        bool m_bUseNML;  //是否使用正常物体提取特征
        bool m_bUseFSM;  //是否使用前景小物体提前角点
        bool m_bUseSHT;  //是否使用正常物体遮挡前沿提取角点
        bool m_bUseRND;  //是否使用圆形物体提取面点
        bool m_bUseSPA;  //是否使用稀疏物体物体提取面点
        bool m_bUseBSM;  //是否使用背景小物体物体提取面点
        bool m_bUseGND;  //是否使用地面提取面点
        bool m_bUseUNK;  //是否使用背景小物体物体提取面点
        st_UseConfig()
            : m_bUseNML(true), m_bUseFSM(true), m_bUseSHT(true), m_bUseRND(true), m_bUseSPA(true),
              m_bUseBSM(false), m_bUseGND(false), m_bUseUNK(false)
        {
        }
        void reset()
        {
            *this = st_UseConfig();
        }
        /**
         * @description:
         * @param {std::vector<int>&} p_vUsingSituation 设置特征点使用情况
         * @return {*}
         * @other:
         */
        void set(const std::vector<int>& p_vUsingSituation)
        {
            // 传入设置数量
            int l_iSetSize = p_vUsingSituation.size();
            //指向结构体的指针
            bool* p_thisMember = &this->m_bUseNML;
            for (int i = 0; i < int(sizeof(this) / sizeof(bool)); ++i)
            {
                if (i < l_iSetSize)
                    (*p_thisMember++) = static_cast<bool>(p_vUsingSituation[i]);
                // 禁止修改未传入设置
                else
                    (*p_thisMember++) = false;
            }
        }
        const std::vector<int> getSituation()
        {
            std::vector<int> l_vUsingSituation(sizeof(this) / sizeof(bool), 0);
            //指向结构体的指针
            bool* p_thisMember = &this->m_bUseNML;
            for (int i = 0; i < int(sizeof(this) / sizeof(bool)); ++i)
                l_vUsingSituation[i] = (*p_thisMember == true) ? 1 : 0;
            return l_vUsingSituation;
        }
        void printf()
        {
            LOGF(WDEBUG,
                 "{} [FE Param] type:[NML:{}, FSM:{}, SHT:{}, RND:{}, SPA:{}, BSM:{}, GND:{}]",
                 WJLog::getWholeSysTime(),
                 m_bUseNML,
                 m_bUseFSM,
                 m_bUseSHT,
                 m_bUseRND,
                 m_bUseSPA,
                 m_bUseBSM,
                 m_bUseGND);
        }
    } st_UseConfig;

    typedef struct st_smoothConfig
    {
        bool m_bIntensitySmoothCheck;  //是否检查强度变化烈度
        bool m_bLineSmoothCheck;       //是否检查角度变化烈度
        u_int m_uiMaxIntensityDiff;  // 相邻点的强度变化阈值，变化剧烈的点太多将删除
        float m_fMaxLineAngDiff;  // 相邻点的位置变化阈值，变化剧烈的点太多将删除
        float m_fIntenDiffPercent;  // 强度不平滑点的最大比例[0,1]
        float m_fAngDiffPercent;    // 位置不平滑点的最大比例[0,1]
        st_smoothConfig()
            : m_bIntensitySmoothCheck(true), m_bLineSmoothCheck(true), m_uiMaxIntensityDiff(6),
              m_fMaxLineAngDiff(cos(100.0f * D2R)), m_fIntenDiffPercent(0.5), m_fAngDiffPercent(0.5)
        {
        }
        void reset()
        {
            *this = st_smoothConfig();
        }
        void setIntensitySmoothCheck(bool p_bTurnOn, u_int p_uiAllowDiff, float p_fPrecentThr)
        {
            m_bIntensitySmoothCheck = p_bTurnOn;
            m_uiMaxIntensityDiff = p_uiAllowDiff;
            m_fIntenDiffPercent = p_fPrecentThr;
        }
        void setLineSmoothCheck(bool p_bTurnOn, float p_fAllowDiff, float p_fPrecentThr)
        {
            m_bLineSmoothCheck = p_bTurnOn;
            m_fMaxLineAngDiff = cos(p_fAllowDiff * D2R);
            m_fAngDiffPercent = p_fPrecentThr;
        }
        void printf()
        {
            LOGF(WDEBUG,
                 "{} [FE Param] check IntensitySmooth:{}, max: {} with precent {}.",
                 WJLog::getWholeSysTime(),
                 m_bIntensitySmoothCheck,
                 m_uiMaxIntensityDiff,
                 m_fIntenDiffPercent);
            LOGF(WDEBUG,
                 "{} [FE Param] check LineAngleSmooth:{}, max: {} with precent {}.",
                 WJLog::getWholeSysTime(),
                 m_bLineSmoothCheck,
                 acos(m_fMaxLineAngDiff) * R2D,
                 m_fAngDiffPercent);
        }
    } st_smoothConfig;
    typedef struct st_ClassConfig
    {
        float m_fBondaryDiffMulti;  // 边界拖尾点 = 两点间距离>近点距离*阈值
        st_smoothConfig m_del;      // 删除分段规则
        u_int m_uiMinBlkPoints;
        float m_fSmallScale[2];  // 小物体的宽度限制
        float m_fMaxRadiusRnd;   // 允许判为圆弧形的最大半径
        st_ClassConfig()
            : m_fBondaryDiffMulti(
                  std::fabs(sin(WLR720_POINT_ANGINCREMENT * D2R) / sin(10.0f * D2R))),
              m_del(), m_uiMinBlkPoints(3), m_fSmallScale{0.05, 0.20}, m_fMaxRadiusRnd(1.8)
        {
        }
        void reset()
        {
            *this = st_ClassConfig();
        }
        void setBondaryAngle(const float p_fBondaryAngle)
        {
            m_fBondaryDiffMulti =
                std::fabs(sin(WLR720_POINT_ANGINCREMENT * D2R) / sin(p_fBondaryAngle * D2R));
        }
        void setMinBlkPoints(const u_int p_uiMinBlkPoints)
        {
            m_uiMinBlkPoints = p_uiMinBlkPoints;
        }
        void setSmallThingScaleRange(const float p_fMinScale, const float p_fMaxScale)
        {
            m_fSmallScale[0] = p_fMinScale;
            m_fSmallScale[1] = p_fMaxScale;
        }
        void setMaxRndRadius(const float p_fMaxRadiusRnd)
        {
            m_fMaxRadiusRnd = p_fMaxRadiusRnd;
        }
        void printf()
        {
            LOGF(WDEBUG,
                 "{} [FE Param] BondaryAngle: {:.0f}(deg).",
                 WJLog::getWholeSysTime(),
                 asin(sin(WLR720_POINT_ANGINCREMENT * D2R) / m_fBondaryDiffMulti) * R2D);
            LOGF(WDEBUG,
                 "{} [FE Param] Min PointNum within one block is {}.",
                 WJLog::getWholeSysTime(),
                 m_uiMinBlkPoints);
            LOGF(WDEBUG,
                 "{} [FE Param] smallThings: {:.2f}(m)~{:.2f}(m).",
                 WJLog::getWholeSysTime(),
                 m_fSmallScale[0],
                 m_fSmallScale[1]);
            LOGF(WDEBUG,
                 "{} [FE Param] Max Radius of identified round-block: {:.1f}(m).",
                 WJLog::getWholeSysTime(),
                 m_fMaxRadiusRnd);
        }
    } st_ClassConfig;
    typedef struct st_FeatureConfig
    {
        float m_fPlaneNoCornMulti;  // 禁止判为角点的大入射平面点 = 两点间距离>近点距离*阈值
        float m_fCornCurvRange[2];  // 判为角点的特征值范围
        float m_fMaxEvalueSurf;     // 判为面点的最大特征值
        float m_fCornDistRange[2];  // [m]角点距离范围
        st_FeatureConfig()
            : m_fPlaneNoCornMulti(
                  std::fabs(sin(WLR720_POINT_ANGINCREMENT * D2R) / sin(20.0f * D2R))),
              m_fCornCurvRange{cos(135.0f * D2R), cos(45.0f * D2R)},
              m_fMaxEvalueSurf(cos(165.0f * D2R)), m_fCornDistRange{1.0, 42.0}
        {
        }
        void reset()
        {
            *this = st_FeatureConfig();
        }
        void setCornDistRange(const float p_fMinDist, const float p_fMaxDist)
        {
            m_fCornDistRange[0] = p_fMinDist;
            m_fCornDistRange[1] = p_fMaxDist;
        }
        void setCornCurvRange(const float l_fMinAngle, const float l_fMaxAngle)
        {
            m_fCornCurvRange[1] = cos(l_fMinAngle * D2R);
            m_fCornCurvRange[0] = cos(l_fMaxAngle * D2R);
        }
        void setSurfCurvRange(const float l_fMinAngle)
        {
            m_fMaxEvalueSurf = cos(l_fMinAngle * D2R);
        }
        void printf()
        {
            LOGF(WDEBUG,
                 "{} [FE Param] No Corner-in-plane under: {:.0f}(deg).",
                 WJLog::getWholeSysTime(),
                 asin(sin(WLR720_POINT_ANGINCREMENT * D2R) / m_fPlaneNoCornMulti) * R2D);
            LOGF(WDEBUG,
                 "{} [FE Param] corner within: {:.0f}(m)~{:.0f}(m).",
                 WJLog::getWholeSysTime(),
                 m_fCornDistRange[0],
                 m_fCornDistRange[1]);
            LOGF(WDEBUG,
                 "{} [FE Param] corner within: {:.0f}(deg)~{:.0f}(deg).",
                 WJLog::getWholeSysTime(),
                 acos(m_fCornCurvRange[1]) * R2D,
                 acos(m_fCornCurvRange[0]) * R2D);
            LOGF(WDEBUG,
                 "{} [FE Param] surf within: {:.0f}(deg)~180(deg).",
                 WJLog::getWholeSysTime(),
                 acos(m_fMaxEvalueSurf) * R2D);
        }
    } st_FeatureConfig;
    typedef struct st_AttachConfig
    {
        float m_fLidarHeight;                //雷达高度（实际高度）
        float m_fGndRange[2];                //地面高度范围上下限（雷达坐标系）
        Eigen::Vector3f m_fFeatureRange[2];  //最小最大位置，限制立方体
        st_AttachConfig()
            : m_fLidarHeight(9999.0), m_fGndRange{-9999.3, -9998.7},
              m_fFeatureRange{Eigen::Vector3f::Constant(-FLT_MAX),
                              Eigen::Vector3f::Constant(FLT_MAX)}
        {
        }
        void reset()
        {
            *this = st_AttachConfig();
        }
        void setLidarHeight(const float p_fHeight, const float p_fGndRange)
        {
            m_fLidarHeight = p_fHeight;
            m_fGndRange[1] = -p_fHeight + p_fGndRange;
            m_fGndRange[0] = -p_fHeight - p_fGndRange;
        }
        void setFeatureXYSpace(const float p_xmin,
                               const float p_ymin,
                               const float p_xmax,
                               const float p_ymax)
        {
            m_fFeatureRange[0].x() = p_xmin;
            m_fFeatureRange[0].y() = p_ymin;
            m_fFeatureRange[1].x() = p_xmax;
            m_fFeatureRange[1].y() = p_ymax;
        }
        void setFeatureZSpace(const float p_zmin, const float p_zmax)
        {
            m_fFeatureRange[0].z() = p_zmin;
            m_fFeatureRange[1].z() = p_zmax;
        }
        void printf()
        {
            LOGF(WDEBUG,
                 "{} [FE Param] lidar height {:.2f}(m).",
                 WJLog::getWholeSysTime(),
                 m_fLidarHeight);
            LOGF(WDEBUG,
                 "{} [FE Param] under-space height: {:.2f}(m)~{:.2f}(m).",
                 WJLog::getWholeSysTime(),
                 m_fGndRange[0],
                 m_fGndRange[1]);
            LOGF(WDEBUG,
                 "{} [FE Param] feature height: {:.2f}(m)~{:.2f}(m).",
                 WJLog::getWholeSysTime(),
                 m_fFeatureRange[0].z(),
                 m_fFeatureRange[1].z());
        }
    } st_AttachConfig;
    typedef struct st_FilterConfig
    {
        bool m_bVerticalCornFilter;   //角点垂直点数过滤
        bool m_bUniSampleSurfFilter;  //面点均匀采样过滤
        u_int m_uiMaxSurfPntNum;      //最大面点数限制
        float m_fSampleRange[3];      //面点均匀采样距离区间
        float m_fSampleScale[5];      //面点采样尺度
        float m_fCornCyldRadius;      //角点圆柱半径
        float m_fCornCyldMinHeight;   //角点圆柱最小高度
        u_int m_uiCornCyldMinPoints;  //角点圆柱最少点数
        st_FilterConfig()
            : m_bVerticalCornFilter(true), m_bUniSampleSurfFilter(true), m_uiMaxSurfPntNum(3000),
              m_fSampleRange{8.0, 16.0, 24.0}, m_fSampleScale{0.16, 0.09, 0.04, 0.01, 0.36},
              m_fCornCyldRadius(0.3), m_fCornCyldMinHeight(0.3), m_uiCornCyldMinPoints(2)
        {
        }
        void reset()
        {
            *this = st_FilterConfig();
        }
        void setVerticalCornFilter(const bool p_bFilter,
                                   const u_int p_uiMinPoints,
                                   const float p_fRadius,
                                   const float p_fMinHeight)
        {
            m_bVerticalCornFilter = p_bFilter;
            m_uiCornCyldMinPoints = p_uiMinPoints;
            m_fCornCyldRadius = p_fRadius;
            m_fCornCyldMinHeight = p_fMinHeight;
        }
        void setUniSampleSurfFilter(const bool p_bFilter,
                                    const u_int p_uiMaxSurfPntNum,
                                    const std::vector<float>* p_fSampleRange,
                                    const std::vector<float>* p_fSampleScale)
        {
            m_bUniSampleSurfFilter = p_bFilter;
            m_uiMaxSurfPntNum = p_uiMaxSurfPntNum;
            if (p_fSampleRange)
                memcpy(m_fSampleRange, &(*p_fSampleRange)[0], sizeof(m_fSampleRange));
            if (p_fSampleScale)
                memcpy(m_fSampleScale, &(*p_fSampleScale)[0], sizeof(m_fSampleScale));
        }
        void printf()
        {
            LOGF(WDEBUG,
                 "{} [FE Param] corner cylinder-filter:{}, at least {} Points in "
                 "cylinder(high{:.1f}(m),radius{:.1f}(m)).",
                 WJLog::getWholeSysTime(),
                 m_bVerticalCornFilter,
                 m_uiCornCyldMinPoints,
                 m_fCornCyldRadius,
                 m_fCornCyldMinHeight);
            LOGF(WDEBUG,
                 "{} [FE Param] surf uniform-filter:{}, with predict-max {} "
                 "Points",
                 WJLog::getWholeSysTime(),
                 m_bUniSampleSurfFilter,
                 m_uiMaxSurfPntNum);
            LOGF(WDEBUG,
                 "{} [FE Param] uniform-filter range: 0(m)<{:.0f}(m)<{:.0f}(m)<{:.0f}(m)<inf.",
                 WJLog::getWholeSysTime(),
                 m_fSampleRange[0],
                 m_fSampleRange[1],
                 m_fSampleRange[2]);
            LOGF(WDEBUG,
                 "{} [FE Param] uniform-filter scale: [{:.2f},{:.2f},{:.2f},{:.2f}] , "
                 "ext-groundHeight:{:.2f}",
                 WJLog::getWholeSysTime(),
                 m_fSampleScale[0],
                 m_fSampleScale[1],
                 m_fSampleScale[2],
                 m_fSampleScale[3],
                 m_fSampleScale[4]);
        }
    } st_TotalFilterConfig;
    typedef struct st_ScanContextConfig
    {
        int m_iRing;       // 20 in the original paper (IROS 18)
        int m_iSector;     // 60 in the original paper (IROS 18)
        double m_iRadius;  // 80 meter max in the original paper (IROS 18)
        st_ScanContextConfig() : m_iRing(20), m_iSector(60), m_iRadius(50) {}
        void setMaxRadius(double p_fRadius)
        {
            m_iRadius = p_fRadius;
        }
        void setRingSector(int p_iRing, int p_iSector)
        {
            m_iRing = p_iRing;
            m_iSector = p_iSector;
        }
        void printf()
        {
            LOGF(WDEBUG,
                 "{} [FE Param] SC :[Ring {},Sector{},Radius{:.2f}]",
                 WJLog::getWholeSysTime(),
                 m_iRing,
                 m_iSector,
                 m_iRadius);
        }
    } st_ScanContextConfig;
    typedef struct st_Config
    {
        st_UseConfig m_use;
        st_ClassConfig m_class;
        st_FeatureConfig m_featu;
        st_AttachConfig m_attach;
        st_FilterConfig m_whlFil;
        st_ScanContextConfig m_sc;
        st_Config() : m_use(), m_class(), m_featu(), m_attach(), m_whlFil(), m_sc() {}
        void printf()
        {
            m_use.printf();
            m_class.printf();
            m_featu.printf();
            m_attach.printf();
            m_whlFil.printf();
            m_sc.printf();
        }
    } st_Config;
    st_Config c_stCfg_;
    // 模式
    int c_iScenarioMode_;
#    pragma endregion

    // label
    // 点块分类描述
    enum blkTyp {
        UNK_BLK = 0,  //  未定义类型
        GND_BLK,      //  地面类型   1
        BSM_BLK,      //  背景小物体 2
        SPA_BLK,      //  稀疏大物体 3
        RND_BLK,      //  类圆大物体 4
        FSM_BLK,      //  前景小物体 5
        NML_BLK       //  一般大物体 6
    };

    // 边界点分类描述
    enum shltTye { OTHER = 0, EMPTY, BACK, FRNT, NONE };
    // 预标记标签，角点预标记为NOT，面点预标记为MAY
    // CLab初始化为NOT，首先经过DS标记出MAY，再经过FILTER标记为NOT
    enum labTyp { NOT, MAY };

    constexpr static float EXP = 0.001f;
    // 1/180.0*M_PI
    constexpr static double D2R = 0.017453292519943;
    // 1*180.0/M_PI
    constexpr static double R2D = 57.295779513082;

    bool c_bDebug_;  // debug 模式
    // static const u_int MAX_NUM_THREAD = 1;   // 线程数

    // static const u_int BLK_MINNUM = 3;               // 最小物体点数
    static const u_int EV_HARFNUM = 3;               // 大物体特征值计算半窗点数
    static const u_int EV_NUM = 2 * EV_HARFNUM + 1;  // 大物体特征值计算窗口大小
    // static const u_int HORIZON_SCAN_NUM = HORIZON_SCAN_NUM;      // 大物体特征值计算半窗点数

    /////////////** configuration parameters *//////////////

    // 参数读写锁
    // WR_Mutex c_mtParamMutex_;
    // scanTime
    u_int64_t c_uiScanTimeStamp_;

    // 一条扫面线的描述
    typedef struct st_fCloud
    {
        s_PCloud m_pc;
        int8_t m_iCLab[HORIZON_SCAN_NUM];
        int8_t m_iSLab[HORIZON_SCAN_NUM];
    } st_fCloud;
    // 一段点云的分段分类描述
    typedef struct st_Block
    {
        st_Block(int i) : m_iType(blkTyp::UNK_BLK), m_uiLen(1), m_uiSta(i), m_uiEnd(i) {}
        blkTyp m_iType;      // 分段类型 in blkTyp
        shltTye m_iStaType;  // 起点类型
        shltTye m_iEndType;  // 终点类型
        u_int32_t m_uiLen;   // 分段长度
        u_int32_t m_uiSta;   // 起点序号
        u_int32_t m_uiEnd;   // 终点序号
    } st_Block;

    // 特征值
    //点云曲率排序容器
    typedef struct st_EValue
    {
        float m_fValue;
        u_int32_t m_uiInd;
    } st_EValue;

    // 16*1800完整扫描储存区的拷贝
    st_fCloud c_pcScansCopy_[WLR720_SCANS_PER_FIRING];
    // 16*角点储存区
    pcFeaturePtr c_pcScanCorn_[WLR720_SCANS_PER_FIRING];
    // 16*面点储存区
    pcFeaturePtr c_pcScanSurf_[WLR720_SCANS_PER_FIRING];
    // 面点采样中转
    pcFeaturePtr c_pcSurfTransfer_;
    // 采样点数
    int c_iSampleSurfSize;
    std::vector<int> c_viCurbLineId;
    // debug
    pcFeaturePtr c_pcSegment_;
    pcFeaturePtr c_pcSegmentMiddleScan_;
    pcFeaturePtr c_pcSegmentCurbScan_;
    pcDsPtr c_pcDsCloud_;
    pcFeaturePtr c_pcAll_;

  public:
    /**
     * @brief: 默认构造函数，从默认位置加载参数
     * @note: 如果开启debug，必须使用 getSegmentCloud，否则debug信息会会不断积累
     * @param debug 是否调试模式
     * @return {*}
     */
    FeatureExtract();

    /**
     * @brief: 指定构造函数，从指定位置加载参数
     * @note: 如果开启debug，必须使用 getSegmentCloud，否则debug信息会会不断积累
     * @param {string} p_sConfigPath
     * @param debug 是否调试模式
     * @return {*}
     */
    FeatureExtract(bool p_bDebug = false);

    /**
     * @brief: 设置参数组
     * @param p_Mode 参数组名称
     * @return 0-完成
     */
    void setParamScenario(int p_Mode);

    /**
     * @brief: 设置debug模式
     * @param p_bDebug debug模式开关
     * @return 0-完成
     */
    void setDebugMode(bool p_bDebug = true)
    {
        c_bDebug_ = p_bDebug;
    }

    /**
     * @description: 打印参数
     * @param {*}
     * @return {*}
     * @other:
     */
    void debugPrintf()
    {
        c_stCfg_.printf();
    }

    void resetConfig(bool p_bResetFilter = true, bool p_bRestAttach = false)
    {
        c_stCfg_.m_use.reset();
        c_stCfg_.m_class.reset();
        c_stCfg_.m_featu.reset();
        if (p_bResetFilter)
            c_stCfg_.m_whlFil.reset();
        if (p_bRestAttach)
            c_stCfg_.m_attach.reset();
    }
    /**
     * @description: 设置使用哪些特征
     * @param {std::vector<int>&} p_vUsingSituation 列表
     * @return {*}
     * @other:
     */
    void setFeatureUse(const std::vector<int>& p_vUsingSituation)
    {
        c_stCfg_.m_use.set(p_vUsingSituation);
    }
    const std::vector<int> getFeatureUse()
    {
        return c_stCfg_.m_use.getSituation();
    }
    /**
     * @description: 设置可用特征的xyz范围，当前只使用z范围
     * @note: 范围值相对于当前雷达（0，0，0）位置
     * @param {float} p_xmin
     * @param {float} p_ymin
     * @param {float} p_zmin
     * @param {float} p_xmax
     * @param {float} p_ymax
     * @param {float} p_zmax
     * @return {*}
     */
    void setFeatureXYSpace(const float p_xmin,
                           const float p_ymin,
                           const float p_xmax,
                           const float p_ymax)
    {
        c_stCfg_.m_attach.setFeatureXYSpace(p_xmin, p_ymin, p_xmax, p_ymax);
    }
    /**
     * @description: 设置可用的特征高度范围
     * @param {float} p_zmin 最低
     * @param {float} p_zmax 最高
     * @return {*}
     * @other:
     */
    void setFeatureZSpace(const float p_zmin = -FLT_MAX, const float p_zmax = FLT_MAX)
    {
        c_stCfg_.m_attach.setFeatureZSpace(p_zmin, p_zmax);
    }
    /**
     * @description: 设置雷达高度
     * @param {float} p_fHeight 雷达高度
     * @param {float} p_fAllowSection 认为的低空高度区间
     * @return {*}
     * @other:
     */
    void setLidarHeight(const float p_fHeight, const float p_fAllowSection = 0.3)
    {
        c_stCfg_.m_attach.setLidarHeight(p_fHeight, p_fAllowSection);
    }
    void setBondaryAngle(const float p_fBondaryAngle)
    {
        c_stCfg_.m_class.setBondaryAngle(p_fBondaryAngle);
    }
    /**
     * @description: 设置小物体的尺寸范围
     * @param {float} p_fMinScale 最小尺寸
     * @param {float} p_fMaxScale 最大尺寸
     * @return {*}
     * @other:
     */
    void setSmallThingScaleRange(const float p_fMinScale, const float p_fMaxScale)
    {
        c_stCfg_.m_class.setSmallThingScaleRange(p_fMinScale, p_fMaxScale);
    }
    /**
     * @description: 设置角点的可用距离范围
     * @param {float} p_fMinDist 最近距离
     * @param {float} p_fMaxDist 最远距离
     * @return {*}
     * @other:
     */
    void setCornDistRange(const float p_fMinDist, const float p_fMaxDist)
    {
        c_stCfg_.m_featu.setCornDistRange(p_fMinDist, p_fMaxDist);
    }
    /**
     * @description: 设置角点的角度范围
     * @param {float} l_fMinAngle 最小角度[0~90)
     * @param {float} l_fMaxAngle 最大角度(90~160)
     * @return {*}
     * @other:
     */
    void setCornCurvRange(const float l_fMinAngle, const float l_fMaxAngle)
    {
        c_stCfg_.m_featu.setCornCurvRange(l_fMinAngle, l_fMaxAngle);
    }
    /**
     * @description: 设置面点的角度范围（最大为固定值180deg）
     * @param {float} l_fMinAngle 最小角度[90~170)
     * @return {*}
     * @other:
     */
    void setSurfCurvRange(const float l_fMinAngle)
    {
        c_stCfg_.m_featu.setSurfCurvRange(l_fMinAngle);
    }
    /**
     * @description: 设置强度过滤器
     * @param {bool} p_bTurnOn 开关
     * @param {u_int} p_uiAllowDiff 允许的连续强度差异
     * @param {float} p_fPrecentThr 允许的最大异常比例
     * @return {*}
     * @other:
     */
    void setIntensitySmoothCheck(bool p_bTurnOn, u_int p_uiAllowDiff = 6, float p_fPrecentThr = 0.5)
    {
        c_stCfg_.m_class.m_del.setIntensitySmoothCheck(p_bTurnOn, p_uiAllowDiff, p_fPrecentThr);
    }
    /**
     * @description: 设置线性过滤器
     * @param {bool} p_bTurnOn 开关
     * @param {u_int} p_uiAllowDiff 允许的连续点角度
     * @param {float} p_fPrecentThr 允许的最大异常比例
     * @return {*}
     * @other:
     */
    void setLineSmoothCheck(bool p_bTurnOn, float p_fAllowDiff = 100.0f, float p_fPrecentThr = 0.5)
    {
        c_stCfg_.m_class.m_del.setLineSmoothCheck(p_bTurnOn, p_fAllowDiff, p_fPrecentThr);
    }
    /**
     * @description: 垂向角点设置
     * @param {bool} p_bFilter 开关
     * @param {u_int} p_uiMinPoints 最小圆柱内角点数
     * @param {float} p_fRadius 圆柱半径
     * @param {float} p_fMinHeight 最小圆柱高度
     * @return {*}
     * @other:
     */
    void setVerticalCornFilter(const bool p_bFilter,
                               const u_int p_uiMinPoints = 2,
                               const float p_fRadius = 0.3,
                               const float p_fMinHeight = 0.3)
    {
        c_stCfg_.m_whlFil.setVerticalCornFilter(p_bFilter, p_uiMinPoints, p_fRadius, p_fMinHeight);
    }
    /**
     * @description: 设置过滤器状态
     * @param p_bFilter 面点采样过滤开关
     * @param p_bExtraFilterGnd 是否强效过滤地面点（极大降低地面点数）
     * @param p_uiMaxSurfPntNum 最大面点数量（点数控制）
     * @return {*}
     */
    void setUniSampleSurfFilter(const bool p_bFilter,
                                const u_int p_uiMaxSurfPntNum = 3000,
                                const std::vector<float>* p_fSampleRange = nullptr,
                                const std::vector<float>* p_fSampleScale = nullptr)
    {
        c_stCfg_.m_whlFil.setUniSampleSurfFilter(
            p_bFilter, p_uiMaxSurfPntNum, p_fSampleRange, p_fSampleScale);
    }
    /**
     * @description: 设置sc的可用距离范围
     * @param {float} p_fMaxDist 最远距离
     * @return {*}
     * @other:
     */
    void setSCDistRange(const float p_fMaxDist)
    {
        c_stCfg_.m_sc.setMaxRadius(p_fMaxDist);
    }
    /**
     * @description: 设置sc参数
     * @param {int} p_iRing Y像素
     * @param {int} p_iSector X像素
     * @return {*}
     * @other:
     */
    void setSCRingSector(const int p_iRing, const int p_iSector)
    {
        c_stCfg_.m_sc.setRingSector(p_iRing, p_iSector);
    }
    /**
     * @description: 返回采样点数量
     * @param {*}
     * @return {int} 采样点数量
     * @other:
     */
    int getUniSampleSurfSize()
    {
        return c_iSampleSurfSize;
    }

    /**
     * @description: 设置输入点云，准备特征提取
     * @note： 输入点云会被内部深拷贝，下一步 extractFeature
     * @param p_pcGlobal 输入16线点云
     * @return 0-完成
     */
    int setInputCloud(s_PCloud (&p_pcGlobal)[WLR720_SCANS_PER_FIRING])
    {
        return copyScansToThis_(p_pcGlobal, c_pcScansCopy_);
    }

    /**
     * @description: 提取特征点
     * @note: 必须 setInputCloud 输入点云后使用
     * @param {pcFeaturePtr} p_pcCorn 预备角特征点云指针
     * @param {pcFeaturePtr} p_pcSurf 预备面特征点云指针
     * @return {*}
     */
    int extractFeature(pcFeaturePtr p_pcCorn, pcFeaturePtr p_pcSurf)
    {
        c_pcSegment_->clear();
        c_pcSegmentMiddleScan_->clear();
        c_pcSegmentCurbScan_->clear();
        c_pcDsCloud_->clear();
        c_pcAll_->clear();

        // #pragma omp parallel for nowait num_threads(MAX_NUM_THREAD)
        for (int i = 0; i < WLR720_SCANS_PER_FIRING; i++)
        {
            lineProcess_(c_pcScansCopy_[i], c_pcScanCorn_[i], c_pcScanSurf_[i], i);
        }
        return copyFeatureToOutput_(c_pcScanCorn_, c_pcScanSurf_, p_pcCorn, p_pcSurf);
    }
    void setCurbLineId(std::vector<int> p_v)
    {
        if (p_v.size())
            c_viCurbLineId = p_v;
    }
    int extractScancontext(Eigen::MatrixXd& p_sc)
    {
        p_sc = Eigen::MatrixXd::Zero(c_stCfg_.m_sc.m_iRing, c_stCfg_.m_sc.m_iSector);
        int l_iFromH = WLR720_SCANS_PER_FIRING - 1;
        int l_iFromLow = 0;
        // 从外到内,在大多数安装状态下能减少重复赋值
        // 顺序 15-->0-->14-->1-->13.... 先扫描可能z最大的线序
        // 注意线数为奇数时不能从高序开始,会漏
        for (int i = 0, j = 0; i < WLR720_SCANS_PER_FIRING; i++)
        {
            if (i % 2 == 0)
                j = l_iFromH--;
            else
                j = l_iFromLow++;
            lineProcessSC_(c_pcScansCopy_[j], p_sc, j);
        }
        return state::DONE;
    }
    /**
     * @description: 获取分类点云
     * @note: 必须在 extractFeature 之后使用
     * @param {pcFeaturePtr} p_pcSegment 预备分类点云指针
     * @return {*}
     */
    pcFeaturePtr getSegmentCloud()
    {
        return c_pcSegment_;
    }
    pcFeaturePtr getSegmentMiddleScan()
    {
        return c_pcSegmentMiddleScan_;
    }
    pcFeaturePtr getSegmentCurbScan()
    {
        return c_pcSegmentCurbScan_;
    }
    pcDsPtr getDownSizeCloud()
    {
        return c_pcDsCloud_;
    }
    pcFeaturePtr getAllCloud()
    {
        u_int32_t pointSize = 0;
        u_int32_t num = 0;
        for (int i = 0; i < WLR720_SCANS_PER_FIRING; i++)
        {
            pointSize += c_pcScansCopy_[i].m_pc.m_praw->points.size();
            c_pcAll_->resize(pointSize);
            for (u_int32_t j = 0; j < c_pcScansCopy_[i].m_pc.m_praw->points.size(); j++)
            {
                c_pcAll_->points[num].x = c_pcScansCopy_[i].m_pc.m_praw->points[j].x;
                c_pcAll_->points[num].y = c_pcScansCopy_[i].m_pc.m_praw->points[j].y;
                c_pcAll_->points[num].z = c_pcScansCopy_[i].m_pc.m_praw->points[j].z;
                c_pcAll_->points[num].h = i;
                c_pcAll_->points[num].s = c_pcScansCopy_[i].m_pc.m_padd->at(j).time;
                c_pcAll_->points[num].v = c_pcScansCopy_[i].m_pc.m_padd->at(j).intensity;
                num++;
            }
        }
        return c_pcAll_;
    }

  private:
    // 点到点的距离pcl
    template <typename T1, typename T2> inline double calcDistP2P_point(T1& pcur, T2& ptra)
    {
        return sqrt(pow(pcur.x - ptra.x, 2) + pow(pcur.y - ptra.y, 2) + pow(pcur.z - ptra.z, 2));
    }
    // 点到点的距离pcl
    template <typename T1, typename T2> inline double calcXYDistP2P_point(T1& pcur, T2& ptra)
    {
        return sqrt(pow(pcur.x - ptra.x, 2) + pow(pcur.y - ptra.y, 2));
    }
    /**
     * @description: 求适量夹角的余弦值[0->180]->[1->0->-1]
     * @param {Vector3d} & v1
     * @param {Vector3d} & v2
     * @return {*}
     */
    inline float getAngleTwoVectors(const Eigen::Vector2f& v1, const Eigen::Vector2f& v2)
    {
        return v1.dot(v2) / (v1.norm() * v2.norm());  //[-1,1]
    }

    // 次要函数
    int allocateMemery_();

    /**
     * @brief: 加载参数，允许运行中修改
     * @return 0-完成
     */
    int setParamOutDoor_();
    int setParamInDoor_();
    int setParamVacuous_();
    int setParamLabor_();
    int debugParamPrint_();

    /**
     * @description: 等待信号量，深拷贝点云，然后释放信号量
     * @param p_pcGlobal 待拷贝的点云
     * @param p_pcLocal 实例中的备份点云
     * @return 0-完成
     */
    int copyScansToThis_(s_PCloud (&p_pcGlobal)[WLR720_SCANS_PER_FIRING],
                         st_fCloud (&p_pcLocal)[WLR720_SCANS_PER_FIRING]);

    /**
     * @description: 拷贝内部的特征点储存区到全局
     * @param p_pcCIn 16线特征点云1储存区
     * @param p_pcSIn 16线特征点云2储存区
     * @param p_pcCorn 特征点云1外部储存区
     * @param p_pcSurf 特征点云2外部储存区
     * @return 0-完成
     */
    int copyFeatureToOutput_(pcFeaturePtr (&p_pcCIn)[WLR720_SCANS_PER_FIRING],
                             pcFeaturePtr (&p_pcSIn)[WLR720_SCANS_PER_FIRING],
                             pcFeaturePtr p_pcCorn,
                             pcFeaturePtr p_pcSurf);

    /**
     * @description: 对输出特征进行垂直过滤和标记
     * @param {pcFeaturePtr} p_pcCorn
     * @param {pcFeaturePtr} p_pcSurf
     * @return {*}
     */
    int verticalCornerFilter_(pcFeaturePtr p_pcCorn);
    /**
     * @description: 对输出特征进行均匀采样和标记
     * @param {pcFeaturePtr} p_pcCorn
     * @param {pcFeaturePtr} p_pcSurf
     * @return {*}
     */
    int uniformSamplingFilter_(pcFeaturePtr p_pcSurf);
    /**
     * @description: 单线解析线程，对每条线进行分段-分类-提取特征
     * @param p_pcL 单线点云引用
     * @param p_pcLC 单线特征点云指针1
     * @param p_pcLS 单线特征点云指针2
     * @return 0-完成
     */
    int lineProcess_(st_fCloud& p_pcL, pcFeaturePtr p_pcLC, pcFeaturePtr p_pcLS, int p_iLineId);

    /**
     * @description: 对点云进行分段和初步分类，标记了首尾坐标和类型，标记了分段类型
     * @note: 首尾类型可能错误，还需进一步标注
     * @param p_pcL 单线点云引用
     * @param p_vBlockList 分段容器
     * @return {*}
     */
    int segmenteCloud_(st_fCloud& p_pcL, std::vector<st_Block>& p_vBlockList);
    /**
     * @description: 使用classificateBlock_点云分类
     * @param p_pcL 单线点云指针
     * @param p_vBlockList 点块储存区
     * @param p_iLineId 当前处理线号
     * @return 0-完成
     */
    int classificateCloud_(st_fCloud& p_pcL, std::vector<st_Block>& p_vBlockList, int p_iLineId);

    /**
     * @description: 首先分割点云段中的地面
     * @param p_pcL 单线点云指针
     * @param p_stBlk 点块
     * @param p_vNewBlkList 如果点块被分割出地面，储存分割后的多个点块
     * @return {*}
     */
    bool isBlockGnd_(st_fCloud& p_pcL, st_Block& p_stBlk, std::vector<st_Block>& p_vNewBlkList);

    /**
     * @description: 对非地面点云段进行分类
     * @param p_pcL 单线点云指针
     * @param p_stBlk 点块
     * @return {*}
     */
    int classificateBlock_(st_fCloud& p_pcL, st_Block& p_stBlk);
    /**
     * @description: 判断是否为分界点，如果是，则上一个点是本分段终点，这个点是下分段起点
     * 注意：输出分为 NONE、OTHER、FRONT、BACK，
     * @param p_pcL 输入单线点云
     * @param p_uiInd 当前点序号（如果分界，是下分段起点）
     * @return shltTye::NONE 不是拖尾点 其他都是拖尾点
     */
    inline shltTye isBoundaryPoint_(st_fCloud& p_pcL, u_int32_t& p_uiInd);

    /**
     * @description:判断分段是否平滑，否则认为是杂乱分段
     * @param p_pcL 输入单线点云
     * @param p_stBlk 点云分段
     * @return true-平滑 false-不平滑
     */
    bool isBlockSmooth_(st_fCloud& p_pcL, st_Block& p_stBlk);
    /**
     * @description:判断强度是否平滑，超过最大差异的点的比例超过最大比例则不平滑
     * @param p_pcL 输入单线点云
     * @param p_stBlk 点云分段
     * @param p_fMaxDiff 最大差异
     * @param p_fMaxPercent 超过差异的最大比例
     * @return true-平滑 false-不平滑
     */
    bool ifIntensitySmooth_(st_fCloud& p_pcL, st_Block& p_stBlk);
    /**
     * @description:判断强度是否平滑，超过最大差异的点的比例超过最大比例则不平滑
     * @param p_pcL 输入单线点云
     * @param p_stBlk 点云分段
     * @param p_fMaxDiff 最大差异
     * @param p_fMaxPercent 超过差异的最大比例
     * @return true-平滑 false-不平滑
     */
    bool ifLineSmooth_(st_fCloud& p_pcL, st_Block& p_stBlk);
    /**
     * @description:判断是否小物体和稀疏物体
     * @note: 初次判断物体角度，快速过滤大物体
     * @note: 使用物体段的投影宽度（假设对称），降低瑕疵物体命中概率
     * @param p_pcL 输入单线点云
     * @param p_stBlk 点云分段
     * @return true-小物体
     */
    bool isBlockSmallOrSpare_(st_fCloud& p_pcL, st_Block& p_stBlk);

    /**
     * @description:判断是否凹圆类物体
     * @param p_pcL 输入单线点云
     * @param p_stBlk 点云分段
     * @return true-凹圆类物体
     */
    bool isBlockArc_(st_fCloud& p_pcL, st_Block& p_stBlk);
    /**
     * @description: 三点计算圆心和半径
     * @param abc 三点
     * @param p_pCenter 圆心坐标输出
     * @return 半径
     */
    template <typename T, typename T2> float calcCircle_(T& p_pA, T& p_pB, T& p_pC, T2& p_pCenter);

    /**
     * @description: 滤波器合集，对CLab和SLab进行标记
     * @note: 输入容器包含成功分类的全部分段
     * @param p_pcL 单线点云引用
     * @param p_vBlockList 分段容器
     * @return {*}
     */
    int segmentCloudFilter_(st_fCloud& p_pcL, std::vector<st_Block>& p_vBlockList);

    /**
     * @description: 下采样原始单线点云并对可能为边角进行标记
     * @param p_pcL 原始单线点云结构体
     * @return {*}
     */
    int sigmentCloudDownSize_(st_fCloud& p_pcL, std::vector<st_Block>& p_vBlockList);

    /**
     * @description: 对可能为边角进行标记
     * @param p_pcL 原始单线点云结构体
     * @return {*}
     */
    int downSizeCloudFilter_(st_fCloud& p_pcL,
                             std::vector<st_Block>& p_vBlockList,
                             pcDsPtr p_dsC,
                             std::vector<std::array<u_int32_t, 3>>& p_vSub);

    bool isPointsAShape_(pcDsPtr p_dsC, std::vector<u_int32_t>& p_vDsSub);

    /**
     * @description: 对稀疏分段进行过滤
     * @param {*}
     * @return {*}
     */
    int Spare_filter_(st_fCloud& p_pcL, std::vector<st_Block>& p_vBlockList);

    /**
     * @description: 分析所有前景点并重新标记CLab
     * @note: 输入容器包含成功分类的全部分段
     * @param p_pcL 单线点云引用
     * @param p_vBlockList 分段容器
     * @return {*}
     */
    int FrontSmall_filter_(st_fCloud& p_pcL, std::vector<st_Block>& p_vBlockList);

    /**
     * @description: 分析所有大物体前景边缘点并重新标记CLab
     * @note: 输入容器包含成功分类的全部分段
     * @param p_pcL 单线点云引用
     * @param p_vBlockList 分段容器
     * @return {*}
     */
    int FrontShelter_filter_(st_fCloud& p_pcL, std::vector<st_Block>& p_vBlockList);

    /**
     * @description: 有时仅仅判断两个分段首尾不足以找到真实距离
     * @param {*}
     * @return {*}
     */
    bool isLessWidthBetweenP2Blk_(st_fCloud& p_pcL, u_int32_t p_uiFrontP, st_Block& p_stBack);

    /**
     * @description: 根据两个前景小物体分段间距过滤太过靠近的小物体，防止匹配的异常
     * @param p_pcL 单线点云引用
     * @param {u_int32_t} p_uiFrontP 当前分段未点
     * @param {u_int32_t} p_uiBackP 下个分段起点
     * @return {*}
     */
    bool isLessWidthBetween2P_(st_fCloud& p_pcL,
                               u_int32_t p_uiFrontP,
                               u_int32_t p_uiBackP,
                               float p_width = 0.3);

    /**
     * @description: 根据两个前景次相邻物体边缘点的夹角，防止获取异常边缘点
     * @param p_pcL 单线点云引用
     * @param {u_int32_t} p_uiFrontP 当前分段未点
     * @param {u_int32_t} p_uiBackP 下个分段起点
     * @return {*}
     */
    bool isLessAngBetween2P_(st_fCloud& p_pcL, u_int32_t p_uiFrontP, u_int32_t p_uiBackP);

    /**
     * @description: 根据分界点旁三点的距离差和角度差确定这三点是否在一个转角上或者处于大入射角
     * @param p_pcL 输入单线点云
     * @param {u_int32_t} p_uiThisP   分界点
     * @param {u_int32_t} p_uiNeib1P  分界点旁第一点
     * @param {u_int32_t} p_uiNeib2P  分界点旁第二点
     * @return true 是无效的
     */
    bool isFRNTEdgeNotClear_(st_fCloud& p_pcL,
                             u_int32_t p_uiThisP,
                             u_int32_t p_uiNeib1P,
                             u_int32_t p_uiNeib2P);

    /**
     * @description: 根据分界点旁三点的距离差和角度差确定这三点是否在一个转角上或者处于大入射角
     * @param p_pcL 输入单线点云
     * @param {u_int32_t} p_uiThisP   分界点
     * @param {u_int32_t} p_uiNeib1P  分界点旁第一点
     * @param {u_int32_t} p_uiNeib2P  分界点旁第二点
     * @return true 是无效的
     */
    bool
    isPointsAnLine_(st_fCloud& p_pcL, u_int32_t l_uiLeft, u_int32_t l_uiMid, u_int32_t l_uiRigh);

    /**
     * @description: 判断当前点是否在大入射角平面上，从而不是正确的角点
     * @param {u_int32_t} p_pcL 输入单线点云
     * @param {u_int32_t} p_uiP1 一侧点序号
     * @param {u_int32_t} p_uiP2 中间点序号
     * @param {u_int32_t} p_uiP3 另一侧侧点序号
     * @return true 大入射角平面点
     */
    bool isCornInPlane_(st_fCloud& p_pcL, u_int32_t p_uiP1, u_int32_t p_uiP2, u_int32_t p_uiP3);

    int extractFeature_(st_fCloud& p_pcL,
                        std::vector<st_Block>& p_vBlockList,
                        pcFeaturePtr p_pcLC,
                        pcFeaturePtr p_pcLS);
    /**
     * @description: 提取特征点
     * @param p_pcL 输入单线点云
     * @param p_stBlk 输入单线点云的点块结构
     * @param p_pcLC 输出单线点云的角点
     * @param p_pcLS 输入单线点云的面点
     * @return 0-完成
     */
    int extractFeatureOneBlock_(st_fCloud& p_pcL,
                                st_Block& p_stBlk,
                                pcFeaturePtr p_pcLC,
                                pcFeaturePtr p_pcLS);

    /**
     * @description: 获取小物体内的特征点
     * @param p_pcL 输入单线点云
     * @param p_stBlk 点云分段
     * @param p_pcLC 输出单线点云的角点
     * @param p_pcLS 输入单线点云的面点
     * @return 0 完成
     */
    int getFeature_FSMBLK_(st_fCloud& p_pcL,
                           st_Block& p_stBlk,
                           pcFeaturePtr p_pcLC,
                           pcFeaturePtr p_pcLS);

    /**
     * @description: 获取小物体内的特征点
     * @param p_pcL 输入单线点云
     * @param p_stBlk 点云分段
     * @param p_pcLC 输出单线点云的角点
     * @param p_pcLS 输入单线点云的面点
     * @return 0 完成
     */
    int getFeature_BSMBLK_(st_fCloud& p_pcL,
                           st_Block& p_stBlk,
                           pcFeaturePtr p_pcLC,
                           pcFeaturePtr p_pcLS);

    /**
     * @description: 获取大物体内的特征点（排序）
     * 注意：对于前景点，必须检查有效性（点是否在一个大入射角斜面上）
     * @param p_pcL 输入单线点云
     * @param p_stBlk 点云分段
     * @param p_pcLC 输出单线点云的角点
     * @param p_pcLS 输入单线点云的面点
     * @return 0 完成
     */
    int getFeature_BIGBLK_(st_fCloud& p_pcL,
                           st_Block& p_stBlk,
                           pcFeaturePtr p_pcLC,
                           pcFeaturePtr p_pcLS);

    /**
     * @description: 获取大物体内的特征点（不排序）
     * @param p_pcL 输入单线点云
     * @param p_stBlk 点云分段
     * @param p_pcLC 输出单线点云的角点
     * @param p_pcLS 输入单线点云的面点
     * @return 0 完成
     */
    int getFeature_SPABLK_(st_fCloud& p_pcL,
                           st_Block& p_stBlk,
                           pcFeaturePtr p_pcLC,
                           pcFeaturePtr p_pcLS);

    /**
     * @description: 获取大物体内的特征点（不排序）
     * @param p_pcL 输入单线点云
     * @param p_stBlk 点云分段
     * @param p_pcLC 输出单线点云的角点
     * @param p_pcLS 输入单线点云的面点
     * @return 0 完成
     */
    int getFeature_GNDBLK_(st_fCloud& p_pcL,
                           st_Block& p_stBlk,
                           pcFeaturePtr p_pcLC,
                           pcFeaturePtr p_pcLS);

    /**
     * @description: 获取大物体内的特征点（不排序）
     * @param p_pcL 输入单线点云
     * @param p_stBlk 点云分段
     * @param p_pcLC 输出单线点云的角点
     * @param p_pcLS 输入单线点云的面点
     * @return 0 完成
     */
    int getFeature_UNKBLK_(st_fCloud& p_pcL,
                           st_Block& p_stBlk,
                           pcFeaturePtr p_pcLC,
                           pcFeaturePtr p_pcLS);
    /**
     * @description: 计算这一分段内可计算特征值的点的特征值并加入排序容器
     * 注意：在进入函数后会自行resize排序容器，大小=len-2*EV_HARFNUM
     * 仅适用于长度大于2*EV_HARFNUM的分段
     * @param p_pcL 输入单线点云
     * @param p_stBlk 点云分段
     * @param p_vEvBuf 排序容器
     */
    void setEvalue_(st_fCloud& p_pcL, st_Block& p_stBlk, std::vector<st_EValue>& p_vEvBuf);

    /**
     * @description: 将该点加入对应的特征点云并加入类型
     * @param p_pcL 输入单线点云
     * @param p_uiInd 这个点在输入单线点云中的序号
     * @param p_pcLf 输出到特征点云指针
     * @param p_iFType 输出时赋予这一特征类别
     */
    void addFeature_(st_fCloud& p_pcL,
                     u_int32_t& p_uiInd,
                     pcFeaturePtr p_pcLf,
                     FuTyp p_iFType,
                     float p_fEvalue = 0.0f);

    /**
     * @description: 设置点周围一段范围内的为非特征,至少设置EVHARF点
     * 注意：设置范围是blk.start->end，
     * 设置方式为设置pl.m_padd->at(cnt).intensity = NOCORN 或者 NOSURF
     * 设置后会破坏原有储存的强度特征
     * @param p_pcL 输入单线点云
     * @param p_stBlk 点云分段
     * @param p_uiInd 这个点在输入单线点云中的序号
     * @param p_iFType 赋予这一特征类别（NOSURF、NOCORN）
     * @param p_fShieldRange 遮蔽范围[m]，默认为0.1m
     */
    void setNoFeature_(st_fCloud& p_pcL,
                       st_Block& p_stBlk,
                       u_int32_t& p_uiInd,
                       int8_t* p_vLabs,
                       labTyp p_iFType,
                       u_int p_uiShieldNum = EV_HARFNUM,
                       float p_fShieldRange = 0.11f);

    bool isSurfPosValid_(PRaw& p_point)
    {
        if (p_point.z > c_stCfg_.m_attach.m_fFeatureRange[0].z()
            && p_point.z < c_stCfg_.m_attach.m_fFeatureRange[1].z())
        {
            return true;
        }
        else
            return false;
    }
    bool isCornPosValid_(PRaw& p_point)
    {
        if (p_point.z > c_stCfg_.m_attach.m_fFeatureRange[0].z()
            && p_point.z < c_stCfg_.m_attach.m_fFeatureRange[1].z())
        {
            return true;
        }
        else
            return false;
    }
    bool isCornDepthValid_(float& p_fXydist)
    {
        if (p_fXydist < c_stCfg_.m_featu.m_fCornDistRange[1]
            && p_fXydist > c_stCfg_.m_featu.m_fCornDistRange[0])
            return true;
        else
            return false;
    }
    bool isCornEvalueValid_(float& p_fEvalue)
    {
        if (p_fEvalue < c_stCfg_.m_featu.m_fCornCurvRange[1]
            && p_fEvalue > c_stCfg_.m_featu.m_fCornCurvRange[0])
            return true;
        else
            return false;
    }

    // 用于debug的分段记录
    void debugSegmentCopy_(st_fCloud& p_pcL, int p_iLineId, st_Block& p_stBlk);
    /**
     * @brief 单线解析线程，对每条线进行描述符求解
     *
     * @param[in] p_pcL 单线点云引用
     * @param[out] p_sc 描述符储存容器
     * @param[in] p_iLineId 线号
     * @return [int] [完成:0, 失败:其他]
     */
    int lineProcessSC_(st_fCloud& p_pcL, Eigen::MatrixXd& p_sc, int p_iLineId);
};  // class FeatureExtract

}  // namespace wj_FE

#endif  // __wslam_pretreat_FEATUREEXTRACT_H
