#ifndef MARK_REC_H
#define MARK_REC_H

#include <malloc.h>
#include <math.h>
#include <stdint.h>

#include <boost/shared_ptr.hpp>
#include <string.h>
#include <vector>

#include "algorithm/location/mark_location/mark/marktype.h"
#include "common_WLR720.h"

class Mark_Rec {
  public:
    typedef boost::shared_ptr<Mark_Rec> Ptr;

    Mark_Rec();
    ~Mark_Rec();

    bool Points_Diff_Cmp(uint32_t p_Diff_In, uint32_t p_Diff_Cmp_In);

    uint16_t Return_Max(uint16_t p_In1, uint16_t p_In2);

    /**
     * @brief 根据强度筛选靶标起止点
     *
     * @param p_plwidth 强度
     * @param p_dist 距离
     * @param p_CarveBuf 输出的靶标起止点
     * @param p_Ref 发射率无用
     */
    void GetOutlineOfMarks_WD(uint16_t* p_plwidth,
                              uint16_t* p_dist,
                              STRUCT_CARVE* p_CarveBuf,
                              uint16_t* p_Ref);
    /**
     * @brief 找到靶标根据强度
     *
     * @param p_Ratio 强度-反射率
     * @param p_dist 距离
     * @param p_CorrTime 速度修正的修正时间
     * @param l_PLCarve_Copy 无用
     */
    void Find_ScanMark_PluseDist(uint16_t* p_Ratio,
                                 uint16_t* p_dist,
                                 float p_CorrTime,
                                 STRUCT_CARVE* l_PLCarve_Copy,
                                 std::vector<STRUCT_MARK_INFO>& p_vecOutXY);

    /**
     * @brief 初始化参数数值
     *
     */
    void Empty_UsingBuf();

    /**
     * @brief 找到每个切割到的靶标上反射率最大点
     *
     * @param Width_Carve
     * @param p_Filter 输出处理的靶标
     * @param p_CraveBuf 输入待处理靶标
     * @param p_dist
     * @return uint8_t
     */
    uint8_t Find_MaxWidthPoint_WD(uint16_t* Width_Carve,
                                  STRUCT_FILTER_TARGET* p_Filter,
                                  STRUCT_CARVE* p_CraveBuf,
                                  uint16_t* p_dist);

    /**
     * @brief 在起始与结束点间找到脉宽最大点
     *
     * @param p_u8MaxMarkNum 允许存放最多的靶标个数
     * @param p_MarkNum 找到的靶标个数
     * @param p_u16start 存放靶标起始点首地址
     * @param p_u16end 存放靶标结束点首地址
     * @param p_Width_Carve 存放脉宽首地址
     * @param p_Filter
     * @param p_dist
     */
    void Get_MaxWidth_WD(uint8_t p_u8MaxMarkNum,
                         uint8_t p_MarkNum,
                         uint16_t* p_u16start,
                         uint16_t* p_u16end,
                         uint16_t* p_Width_Carve,
                         STRUCT_FILTER_TARGET* p_Filter,
                         uint16_t* p_dist);

    /**
     * @brief 找到每个靶标的ref极值及找起始点终止点百分比
     *
     * @param data 反射率
     * @param p_dist 距离
     * @param Sta 靶标起始点
     * @param End 靶标终止点
     * @param p_MarkInfo 靶标信息存放地址
     * @param mul 百分比
     */
    void FindPeakData(uint16_t* data,
                      uint16_t* p_dist,
                      uint16_t Sta,
                      uint16_t End,
                      STRUCT_MARK_INFO* p_MarkInfo,
                      uint8_t* mul);

    /**
     * @brief 找到极值距离
     *
     * @param sta
     * @param end
     * @param Dist
     * @param max_dist
     * @param min_dist
     * @param Min_offset
     */
    void Find_MaxMin_Dist_WD(uint16_t sta,
                             uint16_t end,
                             uint16_t* Dist,
                             uint16_t* max_dist,
                             uint16_t* min_dist,
                             uint16_t* Min_offset);

    /**
     * @brief 在起始地与终止点范围内找到最大脉宽极值
     *
     * @param p_u16start 靶标起始点
     * @param p_u16end 靶标终止点
     * @param p_Data 输入的脉宽序列
     * @return uint16_t 脉宽极值
     */
    uint16_t Find_Max_WD(uint16_t p_u16start, uint16_t p_u16end, uint16_t* p_Data);

    /**
     * @brief 根据反射率和百分比寻找靶标的起始点和结束点
     *
     * @param p_PulseDistRatio 反射率
     * @param p_MarkNum 疑似靶标的总个数
     * @param p_Filter 靶标存放地址
     * @param dist 扫描数据-距离首地址
     * @param mul 百分比
     * @return uint32_t
     */
    uint32_t Find_StaEnd_Point_RatioPulseDist(uint16_t* p_PulseDistRatio,
                                              uint8_t p_MarkNum,
                                              STRUCT_FILTER_TARGET* p_Filter,
                                              uint16_t* dist,
                                              uint8_t* mul);

    /**
     * @brief 寻找mul%反射率点(起始点)
     *
     * @param Width_Aver 反射率数据
     * @param Max_Width_Point 靶标的脉宽最大点
     * @param p_Width_80Percent mul%点ref值
     * @return uint16_t 80%脉宽点(起始点)
     */
    uint16_t Find_80Percent_Sta_Point_PulseDist(uint16_t* Width_Aver,
                                                uint16_t Max_Width_Point,
                                                uint16_t p_Width_80Percent);

    /**
     * @brief 寻找mul%反射率点(结束点)
     *
     * @param Width_Aver 反射率数据
     * @param Max_Width_Point 靶标的脉宽最大点
     * @param p_Width_80Percent mul%点ref值
     * @return uint16_t 80%脉宽点(结束点)
     */
    uint16_t Find_80Percent_End_Point_PulseDist(uint16_t* Width_Aver,
                                                uint16_t Max_Width_Point,
                                                uint16_t p_Width_80Percent);

    /**
     * @brief 根据靶标上的点数滤除假靶
     *
     * @param p_MarkNum
     * @param p_Filter
     */
    void Filter_FakeMark_ByPointNum_WD(uint8_t p_MarkNum,
                                       STRUCT_FILTER_TARGET* p_Filter);  //根据靶标上的点数滤除假靶

    /**
     * @brief 根据当前的距离,来获取最低的打在靶标的点数
     *
     * @param p_dist 疑似靶标的距离
     * @return uint16_t
     */
    uint16_t Cal_MarkDot_ByDist_Theory(uint32_t p_dist);

    /**
     * @brief 有角速度时，过滤一个靶标被扫描两次的靶标
     *
     * @param p_Filter
     */
    void Filter_SameMark_BySpeedRad(STRUCT_FILTER_TARGET* p_Filter);

    /**
     * @brief 剔除无效靶标
     *
     * @param pDistOld 存放原始靶标首地址
     * @param pDistNew 存放剔除后靶标首地址
     * @return uint32_t
     */
    uint32_t CopyFilterMark_WD(STRUCT_FILTER_TARGET* pDistOld, STRUCT_FILTER_TARGET* pDistNew);

    /**
     * @brief 找到靶标上的最大最小距离
     *
     * @param l_u8cnt 靶标个数
     * @param Dist 存放原始靶标距离首地址
     * @param p_Filter
     * @param max_dist
     * @param min_dist
     */
    void Find_MaxMin_Dist(uint8_t l_u8cnt,
                          uint16_t* Dist,
                          STRUCT_FILTER_TARGET* p_Filter,
                          uint16_t* max_dist,
                          uint16_t* min_dist);

    /**
     * @brief 剔除靠的太近的靶
     *
     * @param p_Filter
     * @param p_Dist
     * @param p_PulseDistRatio
     */
    void
    DeletCloseMark_WD(STRUCT_FILTER_TARGET* p_Filter, uint16_t* p_Dist, uint16_t* p_PulseDistRatio);

    /**
     * @brief 返回两个靶标间的最小夹角
     *
     * @param p_Ang_In1 靶标1的角度
     * @param p_Ang_In2 靶标2的角度
     * @return uint16_t
     */
    uint16_t Find_IncludedAng_Min(uint16_t p_Ang_In1, uint16_t p_Ang_In2);

    /**
     * @brief 根据距离返回两靶间允许的最小夹角
     *
     * @param p_Mark1
     * @param p_Mark2
     * @return uint16_t
     */
    uint16_t Return_FilterMinIncludedAng_WD(STRUCT_MARK_INFO* p_Mark1, STRUCT_MARK_INFO* p_Mark2);

    /**
     * @brief 根据靶标上最小距离与中心点距离比较去过滤靶标，主要过滤被遮挡情况
     *
     * @param p_Filter 靶标信息
     * @param p_Dist 距离
     * @param p_PulseDistRatio 反射率
     * @param l_min_dist
     * @return uint8_t
     */
    uint8_t FilterMark_ByMarkMaxMinDist_WD(STRUCT_FILTER_TARGET* p_Filter,
                                           uint16_t* p_Dist,
                                           uint16_t* p_PulseDistRatio,
                                           uint16_t* l_min_dist);

    void Renew_MarkCenter_PulseDist(uint16_t p_Max_Width_Point,
                                    uint16_t* p_PulseDistRatio,
                                    STRUCT_MARK_INFO* p_MarkInfo,
                                    uint16_t* p_dist);

    void Find_MaxMin_Dist_Sigle(uint16_t* Dist,
                                STRUCT_MARK_INFO* p_MarkInfo,
                                uint16_t* p_DistMax,
                                uint16_t* p_DistMin);

    void Filter_Mark_ByScanRadius(STRUCT_FILTER_TARGET* p_Filter);

    void FixMarkAng_PINGXINGZHOU(STRUCT_MARK_INFO* p_MarkInfo);

    void CodeZeroCorrect(STRUCT_FILTER_TARGET* pFilter);

    uint32_t CopyFilterMark(STRUCT_FILTER_TARGET* pDistOld, STRUCT_FILTER_TARGET* pDistNew);

    void CalAngOffsetBySpeed360TOHalf(STRUCT_FILTER_TARGET* p_Filter,
                                      ROBOT_XY* robot,
                                      INPUTSPEED* p_Speed,
                                      float p_CorrTime);

    void
    Corr_Speed(INPUTSPEED* p_SpeedIn, float p_CorrTime, SPEED* p_SpeedOut, float p_LastMoveTime);

    void Speed_Transfer_To_Coordinates(SPEED* p_Speed_Abs, ROBOT_XY* robot, SPEED* p_Speed_Rel);

    void Mark_Corr_BySpeed(INPUTSPEED* p_Speed,
                           uint8_t p_MarkNum,
                           STRUCT_FILTER_TARGET* p_Filter,
                           float p_CorrTime);

    uint64_t Calculate_Point2Point_Dist_Int(int32_t p_Point_1, int32_t p_Point_2);

    template <typename PointTypePcPtr>
    bool extractMark(s_PCloud p_pmarkScan,
                     float p_lidarRotateSpeedRad,
                     PointTypePcPtr p_ptrMarkCenters,
                     std::string p_sPath,
                     float p_iMarkDisCom);

  private:
    int32_t
    Filter_Mark_By_Ref_WD(uint16_t* PulseWidth, uint16_t* Dist, uint16_t* p_RefBuf, uint8_t chn);

    GLOBALPARAMNAV g_navparam;
    SYS_PIB_TypeDef g_sSysPib;

    STRUCT_FILTER_TARGET_LITE g_sFilterMark_SpeedCorr;
    XY2ROBOT_CNT g_sMappingXY_old;
    STRUCT_FILTER_TARGET_LITE g_sFilterDistShortNewLast;
    INPUTSPEED g_sInSpeedUsed;
    STRUCT_MarkMatch g_sMarkMatch_Set;

    uint8_t g_u8Mul[TARGET_MAX];
    uint16_t g_au16MarkMaxPluseDistRatio[TARGET_MAX];
    uint32_t g_u16MinIncludedAng;
    int8_t g_EccentList[7680];  //实际12500(6250*2)个数
    ROBOT_XY g_sSavePosCur;
    STRUCT_SpeedIcp g_sIcpSpeed;

  public:
    STRUCT_FILTER_TARGET g_sFilterDist;
};
#include "impl/markRec.hpp"
#endif