/*
 * @Description:
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2021-10-27 15:41:19
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-04-29 14:35:40
 */

#pragma once
#include <Eigen/Core>
#include <cfloat>
#include <cmath>
#include <fstream>
#include <iomanip>
#include <iostream>
#include <list>
#include <math.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <queue>
#include <sstream>
#include <vector>

namespace csf {

typedef pcl::PointXYZ PointCSF;
typedef pcl::PointCloud<PointCSF> PointCloudT;
typedef std::vector<PointCSF, Eigen::aligned_allocator<PointCSF>> PointsVector;

/* Some physics constants */
constexpr static float DAMPING = 0.01;
constexpr static float MAX_INF = FLT_MAX;
constexpr static float MIN_INF = FLT_MIN;

/*
Initially, we have to make modifications of particle positions for each
constraint(constraintTimes = m_iRigidness), However, to save computation time,
we precomputed the total displacement of a particle for all constraintTimes.
For singleMove1, which means one of the two particles is unmovable, then we
move the other one only: if constraintTimes = 0: singleMove1 = 0 if
constraintTimes = 1: singleMove1 = 0.3, i.e., each time we move 0.3 (scale
factor of the total distance) for a particle towards the other one if
constraintTimes = 2: singleMove1 = (1-0.3)*0.3+0.3 = 0.51 if constraintTimes
= 3: singleMove1 = (1-0.51)*0.3+0.51 = 0.657
...

For doubleMove1, we move both of the two particles towards each other.
if constraintTimes = 0: singleMove2 = 0
if constraintTimes = 1: singleMove2 = 0.3, i.e., each time we move 0.3
(scale factor of the total distance) for the two particles towards each
other if constraintTimes = 2: singleMove2 = (1-0.3*2)*0.3+0.3 = 0.42 if
constraintTimes = 3: singleMove2 = (1-0.42*2)*0.3+0.42 = 0.468
...

*/
const float singleMove1[15] = {0,
                               0.3,
                               0.51,
                               0.657,
                               0.7599,
                               0.83193,
                               0.88235,
                               0.91765,
                               0.94235,
                               0.95965,
                               0.97175,
                               0.98023,
                               0.98616,
                               0.99031,
                               0.99322};
const float doubleMove1[15] = {0,
                               0.3,
                               0.42,
                               0.468,
                               0.4872,
                               0.4949,
                               0.498,
                               0.4992,
                               0.4997,
                               0.4999,
                               0.4999,
                               0.5,
                               0.5,
                               0.5,
                               0.5};

/* The particle class represents a particle of mass that can move
 * around in 3D space*/
class Particle {
  private:
    bool movable;                  // can the particle move or not ? used to pin parts of the cloth
    float mass;                    // the mass of the particle (is always 1 in this example)
    Eigen::Vector3f acceleration;  // a vector representing the current
                                   // acceleration of the particle
    Eigen::Vector3f accumulated_normal;  // an accumulated normal (i.e. non normalized), used
                                         // for OpenGL soft shading
    float time_step2;

  public:
    // These two memebers are used in the process of edge smoothing after
    // the cloth simulation step.
    Eigen::Vector3f pos;  // the current position of the particle in 3D space
    // the position of the particle in the previous time step, used as
    // part of the verlet numerical integration scheme
    Eigen::Vector3f old_pos;
    bool isVisited;
    int neibor_count;
    int pos_x;  // position in the cloth grid
    int pos_y;
    int c_pos;

    std::vector<Particle*> neighborsList;

    std::vector<int> correspondingLidarPointList;
    std::size_t nearestPointIndex;
    float nearestPointHeight;
    float tmpDist;
    void satisfyConstraintSelf(int constraintTimes)
    {
        Particle* p1 = this;

        for (std::size_t i = 0; i < neighborsList.size(); i++)
        {
            Particle* p2 = neighborsList[i];
            Eigen::Vector3f correctionVector(0, p2->pos[1] - p1->pos[1], 0);

            if (p1->isMovable() && p2->isMovable())
            {
                // Lets make it half that length, so that we can move BOTH p1 and p2.
                Eigen::Vector3f correctionVectorHalf =
                    correctionVector * (constraintTimes > 14 ? 0.5 : doubleMove1[constraintTimes]);
                p1->offsetPos(correctionVectorHalf);
                p2->offsetPos(-correctionVectorHalf);
            }
            else if (p1->isMovable() && !p2->isMovable())
            {
                Eigen::Vector3f correctionVectorHalf =
                    correctionVector * (constraintTimes > 14 ? 1 : singleMove1[constraintTimes]);
                p1->offsetPos(correctionVectorHalf);
            }
            else if (!p1->isMovable() && p2->isMovable())
            {
                Eigen::Vector3f correctionVectorHalf =
                    correctionVector * (constraintTimes > 14 ? 1 : singleMove1[constraintTimes]);
                p2->offsetPos(-correctionVectorHalf);
            }
        }
    }

  public:
    Particle(Eigen::Vector3f pos, float m_fTimeStep)
        : movable(true), mass(1), acceleration(Eigen::Vector3f(0, 0, 0)),
          accumulated_normal(Eigen::Vector3f(0, 0, 0)), time_step2(m_fTimeStep), pos(pos),
          old_pos(pos)
    {
        isVisited = false;
        neibor_count = 0;
        pos_x = 0;
        pos_y = 0;
        c_pos = 0;
        nearestPointIndex = 0;
        nearestPointHeight = MIN_INF;
        tmpDist = MAX_INF;
    }

    Particle()
        : movable(true), mass(1), acceleration(Eigen::Vector3f(0, 0, 0)),
          accumulated_normal(Eigen::Vector3f(0, 0, 0)), pos(Eigen::Vector3f(0, 0, 0)),
          old_pos(Eigen::Vector3f(0, 0, 0))
    {
        isVisited = false;
        neibor_count = 0;
        pos_x = 0;
        pos_y = 0;
        c_pos = 0;
        nearestPointIndex = 0;
        nearestPointHeight = MIN_INF;
        tmpDist = MAX_INF;
    }

    bool isMovable()
    {
        return movable;
    }

    void addForce(Eigen::Vector3f f)
    {
        acceleration += f / mass;
    }

    /* This is one of the important methods, where the time is
     * progressed a single step size (TIME_STEPSIZE) The method is
     * called by Cloth.m_fTimeStep()*/
    void timeStep()
    {
        if (movable)
        {
            Eigen::Vector3f temp = pos;
            pos = pos + (pos - old_pos) * (1.0 - DAMPING) + acceleration * time_step2;
            old_pos = temp;
        }
    }

    Eigen::Vector3f& getPos()
    {
        return pos;
    }

    Eigen::Vector3f getPosCopy()
    {
        return pos;
    }

    void resetAcceleration()
    {
        acceleration = Eigen::Vector3f(0, 0, 0);
    }

    void offsetPos(const Eigen::Vector3f v)
    {
        if (movable)
            pos += v;
    }

    void makeUnmovable()
    {
        movable = false;
    }

    void addToNormal(Eigen::Vector3f normal)
    {
        accumulated_normal += normal.normalized();
    }

    Eigen::Vector3f& getNormal()
    {
        // note: The normal is not unit length
        return accumulated_normal;
    }

    void resetNormal()
    {
        accumulated_normal = Eigen::Vector3f(0, 0, 0);
    }
};

class Cloth {
  private:
    struct XY
    {
        XY(int x1, int y1)
        {
            x = x1;
            y = y1;
        }

        int x;
        int y;
    };
    // post processing is only for connected component which is large than 50
    // #define MAX_PARTICLE_FOR_POSTPROCESSIN 50
    static const int MAX_PARTICLE_FOR_POSTPROCESSIN = 50;
    // total number of particles is num_particles_width * num_particles_height
    int constraint_iterations;

    int m_iRigidness;
    float m_fTimeStep;

    std::vector<Particle> particles;  // all particles that are part of this cloth

    float smoothThreshold;
    float heightThreshold;

  public:
    Eigen::Vector3f origin_pos;
    float step_x, step_y;
    std::vector<float> heightvals;  // height values
    int num_particles_width;        // number of particles in width direction
    int num_particles_height;       // number of particles in height direction

    Particle* getParticle(int x, int y)
    {
        return &particles[y * num_particles_width + x];
    }

    void makeConstraint(Particle* p1, Particle* p2)
    {
        p1->neighborsList.push_back(p2);
        p2->neighborsList.push_back(p1);
    }

  public:
    /* This is a important constructor for the entire system of
     * particles and constraints */
    Cloth(const Eigen::Vector3f& _origin_pos,
          int _num_particles_width,
          int _num_particles_height,
          float _step_x,
          float _step_y,
          float _smoothThreshold,
          float _heightThreshold,
          int m_iRigidness,
          float m_fTimeStep)
        : constraint_iterations(m_iRigidness), m_fTimeStep(m_fTimeStep),
          smoothThreshold(_smoothThreshold), heightThreshold(_heightThreshold),
          origin_pos(_origin_pos), step_x(_step_x), step_y(_step_y),
          num_particles_width(_num_particles_width), num_particles_height(_num_particles_height)
    {
        // I am essentially using this vector as an array with room for
        // num_particles_width*num_particles_height particles
        particles.resize(num_particles_width * num_particles_height);

        float time_step2 = m_fTimeStep * m_fTimeStep;

        // creating particles in a grid of particles from (0,0,0) to
        // (width,-height,0) creating particles in a grid
        for (int i = 0; i < num_particles_width; i++)
        {
            for (int j = 0; j < num_particles_height; j++)
            {
                Eigen::Vector3f pos(
                    origin_pos[0] + i * step_x, origin_pos[1], origin_pos[2] + j * step_y);

                // insert particle in column i at j'th row
                particles[j * num_particles_width + i] = Particle(pos, time_step2);
                particles[j * num_particles_width + i].pos_x = i;
                particles[j * num_particles_width + i].pos_y = j;
            }
        }

        // Connecting immediate neighbor particles with constraints
        // (distance 1 and sqrt(2) in the grid)
        for (int x = 0; x < num_particles_width; x++)
        {
            for (int y = 0; y < num_particles_height; y++)
            {
                if (x < num_particles_width - 1)
                    makeConstraint(getParticle(x, y), getParticle(x + 1, y));

                if (y < num_particles_height - 1)
                    makeConstraint(getParticle(x, y), getParticle(x, y + 1));

                if ((x < num_particles_width - 1) && (y < num_particles_height - 1))
                    makeConstraint(getParticle(x, y), getParticle(x + 1, y + 1));

                if ((x < num_particles_width - 1) && (y < num_particles_height - 1))
                    makeConstraint(getParticle(x + 1, y), getParticle(x, y + 1));
            }
        }

        // Connecting secondary neighbors with constraints (distance 2 and sqrt(4)
        // in the grid)
        for (int x = 0; x < num_particles_width; x++)
        {
            for (int y = 0; y < num_particles_height; y++)
            {
                if (x < num_particles_width - 2)
                    makeConstraint(getParticle(x, y), getParticle(x + 2, y));

                if (y < num_particles_height - 2)
                    makeConstraint(getParticle(x, y), getParticle(x, y + 2));

                if ((x < num_particles_width - 2) && (y < num_particles_height - 2))
                    makeConstraint(getParticle(x, y), getParticle(x + 2, y + 2));

                if ((x < num_particles_width - 2) && (y < num_particles_height - 2))
                    makeConstraint(getParticle(x + 2, y), getParticle(x, y + 2));
            }
        }
    }
    int getSize()
    {
        return num_particles_width * num_particles_height;
    }

    std::size_t get1DIndex(int x, int y)
    {
        return y * num_particles_width + x;
    }

    inline std::vector<float>& getHeightvals()
    {
        return heightvals;
    }

    Particle* getParticle1d(int index)
    {
        return &particles[index];
    }

    /* this is an important methods where the time is progressed one
     * time step for the entire cloth.  This includes calling
     * satisfyConstraint() for every constraint, and calling
     * timeStep() for all particles
     */
    float timeStep()
    {
        int particleCount = static_cast<int>(particles.size());
        // //#pragma omp parallel for
        for (int i = 0; i < particleCount; i++)
        {
            particles[i].timeStep();
        }

        //#pragma omp parallel for
        for (int j = 0; j < particleCount; j++)
        {
            particles[j].satisfyConstraintSelf(constraint_iterations);
        }

        float maxDiff = 0;

        for (int i = 0; i < particleCount; i++)
        {
            if (particles[i].isMovable())
            {
                float diff = fabs(particles[i].old_pos[1] - particles[i].pos[1]);

                if (diff > maxDiff)
                    maxDiff = diff;
            }
        }

        return maxDiff;
    }

    /* used to add gravity (or any other arbitrary vector) to all
     * particles */
    void addForce(const Eigen::Vector3f direction)
    {
        for (std::size_t i = 0; i < particles.size(); i++)
        {
            particles[i].addForce(direction);
        }
    }

    void terrCollision()
    {
        int particleCount = static_cast<int>(particles.size());
        //#pragma omp parallel for
        for (int i = 0; i < particleCount; i++)
        {
            Eigen::Vector3f v = particles[i].getPos();

            if (v[1] < heightvals[i])
            {
                particles[i].offsetPos(Eigen::Vector3f(0, heightvals[i] - v[1], 0));
                particles[i].makeUnmovable();
            }
        }
    }

    void movableFilter()
    {
        std::vector<Particle> tmpParticles;

        for (int x = 0; x < num_particles_width; x++)
        {
            for (int y = 0; y < num_particles_height; y++)
            {
                Particle* ptc = getParticle(x, y);

                if (ptc->isMovable() && !ptc->isVisited)
                {
                    std::queue<int> que;
                    std::vector<XY> connected;  // store the connected component
                    std::vector<std::vector<int>> neibors;
                    int sum = 1;
                    int index = y * num_particles_width + x;

                    // visit the init node
                    connected.push_back(XY(x, y));
                    particles[index].isVisited = true;

                    // enqueue the init node
                    que.push(index);

                    while (!que.empty())
                    {
                        Particle* ptc_f = &particles[que.front()];
                        que.pop();
                        int cur_x = ptc_f->pos_x;
                        int cur_y = ptc_f->pos_y;
                        std::vector<int> neibor;

                        if (cur_x > 0)
                        {
                            Particle* ptc_left = getParticle(cur_x - 1, cur_y);

                            if (ptc_left->isMovable())
                            {
                                if (!ptc_left->isVisited)
                                {
                                    sum++;
                                    ptc_left->isVisited = true;
                                    connected.push_back(XY(cur_x - 1, cur_y));
                                    que.push(num_particles_width * cur_y + cur_x - 1);
                                    neibor.push_back(sum - 1);
                                    ptc_left->c_pos = sum - 1;
                                }
                                else
                                {
                                    neibor.push_back(ptc_left->c_pos);
                                }
                            }
                        }

                        if (cur_x < num_particles_width - 1)
                        {
                            Particle* ptc_right = getParticle(cur_x + 1, cur_y);

                            if (ptc_right->isMovable())
                            {
                                if (!ptc_right->isVisited)
                                {
                                    sum++;
                                    ptc_right->isVisited = true;
                                    connected.push_back(XY(cur_x + 1, cur_y));
                                    que.push(num_particles_width * cur_y + cur_x + 1);
                                    neibor.push_back(sum - 1);
                                    ptc_right->c_pos = sum - 1;
                                }
                                else
                                {
                                    neibor.push_back(ptc_right->c_pos);
                                }
                            }
                        }

                        if (cur_y > 0)
                        {
                            Particle* ptc_bottom = getParticle(cur_x, cur_y - 1);

                            if (ptc_bottom->isMovable())
                            {
                                if (!ptc_bottom->isVisited)
                                {
                                    sum++;
                                    ptc_bottom->isVisited = true;
                                    connected.push_back(XY(cur_x, cur_y - 1));
                                    que.push(num_particles_width * (cur_y - 1) + cur_x);
                                    neibor.push_back(sum - 1);
                                    ptc_bottom->c_pos = sum - 1;
                                }
                                else
                                {
                                    neibor.push_back(ptc_bottom->c_pos);
                                }
                            }
                        }

                        if (cur_y < num_particles_height - 1)
                        {
                            Particle* ptc_top = getParticle(cur_x, cur_y + 1);

                            if (ptc_top->isMovable())
                            {
                                if (!ptc_top->isVisited)
                                {
                                    sum++;
                                    ptc_top->isVisited = true;
                                    connected.push_back(XY(cur_x, cur_y + 1));
                                    que.push(num_particles_width * (cur_y + 1) + cur_x);
                                    neibor.push_back(sum - 1);
                                    ptc_top->c_pos = sum - 1;
                                }
                                else
                                {
                                    neibor.push_back(ptc_top->c_pos);
                                }
                            }
                        }
                        neibors.push_back(neibor);
                    }

                    if (sum > MAX_PARTICLE_FOR_POSTPROCESSIN)
                    {
                        std::vector<int> edgePoints = findUnmovablePoint(connected);
                        handle_slop_connected(edgePoints, connected, neibors);
                    }
                }
            }
        }
    }

    std::vector<int> findUnmovablePoint(std::vector<XY> connected)
    {
        std::vector<int> edgePoints;

        for (std::size_t i = 0; i < connected.size(); i++)
        {
            int x = connected[i].x;
            int y = connected[i].y;
            int index = y * num_particles_width + x;
            Particle* ptc = getParticle(x, y);

            if (x > 0)
            {
                Particle* ptc_x = getParticle(x - 1, y);

                if (!ptc_x->isMovable())
                {
                    int index_ref = y * num_particles_width + x - 1;

                    if ((fabs(heightvals[index] - heightvals[index_ref]) < smoothThreshold)
                        && (ptc->getPos()[1] - heightvals[index] < heightThreshold))
                    {
                        Eigen::Vector3f offsetVec =
                            Eigen::Vector3f(0, heightvals[index] - ptc->getPos()[1], 0);
                        particles[index].offsetPos(offsetVec);
                        ptc->makeUnmovable();
                        edgePoints.push_back(i);
                        continue;
                    }
                }
            }

            if (x < num_particles_width - 1)
            {
                Particle* ptc_x = getParticle(x + 1, y);

                if (!ptc_x->isMovable())
                {
                    int index_ref = y * num_particles_width + x + 1;

                    if ((fabs(heightvals[index] - heightvals[index_ref]) < smoothThreshold)
                        && (ptc->getPos()[1] - heightvals[index] < heightThreshold))
                    {
                        Eigen::Vector3f offsetVec =
                            Eigen::Vector3f(0, heightvals[index] - ptc->getPos()[1], 0);
                        particles[index].offsetPos(offsetVec);
                        ptc->makeUnmovable();
                        edgePoints.push_back(i);
                        continue;
                    }
                }
            }

            if (y > 0)
            {
                Particle* ptc_y = getParticle(x, y - 1);

                if (!ptc_y->isMovable())
                {
                    int index_ref = (y - 1) * num_particles_width + x;

                    if ((fabs(heightvals[index] - heightvals[index_ref]) < smoothThreshold)
                        && (ptc->getPos()[1] - heightvals[index] < heightThreshold))
                    {
                        Eigen::Vector3f offsetVec =
                            Eigen::Vector3f(0, heightvals[index] - ptc->getPos()[1], 0);
                        particles[index].offsetPos(offsetVec);
                        ptc->makeUnmovable();
                        edgePoints.push_back(i);
                        continue;
                    }
                }
            }

            if (y < num_particles_height - 1)
            {
                Particle* ptc_y = getParticle(x, y + 1);

                if (!ptc_y->isMovable())
                {
                    int index_ref = (y + 1) * num_particles_width + x;

                    if ((fabs(heightvals[index] - heightvals[index_ref]) < smoothThreshold)
                        && (ptc->getPos()[1] - heightvals[index] < heightThreshold))
                    {
                        Eigen::Vector3f offsetVec =
                            Eigen::Vector3f(0, heightvals[index] - ptc->getPos()[1], 0);
                        particles[index].offsetPos(offsetVec);
                        ptc->makeUnmovable();
                        edgePoints.push_back(i);
                        continue;
                    }
                }
            }
        }

        return edgePoints;
    }

    void handle_slop_connected(std::vector<int> edgePoints,
                               std::vector<XY> connected,
                               std::vector<std::vector<int>> neibors)
    {
        std::vector<bool> visited;

        for (std::size_t i = 0; i < connected.size(); i++)
            visited.push_back(false);

        std::queue<int> que;

        for (std::size_t i = 0; i < edgePoints.size(); i++)
        {
            que.push(edgePoints[i]);
            visited[edgePoints[i]] = true;
        }

        while (!que.empty())
        {
            int index = que.front();
            que.pop();

            int index_center = connected[index].y * num_particles_width + connected[index].x;

            for (std::size_t i = 0; i < neibors[index].size(); i++)
            {
                int index_neibor = connected[neibors[index][i]].y * num_particles_width
                                   + connected[neibors[index][i]].x;

                if ((fabs(heightvals[index_center] - heightvals[index_neibor]) < smoothThreshold)
                    && (fabs(particles[index_neibor].getPos()[1] - heightvals[index_neibor])
                        < heightThreshold))
                {
                    Eigen::Vector3f offsetVec = Eigen::Vector3f(
                        0, heightvals[index_neibor] - particles[index_neibor].getPos()[1], 0);
                    particles[index_neibor].offsetPos(offsetVec);
                    particles[index_neibor].makeUnmovable();

                    if (visited[neibors[index][i]] == false)
                    {
                        que.push(neibors[index][i]);
                        visited[neibors[index][i]] = true;
                    }
                }
            }
        }
    }
};

class c2cdist {
  public:
    c2cdist(float threshold) : class_treshold(threshold) {}

  private:
    float class_treshold;  //
  public:
    void calCloud2CloudDist(Cloth& cloth,
                            PointsVector& pc,
                            std::vector<int>& groundIndexes,
                            std::vector<int>& offGroundIndexes)
    {
        groundIndexes.resize(0);
        offGroundIndexes.resize(0);

        for (std::size_t i = 0; i < pc.size(); i++)
        {
            float pc_x = pc[i].x;
            float pc_z = pc[i].z;

            float deltaX = pc_x - cloth.origin_pos[0];
            float deltaZ = pc_z - cloth.origin_pos[2];

            int col0 = int(deltaX / cloth.step_x);
            int row0 = int(deltaZ / cloth.step_y);
            int col1 = col0 + 1;
            int row1 = row0;
            int col2 = col0 + 1;
            int row2 = row0 + 1;
            int col3 = col0;
            int row3 = row0 + 1;

            float subdeltaX = (deltaX - col0 * cloth.step_x) / cloth.step_x;
            float subdeltaZ = (deltaZ - row0 * cloth.step_y) / cloth.step_y;

            float fxy = cloth.getParticle(col0, row0)->pos[1] * (1 - subdeltaX) * (1 - subdeltaZ)
                        + cloth.getParticle(col3, row3)->pos[1] * (1 - subdeltaX) * subdeltaZ
                        + cloth.getParticle(col2, row2)->pos[1] * subdeltaX * subdeltaZ
                        + cloth.getParticle(col1, row1)->pos[1] * subdeltaX * (1 - subdeltaZ);
            float height_var = fxy - pc[i].y;

            if (std::fabs(height_var) < class_treshold)
            {
                groundIndexes.push_back(i);
            }
            else
            {
                offGroundIndexes.push_back(i);
            }
        }
    };
};

class Rasterization {
  public:
    Rasterization() {}

    // for a cloth particle, if no corresponding lidar point are found.
    // the heightval are set as its neighbor's

    float findHeightValByScanline(Particle* p, Cloth& cloth)
    {
        int xpos = p->pos_x;
        int ypos = p->pos_y;

        for (int i = xpos + 1; i < cloth.num_particles_width; i++)
        {
            float crresHeight = cloth.getParticle(i, ypos)->nearestPointHeight;

            if (crresHeight > MIN_INF)
                return crresHeight;
        }

        for (int i = xpos - 1; i >= 0; i--)
        {
            float crresHeight = cloth.getParticle(i, ypos)->nearestPointHeight;

            if (crresHeight > MIN_INF)
                return crresHeight;
        }

        for (int j = ypos - 1; j >= 0; j--)
        {
            float crresHeight = cloth.getParticle(xpos, j)->nearestPointHeight;

            if (crresHeight > MIN_INF)
                return crresHeight;
        }

        for (int j = ypos + 1; j < cloth.num_particles_height; j++)
        {
            float crresHeight = cloth.getParticle(xpos, j)->nearestPointHeight;

            if (crresHeight > MIN_INF)
                return crresHeight;
        }

        return findHeightValByNeighbor(p);
    }

    float findHeightValByNeighbor(Particle* p)
    {
        std::queue<Particle*> nqueue;
        std::vector<Particle*> pbacklist;
        int neiborsize = p->neighborsList.size();

        for (int i = 0; i < neiborsize; i++)
        {
            p->isVisited = true;
            nqueue.push(p->neighborsList[i]);
        }

        // iterate over the nqueue
        while (!nqueue.empty())
        {
            Particle* pneighbor = nqueue.front();
            nqueue.pop();
            pbacklist.push_back(pneighbor);

            if (pneighbor->nearestPointHeight > MIN_INF)
            {
                for (std::size_t i = 0; i < pbacklist.size(); i++)
                    pbacklist[i]->isVisited = false;

                while (!nqueue.empty())
                {
                    Particle* pp = nqueue.front();
                    pp->isVisited = false;
                    nqueue.pop();
                }

                return pneighbor->nearestPointHeight;
            }
            else
            {
                int nsize = pneighbor->neighborsList.size();

                for (int i = 0; i < nsize; i++)
                {
                    Particle* ptmp = pneighbor->neighborsList[i];

                    if (!ptmp->isVisited)
                    {
                        ptmp->isVisited = true;
                        nqueue.push(ptmp);
                    }
                }
            }
        }

        return MIN_INF;
    }

    void RasterTerrian(Cloth& cloth, PointsVector& pc, std::vector<float>& heightVal)
    {
        for (std::size_t i = 0; i < pc.size(); i++)
        {
            float pc_x = pc[i].x;
            float pc_z = pc[i].z;

            float deltaX = pc_x - cloth.origin_pos[0];
            float deltaZ = pc_z - cloth.origin_pos[2];
            int col = int(deltaX / cloth.step_x + 0.5);
            int row = int(deltaZ / cloth.step_y + 0.5);

            if ((col >= 0) && (row >= 0))
            {
                Particle* pt = cloth.getParticle(col, row);
                pt->correspondingLidarPointList.push_back(i);
                float pc2particleDist =
                    pow(pc_x - pt->getPos()[0], 2) + pow(pc_z - pt->getPos()[2], 2);

                if (pc2particleDist < pt->tmpDist)
                {
                    pt->tmpDist = pc2particleDist;
                    pt->nearestPointHeight = pc[i].y;
                    pt->nearestPointIndex = i;
                }
            }
        }
        heightVal.resize(cloth.getSize());

        // //#pragma omp parallel for
        for (int i = 0; i < cloth.getSize(); i++)
        {
            Particle* pcur = cloth.getParticle1d(i);
            float nearestHeight = pcur->nearestPointHeight;

            if (nearestHeight > MIN_INF)
            {
                heightVal[i] = nearestHeight;
            }
            else
            {
                heightVal[i] = findHeightValByScanline(pcur, cloth);
            }
        }
    }
};

}  // namespace csf
