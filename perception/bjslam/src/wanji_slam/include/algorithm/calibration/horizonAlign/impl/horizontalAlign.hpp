/**
 * @file horizontalAlign.hpp
 * <AUTHOR> <PERSON>
 * @brief 地平校准公式实现
 * @version 0.1
 * @date 2023-07-20
 *
 * @copyright Copyright (c) 2023
 *
 */
/////////////////////////////////// usecase /////////////////////////////////////

#pragma once

#include "../CSF.h"
#include <Eigen/Core>
#include <boost/shared_ptr.hpp>
#include <pcl/features/normal_3d.h>
#include <pcl/sample_consensus/ransac.h>
#include <pcl/sample_consensus/sac_model_plane.h>

namespace wj_hA {

#define RADIAN_TO_DEGREE_FACTOR 57.2957795131 /**< 弧度转角度系数*/

//地面点过滤参数
#define CURVATURE_THRESHOLD 0.03f /**< 地面曲率过率阈值*/
#define K_NEIGHBORS_COUNT 20      /**< 法向量临域搜索点数*/
#define MINIMUM_POINTS_RATIO 0.06 /**< 最小地面点数占所有点云比例*/

class GroundPlaneExtract {
  private:
    csf::CSF* c_pCSFextracter_;  /**< 实现方法*/
    csf::PointCloudT::Ptr c_pc_; /**< 接收点云*/

  public:
    GroundPlaneExtract() : c_pCSFextracter_(nullptr), c_pc_(new csf::PointCloudT()) {}

    ~GroundPlaneExtract() {}

    /**
     * @brief 设置输入点云指针
     *
     * @tparam PointT
     * @param cin
     */
    template <typename PointT> void setInputCloud(boost::shared_ptr<pcl::PointCloud<PointT>> cin)
    {
        c_pc_ = cin;
    }

    /**
     * @brief 对单帧点云进行校准
     *
     * @param p_qHorizon
     * @return true
     * @return false
     */
    bool filter(Eigen::Quaterniond& p_qHorizon)
    {
        Eigen::VectorXf l_v4PlaneModel = Eigen::VectorXf::Zero(4);
        bool l_bRansac = calGroPara_(l_v4PlaneModel);  //获取平面方程
        if (l_bRansac)
            calcHorizontalQuat_(l_v4PlaneModel, p_qHorizon);
        //失败则不计算
        return l_bRansac;
    }

    /**
     * @brief 对点云进行地面倾角/高度评估
     *
     * @param p_fAngle 待填充角度,失败则高度赋0值,角度赋90
     * @param p_fGroundHeight 待填充高度
     */
    void angleAssess(float& p_fAngle, float& p_fGroundHeight)
    {
        Eigen::VectorXf l_v4PlaneModel = Eigen::Vector4f::Zero();
        bool l_bRansac = calGroPara_(l_v4PlaneModel);
        //拟合结果好就计算平面高度，否则平面高度为0
        if (l_bRansac)
        {
            calcGroundHeight_(l_v4PlaneModel, p_fGroundHeight);
            calcPlanAngle_(l_v4PlaneModel, p_fAngle);
        }
        else
        {
            p_fGroundHeight = 0;  //赋0值
            p_fAngle = 90;        //给一个较大的值
        }

        //计算平面倾角
    }

  private:
    /**
     * @brief 曲率滤波
     *
     * @param p_vGndList 地面点云索引
     * @param p_vOffGndList 非地面点云索引
     */
    void curvature_filter_(std::vector<int>& p_vGndList, std::vector<int>& p_vOffGndList)
    {
        pcl::NormalEstimation<pcl::PointXYZ, pcl::Normal> n;
        pcl::PointCloud<pcl::Normal>::Ptr normals(new pcl::PointCloud<pcl::Normal>());
        //建立kdtree来进行近邻点集搜索
        pcl::search::KdTree<pcl::PointXYZ>::Ptr tree(new pcl::search::KdTree<pcl::PointXYZ>());
        // 分离地面点
        pcl::PointCloud<pcl::PointXYZ>::Ptr l_pcGnd(new pcl::PointCloud<pcl::PointXYZ>());
        pcl::copyPointCloud(*c_pc_, p_vGndList, *l_pcGnd);
        //输入点云
        n.setInputCloud(l_pcGnd);
        n.setSearchMethod(tree);
        //点云法向计算时，需要所搜的近邻点大小
        n.setKSearch(K_NEIGHBORS_COUNT);
        //开始进行法向量计算
        n.compute(*normals);  //这里获取的法向量点云 normals存储法向量,顺序与l_pcGnd一致
        //利用曲率滤波
        for (int i = 0; i < static_cast<int>(normals->size()); ++i)
        {
            if (normals->points[i].curvature > CURVATURE_THRESHOLD)
            {
                // 点归类到offground
                p_vOffGndList.emplace_back(p_vGndList[i]);
                // 移除点
                p_vGndList[i] = -1;
            }
        }
        // 清理空间
        pcl::PointCloud<pcl::Normal>().swap(*normals);
        pcl::PointCloud<pcl::PointXYZ>().swap(*l_pcGnd);
        if (!p_vGndList.empty())
        {
            // 排序把-1放到一起
            std::sort(p_vGndList.begin(), p_vGndList.end());  //升序处理,把-1放在最前面
            // 移除重复的-1
            p_vGndList.erase(
                std::unique(p_vGndList.begin(), p_vGndList.end()),
                p_vGndList.end());  // unique 获取不重复元素,按顺序填入vector,但没改变vector其他数据
            // unique返回不重复元素的末尾,从不重复元素末尾到vector末尾的数都要被移除
            // 移除唯一一个-1
            p_vGndList.erase(p_vGndList.begin());
        }
        if (!p_vOffGndList.empty())
        {
            // 因为新加入点，排序
            std::sort(p_vOffGndList.begin(), p_vOffGndList.end());
            p_vOffGndList.erase(std::unique(p_vOffGndList.begin(), p_vOffGndList.end()),
                                p_vOffGndList.end());
        }
    }

    /**
     * @brief 求平面方程，输入点云，输出平面方程参数
     *
     * @tparam PointT
     * @param cin 输入点云
     * @param p_v4PlaneModel 平面方程系数 Ax + By + Cz + D = 0;
     * @return true 成功
     * @return false 失败
     */
    template <typename PointT>
    bool caclPlaneFunctionRANSAC_(const boost::shared_ptr<pcl::PointCloud<PointT>> cin,
                                  Eigen::VectorXf& p_v4PlaneModel)
    {
        pcl::SampleConsensusModelPlane<pcl::PointXYZ>::Ptr model_p(
            new pcl::SampleConsensusModelPlane<pcl::PointXYZ>(cin));
        pcl::RandomSampleConsensus<pcl::PointXYZ> ransac(model_p);
        ransac.setDistanceThreshold(0.05);
        ransac.setMaxIterations(100);  //设置迭代次数
        if (ransac.computeModel())
        {
            ransac.getModelCoefficients(p_v4PlaneModel);  //返回直线法向量形式方程参数A\B\C\D
            return true;
        }
        // // 无法计算模型！
        else
        {
            p_v4PlaneModel = Eigen::Vector4f(0, 0, 1, 0);
            return false;
        }
    }

    /**
     * @brief 根据平面方程参数，获取使平面旋转到水平的四元数
     *
     * @param p_v4PlaneModel 平面方程系数
     * @param p_qHorizon 平面旋转矩阵四元数
     */
    void calcHorizontalQuat_(const Eigen::VectorXf& p_v4PlaneModel, Eigen::Quaterniond& p_qHorizon)
    {
        //需要判断下地面方向两的方向，方向影响旋转效果
        Eigen::Vector3d v1;
        if (p_v4PlaneModel(2) > 0)
        {
            v1[0] = p_v4PlaneModel(0);
            v1[1] = p_v4PlaneModel(1);
            v1[2] = p_v4PlaneModel(2);
        }
        else
        {
            v1[0] = -p_v4PlaneModel(0);
            v1[1] = -p_v4PlaneModel(1);
            v1[2] = -p_v4PlaneModel(2);
        }
        Eigen::Vector3d v2(0, 0, 1);  //水平面法向量
        Eigen::Quaterniond l_qHorizonThis = Eigen::Quaterniond::FromTwoVectors(v1, v2);

        // 新的转移矩阵
        p_qHorizon = l_qHorizonThis * p_qHorizon;
    }

    /**
     * @brief 根据平面方程参数，计算平面与水平面夹角
     *
     * @param p_v4PlaneModel 平面方程系数
     * @param p_fAngle 计算倾角结果
     */
    void calcPlanAngle_(const Eigen::VectorXf& p_v4PlaneModel, float& p_fAngle)
    {
        float sum =
            sqrt(p_v4PlaneModel(0) * p_v4PlaneModel(0) + p_v4PlaneModel(1) * p_v4PlaneModel(1)
                 + p_v4PlaneModel(2) * p_v4PlaneModel(2));
        float ang = acos(fabs(p_v4PlaneModel(2)) / sum);  //归一化后的C与法向量(0,0,1)的 arccos

        p_fAngle = ang * RADIAN_TO_DEGREE_FACTOR;
    }

    /**
     * @brief 根据平面方程参数，计算平面(地面)高度
     *
     * @param p_v4PlaneModel 平面方程系数
     * @param p_fGroundHeight 计算高度结果
     */
    void calcGroundHeight_(Eigen::VectorXf& p_v4PlaneModel, float& p_fGroundHeight)
    {
        p_fGroundHeight = -1 * p_v4PlaneModel(3) / p_v4PlaneModel(2);
    }

    /**
     * @brief 计算平面参数
     *
     * @param l_v4PlaneModel 待填充平面方程参数
     * @return true
     * @return false
     */
    bool calGroPara_(Eigen::VectorXf& l_v4PlaneModel)
    {
        // 地面点云
        csf::PointCloudT::Ptr l_pcGnd(new csf::PointCloudT());
        std::vector<int> l_vGroundList;    /**< 地面点索引*/
        std::vector<int> l_vOffGroundList; /**< 非地面点索引*/
        c_pCSFextracter_ = new csf::CSF();
        //是否进行边坡后处理。当有陡变地形是设置为ture
        c_pCSFextracter_->c_stParams.m_bSloopSmooth = true;
        //表示布料网格大小，一般与点云间距相当。默认0.5m(大一点，布的刚性大，弹性小)
        c_pCSFextracter_->c_stParams.m_fClothResolution = 1.5;
        // 3表示平坦地形;2表示有缓坡的地形;1表示有较陡的地形
        c_pCSFextracter_->c_stParams.m_iRigidness = 2;
        // 每次迭代下降,默认是0.65
        c_pCSFextracter_->c_stParams.m_fTimeStep = 0.75;
        // 指根据点与模拟地形之间的距离，将点云划分为地面和非地面部分的阈值
        c_pCSFextracter_->c_stParams.m_fClassThreshold = 0.25;
        //最大迭代次数
        c_pCSFextracter_->c_stParams.m_iInterations = 500;
        // csf滤波输入点云
        if (!c_pc_->empty())
            c_pCSFextracter_->setPointCloud(c_pc_);
        else
        {
            std::cout << "[地平校准]:标定失败!" << std::endl;
            std::cout << "输入点云为空" << std::endl;
            return false;
        }
        // 输出地面点和非地面点索引
        c_pCSFextracter_->do_filtering(l_vGroundList, l_vOffGroundList, false);
        if (l_vGroundList.size() < c_pc_->size() * MINIMUM_POINTS_RATIO)
        {
            std::cout << "[地平校准]:标定失败!" << std::endl;
            std::cout << "当前地面点数过少:" << l_vGroundList.size() << "低于设定值["
                      << ((c_pc_->size()) * MINIMUM_POINTS_RATIO) << "]" << std::endl;
            // todo:若失败,应存储该帧点云看原因
            //若点数过少,有可能是布料模型提取有问题,也可能是点云本身有问题
            return false;
        }
        // 曲率滤波地面点
        curvature_filter_(l_vGroundList, l_vOffGroundList);
        // 分离地面点
        pcl::copyPointCloud(*c_pc_, l_vGroundList, *l_pcGnd);
        // 计算平面方程
        bool l_bRansac = caclPlaneFunctionRANSAC_(l_pcGnd, l_v4PlaneModel);
        return l_bRansac;
    }
};

}  // namespace wj_hA