/*
 * @Description:
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2021-09-13 08:48:26
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-04-28 09:46:58
 */
// ======================================================================================
// Copyright 2017 State Key Laboratory of Remote Sensing Science,
// Institute of Remote Sensing Science and Engineering, Beijing Normal
// University

// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at

//     http://www.apache.org/licenses/LICENSE-2.0

// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// ======================================================================================

// #######################################################################################
// # # #            CSF: Airborne LiDAR filtering based on Cloth Simulation # #
// # #  Please cite the following paper, If you use this software in your work.
// # # # #  Zhang W, Qi J, Wan P, Wang H, Xie D, Wang X, Yan G. An Easy-to-Use
// Airborne LiDAR  # #  Data Filtering Method Based on Cloth Simulation. Remote
// Sensing. 2016; 8(6):501.   # # (http://ramm.bnu.edu.cn/) # # # # Wuming
// Zhang; Jianbo Qi; Peng Wan; Hongtao Wang                # # # # contact us:
// <EMAIL>; <EMAIL>                # # #
// #######################################################################################

// cloth simulation filter for airborne lidar filtering
#pragma once

#include "impl/CSF_base.hpp"
#include <Eigen/Core>
#include <fstream>
#include <pcl/common/common.h>
#include <pcl/io/pcd_io.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <queue>
#include <string>
#include <vector>

namespace csf {

class CSF {
  public:
    CSF();
    ~CSF();

    // set pointcloud from vector
    void setPointCloud(std::vector<PointCSF> points);
    // set point cloud from a one-dimentional array. it defines a N*3 point cloud
    // by the given rows.
    void setPointCloud(double* points, int rows);

    // set point cloud for python
    void setPointCloud(std::vector<std::vector<float>> points);

    // read pointcloud from txt file: (X Y Z) for each line
    // void readPointsFromFile(std::string filename);
    void setPointCloud(pcl::PointCloud<pcl::PointXYZ>::Ptr cloud_in);

    inline const PointCloudT::Ptr getPointCloud()
    {
        return point_cloud;
    }

    // save points to file
    void savePoints(std::vector<int> grp, pcl::PointCloud<pcl::PointXYZ>::Ptr cloud_out);

    // get size of pointcloud
    std::size_t size()
    {
        return point_cloud->points.size();
    }

    // PointCloud set pointcloud
    void setPointCloud(PointCloudT& pc);

    // The results are index of ground points in the original
    // pointcloud
    void do_filtering(std::vector<int>& groundIndexes,
                      std::vector<int>& offGroundIndexes,
                      bool exportCloth);

  private:
    PointCloudT::Ptr point_cloud;

  public:
    typedef struct st_Params
    {
        // refer to the website:http://ramm.bnu.edu.cn/projects/CSF/ for the setting
        // of these paramters
        bool m_bSloopSmooth;
        float m_fTimeStep;
        float m_fClassThreshold;
        float m_fClothResolution;
        int m_iRigidness;
        int m_iInterations;
        bool m_bDebug;
    } st_Params;

    st_Params c_stParams;
};

CSF::CSF()
{
    c_stParams.m_bSloopSmooth = true;
    c_stParams.m_fTimeStep = 0.65;
    c_stParams.m_fClassThreshold = 0.5;
    c_stParams.m_fClothResolution = 1;
    c_stParams.m_iRigidness = 3;
    c_stParams.m_iInterations = 500;
    c_stParams.m_bDebug = false;
    // 新建点云缓存
    point_cloud.reset(new PointCloudT());
}

CSF::~CSF()
{
    PointCloudT().swap(*point_cloud);
}

// read pointcloud from pcl::PointsVector
void CSF::setPointCloud(pcl::PointCloud<pcl::PointXYZ>::Ptr cloud_in)
{
    point_cloud->points.resize(cloud_in->points.size());

    for (size_t i = 0; i < cloud_in->points.size(); i++)
    {
        point_cloud->points[i].x = cloud_in->points[i].x;
        point_cloud->points[i].y = -cloud_in->points[i].z;
        point_cloud->points[i].z = cloud_in->points[i].y;
    }
}

// The results are index of ground points in the original
// pointcloud
void CSF::do_filtering(std::vector<int>& groundIndexes,
                       std::vector<int>& offGroundIndexes,
                       bool exportCloth = false)
{
    // Terrain
    if (c_stParams.m_bDebug)
        std::cout << " Configuring terrain..." << std::endl;
    // 获取AABB包围盒
    PointCSF bbMin, bbMax;
    pcl::getMinMax3D(*point_cloud, bbMin, bbMax);

    float cloth_y_height = 0.05;

    int clothbuffer_d = 2;
    Eigen::Vector3f origin_pos(bbMin.x - clothbuffer_d * c_stParams.m_fClothResolution,
                               bbMax.y + cloth_y_height,
                               bbMin.z - clothbuffer_d * c_stParams.m_fClothResolution);

    int width_num =
        static_cast<int>(std::floor((bbMax.x - bbMin.x) / c_stParams.m_fClothResolution))
        + 2 * clothbuffer_d;

    int height_num =
        static_cast<int>(std::floor((bbMax.z - bbMin.z) / c_stParams.m_fClothResolution))
        + 2 * clothbuffer_d;

    if (c_stParams.m_bDebug)
        std::cout << " Configuring cloth..." << std::endl;
    if (c_stParams.m_bDebug)
        std::cout << "  - width: " << width_num << " "
                  << "height: " << height_num << std::endl;

    Cloth cloth(origin_pos,
                width_num,
                height_num,
                c_stParams.m_fClothResolution,
                c_stParams.m_fClothResolution,
                0.3,
                9999,
                c_stParams.m_iRigidness,
                c_stParams.m_fTimeStep);

    if (c_stParams.m_bDebug)
        std::cout << " Rasterizing..." << std::endl;
    Rasterization rasterization;
    rasterization.RasterTerrian(cloth, point_cloud->points, cloth.getHeightvals());

    float time_step2 = c_stParams.m_fTimeStep * c_stParams.m_fTimeStep;
    float gravity = 0.2;

    if (c_stParams.m_bDebug)
        std::cout << " Simulating..." << std::endl;
    cloth.addForce(Eigen::Vector3f(0, -gravity, 0) * time_step2);

    for (int i = 0; i < c_stParams.m_iInterations; i++)
    {
        float maxDiff = cloth.timeStep();
        cloth.terrCollision();
        if ((maxDiff != 0) && (maxDiff < 0.005))
        {
            // early stop
            break;
        }
    }

    if (c_stParams.m_bSloopSmooth)
    {
        if (c_stParams.m_bDebug)
            std::cout << "  - post handle..." << std::endl;
        cloth.movableFilter();
    }

    c2cdist c2c(c_stParams.m_fClassThreshold);
    c2c.calCloud2CloudDist(cloth, point_cloud->points, groundIndexes, offGroundIndexes);
}
}  // namespace csf