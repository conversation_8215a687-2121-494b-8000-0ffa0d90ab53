/**
 * @file continueAlign.hpp
 * <AUTHOR> Li
 * @brief 地平校准算法逻辑类
 * @version 0.1
 * @date 2023-07-21
 *
 * @copyright Copyright (c) 2023
 *
 */
#pragma once
#include "horizontalAlign.hpp"
#include <Eigen/Core>
#include <boost/shared_ptr.hpp>
#include <queue>
#include <thread>
namespace wj_cA {
#define CALIBRATION_ANGLE_THRESHOLD 0.2f  /**< 校准后偏移角度阈值*/
#define STANDARD_DEVIATION_THRESHOLD 0.2f /**< 校准结果高度标准差阈值*/
class GroundContinueAlign {
  public:
    /**
     * @brief 地平校准状态
     *
     */
    enum c_State {
        NotStart = 0,        /**< 未启动*/
        WAITDATA = 1,        /**< 等待数据*/
        InProgress = 2,      /**< 进行中*/
        Finished = 3,        /**< 校准完成,且结果好*/
        DataNotContinue = 4, /**< push进来数据不连续      异常*/
        AlignNotGood = 5,    /**< 校准结果不是很好        异常*/
        HorizonAbnormal = 6, /**< 视野异常               异常*/
    };
    typedef pcl::PointXYZ PT;

  private:
    /**
     * @brief 校准结果结构体
     *
     */
    typedef struct AlignConfig
    {
        Eigen::Quaterniond m_OnceQuat; /**< 校准四元数*/
        float m_fAngle;                /**< 校准后角度*/
        float m_fGroundHeight;         /**< 校准后高度*/
    } s_AlignConfig;

    /* data */
    bool c_bAlignOver_ = false;         /**< 校准计算是否完成*/
    bool c_bResultGood_ = true;         /**< 校准结果是否好*/
    bool c_bPushOver_ = false;          /**< push是否完成*/
    bool c_bShutdown = false;           /**< 关闭*/
    bool c_bShutdownOver = false;       /**< 已关闭标志*/
    float c_fGroundHeight_ = -99;       /**< 地面高度*/
    float c_fGroundAngle_ = -99;        /**< 地面角度*/
    int c_iRansacFailedTime_ = 0;       /**< Ransac拟合失败次数*/
    int c_iAligeTime_;                  /**< 校准次数*/
    bool c_bDataContinue_ = true;       /**< push进来的数据连续标志*/
    Eigen::Quaterniond c_qOptimalQurt_; /**< 最优校准四元素*/
    std::queue<boost::shared_ptr<pcl::PointCloud<PT>>> c_qInputCloud_; /**<
                                                                          push需要校准点云指针队列*/
    std::vector<int> c_vScanFrameID_;                                  /**< push进来帧号*/
    std::vector<s_AlignConfig> c_vAllResult_; /**< 连续多次校准结果*/
    wj_hA::GroundPlaneExtract c_GPE_;         /**< 地面提取对象*/

  public:
    /**
     * @brief 构造函数
     *
     */
    GroundContinueAlign(/* args */) {}
    /**
     * @brief 析构函数
     *
     */
    ~GroundContinueAlign() {}

    /**
     * @brief push点云进队列
     *
     * @tparam PointT
     * @param p_FrameID 帧号
     * @param cin 点云指针
     */
    template <typename PointT>
    void setInputCloud(int p_FrameID, boost::shared_ptr<pcl::PointCloud<PointT>> cin)
    {
        if (!c_bPushOver_)
        {
            //存帧号
            c_vScanFrameID_.push_back(p_FrameID);
            checkDataContinue_();
            //存点云指针
            boost::shared_ptr<pcl::PointCloud<PT>> l_pc(new pcl::PointCloud<PT>);
            pcl::copyPointCloud(*cin, *l_pc);
            c_qInputCloud_.push(l_pc);
            //如果push够了
            if ((int)c_qInputCloud_.size() == (c_iAligeTime_ + 1))
            {
                c_bPushOver_ = true;
            }
        }
    }

    /**
     * @brief 设置校准次数
     *
     * @param p_iAlignTime
     */
    void setAlignTime(int p_iAlignTime)
    {
        c_iAligeTime_ = p_iAlignTime;
    }

    /**
     * @brief 获取最优校正结果
     *
     * @return Eigen::Quaterniond 最有校准四元数
     */
    Eigen::Quaterniond getOutputQurt()
    {
        return c_qOptimalQurt_;
    }

    /**
     * @brief 获取校准流程结束状态
     *
     * @return true
     * @return false
     */
    bool checkAlignOver()
    {
        return c_bAlignOver_;
    }

    /**
     * @brief 检测push流程是否结束
     *
     * @return true
     * @return false
     */
    bool checkPushOver()
    {
        return c_bPushOver_;
    }

    /**
     * @brief 获取校准完后的地面高度
     *
     * @return float
     */
    float getGroundHeight()
    {
        return c_fGroundHeight_;
    }

    /**
     * @brief 获取校准完后的地面角度
     *
     * @return float
     */
    float getGroundAngle()
    {
        return c_fGroundAngle_;
    }

    /**
     * @brief 判断当前水平校准状态
     *
     * @return int 不同状态码
     */
    int checkAlignState()
    {
        if (c_qInputCloud_.empty() && !c_bAlignOver_)
        {
            return c_State::WAITDATA;  //没开始
        }
        else if (!c_bDataContinue_)
        {
            return c_State::DataNotContinue;  //数据不连续   过程中就可以判断
        }
        else if (checkRANSACError_())
        {
            return c_State::HorizonAbnormal;  //视野异常（RANSAC失败）     过程中可以判断
        }
        else if (c_bAlignOver_ && c_bResultGood_)
        {
            return c_State::Finished;  //校准完成，且结果好
        }
        else if (c_bAlignOver_ && !c_bResultGood_)
        {
            return c_State::AlignNotGood;  //校准完成，但结果不好
        }
        else
        {
            return c_State::InProgress;  //进行中
        }
    }

    /**
     * @brief 校准流程实现
     *
     */
    void run()
    {
        while (!c_bShutdown)
        {
            // push完成
            if (c_bPushOver_)
            {
                if (!c_vAllResult_.empty())
                {
                    onceAlignAssess_(c_qInputCloud_.front());
                }
                //添加进来的N帧点云依次单帧操作
                if ((int)c_vAllResult_.size() < c_iAligeTime_)
                {
                    onceAlign_(c_qInputCloud_.front());
                }
                pcl::PointCloud<PT>().swap(*c_qInputCloud_.front());
                c_qInputCloud_.pop();
                if ((int)c_vAllResult_.size() >= c_iAligeTime_)
                {
                    onceAlignAssess_(c_qInputCloud_.front());
                    pcl::PointCloud<PT>().swap(*c_qInputCloud_.front());
                    c_qInputCloud_.pop();
                    // 如果RANSAC都成功的化进入求解环节
                    if (!checkRANSACError_())
                        optimalQurt_(c_qOptimalQurt_);
                    c_bAlignOver_ = true;
                    break;
                }
            }
            else
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
        c_bShutdownOver = true;
    }
    /**
     * @brief 关闭校准
     *
     */
    void shutdown()
    {
        c_bShutdown = true;
        while (!c_bShutdownOver)
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

  private:
    /**
     * @brief 对单帧进行水平面校准
     *
     * @param p_cin
     */
    void onceAlign_(boost::shared_ptr<pcl::PointCloud<PT>> p_cin)
    {
        c_GPE_.setInputCloud(p_cin);
        Eigen::Quaterniond l_quat(1.0, 0.0, 0.0, 0.0);
        bool l_Ransac = c_GPE_.filter(l_quat);  //若成功,输出修正值的四元数/若失败,输出初始值
        if (!l_Ransac)
            c_iRansacFailedTime_++;
        s_AlignConfig l_alignConfig;
        l_alignConfig.m_OnceQuat = l_quat;  //存储校准四元数
        c_vAllResult_.push_back(l_alignConfig);
    }

    /**
     * @brief 对单帧点云的地面倾角进行评估
     *
     * @param p_cin
     */
    void onceAlignAssess_(boost::shared_ptr<pcl::PointCloud<PT>> p_cin)
    {
        //先用上一帧校准的四元素对当前帧进行旋转
        int l_size = p_cin->size();
        Eigen::Quaterniond l_qLast = c_vAllResult_.back().m_OnceQuat;  //获取上一帧点云的校准四元数
        pcl::PointCloud<PT>::Ptr l_aligned(new pcl::PointCloud<PT>);  //实例化一临时点云指针
        l_aligned->resize(l_size);
        for (int i = 0; i < l_size; i++)
        {
            // 将点云进行旋转
            Eigen::Vector3d l_point(p_cin->points[i].x, p_cin->points[i].y, p_cin->points[i].z);
            l_point = l_qLast * l_point;  //旋转点云
            l_aligned->points[i].x = static_cast<float>(l_point[0]);
            l_aligned->points[i].y = static_cast<float>(l_point[1]);
            l_aligned->points[i].z = static_cast<float>(l_point[2]);
        }

        // 进行评估
        c_GPE_.setInputCloud(l_aligned);
        float l_fAngle = 0.0f;
        float l_fGroundHeight = 0.0f;
        c_GPE_.angleAssess(l_fAngle, l_fGroundHeight);           //获取校准后的角度和高度
        c_vAllResult_.back().m_fAngle = l_fAngle;                //角度结果存储
        c_vAllResult_.back().m_fGroundHeight = l_fGroundHeight;  //高度结果存储
    }

    /**
     * @brief 求最优四元素
     *
     * @param p_qOptimalquet
     */
    void optimalQurt_(Eigen::Quaterniond& p_qOptimalquet)
    {
        //选择结果最好的次数,取校准次数的1/4
        int l_num = c_iAligeTime_ / 4;
        //如果次数小于2
        if (l_num < 2 && c_iAligeTime_ >= 2 && c_vAllResult_.size() >= 2)
            l_num = 2;

        std::sort(
            c_vAllResult_.begin(), c_vAllResult_.end(), [](s_AlignConfig p_a, s_AlignConfig p_b) {
                return (p_a.m_fAngle < p_b.m_fAngle);
            });
        std::vector<Eigen::Quaterniond> l_vQurt;
        std::vector<float> l_vHeight;
        std::vector<float> l_vAngle;
        for (int i = 0; i < l_num; i++)
        {
            l_vQurt.push_back(c_vAllResult_[i].m_OnceQuat);
            l_vHeight.push_back(c_vAllResult_[i].m_fGroundHeight);
            l_vAngle.push_back(c_vAllResult_[i].m_fAngle);
        }
        quatAverage_(l_vQurt, p_qOptimalquet);     // 求最优四元数
        bool l_bHeight = checkHeight_(l_vHeight);  //地面高度异常判断
        bool l_bAngle = checkAngle_(l_vAngle);     //地面角度异常判断
        //校准之后的倾斜角度不超过阈值，且地面高度标准差不超过阈值，则表明校准结果好
        if (l_bAngle && l_bHeight)
        {
            c_bResultGood_ = true;
        }
        else
        {
            c_bResultGood_ = false;
        }
    }

    /**
     * @brief 通过帧号是否连续，检测push进来数据是否连续
     *
     */
    void checkDataContinue_()
    {
        int l_numID = c_vScanFrameID_.back() - c_vScanFrameID_.front();
        if ((int)c_vScanFrameID_.size() - 1 == l_numID)
            c_bDataContinue_ = true;
        else
            c_bDataContinue_ = false;
    }

    /**
     * @brief RANSAC失败两次及以上表示区域异常
     *
     * @return true
     * @return false
     */

    bool checkRANSACError_()
    {
        // RANSAC失败两次及以上表示区域异常
        // 校准次数过低直接失败
        if (c_iRansacFailedTime_ > 2 || c_iAligeTime_ < 2)
            return true;
        else
            return false;
    }

    /**
     * @brief 四元数求平均 采用特征值与特征向量方法
     *
     * @param p_vQurt 四元素的vector
     * @param p_qurt 平均后的结果
     */
    void quatAverage_(std::vector<Eigen::Quaterniond> p_vQurt, Eigen::Quaterniond& p_qurt)
    {
        if (!p_vQurt.empty())
        {
            // 构造矩阵
            float l_weight = 1.0 / p_vQurt.size();
            Eigen::Matrix4d l_matrAll = Eigen::Matrix4d::Zero();
            Eigen::Matrix4d l_matr;
            Eigen::MatrixXd l_matrV(4, 1);
            for (int i = 0; i < (int)p_vQurt.size(); i++)
            {
                l_matrV << p_vQurt[i].w(), p_vQurt[i].x(), p_vQurt[i].y(), p_vQurt[i].z();
                l_matr = l_matrV * l_matrV.transpose();
                l_matrAll += l_weight * l_matr;
            }
            //求矩阵的特征值与特征向量
            Eigen::EigenSolver<Eigen::Matrix4d> es(l_matrAll);
            Eigen::Matrix4d l_V = es.pseudoEigenvectors();
            Eigen::Quaterniond l_q(l_V(0, 0), l_V(1, 0), l_V(2, 0), l_V(3, 0));
            p_qurt = l_q;
        }
    }

    /**
     * @brief 检测高度值异常
     *
     * @param p_vHeight 存储各帧高度结果值容器
     * @return true
     * @return false
     */
    bool checkHeight_(std::vector<float> p_vHeight)
    {
        if (!p_vHeight.empty())
        {
            float l_fSum = 0;
            int l_iSize = p_vHeight.size();
            for (int i = 0; i < l_iSize; i++)
                l_fSum += p_vHeight[i];
            float l_fAverage = l_fSum / l_iSize;

            float sum = 0;
            for (int i = 0; i < l_iSize; i++)
                sum += (p_vHeight[i] - l_fAverage) * (p_vHeight[i] - l_fAverage);

            float l_fVariance = sum / l_iSize;
            float l_fHeiStanDv = sqrt(l_fVariance);

            c_fGroundHeight_ = l_fAverage;
            if (l_fHeiStanDv > STANDARD_DEVIATION_THRESHOLD)
                return false;
            else
                return true;
        }
        else
            return false;
    }
    /**
     * @brief 计算角度均值符合要求与否
     *
     * @param p_vAngle
     * @return true
     * @return false
     */
    bool checkAngle_(std::vector<float> p_vAngle)
    {
        if (!p_vAngle.empty())
        {
            float l_fSum = 0;
            int l_iSize = p_vAngle.size();
            for (int i = 0; i < l_iSize; i++)
                l_fSum += p_vAngle[i];
            float l_fAverage = l_fSum / l_iSize;
            c_fGroundAngle_ = l_fAverage;
            // float sum = 0;
            // for (int i = 0; i < l_iSize; i++)
            //     sum += (p_vAngle[i] - l_fAverage) * (p_vAngle[i] - l_fAverage);
            // float l_fVariance   =   sum / l_iSize;
            // float l_fHeiStanDv = sqrt(l_fVariance);
            //高度值异常
            if (c_fGroundAngle_ > CALIBRATION_ANGLE_THRESHOLD)
                return false;
            else
                return true;
        }
        else
            return false;
    }
};

}  // namespace wj_cA