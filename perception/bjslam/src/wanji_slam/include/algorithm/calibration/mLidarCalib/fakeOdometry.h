/*
 * @Description:
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2022-03-30 09:22:15
 * @LastEditors: <PERSON><PERSON><PERSON> Wen
 * @LastEditTime: 2022-10-14 14:04:57
 */
#pragma once
#include "algorithm/optimize/laserTransform.h"
#include "common/common_ex.h"
#include <boost/function.hpp>
#include <boost/shared_ptr.hpp>
#include <queue>
#define _FAKEODOMETRY_PRIVATE_TEST_
#ifdef _FAKEODOMETRY_PRIVATE_TEST_
#    include <gtest/gtest.h>
#endif

namespace wj_slam {

template <typename P> class FakeOdometry {
  public:
    typedef boost::shared_ptr<FakeOdometry<P>> Ptr;

  private:
    typedef typename KEYFRAME<P>::Ptr KEYFRAME_PTR;
    typedef typename FEATURE_PAIR<P>::Ptr FEATURE_PAIR_PTR;
    typedef s_POSE6D MoveSpeed;
    typedef s_POSE6D PoseDev;
    typedef s_POSE6D Pose;
    typedef double Time;
    typedef boost::function<void(KEYFRAME_PTR)> KF_OUTPUT_CB;

    boost::shared_ptr<LaserTransform<P>> c_pTranser_;  //点云转移类
    KF_OUTPUT_CB c_keyFrameCb_;                        //关键帧输出回调

    std::mutex c_mtxCalibLock_;

    bool c_bCalibRun_;         // Calib模块开始工作
    bool c_bCalibRunOver_;     //一次Calib完成
    bool c_bSysHasInit;        //第一帧初始化
    bool c_bShutDown_;         // Calib模块关闭
    bool c_bHasLocationDone_;  // Calib模块关闭

    std::queue<FEATURE_PAIR_PTR>& c_featureBuf_;
    FEATURE_PAIR_PTR c_pCurrFeature_;  // 当前帧特征

    Time c_timeCurr_ = 0;
    Time c_timeLast_ = 0;
    float c_fJumpNum_ = 0;

    s_PoseWithTwist c_stPredictPose_;      //预测当前帧位姿（即当前帧终点位姿）
    MoveSpeed c_stCurSpeed_;               // 0.1秒标准时间的位姿变化
    s_PoseWithTwist c_stLastPredictPose_;  //上一次全局位置

    void paramReset_();
    void paramInit_();

    void corretPC_(FEATURE_PAIR_PTR& p_pcInput);
    float getJumpNum_();
    void renewOdom_();
    void addKeyFrame_();

    void frameCallback(int p_iLaserId, FEATURE_PAIR_PTR& p_fEPair);
    void clearFeatureBuf2Lastest_();
    void clearFeatureBuf_();
    bool hasPointCloud_();
    FEATURE_PAIR_PTR getCurrFeature_()
    {
        FEATURE_PAIR_PTR l_pFeatrue;
        l_pFeatrue = c_featureBuf_.front();
        c_featureBuf_.pop();
        c_bHasLocationDone_ = false;
        return l_pFeatrue;
    }

#ifdef _FAKEODOMETRY_PRIVATE_TEST_
    FRIEND_TEST(fakeOdometryTest, poseTwistFlow);
    FRIEND_TEST(fakeOdometryTest, run);
    FRIEND_TEST(fakeOdometryTest, restart);

#endif
  public:
    s_PoseWithTwist renewPrecisionPose(s_PoseWithTwist& p_stPoseGlobal);
    s_PoseWithTwist getPrecisionPose();
    void setInitPose(s_PoseWithTwist& p_stPoseGlobal);
    void setSpeed(MoveSpeed p_speed);
    void waitLocatDone();
    void shutDown();
    void start();
    void run();
    void stop();
    bool isStop();
    FakeOdometry(std::queue<FEATURE_PAIR_PTR>& p_feature,
                 KF_OUTPUT_CB p_keyFrameCb,
                 int timeOffset);
    ~FakeOdometry();
};

}  // namespace wj_slam
// #ifdef WJSLAM_NO_PRECOMPILE
#include "impl/fakeOdometry.hpp"
// #else
// #    define WJSLAM_MLidarCalib(P) template class wj_slam::FakeOdometry<P>;
// #endif