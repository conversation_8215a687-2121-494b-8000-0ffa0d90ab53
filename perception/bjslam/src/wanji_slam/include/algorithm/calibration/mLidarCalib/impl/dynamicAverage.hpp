/**
 * @file dynamicAverage.hpp
 * <AUTHOR> <PERSON>
 * @brief
 * @version 1.0
 * @date 2023-07-28
 * @copyright Copyright (c)2023 Van<PERSON>
 */
#pragma once

#include <Eigen/Core>
#include <cmath>

namespace wj_slam {
#pragma region runningStat类
class runningStat
// see: <<Accurately computing running variance>>
// https://www.johndcook.com/blog/standard_deviation/
// 连续平均数和方差计算
{
  public:
    runningStat() : m_iN(0) {}

    void clear()
    {
        m_iN = 0;
    }

    void push(double x)
    {
        m_iN++;

        if (m_iN == 1)
        {
            m_fOldM = m_fNewM = x;
            m_fOldS = 0.0;
        }
        else
        {
            /**
             * @brief /Welford算法
             * Mk = Mk-1+ (xk – Mk-1)/k
                Sk = Sk-1 + (xk – Mk-1)*(xk – Mk)
                对于 2 ≤ k ≤ n，方差的第k个估计值为s^2 = Sk /( k – 1)。
             */
            m_fNewM = m_fOldM + (x - m_fOldM) / m_iN;           //威尔霍夫德算法算均值
            m_fNewS = m_fOldS + (x - m_fOldM) * (x - m_fNewM);  // 计算方差方差

            m_fOldM = m_fNewM;
            m_fOldS = m_fNewS;
        }
    }

    int num()
    {
        return m_iN;
    }

    double mean()
    {
        return (m_iN > 0) ? m_fNewM : 0.0;
    }

    double variance()
    {
        return ((m_iN > 1) ? m_fNewS / (m_iN - 1) : 0.0);
    }

    double stdDev()
    {
        return sqrt(variance());
    }

  private:
    int m_iN;
    double m_fOldM, m_fNewM, m_fOldS, m_fNewS;
};
#pragma endregion

#pragma region runningStatAngle类
class runningStatAngle
// see: <<Accurately computing running variance>>
// 连续平均数和方差计算
{
  public:
    runningStatAngle() : m_iN(0) {}

    void clear()
    {
        m_iN = 0;
    }
    //这里是为了防止超过360度时候出问题
    void push(double x)
    {
        m_iN++;
        double l_fSin = sin(x * D2R);
        double l_fCos = cos(x * D2R);
        if (m_iN == 1)
        {
            m_fOldM[0] = m_fNewM[0] = l_fSin;
            m_fOldS[0] = 0.0;

            m_fOldM[1] = m_fNewM[1] = l_fCos;
            m_fOldS[1] = 0.0;
        }
        else
        {
            m_fNewM[0] = m_fOldM[0] + (l_fSin - m_fOldM[0]) / m_iN;
            m_fNewS[0] = m_fOldS[0] + (l_fSin - m_fOldM[0]) * (l_fSin - m_fNewM[0]);

            m_fOldM[0] = m_fNewM[0];
            m_fOldS[0] = m_fNewS[0];

            m_fNewM[1] = m_fOldM[1] + (l_fCos - m_fOldM[1]) / m_iN;
            m_fNewS[1] = m_fOldS[1] + (l_fCos - m_fOldM[1]) * (l_fCos - m_fNewM[1]);

            m_fOldM[1] = m_fNewM[1];
            m_fOldS[1] = m_fNewS[1];
        }
    }

    int num()
    {
        return m_iN;
    }
    double mean()
    {
        double l_fMean = 0.0;
        if (m_iN > 0)
            l_fMean = atan2(m_fNewM[0], m_fNewM[1]) * R2D;
        return l_fMean;
    }

    double variance()
    {
        double l_fVariance = 0.0;

        if (m_iN > 1)
        {
            l_fVariance = -log(m_fNewM[0] * m_fNewM[0] + m_fNewM[1] * m_fNewM[1]) * R2D * R2D;
        }
        return l_fVariance;
    }

    double stdDev()
    {
        return sqrt(variance());
    }

  private:
    int m_iN;
    double m_fOldM[2], m_fNewM[2], m_fOldS[2], m_fNewS[2];
    // 1/180.0*M_PI
    static constexpr double D2R = 0.017453292519943;
    // 1*180.0/M_PI
    static constexpr double R2D = 57.295779513082;
};

#pragma endregion

#pragma region runningStatPose类
class runningStatPose {
  public:
    void clear()
    {
        for (int i = 0; i < 3; ++i)
        {
            m_fT[i].clear();
            m_fR[i].clear();
        }
    }
    int num()
    {
        return m_fT[0].num();
    }
    void push(Eigen::VectorXd p_stnewPose)
    {
        for (int i = 0; i < 3; ++i)
        {
            m_fT[i].push(p_stnewPose[i]);
            m_fR[i].push(p_stnewPose[i + 3]);
        }
    }
    Eigen::VectorXd mean()
    {
        Eigen::VectorXd l_coeffs = Eigen::VectorXd::Zero(6);
        for (int i = 0; i < 3; ++i)
        {
            l_coeffs[i] = m_fT[i].mean();
            l_coeffs[i + 3] = m_fR[i].mean();
        }
        return l_coeffs;
    }
    Eigen::VectorXd variance()
    {
        Eigen::VectorXd l_coeffs = Eigen::VectorXd::Zero(6);
        for (int i = 0; i < 3; ++i)
        {
            l_coeffs[i] = m_fT[i].variance();
            l_coeffs[i + 3] = m_fR[i].variance();
        }
        return l_coeffs;
    }
    Eigen::VectorXd stdDev()
    {
        Eigen::VectorXd l_coeffs = Eigen::VectorXd::Zero(6);
        for (int i = 0; i < 3; ++i)
        {
            l_coeffs[i] = m_fT[i].stdDev();
            l_coeffs[i + 3] = m_fR[i].stdDev();
        }
        return l_coeffs;
    }
    Eigen::VectorXd stdDevMerg()
    {
        Eigen::VectorXd l_coeffs = variance();
        Eigen::VectorXd l_mergStdDev = Eigen::VectorXd::Zero(2);
        for (int i = 0; i < 3; ++i)
        {
            l_mergStdDev[0] += l_coeffs[i];
            l_mergStdDev[1] += l_coeffs[i + 3];
        }
        l_mergStdDev[0] = sqrt(l_mergStdDev[0]);
        l_mergStdDev[1] = sqrt(l_mergStdDev[1]);
        return l_mergStdDev;
    }

  private:
    runningStat m_fT[3];
    runningStatAngle m_fR[3];
};
#pragma endregion

#pragma region runningStatPoseWithTwist类
class runningStatPoseWithTwist {
  public:
    void clear()
    {
        m_fP.clear();
        m_fV.clear();
    }
    int num()
    {
        return m_fP.num();
    }
    void push(Eigen::VectorXd p_stNewPose, Eigen::VectorXd p_stNewVelo)
    {
        m_fP.push(p_stNewPose);
        m_fV.push(p_stNewVelo);
    }
    /**
     * @brief 获取pose的均值
     * @code
     *
     * @endcode
     * @return [Eigen::VectorXd] \n
     * [pose均值的表达]
     *
     */
    Eigen::VectorXd meanPose()
    {
        return m_fP.mean();
    }
    /**
     * @brief 获取pose的标准差
     * @code
     *
     * @endcode
     * @return [Eigen::VectorXd] \n
     * [details wirte here]
     *
     */
    Eigen::VectorXd stdDevPose()
    {
        return m_fP.stdDev();
    }
    /**
     * @brief 获取偏移和旋转方差和的根
     * @code
     *
     * @endcode
     * @return [Eigen::VectorXd] \n
     * [方差和的根,类似于多个维度统一考察方差]
     *
     */
    Eigen::VectorXd stdDevPoseMerg()
    {
        return m_fP.stdDevMerg();
    }
    /**
     * @brief 获取速度的均值
     * @code
     *
     * @endcode
     * @return [Eigen::VectorXd] \n
     * [details wirte here]
     *
     */
    Eigen::VectorXd meanTwist()
    {
        return m_fV.mean();
    }

    /**
     * @brief 获取速度的标准差
     * @code
     *
     * @endcode
     * @return [Eigen::VectorXd] \n
     * [details wirte here]
     *
     */
    Eigen::VectorXd stdDevTwist()
    {
        return m_fV.stdDev();
    }

    /**
     * @brief 获取速度的融合标准差
     * @code
     *
     * @endcode
     * @return [Eigen::VectorXd] \n
     * [方差和的根,类似于多个维度统一考察方差]
     *
     */
    Eigen::VectorXd stdDevTwistMerg()
    {
        return m_fV.stdDevMerg();
    }

  private:
    runningStatPose m_fP;
    runningStatPose m_fV;
};
#pragma endregion
}  // namespace wj_slam