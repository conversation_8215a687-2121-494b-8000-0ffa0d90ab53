/**
 * @file trajectoryAnalysis.hpp
 * <AUTHOR> <PERSON>
 * @brief 雷达标定过程静态\动态位姿处理收集处理器
 * @version 1.0
 * @date 2023-07-28
 * @copyright Copyright (c)2023 Vanjee
 */
#pragma once
#include "../trajectoryAnalysis.h"
#include <fstream>
namespace wj_slam {
#pragma region StaticAnalysis类

StaticAnalysis::StaticAnalysis(int p_iUnstableTHR,
                               double p_fVeloTHR,
                               double p_fPosStdDev,
                               double p_fAnglStdDev)
    : c_iUnstableTHR_(p_iUnstableTHR),
      c_fVeloTHR_(p_fVeloTHR), c_fStdDevTHR_{p_fPosStdDev, p_fAnglStdDev}
{
    c_iStaticEndTime_ = -1;    // 静止终止时间戳
    c_iUnStaticDuration_ = 0;  // 累计抖动持续时间
    c_iStaticTime_ = 0;        //累计静止持续时间
    c_iNewPose_.reset();       //位姿重置
    c_iLastPose_.reset();      //位姿重置
}
StaticAnalysis::~StaticAnalysis() {}
void StaticAnalysis::renewPose(PoseTwist p_stPoseGlobal)
{
    c_iNewPose_ = p_stPoseGlobal;
    // 位姿静止
    if (checkStable_(c_iNewPose_, c_iLastPose_))
    {
        //若静止状态,则存入数据用于求均值和方差
        c_MeanPose_.push(c_iNewPose_.m_Pose.coeffs_6(), c_iNewPose_.m_Twist.coeffs_6());
        //累计静止时间
        c_iStaticTime_ += c_iNewPose_.m_tsSyncTime - c_iLastPose_.m_tsSyncTime;
        // 重置抖动时间累计
        c_iUnStaticDuration_ = 0;
        //记录最终静止时间戳
        c_iStaticEndTime_ = c_iNewPose_.m_tsSyncTime;
    }
    else
    {
        //累计非静止时间
        c_iUnStaticDuration_ += c_iNewPose_.m_tsSyncTime - c_iLastPose_.m_tsSyncTime;
        // 如果长时间不静止，则重置数据
        if (c_iUnStaticDuration_ > c_iUnstableTHR_)
        {
            //清空队列
            c_MeanPose_.clear();
            //重置静止时间
            c_iStaticTime_ = 0;
        }
    }
    c_iLastPose_ = c_iNewPose_;
}
int StaticAnalysis::hasStaticTime()
{
    return c_iStaticTime_;
}
bool StaticAnalysis::getLatestStaticMeanPose(PoseTwist& p_stStaticMean)
{
    if (c_MeanPose_.num() > MINNUM_ANAL)
    {
        Eigen::VectorXd l_PoseTwist = c_MeanPose_.meanPose();
        p_stStaticMean.m_Pose.setX(l_PoseTwist[0]);
        p_stStaticMean.m_Pose.setY(l_PoseTwist[1]);
        p_stStaticMean.m_Pose.setZ(l_PoseTwist[2]);
        p_stStaticMean.m_Pose.setRPY(l_PoseTwist[3], l_PoseTwist[4], l_PoseTwist[5]);
        // p_stStaticMean.m_Pose.printf("p_stStaticMean");
        p_stStaticMean.m_Twist.reset();
        p_stStaticMean.m_tsSyncTime = c_iStaticEndTime_;
        return true;
    }
    return false;
}
bool StaticAnalysis::checkStable_(PoseTwist p_newPose, PoseTwist p_lastPose)
{
    // 位姿静止
    if (p_newPose.m_Twist.norm() > c_fVeloTHR_ || p_lastPose.m_Twist.norm() > c_fVeloTHR_)
    {
        LOGO(WDEBUG,
             "{} [MLC] unstable-velo= {},{}",
             WJLog::getWholeSysTime(),
             p_newPose.m_Twist.norm(),
             p_lastPose.m_Twist.norm());
        return false;
    }
    // 平均数数量，数量太少则不检查差异
    int l_iMeanPoseNum = c_MeanPose_.num();  // push进去的静态的位姿数值
    if (l_iMeanPoseNum > MINNUM_ANAL)
    {
        // 若位置方差太大，则位置不稳定、开始运动
        Eigen::Vector2d l_StdDev = c_MeanPose_.stdDevPoseMerg();
        if (l_StdDev[0] > c_fStdDevTHR_[0] || l_StdDev[1] > c_fStdDevTHR_[1])
        {
            LOGO(WDEBUG,
                 "{} [MLC] unstable-dev= {},{}",
                 WJLog::getWholeSysTime(),
                 l_StdDev[0],
                 l_StdDev[1]);
            return false;
        }
    }
    return true;
}

#pragma endregion

#pragma region TrajectoryAnalysis类

TrajectoryAnalysis::TrajectoryAnalysis(int p_iJumpDuration,
                                       float p_fMoveVeloTHR,
                                       float p_fMaxRotSpeedTHR,
                                       float p_fMinChangeDistance,
                                       float p_fMinChangeAng)
    : c_iJumpDuration_(p_iJumpDuration), c_fMoveVeloTHR_(p_fMoveVeloTHR),
      c_fMaxRotSpeedTHR_(p_fMaxRotSpeedTHR), c_fMinChangeDistance_(p_fMinChangeDistance),
      c_fMinChangeAng_(p_fMinChangeAng), c_iCaliNum_(-1)
{
    initParam();
}
void TrajectoryAnalysis::initParam()
{
    c_LastPose_.m_bFlag = PoseStatus::SettingPose;
    c_vPath_.clear();
}
void TrajectoryAnalysis::renewPose(Pose p_stPoseGlobal)
{
    if (c_LastPose_.m_bFlag != PoseStatus::SettingPose)
    {
        // 静止
        if (isUniformVelocity_(c_LastPose_.m_Twist, p_stPoseGlobal.m_Twist))
            calcPoseAtDTime_(c_LastPose_, p_stPoseGlobal, c_vPath_);
    }
    c_LastPose_ = p_stPoseGlobal;
}
bool TrajectoryAnalysis::isUniformVelocity_(Velo& p_lastSpeed, Velo& p_curSpeed)
{
    //若上次用于计算的静止位置与当前位置差别大于设定阈值,则重置计数
    if ((sqrt((pow((c_LastPose_.m_Pose.x() - c_LastStaticPose_.m_Pose.x()), 2)
               + pow((c_LastPose_.m_Pose.y() - c_LastStaticPose_.m_Pose.y()), 2)))
         > c_fMinChangeDistance_)
        && (std::fabs(c_LastPose_.m_Pose.yaw() - c_LastStaticPose_.m_Pose.yaw())
            > c_fMinChangeAng_))
    {
        c_iCaliNum_ = 0;
    }
    //计算速度XYZ的平方和的根,欧几里德范数
    double l_dMaxspeed =
        (p_lastSpeed.norm() > p_curSpeed.norm()) ? p_lastSpeed.norm() : p_curSpeed.norm();
    //若线速度的范数大于阈值,不算静止帧
    if (l_dMaxspeed > c_fMoveVeloTHR_)
    {
        return false;
    }
    //若角速度大于阈值,不算静止帧
    if (std::fabs(p_lastSpeed.yaw()) > c_fMaxRotSpeedTHR_
        || std::fabs(p_curSpeed.yaw()) > c_fMaxRotSpeedTHR_)
    {
        return false;
    }
    //计算两个四元数的点积 点积的结果越接近1，表示两个旋转之间越相似
    //接近-1表示它们之间的旋转相反；接近0表示它们之间的旋转接近垂直。
    float l_fCosarg = std::fabs(p_lastSpeed.m_quat.dot(p_curSpeed.m_quat));
    //若旋转角度差别较大 去除
    if (l_fCosarg < 0.99)
        return false;
    //累计参与计算静止帧
    c_iCaliNum_++;
    //到达新的静止pose后,先静止稳定20帧,数据丢弃
    if (c_iCaliNum_ < 20)
    {
        c_LastStaticPose_ = c_LastPose_;
        return false;
    }
    /* todo:
        假设:以没有超过静止阈值的速度缓慢移动20帧,静止位置不断更改,若持续以接近静止的速度移动,只能认为也是静止了,虽然低速时不影响效果,
        还有更完美的逻辑去解决吗?
    */

    //一个静止位置最多累计加入计算共100帧,多余的数据丢弃
    if (c_iCaliNum_ > 120)
        return false;
    return true;
}
void TrajectoryAnalysis::calcPoseAtDTime_(Pose& p_start, Pose& p_end, PoseList& p_path)
{
    timeMs l_iSt = p_start.m_tsSyncTime;  //上一时刻的时间戳
    timeMs l_iEt = p_end.m_tsSyncTime;    //当前帧的时间戳
    timeMs l_iDt = l_iEt - l_iSt;        //帧间时间差
    // 如果时间差异过大或过小，不计算
    if (l_iDt > DELET_DELAY || l_iDt < 1)
        return;
    std::vector<int> l_vNewPathTime;  //准备插入虚拟路径点的时间戳集合
    // 首个时间戳(跳跃时间的N倍)
    int l_iPt = int(l_iSt) / c_iJumpDuration_ * c_iJumpDuration_;
    // 循环增加时间戳
    while (l_iPt <= l_iEt)
    {
        if (l_iPt >= l_iSt)
            l_vNewPathTime.push_back(l_iPt);
        l_iPt += c_iJumpDuration_;
    }
    // 为空则表示没有可插入位姿
    if (l_vNewPathTime.empty())
        return;
    // 匀速段加入新位姿
    Pose l_tempPose;
    // 位姿增量
    Velo l_tempVelo = p_start.m_Pose.inverse() * p_end.m_Pose;
    float l_fFromStart = 0;
    // 对每个可行的时间戳计算位姿插值
    for (auto l_pT : l_vNewPathTime)
    {
        l_tempPose.m_tsSyncTime = l_pT;
        l_fFromStart = (float)(l_pT - l_iSt) / l_iDt;
        l_tempPose.m_Pose = p_start.m_Pose * (l_tempVelo * l_fFromStart);
        p_path.push_back(l_tempPose);
    }
}

void TrajectoryAnalysis::getNewPoseList(PoseList& p_list)
{
    p_list = c_vPath_;
    c_vPath_.clear();
}

#pragma endregion

#pragma region MultiTrajectoryAnalysis类

MultiTrajectoryAnalysis::MultiTrajectoryAnalysis(int p_iLidarNum, int p_iBaseLidar)
    : c_iLidarNum_(p_iLidarNum), c_iBaseLidar_(p_iBaseLidar)
{
    c_vMean_.resize(c_iLidarNum_);
    c_iLastestTime_ = 0;
}
void MultiTrajectoryAnalysis::renewPose(int p_iLidar, int p_iTimeStamp, Pose p_new)
{
    PoseID l_new;
    // 储存雷达号
    l_new.first = p_iLidar;
    // 储存数据
    l_new.second = p_new.coeffs_6();
    c_iLastestTime_ = p_iTimeStamp;
    // 储存时间-位姿对(此处允许反复插入)
    //根据p_iTimeStamp 升序排列的键值容器
    c_mPosePairList_.insert(std::make_pair(p_iTimeStamp, l_new));
    //该容器存储格式为:first:时间戳 second.first:雷达号 second.second: 当前pose
}
void MultiTrajectoryAnalysis::calc()
{
    //对存储有多个雷达时间戳+位姿的容器去除无用数据
    checkNewPair_(c_mPosePairList_);
}
int MultiTrajectoryAnalysis::getNewCalibData(int p_iID,
                                             Eigen::VectorXd& p_pose,
                                             Eigen::VectorXd& p_dev)
{
    p_pose = c_vMean_[p_iID].mean();
    p_dev = c_vMean_[p_iID].stdDev();
    return c_vMean_[p_iID].num();
}
void MultiTrajectoryAnalysis::checkNewPair_(List& p_list)
{
    // 时间戳
    int l_key = 0;
    std::vector<int> l_vEraseKey;  //将被移除数据容器,根据时间戳移除排序
    //遍历该关联容器
    for (multiMapItor it = p_list.begin(); it != p_list.end();)
    {
        l_key = it->first;  // 获取关联容器内的首时间
        // 获取某一时间戳下的全部数据
        std::pair<multiMapItor, multiMapItor> pos = p_list.equal_range(l_key);
        /*获取时间为l_key的关联容器里的所有元素,用一个pair存储,第一个元素first指向第一个元素,
        second指向第N个元素的结尾
        对 pos 进行遍历可以获取这个范围内的所有元素
        */
        std::vector<int> l_IDQue;
        // 检查数据是否完整
        for (it = pos.first; it != pos.second; it++)
        {
            //获取当前遍历的时间戳下的存在数据的雷达号
            l_IDQue.push_back(it->second.first);
        }
        //雷达号排序
        std::sort(l_IDQue.begin(), l_IDQue.end());
        //去除重复元素
        l_IDQue.erase(std::unique(l_IDQue.begin(), l_IDQue.end()), l_IDQue.end());
        // 找到一组完整记录的标定数据
        if ((int)l_IDQue.size() == c_iLidarNum_)
        {
            // 进行标定数据计算并加入平均值
            getNewCalibData_(pos);
            l_vEraseKey.push_back(l_key);  //用过的数据将被删除
        }
        else if (c_iLastestTime_ - l_key > DELET_DELAY)
        {
            //如果这个时间戳数据不完整,且关联容器内最新帧的时间已经超过这个时间很久
            l_vEraseKey.push_back(l_key);
        }
        it = pos.second;  //指向下一个
    }
    for (int l_iEraseKey : l_vEraseKey)
        p_list.erase(l_iEraseKey);
}
void MultiTrajectoryAnalysis::getNewCalibData_(std::pair<multiMapItor, multiMapItor>& p_pos)
{
    multiMapItor it;
    s_POSE6D l_BaseQT, l_ThisQT;
    Eigen::VectorXd l_BaseRT, l_ThisRT;
    // 基准雷达位姿
    for (it = p_pos.first; it != p_pos.second; it++)
    {
        if (it->second.first == c_iBaseLidar_)
        {
            // 获取标定基础位置--基准雷达
            l_BaseRT = it->second.second;
            l_BaseQT.setX(l_BaseRT[0]);
            l_BaseQT.setY(l_BaseRT[1]);
            l_BaseQT.setZ(l_BaseRT[2]);
            l_BaseQT.setRPY(l_BaseRT[3], l_BaseRT[4], l_BaseRT[5]);
            break;
        }
    }
    // 其他雷达
    for (it = p_pos.first; it != p_pos.second; it++)
    {
        if (it->second.first == c_iBaseLidar_)
            continue;
        else
        {
            // 获取待标定雷达位置
            l_ThisRT = it->second.second;
            l_ThisQT.setX(l_ThisRT[0]);
            l_ThisQT.setY(l_ThisRT[1]);
            l_ThisQT.setZ(l_ThisRT[2]);
            l_ThisQT.setRPY(l_ThisRT[3], l_ThisRT[4], l_ThisRT[5]);
            // 标定数据计算

            Eigen::Quaterniond p_q =
                l_BaseQT.m_quat.conjugate() * l_ThisQT.m_quat;  //四元数的共轭表示旋转矩阵的逆
            Eigen::Vector3d p_t =
                l_BaseQT.m_quat.conjugate()
                * (l_ThisQT.m_trans - l_BaseQT.m_trans);  // 将世界坐标系的量转为局部坐标系转移矩阵
            l_ThisQT.m_trans = p_t;
            l_ThisQT.setQuat(p_q);
            // 储存标定数据
            c_vMean_[it->second.first].push(l_ThisQT.coeffs_6());

            // test
            // static std::string p_sFilePath =
            //     "/home/<USER>/SLAM/720slam/src/wanji_slam/data/Pose/calibdata.csv";
            // static std::fstream l_filePoseWR;
            // static bool l_bInit = true;
            // if (l_bInit || !l_filePoseWR.is_open())
            // {
            //     l_bInit = false;
            //     l_filePoseWR.open(p_sFilePath.c_str(), std::ios::out | std::ios::trunc);
            //     l_filePoseWR << "t,ref_x,ref_y,ref_yaw,";
            //     l_filePoseWR << "Tx,Ty,Tyaw,Tx_stdev,Ty_stdev,Tyaw_stdev,T_stdev,R_stdev"
            //                  << std::endl;
            // }
            // l_TransDev = c_vMean_[it->second.first].stdDev();
            // l_TransDevMerg = c_vMean_[it->second.first].stdDevMerg();
            // l_filePoseWR << p_pos.first->first << ",";
            // l_filePoseWR << l_ThisRT[0] << "," << l_ThisRT[1] << "," << l_ThisRT[5] << ",";
            // l_filePoseWR << l_TransRT[0] << "," << l_TransRT[1] << "," << l_TransRT[5] << ",";
            // l_filePoseWR << l_TransDev[0] << "," << l_TransDev[1] << "," << l_TransDev[5] << ",";
            // l_filePoseWR << l_TransDevMerg[0] << "," << l_TransDevMerg[1] << std::endl;
        }
    }
}
#pragma endregion

}  // namespace wj_slam