#include "protocolTool.h"

/**
 * @description: 协议要求最低长度为34 去除校验位和帧尾后最低30 验证并补0以满足最低长度
 * @param {char*} &p_pcBufCMD 完整协议
 * @param {int} p_iOffset ascii读取偏移量
 * @param {std::string&} p_sData 返回提取的string
 * @return {bool} true读取成功 false 协议长度不足strLen
 **/
void fillAskField(char* p_pcBufResponse, int& p_iLen)
{
    for (int i = p_iLen; i < 30; ++i)
        p_pcBufResponse[p_iLen++] = 0;
}

/**
 * @description: 计算异或校验值 不是CRC 历史起名错误
 * @param {u_char*} &p_pcBufResponse  协议去除帧头后地址
 * @param {int} p_iLen 去除帧头帧尾和校验位的协议长度
 * @return {char}  校验结果
 **/
char checkXOR(char* p_pcBufResponse, int p_iLen)
{
    char l_cRes = 0;
    int i = 0;
    for (i = 0; i < p_iLen; i++)
    {
        l_cRes ^= p_pcBufResponse[i];
    }
    return l_cRes;
}

u_char checkXOR(u_char* p_pucBufResponse, int p_iLen)
{
    char l_cRes = 0;
    int i = 0;
    for (i = 0; i < p_iLen; i++)
    {
        l_cRes ^= p_pucBufResponse[i];
    }
    return l_cRes;
}

/**
 *
 * @description:  获取当前协议第3、4个字节的
 * @param {int} p_iData   数值
 * @param {u_char*} p_pcBufCMD 当前协议
 * @param { int&} p_iLen
 * @return {*}
 * @other:
 */
uint32_t getFrameLen(u_char*& p_pucBuf, int p_iLen)
{
    if (p_iLen >= 4)
        return (((p_pucBuf[2] << 8) & 0xFF00 | p_pucBuf[3]) & 0xFFFF);
    return 0;
}

/**
 * @description: ascii协议提取string 自动修改偏移量
 * @param {u_char*} &p_pucBufCMD 完整协议
 * @param {int} p_iOffset ascii读取偏移量 即字节数+string
 * @param {std::string&} p_sData 返回提取的string
 * @return {bool} true读取成功 false 协议长度不足strLen
 **/
bool asciiToString_(u_char*& p_pucBufCMD, int& p_iOffset, std::string& p_sData)
{
    // 协议长度
    uint32_t l_uiAllLen = getFrameLen(p_pucBufCMD, p_iOffset);
    // 数据长度
    uint32_t l_uiDataLen = p_pucBufCMD[p_iOffset];

    if (l_uiAllLen >= l_uiDataLen + p_iOffset + 1)
    {
        char* l_pacBufTmp = new char[l_uiDataLen + 1];
        memcpy(l_pacBufTmp, &p_pucBufCMD[p_iOffset + 1], l_uiDataLen);
        l_pacBufTmp[l_uiDataLen] = 0;
        p_sData = l_pacBufTmp;
        // printf("asciiToStr: %s\n", p_sData.c_str());
        // 更新偏移量
        p_iOffset += p_pucBufCMD[p_iOffset] + 1;
        return true;
    }
    else
        printf("ascii len error %d | %d\n", l_uiAllLen, l_uiDataLen);

    return false;
}

/**
 * @description: 字符串转ascii码 自动修改偏移量
 * @param {string&} p_sData
 * @param {char*&} p_pcBuf
 * @param {int&} p_iLen 从此字节开始 2字节字符串长度 + 字符串
 * @return {*}
 * @other: 注意 限制字符串长度不可超过255 否则为0
 */
void stringToAscii_(std::string p_sData, char*& p_pcBuf, int& p_iLen)
{
    uint32_t l_uiDataSize = strlen(p_sData.c_str());
    if (l_uiDataSize > 255)
    {
        printf("str[%s] Len[%d] > 255 ", p_sData.c_str(), l_uiDataSize);
        l_uiDataSize = 0;
    }
    p_pcBuf[p_iLen++] = l_uiDataSize;

    if (l_uiDataSize)
    {
        char l_acData[255];
        memset(&l_acData[0], 0, 255);
        strcat(l_acData, p_sData.c_str());
        l_acData[l_uiDataSize] = 0;
        // printf("stringToAscii_: %s | %p | ", p_sData.c_str(), &l_acData[0]);
        for (uint32_t i = 0; i < l_uiDataSize; i++)
        {
            p_pcBuf[p_iLen++] = l_acData[i];
            // printf("%d ", l_acData[i]);
        }
        // printf("| %d\n", l_uiDataSize);
    }
}

/**
 * @description: 分割字符串
 * @param {const string&} p_sStr 待分割字符串
 * @param {std::vector<std::string>&} p_vList 分割后字符串List
 * @param {const std::string&} p_sSeparator 分隔符
 * @return {*}
 * @other:
 */
void splitString(const std::string& p_sStr,
                 std::vector<std::string>& p_vList,
                 const std::string& p_sSeparator)
{
    p_vList.clear();
    std::string::size_type pos1, pos2;
    pos2 = p_sStr.find(p_sSeparator);
    pos1 = 0;
    while (std::string::npos != pos2)
    {
        p_vList.push_back(p_sStr.substr(pos1, pos2 - pos1));

        pos1 = pos2 + p_sSeparator.size();
        pos2 = p_sStr.find(p_sSeparator, pos1);
    }
    if (pos1 != p_sStr.length())
        p_vList.push_back(p_sStr.substr(pos1));
}

/**
 * @description:IP eg:*********** 转ascii 自动修改偏移量
 * @param {string&} p_sIp
 * @param {u_char*&} p_pucBuf
 * @param {int&} p_iLen
 * @return {*}
 * @other: 4个字节写入ascitt对应位置
 */
void ipToAscii(std::string p_sIp, char*& p_pucBuf, int& p_iLen)
{
    std::vector<std::string> l_vsTmp;
    splitString(p_sIp, l_vsTmp, ".");
    if (l_vsTmp.size() != 4)
    {
        // IP格式错误 回复 0000
        p_pucBuf[p_iLen++] = 0;
        p_pucBuf[p_iLen++] = 0;
        p_pucBuf[p_iLen++] = 0;
        p_pucBuf[p_iLen++] = 0;
        return;
    }
    p_pucBuf[p_iLen++] = atoi(l_vsTmp[0].c_str());
    p_pucBuf[p_iLen++] = atoi(l_vsTmp[1].c_str());
    p_pucBuf[p_iLen++] = atoi(l_vsTmp[2].c_str());
    p_pucBuf[p_iLen++] = atoi(l_vsTmp[3].c_str());
}

/**
 * @description:ascii 提取IP 内部自动验证IP段是否满足4个字节 自动修改偏移量
 * @param {u_char*&} p_pucBuf
 * @param {int&} p_iLen
 * @param {string&} p_sData
 * @return {bool}  false  偏移量+4 少于 协议长度
 * @other:
 */
bool asciiToIP(u_char*& p_pucBuf, int& p_iLen, std::string& p_sIp)
{
    // 协议长度 = 去帧头帧尾
    int l_iAllLen = (int)getFrameLen(p_pucBuf, p_iLen);
    // 帧头[2] + l_iAllLen + 帧尾[2] >=  p_iLen +IP_Len[4] +校验位[2] +  帧尾[2] +1
    if (l_iAllLen >= p_iLen + 4)
    {
        p_sIp = "";
        p_sIp = std::to_string((int)(p_pucBuf[p_iLen])) + "."
                + std::to_string((int)(p_pucBuf[p_iLen + 1])) + "."
                + std::to_string((int)(p_pucBuf[p_iLen + 2])) + "."
                + std::to_string((int)(p_pucBuf[p_iLen + 3]));
        p_iLen += 4;
        return true;
    }
    else
    {
        printf("长度不符--l_iAllLen:%d,p_iLen:%d\n", l_iAllLen, p_iLen);
    }

    return false;
}

/**
 *
 * @description:  int转2字节 写入协议 自动修改偏移量
 * @param {int} p_iData   数值
 * @param {u_char*} p_pcBufCMD 当前协议
 * @param { int&} p_iLen
 * @return {*}
 * @other:
 */
void fillIntTo16Bytes(int p_iData, char* p_pcBufCMD, int& p_iLen)
{
    p_pcBufCMD[p_iLen++] = (p_iData & 0xff00) >> 8;
    p_pcBufCMD[p_iLen++] = p_iData & 0xff;
}

/**
 *
 * @description:  int转4字节 写入协议 自动修改偏移量
 * @param {int} p_iData   数值
 * @param {u_char*} p_pcBufCMD 当前协议
 * @param { int&} p_iLen
 * @return {*}
 * @other:
 */
void fillIntTo32Bytes(int p_iData, char* p_pcBufCMD, int& p_iLen)
{
    p_pcBufCMD[p_iLen++] = (p_iData & 0xff000000) >> 24;
    p_pcBufCMD[p_iLen++] = (p_iData & 0xff0000) >> 16;
    p_pcBufCMD[p_iLen++] = (p_iData & 0xff00) >> 8;
    p_pcBufCMD[p_iLen++] = p_iData & 0xff;
}