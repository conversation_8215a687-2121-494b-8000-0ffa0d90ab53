/*
 * @Description: 监控键盘指令
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2022-03-18 15:04:43
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-10-14 14:02:54
 */
#pragma once
#include <signal.h>
#include <stdio.h>
#include <termio.h>

#include <thread>

#include "master/driver_virtual/virtual_driver.h"

namespace wj_slam {
class KeyBoard {
  public:
    typedef typename VirtualDriver::Ptr VirtualDriver_Ptr;

  private:
    boost::shared_ptr<VirtualDriver>& c_driverPtr_;
    boost::function<int(int, int)> c_setActionCb_;  // 回调-调用其他模块进行操作
    enum KeyValue {
        Key_Space = 32 /* 空格键 */,
        Key_D = 68 /* D键 */,
        Key_d = 100 /* d键 */,
        Key_E = 69 /* E键 */,
        Key_e = 101 /* e键 */,
        Key_1 = 49 /* 1键 */,
        Key_Add = 43 /* +键 */,
        Key_Minus = 45 /* -键 */,
        Key_R = 82 /* R键 */,
        Key_r = 114 /* r键盘 */,
        Key_Well = 35 /* #键 */,
        Key_H = 72 /* H键 */,
        Key_h = 104 /* h键 */,
        Key_V = 86 /* V键 */,
        Key_v = 118 /* v键 */,
        Key_P = 80 /* P键*/,
        Key_p = 112 /* p键*/,
        Key_W = 87 /* W键*/,
        Key_w = 119 /* w键*/,
        Key_O = 79 /* O键*/,
        Key_o = 111 /* o键*/
    };

    bool c_bIsKeyOver_ = true;  // 键盘响应是否处理完成
    bool& c_bIsStart;           // 是否播放默认-运行
    bool c_bProvRun_ = true;
    bool c_bProcOver_ = true;

    /**
     * @description: 键盘响应 获取字符 并处理
     * @param {*}
     * @return {*}
     * @other:
     */
    void scanKeyboard_()
    {
        struct termios new_settings;
        struct termios stored_settings;
        tcgetattr(0, &stored_settings);
        new_settings = stored_settings;
        new_settings.c_lflag &= (~ICANON);
        new_settings.c_cc[VTIME] = 0;
        tcgetattr(0, &stored_settings);
        new_settings.c_cc[VMIN] = 1;
        tcsetattr(0, TCSANOW, &new_settings);
        int l_inputSignal = getchar();
        tcsetattr(0, TCSANOW, &stored_settings);
        procSignal_(l_inputSignal);
        c_bIsKeyOver_ = true;
    }

    /**
     * @description: 处理键盘key值
     * @param {int} l_iKey 键盘字符对应的十进制ASCII值
     * @return {*}
     * @other:
     */
    void procSignal_(int l_iKey)
    {
        // 不允许运行 or 上次处理未运行完毕 均等待
        if (!c_bProvRun_ || !c_bProcOver_)
            return;
        c_bProcOver_ = false;
        static unsigned char KeyNum = 0;
        // printf("get Key : %d\n", l_iKey);
        switch (l_iKey)
        {
            case KeyValue::Key_Space:
                KeyNum = 0;
                if (c_driverPtr_)
                {
                    c_bIsStart = !c_bIsStart;
                    c_driverPtr_->playPause(c_bIsStart);
                }
                break;
            case KeyValue::Key_D: /* Debug模式开关，先按D/d */
                if (KeyNum & 0xF0)
                {
                    KeyNum |= 0x01;
                }
                break;
            case KeyValue::Key_d: /* Debug模式开关，先按D/d */
                if (KeyNum & 0xF0)
                {
                    KeyNum |= 0x01;
                }
                break;
            case KeyValue::Key_E: /* Debug模式开关，再按E/e */
                if (KeyNum & 0xF1)
                {
                    printf("changeMode !Debug \n");
                    c_setActionCb_(11, 0);
                }
                KeyNum = 0;
                break;
            case KeyValue::Key_e:
                if (KeyNum & 0xF1)
                {
                    printf("changeMode !Debug \n");
                    c_setActionCb_(11, 0);
                }
                KeyNum = 0;
                break;
            case KeyValue::Key_r:
                KeyNum = 0;
                if (c_driverPtr_)
                {
                    // 重播涉及时间重置，未开发
                    // c_driverPtr_->playStart();
                }
                break;
            case KeyValue::Key_R:
                KeyNum = 0;
                if (c_driverPtr_)
                {
                    // 重播涉及时间重置，未开发
                    // c_driverPtr_->playStart();
                }
                break;
            case KeyValue::Key_Minus: /* 减0.1倍速 */
                KeyNum = 0;
                if (c_driverPtr_)
                {
                    c_driverPtr_->playMinusSpeed();
                }
                break;
            case KeyValue::Key_Add: /* 加0.1倍速 */
                KeyNum = 0;
                if (c_driverPtr_)
                {
                    c_driverPtr_->playAddSpeed();
                }
                break;
            case KeyValue::Key_1: /* 1.0倍速 */
                KeyNum = 0;
                if (c_driverPtr_)
                {
                    c_driverPtr_->playOriSpeed();
                }
                break;
            case KeyValue::Key_h: /* 执行雷达标定 */
                KeyNum = 0;
                c_setActionCb_(12, 0);
                break;
            case KeyValue::Key_H: /* 执行雷达标定 */
                KeyNum = 0;
                c_setActionCb_(12, 0);
                break;
            case KeyValue::Key_V: /* 执行2D/3D显示 */
                KeyNum = 0;
                c_setActionCb_(18, 0);
                break;
            case KeyValue::Key_v: /* 执行2D/3D显示 */
                KeyNum = 0;
                c_setActionCb_(18, 0);
                break;
            case KeyValue::Key_P:
                KeyNum = 0;
                c_setActionCb_(20, 0);
                break;
            case KeyValue::Key_p:
                KeyNum = 0;
                c_setActionCb_(20, 0);
                break;
            case KeyValue::Key_W:
                KeyNum = 0;
                c_setActionCb_(21, 0);
                break;
            case KeyValue::Key_w:
                KeyNum = 0;
                c_setActionCb_(21, 0);
                break;
            case KeyValue::Key_O:
                KeyNum = 0;
                c_setActionCb_(22, 0);
                break;
            case KeyValue::Key_o:
                KeyNum = 0;
                c_setActionCb_(22, 0);
                break;
            case KeyValue::Key_Well: /* 工作模式入口 */ KeyNum = 0xf0; break;
            default: break;
        }
        c_bProcOver_ = true;
    }

  public:
    KeyBoard(bool& p_bIsStart,
             boost::shared_ptr<VirtualDriver>& p_virtualDriver,
             boost::function<int(int, int)> setCb)
        : c_driverPtr_(p_virtualDriver), c_setActionCb_(setCb), c_bIsStart(p_bIsStart)
    {
    }
    ~KeyBoard(){};

    void stop()
    {
        // 禁止等待当前键盘响应处理结束后将不允许再次处理
        c_bProvRun_ = false;
        while (1)
        {
            if (c_bProcOver_)
                break;
            usleep(500000);
        }
    }

    void resetDriverPtr(boost::shared_ptr<VirtualDriver>& p_virtualDriver)
    {
        stop();
        c_driverPtr_ = p_virtualDriver;
        c_bProvRun_ = true;
        c_bProcOver_ = true;
    }
    /**
     * @description: 创建线程监听并处理键盘事件
     * @param {*}
     * @return {*}
     * @other:
     */
    void startCtrl()
    {
        if (!c_bIsKeyOver_)
            return;
        c_bIsKeyOver_ = false;
        std::thread pcapCtrl(&KeyBoard::scanKeyboard_, this);
        pcapCtrl.detach();
    }
};
}  // namespace wj_slam