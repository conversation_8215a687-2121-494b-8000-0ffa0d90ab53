/*
 * @Description: 解析PCAP 通过网络重新发送
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2022-03-17 13:52:14
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-28 13:42:22
 */
#ifndef __INPUT_OFFLINE_H_
#define __INPUT_OFFLINE_H_

#include "common/common_master.h"
#include "ratelimiter.h"
#include "tool/fileTool/fileTool.h"
#include <arpa/inet.h>
#include <boost/array.hpp>
#include <boost/function.hpp>
#include <errno.h>
#include <fcntl.h>
#include <inttypes.h>
#include <linux/errqueue.h>
#include <linux/net_tstamp.h>
#include <linux/sockios.h>
#include <mutex>
#include <net/if.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <pcap.h>
#include <poll.h>
#include <signal.h>
#include <sstream>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <sys/file.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>
#include <vector>
using namespace std;
namespace wj_slam {

#pragma region 结构体
struct s_LIDAR_RAW_DATA_
{
    struct pcapHeader
    {
        u_int16_t m_header;
        u_int16_t m_headerLen;
        int m_iDataLen;
        sTimeval m_time;
    };
    pcapHeader m_pcapHeader;
    boost::array<uint8_t, 1600> m_data;
    s_LIDAR_RAW_DATA_()
    {
        m_pcapHeader.m_header = 0xbbff;
        m_pcapHeader.m_headerLen = sizeof(pcapHeader);
        m_pcapHeader.m_iDataLen = 0;
        m_pcapHeader.m_time.reset();
    }
};

struct s_pcapInfo
{
  public:
    s_pcapInfo()
    {
        m_sPcapPath = "";
        m_uiSrcPort = 2100;
        m_uiDstPort = 2101;
        m_sSrcIP = "127.0.0.1";
        m_sDstIP = "127.0.0.1";
        m_sProtocalMode = "TCP";
    }

    void printf(std::string head = "*")
    {
        std::cout << "*********** Pcap " << head << "***********" << std::endl;
        std::cout << "**  pcapFile     : " << m_sPcapPath.c_str() << "  **" << std::endl;
        std::cout << "**  ProtocalMode : " << m_sProtocalMode.c_str() << "  **" << std::endl;
        std::cout << "**  srcIp        : " << m_sSrcIP.c_str() << "  **" << std::endl;
        std::cout << "**  DstIp        : " << m_sDstIP.c_str() << "  **" << std::endl;
        std::cout << "**  SrcPort      : " << m_uiSrcPort << "  **" << std::endl;
        std::cout << "**  DstPort      : " << m_uiDstPort << "  **" << std::endl;
        std::cout << "**************************" << std::endl;
    }

    std::string m_sPcapPath;
    uint32_t m_uiSrcPort;
    uint32_t m_uiDstPort;
    std::string m_sSrcIP;
    std::string m_sDstIP;
    std::string m_sProtocalMode;  // 0x11[17] UDP  | 0x06[6] TCP
};

#pragma endregion

#pragma region 基类
class InputOffLine {
  public:
    InputOffLine(uint32_t p_uiDevId,
                 boost::shared_ptr<RateLimiterr> p_stRateL,
                 boost::function<void(uint32_t, boost::shared_ptr<s_LIDAR_RAW_DATA_>&)> p_outCb_);

    virtual ~InputOffLine();

    /**
     * @description: 生成PCAP路径
     * @param {string} p_sDataFilesPath PCAP路径
     * @param {string} p_sPcapName PCAP包名 不涵后缀
     * @return {std::string} 返回 PCAP完整路径 默认以pcap后缀 文件不存在则 使用pcapng后缀
     * 但不再判断文件是否存在
     * @other:
     */
    virtual std::string setPcapFile_(std::string p_sDataFilesPath, std::string p_sPcapName);

    /**
     * @description: 打开PCAP文件 分析内部IP 并过滤
     * @param {string} p_sDataFilePath PCAP文件路径
     * @return {*}
     * @other:
     */
    virtual bool pcapInit_(std::string p_sDataFilePath);

    virtual bool isValidPcap()
    {
        return c_bVaildPcap_;
    }
    /**
     * @description: 自动分析PCAP，从lidar/agv角度 获取网络配置，根据最小长度和lidar/agv ip 过滤PCAP
     * @param {*}
     * @return {*}
     * @other:
     */
    virtual bool pcapFilter() = 0;

    /**
     * @description: 逐包分析数据包 根据协议确定数据偏移值 确定是来自设备端的数据包(相对SLAM而言
     * agv/lidar为远端)之后确定雷达协议类型 及网络数据
     * @param {const u_char*} p_ucData PCAP的某包数据
     * @param {uint32_t} p_iLen 数据完整长度 涵盖-协议头
     * @param {s_pcapInfo&} p_stPcapInfo 待填充的PCAP信息
     * @return {bool} true：分析协议头 成功
     * @other:
     * 基于WLR720协议 不能单纯通过FFEE头确定 后续可能更改：双向交互都使用FFAA头 ，须引入数据长度
     */
    virtual bool analyPcapHeader(const u_char* p_ucData, uint32_t p_iLen, s_pcapInfo& p_stPcapInfo);

    /**
     * @function: setOffset_
     * @description: 根据协议判断FF EE 获取 雷达数据 起始偏移
     * @param {const u_char*} pcap读取数据
     * @param {uint32_t} p_iLen 包长度
     * @param {uint32_t& } p_uiOffset 待修改 数据起始字节
     * @return {*} true 起始偏移非0  即成功  仅设置1次
     * @others: 基于WLR720协议 不能单纯通过FFEE头确定
     * 后续可能更改：双向交互都使用FFAA头，须引入数据长度 c_uiPktDataMinLen
     */
    virtual bool setOffset_(const u_char* p_ucData, uint32_t p_iLen, uint32_t& p_uiOffset);

    /**
     * @description: 基于PCAP读取到的s_pcapInfo信息 初始化网络端口-ip配置
     * @param {s_pcapInfo&} p_stPcapInfo    基于PCAP包读取的网络配置
     * @param {sockaddr_in&} p_sMyAddr      待设置 设备网络配置 eg lidar/agv
     * @param {sockaddr_in&} p_sRemoteAddr  代设置 远端配置 SLAM
     * @return {*}
     * @other:
     */
    virtual void
    netInit_(s_pcapInfo& p_stPcapInfo, sockaddr_in& p_sMyAddr, sockaddr_in& p_sRemoteAddr);

    // /**
    //  * @description: 基于PCAP读取到的s_pcapInfo信息 初始化网络端口-ip配置
    //  * @param {wj_slam::s_DevCfg&} p_stDevCfg    设备配置信息
    //  * @param {sockaddr_in&} p_sMyAddr      待设置 设备网络配置 eg lidar/agv
    //  * @param {sockaddr_in&} p_sRemoteAddr  代设置 远端配置 SLAM
    //  * @return {*}
    //  * @other:
    //  */
    //  virtual void
    //  netInit_(wj_slam::s_DevCfg& p_stDevCfg, sockaddr_in& p_sMyAddr, sockaddr_in& p_sRemoteAddr);

    void netStartThr()
    {
        netStart_(c_sockfd_, c_myAddr_, c_remoteAddr_);
    }
    /**
     * @description: 网络初始化 建立套接字 绑定端口 默认720Lidar UDP服务器
     * @param {sockaddr_in&} p_sMyAddr
     * @param {sockaddr_in&} p_sRemoteAddr
     * @return {*}
     * @other:
     */
    virtual void netStart_(int& p_iFd, sockaddr_in& p_sMyAddr, sockaddr_in& p_sRemoteAddr);

    /**
     * @description: 连接TCP服务器 3s未连接则认为失败
     * @param {int&} p_iFd 网络句柄 须已建立socket
     * @param {sockaddr_in&} p_sRemoteAddr 服务器地址
     * @return {bool} TCP服务器连接成功
     * @other:
     */
    bool connectTcpServer_(int& p_iFd, sockaddr_in& p_sMyAddr, sockaddr_in& p_sRemoteAddr);

    /**
     * @description: UDP服务器启动后 始终接收信息
     * @param {*}
     * @return {*}
     * @other:
     */
    virtual void procRecv();

    /**
     * @description: 主循环 提取pcap数据 扔给回调
     * @param {*}
     * @return {*}
     * @other:
     */
    void run_();

    /**
     * @description: 发送数据 根据协议类型TCP/UDP选择不同发送
     * @param {boost::shared_ptr<s_LIDAR_RAW_DATA_>&} p_data_ 数据
     * @return {bool} 是否发送成功
     * @other:
     */
    bool sendMsg(boost::shared_ptr<s_LIDAR_RAW_DATA_>& p_data_);

    /**
     * @description: 获取网络状态 是否属于连接状态 UDP服务器默认连接 TCP客户端须检测是否连接服务器
     * @param {*}
     * @return {*}
     * @other:
     */
    bool getNetStatus();

    void setNetStatus(bool p_bStatus);

    virtual bool isStart();
    virtual void playStart()
    {
        c_bRun_ = true;
    };
    virtual void setTimeSync()
    {
        c_bWaitSetTIme_ = false;
    };
    virtual void shutdown();
    virtual bool isShutdown();

  protected:
    s_masterCfg* c_masterParamPtr = nullptr;
    bool c_bInitSucc_ = false;  // 初始化标志
    bool c_bConnectSucc = false;  // 是否连接 UDP:默认连接成功（服务器嘛） TCP：须验证连接成服务器
    bool c_bRun_ = false;  // 是否启动循环
    bool c_bVaildPcap_ = false;
    bool c_bWaitSetTIme_ = true;
    uint32_t c_iRunNum_ = 0;      // 线程控制符 一个thread ++
    uint32_t c_iRunOverNum_ = 0;  // 关闭一个thread --

    sockaddr_in c_myAddr_;
    sockaddr_in c_remoteAddr_;

    bool c_bIsOnlineMode;            // 驱动模式
    uint32_t c_uiId;                 // PCAP序号 用于time初始化
    uint32_t c_uiDataOffset = 0;     // 读取数据偏移
    uint32_t c_uiPktDataMinLen = 1;  // 每个包的最小数据长度  eg 720lidar为1260 sick 为 1
    boost::shared_ptr<RateLimiterr> c_stRL_;

#pragma region TCP客户端 / UDP服务器相关
    int c_sockfd_ = -1;
    std::string c_sLocalIP_;
    std::string c_sDevPrintfName_;
    struct tcp_info c_stConnectInfo_;
    int c_iConnectInfoLen_ = sizeof(struct tcp_info);
    std::mutex c_mutexNet_;
#pragma endregion

#pragma region PCAP相关
    s_pcapInfo c_stPcapInfo_;      // PCAP结构体
    std::mutex c_mutexPcap_;       // PCAP锁
    pcap_t* c_pacp_ = NULL;        // PCAP句柄
    bpf_program c_pcapPktFilter_;  // PCAP过滤器
    char errbuf_[PCAP_ERRBUF_SIZE];
#pragma endregion
    boost::function<void(uint32_t, boost::shared_ptr<s_LIDAR_RAW_DATA_>&)> c_outCb_;  // 输出回调
};
#pragma endregion

#pragma region 720Pcap类
class InputOffLine720 : public InputOffLine {
  public:
    InputOffLine720(
        uint32_t p_uiDevId,
        boost::shared_ptr<RateLimiterr> p_stRateL,
        boost::function<void(uint32_t, boost::shared_ptr<s_LIDAR_RAW_DATA_>&)> p_outCb_);

    virtual ~InputOffLine720();

    /**
     * @description: 自动分析PCAP，从lidar720 角度 获取网络配置，根据最小长度和lidar/agv ip 过滤PCAP
     * @param {*}
     * @return {*}
     * @other:
     */
    virtual bool pcapFilter();

  private:
#pragma region 变量
#pragma endregion

#pragma region 函数
#pragma endregion
};
#pragma endregion

#pragma region sickPcap类
class InputOffLineSick : public InputOffLine {
  public:
    InputOffLineSick(
        uint32_t p_uiDevId,
        boost::shared_ptr<RateLimiterr> p_stRateL,
        boost::function<void(uint32_t, boost::shared_ptr<s_LIDAR_RAW_DATA_>&)> p_outCb_);

    ~InputOffLineSick();

    /**
     * @description: 自动分析PCAP，从agv角度 获取网络配置，根据最小长度和lidar/agv ip 过滤PCAP
     * @param {*}
     * @return {*}
     * @other:
     */
    virtual bool pcapFilter();

    /**
     * @description: 逐包分析数据包 根据协议确定数据偏移值 之后确定雷达协议类型 及网络数据
     * @param {const u_char*} p_ucData PCAP的某包数据
     * @param {uint32_t} p_iLen 数据完整长度 涵盖-协议头
     * @param {s_pcapInfo&} p_stPcapInfo 待填充的PCAP信息
     * @return {bool}; true：分析协议头 成功
     * @other:
     * 基于SICK协议 以客户端角度 确定客户端配置ip port 和 服务器配置ip port
     * 未过滤时 包含TCP客户端和服务器端双向交互信息，须通过协议选择 客户端发的数据包 进行分析
     */
    virtual bool analyPcapHeader(const u_char* p_ucData, uint32_t p_iLen, s_pcapInfo& p_stPcapInfo);

    /**
     * @function: setOffset_
     * @description: 根据SICK协议判断 02 s 获取 雷达数据 起始偏移
     * @param {const u_char*} pcap读取数据
     * @param {uint32_t} p_iLen 包长度
     * @param {uint32_t& } p_uiOffset 待修改 数据起始字节
     * @return {*} true 起始偏移非0  即成功  仅设置1次
     * @others: null
     */
    virtual bool setOffset_(const u_char* p_ucData, uint32_t p_iLen, uint32_t& p_uiOffset);

    /**
     * @description: 初始化 TCP客户端 连接TCP服务器 并接受数据
     * @param {int&} p_iFd 句柄
     * @param {sockaddr_in&} p_sMyAddr TCP客户端地址
     * @param {sockaddr_in&} p_sRemoteAddr TCP服务器地址
     * @return {*}
     * @other:
     */
    virtual void netStart_(int& p_iFd, sockaddr_in& p_sMyAddr, sockaddr_in& p_sRemoteAddr);

    /**
     * @description: 判断TCP是否连接成功，接受数据
     * @param {*}
     * @return {*}
     * @other:
     */
    virtual void procRecv();

  private:
#pragma region 变量
#pragma endregion

#pragma region 函数
#pragma endregion
};
#pragma endregion

}  // namespace wj_slam

#endif