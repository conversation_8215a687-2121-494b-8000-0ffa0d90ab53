/*
 * @Description: 基于令牌桶的速率限制类，用于保证多雷达插入顺序的公平
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2022-03-02 13:25:58
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-10-14 14:03:00
 */
#ifndef __RATELIMITERR_H_
#define __RATELIMITERR_H_

#include <mutex>
#include <stdlib.h>
#include <unistd.h>
#include <vector>
class RateLimiterr {
  public:
    RateLimiterr(u_int32_t p_tokenSize) : c_uiTokenSize_(p_tokenSize), c_vFreeToken_(p_tokenSize)
    {
        c_bShutdown = false;
    }
    ~RateLimiterr() {}

    //必定成功获得令牌
    bool mustGetToken()
    {
        // 直接获取，失败则while等待
        if (tryGetToken())
            return true;
        while (!c_bShutdown)
        {
            if (tryGetToken())
                return true;
            else if (c_bShutdown)
                break;
            else
                usleep(50);
            // sleep(0);  //让位-释放一些未用的时间片给其他线程或进程使用
        }
        return false;
    }

    // 更新对应雷达令牌桶中的令牌
    void updateToken()
    {
        std::lock_guard<std::mutex> l_lock(c_Lock_);
        if (c_vFreeToken_ < c_uiTokenSize_)
            c_vFreeToken_++;
        else
            printf("Error %d - %d\n", c_vFreeToken_, c_uiTokenSize_);
    }

    uint32_t getTokenAllSize()
    {
        return c_uiTokenSize_;
    }

    uint32_t getTokenFreeSize()
    {
        return c_vFreeToken_;
    }

    uint32_t getTokenUseSize()
    {
        return c_uiTokenSize_ - c_vFreeToken_;
    }

    void reset()
    {
        std::lock_guard<std::mutex> l_lock(c_Lock_);
        c_vFreeToken_ = c_uiTokenSize_;
    }

    void shutdown()
    {
        c_bShutdown = true;
    }

  private:
    std::mutex c_Lock_;
    uint32_t c_uiTokenSize_;  // 令牌池内宗令牌数
    uint32_t c_vFreeToken_;   // 剩余令牌数
    bool c_bShutdown;         // 超级权限 必获取令牌 用于inputOffline推出
    // 尝试获取令牌
    bool tryGetToken()
    {
        std::lock_guard<std::mutex> l_lock(c_Lock_);
        if (c_vFreeToken_)
        {
            c_vFreeToken_--;
            // printf("available token %d\n", c_vFreeToken_);
            return true;
        }
        return false;
    }
};
#endif