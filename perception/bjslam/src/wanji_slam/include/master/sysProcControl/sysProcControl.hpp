/*
 * @Description: 此模块可启动/关闭外置程序- eg slam rosbag等
 * @Version: 1.0
 * @Autor:  <PERSON><PERSON><PERSON>
 * @Date: 2022-04-07 19:49:01
 * @LastEditors: wen <EMAIL>
 * @LastEditTime: 2022-06-01 17:20:08
 */
#include <string>
#include <unistd.h>
#include <vector>

namespace wj_slam {

enum PROCNAME { NOSET = 0, WANSLAM };

/*
程序运行状态
0 未知: 情况1：kill失败 pid仍存在 情况2: 句柄释放失败  情况3：启动后查询不到pid
1 关闭中
2 已关闭
3 启动中
4 已启动
*/
enum PROCSTATUS { UNKNOWN = 0, CLOSEING, CLOSED, STARTING, STARTED };

typedef struct st_ProcCfg
{
    st_ProcCfg()
    {
        m_sStartCMD = "";               // commond运行指令 rosnode必须使用rosrun pkgname xx
        m_sCloseCMD = "sudo kill -2 ";  // 发送ctrl+c信号
        m_sCheckPIDCMD = "";
        m_pid = 0;
        m_file = NULL;
        m_name = PROCNAME::NOSET;
        m_status = PROCSTATUS::UNKNOWN;
    }

    std::string m_sStartCMD;  // 启动指令
    std::string m_sCloseCMD;  // 关闭指令
    std::string m_sCheckPIDCMD;  // 查询pid关键词 注意：若存在相同关键词程序 容易误kill
    pid_t m_pid;                 // 程序首个pid
    FILE* m_file;                // 程序运行句柄
    PROCNAME m_name;             // 程序名字 不可重复
    PROCSTATUS m_status;  // 状态量: 1-关闭中 2-关闭 3-启动中 4-启动

} st_ProcCfg;

class SysProcControl {
  public:
#pragma region "公有变量"
#pragma endregion

#pragma region “公有函数”

    /**
     * @description: 启动程序 并通过pid确认启动
     * @param {PROCNAME} p_stProcName 程序名字
     * @return {*}
     * @other:
     * PROCSTATUS::UNKNOWN : 情况1：启动后查询不到pid
     */
    bool startProc(PROCNAME p_stProcName)
    {
        uint32_t l_uiProcId;
        if (!c_bIsRoot_ || !procNameToId_(p_stProcName, l_uiProcId))
        {
            printf("[error] startProc cancil: isRoot[%d]\n", c_bIsRoot_);
            return false;
        }

        // 已存在则先关闭
        if (!stopProc(p_stProcName))
        {
            printf("[error] startProc exit fail\n");
            return false;
        }

        c_vtProcList_[l_uiProcId].m_status = PROCSTATUS::STARTING;
        // 启动程序后等待10s 查询pid
        if (popenProc_(c_vtProcList_[l_uiProcId].m_sStartCMD, c_vtProcList_[l_uiProcId].m_file))
        {
            int l_iTryNum = 10;
            while (l_iTryNum--)
            {
                c_vtProcList_[l_uiProcId].m_pid =
                    getProPidByName_(c_vtProcList_[l_uiProcId].m_sCheckPIDCMD);
                printf("startProc getPid = %d \n", c_vtProcList_[l_uiProcId].m_pid);
                if (c_vtProcList_[l_uiProcId].m_pid)
                {
                    c_vtProcList_[l_uiProcId].m_status = PROCSTATUS::STARTED;
                    printf("proc[%s] start succ\n", c_vtProcList_[l_uiProcId].m_sStartCMD.c_str());
                    return true;
                }
                sleep(1);
            }
            printf("proc[%s] start fail\n", c_vtProcList_[l_uiProcId].m_sStartCMD.c_str());
            c_vtProcList_[l_uiProcId].m_status = PROCSTATUS::UNKNOWN;
            return false;
        }
        else
        {
            // 未成功启动
            printf("proc[%s] start error\n", c_vtProcList_[l_uiProcId].m_sStartCMD.c_str());
            c_vtProcList_[l_uiProcId].m_status = PROCSTATUS::CLOSED;
            return false;
        }
    }

    /**
     * @description: 关闭程序 确保pid不存在
     * @param {PROCNAME} p_stProcName 程序名字
     * @return {*}
     * @other:
     * PROCSTATUS::UNKNOWN : 情况1：kill失败 pid仍存在 情况2: 句柄释放失败
     */
    bool stopProc(PROCNAME p_stProcName)
    {
        uint32_t l_uiProcId;
        if (!c_bIsRoot_ || !procNameToId_(p_stProcName, l_uiProcId))
        {
            printf("[error] stoproc cancil: isRoot[%d]\n", c_bIsRoot_);
            return false;
        }

        c_vtProcList_[l_uiProcId].m_pid =
            getProPidByName_(c_vtProcList_[l_uiProcId].m_sCheckPIDCMD);
        if (c_vtProcList_[l_uiProcId].m_pid)
        {
            c_vtProcList_[l_uiProcId].m_status = PROCSTATUS::CLOSEING;
            int l_iTryNum = 10;
            while (l_iTryNum--)
            {
                forceKillProc_(c_vtProcList_[l_uiProcId].m_sCloseCMD
                               + std::to_string(c_vtProcList_[l_uiProcId].m_pid));
                sleep(1);
                c_vtProcList_[l_uiProcId].m_pid =
                    getProPidByName_(c_vtProcList_[l_uiProcId].m_sCheckPIDCMD);
                if (!c_vtProcList_[l_uiProcId].m_pid)
                    break;
            }
        }

        // 查询不到pid, 可以认为程序已关闭，仍须释放句柄
        if (!c_vtProcList_[l_uiProcId].m_pid)
        {
            // 释放句柄失败 则认为程序状态：未知
            if (pcloseProc_(c_vtProcList_[l_uiProcId].m_file))
            {
                c_vtProcList_[l_uiProcId].m_status = PROCSTATUS::CLOSED;
                return true;
            }
        }
        printf("proc[%s] stop fail\n", c_vtProcList_[l_uiProcId].m_sStartCMD.c_str());
        c_vtProcList_[l_uiProcId].m_status = PROCSTATUS::UNKNOWN;
        return false;
    }

    /**
     * @description: 关闭程序 确保pid不存在
     * @param {PROCNAME} p_stProcName 程序名字
     * @return {*}
     * @other:
     */
    bool resetProc(PROCNAME p_stProcName)
    {
        if (stopProc(p_stProcName))
            return startProc(p_stProcName);
        return false;
    }

    int checkProcStatus(PROCNAME p_stProcName)
    {
        uint32_t l_uiProcId;
        if (!procNameToId_(p_stProcName, l_uiProcId))
        {
            printf("[error] checkProcStatus quit\n");
            return PROCSTATUS::UNKNOWN;
        }
        if (c_vtProcList_[l_uiProcId].m_status == PROCSTATUS::UNKNOWN)
        {
            c_vtProcList_[l_uiProcId].m_pid =
                getProPidByName_(c_vtProcList_[l_uiProcId].m_sCheckPIDCMD);
            printf("check pid %d\n", c_vtProcList_[l_uiProcId].m_pid);
            if (c_vtProcList_[l_uiProcId].m_pid)
                return PROCSTATUS::STARTED;
            else
                return PROCSTATUS::CLOSED;
        }
        else
            return c_vtProcList_[l_uiProcId].m_status;
    }

    /**
     * @description: 增加启动程序配置  c_vtProcList_中不可有相同程序名枚举量
     * @param {st_ProcCfg&} p_stProcCfg 程序配置
     * @return {true} false: PROCNAME 未设置或重复
     * @other:
     */
    bool addProcCfg(st_ProcCfg& p_stProcCfg)
    {
        if (p_stProcCfg.m_name == PROCNAME::NOSET)
        {
            printf("addProcCfg fail: PROCNAME noe set\n");
            return false;
        }
        for (uint32_t i = 0; i < c_vtProcList_.size(); i++)
        {
            if (c_vtProcList_[i].m_name == p_stProcCfg.m_name)
            {
                printf("addProcCfg fail: PROCNAME repeat\n");
                return false;
            }
        }
        c_vtProcList_.push_back(p_stProcCfg);
        return true;
    }

#pragma endregion

  private:
#pragma region "私有变量"
    std::vector<st_ProcCfg> c_vtProcList_;
    bool c_bIsRoot_ = false;  //启动关闭程序须超级权限
#pragma endregion

#pragma region “私有函数”

    void init_()
    {
        // 须确保ROOT权限
        if (geteuid() == 0)
            c_bIsRoot_ = true;

        st_ProcCfg l_slamProc;
        l_slamProc.m_sStartCMD = "rosrun wj_slam wanjislam";
        l_slamProc.m_sCheckPIDCMD = "wanjislam";
        l_slamProc.m_name = PROCNAME::WANSLAM;

        addProcCfg(l_slamProc);
    }

    /**
     * @description: 根据关键字 获取首个pid
     * @param {string} p_procName
     * @return {*}
     * @other:
     */
    pid_t getProPidByName_(std::string p_procName)
    {
        FILE* fp;
        char buf[100];
        char cmd[200] = {'\0'};
        pid_t pid = 0;
        sprintf(cmd, "pidof %s", p_procName.c_str());

        if ((fp = popen(cmd, "r")) != NULL)
        {
            if ((fgets(buf, sizeof(buf), fp)) != NULL)
                pid = atoi(buf);
        }

        pclose(fp);
        printf("getProPidByName: %d\n", pid);
        return pid;
    }

    /**
     * @description: 执行CMD指令，启动程序
     * @param {const char *} p_cCmd 指令
     * @param {FILE*} &p_fileOut 文件句柄，可用于打印程序的消息
     * @return {Bool} 成功/失败
     */
    bool popenProc_(std::string p_sCMD, FILE*& p_fileOut)
    {
        if (p_sCMD == "")
        {
            printf("[error] openProc CMD is empty\n");
            return false;
        }

        if (p_fileOut != NULL)
        {
            printf("[error] openProc File\n");
            return false;
        }

        if ((p_fileOut = popen(p_sCMD.c_str(), "r")) == NULL)
        {
            return false;
        }
        return true;
    }

    /**
     * @description: 关闭CMD指令打开的程序， 在此之前须
     * @param {FILE*} &p_fileOut
     * @return {Bool} 成功/失败
     */
    bool pcloseProc_(FILE*& p_fileOut)
    {
        if (p_fileOut == NULL)
            return true;

        int l_nRes;
        if ((l_nRes = pclose(p_fileOut)) == -1)
            return false;
        p_fileOut = NULL;
        printf("close proc succ\n");
        return true;
    }

    /**
     * @description: 通过CMD强制Kill程序
     * @param {string} p_sCmd
     * @return {*}
     */
    bool forceKillProc_(std::string p_sCmd)
    {
        if (system(p_sCmd.data()) == -1)
        {
            std::cout << "fail kill: " << p_sCmd.data() << std::endl;
            return false;
        }
        return true;
    }

    /**
     * @description: 程序名枚举量转id 要求唯一性质 c_vtProcList_中不可有相同程序名枚举量
     * @param {string} p_sCmd
     * @return {*}
     */
    bool procNameToId_(PROCNAME p_stProcName, uint32_t& p_uiId)
    {
        for (uint32_t i = 0; i < c_vtProcList_.size(); i++)
        {
            if (c_vtProcList_[i].m_name == p_stProcName)
            {
                p_uiId = i;
                return true;
            }
        }
        return false;
    }

#pragma endregion

  public:
    void shutdown()
    {
        printf("SysProcControl shutdown\n");
        for (uint32_t i = 0; i < c_vtProcList_.size(); i++)
            stopProc(c_vtProcList_[i].m_name);
        printf("SysProcControl shutdown over\n");
    }

    SysProcControl()
    {
        init_();
    }
    ~SysProcControl()
    {
        shutdown();
    }
};
}  // namespace wj_slam