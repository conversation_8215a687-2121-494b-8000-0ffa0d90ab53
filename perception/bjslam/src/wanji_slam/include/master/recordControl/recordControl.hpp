/*
 * @Description: 控制录包-返回录包容量-
 * @Version: 1.0
 * @Autor:  <PERSON><PERSON><PERSON>
 * @Date: 2022-04-07 17:38:21
 * @LastEditors: wen <EMAIL>
 * @LastEditTime: 2023-04-12 13:18:16
 */
#pragma once

#include "common/common_master.h"
#include "master/recordControl/pcapHandle/pcapHandle.h"

namespace wj_slam {
class RecordControl {
  public:
    typedef typename PcapHandle::Ptr PcapHandle_Ptr;

    /**
     * @description: 控制播包启动/暂停
     * @param {bool} isStart 是否启动
     * @return {bool} 启动/关闭是否执行成功
     * @other:
     */
    bool recordPcap(bool p_bIsRecord)
    {
        // printf("recordPcap: %d\n",p_bIsRecord );

        if (geteuid() != 0)
        {
            LOGFAE(WERROR, "录制PCAP失败 | 请以root权限启动此程序!");
            c_masterParamPtr->m_slam->m_fae.setErrorCode("G1");
            return false;
        }

        // 如果启动 则先确认 初始化完成
        if (p_bIsRecord)
        {
            // 以雷达网卡为标志，这个为空 则reset全部
            if (c_vPcapHandlePtr.size())
            {
                bool l_bInitOver = false;
                for (size_t i = 0; i < c_vPcapHandlePtr.size(); i++)
                {
                    if (c_vPcapHandlePtr[i])
                        l_bInitOver = true;
                }
                if (!l_bInitOver)
                    initPcap_();
            }
        }

        // 启动录制 -更新变量：是否录制中 有一个录制则为录制状态
        bool l_bIsStart = false;
        for (size_t i = 0; i < c_vPcapHandlePtr.size(); i++)
        {
            // 有1个正常启动录制即属于录制中
            bool l_bTemp = false;
            if (c_vPcapHandlePtr[i])
                l_bIsStart = c_vPcapHandlePtr[i]->isStart(p_bIsRecord) || l_bIsStart;
        }
        c_bRecordStatus_ = l_bIsStart;
        // 更新录制时间
        c_stRecordStartTime_ = setRecordStartTime_(c_bRecordStatus_);

        // 返回录制/关闭的执行状态
        if (c_bRecordStatus_ == p_bIsRecord)
            return true;
        else
            return false;
    }

    /**
     * @description: 获取录制时长 单位Sec
     * @param {}
     * @return {*}
     * @other:
     */
    int getRecordTime()
    {
        // 未录制时复位录制起始时间
        if (!c_bRecordStatus_)
        {
            c_stRecordStartTime_.reset();
        }

        // 录制起始时间为0时意味着未录制
        if (!c_stRecordStartTime_.second())
            return 0;
        sTimeval now(0);
        return (now.getDiffMs(c_stRecordStartTime_) / 1000);
    }

    /**
     * @description: 获取录制时间
     * @param {}
     * @return {*}
     * @other:
     */
    int getRecordStatus()
    {
        return (int)c_bRecordStatus_;
    }
    /**
     * @description: 停止保存录制且关闭该类
     * @param {}
     * @return {*}
     * @other:
     */
    void shutdown()
    {
        for (size_t i = 0; i < c_vPcapHandlePtr.size(); i++)
        {
            if (c_vPcapHandlePtr[i])
                c_vPcapHandlePtr[i]->breakPcap(SIGINT);
        }

        for (size_t i = 0; i < c_vPcapHandlePtr.size(); i++)
        {
            if (c_vPcapHandlePtr[i])
                c_vPcapHandlePtr[i] = nullptr;
        }
    }

  private:
    s_masterCfg* c_masterParamPtr = nullptr;
    std::vector<PcapHandle_Ptr> c_vPcapHandlePtr;
    sTimeval c_stRecordStartTime_; /*启动录制时间*/
    bool c_bRecordStatus_ = false; /*是否启动录制*/

    /**
     * @description: 参数初始化
     * @param {}
     * @return {*}
     * @other:
     */
    void init_()
    {
        c_masterParamPtr = s_masterCfg::getIn();
        initPcap_();
        recordPcap(c_masterParamPtr->m_bDefaultSavePcap);
        c_bRecordStatus_ = getRecordStatus();
    }

    /**
     * @description: PCAP类初始化
     * @param {}
     * @return {*}
     * @other:
     */
    void initPcap_()
    {
        c_vPcapHandlePtr.resize(c_masterParamPtr->m_slam->m_devList.size());
        for (uint32_t l_iDevId = 0; l_iDevId < c_masterParamPtr->m_slam->m_devList.size();
             l_iDevId++)
        {
            // printf("dev[%d] netName : %s\n",
            // l_iDevId,c_masterParamPtr->m_slam->m_devList[l_iDevId]->m_sNetName.c_str());
            if (c_masterParamPtr->m_slam->m_devList[l_iDevId]->m_sNetName == "")
            {
                continue;
            }
            if (c_masterParamPtr->m_slam->m_devList[l_iDevId]->m_sDevType == "WheelSick")
                c_vPcapHandlePtr[l_iDevId].reset(new PcapHandle(
                    c_masterParamPtr->m_slam->m_devList[l_iDevId]->m_sNetName,
                    c_masterParamPtr->m_sOffLineDataPath + "agv",
                    "tcp && port "
                        + std::to_string(
                              c_masterParamPtr->m_slam->m_devList[l_iDevId]->m_uiLocalPort),
                    c_masterParamPtr->m_uiRecordTimeInterval,
                    c_masterParamPtr->m_uiRecordAGVMaxNum,
                    500));
            else
            {
                // printf("dev[%d] ip : %s\n",
                // l_iDevId,c_masterParamPtr->m_slam->m_devList[l_iDevId]->m_sDevIP.c_str());
                std::string l_sFilter = "not tcp && src host "
                                        + c_masterParamPtr->m_slam->m_devList[l_iDevId]->m_sDevIP;
                c_vPcapHandlePtr[l_iDevId].reset(
                    new PcapHandle(c_masterParamPtr->m_slam->m_devList[l_iDevId]->m_sNetName,
                                   c_masterParamPtr->m_sOffLineDataPath + "lidar_"
                                       + c_masterParamPtr->m_slam->m_lidar[l_iDevId].m_sLaserName,
                                   l_sFilter,
                                   c_masterParamPtr->m_uiRecordTimeInterval,
                                   c_masterParamPtr->m_uiRecordMaxNum,
                                   500));
            }
        }
    }

    /**
     * @description: 设置录制起始时间
     * @param {bool} p_bIsRecord 是否启动录制
     * @return {*}
     * @other:
     */
    sTimeval setRecordStartTime_(bool p_bIsRecord)
    {
        sTimeval l_stTime;
        if (p_bIsRecord)
            l_stTime.now();
        return l_stTime;
    }

  public:
    RecordControl()
    {
        init_();
    }

    ~RecordControl()
    {
        for (size_t i = 0; i < c_vPcapHandlePtr.size(); i++)
        {
            if (c_vPcapHandlePtr[i])
                c_vPcapHandlePtr[i] = nullptr;
        }
    }
};
}  // namespace wj_slam
