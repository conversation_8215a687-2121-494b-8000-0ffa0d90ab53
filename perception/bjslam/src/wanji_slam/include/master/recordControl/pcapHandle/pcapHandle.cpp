#include "./pcapHandle.h"

struct pcap_timeval
{
    bpf_int32 tv_sec;  /* seconds */
    bpf_int32 tv_usec; /* microseconds */
};

struct pcap_sf_pkthdr
{
    struct pcap_timeval ts; /* time stamp */
    bpf_u_int32 caplen;     /* length of portion present */
    bpf_u_int32 len;        /* length of this packet (off wire) */
};

PcapHandle::PcapHandle(std::string p_sNetcardName,
                       std::string p_pSavePath,
                       std::string p_sFilterRule,
                       int p_pTimeIntervalSec,
                       int p_iMaxPcapNum,
                       int p_pPcapTimeOut,
                       MODE p_Mode)
    : c_mode(p_Mode), c_sNetCard_(p_sNetcardName), c_sFilterRule_(p_sFilterRule),
      c_sDataDir_(p_pSavePath), c_iTimeInterval_(p_pTimeIntervalSec),
      c_iPcapTimeOut(p_pPcapTimeOut), c_iMaxPcapNum_(p_iMaxPcapNum)
{
    // 检查路径
    if (c_sDataDir_[c_sDataDir_.length() - 1] != '/')
        c_sDataDir_ += "/";
    // 获取网卡名
    c_pNetCard_ = const_cast<char*>(c_sNetCard_.c_str());
    // 开启自动删除包
    c_pcapControl_.reset(new PcapManage(c_sDataDir_, p_iMaxPcapNum));
    // 初始化
    init();
    if (c_mode == MODE::WRITE)
    {
        std::thread l_tt = std::thread(&PcapHandle::recordRun_, this);
        l_tt.detach();
    }
}

PcapHandle::~PcapHandle()
{
    // 停止录制
    c_pcapControl_ = nullptr;
}

/**
 * @description: 初始化
 * @return {*}
 */
void PcapHandle::init()
{
    if (c_mode == MODE::WRITE)
    {
        c_iResPcap_ = 0;
        c_bIsOver_ = false;
        c_bRuning = true;
        c_bStartPcap_ = false;
        c_bIsSaveOver_ = true;
        c_iPingPang_ = PING;
    }
}

/**
 * @description: 设置抓包过滤规则
 * @param {std::string} p_sFilterRule 过滤规则
 * @return {*}
 */
void PcapHandle::setfilter(std::string p_sFilterRule)
{
    //滤波
    struct bpf_program l_stFilter;
    int l_iRes = pcap_compile(c_pDevice_,
                              &l_stFilter,
                              p_sFilterRule.c_str(),
                              1,
                              0);  ////"src host ************ and not tcp"
    pcap_setfilter(c_pDevice_, &l_stFilter);
}

/**
 * @description: 设置网卡
 * @param {char * } p_pNetCard 网卡名称
 * @return {*}
 */
bool PcapHandle::setNetCard(char* p_pNetCard)
{
    //获取网口  只获取一次
    printf("Start PCAP Ping %s\n", p_pNetCard);
    char l_cErrBuf[PCAP_ERRBUF_SIZE];

    c_pDevice_ = pcap_open_live(p_pNetCard, 65535, 1, c_iPcapTimeOut, l_cErrBuf);
    if (!c_pDevice_)
    {
        // 设定的网口无法打开 自动获取网口
        c_pNetCard_ = pcap_lookupdev(l_cErrBuf);
        printf("Set lidar_dev error, auto open lidarNetCard: %s\n", c_pNetCard_);
        if (!c_pNetCard_)
        {
            printf("No Find NetWord\n");
            return false;
        }
        c_pDevice_ = pcap_open_live(c_pNetCard_, 65535, 1, c_iPcapTimeOut, l_cErrBuf);
        if (!c_pDevice_)
        {
            printf("Workcard open fail\n");
            return false;
        }
    }

    pcap_setnonblock(c_pDevice_, 1, l_cErrBuf);
    return true;
}

/**
 * @description: c_bRuning为true，程序启动，等待启动抓包
 * @return {*}
 */
void PcapHandle::recordRun_()
{
    c_bIsOver_ = false;
    while (c_bRuning)
    {
        if (c_bStartPcap_)
        {
            doCaptureSave_();
            breakPcap(0);
        }
        // else if(c_vPktdataPing_.size()!=0||c_vPktdataPing_.size()!=0)
        // {
        //     breakPcap(0);
        // }
        usleep(1000);
    }
    c_bIsOver_ = true;
}

/**
 * @description: 执行pcap抓包，满足时间间隔保存
 * @return {*}
 */
void PcapHandle::doCaptureSave_()
{
    c_LastTime_ = system_clock::now();
    while (c_bStartPcap_ && c_bRuning)
    {
        captureData_();
        c_CurTime_ = system_clock::now();
        int l_iTimediff = duration_cast<seconds>(c_CurTime_ - c_LastTime_).count();
        if (l_iTimediff > c_iTimeInterval_)
        {
            std::thread l_tSaveData = std::thread(&PcapHandle::saveData_, this, c_iPingPang_);
            l_tSaveData.detach();
            if (PING == c_iPingPang_)
            {
                c_iPingPang_ = PANG;
            }
            else
            {
                c_iPingPang_ = PING;
            }
            c_LastTime_ = system_clock::now();
        }
        usleep(100);
    }
}

/**
 * @description: 将抓包数据存入vec
 * @return {*}
 */
void PcapHandle::captureData_()
{
    struct pcap_pkthdr* l_stPktHeader;
    const u_char* l_pPktData;
    c_iResPcap_ = pcap_next_ex(c_pDevice_, &l_stPktHeader, &l_pPktData);  //等待抓包
    //返回值: 1: 成功   0: 获取报文超时  -1: 发生错误   -2: 获取到离线记录文件的最后一个报文
    if (0 == c_iResPcap_)
    {
        // printf("获取报文超时\n");
        return;
    }
    else if (-1 == c_iResPcap_)
    {
        printf("获取报文发生错误\n");
        return;
    }
    else if (1 == c_iResPcap_)  //&& pktHeader->caplen==1302
    {
        struct pcap_sf_pkthdr sf_hdr;
        sf_hdr.ts.tv_sec = (bpf_int32)l_stPktHeader->ts.tv_sec;
        sf_hdr.ts.tv_usec = (bpf_int32)l_stPktHeader->ts.tv_usec;
        sf_hdr.caplen = l_stPktHeader->caplen;
        sf_hdr.len = l_stPktHeader->len;
        int len = sizeof(pcap_sf_pkthdr);

        char temp_head[len];
        memcpy(temp_head, &sf_hdr, len);
        std::vector<u_char> l_vPktHeader(temp_head, temp_head + len);
        std::vector<u_char> l_vPktData;
        l_vPktData.insert(l_vPktData.end(), l_pPktData, l_pPktData + l_stPktHeader->caplen);
        if (PING == c_iPingPang_)
        {
            c_vPktdataPing_.insert(c_vPktdataPing_.end(), l_vPktHeader.begin(), l_vPktHeader.end());
            c_vPktdataPing_.insert(c_vPktdataPing_.end(), l_vPktData.begin(), l_vPktData.end());
        }
        else
        {
            c_vPktdataPang_.insert(c_vPktdataPang_.end(), l_vPktHeader.begin(), l_vPktHeader.end());
            c_vPktdataPang_.insert(c_vPktdataPang_.end(), l_vPktData.begin(), l_vPktData.end());
        }
        std::vector<u_char>().swap(l_vPktHeader);
        std::vector<u_char>().swap(l_vPktData);
    }
}

/**
 * @description: ping、pang交互批量保存数据
 * @param {char * } p_iPingPang PING\PANG
 * @return {*}
 */
void PcapHandle::saveData_(int p_iPingPang)
{
    c_bIsSaveOver_ = false;

    FILE* f;
    struct pcap_file_header hdr;
    hdr.magic = 0xa1b2c3d4;  // 0xa1b2c3d4精确到较低
    hdr.version_major = PCAP_VERSION_MAJOR;
    hdr.version_minor = PCAP_VERSION_MINOR;
    hdr.thiszone = 0;
    hdr.sigfigs = 0;
    hdr.snaplen = 65535;
    hdr.linktype = 1;

    if (PING == p_iPingPang)
    {
        if (!c_vPktdataPing_.size())
        {
            c_bIsSaveOver_ = true;
            return;
        }
        //路径更新
        std::string l_str = makePcapPath_(c_sDataDir_);
        if (!makeDir(l_str))
        {
            usleep(10000);
            if (!makeDir(l_str))
            {
                printf("pcapFolder: %s create fail， cancel save\n", l_str.c_str());
                std::vector<u_char>().swap(c_vPktdataPing_);
                c_bIsSaveOver_ = true;
                return;
            }
        }
        c_sPcapPathPing_ = c_sPcapPath_;
        // std::ofstream l_FileWrite(c_sPcapPathPing_,
        //                           std::ios::out | std::ios::app | std::ios::binary);
        // //写入本地
        // l_FileWrite.write((char*)&c_vPktdataPing_[0], c_vPktdataPing_.size() * sizeof(u_char));
        // printf("save file: %s\n", c_sPcapPathPing_.c_str());
        // std::vector<u_char>().swap(c_vPktdataPing_);
        // l_FileWrite.close();

        if ((f = fopen(c_sPcapPathPing_.c_str(), "wb")) == NULL)
        {
            printf("save file: %s | open fail\n", c_sPcapPathPing_.c_str());
            std::vector<u_char>().swap(c_vPktdataPing_);
            c_bIsSaveOver_ = true;
            return;
        }

        fwrite((char*)&hdr, sizeof(hdr), 1, f);
        fwrite((char*)&c_vPktdataPing_[0], c_vPktdataPing_.size() * sizeof(char), 1, f);
        fclose(f);
        // printf("save file: %s\n", c_sPcapPathPing_.c_str());
        c_pcapControl_->deleteOldPcap(c_sPcapPathPing_);
        std::vector<u_char>().swap(c_vPktdataPing_);
    }
    else
    {
        if (!c_vPktdataPang_.size())
        {
            c_bIsSaveOver_ = true;
            return;
        }

        //路径更新
        std::string l_str = makePcapPath_(c_sDataDir_);
        if (!makeDir(l_str))
        {
            usleep(10000);
            if (!makeDir(l_str))
            {
                printf("pcapFolder: %s create fail， cancel save\n", l_str.c_str());
                std::vector<u_char>().swap(c_vPktdataPing_);
                c_bIsSaveOver_ = true;
                return;
            }
        }
        c_sPcapPathPang_ = c_sPcapPath_;
        // std::ofstream l_FileWrite(c_sPcapPathPang_,
        //                           std::ios::out | std::ios::app | std::ios::binary);
        // if (l_FileWrite.is_open())
        // {
        //     //写入本地
        //     l_FileWrite.write((char*)&c_vPktdataPang_[0], c_vPktdataPang_.size() *
        //     sizeof(u_char)); printf("save file: %s\n", c_sPcapPathPing_.c_str());
        //     l_FileWrite.close();
        // }
        // else
        //     printf("save file: %s | open fail\n", c_sPcapPathPing_.c_str());
        // std::vector<u_char>().swap(c_vPktdataPang_);

        if ((f = fopen(c_sPcapPathPang_.c_str(), "wb")) == NULL)
        {
            printf("save file: %s | open fail\n", c_sPcapPathPang_.c_str());
            std::vector<u_char>().swap(c_vPktdataPang_);
            c_bIsSaveOver_ = true;
            return;
        }
        fwrite((char*)&hdr, sizeof(hdr), 1, f);
        fwrite((char*)&c_vPktdataPang_[0], c_vPktdataPang_.size() * sizeof(char), 1, f);
        fclose(f);
        // printf("save file: %s\n", c_sPcapPathPing_.c_str());
        c_pcapControl_->deleteOldPcap(c_sPcapPathPang_);
        std::vector<u_char>().swap(c_vPktdataPang_);
    }
    c_bIsSaveOver_ = true;
}

/**
 * @description: 保存缓存数据，关闭网卡
 * @param {int } p_iSignum为2表示ctrl + c触发的保存  p_iSignum为0表示暂停触发的保存
 * @return {*}
 */

void PcapHandle::breakPcap(int p_iSignum)
{
    if (2 == p_iSignum || 15 == p_iSignum)
    {
        c_bRuning = false;
        printf("wait pcap record quit\n");
        while (!c_bIsOver_)  //等待循环结束
        {
            usleep(100);
        }
        printf("succ pcap record quit\n");
    }

    while (!c_bIsSaveOver_)  //等待保存结束
    {
        usleep(100);
    }
    if (c_vPktdataPing_.size())
    {
        saveData_(PING);
    }

    if (c_vPktdataPang_.size())
    {
        saveData_(PANG);
    }

    if (c_pDevice_)
    {
        pcap_close(c_pDevice_);
        c_pDevice_ = NULL;
    }
}

/**
 * @description: 生成pcap 保存文件夹路径 生成pcap保存路径
 * @param {string} p_sFolderPath 路径头
 * @param {string} p_sPcapPath 填充pcap绝对路径
 * /media/wjdata/年-月-日/时-分妙.pcapng
 * @return {string} 返回 文件夹名 /media/wjdata/年-月-日
 */
std::string PcapHandle::makePcapPath_(std::string p_sFolderPath)
{
    std::string l_str;
    // 基于当前系统的当前日期/时间
    time_t now = time(0);
    tm* ltm = localtime(&now);
    std::string l_myear = std::to_string(1900 + ltm->tm_year);
    std::string l_mon = std::to_string(1 + ltm->tm_mon);
    if (1 + ltm->tm_mon < 10)
        l_mon = "0" + l_mon;
    std::string l_mday = std::to_string(ltm->tm_mday);
    if (ltm->tm_mday < 10)
        l_mday = "0" + l_mday;
    std::string l_mhour = std::to_string(ltm->tm_hour);
    if (ltm->tm_hour < 10)
        l_mhour = "0" + l_mhour;
    std::string l_mMin = std::to_string(ltm->tm_min);
    if (ltm->tm_min < 10)
        l_mMin = "0" + l_mMin;
    std::string l_mSec = std::to_string(ltm->tm_sec);
    if (ltm->tm_sec < 10)
        l_mSec = "0" + l_mSec;
    l_str = p_sFolderPath;
    c_sPcapPath_ = l_str + l_myear + l_mon + l_mday + l_mhour + l_mMin + l_mSec + ".pcapng";
    return l_str;
}

/**
 * @description: 是否存在目录 否则创建
 * @param {string} p_sFileDir 文件夹路径
 * @return {*}
 */
bool PcapHandle::createfolder_(std::string p_sFileDir)
{
    DIR* dp = NULL;
    bool l_bRes = false;
    //目录不存在
    if ((dp = opendir(p_sFileDir.c_str())) == NULL)
    {
        // 创建成功返回0
        int isCreate = mkdir(p_sFileDir.c_str(), S_IRUSR | S_IWUSR | S_IXUSR | S_IRWXG | S_IRWXO);
        if (!isCreate)
            l_bRes = true;
        else
            printf("file:< %s > mkdir fail | error: %s\n", p_sFileDir.c_str(), strerror(errno));
    }
    else
    {
        closedir(dp);
        l_bRes = true;
    }
    return l_bRes;
}