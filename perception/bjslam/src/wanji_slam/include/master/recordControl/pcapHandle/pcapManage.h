#pragma once
#include <boost/filesystem.hpp>
#include <boost/regex.hpp>
#include <boost/system/system_error.hpp>
#include <cstdio>  //C++
#include <iostream>
#include <queue>
#include <string>
#include <utility>
#include <vector>
using namespace std;
// using namespace boost::filesystem;

class PcapManage {
    /**
     * @class
     * @brief 遍历文件夹下pcap文件，文件夹内超过10个pcap文件，则保留最新10个。
     * <AUTHOR>
     */
  public:
    /**
     * @brief 创建一个pcap文件管理器
     *
     * @param {std::string} p_p pcap文件路径
     * @param {int} p_iNum  最多保留的pcap文件数目
     *
     */
    PcapManage(std::string p_p, int p_iNum)
    {
        c_vsPcapNameList_.clear();
        c_iPcapMaxNum_ = p_iNum;
        std::string p_sPath = p_p;
        if (p_sPath[p_sPath.length() - 1] != '/')
            p_sPath += "/";
        c_pDataPath_ = boost::filesystem::path(p_sPath);
        funBrowseFile_(c_vsPcapNameList_, c_pDataPath_, c_iPcapMaxNum_);
    }

    bool isFileExistent(const boost::filesystem::path& p_path)
    {
        boost::system::error_code error;
        return boost::filesystem::is_regular_file(p_path, error);
    }

    /**
     * @brief 添加新的pcap文件 删除旧的
     * @param {std::string} p_sFile pcap完整路径
     * @return [true] [添加成功]
     * @return [false][添加失败]
     */
    bool deleteOldPcap(std::string p_sPcapPath)
    {
        // 从完整路径中获取PCAP名
        boost::filesystem::path l_pPcapPath(p_sPcapPath);
        std::string l_sPcapName = l_pPcapPath.filename().string();
        c_vsPcapNameList_.push_back(l_sPcapName);
        if ((int)c_vsPcapNameList_.size() > c_iPcapMaxNum_)
        {
            std::string l_file_path = c_pDataPath_.string() + c_vsPcapNameList_.front();
            if (removeFile_(l_file_path))
            {
                // std::cout << "删除成功:" << c_vsPcapNameList_.front() << std::endl;
                c_vsPcapNameList_.erase(c_vsPcapNameList_.begin());
                // reverse(c_vsPcapNameList_.begin(),c_vsPcapNameList_.end());
                // std::sort(c_vsPcapNameList_.begin(), c_vsPcapNameList_.end(), funMysort());
            }
            if (isFileExistent(boost::filesystem::path(l_file_path)))
                printf("文件[%s]删除失败\n", c_vsPcapNameList_.front().c_str());
        }
        sort(c_vsPcapNameList_.begin(), c_vsPcapNameList_.end());
        return true;
    }
    std::vector<std::string> c_vsPcapNameList_; /**< 文件夹下所有pcap文件名称+后缀*/
  private:
    int c_iPcapMaxNum_;                   /**< 保留的最多pcap数目*/
    boost::filesystem::path c_pDataPath_; /**< pcap路径*/

    /**
     * @brief 将string转为int
     * @param {const std::string &} p_sText 待转的string文本
     * @return [int][为数字，string转换成功。否则为0]
     */
    static int funSt2Num_(const std::string& p_sText) /**string 2 num*/
    {
        std::stringstream l_ss(p_sText);
        int l_result;
        return l_ss >> l_result ? l_result : 0;
    }

    /**
     * @brief 文件名排序
     *
     *
     */
    struct funMysort /**sort 函数，用作下面排序*/
    {
        bool operator()(const std::string& p_sa, const std::string& p_sb)
        {
            boost::regex re("(\\d+)");
            boost::match_results<std::string::const_iterator> what1, what2;
            boost::regex_search(p_sa.cbegin(), p_sa.cend(), what1, re, boost::match_default);
            boost::regex_search(p_sb.cbegin(), p_sb.cend(), what2, re, boost::match_default);

            return funSt2Num_(what1[1]) > funSt2Num_(what2[1]);
        }
    };

    bool removeFile_(std::string l_path)
    {
        try
        {
            boost::filesystem::remove(boost::filesystem::path(l_path));
            return true;
        }
        catch (const std::exception& e)
        {
            printf("删除失败，请检查 | %s\n", e.what());
        }
        return false;
    }
    /**
     * @brief 遍历目录下所有文件，添加至l_vfiles中
     * @param {std::vector<std::string> &} l_vfiles 保存的所有文件
     * @param {boost::filesystem::path} p path路径
     * @param {int} num 最多保留的pcap文件数目
     */
    void funBrowseFile_(std::vector<std::string>& l_vfiles,
                        boost::filesystem::path p,
                        int p_iMaxNum = 10)
    {
        if (!boost::filesystem::exists(p))
            return;
        for (auto i = boost::filesystem::directory_iterator(p);
             i != boost::filesystem::directory_iterator();
             i++)
        {
            // 非目录则添加
            if (!boost::filesystem::is_directory(i->path()))
            {
                // cout << i->path().filename().string() << endl;
                l_vfiles.push_back(i->path().filename().string());
            }
        }
        // 从小到大排序
        if (!l_vfiles.empty())
            sort(l_vfiles.begin(), l_vfiles.end());
        int l_Size = l_vfiles.size();
        while (1)
        {
            if ((int)l_vfiles.size() <= p_iMaxNum)
                break;
            std::string l_file_path = c_pDataPath_.string() + l_vfiles.front();
            l_vfiles.erase(l_vfiles.begin());
            if (!removeFile_(l_file_path))
                break;
        }
        for (size_t i = 0; i < l_vfiles.size(); i++)
        {
            std::cout << "file[" << i << "]" << l_vfiles[i] << std::endl;
        }
    }
};

// int main()
// {
//     string g_p("../pcap/");
//     std::string g_file = "../aa/1111.pcapng";

//     PcapManage f(g_p, 10);
//     for (int i = 0; i < f.c_vsPcapNameList_.size(); i++)
//     {
//         std::cout << "file[" << i << "]" << f.c_vsPcapNameList_[i] << std::endl;
//     }
//     std::cout << std::endl;
//     f.deleteOldPcap(g_file);
//     for (int i = 0; i < f.c_vsPcapNameList_.size(); i++)
//     {
//         std::cout << "file[" << i << "]" << f.c_vsPcapNameList_[i] << std::endl;
//     }
// }