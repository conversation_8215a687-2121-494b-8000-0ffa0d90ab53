/*
 * @Description: web协议
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2022-03-24 17:36:27
 * @LastEditors: wen <EMAIL>
 * @LastEditTime: 2022-10-14 12:43:51
 */
#pragma once

#include "common/common_master.h"
#include "net_app/common.h"
#include "tool/fileTool/fileTool.h"
#include "tool/protocolTool/protocolTool.h"
#include <dirent.h>
#include <fstream>
#include <mutex>
#include <sys/types.h>
enum FRAMETYPE { FRAMEERR = 0, SETQUERY = 1, REPLY = 2 };
namespace wj_slam {
class WebProtocol {
  public:
    void selectSlaverProtocol(u_char*, int, char*);
    void selectMasterProtocol(u_char*, int, char*);
    void selectDeviceProtocol(u_char*, int, char*);
    void selectParamProtocol(u_char*, int, char*);
    void selectPrivateProtocol(u_char*, int, char*);

  private:
    s_masterCfg* c_masterParamPtr;
    // SYSPARAM c_slamBak_;
    boost::function<void(char*, int)> c_sendTcpCb_;  // 回调-Tcp协议回复
    boost::function<void(char*, int)> c_sendRosCb_;  // 回调-Ros协议回复
    boost::function<int(int, int)> c_setActionCb_;   // 回调-调用其他模块进行操作
    int m_iTCPDataMaxLen_ = 1460 - 4;  // TCP允许数据长度为1460 去帧尾校验后-4
    bool c_bSlamControlFlag_ = false;
    void calcWebProtocolHead_(u_char*, char*, int&);
    void calcWebProtocolTail_(char*, int&);
    FRAMETYPE getFrameType(int p_iFrameType);

    /**
     * @description: 根据协议删除某类型的数据
     * @param {u_char*&} p_pcBufCMD 完整协议
     * @param {int} p_iLen ascii偏移量
     * @param {std::string} p_sPath 文件/文件夹上一级目录
     * @return {bool} true 是否删除成功
     * @other:
     */
    bool deleteData_(u_char*& p_pucBufCMD,
                     int p_iLen,
                     std::string p_sPath,
                     std::string p_sFileTail = "");

    /**
     * @description: 填充某目录下 某后缀格式文件列表
     * @param {std::string } p_sFindPath 搜寻目录
     * @param {std::string } p_sFileTail 文件后缀标志 eg pcapng/bag 不带. ""意味着文件夹
     * @param {char*} p_pcBuf
     * @param {int&} p_iLen
     * @return {*}
     * @other:
     */
    void
    fillFileList_(std::string p_sFindPath, std::string p_sFileTail, char* p_pcBuf, int& p_iLen);

    /**
     * @description: 填充某目录下Map文件列表
     * @param {std::string } p_sFindPath 搜寻目录
     * @param {char*} p_pcBuf
     * @param {int&} p_iLen
     * @return {*}
     * @other: 注意：该函数针对Map做了特殊化处理
     */
    void fillMapList_(std::string p_sFindPath, char* p_pcBuf, int& p_iLen);
    /**
     * @description: 设置某id网卡的网络配置
     * @param {u_char*&} p_pucBufCMD
     * @param {int} p_iLen
     * @return {*}
     * @other:
     */
    bool setNetInfo_(u_char*& p_pucBufCMD, int& p_iLen, s_NetCfg& p_sNetInfo);

    /**
     * @description: 提取网卡名转换id
     * @param {u_char*&} p_pucBufCMD
     * @param {int} p_iLen
     * @return {*}
     * @other:
     */
    int getNetNameToId_(u_char*& p_pucBufCMD, int& p_iLen);

    /**
     * @description: 填充某id网卡的网络配置
     * @param {u_char*} p_pcBuf
     * @param {int&} p_iLen
     * @param {int} p_iNetid 网卡id
     * @return {*}
     * @other:
     */
    void fillNetInfo_(char* p_pcBuf, int& p_iLen, int p_iNetid);

    bool setMapCorrect_(u_char*&, int);
    int getLidarId_(u_char*& p_pcBufCMD, int& p_iOffset);

    /**
     * @description: 增加雷达时提取雷达名称 并确保名称唯一
     * @param {u_char*} p_pcBufCMD
     * @param {int&} p_iOffset
     * @param {string} p_sLidarName 待填充雷达名称
     * @return {bool}  true提取成功且未重复
     * @other:
     */
    int getNewLidarName_(u_char*& p_pcBufCMD, int& p_iOffset, std::string& p_sLidarName);

    uint32_t getOnlyId_(std::vector<s_LidarConfig>& p_vLidar);

    /**
   * @description: 从文件读取上次定位存储位姿
   * @param {s_POSE6D} &
   * @param {string} p_sFilePath
   * @return {*}
   */
  bool readPoseInFile_(s_POSE6D& p_sPose, std::string p_sFilePath);

  /**
   * @description: 删除文件后缀
   * @param {string} & p_sFileName
   * @param {string} & p_sSeparator 文件后缀
   * @return {string} 文件名
   */
  std::string fileFilterTail_(std::string& p_sFileName, const std::string& p_sSeparator);

  void sendRecvSuccCMD_(char* p_pcBufResponse, int p_iLen, int p_iRes);

  void setSlamControl_(int p_newSign, char* p_pcBufResponse, int p_iLen);

  public:
    WebProtocol(boost::function<void(char*, int)>,
                boost::function<void(char*, int)>,
                boost::function<int(int, int)>);
    ~WebProtocol();
};
}  // namespace wj_slam