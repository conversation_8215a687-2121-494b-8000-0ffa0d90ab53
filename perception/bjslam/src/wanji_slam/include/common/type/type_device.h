/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2022-03-22 09:36:23
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2023-05-26 10:38:38
 */

#pragma once
#include "./type_status.h"
#include "common/config/conf_timer.h"
#include <string>

namespace wj_slam {

typedef struct s_DevCfg
{
    std::string m_sDevStatus[18] = {
        "DEFAULTSTATUS", /*默认最低级别*/
        "WAITCONNECT",   /*等待连接*/
        "VIRTUALDEV",    /*虚拟设备*/

        "DATAWAIT",      /*等待雷达数据，5者允许跨级显示*/
        "DATADATAERROR", /*设备数据错误：超时/扫描异常，5者允许跨级显示*/
        "PCAPRUN",       /*正在播放，5者允许跨级显示*/
        "PCAPSTOP",      /*暂停播放，5者允许跨级显示*/
        "DEVCONNECT",    /*识别设备: 网络无误且连接，5者允许跨级显示*/

        "LOADPARAMERROR", /*垂直偏心获取/加载异常*/
        "GETPARAMERROR",  /*获取参数错误：eg: SN/雷达类型/数据表*/
        "NOFINDDEV",      /*设备未识别：PING未识别 或 未连接*/
        "NETNOALIVE",     /*网卡未活跃*/
        "NETNOIP",        /*网卡未活跃且未配置IP*/
        "NETIPERROR",     /*网卡IP错误: PC-IP和网卡实时IP/配置IP不符*/
        "NETSETERROR",    /*网卡设置错误: 未设置*/
        "PCAPOVER",       /*播放完毕*/
        "PCAPERROR",      /*PCPA错误：不存在或异常*/
        "IPINFOERROR"     /*网络配置错误: PC-IP 和 雷达IP 非同网段*/
    };
    std::string m_sDevType;     /*传感器类型*/
    std::string m_sPcapName;    /*PCAP名*/
    std::string m_sBagName;     /*ROSBAG名*/
    std::string m_sDevIP;       /*设备IP*/
    std::string m_sLocalIP;     /*目标IP*/
    std::string m_sMulticastIP; /*组播IP*/
    std::string m_sNetName;     /*网卡名*/
    uint16_t m_uiDevPort;       /*设备端口(即-lidar/agv端口)*/
    uint16_t m_uiLocalPort;     /*目标端口(即-本机端口)*/
    uint32_t m_uiDataMinLen;    /*数据最小长度 eg: WLR720稳定为1260 SICK不稳定设置0*/
    uint32_t m_uiId;            /*设备id*/
    DevStatus m_status; /*设备状态*/

    s_DevCfg()
    {
        m_sDevType = "Default";
        m_sDevIP = "************";
        m_sLocalIP = "************";
        m_sMulticastIP = "**********";
        m_sPcapName = "";
        m_sBagName = "";
        m_sNetName = "";
        m_uiDevPort = 3333;
        m_uiLocalPort = 3001;
        m_uiDataMinLen = 1;
        m_uiId = 0;
        m_status = DevStatus::DEFAULTSTATUS;
    }

    void setStatus(DevStatus p_status)
    {
        if ((int)p_status > (int)this->m_status)
            this->m_status = p_status;
        else
        {
            // 小于 则考虑是否是跨级显示
            if ((this->m_status == DevStatus::DATADATAERROR || this->m_status == DevStatus::PCAPRUN
                 || this->m_status == DevStatus::PCAPSTOP || this->m_status == DevStatus::DEVCONNECT
                 || this->m_status == DevStatus::DATAWAIT)
                && (p_status == DevStatus::DATADATAERROR || p_status == DevStatus::PCAPRUN
                    || p_status == DevStatus::PCAPSTOP || p_status == DevStatus::DEVCONNECT
                    || p_status == DevStatus::DATAWAIT))
                this->m_status = p_status;
            // else
            //     printf("级别不足: new %s  old %s\n",  m_sDevStatus[(int)p_status].c_str(),
            //     m_sDevStatus[(int)this->m_status].c_str());
        }
    }

    // 打印
    void log()
    {
        //
    }

} s_DevCfg;

}  // namespace wj_slam