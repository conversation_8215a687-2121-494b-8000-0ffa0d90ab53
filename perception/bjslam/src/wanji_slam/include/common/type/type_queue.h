#pragma once

#include "circleQueue.h"

/***
 * @class   Class of Circle Queue, the same as std::queue's interface
 * @brief   As a fixed size buffer
 * <AUTHOR>
 */

namespace wj_slam {
template <class T> class Queue {
  public:
    Queue(uint32_t size = 100);
    ~Queue();

    T front();
    void push(const T& t);
    T pop();
    bool empty();
    int size();
    int capacity();
    void clear();

  private:
    CircleQueue<T> c_queue_;
    uint32_t c_size_;
};

template <class T> Queue<T>::Queue(uint32_t size) : c_size_(size)
{
    clear();
}

template <class T> Queue<T>::~Queue()
{
    clear();
}

template <class T> T Queue<T>::front()
{
    if (!c_queue_.Size())
    {
        printf("[type_quque] Queue front empty.");
    }

    return c_queue_.Front();
}

template <class T> void Queue<T>::push(const T& t)
{
    c_queue_.Push(t);
}

template <class T> T Queue<T>::pop()
{
    if (!c_queue_.Size())
    {
        printf("[type_quque] Queue pop empty.");
    }

    return c_queue_.Pop();
}

template <class T> bool Queue<T>::empty()
{
    return !c_queue_.Size();
}

template <class T> int Queue<T>::size()
{
    return c_queue_.Size();
}

template <class T> int Queue<T>::capacity()
{
    return c_size_;
}

template <class T> void Queue<T>::clear()
{
    c_queue_.Reset(c_size_);
}

}  // namespace wj_slam
