#pragma once

#include <mutex>
#include <string.h>

/***
 *
 * @class   Class of Circle Queue
 * @brief   As a fixed size buffer
 * <AUTHOR>
 */

template <class T> class CircleQueue {
  public:
    CircleQueue();
    CircleQueue(uint32_t capacity);
    ~CircleQueue();

    void Reset(uint32_t capacity);
    uint32_t Push(const T* t, uint32_t len);
    void Push(const T& t);
    uint32_t Pop(uint32_t len);
    T Pop();
    T Front();
    uint32_t Data(T* t, uint32_t len);
    uint32_t Size();

  private:
    void AddIn_(uint32_t n);
    void AddOut_(uint32_t n);
    uint32_t Size_();

    uint32_t m_capacity;
    uint32_t m_in;
    uint32_t m_out;
    uint32_t m_tSize;

    T* m_data;

    std::mutex m_mutex;
};

template <class T>
CircleQueue<T>::CircleQueue()
    : m_capacity(4096), m_in(0), m_out(0), m_tSize(sizeof(T)), m_data(nullptr)
{
    m_data = new T[m_capacity];
    if (m_data == nullptr)
    {
        fprintf(stderr, "[CircleQueue] : Failed to allocate memory, Capacity : %n", &m_capacity);
    }
}

template <class T>
CircleQueue<T>::CircleQueue(uint32_t size) : m_capacity(4096), m_in(0), m_out(0), m_data(nullptr)
{
    m_data = new T[m_capacity];
    if (m_data == nullptr)
    {
        fprintf(stderr, "[CircleQueue] : Failed to allocate memory, Capacity : %n", &m_capacity);
    }
}

template <class T> CircleQueue<T>::~CircleQueue()
{
    if (m_data != nullptr)
    {
        delete[] m_data;
        m_data = nullptr;
    }

    m_capacity = 0;
    m_in = 0;
    m_out = 0;
}

template <class T> void CircleQueue<T>::Reset(uint32_t capacity)
{
    std::lock_guard<std::mutex> locker(m_mutex);
    m_capacity = capacity;
    m_in = 0;
    m_out = 0;

    if (m_data != nullptr)
    {
        delete[] m_data;
        m_data = nullptr;
    }

    m_data = new T[m_capacity];
    if (m_data == nullptr)
    {
        fprintf(stderr, "[CircleQueue] : Failed to allocate memory, Capacity : %n", &m_capacity);
    }
}

template <class T> uint32_t CircleQueue<T>::Push(const T* t, uint32_t len)
{
    std::lock_guard<std::mutex> locker(m_mutex);
    if (t == nullptr)
    {
        fprintf(stderr, "[CircleQueue] : Push Failed");
        return 0;
    }

    uint32_t reallen = std::min(
        len, m_capacity);  // len 大于 Size() 时会覆盖掉已有数据，并且可能会改变m_in与m_out的关系
    if (reallen >= m_capacity - Size_())
    {
        fprintf(stderr, "[CircleQueue] : len > Size;\n");
        return 0;
    }
    // 跨4096分割保存
    if (m_in + reallen > m_capacity)
    {
        int n = (m_capacity - m_in);
        memcpy(&m_data[m_in], t, m_tSize * n);
        memcpy(m_data, &t[n], m_tSize * (reallen - n));
    }
    else
    {
        memcpy(&m_data[m_in], t, m_tSize * reallen);
    }

    AddIn_(reallen);
    return reallen;
}

template <class T> uint32_t CircleQueue<T>::Data(T* t, uint32_t len)
{
    std::lock_guard<std::mutex> locker(m_mutex);
    if (t == nullptr)
    {
        fprintf(stderr, "[CircleQueue] : Get data Failed");
        return 0;
    }

    uint32_t reallen = std::min(len, Size_());
    if (reallen == 0)
    {
        return 0;
    }

    if (m_out + reallen > m_capacity)
    {
        int n = m_capacity - m_out;
        memcpy(t, &m_data[m_out], m_tSize * n);
        memcpy(&t[n], m_data, m_tSize * (reallen - n));
    }
    else
    {
        memcpy(t, &m_data[m_out], m_tSize * reallen);
    }

    return reallen;
}

template <class T> uint32_t CircleQueue<T>::Pop(uint32_t len)
{
    std::lock_guard<std::mutex> locker(m_mutex);
    len = std::min(len, Size_());
    AddOut_(len);
    return len;
}

template <class T> void CircleQueue<T>::Push(const T& t)
{
    std::lock_guard<std::mutex> locker(m_mutex);
    if (t == nullptr)
    {
        fprintf(stderr, "[CircleQueue] : Get data Failed");
        return;
    }

    uint32_t reallen = m_capacity - Size_();
    m_data[m_in] = t;
    AddIn_(1);
    if (reallen == 1)
    {
        AddOut_(1);
    }
}

template <class T> T CircleQueue<T>::Pop()
{
    std::lock_guard<std::mutex> locker(m_mutex);
    T t = m_data[m_out];
    if (Size_())
    {
        AddOut_(1);
    }
    return t;
}

template <class T> T CircleQueue<T>::Front()
{
    std::lock_guard<std::mutex> locker(m_mutex);
    return m_data[m_out];
}

template <class T> void CircleQueue<T>::AddIn_(uint32_t n)
{
    m_in = (m_in + n) % m_capacity;
}

template <class T> void CircleQueue<T>::AddOut_(uint32_t n)
{
    m_out = (m_out + n) % m_capacity;
}

template <class T> uint32_t CircleQueue<T>::Size()
{
    std::lock_guard<std::mutex> locker(m_mutex);
    return Size_();
}

template <class T> uint32_t CircleQueue<T>::Size_()
{
    return (m_in >= m_out) ? (m_in - m_out) : (m_in + m_capacity - m_out);
}