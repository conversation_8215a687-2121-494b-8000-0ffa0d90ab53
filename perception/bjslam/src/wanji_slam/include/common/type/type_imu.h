#pragma once
#ifndef _TYPE_IMU_H_
#    define _TYPE_IMU_H_
#    include <Eigen/Dense>
#    include <iostream>
namespace wj_slam {
struct IMUData
{
  public:
    /**< imu数据状态, 0: 不可用, 1: 零偏修正完成,但数据不可用, 2:
     * 零偏修正完成,数据已经初始化,该状态由外部触发 3: 零偏修正完,四元数数据可用,速度不可用 4:
     * 修正完成,四元数可用,速度也可用*/
    enum IMUStatus { NoValid = 0, BiasDone, InitDone, QuatValid, AllValid };

  protected:
    typedef timeMs Time;
    Time m_tsSensorTime;
    Time m_tsSyncTime;
    Time m_tsWallTime;
    int m_iScanId;
    float m_fTemperature_;
    IMUStatus m_bIsValid_;
    Eigen::Vector3d m_vAngularVel_;
    Eigen::Vector3d m_vLinearAcc_;
    Eigen::Quaterniond m_quat_;

  public:
    IMUData()
    {
        m_vAngularVel_ = Eigen::Vector3d::Zero();
        m_vLinearAcc_ = Eigen::Vector3d::Zero();
        m_tsSyncTime = -1;
        m_tsSensorTime = -1;
        m_tsWallTime = -1;
        m_iScanId = -1;
        m_fTemperature_ = -1000;
        m_bIsValid_ = IMUStatus::NoValid;
        m_quat_ = Eigen::Quaterniond::Identity();
    }
    IMUData(const IMUData& data)
    {
        *this = data;
    }
    void reset()
    {
        m_vAngularVel_ = Eigen::Vector3d::Zero();
        m_vLinearAcc_ = Eigen::Vector3d::Zero();
        m_tsSyncTime = -1;
        m_tsSensorTime = -1;
        m_tsWallTime = -1;
        m_iScanId = -1;
        m_fTemperature_ = -1000;
        m_bIsValid_ = IMUStatus::NoValid;
        m_quat_ = Eigen::Quaterniond::Identity();
    }
    /**
     * @brief 考虑下是否要修改四元数?
     * @param datain
     * @return IMUData
     *
     */
    IMUData operator-(const IMUData& datain)
    {
        IMUData l_imuData(*this);
        l_imuData.m_vAngularVel_ -= datain.m_vAngularVel_;
        return l_imuData;
    }
    IMUData& operator-=(const IMUData& datain)
    {
        this->m_vAngularVel_ -= datain.m_vAngularVel_;
        return *this;
    }
    IMUData& operator=(const IMUData& data)
    {
        m_vAngularVel_ = data.m_vAngularVel_;
        m_vLinearAcc_ = data.m_vLinearAcc_;
        m_tsSyncTime = data.m_tsSyncTime;
        m_tsSensorTime = data.m_tsSensorTime;
        m_tsWallTime = data.m_tsWallTime;
        m_iScanId = data.m_iScanId;
        m_bIsValid_ = data.m_bIsValid_;
        m_fTemperature_ = data.m_fTemperature_;
        m_quat_ = data.m_quat_;
        return *this;
    }
    float ax() const
    {
        return m_vLinearAcc_.x();
    }
    float ay() const
    {
        return m_vLinearAcc_.y();
    }
    float az() const
    {
        return m_vLinearAcc_.z();
    }
    float wx() const
    {
        return m_vAngularVel_.x();
    }
    float wy() const
    {
        return m_vAngularVel_.y();
    }
    float wz() const
    {
        return m_vAngularVel_.z();
    }
    Time syncTime() const
    {
        return m_tsSyncTime;
    }
    Time& syncTime()
    {
        return m_tsSyncTime;
    }
    Time imuTime() const
    {
        return m_tsSensorTime;
    }
    Time& imuTime()
    {
        return m_tsSensorTime;
    }
    Time imuRecvTime() const
    {
        return m_tsWallTime;
    }
    Time& imuRecvTime()
    {
        return m_tsWallTime;
    }
    float temperature() const
    {
        return m_fTemperature_;
    }
    float& temperature()
    {
        return m_fTemperature_;
    }
    int scanId() const
    {
        return m_iScanId;
    }
    int& scanId()
    {
        return m_iScanId;
    }
    /**
     * @brief 返回四元数值
     * @return Eigen::Quaterniond
     *
     */
    Eigen::Quaterniond quat() const
    {
        return m_quat_;
    }
    /**
     * @brief 返回四元数引用
     * @return Eigen::Quaterniond&
     *
     */
    Eigen::Quaterniond& quat()
    {
        return m_quat_;
    }
    Eigen::Vector3d linearAcceleration() const
    {
        return m_vLinearAcc_;
    }
    Eigen::Vector3d& linearAcceleration()
    {
        return m_vLinearAcc_;
    }
    Eigen::Vector3d angularVelocity() const
    {
        return m_vAngularVel_;
    }
    Eigen::Vector3d& angularVelocity()
    {
        return m_vAngularVel_;
    }
    IMUStatus status() const
    {
        return m_bIsValid_;
    }
    void setStatus(const IMUStatus& status)
    {
        m_bIsValid_ = status;
    }
    void printf(std::string str = "*") const
    {
        std::cout << "******** imuData " << str << "*******" << std::endl;
        std::cout << "syncTime: " << std::to_string(m_tsSyncTime)
                  << ", sensorTime: " << std::to_string(m_tsSensorTime)
                  << ", recvTime: " << std::to_string(m_tsWallTime) << std::endl;
        std::cout << "scanId: " << m_iScanId << ", temperature: " << m_fTemperature_ << std::endl;
        std::cout << "status: " << int(m_bIsValid_) << std::endl;
        std::cout << "AngularVel(deg/100ms): " << this->wx() / M_PI * 18 << ", "
                  << this->wy() / M_PI * 18 << ", " << this->wz() / M_PI * 18 << std::endl;
        std::cout << "AngularVel(rad/s): " << this->wx() << ", " << this->wy() << ", " << this->wz()
                  << std::endl;
        std::cout << "LinearAcc(m/s^2): " << this->ax() << ", " << this->ay() << ", " << this->az()
                  << std::endl;
        std::cout << "quat : " << m_quat_.x() << ", " << m_quat_.y() << ", " << m_quat_.z() << ", "
                  << m_quat_.w() << std::endl;
    }
    void printfData(std::string str = "*") const
    {
        std::cout << "imu raw data " << str << "," << std::to_string(m_tsSyncTime) << ","
                  << std::to_string(m_tsSensorTime) << "," << std::to_string(m_tsWallTime) << ","
                  << m_iScanId << "," << m_fTemperature_ << "," << m_vAngularVel_.x() << ","
                  << m_vAngularVel_.y() << "," << m_vAngularVel_.z() << "," << m_vLinearAcc_.x()
                  << "," << m_vLinearAcc_.y() << "," << m_vLinearAcc_.z() << std::endl;
    }
};
struct IMUPerScan
{
  private:
    std::vector<IMUData> m_vImuData;

  public:
    IMUPerScan()
    {
        m_vImuData.resize(0);
        m_vImuData.reserve(12);  //提前开辟12个空间
    }
    ~IMUPerScan()
    {
        std::vector<IMUData>().swap(m_vImuData);  //清空数据
    }
    void push_back(const IMUData& p_data)
    {
        m_vImuData.push_back(p_data);
    }
    int scanId() const
    {
        if (!m_vImuData.empty())
            return m_vImuData[0].scanId();
        else
            return -1;
    }
    uint32_t size() const
    {
        return m_vImuData.size();
    }
    IMUData& at(int index)
    {
        return m_vImuData.at(index);
    }
    void printf(std::string str = "*") const
    {
        std::cout << "***********imuScan " << str << "size: " << m_vImuData.size()
                  << "***************" << std::endl;
        for (int i = 0; i < m_vImuData.size(); ++i)
        {
            m_vImuData.at(i).printf(str + "_" + std::to_string(i));
        }
        std::cout << "***********  imuScan  ***************" << std::endl;
    }
};
}  // namespace wj_slam
#endif