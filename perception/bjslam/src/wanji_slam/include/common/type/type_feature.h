/*
 * @Description:
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2021-12-01 15:28:21
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-07-04 15:46:44
 */
#pragma once
#include "common/config/conf_timer.h"
#include "type_imu.h"
#include <boost/shared_ptr.hpp>
#include <mutex>
#include <pcl/io/pcd_io.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>

namespace wj_slam {

template <typename T> class FEATURE_PAIR {
  private:
    typedef pcl::PointCloud<T> PC_;
    std::mutex c_mutex;

  public:
    boost::shared_ptr<PC_> first = NULL;   // 角
    boost::shared_ptr<PC_> second = NULL;  // 面
    boost::shared_ptr<PC_> third = NULL;   // 靶标
    boost::shared_ptr<PC_> fourth = NULL;  // 马路伢子&车道线
    boost::shared_ptr<PC_> allPC = NULL;
    boost::shared_ptr<Eigen::MatrixXd> sc = NULL; /**< scan context 描述符 */
    IMUData m_imuData;  // imu数据,考虑下operator函数作用,暂时只定义了operator=
    timeMs m_tsSyncTime;
    timeMs m_tsWallTime;
    timeMs m_dTimespan;
    u_int32_t m_uiScanFrame;
    int m_iSample2ndSize;
    bool m_bHasContext; /**< 是否拥有有效描述符 */
    FEATURE_PAIR()
    {
        first.reset(new PC_());
        second.reset(new PC_());
        third.reset(new PC_());
        fourth.reset(new PC_());
        allPC.reset(new PC_());
        sc.reset(new Eigen::MatrixXd);
        m_tsWallTime = 0;
        m_tsSyncTime = 0;
        m_dTimespan = 0;
        m_uiScanFrame = 0;
        m_iSample2ndSize = -1;
        m_bHasContext = false;
    }
    int markSize()
    {
        return third->points.size();
    }
    int cornerSize()
    {
        return first->points.size();
    }
    int surfSize()
    {
        return second->points.size();
    }
    int curbSize()
    {
        return fourth->points.size();
    }
    int pcSize()
    {
        return allPC->points.size();
    }
    int recvTime()
    {
        return m_tsWallTime;
    }
    int time()
    {
        return m_tsSyncTime;
    }
    u_int32_t scanFrameID()
    {
        return m_uiScanFrame;
    }
    void free()
    {
        std::lock_guard<std::mutex> locker(c_mutex);
        if (this->first)
            PC_().swap(*(this->first));
        if (this->second)
            PC_().swap(*(this->second));
        if (this->third)
            PC_().swap(*(this->third));
        if (this->fourth)
            PC_().swap(*(this->fourth));
        if (this->allPC)
            PC_().swap(*(this->allPC));
    }
    /**
     * @function:
     * @description: 深拷贝
     * @param {*}
     * @return {*}
     * @others: null
     */
    FEATURE_PAIR& operator=(const FEATURE_PAIR& p_in)
    {
        std::lock_guard<std::mutex> locker(c_mutex);
        if (this->first == NULL)
            this->first.reset(new PC_());
        if (this->second == NULL)
            this->second.reset(new PC_());
        if (this->third == NULL)
            this->third.reset(new PC_());
        if (this->fourth == NULL)
            this->fourth.reset(new PC_());
        if (this->allPC == NULL)
            this->allPC.reset(new PC_());

        pcl::copyPointCloud(*(p_in.allPC), *(this->allPC));
        pcl::copyPointCloud(*(p_in.first), *(this->first));
        pcl::copyPointCloud(*(p_in.second), *(this->second));
        pcl::copyPointCloud(*(p_in.third), *(this->third));
        pcl::copyPointCloud(*(p_in.fourth), *(this->fourth));
        *(this->sc) = *(p_in.sc);
        this->m_imuData = p_in.m_imuData;
        this->m_tsWallTime = p_in.m_tsWallTime;
        this->m_tsSyncTime = p_in.m_tsSyncTime;
        this->m_dTimespan = p_in.m_dTimespan;
        this->m_uiScanFrame = p_in.m_uiScanFrame;
        this->m_iSample2ndSize = p_in.m_iSample2ndSize;
        this->m_bHasContext = p_in.m_bHasContext;
        return *this;
    }
    FEATURE_PAIR& operator+=(const FEATURE_PAIR& p_in)
    {
        std::lock_guard<std::mutex> locker(c_mutex);
        if (this->first == NULL)
            this->first.reset(new PC_());
        if (this->second == NULL)
            this->second.reset(new PC_());
        if (this->third == NULL)
            this->third.reset(new PC_());
        if (this->fourth == NULL)
            this->fourth.reset(new PC_());
        if (this->allPC == NULL)
            this->allPC.reset(new PC_());
        *(this->allPC) += *p_in.allPC;
        // wjPrint(COLOR_RED, "deep copy FEATURE_PAIR", NULL);
        *(this->first) += *p_in.first;
        *(this->second) += *p_in.second;
        *(this->third) += *p_in.third;
        *(this->fourth) += *p_in.fourth;
        //
        this->m_tsWallTime = p_in.m_tsWallTime;
        this->m_tsSyncTime = p_in.m_tsSyncTime;
        this->m_uiScanFrame = p_in.m_uiScanFrame;
        return *this;
    }
    void printf(std::string str)
    {
        std::cout << "*********** Fe " << str << "***********" << std::endl;
        std::cout << "**  syncTime : " << std::to_string(this->m_tsSyncTime) << std::endl;
        std::cout << "**  m_tsWallTime : " << std::to_string(this->m_tsWallTime) << std::endl;
        std::cout << "**  m_uiScanFrame : " << std::to_string(this->m_uiScanFrame) << std::endl;
        std::cout << "**************************" << std::endl;
    }
    ~FEATURE_PAIR()
    {
        this->free();
        // std::cerr << "~FEATURE_PAIR" << this << std::endl;
    }
    typedef boost::shared_ptr<FEATURE_PAIR<T>> Ptr;
};

}  // namespace wj_slam