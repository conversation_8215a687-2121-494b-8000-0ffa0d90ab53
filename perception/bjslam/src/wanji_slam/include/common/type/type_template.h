/**
 * @file type_template.h
 * <AUTHOR>
 * @brief 模板类型基类
 * @version 1.0
 * @date 2023-09-14
 * @copyright Copyright (c)2023 Vanjee
 */

#pragma once

// local
#include "type_frame.h"

// 地图点云类型模板
template <typename M> class TypeMap
{
public:
    // 地图
    using Ptr = boost::shared_ptr<pcl::PointCloud<M>>;
    using KeyFrame = wj_slam::KEYFRAME<M>;
    using KeyFramePtr = boost::shared_ptr<KeyFrame>;
    using ConstKeyFramePtr = boost::shared_ptr<const KeyFrame>;
};

// 位姿点云类型模板
template <typename P> class TypePose
{
public:
    // 位姿
    using PointCloud = pcl::PointCloud<P>;
    using Ptr = boost::shared_ptr<PointCloud>;
    using ConstPtr = boost::shared_ptr<const PointCloud>;
};

// 地图及位姿类型定义
namespace type
{
    typedef pcl::PointXYZI POSE;
    typedef pcl::PointXYZHSV PointMap;
    // map type
    typedef typename TypeMap<PointMap>::Ptr FeaturePcPtr;
    typedef typename TypeMap<PointMap>::KeyFrame KeyFrame;
    typedef typename TypeMap<PointMap>::KeyFramePtr KeyFramePtr;
    typedef typename TypeMap<PointMap>::ConstKeyFramePtr ConstKeyFramePtr;

    // pose type
    typedef typename TypePose<POSE>::PointCloud PosePc;
    typedef typename TypePose<POSE>::Ptr PosePcPtr;
    typedef typename TypePose<POSE>::ConstPtr ConstPosePcPtr;
};

// 关键帧类型模板特化类
class KfBase : public TypeMap<type::PointMap>, public TypePose<type::POSE>
{

};



