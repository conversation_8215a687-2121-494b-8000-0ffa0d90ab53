/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON>
 * @Date: 2021-12-01 15:58:11
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2023-05-24 17:44:10
 */
#pragma once
#include "../type/type_device.h"
#include "../type/type_pose.h"
#include <mutex>
#include <string>
#include <wj_log.h>

namespace wj_slam {

/** 外置速度模板 eg wheelVel imuVel ... */
typedef struct s_VelConfig
{
    s_VelConfig()
    {
        m_stWheelTwist.reset();
        m_stWheelTwistMin.reset();
        m_stWheelTwistMax.reset();
        m_stWheelTwistMax.setXYZ(0.2, 0, 0);
        m_bWheelFilter = true;
        m_fWheelProcPN = 0.1;
        m_fWheelProcRN = 0.1;
        m_fWheelMeasPN = 0.02;
        m_fWheelMeasRN = 0.05;
    }
    // 打印
    void log()
    {
        LOGW(WINFO,
             "[param] WheelOdometry Trans: XYA=[{:.3f},{:.3f},{:.3f}]",
             m_stWheelToLidar.x(),
             m_stWheelToLidar.y(),
             m_stWheelToLidar.yaw());
        LOGW(WINFO,
             "[param] WheelOdometry filter-{}: Noise=[{:.2f},{:.2f},{:.2f},{:.2f}]",
             m_bWheelFilter,
             m_fWheelProcPN,
             m_fWheelProcRN,
             m_fWheelMeasPN,
             m_fWheelMeasRN);
    }

    s_POSE6D m_stWheelToLidar;  /**< 车体校正参数*/
    s_POSE6D m_stWheelTwistMin; /**< 轮式最小速度*/
    s_POSE6D m_stWheelTwistMax; /**< 轮式最大速度*/
    s_fuseTwist m_stWheelTwist; /**< 轮式速度*/
    bool m_bWheelFilter;        /**< 轮式里程计滤波器开关*/
    double m_fWheelProcPN;      /**< 轮式里程计过程噪声 [m/100ms] */
    double m_fWheelProcRN;      /**< 轮式里程计过程噪声 [deg/100ms] */
    double m_fWheelMeasPN;      /**< 轮式里程计测量噪声 [m/100ms] */
    double m_fWheelMeasRN;      /**< 轮式里程计测量噪声 [deg/100ms] */
} s_VelConfig;

/** 建图/定位-位置配置参数模板 */
typedef struct s_PoseConfig
{
    s_PoseConfig()
    {
        m_stSetPose.reset();
        m_stUIPose.reset();
        m_stCurrPose.reset();
        m_stLastPose.reset();
        m_stCurrPoseWJ.reset();
        m_stLastPoseWJ.reset();
        m_sPoseFilePath = "";
        m_bSaveWholePath = false;
        m_bReWirteByPathZ = false;
        m_mtRequestSend.reset(new std::mutex());
        m_mtRequestSendWJ.reset(new std::mutex());
    }
    // 打印
    void log()
    {
        LOGW(WINFO, "{} [param] PoseFilePath", WJLog::getWholeSysTime(), m_sPoseFilePath);
        LOGW(WINFO,
             "{} [param] Set Pos: XYA=[{:.3f},{:.3f},{:.3f}]",
             WJLog::getWholeSysTime(),
             m_stSetPose.x(),
             m_stSetPose.y(),
             m_stSetPose.yaw());
    }

    s_POSE6D m_stSetPose;                           // 设定位姿势
    s_POSE6D m_stUIPose;                            // 设定UI位姿
    s_PoseWithTwist m_stCurrPose;                   // 用于SICK协议发送
    s_PoseWithTwist m_stLastPose;                   // 用于SICK协议发送
    s_PoseWithTwist m_stCurrPoseWJ;                 // 用于WJ协议发送
    s_PoseWithTwist m_stLastPoseWJ;                 // 用于WJ协议发送
    std::string m_sPoseFilePath;                    // 位姿保存读取路径
    bool m_bSaveWholePath;                          // 记录定位路径
    bool m_bReWirteByPathZ;                         // 是否需要用路径Path的Z值替换设定位姿的Z值
    std::shared_ptr<std::mutex> m_mtRequestSend;    // 是否需要发送:SICK要数
    std::shared_ptr<std::mutex> m_mtRequestSendWJ;  // 是否需要发送:WJ要数

    s_POSE6D m_stSetLidarRFU2CarBackRFUPose; //hsq 设定lidarRFU坐标系到车后轴RFU坐标系的外参
    s_POSE6D m_stSetStartintPointPose; //hsq 设定建图定位原点的绝对坐标：UTM坐标及RPY姿态
    s_POSE6D m_stSetStartintPointGPS; //hsq 设定建图定位原点的经纬度
    int m_UTMCode; //hsq UTMCode
} s_PoseConfig;

/** 对外通信参数 */
typedef struct s_CommunicateConfig
{
    s_CommunicateConfig()
    {
        m_dev.m_sLocalIP = "0.0.0.0";  // 任意IP
        m_dev.m_uiLocalPort = 2112;
        m_dev.m_sPcapName = "NOSET";
        m_dev.m_sDevType = "WheelSick";
        m_dev.m_sNetName = "NET2";  //以WLC-703为默认值
        m_stTrans.reset();
        m_fMymap[0] = 0.0;
        m_fMymap[1] = 0.0;
        m_fMymap[2] = 0.0;
        m_fTomap[0] = 0.0;
        m_fTomap[1] = 0.0;
        m_fTomap[2] = 0.0;
        m_bPoseAlign = true;
        m_fPoseValidTime = 205.0;
    }
    // 打印
    void log()
    {
        LOGW(WINFO, "{} [param] AGV IP: {}", WJLog::getWholeSysTime(), m_dev.m_sLocalIP);
        LOGW(WINFO, "{} [param] AGV Port: {}", WJLog::getWholeSysTime(), m_dev.m_uiLocalPort);
        LOGW(WINFO, "{} [param] AGV PoseAlign: {}", WJLog::getWholeSysTime(), m_bPoseAlign);
        LOGW(WINFO,
             "{} [param] Map Trans: XYA=[{:.3f},{:.3f},{:.3f}]",
             WJLog::getWholeSysTime(),
             m_stTrans.x(),
             m_stTrans.y(),
             m_stTrans.yaw());
    }

    s_DevCfg m_dev;
    s_POSE6D m_stLidarToAgv;  // 车体校正参数
    s_POSE6D m_stTrans;       // 地图校正参数
    float m_fMymap[3];
    float m_fTomap[3];
    float m_fPoseValidTime;  // 位姿有效时间阈值
    bool m_bPoseAlign;       // 预估位姿是否对齐至发送时刻
} s_CommunicateConfig;

/** 位姿校验参数 配置模板 */
typedef struct s_PoseCheckConfig
{
    s_PoseCheckConfig()
    {
        m_bOpenPoseCheck = false;
        m_bOnlyWheelOdom = false;
        m_bUsePoseFuse = false;
        m_bUsePoseCheck = false;
        m_bSafeModel = false;
        m_bUseCurb = false;
        m_bTestCurb = false;
        m_bUseOdom = false;
        m_bUseLoct = false;
        m_bOpenOccupyVerify = false;
        m_bUseOccupyVerify = false;
        m_iOccVerifyBypassThd = 10;
        m_fVerifyGauss = 0.2;
        m_fKdMatchNumPercent = 0.1;
        m_fMatchNumPercent = 0.3;
        m_fMatchOccupyScore = 0.3;
        m_fPredPosFarDist = 30.0;
        m_fPredPosFarTime = 30;
        m_iLidarBakFrame = 100;  // 备份300帧=30s=60MB, 15帧3MB
        m_iPredValidTime = 300;
        m_iMaxNumLidarBak = 20;  // 空间1200MB
        m_vNoCheckAreaList.clear();

        m_stPoseCheck.reset();
        m_stPoseCheck.m_Pose.setXYZ(0.03, 0.03, 0.0);
        m_stPoseCheck.m_Pose.setRPY(0.0, 0.0, 2.0);
        m_stPoseCheck.m_Twist.setXYZ(0.5, 0.3, 0.0);// 单位？
        m_stPoseCheck.m_Twist.setRPY(0.0, 0.0, 0.999);

        m_stPoseMoveStatus.reset();
        m_stPoseMoveStatus.m_Pose.setXYZ(0.05, 0.03, 0.0);
        m_stPoseMoveStatus.m_Pose.setRPY(0.0, 0.0, 1.0);

        m_stCurbCheck.reset();
        m_stCurbCheck.m_Pose.setXYZ(0.1, 0, 0);
        m_stCurbCheck.m_Pose.setRPY(0.0, 0.0, 0.0);
    }
    // 打印
    void log()
    {
        LOGW(WINFO, "{} [param] openPoseCheck: {}", WJLog::getWholeSysTime(), m_bOpenPoseCheck);
        LOGW(WINFO, "{} [param] onlyWheelOdom: {}", WJLog::getWholeSysTime(), m_bOnlyWheelOdom);
        LOGW(WINFO, "{} [param] safeModel: {}", WJLog::getWholeSysTime(), m_bSafeModel);
        LOGW(WINFO, "{} [param] useCurb: {}", WJLog::getWholeSysTime(), m_bUseCurb);
        LOGW(WINFO, "{} [param] testCurb: {}", WJLog::getWholeSysTime(), m_bTestCurb);
        LOGW(WINFO, "{} [param] wheelOdomFarDist: {}", WJLog::getWholeSysTime(), m_fPredPosFarDist);
        LOGW(WINFO, "{} [param] wheelOdomFarTime: {}", WJLog::getWholeSysTime(), m_fPredPosFarTime);

        for (size_t i = 0; m_vNoCheckAreaList.size(); i++)
        {
            if (m_vNoCheckAreaList[i].size() == 4)
                LOGW(WINFO,
                     "{} [param] noCheckArea[{}]: xmin {} | xmax {} | ymin {} | ymax {} ",
                     WJLog::getWholeSysTime(),
                     i,
                     m_vNoCheckAreaList[i][0],
                     m_vNoCheckAreaList[i][1],
                     m_vNoCheckAreaList[i][2],
                     m_vNoCheckAreaList[i][3]);
            else
                LOGW(WINFO,
                     "{} [param] error noCheckArea[{}] size: {} ",
                     WJLog::getWholeSysTime(),
                     i,
                     m_vNoCheckAreaList[i].size());
        }

        LOGW(WINFO,
             "{} [param] Set slamPosCheck Pos Res: XYRPY=[{:.3f},{:.3f},{:.3f},{:.3f},{:.3f}]",
             WJLog::getWholeSysTime(),
             m_stPoseCheck.m_Pose.x(),
             m_stPoseCheck.m_Pose.y(),
             m_stPoseCheck.m_Pose.roll(),
             m_stPoseCheck.m_Pose.pitch(),
             m_stPoseCheck.m_Pose.yaw());
        LOGW(WINFO,
             "{} [param] Set slamPosCheck Vel Res: XYRPY=[{:.3f},{:.3f},{:.3f},{:.3f},{:.3f}]",
             WJLog::getWholeSysTime(),
             m_stPoseCheck.m_Twist.x(),
             m_stPoseCheck.m_Twist.y(),
             m_stPoseCheck.m_Twist.roll(),
             m_stPoseCheck.m_Twist.pitch(),
             m_stPoseCheck.m_Twist.yaw());

        LOGW(WINFO,
             "{} [param] Set CurbPosCheck Pos Res: XYRPY=[{:.3f},{:.3f},{:.3f},{:.3f},{:.3f}]",
             WJLog::getWholeSysTime(),
             m_stCurbCheck.m_Pose.x(),
             m_stCurbCheck.m_Pose.y(),
             m_stCurbCheck.m_Pose.roll(),
             m_stCurbCheck.m_Pose.pitch(),
             m_stCurbCheck.m_Pose.yaw());
        LOGW(WINFO,
             "{} [param] Set CurbPosCheck Vel Res: XYRPY=[{:.3f},{:.3f},{:.3f},{:.3f},{:.3f}]",
             WJLog::getWholeSysTime(),
             m_stCurbCheck.m_Twist.x(),
             m_stCurbCheck.m_Twist.y(),
             m_stCurbCheck.m_Twist.roll(),
             m_stCurbCheck.m_Twist.pitch(),
             m_stCurbCheck.m_Twist.yaw());
    }

    bool m_bOpenPoseCheck;  // 设置是否启动位姿校验 即 是否使用轮式里程计等外部融合速度
    bool m_bOnlyWheelOdom;  // 设置纯里程计模式
    bool m_bUsePoseFuse;    // 设置使用融合位姿
    bool m_bUsePoseCheck;   // 设置使用位姿对比
    bool m_bSafeModel;  // 设置安全模式 - slam定位失败后是否可无限使用里程计模式
    bool m_bUseCurb;   // 设置是否使用车道线+马路伢子+中间线
    bool m_bTestCurb;  // 设置是否启动测试车道线+马路伢子+中间线匹配情况
    bool m_bUseOdom;   // 设置里程计校验
    bool m_bUseLoct;   // 设置定位校验
    float m_fPredPosFarTime;  // 设置预估Pos下AGV最长运行时间
    float m_fPredPosFarDist;  // 设置预估Pos下AGV最远行进距离
    int m_iLidarBakFrame;
    int m_iMaxNumLidarBak;                               // 设置备份雷达文件夹数量
    int m_iPredValidTime;                                // 设置预估Pose有效时间
    float m_fKdMatchNumPercent;                          // kdtree匹配点数比例
    bool m_bOpenOccupyVerify;                            // 是否开启概率校验
    int m_iOccVerifyBypassThd;                           // 概率校验恢复阈值（帧数）
    bool m_bUseOccupyVerify;                             // 是否使用概率恢复
    float m_fMatchNumPercent;                            // 概率校验匹配点数比例
    float m_fMatchOccupyScore;                           // 概率校验得分
    float m_fVerifyGauss;                                // 概率校验高斯分布系数
    std::vector<std::vector<float>> m_vNoCheckAreaList;  // 设置不校验区域List xmin xmax ymin ymax

    s_PoseWithTwist m_stPoseCheck;  // slamPose校验阈值,pose为静止阈值　twist为运动阈值
    s_PoseWithTwist m_stPoseMoveStatus;  // 运动状态阈值 twist nouse
    s_PoseWithTwist m_stCurbCheck;  // 马路伢子优化位姿校验 m_Pose为阈值 m_Twist为速度阈值
                                    // 用于选择m_Pose阈值
} s_PoseCheckConfig;
}  // namespace wj_slam