/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON>
 * @Date: 2021-12-01 15:41:18
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-07-04 15:14:07
 */
#pragma once
#include "../type/type_optimiz.h"
#include "../type/type_status.h"
#include <wj_log.h>
namespace wj_slam {

enum OptimizeMapType {
    DEFAULT_TYPE = -1,
    KDTREE_TYPE, /*匹配优化选择使用Kdtree实现*/
    IVOX_TYPE    /*匹配优化选择使用iVox实现*/
};
/** 建图/定位-地图配置参数模板 */
typedef struct s_MapConfig
{
    s_MapConfig()
    {
        m_sMapName = "map";
        m_fMap_grid = 0.1;
        m_fMapRange = 10.0;
        m_fEuclideanDis = 10.0;
        m_fMapKFPoseGrid = 8.0;
        m_fLoadMapKFPoseGrid = 8.0;
        m_fMapMaxRange = 30.0;
        m_fMapKFSubRange = 30.0;
        m_iMapKFTimeStep = 900;
        m_fMapKFDistStep = 0.5;
        m_fMapKFAnglStep = 180.0;
        m_fOutGridResolu = 0.5;
        m_fInGridResolu = 0.1;
        m_iGridSearchNum = 10;
        m_iGridMatchType = 3;
        m_iGridSize = 1000000;
        m_fGroundHigh = 0.4;
        m_fRoofHigh = 1.2;
        m_iOptimizeModel = 1;
        m_fFilterZValue = 0.5;
        m_fCorSampleSize = 0.8;
        m_fCurbSampleSize = 0.8;
        m_fSurSampleSize = 0.2;
        m_fPathSampleSize = 0.9;
        m_fAllPcSize = 0.2;
        m_fLinePcSize = 0.1;
        m_fSurfPcsize = 0.2;
        m_bSaveMapProc = true;
        m_bSaveKFMap = false;
    }

    // 打印
    void log()
    {
        LOGW(WINFO, "{} [param] Map Name {}", WJLog::getWholeSysTime(), m_sMapName);
        LOGW(WINFO, "{} [param] Map Grid {}", WJLog::getWholeSysTime(), m_fMap_grid);
        LOGW(WINFO, "{} [param] Map Size {}", WJLog::getWholeSysTime(), m_fMapRange);
        LOGW(WINFO, "{} [param] Map PathGrid {}", WJLog::getWholeSysTime(), m_fMapKFPoseGrid);
        LOGW(WINFO,
             "{} [param] Map Load PathGrid {}",
             WJLog::getWholeSysTime(),
             m_fLoadMapKFPoseGrid);
        LOGW(WINFO, "{} [param] Map MaxRange {}", WJLog::getWholeSysTime(), m_fMapMaxRange);
        LOGW(WINFO, "{} [param] Map PathRange {}", WJLog::getWholeSysTime(), m_fMapKFSubRange);
        LOGW(WINFO, "{} [param] Map TimeStep {}", WJLog::getWholeSysTime(), m_iMapKFTimeStep);
        LOGW(WINFO, "{} [param] Map DistStep {}", WJLog::getWholeSysTime(), m_fMapKFDistStep);
        LOGW(WINFO, "{} [param] Map AnglStep {}", WJLog::getWholeSysTime(), m_fMapKFAnglStep);
        LOGW(WINFO, "{} [param] iVox OutGridResolu {}", WJLog::getWholeSysTime(), m_fOutGridResolu);
        LOGW(WINFO, "{} [param] iVox InGridResolu {}", WJLog::getWholeSysTime(), m_fInGridResolu);
        LOGW(WINFO, "{} [param] iVox GridSearchNum {}", WJLog::getWholeSysTime(), m_iGridSearchNum);
        LOGW(WINFO, "{} [param] iVox GridMatchType {}", WJLog::getWholeSysTime(), m_iGridMatchType);
        LOGW(WINFO, "{} [param] iVox GridSize {}", WJLog::getWholeSysTime(), m_iGridSize);
        LOGW(WINFO, "{} [param] iVox GroundHigh {}", WJLog::getWholeSysTime(), m_fGroundHigh);
        LOGW(WINFO, "{} [param] iVox RoofHigh {}", WJLog::getWholeSysTime(), m_fRoofHigh);
        LOGW(WINFO, "{} [param] OptimizeMode {}", WJLog::getWholeSysTime(), m_iOptimizeModel);
        LOGW(WINFO, "{} [param] FilterZValue {}", WJLog::getWholeSysTime(), m_fFilterZValue);
    }

    void setModeDefault(int p_WorkMode, int p_ScenMode)
    {
        switch ((WorkMode)p_WorkMode)
        {
            case WorkMode::InitMapMode:
                m_fMapRange = 10.0;
                m_fMapKFPoseGrid = 1.0;
                m_fLoadMapKFPoseGrid = 8.0;
                m_fMapMaxRange = 30.0;
                break;
            case WorkMode::ContMapMode:
                m_fMapRange = 10.0;
                m_fMapKFPoseGrid = 1.0;
                m_fLoadMapKFPoseGrid = 8.0;
                m_fMapMaxRange = 30.0;
                break;
            case WorkMode::UpdateMapMode:
                m_fMapRange = 15.0;
                m_fMapKFPoseGrid = 1.0;
                m_fLoadMapKFPoseGrid = 8.0;
                m_fMapMaxRange = 30.0;
                break;
            case WorkMode::LocatMode:
                m_fMapRange = 15.0;
                m_fMapKFPoseGrid = 8.0;
                m_fLoadMapKFPoseGrid = 8.0;
                m_fMapMaxRange = 30.0;
                m_fMapKFSubRange = 25.0;
                break;
            default: break;
        }
    }
    std::string m_sMapName;      //地图名 SLAM保存 定位加载
    float m_fMap_grid;           //地图下采样size
    float m_fMapRange;           //附近子图半径
    float m_fEuclideanDis;       //子图中生成path欧式距离
    float m_fMapKFPoseGrid;      // 实时地图KFPose下采样
    float m_fLoadMapKFPoseGrid;  // 加载地图KFPose下采样
    float m_fMapMaxRange;        //地图最大范围-半径
    float m_fMapKFSubRange;      //地图子图切割半径
    int m_iMapKFTimeStep;        //建图频率控制（时间步长）单位 ms
    float m_fMapKFDistStep;      //建图频率控制（距离步长）
    float m_fMapKFAnglStep;      //建图频率控制（角度步长）
    float m_fOutGridResolu;      // iVox大栅格尺寸
    float m_fInGridResolu;       // iVox小栅格尺寸
    int m_iGridSearchNum;        // iVox栅格内部搜索点数
    int m_iGridMatchType;        // iVox栅格周围类型
    std::size_t m_iGridSize;     // iVox栅格数量
    float m_fGroundHigh;         //双层墙地面高度
    float m_fRoofHigh;           //双层墙房顶高度
    float m_fFilterZValue;       //过滤点云Z值高度阈值
    int m_iOptimizeModel;        //选择 0:kdtree     1:iVox
    float m_fCorSampleSize;      // iVox模式下submap角点下采样尺寸
    float m_fCurbSampleSize;     // iVox模式下submap curb点下采样尺寸
    float m_fSurSampleSize;      // iVox模式下submap面点下采样尺寸
    float m_fPathSampleSize;     // iVox模式下submap路径点下采样尺寸
    float m_fAllPcSize;          //地图后处理中所有点云下采样尺寸
    float m_fLinePcSize;         //地图后处理中角点下采样尺寸
    float m_fSurfPcsize;         //地图后处理中面点下才样尺寸
    bool m_bSaveMapProc;         //地图保存后处理
    bool m_bSaveKFMap;           //地图保存逐帧保存KF
    bool m_isUseGPSOptimize;     //是否使用GPS优化
} s_MapConfig;

/** 建图-动态过滤参数模板 */
typedef struct s_RemovingConfig
{
    s_RemovingConfig()
    {
        m_bReMoving = true;
        m_bReMovingRawCloud = true;
        m_iReMovingRawGrow = 1;
        m_fReMovingGrid = 0.3;
        m_iReMovingWindowSize = 10;
        m_iReMovingKoeff[0] = 3;
        m_iReMovingKoeff[1] = 3;
        m_iReMovingKoeff[2] = 3;
        m_iReMovingKoeff[3] = 5;
        m_iReMovingOverlapLevel = 1;
    }

    // 打印
    void log()
    {
        LOGW(WINFO, "{} [param] RM RawCloud {}", WJLog::getWholeSysTime(), m_bReMovingRawCloud);
        LOGW(WINFO, "{} [param] RM VoxelGrid {}", WJLog::getWholeSysTime(), m_fReMovingGrid);
        LOGW(WINFO, "{} [param] RM WindowSize {}", WJLog::getWholeSysTime(), m_iReMovingWindowSize);
        LOGW(WINFO,
             "{} [param] RM Delt Strag: (Neighber<{} && Occ<{}) || (ray>{} && Occ<{})",
             WJLog::getWholeSysTime(),
             m_iReMovingKoeff[0],
             m_iReMovingKoeff[1],
             m_iReMovingKoeff[2],
             m_iReMovingKoeff[3]);
        LOGW(WINFO,
             "{} [param] RM OverlapLevel {}",
             WJLog::getWholeSysTime(),
             m_iReMovingOverlapLevel);
    }

    void setModeDefault(int p_WorkMode, int p_ScenMode)
    {
        switch ((WorkMode)p_WorkMode)
        {
            case WorkMode::InitMapMode:
            case WorkMode::ContMapMode:
            case WorkMode::UpdateMapMode: m_bReMoving = true; break;
            case WorkMode::LocatMode: m_bReMoving = false; break;
            default: break;
        }
    }
    void setModeDefault_4L(int p_WorkMode, int p_ScenMode)
    {
        switch ((WorkMode)p_WorkMode)
        {
            case WorkMode::InitMapMode:
            case WorkMode::ContMapMode:
            case WorkMode::UpdateMapMode: m_bReMoving = true; break;
            case WorkMode::LocatMode: m_bReMoving = false; break;
            default: break;
        }
        m_bReMovingRawCloud = true;
        m_iReMovingRawGrow = 3;
        m_fReMovingGrid = 0.15;
        m_iReMovingWindowSize = 15;
        m_iReMovingKoeff[0] = 5;
        m_iReMovingKoeff[1] = 8;
        m_iReMovingKoeff[2] = 2;
        m_iReMovingKoeff[3] = 6;
        m_iReMovingOverlapLevel = 2;
    }
    bool m_bReMoving;          /**< 动态过滤开关 */
    bool m_bReMovingRawCloud;  /**< 动态过滤原始点云 */
    int m_iReMovingRawGrow;    /**< 动态过滤原始点云膨胀 */
    float m_fReMovingGrid;     /**< 动态过滤栅格大小 */
    int m_iReMovingWindowSize; /**< 动态过滤窗口帧数 */
    uint m_iReMovingKoeff[4]; /**< 动态过滤参数 (Neighber<{0} && Occ<{1}) || (ray>{2} && Occ<{3})*/
    int m_iReMovingOverlapLevel; /**< 穿透级别 */
} s_RemovingConfig;

/** 建图参数模板 */
typedef struct s_MappingConfig
{
    // default
    s_MappingConfig() : m_stReMv()
    {
        setDefaultMatch();
        m_bEnableLoop = true;
        optimiz_o3D = false;
    }
    // 默认为室外匹配参数
    void setDefaultMatch()
    {
        for (int i = 0; i < 5; ++i)
        {
            this->m_match[i].m_uiPlaneMaxPoints = 12;
            this->m_match[i].m_fPlaneMaxRadius = 3.5;
            this->m_match[i].m_fPlaneMeanDiff = 0.15;
            this->m_match[i].m_fLine2DRadius = 0.2;
            this->m_match[i].m_fLine2DMarkRadius = std::max(0.8 - 0.15 * i, 0.2);
            this->m_match[i].m_uiLineMinPoints = 4;
            this->m_match[i].m_fLineMaxZDiff = 2.0;
            this->m_match[i].m_fMaxDist = std::max(0.8 - 0.2 * i, 0.2);
            this->m_match[i].m_bSampleMatch = true;
            this->m_match[i].m_vfPlanePCA.resize(2);
            this->m_match[i].m_vfPlanePCA[0] = 2;
            this->m_match[i].m_vfPlanePCA[1] = 0.995;
        }
    }
    void setModeDefault(int p_WorkMode, int p_ScenMode)
    {
        switch ((ScenMode)p_ScenMode)
        {
            // 室内匹配参数
            case ScenMode::INDOOR:
                for (int i = 0; i < 5; ++i)
                {
                    this->m_match[i].m_uiPlaneMaxPoints = 8;
                    this->m_match[i].m_fPlaneMaxRadius = 2.5;
                    this->m_match[i].m_fPlaneMeanDiff = 0.15;
                    this->m_match[i].m_fLine2DRadius = 0.2;
                    this->m_match[i].m_fLine2DMarkRadius = std::max(0.8 - 0.3 * i, 0.2);
                    this->m_match[i].m_uiLineMinPoints = 4;
                    this->m_match[i].m_fLineMaxZDiff = 2.0;
                    this->m_match[i].m_fMaxDist = std::max(0.4 - 0.1 * i, 0.2);
                    this->m_match[i].m_bSampleMatch = true;

                    this->m_match[i].m_vfPlanePCA.resize(2);
                    this->m_match[i].m_vfPlanePCA[0] = 2;
                    this->m_match[i].m_vfPlanePCA[1] = 0.995;
                }
                break;
            case ScenMode::OUTDOOR:
                // 默认参数
                setDefaultMatch();
                break;
            case ScenMode::VACROUS:
                // 默认参数
                setDefaultMatch();
                break;
            default: break;
        }
        m_stReMv.setModeDefault(p_WorkMode, p_ScenMode);
    }
    s_MatcherConfig m_match[5];  // 建图匹配参数
    bool m_bEnableLoop;          //是否启动回环
    bool optimiz_o3D;
    s_RemovingConfig m_stReMv;
} s_MappingConfig;

/** 定位参数模板 */
typedef struct s_LoctConfig
{
    // default
    s_LoctConfig()
    {
        setDefaultMatch();
        this->m_manual.m_uiPlaneMaxPoints = 15;
        this->m_manual.m_fPlaneMaxRadius = 10.0;
        this->m_manual.m_fPlaneMeanDiff = 0.15;
        this->m_manual.m_fLine2DRadius = 0.6;
        this->m_manual.m_fLine2DMarkRadius = 0.8;
        this->m_manual.m_uiLineMinPoints = 3;
        this->m_manual.m_fLineMaxZDiff = 10.0;
        this->m_manual.m_fMaxDist = 1.0;
        this->m_manual.m_bSampleMatch = true;
        this->m_manual.m_vfPlanePCA.resize(2);
        this->m_manual.m_vfPlanePCA[0] = 2;
        this->m_manual.m_vfPlanePCA[1] = 0.995;
        this->optimiz_o3D = false;
        this->m_iManualFrameCnt = 20;
        this->m_fTwistSmoothRes = 360.0;  // 默认速度平均
    }
    // 默认为室外匹配参数
    void setDefaultMatch()
    {
        this->m_match.m_uiPlaneMaxPoints = 10;
        this->m_match.m_fPlaneMaxRadius = 3.0;
        this->m_match.m_fPlaneMeanDiff = 0.15;
        this->m_match.m_fLine2DRadius = 0.4;
        this->m_match.m_fLine2DMarkRadius = 0.4;
        this->m_match.m_uiLineMinPoints = 4;
        this->m_match.m_fLineMaxZDiff = 1.0;
        this->m_match.m_fMaxDist = 0.8;
        this->m_match.m_bSampleMatch = true;

        this->m_match.m_vfPlanePCA.resize(2);
        this->m_match.m_vfPlanePCA[0] = 2;
        this->m_match.m_vfPlanePCA[1] = 0.995;
    }
    void setModeDefault(int p_WorkMode, int p_ScenMode)
    {
        switch ((ScenMode)p_ScenMode)
        {
            // 室内匹配参数
            case ScenMode::INDOOR:
                this->m_match.m_uiPlaneMaxPoints = 10;
                this->m_match.m_fPlaneMaxRadius = 3.0;
                this->m_match.m_fPlaneMeanDiff = 0.15;
                this->m_match.m_fLine2DRadius = 0.2;
                this->m_match.m_fLine2DMarkRadius = 0.2;
                this->m_match.m_uiLineMinPoints = 4;
                this->m_match.m_fLineMaxZDiff = 2.0;
                this->m_match.m_fMaxDist = 0.4;
                this->m_match.m_bSampleMatch = true;

                this->m_match.m_vfPlanePCA.resize(2);
                this->m_match.m_vfPlanePCA[0] = 2;
                this->m_match.m_vfPlanePCA[1] = 0.995;
                break;
            case ScenMode::OUTDOOR:
                // 默认参数
                setDefaultMatch();
                break;
            case ScenMode::VACROUS:
                // 默认参数
                setDefaultMatch();
                break;
            default: break;
        }
    }
    s_MatcherConfig m_match;   // 定位匹配参数
    s_MatcherConfig m_manual;  // 手动定位匹配参数
    bool optimiz_o3D;
    int m_iManualFrameCnt;    // 手动设定位姿匹配帧数
    float m_fTwistSmoothRes;  // 速度平均策略阈值
} s_LoctConfig;

/** 回环参数模板 */
typedef struct s_LoopCheckConfig
{
    s_LoopCheckConfig()
    {
        m_iSector = 60;
        m_iRing = 20;
        m_dRadius = 50;
        m_fSCthr = 0.75;
        m_iOptTimes = 15;
        m_fMatchAveDistanceThr = 0.065;
        m_fMatchMinNumPercentThr = 0.35;
        m_fVerifyScoreThr = 0.7;
        m_fVerifyMatchPercentThr = 0.65;
        m_fParticleFilterRangeX = 1.5;
        m_fParticleFilterRangeY = 1.5;
        m_fParticleFilterRangeZ = 0.02;
        m_fParticleFilterRangeA = 8;
        m_iParticleNums = 300;
        m_iParticleFilterTimes = 0;
    }

    // SC参数
    int m_iSector;    /**< SC提取sector*/
    int m_iRing;      /**< SC提取Ring*/
    double m_dRadius; /**< SC提取雷达扫描范围*/
    //回环匹配参数
    int m_iOptTimes; /**< 优化次数*/
    float m_fSCthr;
    float m_fMatchAveDistanceThr;   /**< 匹配成功面点平均距离阈值*/
    float m_fMatchMinNumPercentThr; /**< 最小匹配比例*/
    float m_fVerifyScoreThr;        /**< 回环检测匹配概率校验得分阈值*/
    float m_fVerifyMatchPercentThr; /**< 回环检测匹配比例校验阈值*/
    //局部AMCL参数
    int m_iParticleNums;           /**< AMCL粒子数量*/
    int m_iParticleFilterTimes;    /**< 粒子滤波次数*/
    float m_fParticleFilterRangeX; /**< 粒子滤波X方向范围*/
    float m_fParticleFilterRangeY; /**< 粒子滤波Y方向范围*/
    float m_fParticleFilterRangeZ; /**< 粒子滤波Z方向范围*/
    float m_fParticleFilterRangeA; /**< 粒子滤波YAW范围(度)*/

} s_LoopCheckConfig;

}  // namespace wj_slam