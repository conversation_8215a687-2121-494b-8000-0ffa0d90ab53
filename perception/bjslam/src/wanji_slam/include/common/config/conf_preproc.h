/*
 * @Description:
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2021-12-01 15:29:27
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-03-23 09:21:05
 */
#pragma once
#include "conf_lidar.h"
#include <vector>

namespace wj_slam {

/** 特征提取-角面点提取参数模板 */
class CurvFeatureConfig {
  public:
    struct st_basic
    {
        st_basic() : m_fMaxBondaryAng(-1), m_fCornAngRange{-1, -1}, m_fMinSurfAng(-1) {}
        float m_fMaxBondaryAng;              // 判为边界拖尾点最大角度
        std::vector<float> m_fCornAngRange;  // 判为角点的最大角度
        float m_fMinSurfAng;                 // 判为面点的最小角度
        std::vector<bool> hasSet()
        {
            std::vector<bool> l_setCond(3, false);
            if (m_fMaxBondaryAng > 0)
                l_setCond[0] = true;
            if (m_fCornAngRange[0] > 0)
                l_setCond[1] = true;
            if (m_fMinSurfAng > 0)
                l_setCond[2] = true;
            return l_setCond;
        }
    };
    struct st_scale
    {
        st_scale()
            : m_fSmallScale{-1, -1}, m_fFeatHighRange{-1, -1}, m_fCornDistRange{-1, -1},
              m_fGroundZSpace(-1)
        {
        }
        std::vector<float> m_fSmallScale;     // 小物体的宽度限制
        std::vector<float> m_fFeatHighRange;  // [m]最小可用特征高度
        std::vector<float> m_fCornDistRange;  // [m]最小角点距离
        float m_fGroundZSpace;                // 简化地面区间
        std::vector<bool> hasSet()
        {
            std::vector<bool> l_setCond(4, false);
            if (m_fSmallScale[0] > 0)
                l_setCond[0] = true;
            if (m_fFeatHighRange[0] < m_fFeatHighRange[1])
                l_setCond[1] = true;
            if (m_fCornDistRange[0] < m_fCornDistRange[1])
                l_setCond[2] = true;
            if (m_fGroundZSpace > 0)
                l_setCond[3] = true;
            return l_setCond;
        }
    };
    struct st_smooth
    {
        st_smooth()
            : m_bIntensitySmoothCheck(true), m_bLineSmoothCheck(true), m_uiMaxIntensityDiff(-1),
              m_fMaxLineAng(-1), m_fIntenDiffPercent(-1), m_fAngDiffPercent(-1)
        {
        }
        bool m_bIntensitySmoothCheck;  // 是否检查强度变化烈度
        bool m_bLineSmoothCheck;       // 是否检查角度变化烈度
        int m_uiMaxIntensityDiff;  // 相邻点的强度变化阈值，变化剧烈的点太多将删除分段
        float m_fMaxLineAng;  // 相邻点的最大角度，变化剧烈的点太多将删除分段
        float m_fIntenDiffPercent;  // 强度不平滑点的最大比例[0,1]
        float m_fAngDiffPercent;    // 位置不平滑点的最大比例[0,1]
        std::vector<bool> hasSet()
        {
            std::vector<bool> l_setCond(2, false);
            if (m_uiMaxIntensityDiff > 0 && m_fIntenDiffPercent > 0)
                l_setCond[0] = true;
            if (m_fMaxLineAng > 0 && m_fAngDiffPercent > 0)
                l_setCond[1] = true;
            return l_setCond;
        }
    };
    struct st_filter
    {
        st_filter()
            : m_bVerticalCornFilter(true), m_bUniSampleSurfFilter(true),
              m_uiMaxSurfPntNum(-1), m_fSampleRange{-1, -1, -1}, m_fSampleScale{-1, -1, -1, -1, -1},
              m_fCornCyldRadius(-1), m_fCornCyldMinHeight(-1), m_uiCornCyldMinPoints(-1)
        {
        }
        bool m_bVerticalCornFilter;         //角点垂直点数过滤
        bool m_bUniSampleSurfFilter;        //面点均匀采样过滤
        int m_uiMaxSurfPntNum;              //最大面点数限制
        std::vector<float> m_fSampleRange;  //面点均匀采样距离区间
        std::vector<float> m_fSampleScale;  //面点采样尺度
        float m_fCornCyldRadius;            //角点圆柱半径
        float m_fCornCyldMinHeight;         //角点圆柱最小高度
        int m_uiCornCyldMinPoints;          //角点圆柱最少点数
        std::vector<bool> hasSet()
        {
            std::vector<bool> l_setCond(3, false);
            if (m_uiMaxSurfPntNum > 0)
                l_setCond[0] = true;
            if (m_fSampleRange[0] > 0 && m_fSampleScale[0] > 0)
                l_setCond[1] = true;
            if (m_fCornCyldRadius > 0 && m_fCornCyldMinHeight > 0 && m_uiCornCyldMinPoints > 0)
                l_setCond[2] = true;
            return l_setCond;
        }
    };
    std::vector<int> m_vUseStat;  // 使用特征种类
    st_basic m_basic;
    st_scale m_scale;
    st_smooth m_smooth;
    st_filter m_filter;

  public:
    CurvFeatureConfig()
        : m_vUseStat{-1, 0, 0, 0, 0, 0, 0, 0}, m_basic(), m_scale(), m_smooth(), m_filter()
    {
    }
};

/** 特征提取-马路伢子-车道线提取参数模板 */
class CurbFeatureConfig {
  public:
    float m_fCurbHeight;  // 马路伢子高度
    float
        m_fAxisAngleDiff;  // 轴角差-车前进方向与轴角(单雷达为X轴，多雷达视点云拼接情况而定)的夹角,逆时针为正,0-360
    float m_fGroundClearance;  // 离地高度-单雷达为雷达安装高度，多雷达视点云拼接情况而定
  public:
    CurbFeatureConfig() : m_fCurbHeight(0.15), m_fAxisAngleDiff(90.0), m_fGroundClearance(99.9) {}
};

class TimeSyncSensorConfig {
  public:
    // 最大允许落后时间
    int m_iMaxTimeBehind;
    // 最大允许同步时间
    int m_iMaxTimeSync;
    // 最大允许发送延时时间
    int m_iMaxTimeDelay;

    TimeSyncSensorConfig()
    {
        m_iMaxTimeBehind = -55;
        m_iMaxTimeSync = SCAN_TIME_MS;
        m_iMaxTimeDelay = 200000000;
    }
};
class ImuConfig {
  public:
    Eigen::Vector3d m_vAngularVelNoise;
    ImuConfig()
    {
        m_vAngularVelNoise << 0.005, 0.007, 0.004;
    }
};

typedef struct s_PreprocConfig
{
    s_PreprocConfig() : m_curv(), m_curb(), m_sync(), m_imu(), m_iVisualCloud(1) {}
    CurvFeatureConfig m_curv;
    CurbFeatureConfig m_curb;
    TimeSyncSensorConfig m_sync;
    ImuConfig m_imu;
    int m_iVisualCloud;  // 使用全部点云-0 滤波点云-1 采样点云-2
} s_PreprocConfig;

}  // namespace wj_slam