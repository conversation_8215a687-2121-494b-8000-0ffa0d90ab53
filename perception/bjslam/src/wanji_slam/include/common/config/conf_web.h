/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2021-12-01 15:58:11
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-12-01 15:46:32
 */
#pragma once
#include "../type/type_device.h"
#include <mutex>
#include <string>
#include <wj_log.h>

namespace wj_slam {

/** Web服务器参数 */
typedef struct s_WebCfg
{
    s_WebCfg()
    {
        m_socket.m_sLocalIP = "**************";
        m_socket.m_uiLocalPort = 10088;
    }
    // 打印
    void log()
    {
        LOGW(WINFO, "{} [param] WebServer IP: {}", WJLog::getWholeSysTime(), m_socket.m_sLocalIP);
        LOGW(WINFO,
             "{} [param] WebServer Port: {}",
             WJLog::getWholeSysTime(),
             m_socket.m_uiLocalPort);
    }

    s_DevCfg m_socket;

} s_WebCfg;

}  // namespace wj_slam