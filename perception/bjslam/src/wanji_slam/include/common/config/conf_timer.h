/*
 * @Description:
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2022-02-08 17:47:57
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-11-03 17:32:56
 */
#pragma once
#include "../type/type_status.h"
#include <iostream>
#include <math.h>
#include <mutex>
#include <string>
#include <sys/time.h>
#include <unistd.h>
typedef struct timeval Timeval;
typedef double timeMs;
namespace wj_slam {

struct sTimeval : protected Timeval
{
  protected:
    mutable std::mutex m_mLock_;
    double doubleTime = 0.0;
    void toDouble_()
    {
        doubleTime = this->tv_sec + this->tv_usec * 1e-6;
    }

  public:
    sTimeval()
    {
        this->reset();
    }
    /**
     * @brief Construct a new s Timeval object
     * @param isRest false: 当前时间 ; true: 初始化为0
     *
     */
    sTimeval(bool isRest)
    {
        if (!isRest)
        {
            this->now();
        }
        else
        {
            this->reset();
        }
    }
    /**
     * @brief Construct a new s Timeval object
     * @param p_in
     *
     */
    sTimeval(const sTimeval& p_in)
    {
        *this = p_in;
    }
    sTimeval& operator=(const sTimeval& p_in)
    {
        std::lock_guard<std::mutex> l_loc(m_mLock_);
        this->tv_sec = p_in.tv_sec;
        this->tv_usec = p_in.tv_usec;
        return *this;
    }
    sTimeval& operator=(const struct timeval& p_in)
    {
        std::lock_guard<std::mutex> l_loc(m_mLock_);
        this->tv_sec = p_in.tv_sec;
        this->tv_usec = p_in.tv_usec;
        return *this;
    }
    sTimeval& operator-=(const sTimeval& p_in)
    {
        *this = *this - p_in;
        return *this;
    }
    bool operator>(const sTimeval& p_in) const
    {
        return this->getDiffMs(p_in) > 0 ? true : false;
    }

    sTimeval operator-(const sTimeval& p_in) const
    {
        std::lock_guard<std::mutex> l_loc(m_mLock_);
        sTimeval timeOut;
        auto usdiff = this->tv_usec - p_in.tv_usec;
        if (usdiff < 0)
        {
            timeOut.tv_sec = this->tv_sec - p_in.tv_sec - 1;
            timeOut.tv_usec = 1e6 + usdiff;
        }
        else
        {
            timeOut.tv_sec = this->tv_sec - p_in.tv_sec;
            timeOut.tv_usec = usdiff;
        }
        return timeOut;
    }
    /**
     * @brief Get the Diff Ms object
     * @param p_in 被比较的时间
     * @return timeMs 输出偏差值,ms单位
     *
     */
    timeMs getDiffMs(const sTimeval& p_in) const
    {
        sTimeval timeOut;
        timeMs outdata;
        timeOut = *this - p_in;
        outdata = timeOut.tv_sec * 1e3 + (double)timeOut.tv_usec / 1000.0;
        return outdata;
    }
    /**
     * @brief Get the Diff Us object
     * @param p_in 被比较的时间
     * @return timeMs 输出偏差值,us单位
     *
     */
    timeMs getDiffUs(const sTimeval& p_in) const
    {
        sTimeval timeOut;
        timeMs outdata;
        timeOut = *this - p_in;
        outdata = timeOut.tv_sec * 1e6 + (double)timeOut.tv_usec;
        return outdata;
    }
    /**
     * @brief 通过sec和us设置时间
     * @param tv_sec 秒
     * @param tv_usec 微秒
     *
     */
    void set(const long int& tv_sec, const long int& tv_usec)
    {
        std::lock_guard<std::mutex> l_loc(m_mLock_);
        this->tv_sec = tv_sec;
        this->tv_usec = tv_usec;
    }
    /**
     * @brief 获取当前系统时间
     *
     */
    void now()
    {
        std::lock_guard<std::mutex> l_loc(m_mLock_);
        gettimeofday(this, NULL);
    }
    /**
     * @brief 时间重置为0
     *
     */
    void reset()
    {
        std::lock_guard<std::mutex> l_loc(m_mLock_);
        this->tv_sec = 0;
        this->tv_usec = 0;
    }
    /**
     * @brief 获取当前s时间
     * @return time_t
     *
     */
    time_t second() const
    {
        std::lock_guard<std::mutex> l_loc(m_mLock_);
        return this->tv_sec;
    }
    /**
     * @brief 获取当前s时间引用,可用于赋值
     * @return time_t&
     *
     */
    time_t& second()
    {
        std::lock_guard<std::mutex> l_loc(m_mLock_);
        return this->tv_sec;
    }
    /**
     * @brief 获取当前us时间
     * @return suseconds_t
     *
     */
    suseconds_t subsecond() const
    {
        std::lock_guard<std::mutex> l_loc(m_mLock_);
        return this->tv_usec;
    }
    /**
     * @brief 获取当前us时间引用,可用于赋值
     * @return suseconds_t&
     *
     */
    suseconds_t& subsecond()
    {
        std::lock_guard<std::mutex> l_loc(m_mLock_);
        return this->tv_usec;
    }
    /**
     * @brief 获取当前时间,double类型
     * @return double
     *
     */
    double data()
    {
        std::lock_guard<std::mutex> l_loc(m_mLock_);
        this->toDouble_();
        return this->doubleTime;
    }
    /**
     * @brief 打印当前时间信息
     * @param str 标签
     *
     */
    void printf(const std::string str)
    {
        std::lock_guard<std::mutex> l_loc(m_mLock_);
        this->toDouble_();
        std::cerr << str << " time = " << std::to_string(this->doubleTime) << std::endl;
    }
};
/**
 * @function: sleepMs
 * @description: 延时函数ms
 * @param {int} p_iDelay:延时时间
 * @input: p_iDelay
 * @output: null
 * @return {*}
 * @others: null
 */
inline void sleepMs(int p_iDelay)
{
    usleep(p_iDelay * 1000);
}

inline std::string getTimeNowStr()
{
    time_t timep;
    time(&timep);
    char tmp[64];
    strftime(tmp, sizeof(tmp), "%Y_%m_%d_%H_%M_%S", localtime(&timep));
    return tmp;
}

/**
 * @class TimeConfig
 * @brief 系统的基准时间类
 *
 */
typedef struct s_TimeConfig
{
  protected:
    mutable std::mutex m_mLock_;
    sTimeval m_sTimeSystemBase; /**< 系统启动时间*/
    sTimeval m_sTimeSensorBase; /**< 系统中所有设备偏移时间,为第一个传感器传入时间,用于设备同步*/
    TimeSource m_eTimeSource; /**< 系统中所有设备同步的时钟源*/
    bool m_bSensorBaseInited; /**< 系统设备偏移时间是否设置,设置后才能进行同步*/

  public:
    s_TimeConfig()
    {
        this->m_sTimeSystemBase.reset();
        this->m_sTimeSensorBase.reset();
        this->m_eTimeSource = TimeSource::Local;
        this->m_bSensorBaseInited = false;
    }

    s_TimeConfig& operator=(const s_TimeConfig& p_in)
    {
        std::lock_guard<std::mutex> l_loc(m_mLock_);
        this->m_sTimeSystemBase = p_in.m_sTimeSystemBase;
        this->m_sTimeSensorBase = p_in.m_sTimeSensorBase;
        this->m_eTimeSource = p_in.m_eTimeSource;
        this->m_bSensorBaseInited = p_in.m_bSensorBaseInited;
        return *this;
    }
    /**
     * @brief Set the Sensor Base Time object
     * @param p_in 时间
     * @note 为了防止在多传感器应用中,网络数据发生抢占,将第一个授时时间向前推10s.
     *
     */
    void setSensorBaseTime(const sTimeval& p_in)
    {
        std::lock_guard<std::mutex> l_loc(m_mLock_);
        if (!m_bSensorBaseInited)
        {
            this->m_sTimeSensorBase = p_in;
            this->m_sTimeSensorBase.second() -= 10;
            m_bSensorBaseInited = true;
        }
    }
    /**
     * @brief Set the System Base Time object.
     *
     */
    void setSystemBaseTime(void)
    {
        std::lock_guard<std::mutex> l_loc(m_mLock_);
        this->m_sTimeSystemBase.now();
    }
    /**
     * @brief 查寻传感器偏移量是否已经设置
     * @return true
     * @return false
     *
     */
    bool isSensorTimeSet() const
    {
        std::lock_guard<std::mutex> l_loc(m_mLock_);
        return m_bSensorBaseInited;
    }
    void resetSensorBaseTime(bool p_bFlag)
    {
        m_bSensorBaseInited = p_bFlag;
    }
    /**
     * @brief Get the Sensor Base Time object
     * @return sTimeval
     *
     */
    sTimeval getSensorBaseTime() const
    {
        std::lock_guard<std::mutex> l_loc(m_mLock_);
        return m_sTimeSensorBase;
    }
    /**
     * @brief Get the System Base Time object
     * @return sTimeval
     *
     */
    sTimeval getSystemBaseTime() const
    {
        std::lock_guard<std::mutex> l_loc(m_mLock_);
        return m_sTimeSystemBase;
    }
    /**
     * @brief Set the Time Source object
     * @param ts
     *
     */
    void setTimeSource(const TimeSource& ts)
    {
        std::lock_guard<std::mutex> l_loc(m_mLock_);
        this->m_eTimeSource = ts;
    }
    /**
     * @brief 获取时间源
     * @return TimeSource
     *
     */
    TimeSource timeSource() const
    {
        std::lock_guard<std::mutex> l_loc(m_mLock_);
        return this->m_eTimeSource;
    }
    /**
     * @brief Get the Time Now, ms单位
     * @return timeMs
     *
     */
    timeMs getTimeNowMs()
    {
        std::lock_guard<std::mutex> l_loc(m_mLock_);
        sTimeval timenow(0);
        return timenow.getDiffMs(this->m_sTimeSystemBase);
    }

} s_TimeConfig;

}  // namespace wj_slam