/**
 * @file time_consuming.hpp
 * <AUTHOR>
 * @brief  各模块耗时统计
 * @version 1.0
 * @date 2023-10-17
 * @copyright Copyright (c)2023 <PERSON><PERSON>
 */

#pragma once

// local
#include "common_ex.h"
#include "wj_log.h"
// std
#include <condition_variable>
#include <fstream>
#include <mutex>
#include <sstream>
#include <string>
#include <thread>

class TimeDataInfo {
  public:
    TimeDataInfo();
    TimeDataInfo(const std::string& p_sDescription);
    ~TimeDataInfo();

    void getData(int& p_iCount, double& p_dAvg, double& p_dMax, double& p_dMin);
    void setData(double p_dTime);

    inline void setDesc(const std::string& p_sDesc)
    {
        c_sDescription_ = p_sDesc;
    }
    inline std::string getDesc()
    {
        return c_sDescription_;
    }

  private:
    double c_dAverage_;
    double c_dMin_;
    double c_dMax_;
    double c_dSum_;
    int c_iCount_;
    std::string c_sDescription_;

    // std::mutex c_mutex_;
};

TimeDataInfo::TimeDataInfo()
    : c_dAverage_(0.0), c_dMin_(1000000.0), c_dMax_(0.0), c_dSum_(0.0), c_iCount_(0),
      c_sDescription_("")
{
}

TimeDataInfo::TimeDataInfo(const std::string& p_sDescription)
    : c_dAverage_(0.0), c_dMin_(0.0), c_dMax_(0.0), c_dSum_(0.0), c_iCount_(0),
      c_sDescription_(p_sDescription)
{
}

TimeDataInfo::~TimeDataInfo() {}

void TimeDataInfo::getData(int& p_iCount, double& p_dAvg, double& p_dMax, double& p_dMin)
{
    // std::lock_guard<std::mutex> lck(c_mutex_);
    p_iCount = c_iCount_;
    p_dAvg = c_dAverage_;
    p_dMax = c_dMax_;
    p_dMin = c_dMin_;
}

void TimeDataInfo::setData(double p_dTime)
{
    // std::lock_guard<std::mutex> lck(c_mutex_);
    c_iCount_++;
    c_dSum_ += p_dTime;
    c_dMax_ = std::max(c_dMax_, p_dTime);
    c_dMin_ = std::min(c_dMin_, p_dTime);
    c_dAverage_ = (c_dSum_ / c_iCount_);
}

#define MOLUDE_CNT 9
#define COST_TIME TimeConsuming::getInstance()
#define COST_TIME_RECODE                                                                           \
    if (COST_TIME)                                                                                 \
    {                                                                                              \
        COST_TIME->record();                                                                       \
    }
#define COST_TIME_UNPACK(cost)                                                                     \
    if (COST_TIME)                                                                                 \
    {                                                                                              \
        COST_TIME->pushUnPackT(cost);                                                              \
    }
#define COST_TIME_FEATURE(cost)                                                                    \
    if (COST_TIME)                                                                                 \
    {                                                                                              \
        COST_TIME->pushFeatureT(cost);                                                             \
    }
#define COST_TIME_MARK(cost)                                                                       \
    if (COST_TIME)                                                                                 \
    {                                                                                              \
        COST_TIME->pushMarkT(cost);                                                                \
    }
#define COST_TIME_CURB(cost)                                                                       \
    if (COST_TIME)                                                                                 \
    {                                                                                              \
        COST_TIME->pushCurbT(cost);                                                                \
    }
#define COST_TIME_ODOM(cost)                                                                       \
    if (COST_TIME)                                                                                 \
    {                                                                                              \
        COST_TIME->pushOdomT(cost);                                                                \
    }
#define COST_TIME_LOCTION(cost)                                                                    \
    if (COST_TIME)                                                                                 \
    {                                                                                              \
        COST_TIME->pushLocT(cost);                                                                 \
    }
#define COST_TIME_MATCH(cost)                                                                      \
    if (COST_TIME)                                                                                 \
    {                                                                                              \
        COST_TIME->pushMatchT(cost);                                                               \
    }
#define COST_TIME_SUBMAP(cost)                                                                     \
    if (COST_TIME)                                                                                 \
    {                                                                                              \
        COST_TIME->pushSubmapT(cost);                                                              \
    }
#define COST_TIME_IVOX(cost)                                                                       \
    if (COST_TIME)                                                                                 \
    {                                                                                              \
        COST_TIME->pushIVoxT(cost);                                                                \
    }

class TimeConsuming {
  public:
    static TimeConsuming* getInstance();
    ~TimeConsuming();

    void record();

    inline void pushUnPackT(double p_dTime)
    {
        c_vMoludeCosts_[0].setData(p_dTime);
    }
    inline void pushFeatureT(double p_dTime)
    {
        c_vMoludeCosts_[1].setData(p_dTime);
    }
    inline void pushMarkT(double p_dTime)
    {
        c_vMoludeCosts_[2].setData(p_dTime);
    }
    inline void pushCurbT(double p_dTime)
    {
        c_vMoludeCosts_[3].setData(p_dTime);
    }
    inline void pushOdomT(double p_dTime)
    {
        c_vMoludeCosts_[4].setData(p_dTime);
    }
    inline void pushLocT(double p_dTime)
    {
        c_vMoludeCosts_[5].setData(p_dTime);
    }
    inline void pushMatchT(double p_dTime)
    {
        c_vMoludeCosts_[6].setData(p_dTime);
    }
    inline void pushSubmapT(double p_dTime)
    {
        c_vMoludeCosts_[7].setData(p_dTime);
    }
    inline void pushIVoxT(double p_dTime)
    {
        c_vMoludeCosts_[8].setData(p_dTime);
    }

  private:
    TimeConsuming();
    void run_();
    void init_();
    void saveData_();

  private:
    std::vector<TimeDataInfo> c_vMoludeCosts_;
    std::vector<int> c_vCount_;
    std::string c_sPath;

    bool c_bRun_;
    std::thread c_thread_;
    std::mutex c_mutex_;
    std::condition_variable c_cond_;
};

TimeConsuming* TimeConsuming::getInstance()
{
    if (wj_slam::SYSPARAM::getIn()->m_fae.m_iLogLevel >= 4)
    {
        static TimeConsuming instance;
        return &instance;
    }
    else
    {
        return nullptr;
    }
}

void TimeConsuming::record()
{
    static int l_iCnt = 0;
    l_iCnt++;
    if (l_iCnt % 100)
    {
        return;
    }
    std::unique_lock<std::mutex> lck(c_mutex_);
    c_cond_.notify_one();
}

TimeConsuming::TimeConsuming() : c_bRun_(true)
{
    init_();
    c_thread_ = std::thread(&TimeConsuming::run_, this);
}

void TimeConsuming::run_()
{
    while (c_bRun_)
    {
        std::unique_lock<std::mutex> lck(c_mutex_);
        c_cond_.wait(lck);
        if (c_bRun_)
        {
            saveData_();
        }
    }
}

void TimeConsuming::init_()
{
    const static std::string l_descs[MOLUDE_CNT] = {
        "unpackT", "featureT", "markT", "curbT", "odomT", "locT", "matchT", "submapT", "iVoxT"};

    c_vMoludeCosts_.resize(MOLUDE_CNT);
    for (int i = 0; i < MOLUDE_CNT; i++)
    {
        c_vMoludeCosts_[i].setDesc(l_descs[i]);
        c_vCount_.push_back(0);
    }

    wj_slam::SYSPARAM* l_sysParam = wj_slam::SYSPARAM::getIn();
    std::string fileName = "time_cost.log";
    c_sPath = l_sysParam->m_fae.m_sLogPath + fileName;
    std::fstream l_file;
    l_file.open(c_sPath.c_str(), std::ios_base::out | std::ios_base::app);
    if (l_file.is_open())
    {
        l_file << "Module"
               << " Index"
               << " AverageT"
               << " MaxT"
               << " MinT" << std::endl;
        l_file.close();
    }
}

TimeConsuming::~TimeConsuming()
{
    c_bRun_ = false;
    c_cond_.notify_all();
    if (c_thread_.joinable())
    {
        c_thread_.join();
    }
}

void TimeConsuming::saveData_()
{
    int l_iCount = 0;
    double l_dAverage = 0.0;
    double l_dMax = 0.0;
    double l_dMin = 0.0;
    std::string l_sModuleName = "";

    std::fstream l_file;
    l_file.open(c_sPath.c_str(), std::ios_base::out | std::ios_base::app);
    if (l_file.is_open())
    {
        for (int i = 0; i < MOLUDE_CNT; i++)
        {
            c_vMoludeCosts_[i].getData(l_iCount, l_dAverage, l_dMax, l_dMin);
            if (l_iCount != c_vCount_[i])
            {
                c_vCount_[i] = l_iCount;
                l_sModuleName = c_vMoludeCosts_[i].getDesc();
                l_file << l_sModuleName << " " << l_iCount << " " << l_dAverage << " " << l_dMax
                       << " " << l_dMin << std::endl;
            }
        }
        l_file.close();
    }
}