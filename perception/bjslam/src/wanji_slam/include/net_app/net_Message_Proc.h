/*
 * @Author: lin<PERSON><PERSON><PERSON>
 * @Date: 2021-05-18 13:13:41
 * @LastEditTime: 2022-06-08 13:02:42
 * @LastEditors: <PERSON><PERSON>
 * @Description: In User Settings Edit
 * @FilePath: /catkin_ws/src/wanji_net/include/message_Proc.h
 */
#pragma once

#include "protocol.h"
#include <std_msgs/ByteMultiArray.h>
#include <std_msgs/Int32MultiArray.h>

#pragma region "ROS"
#include <ros/ros.h>
#pragma endregion

namespace wj_slam {
class NetMessagePro {
  public:
    NetMessagePro(boost::function<bool(int)> setWorkModeCb, ros::NodeHandle& p_nh);
    ~NetMessagePro();
    void shutDown();

  private:
    void handler_(const std_msgs::Int32MultiArrayConstPtr& p_msg);

    void pubMsg_(char* p_pcBuf, int p_iLen);

  public:
    void pubMsg_Test();  //自己发送消息测试

  private:
    ros::NodeHandle& c_nh_;
    ros::Subscriber c_subNetMsg_;  //订阅协议消息

    ros::Publisher c_pubNetMsg_;      //发布消息
    ros::Publisher c_pubNetMsgTest_;  //自测模拟，发布消息

    bool c_bNetStaus_;
    Protocol* c_pProc_;
    int c_iSockFd_ = -1, c_iNewFd_ = -1;
    s_NetMsg* c_pstNetMsg_ = NULL;
    void procMsg_();
};
}  // namespace wj_slam
