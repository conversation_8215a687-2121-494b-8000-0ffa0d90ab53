/**
 * @file sick_Protocol.h
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @brief Sick协议处理类
 * @version 1.1
 * @date 2023-06-16
 * @copyright Copyright (c)2023 <PERSON><PERSON>
 */
#pragma once
// local
#include "common.h"
#include "common/common_ex.h"
#include "tic_toc.h"
#include "algorithm/location/mark_location/mark/marktype.h"
// std
#include <thread>

using namespace wj_slam;
typedef struct AscProtocol
{
    char m_acCmdType[10];
    char m_acCmd[20];
    char m_acParam[6][20];
} s_AscProtocol;

class SickProtocol {
  public:
    typedef boost::function<void(char*, int)> SendMsgCb;
    typedef boost::function<bool(int)> SetWorkModeCb;
    typedef std::shared_ptr<std::thread> ThreadPtr;

    /**
     * @brief 构造函数
     * @param sendCallback    发送函数回调
     * @param netMsg          数据缓存
     * @param setWorkModeCb   设置工作模式回调
     *
     */
    SickProtocol(SendMsgCb sendCallback, s_NetMsg& netMsg, SetWorkModeCb setWorkModeCb);

    /**
     * @brief 析构函数
     *
     */
    ~SickProtocol();

    /**
     * @brief 关闭线程
     *
     */
    void shutDown();

    /**
     * @brief 初始化相关标志
     *
     */
    void init();

    /**
     * @brief 获取Sick协议数据
     * @param p_lock 互斥锁
     * @code
     *
     * @endcode
     * @return [u_char*] \n
     * [数据首地址]
     *
     */
    u_char* getSICKCMD(int&, std::mutex& p_lock);

    /**
     * @brief 解析Sick指令
     * @param p_pucBuf  待解析数据首地址
     * @param p_iLen    数据长度
     *
     */
    void seprateSICKCmd(u_char* p_pucBuf, int p_iLen);

    /**
     * @brief 分析Sick指令，获取回复数据
     * @param l_pcBuf 待回复数据首地址
     * @code
     *
     * @endcode
     * @return [int] \n
     * [待回复数据长度]
     *
     */
    int selectSICKProtocol(char* l_pcBuf);

  private:
    /**
     * @brief 发送位子
     * @param p_iPoseModel 位姿标志 0/1
     *
     */
    void sendPose_(int p_iPoseModel);

    /**
     * @brief 根据四元数求yaw角
     * @param q         四元数
     * @param p_bIn2PI  输出yaw角范围 false: [-M_PI, M_PI]; true: [0, 2*M_PI]
     * @code
     *
     * @endcode
     * @return [double] \n
     * [yaw角，单位弧度]
     *
     */
    double toYaw_(const Eigen::Quaterniond q, bool p_bIn2PI = true);

    /**
     * @brief 获取某个时间戳和当前时刻的差值(ms)
     * @param p_iTimetamp 待比较时间戳
     * @code
     *
     * @endcode
     * @return [float] \n
     * [时间戳差值，单位 ms]
     *
     */
    float getTimeStampDiff(int p_iTimetamp);

    /**
     * @brief 更新预测位姿
     * @param p_sPose6D 待更新位姿
     *
     */
    void updateProbPose(s_PoseWithTwist& p_sPose6D);

    /**
     * @brief 发送数据线程
     *
     */
    void sendThread_();

    /**
     * @brief 保存输出位姿至文件
     * @param p_sCurrPose 待保存位姿
     * @param p_sFilePath 文件路径
     *
     */
    void saveOutPoseInFile_(s_RobotPos& p_sCurrPose, std::string p_sFilePath);

    /**
     * @brief 保存转移前输出位姿至文件
     * @param p_sCurrPose 待保存位姿
     * @param p_sFilePath 文件路径
     *
     */
    void saveOutPoseNoTransInFile_(s_POSE6D& p_sCurrPose, std::string p_sFilePath);

    /**
     * @brief 保存雷达速度至文件
     * @param p_f32Vel      待保存速度
     * @param l_tInterval   相邻速度时间差值
     * @param p_sFilePath   文件路径
     *
     */
    void saveLidarVelInFile_(s_fuseTwist* p_f32Vel, float l_tInterval, std::string p_sFilePath);

    /**
     * @brief 输出位姿时间对齐
     * @param p_stOutPose   对齐后位姿
     * @param p_sLidarPose  对齐前位姿
     * @param p_iPoseModel  位姿模式
     * @code
     *
     * @endcode
     * @return [float] \n
     * [对齐前后时间差值]
     *
     */
    float
    outPoseTimeAlign_(s_RobotPos& p_stOutPose, s_PoseWithTwist& p_sLidarPose, int p_iPoseModel);

    /**
     * @brief 十六进制字符串转四字节整型
     * @param Str 输入字符串
     * @code
     *
     * @endcode
     * @return [u32] \n
     * [转换后的四字节整型数]
     *
     */
    u32 hexStr2U32_(void* Str);

    /**
     * @brief 发送位姿信息
     *
     */
    void sendPoseInfoBySICK_(s_RobotPos&, uint32_t);

    /**
     * @brief 发送位姿确认指令
     *
     */
    void sendPoseACKCmdBySICK_(int);

  private:
    s_NetMsg& c_stNetMsg_;        /**< sick协议数据缓存引用 */
    SYSPARAM* c_stSysParam_;      /**< 系统参数指针 */
    SendMsgCb sendCallback_;      /**< 发送函数回调 */
    SetWorkModeCb setWorkModeCb_; /**< 设置工作模式回调 */
    ThreadPtr c_sendThr_;         /**< 发送线程 */
    s_AscProtocol c_askProtocol_; /**< sick协议解析结构体 */
    s_PoseWithTwist c_LastPose_;  /**< 上一次位姿数据 */
    TicToc c_tVel_;               /**< 速度时间戳记录 */
    char c_cTail_;                /**< sick协议帧尾 */
    bool c_bRun_;                 /**< 运行标志 */
    bool c_bHasRecvFlag_;         /**< 接收标志 */
    bool c_bIsPose1_;             /**< pose标志 */
    bool c_bIsFirst_;             /**< 首包标志 */
};
