/*
 * @Author: l<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-18 13:15:07
 * @LastEditTime: 2022-06-08 11:29:41
 * @LastEditors: <PERSON><PERSON>
 * @Description: In User Settings Edit
 * @FilePath: /catkin_ws/src/wanji_net/src/net_Message_Proc.cpp
 */

#include "net_app/net_Message_Proc.h"
#include "std_msgs/ByteMultiArray.h"
#include <vector>

//配置工作模式
unsigned char c_aData1[34] = {0xFF, 0xAA, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00,
                              0x00, 0x01, 0x01, 0x01, 0x0B, 0x00, 0x00, 0x00, 0x00,
                              0x00, 0x00, 0x00, 0x00, 0x0A, 0x0E, 0x00, 0x00, 0x00,
                              0x00, 0x00, 0x00, 0x00, 0x1F, 0xEE, 0xEE};
unsigned char c_aData2[34] = {0xFF, 0xAA, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00,
                              0x00, 0x01, 0x02, 0x01, 0x0B, 0x00, 0x00, 0x00, 0x00,
                              0x00, 0x00, 0x00, 0x00, 0x0A, 0x01, 0x00, 0x00, 0x00,
                              0x00, 0x00, 0x00, 0x00, 0x1D, 0xEE, 0xEE};

unsigned char c_aDataGetMapReso[34] = {0xFF, 0xAA, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00,
                                       0x00, 0x01, 0x01, 0x01, 0x0B, 0x00, 0x00, 0x00, 0x00,
                                       0x00, 0x00, 0x00, 0x00, 0x0A, 0x0E, 0x00, 0x00, 0x00,
                                       0x00, 0x00, 0x00, 0x00, 0x10, 0xEE, 0xEE};

namespace wj_slam {

NetMessagePro::NetMessagePro(boost::function<bool(int)> setWorkModeCb, ros::NodeHandle& p_nh)
    : c_nh_(p_nh)
{
    // c_odom_ = p_odom;
    // c_location_ = p_location;
    c_subNetMsg_ = c_nh_.subscribe<std_msgs::Int32MultiArray>(
        "/web_from", 20, &NetMessagePro::handler_, this);  //
    c_pubNetMsg_ = c_nh_.advertise<std_msgs::Int32MultiArray>("/web_to", 1);
    c_pubNetMsgTest_ = c_nh_.advertise<std_msgs::Int32MultiArray>("/web_from", 10);

    c_pstNetMsg_ = new s_NetMsg();
    c_pProc_ = new Protocol(
        *c_pstNetMsg_, boost::bind(&NetMessagePro::pubMsg_, this, _1, _2), setWorkModeCb);
    // c_pProc_->setOdom(c_odom_);
    // c_pProc_->setLocation(c_location_);
    c_pProc_->procCmdInThread();
}
/*  */
NetMessagePro::~NetMessagePro()
{
    delete c_pProc_;
    c_pProc_ = NULL;

    delete c_pstNetMsg_;
    c_pstNetMsg_ = NULL;
}
void NetMessagePro::shutDown()
{
    c_pProc_->shutDown();
}
void NetMessagePro::handler_(const std_msgs::Int32MultiArrayConstPtr& p_msg)
{
    printf("recv msg\n");
    // char l_acBufTmp[4096];
    int l_iDataNum = p_msg->data.size();

    if (l_iDataNum > 0 && l_iDataNum <= NET_LENGTH_MAX)
    {
        char* l_cTmp = (char*)&(p_msg->data[0]);
        if ((c_pstNetMsg_->m_uiDataLen + l_iDataNum) <= (NET_LENGTH_MAX))
        {
            // memcpy(&c_pstNetMsg_->m_aucBuf[c_pstNetMsg_->m_uiDataLen], l_cTmp, l_iDataNum);
            // test
            for (int i = 0; i < l_iDataNum; ++i)
            {
                c_pstNetMsg_->m_aucBuf[i + c_pstNetMsg_->m_uiDataLen] = u_char(p_msg->data[i]);
            }

            c_pstNetMsg_->m_uiDataLen += l_iDataNum;
            c_pstNetMsg_->m_uiDataLen %= NET_LENGTH_MAX;
        }
        else
        {
            int offset = 0;
            // memcpy(&c_pstNetMsg_->m_aucBuf[c_pstNetMsg_->m_uiDataLen],
            //        l_cTmp, NET_LENGTH_MAX - c_pstNetMsg_->m_uiDataLen);

            for (int i = 0; i < (int)(NET_LENGTH_MAX - c_pstNetMsg_->m_uiDataLen); ++i)
            {
                c_pstNetMsg_->m_aucBuf[i + c_pstNetMsg_->m_uiDataLen] = u_char(p_msg->data[i]);
            }

            offset = NET_LENGTH_MAX - c_pstNetMsg_->m_uiDataLen;
            // memcpy(&c_pstNetMsg_->m_aucBuf[0], &l_cTmp[offset], l_iDataNum - offset);
            for (int i = 0; i < (l_iDataNum - offset); ++i)
            {
                c_pstNetMsg_->m_aucBuf[i] = u_char(p_msg->data[i + offset]);
            }

            c_pstNetMsg_->m_uiDataLen = l_iDataNum - offset;
        }
    }
}

void NetMessagePro::pubMsg_(char* p_pcBuf, int p_iLen)
{
    printf("pub msg\n");

    std_msgs::Int32MultiArray msg_array;
    // u_char* tmp=(u_char*)p_pcBuf;
    // std::vector<u_char> vec_(tmp, &tmp[p_iLen]);

    msg_array.data.resize(p_iLen);
    // memcpy(&(msg_array.data[0]), (int32_t *)p_pcBuf, p_iLen);
    for (int i = 0; i < (p_iLen); ++i)
    {
        msg_array.data[i] = u_char(p_pcBuf[i]);
    }

    c_pubNetMsg_.publish(msg_array);
}

void NetMessagePro::pubMsg_Test()
{
    // FF AA 00 1E 00 00 00 00 00 00 01 01 01 0B 00 00 00 00 00 00 00 00 0A 01 00 00 00 00 00 00 00
    // 1F EE EE
    // std_msgs::UInt8MultiArray msg_array;

    // std::vector<u_char> vec={0xFF, 0xAA, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00,
    //                    0x00, 0x01, 0x02, 0x01, 0x0B, 0x00, 0x00, 0x00, 0x00,
    //                    0x00, 0x00, 0x00, 0x00, 0x0A, 0x01, 0x00, 0x00};
    // msg_array.data=vec;
    // c_pubNetMsg_.publish(msg_array);

    static int flag = 1;
    if (flag)
    {
        std_msgs::Int32MultiArray msg;
        for (int i = 0; i < 34; i++)
        {
            msg.data.push_back(c_aDataGetMapReso[i]);
        }

        c_pubNetMsgTest_.publish(msg);
        sleep(10);
        flag = 0;
    }
}

}  // namespace wj_slam
