/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2021-05-16 16:15:05
 * @LastEditTime: 2022-12-01 16:43:11
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /720slam/src/wanji_slam/src/net_app/protocol.cpp
 */
#include "master/webApp/protocolProc.h"

ProtocolProc::ProtocolProc(s_NetMsg& p_stWebMsg,
                           std::mutex& l_mtxLockWeb_,
                           s_NetMsg& p_stSlamMsg,
                           std::mutex& l_mtxLockSlam_,
                           boost::function<void(char*, int)> sendRosCb_,
                           boost::function<void(char*, int)> sendTcpCb_,
                           boost::function<int(int, int)> setCb)
    : c_stWebMsg_(p_stWebMsg), c_stSlamMsg_(p_stSlamMsg), c_sendRosCb_(sendRosCb_),
      c_sendTcpCb_(sendTcpCb_), c_setCb_(setCb), c_mtxLockWeb_(l_mtxLockWeb_),
      c_mtxLockSlam_(l_mtxLockSlam_)
{
    c_pWebProtocol_.reset(new wj_slam::WebProtocol(sendTcpCb_, sendRosCb_, c_setCb_));

    std::thread l_t(&ProtocolProc::handlerWebMsg_, this);
    l_t.detach();
}

/**
 * @description: 根据协议头FFEE 协议尾EE 确定协议类型 同时修改偏移量
 * @param {int&} p_iOffset 被修改的当前偏移量
 * @return {*}
 * @other:
 */
WJCMDTYPE
ProtocolProc::recognizeProtocolType_(s_NetMsg& p_msg_, uint32_t& p_uiOffset, std::mutex& p_lock)
{
    uint32_t l_uiOffsetTmp = p_uiOffset;
    int l_iCMDLen = 0;

    while (l_uiOffsetTmp != p_msg_.m_uiDataLen)
    {
        if ((u_char)(p_msg_.m_aucBuf[l_uiOffsetTmp]) == 0xFF
            && (u_char)(p_msg_.m_aucBuf[(l_uiOffsetTmp + 1) % NET_LENGTH_MAX]) == 0xAA)
        {
            l_iCMDLen = p_msg_.m_aucBuf[(l_uiOffsetTmp + 2) % NET_LENGTH_MAX] << 8
                        | p_msg_.m_aucBuf[(l_uiOffsetTmp + 3) % NET_LENGTH_MAX];
            if (p_msg_.m_aucBuf[(l_uiOffsetTmp + l_iCMDLen + 2) % NET_LENGTH_MAX] == 0xEE
                && p_msg_.m_aucBuf[(l_uiOffsetTmp + l_iCMDLen + 3) % NET_LENGTH_MAX] == 0xEE)
            {
                std::lock_guard<std::mutex> l_mtx(p_lock);
                // 退出信号: 外部主动将以下两值相等
                if (p_uiOffset == p_msg_.m_uiDataLen)
                    return WJCMDTYPE::NOMATCHPROTOCOL;
                p_uiOffset = l_uiOffsetTmp;

                switch (p_msg_.m_aucBuf[(l_uiOffsetTmp + c_uiProtocolBtyeNum) % NET_LENGTH_MAX])
                {
                    case 0x0A: return WJCMDTYPE::CMD_SLAVER; break;
                    case 0x0B: return WJCMDTYPE::CMD_MASTER; break;
                    case 0x0C: return WJCMDTYPE::CMD_DEVICE; break;
                    case 0x0D: return WJCMDTYPE::CMD_PARAM; break;
                    case 0x0E: return WJCMDTYPE::CMD_PRVT; break;
                    default:
                        LOGWEB(WERROR,
                               "{} master not match prot {:#X}",
                               WJLog::getWholeSysTime(),
                               p_msg_.m_aucBuf[(l_uiOffsetTmp + c_uiProtocolBtyeNum)
                                               % NET_LENGTH_MAX]);
                        return WJCMDTYPE::NOMATCHPROTOCOL;
                        break;
                }
            }
            else
            {
                LOGWEB(WERROR, "{} master length fail", WJLog::getWholeSysTime());
                l_uiOffsetTmp++;
                l_uiOffsetTmp %= NET_LENGTH_MAX;
            }
        }
        else
        {
            l_uiOffsetTmp++;
            l_uiOffsetTmp %= NET_LENGTH_MAX;
        }
    }
    return WJCMDTYPE::NOMATCHPROTOCOL;
}

/**
 * @description: 拆分数组 ROS发送
 * @param {*}
 * @return {*}
 * @other:
 */
void ProtocolProc::handlerSlamMsg_()
{
    int l_acGetBufLen = 0;
    u_char l_aucGetBuf[200] = {0};
    char l_acReturnBuf[1460] = {0};
    WJCMDTYPE l_ProtocolType = WJCMDTYPE::NOMATCHPROTOCOL;
    c_bProcThrSlam_ = true;
    c_uiSendSlamMsgOffset_ = 0;
    while (c_bProcThrSlam_)
    {
        while (c_uiSendSlamMsgOffset_ != c_stSlamMsg_.m_uiDataLen)
        {
            if (!c_bProcThrSlam_)
                break;
            l_ProtocolType =
                recognizeProtocolType_(c_stSlamMsg_, c_uiSendSlamMsgOffset_, c_mtxLockSlam_);

            if (l_ProtocolType == WJCMDTYPE::NOMATCHPROTOCOL)
            {
                // 防止陷入死循环
                // 考虑到ROSMsg不存在数据缺失情况 以下操作合理
                // TCP不适合，因可能存在半包数据，以下操作将丢失1包
                if (c_uiSendSlamMsgOffset_ != c_stSlamMsg_.m_uiDataLen)
                    c_uiSendSlamMsgOffset_ = (c_uiSendSlamMsgOffset_ + 1) % NET_LENGTH_MAX;
            }
            else
            {
                l_acGetBufLen =
                    getAllCMD(c_stSlamMsg_, c_uiSendSlamMsgOffset_, l_aucGetBuf, c_mtxLockSlam_);
                switch (l_ProtocolType)
                {
                    case WJCMDTYPE::CMD_SLAVER:
                        c_pWebProtocol_->selectSlaverProtocol(
                            l_aucGetBuf, l_acGetBufLen, l_acReturnBuf);
                        break;
                    case WJCMDTYPE::CMD_MASTER:
                        c_pWebProtocol_->selectMasterProtocol(
                            l_aucGetBuf, l_acGetBufLen, l_acReturnBuf);
                        break;
                    case WJCMDTYPE::CMD_DEVICE:
                        c_pWebProtocol_->selectDeviceProtocol(
                            l_aucGetBuf, l_acGetBufLen, l_acReturnBuf);
                        break;
                    case WJCMDTYPE::CMD_PARAM:
                        c_pWebProtocol_->selectParamProtocol(
                            l_aucGetBuf, l_acGetBufLen, l_acReturnBuf);
                        break;
                    case WJCMDTYPE::CMD_PRVT:
                        c_pWebProtocol_->selectPrivateProtocol(
                            l_aucGetBuf, l_acGetBufLen, l_acReturnBuf);
                        break;
                    default: break;
                }
            }
        }
        usleep(1000);
    }
    c_bProcThrSlam_ = false;
    c_bProcThrSlamOver_ = true;
}

void ProtocolProc::handlerWebMsg_()
{
    int l_acGetBufLen = 0;
    int l_acReturnBufLen = 0;
    u_char l_aucGetBuf[200] = {0};
    char l_acReturnBuf[1460] = {0};
    c_bProcThrWeb_ = true;
    while (c_bProcThrWeb_)
    {
        while (c_uiSendWebMsgOffset_ != c_stWebMsg_.m_uiDataLen)
        {
            if (!c_bProcThrWeb_)
                break;
            WJCMDTYPE l_ProtocolType =
                recognizeProtocolType_(c_stWebMsg_, c_uiSendWebMsgOffset_, c_mtxLockWeb_);
            if (l_ProtocolType == WJCMDTYPE::NOMATCHPROTOCOL)
            {
                // 防止陷入死循环
                // 考虑到ROSMsg不存在数据缺失情况 以下操作合理
                // TCP不适合，因可能存在半包数据，以下操作将丢失1包
                if (c_uiSendWebMsgOffset_ != c_stWebMsg_.m_uiDataLen)
                    c_uiSendWebMsgOffset_ = (c_uiSendWebMsgOffset_ + 1) % NET_LENGTH_MAX;
            }
            else
            {
                l_acGetBufLen =
                    getAllCMD(c_stWebMsg_, c_uiSendWebMsgOffset_, l_aucGetBuf, c_mtxLockWeb_);
                switch (l_ProtocolType)
                {
                    case WJCMDTYPE::CMD_SLAVER:
                        c_pWebProtocol_->selectSlaverProtocol(
                            l_aucGetBuf, l_acGetBufLen, l_acReturnBuf);
                        break;
                    case WJCMDTYPE::CMD_MASTER:
                        c_pWebProtocol_->selectMasterProtocol(
                            l_aucGetBuf, l_acGetBufLen, l_acReturnBuf);
                        break;
                    case WJCMDTYPE::CMD_DEVICE:
                        c_pWebProtocol_->selectDeviceProtocol(
                            l_aucGetBuf, l_acGetBufLen, l_acReturnBuf);
                        break;
                    case WJCMDTYPE::CMD_PARAM:
                        c_pWebProtocol_->selectParamProtocol(
                            l_aucGetBuf, l_acGetBufLen, l_acReturnBuf);
                        break;
                    case WJCMDTYPE::CMD_PRVT:
                        c_pWebProtocol_->selectPrivateProtocol(
                            l_aucGetBuf, l_acGetBufLen, l_acReturnBuf);
                        break;
                    default: break;
                }
            }
        }
        usleep(1000);
    }
    c_bProcThrWeb_ = false;
    c_bProcThrWebOver_ = true;
}

/**
 * @description: 从p_iOffset开始确定并复制完整WJ/WEB协议，并修改p_iOffset
 * @param {uint32_t&} p_iOffset 循环数组的偏移量
 * @param {u_char*} p_pcBufCMD 提取的协议（待填充）
 * @param {mutex&} p_lock
 * @return {int} 提取协议的长度
 * @other: 此函数区别与wj/sick::getCMD 提取完整协议内容涵帧头帧尾 且不进行CRC
 */
int ProtocolProc::getAllCMD(s_NetMsg& p_sMsg,
                            uint32_t& p_iOffset,
                            u_char* p_pcBufCMD,
                            std::mutex& p_lock)
{
    int l_iSta = -1, l_iEnd = -1;
    int l_iCmdLen = 0;
    l_iCmdLen = TOUINT16(p_sMsg.m_aucBuf[(p_iOffset + 2) % NET_LENGTH_MAX],
                         p_sMsg.m_aucBuf[(p_iOffset + 3) % NET_LENGTH_MAX]);
    if (p_sMsg.m_aucBuf[p_iOffset] == 0xFF
        && p_sMsg.m_aucBuf[(p_iOffset + 1) % NET_LENGTH_MAX] == 0xAA
        && p_sMsg.m_aucBuf[(p_iOffset + l_iCmdLen + 2) % NET_LENGTH_MAX] == 0xEE
        && p_sMsg.m_aucBuf[(p_iOffset + l_iCmdLen + 3) % NET_LENGTH_MAX] == 0xEE)
    {
        // 区别于wj/sick 这里须加入帧头帧尾
        l_iSta = p_iOffset % NET_LENGTH_MAX;
        l_iEnd = (l_iSta + l_iCmdLen + 4) % NET_LENGTH_MAX;
    }

    if (l_iSta != -1 && l_iEnd != -1)
    {
        // +4 区别于wj/sick 这里须加入帧头帧尾且不须校验CRC
        int l_iDataProcLen = l_iCmdLen + 4;
        if (l_iSta > l_iEnd)
        {
            memcpy(p_pcBufCMD, &p_sMsg.m_aucBuf[l_iSta], NET_LENGTH_MAX - l_iSta);
            if (l_iEnd > 0)
                memcpy(&p_pcBufCMD[NET_LENGTH_MAX - l_iSta], p_sMsg.m_aucBuf, l_iEnd);
        }
        else
            memcpy(p_pcBufCMD, &p_sMsg.m_aucBuf[l_iSta], l_iDataProcLen);

        {
            std::lock_guard<std::mutex> l_mtx(p_lock);
            if (p_iOffset == p_sMsg.m_uiDataLen)
                return 0;
            p_iOffset += l_iDataProcLen;
            p_iOffset %= NET_LENGTH_MAX;
        }
        return l_iDataProcLen;
    }
    return 0;
}