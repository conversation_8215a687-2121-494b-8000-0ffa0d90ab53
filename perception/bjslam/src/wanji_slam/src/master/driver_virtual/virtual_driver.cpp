/*
 * @Description: 离线驱动： 解析PCAP/订阅ROS 通过TCP/UDP重现在线
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2022-03-21 17:07:51
 * @LastEditors: <PERSON><PERSON><PERSON>u <EMAIL>
 * @LastEditTime: 2022-12-01 16:39:35
 */
#include "master/driver_virtual/virtual_driver.h"

namespace wj_slam {
/**
 * @description: 初始化驱动-支持在线切换，即重置
 * @param {int} p_iDriverMode 驱动模式
 * @return {*}
 * @other:
 */
void VirtualDriver::initDriver_()
{
    // 全部析构
    for (uint32_t l_iDevId = 0; l_iDevId < c_masterParamPtr->m_slam->m_devList.size(); l_iDevId++)
    {
        if (c_vpInput_[l_iDevId])
        {
            c_vpInput_[l_iDevId]->shutdown();
            c_vpInput_[l_iDevId] = nullptr;
        }
    }
    resetTimeAlignThread_(true);
    // 全部实例化
    for (uint32_t l_iDevId = 0; l_iDevId < c_masterParamPtr->m_slam->m_devList.size(); l_iDevId++)
    {
        c_vpToken_[l_iDevId]->reset();

        // 根据设备类型-实例化
        if (c_masterParamPtr->m_slam->m_devList[l_iDevId]->m_sDevType == "WLR720A"
            || c_masterParamPtr->m_slam->m_devList[l_iDevId]->m_sDevType == "WLR720C"
            || c_masterParamPtr->m_slam->m_devList[l_iDevId]->m_sDevType == "WLR720F"
            || c_masterParamPtr->m_slam->m_devList[l_iDevId]->m_sDevType == "WLR720F_NP"
            || c_masterParamPtr->m_slam->m_devList[l_iDevId]->m_sDevType == "WLR720FCW"
            || c_masterParamPtr->m_slam->m_devList[l_iDevId]->m_sDevType == "WLR720FCW_NP")
        {
            c_vpInput_[l_iDevId].reset(
                new InputOffLine720(l_iDevId,
                                    c_vpToken_[l_iDevId],
                                    boost::bind(&VirtualDriver::processScanCb, this, _1, _2)));
        }
        else if (c_masterParamPtr->m_slam->m_devList[l_iDevId]->m_sDevType == "WheelSick")
        {
            c_vpInput_[l_iDevId].reset(
                new InputOffLineSick(l_iDevId,
                                     c_vpToken_[l_iDevId],
                                     boost::bind(&VirtualDriver::processScanCb, this, _1, _2)));
        }
        else
        {
            c_vpInput_[l_iDevId] = nullptr;
            wjPrintError("devList Init Fail: DevType Not Match: ",
                         c_masterParamPtr->m_slam->m_devList[l_iDevId]->m_sDevType.c_str());
        }

        //启动
        if (c_vpInput_[l_iDevId])
        {
            c_vbDevValid_[l_iDevId] = c_vpInput_[l_iDevId]->isValidPcap();
            if (!c_vbDevValid_[l_iDevId])
            {
                // c_vpInput_[l_iDevId] = nullptr;
                continue;
            }
            c_vpInput_[l_iDevId]->playStart();
        }
    }
}

/**
 * @description: 启动/关闭 数据对齐线程
 * @param {bool} p_bIsRestart 是否启动
 * @return {*}
 * @other:
 */
void VirtualDriver::resetTimeAlignThread_(bool p_bIsRestart)
{
    // 开->先关闭后启动
    if (c_bRunOlpThr)
    {
        c_bRunOlpThr = false;
        while (1)
        {
            if (c_bRunOlpThrOver)
                break;
            usleep(1000);
        }
    }
    if (p_bIsRestart)
    {
        c_vRawDataOffline_.clear();
        std::thread l_thread(&VirtualDriver::dataTimeAlign_, this);
        l_thread.detach();
    }
}

/**
 * @description: 根据数据涵盖的时间戳，手动排序控制其顺序并通过TCP/UDP发送
 * @param {*}
 * @return {*}
 * @other:
 */
void VirtualDriver::dataTimeAlign_()
{
    c_bRunOlpThr = true;
    c_bRunOlpThrOver = false;
    struct timeval timeLast, pcapTimeLast;
    // debug
    int l_num[3] = {0, 0, 0};

    float l_afTime[10] = {0.0};
    TicToc l_localT;
    TicToc l_recordT;
    int l_iLastBufSize = 0;    // 上次排序时的队列长度
    int l_iUsleepPktDiff = 0;  // 包间隔休眠时间差值 单位us

    float l_fUsCorrectT = 0;  //时间修正差  = 实际消耗T-理论休眠T

    sTimeval l_stPktTimeLast;  // 上一个Pkt的时间戳
    memset(&l_stPktTimeLast, 0, sizeof(l_stPktTimeLast));

    // offline等待buf满足最低排序要求 获取最旧的时间作为首包基准值
    while (c_bRunOlpThr)
    {
        if (isEnoughSort_())
        {
            uint32_t l_iNowBufSize = c_vRawDataOffline_.size();
            // 排序时间 只对当前size长度做排序
            std::stable_sort(c_vRawDataOffline_.begin(),
                             c_vRawDataOffline_.begin() + l_iNowBufSize,
                             [](const RawDataPair& a, const RawDataPair& b) {
                                 return (b.second->m_pcapHeader.m_time
                                         > a.second->m_pcapHeader.m_time);
                             });
            l_stPktTimeLast = c_vRawDataOffline_.front().second->m_pcapHeader.m_time;
            break;
        }
        sleep(0);
    }
    LOGFAE(WINFO, "");
    LOGFAE(WWARN, "离线驱动初始化完毕,请点击Web端[数据回放]按钮启动播放!");
    LOGFAE(WINFO, "");
    while (c_bRunOlpThr)
    {
        if (c_bIsStart_)
        {
            l_localT.tic();
            while (c_bRunOlpThr)
            {
                if (isEnoughSort_())
                    break;
                else
                    usleep(10);
            }
            if (c_bRunOlpThr && c_bIsStart_)
            {
                cLock.lock();
                uint32_t l_iNowBufSize = c_vRawDataOffline_.size();
                cLock.unlock();
                if (true)
                {
                    std::stable_sort(c_vRawDataOffline_.begin(),
                                     c_vRawDataOffline_.begin() + l_iNowBufSize,
                                     [](const RawDataPair& a, const RawDataPair& b) {
                                         return (b.second->m_pcapHeader.m_time
                                                 > a.second->m_pcapHeader.m_time);
                                     });
                }
                // 提取时间差
                uint32_t l_uiDevId = c_vRawDataOffline_.front().first;
                RawDataPtr l_pThisMsg = c_vRawDataOffline_.front().second;
                // 计算两次发送的合理时间差(微秒)，并基于播放速率进行修改
                l_iUsleepPktDiff =
                    (float)(l_pThisMsg->m_pcapHeader.m_time.getDiffUs(l_stPktTimeLast))
                    * (10.0 / (float)c_masterParamPtr->m_uiPlayBagRate); 
                if (l_iUsleepPktDiff > 10)
                    usleep(l_iUsleepPktDiff);
                // 处理帧数据,确保 c_vpInput_ 已实例化且正常运行
                if (c_vpInput_[l_uiDevId] && !c_vpInput_[l_uiDevId]->isShutdown())
                    c_vpInput_[l_uiDevId]->sendMsg(l_pThisMsg);
                else
                    wjPrintError("offline input shutdown: ", l_uiDevId);
                // 从队列中移除已发送的帧
                cLock.lock();
                if (c_vRawDataOffline_.size())
                    c_vRawDataOffline_.erase(c_vRawDataOffline_.begin());
                else
                    LOGFAE(WERROR, "virtual driver erase empty");
                cLock.unlock();

                // 更新令牌数量
                c_vpToken_[l_uiDevId]->updateToken();
                l_stPktTimeLast = l_pThisMsg->m_pcapHeader.m_time;
            }
        }
        else
            usleep(500000);
    }
    c_bRunOlpThrOver = true;
}

/**
 * @description: 各个设备数据量是否均满足排序需求
 * @param {*}
 * @return {bool} true: 满足排序
 * @other: 每个设备数据量应不低于2
 */
bool VirtualDriver::isEnoughSort_()
{
    bool l_bOut = false;
    for (uint32_t l_iDev = 0; l_iDev < c_masterParamPtr->m_slam->m_devList.size(); ++l_iDev)
    {
        if (!c_vbDevValid_[l_iDev])
            continue;
        if (c_vpToken_[l_iDev] && c_vpToken_[l_iDev]->getTokenUseSize() >= 2)
            l_bOut = true;
        else
        {
            l_bOut = false;
            break;
        }
    }
    return l_bOut;
}

/**
 * @description: 是否须重新排序
 * @param {int} p_iNowSize 当前队列长度
 * @param {int&} p_iLastSize 上次队列长度
 * @return {*}
 * @other: 队列长度增加则须重新排序
 */
bool VirtualDriver::isNeedSort_(int p_iNowSize, int& p_iLastSize)
{
    bool l_bOut = false;
    if (p_iNowSize >= p_iLastSize)
        l_bOut = true;
    p_iLastSize = p_iNowSize;
    return l_bOut;
}

/**
 * @description: 回调函数-接收原始网络数据，等待压入权限，压入离线排序队列
 * @param {uint32_t} p_uiDevID 设备ID
 * @param {RawDataPtr&} p_pScanMsg 该设备的原始网络数据
 * @return {*}
 * @other:
 */
void VirtualDriver::processScanCb(uint32_t p_uiDevID, const RawDataPtr p_pScanMsg)
{
    if (!c_bShutdown_)
    {
        cLock.lock();
        TicToc l_t;
        RawDataPair l_pair = std::make_pair(p_uiDevID, p_pScanMsg);
        c_vRawDataOffline_.push_back(l_pair);
        cLock.unlock();
    }
}

VirtualDriver::VirtualDriver()
{
    c_masterParamPtr = s_masterCfg::getIn();
    if (c_masterParamPtr->m_slam->m_devList.size())
    {
        c_vpInput_.resize(c_masterParamPtr->m_slam->m_devList.size());
        c_vpToken_.resize(c_masterParamPtr->m_slam->m_devList.size());
        c_vbDevValid_.resize(c_masterParamPtr->m_slam->m_devList.size());
    }
    for (uint32_t l_iDev = 0; l_iDev < c_masterParamPtr->m_slam->m_devList.size(); ++l_iDev)
    {
        c_vpToken_[l_iDev].reset(new RateLimiterr(20));
    }
    c_vRawDataOffline_.reserve(c_vpToken_[0]->getTokenAllSize()
                               * c_masterParamPtr->m_slam->m_devList.size());
    // c_vRawDataOffline_.reserve(100);
    playStart();
}

VirtualDriver::~VirtualDriver(){};

}  // namespace wj_slam