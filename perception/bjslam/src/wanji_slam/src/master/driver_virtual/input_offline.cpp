/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2022-03-17 14:09:28
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-28 13:41:56
 */
#include "master/driver_virtual/input_offline.h"
#include <fstream>
#include <iomanip>
#include <iostream>

namespace wj_slam {

#pragma region 基类
InputOffLine::InputOffLine(
    uint32_t p_uiDevId,
    boost::shared_ptr<RateLimiterr> p_stRateL,
    boost::function<void(uint32_t, boost::shared_ptr<s_LIDAR_RAW_DATA_>&)> p_outCb_)
    : c_uiId(p_uiDevId), c_stRL_(p_stRateL), c_outCb_(p_outCb_)
{
    c_masterParamPtr = s_masterCfg::getIn();
    c_bIsOnlineMode = c_masterParamPtr->m_slam->m_bIsOnlineMode;
    c_uiPktDataMinLen = c_masterParamPtr->m_slam->m_devList[c_uiId]->m_uiDataMinLen;

    // 设置用于打印的设备名，此处要求默认dev最后一个为AGV
    if (c_uiId < c_masterParamPtr->m_slam->m_devList.size() - 1)
        c_sDevPrintfName_ = "雷达-" + c_masterParamPtr->m_slam->m_lidar[c_uiId].m_sLaserName;
    else
        c_sDevPrintfName_ = "AGV";

    // 设置PCAP完整路径
    c_stPcapInfo_.m_sPcapPath =
        setPcapFile_(c_masterParamPtr->m_sOffLineDataPath,
                     c_masterParamPtr->m_slam->m_devList[c_uiId]->m_sPcapName);
    // 使用外部指定的通信参数
    c_stPcapInfo_.m_sSrcIP = c_masterParamPtr->m_slam->m_devList[c_uiId]->m_sDevIP;
    c_stPcapInfo_.m_sDstIP = c_masterParamPtr->m_slam->m_devList[c_uiId]->m_sLocalIP;
    c_stPcapInfo_.m_uiSrcPort = c_masterParamPtr->m_slam->m_devList[c_uiId]->m_uiDevPort;
    c_stPcapInfo_.m_uiDstPort = c_masterParamPtr->m_slam->m_devList[c_uiId]->m_uiLocalPort;
}

InputOffLine::~InputOffLine() {}

/**
 * @description: 生成PCAP路径
 * @param {string} p_sDataFilesPath PCAP路径
 * @param {string} p_sPcapName PCAP包名 不涵后缀
 * @return {std::string} 返回 PCAP完整路径 默认以pcap后缀 文件不存在则 使用pcapng后缀
 * 但不再判断文件是否存在
 * @other:
 */
std::string InputOffLine::setPcapFile_(std::string p_sDataFilesPath, std::string p_sPcapName)
{
    std::string l_sPcapPath = "";
    std::string l_sDataFilesPath = p_sDataFilesPath;
    if ('/' != l_sDataFilesPath[l_sDataFilesPath.length() - 1])
        l_sDataFilesPath += "/";
    l_sPcapPath = l_sDataFilesPath + p_sPcapName + ".pcap";

    if (!isExistFileOrFolder(l_sPcapPath))
        l_sPcapPath = l_sPcapPath + "ng";
    return l_sPcapPath;
}

/**
 * @description: 打开PCAP文件 分析内部IP 并过滤
 * @param {string} p_sDataFilePath PCAP文件路径
 * @return {*}
 * @other:
 */
bool InputOffLine::pcapInit_(std::string p_sDataFilePath)
{
    std::lock_guard<std::mutex> l_mutex(c_mutexPcap_);
    if (c_pacp_)
    {
        pcap_close(c_pacp_);
        c_pacp_ = NULL;
    }

    if (c_bIsOnlineMode)
        return false;
    LOGFAE(WINFO, "离线[{}]数据包 [{}] 加载中...", c_sDevPrintfName_, p_sDataFilePath);
    if (!isExistFileOrFolder(p_sDataFilePath))
    {
        if (c_sDevPrintfName_ != "AGV")
        {
            // c_masterParamPtr->m_slam->m_fae.setErrorCode("E1");
            LOGFAE(WERROR,
                   "离线[{}]数据包加载异常 | 文件不存在,请确认文件是否已存在!",
                   c_sDevPrintfName_);
        }
        else
            LOGFAE(WWARN, "离线[{}]数据包加载异常 | 文件不存在,请检查!", c_sDevPrintfName_);
        return false;
    }

    if ((c_pacp_ = pcap_open_offline(p_sDataFilePath.c_str(), errbuf_)) == NULL)
    {
        if (c_sDevPrintfName_ != "AGV")
        {
            c_masterParamPtr->m_slam->m_fae.setErrorCode("E1");
            // c_masterParamPtr->m_slam->m_fae.setErrorCode("E2");
            LOGFAE(WERROR,
                   "离线数据包[{}]加载异常 | 文件打开失败,请确认文件未被更改过后缀名!",
                   c_sDevPrintfName_);
        }
        else
            LOGFAE(WWARN, "离线数据包[{}]加载异常 | 文件打开失败,请检查!", c_sDevPrintfName_);
        return false;
    }
    LOGFAE(WINFO, "离线[{}]数据包加载成功", c_sDevPrintfName_);
    return pcapFilter();
}

/**
 * @description: 逐包分析数据包 根据协议确定数据偏移值 确定是来自设备端的数据包(相对SLAM而言
 * agv/lidar为远端)之后确定雷达协议类型 及网络数据
 * @param {const u_char*} p_ucData PCAP的某包数据
 * @param {uint32_t} p_iLen 数据完整长度 涵盖-协议头
 * @param {s_pcapInfo&} p_stPcapInfo 待填充的PCAP信息
 * @return {bool} true：分析协议头 成功
 * @other:
 * 基于WLR720协议 不能单纯通过FFEE头确定 后续可能更改：双向交互都使用FFAA头 ，须引入数据长度
 */
bool InputOffLine::analyPcapHeader(const u_char* p_ucData,
                                   uint32_t p_iLen,
                                   s_pcapInfo& p_stPcapInfo)
{
    if (!p_iLen || p_iLen < c_uiPktDataMinLen)
        return false;
    // 首先确定数据偏移
    if (!setOffset_(p_ucData, p_iLen, c_uiDataOffset))
        return false;

    uint32_t l_offset = 0;
    while (l_offset < p_iLen - 2)
    {
        // 找IP Header IPv4 [08 00] + headerLen [45]
        if (l_offset > 1 && *(p_ucData + l_offset) == 0x45 && *(p_ucData + l_offset - 1) == 0x00
            && *(p_ucData + l_offset - 2) == 0x08)
        {
            uint32_t l_totalLen = *(p_ucData + l_offset + 2) << 8 | *(p_ucData + l_offset + 3);
            if (l_totalLen == p_iLen - l_offset)
            {
                if (*(p_ucData + l_offset + 9) == 0x11)
                    p_stPcapInfo.m_sProtocalMode = "UDP";
                else if (*(p_ucData + l_offset + 9) == 0x06)
                    p_stPcapInfo.m_sProtocalMode = "TCP";
                else
                    p_stPcapInfo.m_sProtocalMode = "ERROR";

                if (p_iLen > c_uiDataOffset)
                {
                    p_stPcapInfo.m_sSrcIP = std::to_string(*(p_ucData + l_offset + 12)) + "."
                                            + std::to_string(*(p_ucData + l_offset + 13)) + "."
                                            + std::to_string(*(p_ucData + l_offset + 14)) + "."
                                            + std::to_string(*(p_ucData + l_offset + 15));
                    p_stPcapInfo.m_sDstIP = std::to_string(*(p_ucData + l_offset + 16)) + "."
                                            + std::to_string(*(p_ucData + l_offset + 17)) + "."
                                            + std::to_string(*(p_ucData + l_offset + 18)) + "."
                                            + std::to_string(*(p_ucData + l_offset + 19));
                    p_stPcapInfo.m_uiSrcPort =
                        *(p_ucData + l_offset + 20) << 8 | *(p_ucData + l_offset + 21);
                    p_stPcapInfo.m_uiDstPort =
                        *(p_ucData + l_offset + 22) << 8 | *(p_ucData + l_offset + 23);
                    return true;
                }
            }
            else
                LOGDR(WERROR,
                      "{} lidar pcap ip-header totalLength: {} {} {}",
                      WJLog::getWholeSysTime(),
                      p_iLen,
                      l_totalLen,
                      l_offset);
        }
        l_offset++;
    }
    return false;
}

/**
 * @function: setOffset_
 * @description: 根据协议判断FF EE 获取 雷达数据 起始偏移
 * @param {const u_char*} pcap读取数据
 * @param {uint32_t} p_iLen 包长度
 * @param {uint32_t& } p_uiOffset 待修改 数据起始字节
 * @return {*} true 起始偏移非0  即成功  仅设置1次
 * @others: 基于WLR720协议 不能单纯通过FFEE头确定 后续可能更改：双向交互都使用FFAA头，须引入数据长度
 * c_uiPktDataMinLen
 */
bool InputOffLine::setOffset_(const u_char* p_ucData, uint32_t p_iLen, uint32_t& p_uiOffset)
{
#ifndef DEBUGPCAP
    if (p_uiOffset)
        return true;
    for (uint32_t i = 0; i < p_iLen - 1; i++)
    {
        // 找ff ee
        if (*(p_ucData + i) == 255 && *(p_ucData + i + 1) == 238)
        {
            p_uiOffset = i;
            break;
        }
    }
#else
    int l_numFFEE = 0;
    int l_iLastIdFFEE = 0;
    for (uint32_t i = 0; i < p_iLen - 1; i++)
    {
        // 找ff ee
        if (*(p_ucData + i) == 255 && *(p_ucData + i + 1) == 238)
        {
            if (!p_uiOffset)
            {
                p_uiOffset = i;
            }

            {
                if (!l_iLastIdFFEE)
                    l_iLastIdFFEE = i - 80;
                if (i - l_iLastIdFFEE == 80)
                    l_numFFEE++;
            }
            l_iLastIdFFEE = i;
        }
    }
    if (l_numFFEE != 15)
        printf("ffee num[%d] <15 %d | %d\n", c_uiAllPktNum, p_iLen, l_numFFEE);

    // debug
    if (p_uiOffset && l_numFFEE != 15)
    {
        if (p_iLen > p_uiOffset)
        {
            for (uint32_t i = 0; i < p_iLen - 1 - p_uiOffset; i++)
            {
                if (i % 80 == 0)
                    printf("\n");
                printf("%x ", *(p_ucData + p_uiOffset + i));
            }
            printf("\n");
        }
    }
#endif  // DEBUGPCAP

    if (p_uiOffset)
        return true;
    else
        return false;
}

/**
 * @description: 基于PCAP读取到的s_pcapInfo信息 初始化网络端口-ip配置
 * @param {s_pcapInfo&} p_stPcapInfo    基于PCAP包读取的网络配置
 * @param {sockaddr_in&} p_sMyAddr      待设置 设备网络配置 eg lidar/agv
 * @param {sockaddr_in&} p_sRemoteAddr  代设置 远端配置 SLAM
 * @return {*}
 * @other:
 */
void InputOffLine::netInit_(s_pcapInfo& p_stPcapInfo,
                            sockaddr_in& p_sMyAddr,
                            sockaddr_in& p_sRemoteAddr)
{
    // 本机地址 也就是设备地址 eg:lidar/agv
    memset(&p_sMyAddr, 0, sizeof(p_sMyAddr));
    p_sMyAddr.sin_family = AF_INET;
    p_sMyAddr.sin_port = htons(p_stPcapInfo.m_uiSrcPort);
    p_sMyAddr.sin_addr.s_addr = inet_addr(p_stPcapInfo.m_sSrcIP.c_str());

    // 远端地址 也就是SLAM端地址 (对应 lidarToPc  agvToPc)
    memset(&p_sRemoteAddr, 0, sizeof(p_sRemoteAddr));
    p_sRemoteAddr.sin_family = AF_INET;
    p_sRemoteAddr.sin_port = htons(p_stPcapInfo.m_uiDstPort);
    p_sRemoteAddr.sin_addr.s_addr = inet_addr(p_stPcapInfo.m_sDstIP.c_str());
}

/**
 * @description: 网络初始化 建立套接字 绑定端口 默认720Lidar UDP服务器
 * @param {sockaddr_in&} p_sMyAddr
 * @param {sockaddr_in&} p_sRemoteAddr
 * @return {*}
 * @other:
 */
void InputOffLine::netStart_(int& p_iFd, sockaddr_in& p_sMyAddr, sockaddr_in& p_sRemoteAddr)
{
    char ipAddr[100];
    inet_ntop(AF_INET, &(p_sMyAddr.sin_addr), ipAddr, sizeof(ipAddr));

    if (p_iFd != -1)
    {
        close(p_iFd);
        p_iFd = -1;
    }

    if ((p_iFd = socket(AF_INET, SOCK_DGRAM, 0)) == -1)
    {
        LOGDR(
            WERROR, "{} offline open socket failed: {}", WJLog::getWholeSysTime(), strerror(errno));
        return;
    }

    // 设置端口复用
    int l_iOpt = SOF_TIMESTAMPING_RX_SOFTWARE | SOF_TIMESTAMPING_SOFTWARE | 0;
    if (setsockopt(p_iFd, SOL_SOCKET, SO_TIMESTAMPING, &l_iOpt, sizeof(l_iOpt)))
    {
        LOGDR(WERROR,
              "{} offline setup_udp_server: setsockopt failed:  {}",
              WJLog::getWholeSysTime(),
              strerror(errno));
        return;
    }

    int l_iOpt2 = 1;
    if (setsockopt(p_iFd, SOL_SOCKET, SO_REUSEPORT, &l_iOpt2, sizeof(l_iOpt2)))
    {
        LOGDR(WERROR,
              "{} offline setup_udp_server: setsockopt2 failed:  {}",
              WJLog::getWholeSysTime(),
              strerror(errno));
        return;
    }

    // 设置超时
    struct timeval timeout;
    timeout.tv_sec = 1;   //秒
    timeout.tv_usec = 0;  //微秒
    if (setsockopt(p_iFd, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout)) == -1)
    {
        LOGDR(
            WERROR, "{} offline setsockopt failed: {}", WJLog::getWholeSysTime(), strerror(errno));
    }

    int ret = bind(p_iFd, (struct sockaddr*)&p_sMyAddr, sizeof(p_sMyAddr));
    if (ret < 0)
    {
        char ipAddr[100];
        inet_ntop(AF_INET, &(p_sMyAddr.sin_addr), ipAddr, sizeof(ipAddr));
        std::string l_sErr = strerror(errno);
        if (l_sErr == "Cannot assign requested address")
        {
            // c_masterParamPtr->m_slam->m_fae.setErrorCode("E3");
            LOGFAE(WERROR, "离线[{}]UDP服务器网络建立失败 | IP不存在,请检查!", c_sDevPrintfName_);
        }
        else
            LOGDR(WERROR,
                  "{} offline udp bind error: {} | Port {} Ip {}",
                  WJLog::getWholeSysTime(),
                  l_sErr,
                  ntohs(p_sMyAddr.sin_port),
                  ipAddr);
        return;
    }

    setNetStatus(true);

    std::thread prcomsg(&InputOffLine::procRecv, this);
    prcomsg.detach();
}

/**
 * @description: 连接TCP服务器 3s未连接则认为失败
 * @param {int&} p_iFd 网络句柄 须已建立socket
 * @param {sockaddr_in&} p_sRemoteAddr 服务器地址
 * @return {bool} TCP服务器连接成功
 * @other:
 */
bool InputOffLine::connectTcpServer_(int& p_iFd, sockaddr_in& p_sMyAddr, sockaddr_in& p_sRemoteAddr)
{
    if (p_iFd != -1)
    {
        close(p_iFd);
        p_iFd = -1;
    }
    // 建立套接字
    p_iFd = socket(PF_INET, SOCK_STREAM, 0);
    if (p_iFd == -1)
    {
        LOGDR(WERROR,
              "{} offline tcp open socket failed: {}",
              WJLog::getWholeSysTime(),
              strerror(errno));
        return false;
    }

    // 端口复用
    int l_iOpt2 = 1;
    if (setsockopt(p_iFd, SOL_SOCKET, SO_REUSEPORT, &l_iOpt2, sizeof(l_iOpt2)))
    {
        LOGDR(WERROR,
              "{} offline tcp reuse port error: {}",
              WJLog::getWholeSysTime(),
              strerror(errno));
        return false;
    }

    // 绑定端口
    if (bind(p_iFd, (sockaddr*)&p_sMyAddr, sizeof(sockaddr)) == -1)
    {
        std::string l_sErr = strerror(errno);
        if (l_sErr == "Cannot assign requested address")
        {
            // c_masterParamPtr->m_slam->m_fae.setErrorCode("F3");
            LOGFAE(WERROR, "离线[{}]TCP服务器网络建立失败 | IP不存在,请检查!", c_sDevPrintfName_);
        }
        else
            LOGDR(WERROR, "{} offline tcp bind error: {}", WJLog::getWholeSysTime(), l_sErr);
        return false;
    }

    int flags = fcntl(p_iFd, F_GETFL, 0);
    // 设置非阻塞方式
    if (fcntl(p_iFd, F_SETFL, flags | O_NONBLOCK) < 0)
    {
        LOGDR(WERROR,
              "{} offline tcp non-block error: {}",
              WJLog::getWholeSysTime(),
              strerror(errno));
        return false;
    }

    int res = connect(p_iFd, (struct sockaddr*)&p_sRemoteAddr, sizeof(p_sRemoteAddr));

    // 连接成功(服务器和客户端在同一台机器上时就有可能发生这种情况)
    if (res == -1)
    {
        // 以非阻塞的方式来进行连接的时候，返回-1,不代表一定连接错误，如果返回EINPROGRESS，代表连接还在进行中
        // 可以通过poll或者select来判断socket是否可写，如果可以写，说明连接完成了
        if (errno == EINPROGRESS)
        {
            fd_set writefds;
            FD_ZERO(&writefds);
            FD_SET(p_iFd, &writefds);

            struct timeval timeout;
            timeout.tv_sec = 3;
            timeout.tv_usec = 0;

            // 调用select来等待连接建立成功完成
            res = select(p_iFd + 1, NULL, &writefds, NULL, &timeout);
            if (res < 0)
            {
                LOGDR(WERROR,
                      "{} offline tcp select error: {}",
                      WJLog::getWholeSysTime(),
                      strerror(errno));
                close(p_iFd);
                p_iFd = -1;
                return false;
            }

            // 返回0,则表示建立连接超时;
            // 我们返回超时错误给用户，同时关闭连接，以防止三路握手操作继续进行
            if (res == 0)
            {
                LOGFAE(WWARN,
                       "离线[{}]连接SICK服务器超时 | 请确认网络正常且SLAM启动",
                       c_sDevPrintfName_);
                close(p_iFd);
                p_iFd = -1;
                return false;
            }
            else
            {
                // 返回大于0的值,则需要检查套接口描述符是否可读或可写;
                if (!FD_ISSET(p_iFd, &writefds))
                {
                    LOGDR(WERROR, "{} offline tcp no events found!", WJLog::getWholeSysTime());
                    close(p_iFd);
                    p_iFd = -1;
                    return false;
                }
                else
                {
                    // 套接口描述符可读或可写,通过调用getsockopt得到套接口上待处理的错误(SO_ERROR)
                    // err 0-建立成功
                    int err = 0;
                    socklen_t elen = sizeof(err);
                    res = getsockopt(p_iFd, SOL_SOCKET, SO_ERROR, (char*)&err, &elen);
                    if (res < 0)
                    {
                        LOGDR(WERROR,
                              "{} offline tcp getsockopt error: {}",
                              WJLog::getWholeSysTime(),
                              strerror(errno));
                        close(p_iFd);
                        p_iFd = -1;
                        return false;
                    }
                    if (err != 0)
                    {
                        close(p_iFd);
                        p_iFd = -1;
                        return false;
                    }
                    else
                        return true;
                }
            }
        }
        else
        {
            LOGDR(WERROR,
                  "{} offline tcp connec error: {}",
                  WJLog::getWholeSysTime(),
                  strerror(errno));
            return false;
        }
    }
    else
        return true;
}

/**
 * @description: UDP服务器启动后 始终接收信息
 * @param {*}
 * @return {*}
 * @other:
 */
void InputOffLine::procRecv()
{
    u_char l_aucBufTmp[4096];
    u_char buffer[6] = {0xFF, 0xEE, 0x01, 0x02, 0xEE, 0xEE};
    int l_iSinSize = 0;

    c_iRunNum_++;
    c_iRunOverNum_++;
    while (c_iRunNum_)
    {
        l_iSinSize = sizeof(c_remoteAddr_);
        int l_iDataNum = recvfrom(
            c_sockfd_, l_aucBufTmp, 4095, 0, (sockaddr*)&c_remoteAddr_, (socklen_t*)&l_iSinSize);
        if (l_iDataNum > 0)
        {
            l_aucBufTmp[l_iDataNum] = 0;

            // sendto(c_sockfd_,
            //        &l_aucBufTmp[0],
            //        l_iDataNum,
            //        0,
            //        (struct sockaddr*)&c_remoteAddr_,
            //        sizeof(c_remoteAddr_));
        }
        if (!c_iRunNum_)
            break;
        usleep(100000);
    }
    setNetStatus(false);
    if (c_iRunOverNum_)
        c_iRunOverNum_--;
}

bool InputOffLine::getNetStatus()
{
    std::lock_guard<std::mutex> l_mutex(c_mutexNet_);
    return c_sockfd_ != -1 && c_bConnectSucc;
}

void InputOffLine::setNetStatus(bool p_bStatus)
{
    std::lock_guard<std::mutex> l_mutex(c_mutexNet_);
    c_bConnectSucc = p_bStatus;
}

/**
 * @description: 发送数据 根据协议类型TCP/UDP选择不同发送
 * @param {boost::shared_ptr<s_LIDAR_RAW_DATA_>&} p_data_ 数据
 * @return {bool} 是否发送成功
 * @other:
 */
bool InputOffLine::sendMsg(boost::shared_ptr<s_LIDAR_RAW_DATA_>& p_data_)
{
    if (!p_data_->m_pcapHeader.m_iDataLen)
        return true;
    boost::array<uint8_t, 1600> sendData;
    memcpy(&sendData,
           &p_data_->m_data[0],
           p_data_->m_pcapHeader.m_iDataLen - p_data_->m_pcapHeader.m_headerLen);
    for (int i = 0; i < p_data_->m_pcapHeader.m_headerLen; ++i)
    {
        sendData[p_data_->m_pcapHeader.m_iDataLen - p_data_->m_pcapHeader.m_headerLen + i] =
            ((uint8_t*)p_data_.get())[i];
    }
    int l_ret = 0;
    if (getNetStatus())
    {
        if (!c_stPcapInfo_.m_sProtocalMode.compare("UDP"))
        {
            int addr_len = sizeof(struct sockaddr_in);
            l_ret = sendto(c_sockfd_,
                           (void*)&sendData,
                           p_data_->m_pcapHeader.m_iDataLen,
                           0,
                           (struct sockaddr*)&c_remoteAddr_,
                           addr_len);
        }
        else if (!c_stPcapInfo_.m_sProtocalMode.compare("TCP"))
        {
            l_ret =
                send(c_sockfd_, (void*)&sendData, p_data_->m_pcapHeader.m_iDataLen, MSG_NOSIGNAL);
            if (l_ret != p_data_->m_pcapHeader.m_iDataLen)
            {
                LOGDR(WINFO,
                      "{} offline dev[{}] tcp send fail | {} {} {}",
                      WJLog::getWholeSysTime(),
                      c_uiId,
                      l_ret,
                      p_data_->m_pcapHeader.m_iDataLen,
                      strerror(l_ret));
                return false;
            }
        }
    }
    else
        return false;
    return true;
}

/**
 * @description: 主循环 提取pcap数据 扔给回调
 * @param {*}
 * @return {*}
 * @other:
 */
void InputOffLine::run_()
{
    // 仅PCAP模式下运行
    if (c_bIsOnlineMode)
        return;

    c_iRunNum_++;
    c_iRunOverNum_++;
    struct pcap_pkthdr* l_stHeader;
    const u_char* l_ucPktData;
    int l_res = -1;
    int l_ress = -1;
    // 等待连接成功
    while (!c_bConnectSucc)
    {
        if (c_bConnectSucc)
            break;
        usleep(10000);
    }
    c_masterParamPtr->m_slam->m_devList[c_uiId]->setStatus(DevStatus::PCAPSTOP);
    while (c_iRunNum_)
    {
        if (!c_iRunNum_)
            break;

        {
            std::lock_guard<std::mutex> l_mutex(c_mutexPcap_);
            // 等待pcap加载成功 或 启动播放
            if (!c_pacp_ || !c_bRun_)
            {
                usleep(1000);
                continue;
            }

            l_res = pcap_next_ex(c_pacp_, &l_stHeader, &l_ucPktData);
            if (l_res >= 0)
                l_ress = pcap_offline_filter(&c_pcapPktFilter_, l_stHeader, l_ucPktData);
            else
            {
                LOGFAE(WINFO, "离线[{}]数据包播放完毕", c_sDevPrintfName_);
                c_masterParamPtr->m_slam->m_devList[c_uiId]->setStatus(DevStatus::PCAPOVER);
                if (c_pacp_)
                    pcap_close(c_pacp_);
                c_pacp_ = NULL;
                c_masterParamPtr->m_bIsStart = false;
            }
        }

        if (l_res >= 0)
        {
            if ((0 == l_ress) || l_stHeader->len < c_uiPktDataMinLen)
                continue;
            if (!c_uiDataOffset)
            {
                continue;
                LOGDR(WINFO,
                      "{} offline dev[{}] data offset no set ",
                      WJLog::getWholeSysTime(),
                      c_uiId);
            }

            boost::shared_ptr<s_LIDAR_RAW_DATA_> l_data(new s_LIDAR_RAW_DATA_());
            memcpy(
                &l_data->m_data[0], l_ucPktData + c_uiDataOffset, l_stHeader->len - c_uiDataOffset);
            l_data->m_pcapHeader.m_iDataLen =
                l_stHeader->len - c_uiDataOffset + l_data->m_pcapHeader.m_headerLen;

            l_data->m_pcapHeader.m_time.set(l_stHeader->ts.tv_sec, l_stHeader->ts.tv_usec);
            if (c_stRL_->mustGetToken())  // 阻塞模式
                c_outCb_(c_uiId, l_data);
        }
    }
    if (c_iRunOverNum_)
        c_iRunOverNum_--;
}

bool InputOffLine::isStart()
{
    return c_bInitSucc_;
}

void InputOffLine::shutdown()
{
    if (c_sockfd_ != -1)
    {
        (void)close(c_sockfd_);
        c_sockfd_ = -1;
    }

    {
        std::lock_guard<std::mutex> l_mutex(c_mutexPcap_);
        if (c_pacp_)
        {
            pcap_close(c_pacp_);
            c_pacp_ = NULL;
        }
    }

    c_iRunNum_ = 0;
    c_bConnectSucc = true;
    while (1)
    {
        if (!c_iRunOverNum_)
            break;
        else
        {
            usleep(100000);
        }
    }
}

bool InputOffLine::isShutdown()
{
    return !c_iRunOverNum_;
}

#pragma endregion

#pragma region WLR720Lidar类

InputOffLine720::InputOffLine720(
    uint32_t p_uiDevId,
    boost::shared_ptr<RateLimiterr> p_stRateL,
    boost::function<void(uint32_t, boost::shared_ptr<s_LIDAR_RAW_DATA_>&)> p_outCb_)
    : InputOffLine(p_uiDevId, p_stRateL, p_outCb_)
{
    // PCAP模式下 打开PCAP 1.获取Ip-port 2.过滤PCAP 3 以PCAP为主，更新ip-port
    c_bVaildPcap_ = pcapInit_(c_stPcapInfo_.m_sPcapPath);
    if (!c_bVaildPcap_)
        c_masterParamPtr->m_slam->m_devList[c_uiId]->setStatus(DevStatus::PCAPERROR);
    netInit_(c_stPcapInfo_, c_myAddr_, c_remoteAddr_);

    // 非在线模式下 启动net
    if (!c_bIsOnlineMode)
    {
        std::thread net(&InputOffLine720::netStartThr, this);
        net.detach();
    }

    std::thread prcomsg(&InputOffLine720::run_, this);
    prcomsg.detach();
}

InputOffLine720::~InputOffLine720() {}

/**
 * @description: 自动分析PCAP，从lidar720 角度 获取网络配置，根据最小长度和lidar/agv ip 过滤PCAP
 * @param {*}
 * @return {*}
 * @other:
 */
bool InputOffLine720::pcapFilter()
{
    struct pcap_pkthdr* l_stHeader_;
    const u_char* l_ucPktData_;
    int l_tryPktNum = 100;
    s_pcapInfo l_stPcapInfo_;
    l_stPcapInfo_.m_sPcapPath = c_stPcapInfo_.m_sPcapPath;
    while (l_tryPktNum)
    {
        if (pcap_next_ex(c_pacp_, &l_stHeader_, &l_ucPktData_) >= 0)
        {
            // 解析第一包数据 获取源IP 目标端口等
            if (analyPcapHeader(l_ucPktData_, l_stHeader_->len, l_stPcapInfo_))
            {
                c_stPcapInfo_.m_sProtocalMode = l_stPcapInfo_.m_sProtocalMode;
                // // 提示用户
                // std::cout << "***********"
                //           << "PCAP_Info"
                //           << "***********" << std::endl;
                // std::cout << "**  FIlePath     : " << l_stPcapInfo_.m_sPcapPath.c_str()
                //           << std::endl;
                // std::cout << "**  ProtocalMode : " << l_stPcapInfo_.m_sProtocalMode << std::endl;
                // std::cout << "**  srcIP        : " << l_stPcapInfo_.m_sSrcIP << std::endl;
                // std::cout << "**  srcPort      : " << l_stPcapInfo_.m_uiSrcPort << std::endl;
                // std::cout << "**  dstIP        : " << l_stPcapInfo_.m_sDstIP << std::endl;
                // std::cout << "**  dstPort      : " << l_stPcapInfo_.m_uiDstPort << std::endl;
                // std::cout << std::endl;
                break;
            }
        }
        else
        {
            // c_masterParamPtr->m_slam->m_fae.setErrorCode("E4");
            c_masterParamPtr->m_slam->m_fae.setErrorCode("E2");
            LOGFAE(WERROR,
                   "离线[{}]数据包自动过滤失败 | 内部有效包数不足，请按照以下步骤检查数据包：",
                   c_sDevPrintfName_);
            LOGFAE(WERROR, " *********************************************** ");
            LOGFAE(WERROR, " * 1. 打开wireshark软件，输入过滤规则确认数据是否正确。 ");
            LOGFAE(WERROR, " * 2. 过滤规则：frame.len==1302 ");
            LOGFAE(WERROR, " * **********************************************");
        }
        l_tryPktNum--;
    }

    // 关闭后再次打开并过滤
    pcap_close(c_pacp_);
    c_pacp_ = pcap_open_offline(l_stPcapInfo_.m_sPcapPath.c_str(), errbuf_);

    if (l_tryPktNum)
    {
        // 通过源IP过滤数据包
        std::stringstream filter;
        filter << "len> " << c_uiPktDataMinLen << "&& src host " << l_stPcapInfo_.m_sSrcIP;
        pcap_compile(c_pacp_, &c_pcapPktFilter_, filter.str().c_str(), 1, PCAP_NETMASK_UNKNOWN);
        pcap_setfilter(c_pacp_, &c_pcapPktFilter_);
        LOGFAE(WINFO,
               "离线[{}]数据包自动过滤成功,过滤规则: 数据长度>{} 源-IP: {}",
               c_sDevPrintfName_,
               c_uiPktDataMinLen,
               l_stPcapInfo_.m_sSrcIP);
        return true;
    }
    else
    {
        pcap_compile(c_pacp_, &c_pcapPktFilter_, NULL, 1, PCAP_NETMASK_UNKNOWN);
        pcap_setfilter(c_pacp_, &c_pcapPktFilter_);
        c_masterParamPtr->m_slam->m_fae.setErrorCode("E3");
        // c_masterParamPtr->m_slam->m_fae.setErrorCode("E5");
        LOGFAE(WERROR,
               "离线[{}]数据包自动过滤失败 | 解析过滤规则异常,请检查数据包!",
               c_sDevPrintfName_);
        return false;
    }
}

#pragma endregion

#pragma region sickPcap类
InputOffLineSick::InputOffLineSick(
    uint32_t p_uiDevId,
    boost::shared_ptr<RateLimiterr> p_stRateL,
    boost::function<void(uint32_t, boost::shared_ptr<s_LIDAR_RAW_DATA_>&)> p_outCb_)
    : InputOffLine(p_uiDevId, p_stRateL, p_outCb_)
{
    if (c_bIsOnlineMode)
        return;
    // PCAP模式下 打开PCAP 1.获取Ip-port 2.过滤PCAP 3 以PCAP为主，更新ip-port
    c_bVaildPcap_ = pcapInit_(c_stPcapInfo_.m_sPcapPath);
    if (!c_bVaildPcap_)
    {
        /** @todo 此错误码是否合理*/
        c_masterParamPtr->m_slam->m_devList[c_uiId]->setStatus(DevStatus::PCAPERROR);
    }

    // 非在线模式下 启动net
    if (c_bVaildPcap_)
    {
        netInit_(c_stPcapInfo_, c_myAddr_, c_remoteAddr_);
        std::thread prcomsg(&InputOffLineSick::netStartThr, this);
        prcomsg.detach();
        std::thread prcomsg1(&InputOffLineSick::run_, this);
        prcomsg1.detach();
    }
}

InputOffLineSick::~InputOffLineSick()
{
    shutdown();
}

/**
 * @description: 自动分析PCAP，从agv角度 获取网络配置，根据最小长度和lidar/agv ip 过滤PCAP
 * @param {*}
 * @return {*}
 * @other:
 */
bool InputOffLineSick::pcapFilter()
{
    struct pcap_pkthdr* l_stHeader_;
    const u_char* l_ucPktData_;
    int l_tryPktNum = 100;
    s_pcapInfo l_stPcapInfo_;
    l_stPcapInfo_.m_sPcapPath = c_stPcapInfo_.m_sPcapPath;
    while (l_tryPktNum)
    {
        if (pcap_next_ex(c_pacp_, &l_stHeader_, &l_ucPktData_) >= 0)
        {
            // 解析第一包数据 获取源IP 目标端口等
            if (analyPcapHeader(l_ucPktData_, l_stHeader_->len, l_stPcapInfo_))
            {
                c_stPcapInfo_.m_sProtocalMode = l_stPcapInfo_.m_sProtocalMode;
                // // 提示用户
                // std::cout << "***********"
                //           << "PCAP_Info"
                //           << "***********" << std::endl;
                // std::cout << "**  FIlePath     : " << l_stPcapInfo_.m_sPcapPath.c_str()
                //           << std::endl;
                // std::cout << "**  ProtocalMode : " << l_stPcapInfo_.m_sProtocalMode << std::endl;
                // std::cout << "**  srcIP        : " << l_stPcapInfo_.m_sSrcIP << std::endl;
                // std::cout << "**  srcPort      : " << l_stPcapInfo_.m_uiSrcPort << std::endl;
                // std::cout << "**  dstIP        : " << l_stPcapInfo_.m_sDstIP << std::endl;
                // std::cout << "**  dstPort      : " << l_stPcapInfo_.m_uiDstPort << std::endl;
                // std::cout << std::endl;
                break;
            }
        }
        else
        {
            // c_masterParamPtr->m_slam->m_fae.setErrorCode("F1");
            LOGFAE(WERROR, "离线[{}]数据包自动过滤失败 | 内部有效包数不足", c_sDevPrintfName_);
        }
        l_tryPktNum--;
    }

    // 关闭后再次打开并过滤
    pcap_close(c_pacp_);
    c_pacp_ = pcap_open_offline(l_stPcapInfo_.m_sPcapPath.c_str(), errbuf_);

    if (l_tryPktNum)
    {
        // 通过源IP过滤数据包
        std::stringstream filter;
        filter << "len> " << c_uiPktDataMinLen << "&& src host " << l_stPcapInfo_.m_sSrcIP;
        pcap_compile(c_pacp_, &c_pcapPktFilter_, filter.str().c_str(), 1, PCAP_NETMASK_UNKNOWN);
        pcap_setfilter(c_pacp_, &c_pcapPktFilter_);
        LOGFAE(WINFO,
               "离线[{}]数据包自动过滤成功 | 过滤规则: 数据长度>{} 源-IP {}",
               c_sDevPrintfName_,
               c_uiPktDataMinLen,
               l_stPcapInfo_.m_sSrcIP);
        return true;
    }
    else
    {
        pcap_compile(c_pacp_, &c_pcapPktFilter_, NULL, 1, PCAP_NETMASK_UNKNOWN);
        pcap_setfilter(c_pacp_, &c_pcapPktFilter_);
        LOGFAE(WERROR,
               "离线[{}]数据包自动过滤失败 | 解析过滤规则异常,请检查数据包!",
               c_sDevPrintfName_);
        // c_masterParamPtr->m_slam->m_fae.setErrorCode("F2");
        return false;
    }
}

/**
 * @description: 逐包分析数据包 根据协议确定数据偏移值 之后确定雷达协议类型 及网络数据
 * @param {const u_char*} p_ucData PCAP的某包数据
 * @param {uint32_t} p_iLen 数据完整长度 涵盖-协议头
 * @param {s_pcapInfo&} p_stPcapInfo 待填充的PCAP信息
 * @return {bool} true：分析协议头 成功
 * @other:
 * 基于SICK协议 以客户端角度 确定客户端配置ip port 和 服务器配置ip port
 * 未过滤时 包含TCP客户端和服务器端双向交互信息，须通过协议选择 客户端发的数据包 进行分析
 */
bool InputOffLineSick::analyPcapHeader(const u_char* p_ucData,
                                       uint32_t p_iLen,
                                       s_pcapInfo& p_stPcapInfo)
{
    if (!p_iLen)
        return false;
    // 首先确定数据偏移
    if (!setOffset_(p_ucData, p_iLen, c_uiDataOffset))
        return false;

    uint32_t l_offset = 0;
    while (l_offset < p_iLen - 2)
    {
        // 找IP Header IPv4 [08 00] + headerLen [45]
        if (l_offset > 1 && *(p_ucData + l_offset) == 0x45 && *(p_ucData + l_offset - 1) == 0x00
            && *(p_ucData + l_offset - 2) == 0x08)
        {
            uint32_t l_totalLen = *(p_ucData + l_offset + 2) << 8 | *(p_ucData + l_offset + 3);
            if (l_totalLen == p_iLen - l_offset)
            {
                if (*(p_ucData + l_offset + 9) == 0x11)
                    p_stPcapInfo.m_sProtocalMode = "UDP";
                else if (*(p_ucData + l_offset + 9) == 0x06)
                    p_stPcapInfo.m_sProtocalMode = "TCP";
                else
                    p_stPcapInfo.m_sProtocalMode = "ERROR";

                // 根据数据偏移 读取数据头sMN 确定属于客户端的包
                if (p_iLen > c_uiDataOffset)
                {
                    // std::cout << " | " << *(p_ucData + c_uiDataOffset) << " | "
                    //           << *(p_ucData + c_uiDataOffset + 1) << " | "
                    //           << *(p_ucData + c_uiDataOffset + 2) << " | "
                    //           << *(p_ucData + c_uiDataOffset + 3) << " | " << std::endl;
                    if (!(*(p_ucData + c_uiDataOffset) == 2
                          && *(p_ucData + c_uiDataOffset + 1) == 's'
                          && *(p_ucData + c_uiDataOffset + 2) == 'M'
                          && *(p_ucData + c_uiDataOffset + 3) == 'N'))
                        break;

                    p_stPcapInfo.m_sSrcIP = std::to_string(*(p_ucData + l_offset + 12)) + "."
                                            + std::to_string(*(p_ucData + l_offset + 13)) + "."
                                            + std::to_string(*(p_ucData + l_offset + 14)) + "."
                                            + std::to_string(*(p_ucData + l_offset + 15));
                    p_stPcapInfo.m_sDstIP = std::to_string(*(p_ucData + l_offset + 16)) + "."
                                            + std::to_string(*(p_ucData + l_offset + 17)) + "."
                                            + std::to_string(*(p_ucData + l_offset + 18)) + "."
                                            + std::to_string(*(p_ucData + l_offset + 19));
                    p_stPcapInfo.m_uiSrcPort =
                        *(p_ucData + l_offset + 20) << 8 | *(p_ucData + l_offset + 21);
                    p_stPcapInfo.m_uiDstPort =
                        *(p_ucData + l_offset + 22) << 8 | *(p_ucData + l_offset + 23);
                    return true;
                }
            }
            else
                LOGDR(WERROR,
                      "{} sick pcap ip-header totalLength: {} {} {}",
                      WJLog::getWholeSysTime(),
                      p_iLen,
                      l_totalLen,
                      l_offset);
        }
        l_offset++;
    }
    return false;
}

/**
 * @function: setOffset_
 * @description: 根据SICK协议判断 02 s 获取 雷达数据 起始偏移
 * @param {const u_char*} pcap读取数据
 * @param {uint32_t} p_iLen 包长度
 * @param {uint32_t& } p_uiOffset 待修改 数据起始字节
 * @return {*} true 起始偏移非0  即成功  仅设置1次
 * @others: null
 */
bool InputOffLineSick::setOffset_(const u_char* p_ucData, uint32_t p_iLen, uint32_t& p_uiOffset)
{
    if (p_uiOffset)
        return true;
    for (uint32_t i = 0; i < p_iLen - 1; i++)
    {
        // 找02 s
        if (*(p_ucData + i) == 2 && *(p_ucData + i + 1) == 's')
        {
            p_uiOffset = i;
            break;
        }
    }

    if (p_uiOffset)
        return true;
    else
        return false;
}

/**
 * @description: 初始化 TCP客户端 连接TCP服务器 并接受数据
 * @param {int&} p_iFd 句柄
 * @param {sockaddr_in&} p_sMyAddr TCP客户端地址
 * @param {sockaddr_in&} p_sRemoteAddr TCP服务器地址
 * @return {*}
 * @other:
 */
void InputOffLineSick::netStart_(int& p_iFd, sockaddr_in& p_sMyAddr, sockaddr_in& p_sRemoteAddr)
{
    // TCP连接保持，连接中接收数据，中断后自动重新连接
    c_iRunNum_++;
    c_iRunOverNum_++;
    while (c_iRunNum_)
    {
        if (connectTcpServer_(p_iFd, p_sMyAddr, p_sRemoteAddr))
        {
            setNetStatus(true);
            std::thread prcomsg(&InputOffLineSick::procRecv, this);
            prcomsg.join();
        }
        setNetStatus(false);
        if (!c_iRunNum_)
            break;
        usleep(100000);
    }
    if (c_iRunOverNum_)
        c_iRunOverNum_--;
}

/**
 * @description: 判断TCP是否连接成功，接受数据
 * @param {*}
 * @return {*}
 * @other:
 */
void InputOffLineSick::procRecv()
{
    u_char l_aucBufTmp[4096];
    LOGFAE(WINFO,
           "[{}] 离线[{}]客户端: 服务器已连接 | 服务器-IP: {} 服务器-Port: {}",
           WJLog::getWholeSysTime(),
           c_sDevPrintfName_,
           inet_ntoa(c_remoteAddr_.sin_addr),
           ntohs(c_remoteAddr_.sin_port));
    c_iRunNum_++;
    c_iRunOverNum_++;
    while (c_iRunNum_)
    {
        int l_res = getsockopt(
            c_sockfd_, IPPROTO_TCP, TCP_INFO, &c_stConnectInfo_, (socklen_t*)&c_iConnectInfoLen_);
        if (l_res == -1 || c_stConnectInfo_.tcpi_state != TCP_ESTABLISHED)
        {
            close(c_sockfd_);
            c_sockfd_ = -1;
            LOGFAE(WWARN,
                   "[{}] 离线[{}]客户端: 服务器已断开 | IP: {} Port: {}",
                   WJLog::getWholeSysTime(),
                   c_sDevPrintfName_,
                   inet_ntoa(c_remoteAddr_.sin_addr),
                   ntohs(c_remoteAddr_.sin_port));
            break;
        }

        if (c_sockfd_ != -1 && c_iRunNum_)
        {
            int l_iDataNum = recv(c_sockfd_, l_aucBufTmp, 4095, 0);
            if (l_iDataNum <= 0)
            {
                getsockopt(c_sockfd_,
                           IPPROTO_TCP,
                           TCP_INFO,
                           &c_stConnectInfo_,
                           (socklen_t*)&c_iConnectInfoLen_);
            }
            else
            {
                l_aucBufTmp[l_iDataNum] = 0;
            }
        }
        usleep(10000);
    }
    setNetStatus(false);
    if (c_iRunOverNum_)
        c_iRunOverNum_--;
}

#pragma endregion

}  // namespace wj_slam
