/*
 * @Description:
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2022-03-23 18:34:13
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-03-23 18:44:51
 */
#include "algorithm/location/initial_location/impl/amcl.hpp"
#include "algorithm/location/initial_location/impl/globalAmcl.hpp"
#include <pcl/point_types.h>
WJSLAM_Amcl2D(pcl::PointXYZ, pcl::PointXYZHSV) WJSLAM_GlobalAmcl(pcl::PointXYZ, pcl::PointXYZHSV)