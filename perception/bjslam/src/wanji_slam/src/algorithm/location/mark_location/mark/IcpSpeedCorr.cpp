#define __ICPSPEEDCORR_C
#include "algorithm/location/mark_location/mark/IcpSpeedCorr.h"
//#include "config.h"
#include <math.h>
//#include "TaskDataApp.h"
#include "algorithm/location/mark_location/mark/ExSram.h"
#include "algorithm/location/mark_location/mark/Marks.h"
#include "algorithm/location/mark_location/mark/TaskTargetApp.h"
//#include "net.h"
#include <string.h>
extern float g_f32Per10mDeg_Rad;
extern ROBOT_XY g_sSavePosCur;
extern u16 g_u16Ang_SetMark_AbsCoor[5];
extern INPUTSPEED g_sInSpeedUsed;
extern float g_f32Time_PerResolution;
extern u8 g_u8Ang_Resolution;
extern u16 g_u16Ang_ScanMark_RelCoor[5];
extern float g_f32ScanTime_HalfPer;
extern u16 g_u16Ang_Laser_AbsCoor[5];
extern ROBOT_XY g_sSavePosOld;
extern EX_SRAM_EXT COMBINE g_sComb;                                    //(BANK1_SRAM1_ADDR);
extern EX_SRAM_EXT STRUCT_FILTER_TARGET g_sFilterDist;                 //(CCM_RAM);
extern EX_SRAM_EXT STRUCT_FILTER_TARGET_LITE g_sFilterMark_SpeedCorr;  //(CCM_RAM);
ICPSPEEDCORR_EXT STRUCT_ICPMarkInfo g_sIcpMarkPair;
ICPSPEEDCORR_EXT STRUCT_FPGA_SpeedIcp g_sFpga_IcpSpeed;
ICPSPEEDCORR_EXT STRUCT_SpeedIcp g_sIcpSpeed;
u32 Cal_IcpSpeed_PerMarkAng(XY_TO_RelCoor* p_ScanMark, XYANG2ROBOT* p_Laser_Pos)
{
    float l_f32rad;
    float l_f32_2PI = 2 * M_PI;
    u16 l_u16ang;
    l_f32rad =
        atan2(p_ScanMark->m_s32y - p_Laser_Pos->m_s32y, p_ScanMark->m_s32x - p_Laser_Pos->m_s32x);
    l_f32rad = fmodf(l_f32rad + l_f32_2PI, l_f32_2PI);
    l_u16ang =
        (u16)(l_f32rad * TenmDeg_Per_Rad + 0.5);  //得到该靶标在世界坐标轴平移后的与x方向的夹角
    // z轴为y轴,激光器顺时针方向
    //激光器的夹角ang是相对y轴的,  环境坐标是相对x轴的, 这就有个90度的固定差
    //方向相反, 顺时针,逆时针

    return l_u16ang;
}

float Cal_IcpSpeedAng(u8 size,
                      XYANG2ROBOT* LaserPos,
                      STRUCT_ICPMarkInfo* p_MarkPair,
                      u16* p_MarkAng)
{
    u8 l_u8i;
    float l_f32LaserAng = 0;
    u16 l_u16ScanMarkAng = 0;
    u32 l_u32sum = 0;
    u16 l_u16SetMarkAbsAng = 0;
    u16 l_u16Ang = 0;
    u16 l_u16Ang_Max = 0;

    for (l_u8i = 0; l_u8i < size; l_u8i++)
    {
        l_u16ScanMarkAng = p_MarkAng[l_u8i];  //扫描到靶的方位角
        //根据当前的激光器的位置计算与靶标的绝对坐标,计算靶标以激光器为圆心的绝对坐标系的角度,[0~DEG_PERSCAN]10mdeg单位
        l_u16SetMarkAbsAng = Cal_IcpSpeed_PerMarkAng(&p_MarkPair->m_sXy2Old[l_u8i], LaserPos);

        //靶标与绝对坐标系夹角(laser为圆心)
        g_u16Ang_SetMark_AbsCoor[l_u8i] = l_u16SetMarkAbsAng;
        //靶标与相对坐标系夹角
        g_u16Ang_ScanMark_RelCoor[l_u8i] = l_u16ScanMarkAng;
        l_u16Ang =
            (DEG_PERSCAN + g_u16Ang_SetMark_AbsCoor[l_u8i] - g_u16Ang_ScanMark_RelCoor[l_u8i])
            % DEG_PERSCAN;
        if (l_u16Ang > l_u16Ang_Max)
            l_u16Ang_Max = l_u16Ang;
        g_u16Ang_Laser_AbsCoor[l_u8i] = l_u16Ang;
    }

    //防止出现方位角 1, 7199跨越边界的出现
    for (l_u8i = 0; l_u8i < size; l_u8i++)
    {
        l_u16Ang = g_u16Ang_Laser_AbsCoor[l_u8i];
        l_u32sum += CorrAng_ByLoop(l_u16Ang, l_u16Ang_Max);
    }

    l_f32LaserAng = (l_u32sum / size) % DEG_PERSCAN;
    //	l_u16LaserAng = Round_Ang(l_u16LaserAng,g_u8Ang_Resolution);
    if (l_f32LaserAng > 18000)
        l_f32LaserAng = l_f32LaserAng - DEG_PERSCAN;
    return l_f32LaserAng;
}
int Get_IcpSpeed_FromFpga(STRUCT_FPGA_SpeedIcp* p_FpgaIcpSpeed,
                          u8 decMarkNum,
                          u8 i,
                          STRUCT_SpeedIcp* p_IcpSpeed)
{
    int l_s32x, l_s32y;
    int l_u32tmp;
    OS_CPU_SR cpu_sr;
    float l_f32tmp;
    u8 l_u8i = i;
    float l_f32tmp1, l_f32tmp2, l_f32tmp3;
    //	if(size < 3)
    //		return -1 ;
    // 1. 从识别到到路标挑选3个出来,计算激光器的初始位置和初始角度
    /*
        //2. 传递给fpga计算初始位置
        FPGA_Write_16bitDatas(FSMC_POSX1H_FPGA,p_FpgaIcpSpeed->m_s32x1 >> 16 );
        FPGA_Write_16bitDatas(FSMC_POSX1L_FPGA,p_FpgaIcpSpeed->m_s32x1&0xffff );
        FPGA_Write_16bitDatas(FSMC_POSX2H_FPGA,p_FpgaIcpSpeed->m_s32x2 >> 16 );
        FPGA_Write_16bitDatas(FSMC_POSX2L_FPGA,p_FpgaIcpSpeed->m_s32x2&0xffff );
        if(decMarkNum>2)
        {
            FPGA_Write_16bitDatas(FSMC_POSX3H_FPGA,p_FpgaIcpSpeed->m_s32x3 >> 16 );
            FPGA_Write_16bitDatas(FSMC_POSX3L_FPGA,p_FpgaIcpSpeed->m_s32x3&0xffff );
        }

        FPGA_Write_16bitDatas(FSMC_POSY1H_FPGA,p_FpgaIcpSpeed->m_s32y1 >> 16 );
        FPGA_Write_16bitDatas(FSMC_POSY1L_FPGA,p_FpgaIcpSpeed->m_s32y1&0xffff );
        FPGA_Write_16bitDatas(FSMC_POSY2H_FPGA,p_FpgaIcpSpeed->m_s32y2 >> 16 );
        FPGA_Write_16bitDatas(FSMC_POSY2L_FPGA,p_FpgaIcpSpeed->m_s32y2&0xffff );
        if(decMarkNum>2)
        {
            FPGA_Write_16bitDatas(FSMC_POSY3H_FPGA,p_FpgaIcpSpeed->m_s32y3 >> 16 );
            FPGA_Write_16bitDatas(FSMC_POSY3L_FPGA,p_FpgaIcpSpeed->m_s32y3&0xffff );
        }
        FPGA_Write_16bitDatas(FSMC_POSRX1H_FPGA,p_FpgaIcpSpeed->m_s32Rx1 >> 16 );
        FPGA_Write_16bitDatas(FSMC_POSRX1L_FPGA,p_FpgaIcpSpeed->m_s32Rx1&0xffff );
        FPGA_Write_16bitDatas(FSMC_POSRX2H_FPGA,p_FpgaIcpSpeed->m_s32Rx2 >> 16 );
        FPGA_Write_16bitDatas(FSMC_POSRX2L_FPGA,p_FpgaIcpSpeed->m_s32Rx2&0xffff );
        if(decMarkNum>2)
        {
            FPGA_Write_16bitDatas(FSMC_POSRX3H_FPGA,p_FpgaIcpSpeed->m_s32Rx3 >> 16 );
            FPGA_Write_16bitDatas(FSMC_POSRX3L_FPGA,p_FpgaIcpSpeed->m_s32Rx3&0xffff );
        }
        FPGA_Write_16bitDatas(FSMC_POSRY1H_FPGA,p_FpgaIcpSpeed->m_s32Ry1 >> 16 );
        FPGA_Write_16bitDatas(FSMC_POSRY1L_FPGA,p_FpgaIcpSpeed->m_s32Ry1&0xffff );
        FPGA_Write_16bitDatas(FSMC_POSRY2H_FPGA,p_FpgaIcpSpeed->m_s32Ry2 >> 16 );
        FPGA_Write_16bitDatas(FSMC_POSRY2L_FPGA,p_FpgaIcpSpeed->m_s32Ry2&0xffff );
        if(decMarkNum>2)
        {
            FPGA_Write_16bitDatas(FSMC_POSRY3H_FPGA,p_FpgaIcpSpeed->m_s32Ry3 >> 16 );
            FPGA_Write_16bitDatas(FSMC_POSRY3L_FPGA,p_FpgaIcpSpeed->m_s32Ry3&0xffff );
        }
        if(decMarkNum>=3)
            decMarkNum=3;
        else
            decMarkNum=2;
        FPGA_Write_16bitDatas(FSMC_MARKNUM_FPGA,decMarkNum&0xffff );


        FPGA_Write_16bitDatas(FSMC_START_FPGA, 1 ) ;//开始
        delayus(200);


        OS_ENTER_CRITICAL();
        l_u32tmp = FPGA_Read_16bitDatas(FSMC_FINALXH_FPGA) ;
        l_u32tmp <<= 16 ;
        l_u32tmp |= FPGA_Read_16bitDatas(FSMC_FINALXL_FPGA) ;
        memcpy((void *)&l_f32tmp,  (void *)&l_u32tmp, 4) ;
        p_IcpSpeed->m_sSpeed_Rel.m_s16speedx = (int)l_f32tmp ;
        p_IcpSpeed->m_sSpeedMatix[l_u8i].m_s16speedx = (int)l_f32tmp;

        l_u32tmp = FPGA_Read_16bitDatas(FSMC_FINALYH_FPGA) ;
        l_u32tmp <<= 16 ;
        l_u32tmp |= FPGA_Read_16bitDatas(FSMC_FINALYL_FPGA) ;
        memcpy((void *)&l_f32tmp,  (void *)&l_u32tmp, 4) ;
        p_IcpSpeed->m_sSpeed_Rel.m_s16speedy = (int)l_f32tmp ;
        p_IcpSpeed->m_sSpeedMatix[l_u8i].m_s16speedy = (int)l_f32tmp;

        OS_EXIT_CRITICAL();

        */
    return 0;
}
void Renew_IcpMarkXY(ROBOT_XY* p_LaserPos)
{
    u32 l_u32i;
    u8 l_u8Cnt = 0;
    memset((void*)&p_LaserPos->m_sXy2Robot[0], 0, STRUCT_SIZE_XY_TO_RelCoor * TARGET_MAX);
    memset((void*)&p_LaserPos->m_u16MatchMarkId, 0, 4 * TARGET_MAX);
    memset((void*)&p_LaserPos->m_u16MarkAng, 0, 2 * TARGET_MAX);
    for (l_u32i = 0; l_u32i < g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In; l_u32i++)
    {
        if (g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u32i]->m_u8IsMark
            == ISMARK)
        {
            memcpy(&p_LaserPos->m_sXy2Robot[l_u8Cnt],
                   g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_sXy2Robot[l_u32i],
                   STRUCT_SIZE_XY_TO_RelCoor);
            p_LaserPos->m_u16MatchMarkId[l_u8Cnt] =
                g_sFilterMark_SpeedCorr.m_psSetMarkAddr[l_u32i]->m_u32no;
            p_LaserPos->m_u16MarkAng[l_u8Cnt] =
                g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u32i]->m_u16Ang;
            l_u8Cnt++;
        }
    }
    p_LaserPos->m_u32MarkNum = l_u8Cnt;
    SortMarkByMarkId(p_LaserPos);
}
void IcpSpeed(u32 p_TSdiff, ROBOT_XY* p_LaserPos)
{
    u8 l_u8MarkPairNum = 0;
    u8 l_u8MinMarkNum_CalPos = 0;
    SortMarkByMarkId(p_LaserPos);
    l_u8MarkPairNum = Find_ICP_MarkPair(p_LaserPos, &g_sSavePosOld, &g_sIcpMarkPair);
    if (l_u8MarkPairNum >= 2)
    {
        l_u8MinMarkNum_CalPos = Get_NavMarkNum(l_u8MarkPairNum);
        Cal_RelSpeedIcp(&g_sIcpMarkPair, p_TSdiff, p_LaserPos, l_u8MinMarkNum_CalPos);
    }
    else
        g_sIcpSpeed.m_s32Flag = 0;
}

void Cal_RelSpeedIcp(STRUCT_ICPMarkInfo* p_MarkPair,
                     u32 p_TSdiff,
                     ROBOT_XY* p_LaserPos,
                     u8 p_MinMarkNum_CalPos)
{
    OS_CPU_SR cpu_sr;
    u8 l_u8i = 0;
    u16 l_u16size = 0;
    int l_s32sumx = 0, l_s32sumy = 0;
    XYANG2ROBOT l_sMoveRelPos;
    float l_f32SpeedTime = 1.0 / p_TSdiff;
    SPEED* l_psSpeed = g_sIcpSpeed.m_sSpeedMatix;

    Get_Combination(p_MarkPair->m_MarkNum, p_MinMarkNum_CalPos);
    l_u16size = g_sComb.m_u16size;
    // OS_ENTER_CRITICAL();
    for (l_u8i = 0; l_u8i < l_u16size; l_u8i++)
    {
        Tran_To_Float_Icp(
            &g_sFpga_IcpSpeed, p_MinMarkNum_CalPos, g_sComb.m_u8buf[l_u8i], p_MarkPair);
        Get_IcpSpeed_FromFpga(&g_sFpga_IcpSpeed, p_MinMarkNum_CalPos, l_u8i, &g_sIcpSpeed);
        l_s32sumx += l_psSpeed[l_u8i].m_s16speedx;
        l_s32sumy += l_psSpeed[l_u8i].m_s16speedy;
    }
    l_sMoveRelPos.m_s32x = l_s32sumx / l_u16size;
    l_sMoveRelPos.m_s32y = l_s32sumy / l_u16size;
    g_sIcpSpeed.m_sSpeed_Rel.m_s16speedx = l_sMoveRelPos.m_s32x * l_f32SpeedTime;
    g_sIcpSpeed.m_sSpeed_Rel.m_s16speedy = l_sMoveRelPos.m_s32y * l_f32SpeedTime;
    g_sIcpSpeed.m_f32SpeedAng =
        Cal_IcpSpeedAng(
            p_MarkPair->m_MarkNum, &l_sMoveRelPos, p_MarkPair, p_MarkPair->m_u16MarkAngCur)
        * l_f32SpeedTime;
    g_sIcpSpeed.m_s32Flag = 1;
    // OS_EXIT_CRITICAL();
}

int Tran_To_Float_Icp(STRUCT_FPGA_SpeedIcp* p_psFpgaIcpSpeed,
                      u8 num,
                      u8* p_Buf,
                      STRUCT_ICPMarkInfo* p_MarkPair)
{
    float l_f32tmp;

    u32 l_u32dist;

    u8 l_u8offset = p_Buf[0];
    l_f32tmp = (float)p_MarkPair->m_sXy2Old[l_u8offset].m_s32x;
    memcpy((void*)&p_psFpgaIcpSpeed->m_s32x1, (void*)&l_f32tmp, 4);
    l_f32tmp = (float)p_MarkPair->m_sXy2Old[l_u8offset].m_s32y;
    memcpy((void*)&p_psFpgaIcpSpeed->m_s32y1, (void*)&l_f32tmp, 4);

    l_f32tmp = (float)p_MarkPair->m_sXy2Cur[l_u8offset].m_s32x;
    memcpy((void*)&p_psFpgaIcpSpeed->m_s32Rx1, (void*)&l_f32tmp, 4);
    l_f32tmp = (float)p_MarkPair->m_sXy2Cur[l_u8offset].m_s32y;
    memcpy((void*)&p_psFpgaIcpSpeed->m_s32Ry1, (void*)&l_f32tmp, 4);

    l_u8offset = p_Buf[1];
    l_f32tmp = (float)p_MarkPair->m_sXy2Old[l_u8offset].m_s32x;
    memcpy((void*)&p_psFpgaIcpSpeed->m_s32x2, (void*)&l_f32tmp, 4);
    l_f32tmp = (float)p_MarkPair->m_sXy2Old[l_u8offset].m_s32y;
    memcpy((void*)&p_psFpgaIcpSpeed->m_s32y2, (void*)&l_f32tmp, 4);

    l_f32tmp = (float)p_MarkPair->m_sXy2Cur[l_u8offset].m_s32x;
    memcpy((void*)&p_psFpgaIcpSpeed->m_s32Rx2, (void*)&l_f32tmp, 4);
    l_f32tmp = (float)p_MarkPair->m_sXy2Cur[l_u8offset].m_s32y;
    memcpy((void*)&p_psFpgaIcpSpeed->m_s32Ry2, (void*)&l_f32tmp, 4);

    if (num > 2)
    {
        l_u8offset = p_Buf[2];
        l_f32tmp = (float)p_MarkPair->m_sXy2Old[l_u8offset].m_s32x;
        memcpy((void*)&p_psFpgaIcpSpeed->m_s32x3, (void*)&l_f32tmp, 4);
        l_f32tmp = (float)p_MarkPair->m_sXy2Old[l_u8offset].m_s32y;
        memcpy((void*)&p_psFpgaIcpSpeed->m_s32y3, (void*)&l_f32tmp, 4);

        l_f32tmp = (float)p_MarkPair->m_sXy2Cur[l_u8offset].m_s32x;
        memcpy((void*)&p_psFpgaIcpSpeed->m_s32Rx3, (void*)&l_f32tmp, 4);
        l_f32tmp = (float)p_MarkPair->m_sXy2Cur[l_u8offset].m_s32y;
        memcpy((void*)&p_psFpgaIcpSpeed->m_s32Ry3, (void*)&l_f32tmp, 4);
    }

    return 0;
}
void Mark_Corr_BySpeed_Icp(STRUCT_SpeedIcp* p_SpeedIcp,
                           u8 p_MarkNum,
                           STRUCT_FILTER_TARGET* p_Filter,
                           float p_CorrTime,
                           STRUCT_FILTER_TARGET_LITE* p_ScanMark)
{
    u8 l_u8i, l_u8size, l_u8j = 0;
    u16 l_u16MarkAng;
    u16 l_u16CorrAng = 0;
    float l_f32CorrTime_ms, l_f32CorrGrad_BySRad, l_f32RadCorred_BySLine;
    float l_f32CorrTime__NoSAng_ms = 0;
    int l_s32CorrGrad_BySRad, l_s32CorrGrad_BySLine;
    u16 l_u16TargetPoint = 0, l_u16ResiduePoint;
    float l_f32CorrLine_X = 0, l_f32CorrLine_Y = 0;
    float l_f32MarkRAD = 0;  //靶标弧度值
    // int l_s32CoorLidar_X = 0, l_s32CoorLidar_Y = 0;
    float l_f32CoorLidar_X = 0, l_f32CoorLidar_Y = 0;
    float l_f32_2PI = M_PI * 2;
    u16 l_u16GradCorred_BySLine = 0;
    // s8 l_s8MarkPose_Type = 0;//靶标在修至目标点方位,大于目标点=1,小于目标点 = -1;
    u16 l_u16sprintflen = 0;
    l_u8size = p_MarkNum;  //靶标个数
    //当目标点不为0或者g_u16ScanPointNum-1时,修正被分为两段
    l_u16TargetPoint =
        18000
        - g_u8Ang_Resolution;  //(g_u16ScanPointNum >> 1) -
                               // 1;//修正目标点,也代表0-目标点,目标点为[0,g_u16ScanPointNum-1]之间值
    l_u16ResiduePoint = 36000 - l_u16TargetPoint - g_u8Ang_Resolution * 2;  //剩余点,扫面点数-目标点
    for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
    {
        while (l_u8j < p_Filter->m_u8In)
        {
            if (p_ScanMark->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i]
                == &p_Filter->m_StructMarkInfo[l_u8j])
            {
                break;
            }
            l_u8j++;
        }

        l_u16MarkAng = p_Filter->m_StructMarkInfoNoCorr[l_u8j].m_u16Ang;
        l_f32CorrTime_ms = (g_f32ScanTime_HalfPer - g_f32Time_PerResolution * l_u16MarkAng);
        if (p_SpeedIcp->m_f32SpeedAng != 0)  //如果有旋转，先将刻度修至0刻线时的刻度
        {
            l_f32CorrTime__NoSAng_ms = g_f32Time_PerResolution * l_u16MarkAng;
            l_s32CorrGrad_BySRad = l_f32CorrTime__NoSAng_ms * p_SpeedIcp->m_f32SpeedAng;

            l_u16MarkAng = (DEG_PERSCAN + l_u16MarkAng + l_s32CorrGrad_BySRad)
                           % DEG_PERSCAN;  //更新用于计算的MarkAng,去除了角速度的影响
        }

        l_f32CorrLine_X =
            p_SpeedIcp->m_sSpeed_Rel.m_s16speedx * l_f32CorrTime_ms;  //单位 mm/s  * 毫秒
        // l_f32CorrLine_X = l_f32CorrLine_X * l_s8MarkPose_Type; //单位mm

        l_f32CorrLine_Y =
            p_SpeedIcp->m_sSpeed_Rel.m_s16speedy * l_f32CorrTime_ms;  //单位 mm/s  * 毫秒
        // l_f32CorrLine_Y = l_f32CorrLine_Y * l_s8MarkPose_Type; //单位mm

        l_f32MarkRAD = g_f32Per10mDeg_Rad * l_u16MarkAng;

        l_f32CoorLidar_X = p_Filter->m_StructMarkInfoNoCorr[l_u8j].m_u16Dist * cosf(l_f32MarkRAD);
        l_f32CoorLidar_Y = p_Filter->m_StructMarkInfoNoCorr[l_u8j].m_u16Dist * sinf(l_f32MarkRAD);
        //			Round_Up_To_Int(&l_f32CoorLidar_X,&l_s32CoorLidar_X);
        //			Round_Up_To_Int(&l_f32CoorLidar_Y,&l_s32CoorLidar_Y);

        l_f32RadCorred_BySLine =
            atan2(l_f32CoorLidar_Y - l_f32CorrLine_Y, l_f32CoorLidar_X - l_f32CorrLine_X);
        l_f32RadCorred_BySLine = fmodf(l_f32RadCorred_BySLine + l_f32_2PI, l_f32_2PI);

        l_u16GradCorred_BySLine = (u16)(l_f32RadCorred_BySLine / g_f32Per10mDeg_Rad);

        p_Filter->m_StructMarkInfo[l_u8j].m_u16Ang =
            (DEG_PERSCAN + l_u16GradCorred_BySLine
             - (int)(g_f32ScanTime_HalfPer * p_SpeedIcp->m_f32SpeedAng))
            % DEG_PERSCAN;  //再补偿

        p_Filter->m_StructMarkInfo[l_u8j].m_u16Dist = Calculate_Point2Point_Dist_Int(
            l_f32CoorLidar_Y - l_f32CorrLine_Y, l_f32CoorLidar_X - l_f32CorrLine_X);
    }
}
void CalAngOffsetBySpeed360TOHalf_Icp(STRUCT_FILTER_TARGET* p_Filter,
                                      STRUCT_SpeedIcp* p_SpeedIcp,
                                      float p_CorrTime,
                                      STRUCT_FILTER_TARGET_LITE* p_ScanMark)
{
    u8 l_u8size = p_Filter->m_u8In;

    Mark_Corr_BySpeed_Icp(p_SpeedIcp, l_u8size, p_Filter, p_CorrTime, p_ScanMark);
}

int Match_CurPos_Icp(STRUCT_MarkMatch* p_IdenWindow, u32 p_TSdiff)
{
    float l_f32CorrTime = p_TSdiff - g_f32ScanTime_HalfPer;
    int l_s32ret = 0;
    u32 l_u32i;
    u8 l_u8Cnt = 0;
    u8 l_u8MarkPairNum = 0;
    u16 l_u16sprintflen = 0;
    ROBOT_XY l_sLastLaserPos;
    memcpy(&l_sLastLaserPos, &g_sSavePosCur, STRUCT_SIZE_LASERPOS);
    l_s32ret = Match_CurPos(p_IdenWindow, p_TSdiff, &l_sLastLaserPos);

    if (l_s32ret != MATCH_FAILED)
    {
        IcpSpeed(p_TSdiff, &l_sLastLaserPos);
        if (g_sIcpSpeed.m_s32Flag == 1)
        {
            CalAngOffsetBySpeed360TOHalf_Icp(
                &g_sFilterDist, &g_sIcpSpeed, l_f32CorrTime, &g_sFilterMark_SpeedCorr);
            g_sInSpeedUsed.m_s32spdang = g_sIcpSpeed.m_f32SpeedAng;
        }
    }
    return 0;
}

u8 Find_ICP_MarkPair(ROBOT_XY* p_CurPos, ROBOT_XY* p_OldPos, STRUCT_ICPMarkInfo* p_MarkPair)
{
    u8 l_u8i = 0, l_u8j = 0;

    int count = 0;
    memset(p_MarkPair, 0, STRUCT_SIZE_ICPMarkInfo);
    while ((l_u8i < p_CurPos->m_u32MarkNum) && (l_u8j < p_OldPos->m_u32MarkNum))
    {
        if (p_CurPos->m_u16MatchMarkId[l_u8i] > p_OldPos->m_u16MatchMarkId[l_u8j])
            l_u8j++;
        else if (p_CurPos->m_u16MatchMarkId[l_u8i] < p_OldPos->m_u16MatchMarkId[l_u8j])
            l_u8i++;
        else
        {
            memcpy(&p_MarkPair->m_sXy2Cur[count],
                   &p_CurPos->m_sXy2Robot[l_u8i],
                   STRUCT_SIZE_XY_TO_RelCoor);
            p_MarkPair->m_u16MarkAngCur[count] = p_CurPos->m_u16MarkAng[l_u8i];
            memcpy(&p_MarkPair->m_sXy2Old[count],
                   &p_OldPos->m_sXy2Robot[l_u8j],
                   STRUCT_SIZE_XY_TO_RelCoor);
            p_MarkPair->m_u16MarkAngOld[count] = p_OldPos->m_u16MarkAng[l_u8j];
            l_u8i++;
            l_u8j++;
            count++;
            if (count >= 5)
                break;
        }
    }
    p_MarkPair->m_MarkNum = count;
    return count;
}
void SortMarkByMarkId(ROBOT_XY* p_LaserPos)
{
    u8 l_u8i, l_u8j;
    u8 l_u8size = p_LaserPos->m_u32MarkNum;
    u8 l_u8Cnt = 0;

    XY_TO_RelCoor l_sXy2Robot;
    u16 l_u16MarkAngTmp;
    u32 l_u32MarkIDtmp;

    for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
    {
        for (l_u8j = 0; l_u8j < l_u8size - l_u8i - 1; l_u8j++)
        {
            if ((p_LaserPos->m_u16MatchMarkId[l_u8j]) > (p_LaserPos->m_u16MatchMarkId[l_u8j + 1]))
            {
                memcpy(&l_sXy2Robot, &p_LaserPos->m_sXy2Robot[l_u8j], STRUCT_SIZE_XY_TO_RelCoor);
                memcpy(&p_LaserPos->m_sXy2Robot[l_u8j],
                       &p_LaserPos->m_sXy2Robot[l_u8j + 1],
                       STRUCT_SIZE_XY_TO_RelCoor);
                memcpy(
                    &p_LaserPos->m_sXy2Robot[l_u8j + 1], &l_sXy2Robot, STRUCT_SIZE_XY_TO_RelCoor);

                l_u32MarkIDtmp = p_LaserPos->m_u16MatchMarkId[l_u8j];
                p_LaserPos->m_u16MatchMarkId[l_u8j] = p_LaserPos->m_u16MatchMarkId[l_u8j + 1];
                p_LaserPos->m_u16MatchMarkId[l_u8j + 1] = l_u32MarkIDtmp;

                l_u16MarkAngTmp = p_LaserPos->m_u16MarkAng[l_u8j];
                p_LaserPos->m_u16MarkAng[l_u8j] = p_LaserPos->m_u16MarkAng[l_u8j + 1];
                p_LaserPos->m_u16MarkAng[l_u8j + 1] = l_u16MarkAngTmp;
            }
        }
    }
}
