#include "algorithm/location/mark_location/markMatch.h"

extern u8 g_u8IntinalFlag;
extern u32 g_u16MaxAng_AVER;  //防止角度0点翻转导致平均角度不正确
extern u32 g_u32CntOfACTIVE_AVER;
extern u8 g_u8NavLeastMarkNum;   // NAV最少扫描靶标数
extern ROBOT_XY g_sSavePosAver;  //
extern ROBOT_XY g_sSavePosSet;
extern LOCAL_MAP_INFO g_sLocalMap_Info;                    //
extern LOCAL_MAP_INFO g_sLocalMap_Info_LastCircle;         //
extern STRUCT_FILTER_TARGET g_sFilterDist;                 //靶标点容器1
extern STRUCT_FILTER_TARGET_LITE g_sFilterMark_SpeedCorr;  //靶标点容器2
extern INPUTSPEED g_sInSpeedUsed;                          // AGV速度
extern sSysPib g_sSysPib;                                  //控制模式设置
extern ROBOT_XY g_sSavePosCur;                             //当前的AGV位姿
extern ROBOT_XY g_sSavePosOld;                             //上一次扫描的AGV位姿
extern ROBOT_XY g_sSavePosSend;                            //当前的AGV位姿
extern MARK_SETS g_sMarkSets;                              // MAP-NAV地图上的靶标
extern KD_NODE_MARK* g_kd_root;                            // MAP-NAV地图上的靶标的kdtree
extern STRUCT_MarkMatch g_sMarkMatch_Set;                  // NAV匹配的靶标
extern ROBOT_XY g_sFusedPosOld;

#pragma region 公有函数

/**
 * @description: 读取靶标csv
 * @param {string} p_strMarkMapPath csv路径
 * @param {MARK_SETS*} p_sMarkSets 读取靶标填充
 * @return {Bool} true:文件成功打开且读取到mark数量>=3
 */
bool MarkMatch::loadMarkMap(std::string p_strMarkMapPath, MARK_SETS* p_sMarkSets)
{
    //清空指针
    memset(&p_sMarkSets, 0, STRUCT_SIZE_MARK_SETS);

    std::fstream m_fileMarkMapWR;  // mark文件读取句柄
    m_fileMarkMapWR.open(p_strMarkMapPath.c_str(), std::ios::in);

    if (!m_fileMarkMapWR.is_open())
    {
        return false;
    }
    //读取地图信息标志
    int l_line = 0;
    //读取靶标行数
    int l_MarkCnt = 0;
    int l_MarkNum = 0;
    //读取
    std::string _line;
    while (!m_fileMarkMapWR.eof())
    {
        getline(m_fileMarkMapWR, _line);
        if (l_line == 0)
        {
            if (0 == sscanf(_line.c_str(), "MAP-DATA"))
                l_line = 1;
        }
        else if (l_line == 1)
        {
            if (1 == sscanf(_line.c_str(), "MARK-NUM,%d", &l_MarkNum))
                l_line = 2;
        }
        else if (l_line == 2)
        {
            if (0 == sscanf(_line.c_str(), "X,Y,Num,Shape,Size"))
                l_line = 3;
        }
        else if (l_line == 3)
        {
            int x, y, no, shape, size;

            if (5 == sscanf(_line.c_str(), "%d,%d,%d,%d,%d", &x, &y, &no, &shape, &size))
            {
                p_sMarkSets->m_sMarkSets[l_MarkCnt].m_s32x = x;
                p_sMarkSets->m_sMarkSets[l_MarkCnt].m_s32y = y;
                p_sMarkSets->m_sMarkSets[l_MarkCnt].m_u32no = (u32)no;
                p_sMarkSets->m_sMarkSets[l_MarkCnt].m_u8shape = (u8)shape;
                p_sMarkSets->m_sMarkSets[l_MarkCnt].m_u8size = (u8)size;
                l_MarkCnt++;
            }
        }
    }
    p_sMarkSets->m_u16size = l_MarkNum;
    m_fileMarkMapWR.close();

    printf("Load Mark-Map with [%d] Marks from file [%s].\n",
           p_sMarkSets->m_u16size,
           p_strMarkMapPath.c_str());

    if (p_sMarkSets->m_u16size > 2)
        return true;
    return false;
}

/**
 * @description: 设置靶标地图
 * @param {MARK_SETS*} p_sMarkMap
 * @return {*}
 */
void MarkMatch::updateMarkMap(MARK_SETS* p_sMarkMap)
{
    //清空现有地图
    memset(&g_sMarkSets, 0, STRUCT_SIZE_MARK_SETS);
    g_sMarkSets = *p_sMarkMap;

    if (g_sMarkSets.m_u16size < 2)
    {
        g_sSysPib.m_u16WorkMode_NavOrMappingOrMark = SYSMODE_NavOrMappingOrMark_SCANF;
        return;
    }

    //靶标数量可能会变化 重新建立kdtree
    Free_KD_Tree();
    g_kd_root = BuildKdTreeMapInfo(&g_sMarkSets);

    g_sSysPib.m_u16WorkMode_NavOrMappingOrMark = SYSMODE_NavOrMappingOrMark_NAV;
}

/**
 * @description: 外部调用接口 传入靶标当前帧 返回位置
 * @param {STRUCT_FILTER_TARGET} *p_sScanMark 当前帧
 * @param {t}  p_t_out         匹配位姿
 * @param {q}  p_q_out
 * @param {vec} p_vMatchInfo   匹配关系
 * @param {Bool} p_bisInitMark 是否为靶标初始定位模式  默认为否
 * @return {Bool}
 */
bool MarkMatch::getMarkPose(STRUCT_FILTER_TARGET* p_sScanMark,
                            Eigen::Vector3d& p_t_out,
                            Eigen::Quaterniond& p_q_out,
                            STRUCT_FILTER_TARGET_LITE* p_sMatchInfo,
                            bool p_bisInitMark = false)
{
    //赋值给g_sFilterMark_SpeedCorr
    targetToLite_(p_sScanMark);

    //重置匹配靶标志位 NOMARK
    Memset_IsMarkFlag(&g_sFilterMark_SpeedCorr);

    Eigen::Vector3d l_t = Eigen::Vector3d::Zero();  //当前帧全局位置
    Eigen::Quaterniond l_q = Eigen::Quaterniond::Identity();
    //更改 g_sFusedPosOld,g_sSavePosCur 设置为0
    // setPose_(l_t,l_q);
    setPose_(Eigen::Vector3d::Zero(), Eigen::Quaterniond::Identity());

    //是否为靶标初始定位，
    if (!p_bisInitMark)
    {
        int l_s32ret = Match_CurPos(&g_sMarkMatch_Set, 0, &g_sSavePosCur);
        if (MATCH_FAILED == l_s32ret)
            return false;
        else
            WorkMode_Continue_Position(0, &g_sSavePosOld);
    }
    else
    {
        WorkMode_Initial_Position(&g_sFilterMark_SpeedCorr, 0, &g_sInSpeedUsed);
    }

    TransPos_To_SendOutTS(&g_sSavePosCur, &g_sSavePosSend, &g_sSavePosOld, &g_sInSpeedUsed, 0);

    if (g_sSavePosCur.m_u16flag == ACTIVE)
    {
        //全局成功后,获取靶标定位信息
        markStructToEigen_(g_sSavePosSend, p_t_out, p_q_out);
        p_sMatchInfo = &g_sFilterMark_SpeedCorr;
        return true;
    }
    else
    {
        g_u32CntOfACTIVE_AVER = 0;
        g_u16MaxAng_AVER = 0;
        memset(&g_sSavePosAver, 0, STRUCT_SIZE_LASERPOS);
        return false;
    }
}

/**
 * @description: 清空kdtree
 * @param {KD_NODE_MARK*}  p_sKd_root  kd指针
 * @return {*}
 */
void MarkMatch::freeKdTree(KD_NODE_MARK* p_sKd_root)
{
    if (p_sKd_root != NULL)
        makeEmpty_(p_sKd_root);
}

/**
 * @description: 建立kdtree
 * @param {MARK_SETS} p_SetMarks 地图靶标
 * @return {*}
 */
KD_NODE_MARK* MarkMatch::buildKdTreeMapInfo(MARK_SETS* p_SetMarks)
{
    return BuildKdTreeMapInfo(p_SetMarks);
}

#pragma endregion

#pragma region 私有函数
/**
 * @description: 初始化变量 设置为匹配模式
 * @param {*}
 * @return {*}
 */
void MarkMatch::init_()
{
    g_sSysPib.m_u16WorkMode_NavOrMappingOrMark = SYSMODE_NavOrMappingOrMark_NAV;
}

/**
 * @description: 外部传入STRUCT_FILTER_TARGET 转 STRUCT_FILTER_TARGET_LITE 用于全局
 * @param {STRUCT_FILTER_TARGET} p_Filter 外部传入的当前帧靶标结构体
 * @return {*}
 */
void MarkMatch::targetToLite_(STRUCT_FILTER_TARGET* p_Filter)
{
    for (u8 i = 0; i < p_Filter->m_u8In; i++)
    {
        g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_StructMarkInfo[i] =
            &p_Filter->m_StructMarkInfo[i];
        g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_sXy2Robot[i] = &p_Filter->m_sXy2Robot[i];
    }
    g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In = p_Filter->m_u8In;
}

/**
 * @description: 设置0位姿,因传入的靶标当前帧为全局，不需要再转一道
 * @param {t} p_t 0位置
 * @param {q} p_q 0方向
 * @return {*}
 */
void MarkMatch::setPose_(const Eigen::Vector3d& p_t, const Eigen::Quaterniond& p_q)
{
    //更新下一帧的位姿估计
    double yaw = yawIn2PI_(p_q);
    g_sFusedPosOld.m_Struct_LaserPos.m_s32x = p_t[0] * 1000.0;
    g_sFusedPosOld.m_Struct_LaserPos.m_s32y = p_t[1] * 1000.0;
    g_sFusedPosOld.m_Struct_LaserPos.m_u16ang = rad2deg_(yaw) * 100;  // deg转为10mdeg的倍数
    g_sFusedPosOld.m_u16flag = ACTIVE;

    g_sSavePosCur.m_Struct_LaserPos.m_s32x = g_sFusedPosOld.m_Struct_LaserPos.m_s32x;
    g_sSavePosCur.m_Struct_LaserPos.m_s32y = g_sFusedPosOld.m_Struct_LaserPos.m_s32y;
    g_sSavePosCur.m_Struct_LaserPos.m_u16ang = g_sFusedPosOld.m_Struct_LaserPos.m_u16ang;
    g_sSavePosCur.m_u16flag = g_sFusedPosOld.m_u16flag;
}

/**
 * @description: 外部传入STRUCT_FILTER_TARGET 转 STRUCT_FILTER_TARGET_LITE 用于全局
 * @param {STRUCT_FILTER_TARGET} p_Filter 外部传入的当前帧靶标结构体
 * @return {*}
 */
double MarkMatch::yawIn2PI_(const Eigen::Quaterniond& q)
{
    // yaw (z-axis rotation)
    double siny_cosp = +2.0 * (q.w() * q.z() + q.x() * q.y());
    double cosy_cosp = +1.0 - 2.0 * (q.y() * q.y() + q.z() * q.z());
    double l_yaw = atan2(siny_cosp, cosy_cosp);

    if (l_yaw < 0.0)
        return l_yaw + 2 * M_PI;
    else if (l_yaw > 2 * M_PI)
        return l_yaw - 2 * M_PI;
    else
        return l_yaw;
}

/*************************************************
Function		:	matchedMarksToPointCloud_
Description		:	获取match靶标数量，更新mark匹配位姿
Input			:	p_t_out: 位置
                    p_q_out: 姿势
Output			:	无
Return			:	true：mark匹配成功 数量>=3
Others			:	无
*************************************************/
bool MarkMatch::matchedMarksToPointCloud_(Eigen::Vector3d& p_t_out, Eigen::Quaterniond& p_q_out)
{
    int l_nMatchMarks = 0;

    for (int i = 0; i < g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In;
         i++)  //修正混补发出坐标
    {
        if (g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_StructMarkInfo[i]->m_u8IsMark
            == ISMARK)
        {
            l_nMatchMarks++;
        }
    }

    if (l_nMatchMarks >= 3)
    {
        p_t_out.x() = float(g_sSavePosSend.m_Struct_LaserPos.m_s32x) / 1000.0;
        p_t_out.y() = float(g_sSavePosSend.m_Struct_LaserPos.m_s32y) / 1000.0;
        p_t_out.z() = 0.0;
        double yaw = g_sSavePosSend.m_Struct_LaserPos.m_u16ang / 18000.0 * M_PI;

        p_q_out = yaw2Quat_(yaw).normalized();
        return true;
    }
    else
        return false;
}

void MarkMatch::markStructToEigen_(ROBOT_XY p_structMark,
                                   Eigen::Vector3d& t_mark,
                                   Eigen::Quaterniond& q_mark)
{
    t_mark.x() = float(p_structMark.m_Struct_LaserPos.m_s32x) / 1000.0;
    t_mark.y() = float(p_structMark.m_Struct_LaserPos.m_s32y) / 1000.0;
    t_mark.z() = 0.0;
    double yaw = p_structMark.m_Struct_LaserPos.m_u16ang / 18000.0 * M_PI;
    if (yaw > M_PI)
        yaw -= 2 * M_PI;

    q_mark = yaw2Quat_(yaw).normalized();
}

Eigen::Quaterniond MarkMatch::yaw2Quat_(double Yaw)
{
    const Eigen::AngleAxisd roll_angle(0.0, Eigen::Vector3d::UnitX());
    const Eigen::AngleAxisd pitch_angle(0.0, Eigen::Vector3d::UnitY());
    const Eigen::AngleAxisd yaw_angle(Yaw, Eigen::Vector3d::UnitZ());
    return yaw_angle * pitch_angle * roll_angle;
}

double MarkMatch::rad2deg_(double radians)
{
    return radians * 180.0 / M_PI;
}

KD_NODE_MARK* MarkMatch::makeEmpty_(KD_NODE_MARK* tree)
{
    if (tree)
    {
        makeEmpty_(tree->kd_left);
        makeEmpty_(tree->kd_right);
        free(tree);
        tree = NULL;
    }
    return tree;
}

#pragma endregion
