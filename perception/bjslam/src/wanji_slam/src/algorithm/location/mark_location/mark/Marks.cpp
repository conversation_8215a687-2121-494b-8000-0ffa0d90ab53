#define __MARKS_C
#include "algorithm/location/mark_location/mark/Marks.h"
#include "algorithm/location/mark_location/mark/FindMarkCenter.h"
#include "algorithm/location/mark_location/mark/IcpSpeedCorr.h"
//#include "TaskDataApp.h"
//#include "TaskNetApp.h"
//#include "TaskStateApp.h"
//#include "Initialize.h"
//#include "net.h"
//#include "FPGA.h"
#include "algorithm/location/mark_location/mark/ExSram.h"
#include <string.h>
//#include "flash.h"
#include "algorithm/location/mark_location/mark/TaskTargetApp.h"
#include "algorithm/location/mark_location/mark/kdtree.h"
#include <math.h>
#include <stdlib.h>
//#include "fm25cl64b_driver.h"
//#include "ucos_ii.h"
//#include "NewProtocol.h"
#include "algorithm/location/mark_location/mark/EdgeList.h"
#include <Eigen/Dense>
#include <ros/ros.h>
#include <stdint.h>

#include <fstream>
/** debug File */
std::ofstream d0File;
extern int SaveNavCnt;

extern sSysPib g_sSysPib;
extern sFactorCorr g_sFactorCorr;
extern u16 g_u16CombOffset;
extern u16 g_u16Mapping_Aver_Done_Cnt;
extern u32 g_u32CntOfACTIVE;
extern __IO int g_NetCnt;
EX_SRAM_EXT INPUTSPEED g_auSpeed[10];
EX_SRAM_EXT u8 g_au8NavMarkBuf[5];
EX_SRAM_EXT u32 g_auspeedCnt;
extern EX_SRAM_EXT KD_NODE_MARK* g_kd_root;
extern EX_SRAM_EXT STRUCT_CARVE* g_sPulseCarveBufCopy;  //(CCM_RAM);
EX_SRAM_EXT COMBINE g_sComb;                            //(BANK1_SRAM1_ADDR);
EX_SRAM_EXT COMBINE1 g_sCombofDecMark;                  //(BANK1_SRAM1_ADDR);
EX_SRAM_EXT XYANG2ROBOT g_auXY2EqlTri[EQLTRI_SIZE];  //(BANK1_SRAM1_ADDR);//全等三角形定位出的坐标值
EX_SRAM_EXT STRUCT_EqlTri g_sEqlTri;                 //(BANK1_SRAM1_ADDR);
EX_SRAM_EXT MARK_SETS g_sMarkSets;                   //(CCM_RAM);//;//(CCM_RAM) ;
EX_SRAM_EXT STRUCT_FPGAPOS g_sFpgaPos;               //(CCM_RAM);
EX_SRAM_EXT STRUCT_FILTER_TARGET g_sFilterDist;                   //(CCM_RAM);
EX_SRAM_EXT STRUCT_FILTER_TARGET_LITE g_sFilterMark_SpeedCorr;    //(CCM_RAM);
EX_SRAM_EXT STRUCT_FILTER_TARGET_LITE g_sFilterDistShortNewLast;  //(CCM_RAM);
EX_SRAM_EXT LOCAL_MAP_INFO g_sLocalMap_Info;                      //(CCM_RAM);
EX_SRAM_EXT LOCAL_MAP_INFO g_sLocalMap_Info_LastCircle;           //(CCM_RAM);
extern ICPSPEEDCORR_EXT STRUCT_SpeedIcp g_sIcpSpeed;
extern FINDMARKCENTER_EXT u16 g_u16DistDiffMax;
extern u16 g_u16point;
extern XY_TO_RelCoor g_sPosOffset;
u16 g_u16SyncMode = 1;         //
u16 g_u8SyncCycleBit = 1;      //
u16 ADDR_FLASH_MARK_INFO = 0;  //
extern STRUCT_MarkMatch g_sMarkMatch_Set;

extern MappingList g_auMappingList;

// MARKS_EXT __IO u8 g_u8socket2flag ;
MARKS_EXT __IO u8 g_u8WirelessVol_NormalFlag;
MARKS_EXT __IO u8 g_u8socket2flag2;
MARKS_EXT __IO u8 g_u8LandmarkSendMode;

MARKS_EXT __IO u32 g_u32PosTimestamp;
MARKS_EXT XY2ROBOT_CNT g_sMappingXY_old;
MARKS_EXT XY2ROBOT_CNT g_sNavXY_Vir;
// MARKS_EXT XY2ROBOT_CNT g_sMappingXY_new;
MARKS_EXT ROBOT_XY g_sSavePosSend;
MARKS_EXT ROBOT_XY g_sSavePosOld;
// MARKS_EXT ROBOT_XY g_sSavePosOld_Old ;
MARKS_EXT ROBOT_XY g_sSavePosCur;
MARKS_EXT ROBOT_XY g_sSavePosSet;
MARKS_EXT XYANG2ROBOT g_sSavePosAverBuf[POS_AVER_CNT];  //前10个用于存储,第11个为平均值
MARKS_EXT ROBOT_XY g_sSavePosAver;
MARKS_EXT INPUTSPEED g_sInSpeed;
MARKS_EXT INPUTSPEED g_sInSpeedCopy;
MARKS_EXT INPUTSPEED g_sInSpeedUsed;
MARKS_EXT __IO u16 g_u16ScanPointNum;
MARKS_EXT __IO u16 g_u16FpgaZeroOffset;
MARKS_EXT u8 g_u8MaxMarkRadio;
MARKS_EXT __IO u8 g_u8ProtocalFlag;
MARKS_EXT u16 g_u16Ang_Laser_AbsCoor[5];
MARKS_EXT u16 g_u16Ang_SetMark_AbsCoor[5];
MARKS_EXT u16 g_u16Ang_ScanMark_RelCoor[5];
MARKS_EXT u16 g_au16max_dist[32];
MARKS_EXT u16 g_au16min_dist[32];
MARKS_EXT u16 g_u16PianXinFlag;
MARKS_EXT u8 g_u8IntinalFlag;
MARKS_EXT u16 g_u16ModeOld;  //上一次的模式
// MARKS_EXT __IO u8 g_u8LocalMapUseNum;
// MARKS_EXT __IO u8 g_u8LocalMapMarkNum;
MARKS_EXT __IO u16 g_u16APD_HV_Expecte;
MARKS_EXT __IO u16 g_u16APD_HV_DAVALUE;
MARKS_EXT __IO u16 g_u16MarkDot_Coeff;
MARKS_EXT __IO u32 g_u32MarkRadio_Coeff;
MARKS_EXT __IO u16 g_u16MarkMaxPointNum;
MARKS_EXT __IO u16 g_u16NavPoweroff;
MARKS_EXT __IO u8 g_u8TotolPackNum;
MARKS_EXT __IO u8 g_u8UartMode;
MARKS_EXT __IO u8 g_u8Uart_GetPosFlag;
// MARKS_EXT __IO float  g_f32PerGrad_Rad;
MARKS_EXT __IO float g_f32Per10mDeg_Rad = Rad_Per_10mDeg;
MARKS_EXT __IO u8 g_u8NavLeastMarkNum = 3;
MARKS_EXT __IO u16 g_u16EdgeDiff;
MARKS_EXT __IO u16 g_u16EdgeCalDiff;
MARKS_EXT __IO u32 g_u32SizeofSetEdge;
MARKS_EXT __IO u8 g_u8Ang_Resolution;
MARKS_EXT __IO float g_f32Time_PerResolution;
MARKS_EXT __IO u8 g_u8ScanFrequency;
MARKS_EXT __IO u8 g_u8HasCrossFlag;
MARKS_EXT __IO int g_s32AngCorr;
MARKS_EXT __IO float g_f32ScanTime_Per;
MARKS_EXT __IO float g_f32ScanTime_HalfPer;
MARKS_EXT u16 g_au16ScanMarkDist[TARGET_MAX];
MARKS_EXT u16 g_au16ScanMarkAng[TARGET_MAX];
__IO int g_NetCnt = 0;

XY_TO_RelCoor g_sPosOffset;
//心跳包计数器
__IO u8 g_u8socket2flag = 0;

u32 g_u32err8 = 0;
u16 g_u16point = 0;
static u16 l_u16buf2ang[16] = {0};

void NetSendDly(void)
{
    // delayus(200);
}

u32 Cal_AngDiff_With_PrioAngValue(u16* buf, u8 p_AverSize)
{
    u8 l_u8i;
    u16 l_u16PrioAng = DEG_PERSCAN / p_AverSize;
    u32 l_u32AngErr = 0;

    for (l_u8i = 0; l_u8i < p_AverSize; l_u8i++)
    {
        l_u32AngErr += abs(*(buf + l_u8i) - l_u16PrioAng);
    }

    return l_u32AngErr;
}

// n : 靶标个数
// m : 选择3个靶标, m=3
// a : 输入的数据
// b : 输出的Cij
// M : M=m
void combine(u8 n, u8 m, u8 a[], u8 b[], const u8 M)
{
    for (int j = n; j >= m; j--)
    {
        b[m - 1] = j - 1;
        if (m > 1)
            combine(j - 1, m - 1, a, b, M);
        else
        {
            for (int i = M - 1; i >= 0; i--)
            //这里应该是跳出来的3个靶标的组合
            {
                // printf("%d ",a[b[i]]);
                // c[i] = a[b[i]] ;
                // buf[cnt][i] = a[b[i]] ;
                g_sComb.m_u8buf[g_sComb.m_u16size][i] = a[b[i]];
            }

            g_sComb.m_u16size++;
        }
    }
}

void combine1(u8 n, u8 m, u8 a[], u8 b[], const u8 M)
{
    for (int j = n; j >= m; j--)
    {
        b[m - 1] = j - 1;
        if (m > 1)
            combine1(j - 1, m - 1, a, b, M);
        else
        {
            for (int i = M - 1; i >= 0; i--)
            //这里应该是跳出来的3个靶标的组合
            {
                // printf("%d ",a[b[i]]);
                // c[i] = a[b[i]] ;
                // buf[cnt][i] = a[b[i]] ;
                g_sCombofDecMark.m_u8buf[g_sCombofDecMark.m_u16size][i] = a[b[i]];
            }

            g_sCombofDecMark.m_u16size++;
        }
    }
}

int SortCombine(COMBINE* comb, u8 size)
{
    u16 l_u16size;
    u16 l_u16j, l_u16i, l_u16k;
    u32* l_pu32val = comb->m_u32val;
    u32 l_u32tmp;
    u8 l_u8tmp;
    u8 l_u8compsize = size;

    l_u16size = comb->m_u16size;
    for (l_u16i = 0; l_u16i < l_u16size; l_u16i++)
    {
        for (l_u16j = 0; l_u16j < l_u16size - l_u16i - 1; l_u16j++)
        {
            if (*(l_pu32val + l_u16j) > *(l_pu32val + l_u16j + 1))
            {
                //交换
                l_u32tmp = *(l_pu32val + l_u16j);
                *(l_pu32val + l_u16j) = *(l_pu32val + l_u16j + 1);
                *(l_pu32val + l_u16j + 1) = l_u32tmp;

                //交换排列组合
                for (l_u16k = 0; l_u16k < l_u8compsize; l_u16k++)
                {
                    l_u8tmp = comb->m_u8buf[l_u16j][l_u16k];
                    comb->m_u8buf[l_u16j][l_u16k] = comb->m_u8buf[l_u16j + 1][l_u16k];
                    comb->m_u8buf[l_u16j + 1][l_u16k] = l_u8tmp;
                }
            }
        }
    }
    return 0;
}

// u32 TriMatch1(u8 *NoofDub,u16 *TriEdge,u32 *TriEdgeDect, u16 i)
//{
//	#define max_diff 180
//	u8 l_u8numi,l_u8numj,l_u8i,l_u8j,l_u8IsEql;
//	u16 l_u16num[3];
//	u32 l_u32edge[3];
//	u16 l_u16size = g_sMarkSets.m_u16size;
//	u8 l_u8No[4] ,l_u8noTMP[3];
//	u8 l_u8cnt=0;
////取出周长匹配上的边长
//	for (l_u8i = 0;l_u8i <3 ;l_u8i++)
//	{
//		l_u8numi = *(NoofDub+l_u8i) ;
//		l_u8numj = *(NoofDub+l_u8i+1);
//		if (l_u8numi > l_u8numj)
//		{
//			u8 tmp = l_u8numi;
//			l_u8numi = l_u8numj;
//			l_u8numj = tmp ;
//		}
//		l_u16num[l_u8i] = l_u8numi*l_u16size -
//((l_u8numi*(l_u8numi+1))>>1)+(l_u8numj-l_u8numi-1);//找出三个边长对应的下标 		l_u32edge[l_u8i]
//=
//*(TriEdge+l_u16num[l_u8i]);//取出边长，此边长已经进行过排序
//	}
//	//进行边长匹配
//	u32 l_u32len = abs(l_u32edge[0] - *(TriEdgeDect));
//	if (l_u32len < max_diff)
//	{
//		l_u32len = abs(l_u32edge[1] - *(TriEdgeDect +1));
//		if (l_u32len < max_diff)
//		{
//			l_u32len = abs(l_u32edge[2] - *(TriEdgeDect + 2));
//			if (l_u32len < max_diff)
//			{
//				if(g_sComb.m_u8matchFlag[i] ==1)
//				{
//					l_u8noTMP[0] = g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][0]].m_u32no;
//					l_u8noTMP[1] = g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][1]].m_u32no;
//					l_u8noTMP[2] =
// g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][2]].m_u32no;//把全等三角形的no的第一组保存下来
//				}
//				g_sFilterMark_SpeedCorr.m_StructMarkScanInfo.m_StructMarkInfo[g_sComb.m_u8buf[i][0]].m_u8IsMark
//= ISMARK;
//				g_sFilterMark_SpeedCorr.m_StructMarkScanInfo.m_StructMarkInfo[g_sComb.m_u8buf[i][1]].m_u8IsMark
//= ISMARK;
//				g_sFilterMark_SpeedCorr.m_StructMarkScanInfo.m_StructMarkInfo[g_sComb.m_u8buf[i][2]].m_u8IsMark
//=
// ISMARK;//g_sComb.m_u8buf中存的是扫描到的三角形的no组合（此三角形已经与设置的三角形匹配上了，no表示的是扫描到的no号）
//				g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][0]].m_u32no =
//*(NoofDub);//NoofDub存的是匹配上的三角形的no组合（此no是设置的no）
//				g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][1]].m_u32no = *(NoofDub+1);
//				g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][2]].m_u32no = *(NoofDub+2);
//				//拷贝靶标信息
//				g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][0]].m_s32x =
// g_sMarkSets.m_sMarkSets[*(NoofDub)].m_s32x;
//				g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][0]].m_s32y =
// g_sMarkSets.m_sMarkSets[*(NoofDub)].m_s32y;
//				g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][1]].m_s32x =
// g_sMarkSets.m_sMarkSets[*(NoofDub+1)].m_s32x;
//				g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][1]].m_s32y =
// g_sMarkSets.m_sMarkSets[*(NoofDub+1)].m_s32y;
//				g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][2]].m_s32x =
// g_sMarkSets.m_sMarkSets[*(NoofDub+2)].m_s32x;
//				g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][2]].m_s32y =
// g_sMarkSets.m_sMarkSets[*(NoofDub+2)].m_s32y;
//				g_sComb.m_u8matchFlag[i]++;//被匹配的次数//第一次被匹配上 则等于1
//				l_u8cnt =l_u8cnt +3;
//				g_sEqlTri.m_u16In = 0;
//
//			}
//		}
//	}
//	if(g_sComb.m_u8matchFlag[i]
//>1)//该三角形被匹配了多次，说明有全等三角形，就将所有三角形组合保存，为后面利用全等三角形进行匹配所用
//	{
//		l_u8cnt =0;//全等三角形不计入匹配到的靶
//		g_sEqlTri.m_u16In =
// g_sComb.m_u8matchFlag[i];//表示有全等三角形，也表示其个数，没有全等三角形时为0，有全等三角形时则表示全等三角形的个数--最小为2
//		if(g_sComb.m_u8matchFlag[i] ==2)
//		{
//			g_sEqlTri.m_sMark[0][0].m_u32no = l_u8noTMP[0];
//			g_sEqlTri.m_sMark[0][1].m_u32no = l_u8noTMP[1];
//			g_sEqlTri.m_sMark[0][2].m_u32no = l_u8noTMP[2];
//
//			g_sEqlTri.m_sMark[1][0].m_u32no =
// g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][0]].m_u32no;
// g_sEqlTri.m_sMark[1][1].m_u32no = g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][1]].m_u32no;
// g_sEqlTri.m_sMark[1][2].m_u32no = g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][2]].m_u32no;
//
//			g_sEqlTri.buf[0]=g_sComb.m_u8buf[i][0];//把扫描到的靶标号保存下来
//			g_sEqlTri.buf[1]=g_sComb.m_u8buf[i][1];
//			g_sEqlTri.buf[2]=g_sComb.m_u8buf[i][2];
//
//		}
//		else
//		{
//			g_sEqlTri.m_sMark[g_sComb.m_u8matchFlag[i]-1][0].m_u32no =
// g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][0]].m_u32no;
//			g_sEqlTri.m_sMark[g_sComb.m_u8matchFlag[i]-1][1].m_u32no =
// g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][1]].m_u32no;
//			g_sEqlTri.m_sMark[g_sComb.m_u8matchFlag[i]-1][2].m_u32no =
// g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][2]].m_u32no;
//		}

//	}
//
//		return l_u8cnt;//返回3则代表匹配
//
//}

// u32 TriMatch(u8 *NoofDub,u32 *TriEdge,u16 size,u32 *TriEdgeDect, u16 i)
//{
//	#define max_diff 180
//	u8 l_u8numi,l_u8numj,l_u8numk,l_u8i,l_u8j;
//	u16 l_u16num[3];
//	u32 l_u32edge[3];
//	u16 l_u16size = g_sMarkSets.m_u16size;
//	u8 l_u8No[4] ;
//	u8 l_u8cnt=0;
//	if(g_sComb.m_u8matchFlag[i] ==1)
//	{
//		for (l_u8numk =0 ; l_u8numk < size ; l_u8numk++)
//		{
//			for (l_u8i = 0;l_u8i <3 ;l_u8i++)
//			{
//				l_u8numi = *(NoofDub+l_u8numk*4+l_u8i) ;
//				l_u8numj = *(NoofDub+l_u8numk*4+l_u8i+1);
//				if (l_u8numi > l_u8numj)
//				{
//					u8 tmp = l_u8numi;
//					l_u8numi = l_u8numj;
//					l_u8numj = tmp ;
//				}
//				l_u16num[l_u8i] = l_u8numi*l_u16size -
//((l_u8numi*(l_u8numi+1))>>1)+(l_u8numj-l_u8numi-1);//找出三个边长对应的下标
// l_u32edge[l_u8i] =
//*(TriEdge+l_u16num[l_u8i]);//取出边长，此边长已经进行过排序
//
//			}
//			u32 l_u32len = abs(l_u32edge[0] - *(TriEdgeDect));
////			g_sFilterMark_SpeedCorr.m_u8IsMark[g_sComb.m_u8buf[i][0]] = 0;
////			g_sFilterMark_SpeedCorr.m_u8IsMark[g_sComb.m_u8buf[i][1]] = 0;
////			g_sFilterMark_SpeedCorr.m_u8IsMark[g_sComb.m_u8buf[i][2]] = 0;
//			if (l_u32len < max_diff)
//			{
//				l_u32len = abs(l_u32edge[1] - *(TriEdgeDect +1));
//				if (l_u32len < max_diff)
//				{
//					l_u32len = abs(l_u32edge[2] - *(TriEdgeDect + 2));
//					if (l_u32len < max_diff)
//					{
//						if(g_sFilterMark_SpeedCorr.m_StructMarkScanInfo.m_StructMarkInfo[g_sComb.m_u8buf[i][0]].m_u8IsMark
//== ISMARK && g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][0]].m_u32no
//!=
//*(NoofDub+l_u8numk*4))
//						{
//							memset((void *)&g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][0]] ,
// 0 , 10) ;
// g_sFilterMark_SpeedCorr.m_StructMarkScanInfo.m_StructMarkInfo[g_sComb.m_u8buf[i][0]].m_u8IsMark =
// NOTMARK; 							g_sComb.m_u8matchFlag[i] = 0;
//						}
//						else
//						{
//							g_sFilterMark_SpeedCorr.m_StructMarkScanInfo.m_StructMarkInfo[g_sComb.m_u8buf[i][0]].m_u8IsMark
//= ISMARK; g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][0]].m_u32no
//=
//*(NoofDub+l_u8numk*4);
// g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][0]].m_s32x
//=
// g_sMarkSets.m_sMarkSets[*(NoofDub+l_u8numk*4)].m_s32x;
//							g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][0]].m_s32y =
// g_sMarkSets.m_sMarkSets[*(NoofDub+l_u8numk*4)].m_s32y; 							l_u8cnt++;
// g_sComb.m_u8matchFlag[i] =1;
//
//						}
//
//						if(g_sFilterMark_SpeedCorr.m_StructMarkScanInfo.m_StructMarkInfo[g_sComb.m_u8buf[i][1]].m_u8IsMark
//==ISMARK && g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][1]].m_u32no
//!=
//*(NoofDub+l_u8numk*4+1))
//						{
//							memset((void *)&g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][1]] ,
// 0 , 10) ;
// g_sFilterMark_SpeedCorr.m_StructMarkScanInfo.m_StructMarkInfo[g_sComb.m_u8buf[i][1]].m_u8IsMark =
// NOTMARK; 							g_sComb.m_u8matchFlag[i] =0;
//						}
//						else
//						{
//							g_sFilterMark_SpeedCorr.m_StructMarkScanInfo.m_StructMarkInfo[g_sComb.m_u8buf[i][1]].m_u8IsMark
//= ISMARK; g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][1]].m_u32no
//=
//*(NoofDub+l_u8numk*4+1);
// g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][1]].m_s32x =
// g_sMarkSets.m_sMarkSets[*(NoofDub+l_u8numk*4+1)].m_s32x;
//							g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][1]].m_s32y =
// g_sMarkSets.m_sMarkSets[*(NoofDub+l_u8numk*4+1)].m_s32y; 							l_u8cnt++;
// g_sComb.m_u8matchFlag[i] =1;
//						}
//
//
//						if(g_sFilterMark_SpeedCorr.m_StructMarkScanInfo.m_StructMarkInfo[g_sComb.m_u8buf[i][2]].m_u8IsMark
//==ISMARK && g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][2]].m_u32no
//!=
//*(NoofDub+l_u8numk*4+2))
//						{
//							memset((void *)&g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][2]] ,
// 0 , 10) ;
// g_sFilterMark_SpeedCorr.m_StructMarkScanInfo.m_StructMarkInfo[g_sComb.m_u8buf[i][2]].m_u8IsMark
//=NOTMARK; 							g_sComb.m_u8matchFlag[i] =0;
//						}
//						else
//						{
//							g_sFilterMark_SpeedCorr.m_StructMarkScanInfo.m_StructMarkInfo[g_sComb.m_u8buf[i][2]].m_u8IsMark
//= ISMARK; g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][2]].m_u32no
//=
//*(NoofDub+l_u8numk*4+2);
// g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][2]].m_s32x =
// g_sMarkSets.m_sMarkSets[*(NoofDub+l_u8numk*4+2)].m_s32x;
//							g_sFilterMark_SpeedCorr.m_sMark[g_sComb.m_u8buf[i][2]].m_s32y =
// g_sMarkSets.m_sMarkSets[*(NoofDub+l_u8numk*4+2)].m_s32y; 							l_u8cnt++;
// g_sComb.m_u8matchFlag[i] =1;
//						}
//
//						break;
//
//
//					}
//					else
//						g_sComb.m_u8matchFlag[i] =0;
//
//				}
//				else
//					g_sComb.m_u8matchFlag[i] =0;
//			}
//			else
//				g_sComb.m_u8matchFlag[i] =0;
//		}
//		return l_u8cnt;
//	}
//
//}

// int SelectMarkByAng2(STRUCT_FILTER_TARGET_LITE *pDist, u8 size, STRUCT_FPGAPOS *pFpgaPos)
//{
//
//	static u8 l_u8bufa[COM_BUF_MAX] = {0} ;
//	static u8 l_u8bufb[COM_BUF_MAX] = {0} ;
//	static u8 l_u8size ;
//	u8 *l_u8tmp;
//	u8 l_u8i ;
//	u16 l_u16ang[3] ;
//	u16 l_u16ang2[3] ;
//	//u16 *l_pu16ang = NULL ;
//	u8 l_u8off ;
//	l_u8size = size ;

//	//l_pu16ang = pDist->m_u16Ang  ;
//	if(size<3)
//	  return -1;
//	if(size > COM_BUF_MAX)
//		size = COM_BUF_MAX ;
//	for(l_u8i = 0 ;l_u8i < size ;l_u8i++)
//	{
//		l_u8bufa[l_u8i] = l_u8i ;
//	}
//	memset((void *)&g_sComb, 0 , STRUCT_SIZE_COMBINE );

//	g_sComb.m_u16size =  0 ;
//	combine(l_u8size,3,l_u8bufa,l_u8bufb,3) ;
//	//生成出来的排列组合表
//	for(l_u8i = 0 ;l_u8i < g_sComb.m_u16size ;l_u8i++)
//	{
//		l_u8off = g_sComb.m_u8buf[l_u8i][0] ;
//		l_u16ang[0] = pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u8off].m_u16Ang ;
//		l_u8off = g_sComb.m_u8buf[l_u8i][1] ;
//		l_u16ang[1] = pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u8off].m_u16Ang  ;
//		l_u8off = g_sComb.m_u8buf[l_u8i][2] ;
//		l_u16ang[2] = pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u8off].m_u16Ang  ;
//		//计算夹角的期望误差
//		l_u16ang2[0] = l_u16ang[1] - l_u16ang[0]  ;
//		l_u16ang2[1] = l_u16ang[2] - l_u16ang[1]  ;
//		l_u16ang2[2] = (DEG_PERSCAN + l_u16ang[0] - l_u16ang[2])%DEG_PERSCAN ;
//
//		g_sComb.m_u32val[l_u8i] = Cal_AngDiff_With_PrioAngValue(l_u16ang2,3) ;
//	}

//	//进行排序
//	SortCombine(&g_sComb,3) ;

//	//排序后误差最小的就是第0个

//	//现在得到的是优化的夹角
//	for(l_u8i = 0; l_u8i < 3; l_u8i++)
//	{
//		l_u8tmp = &g_sComb.m_u8buf[0][l_u8i];
//		pFpgaPos->m_sMark[l_u8i].m_s32x = pDist->m_sMark[*l_u8tmp]->m_s32x ;
//		pFpgaPos->m_sMark[l_u8i].m_s32y = pDist->m_sMark[*l_u8tmp]->m_s32y ;
//		pFpgaPos->m_sMark[l_u8i].m_u32no = pDist->m_sMark[*l_u8tmp]->m_u32no ;
//		pFpgaPos->m_u16ang[l_u8i] = pDist->m_StructMarkScanInfo.m_StructMarkInfo[*l_u8tmp].m_u16Ang
//; 		pFpgaPos->m_u32dist[l_u8i] =
// pDist->m_StructMarkScanInfo.m_StructMarkInfo[*l_u8tmp].m_u16Dist ;
//		pFpgaPos->m_sXy2Robot[l_u8i].m_s32x = pDist->m_sXy2Robot[*l_u8tmp].m_s32x ;
//		pFpgaPos->m_sXy2Robot[l_u8i].m_s32y = pDist->m_sXy2Robot[*l_u8tmp].m_s32y ;
//	}

//	return 0 ;
//}

u16 Cosine_Theorem(u16 p_Edge_a, u16 p_Edge_b, float p_Ang)
{
    return sqrt(p_Edge_a * p_Edge_a + p_Edge_b * p_Edge_b - 2 * p_Edge_a * p_Edge_b * cos(p_Ang));
}

void Cal_Edge_Of_TwoMark(STRUCT_FILTER_TARGET* p_MarkInfo,
                         u16 p_CombOffset,
                         u16* p_au16EdgeofDetect)
{
    u8 l_u8off1 = 0, l_u8off2 = 0;
    u16 l_u16IncludeAng = 0;
    float l_fIncludeRad = 0;
    for (u8 l_u8i = 0; l_u8i < 3; l_u8i++)
    {
        l_u8off1 = g_sCombofDecMark.m_u8buf[p_CombOffset][l_u8i];
        l_u8off2 = g_sCombofDecMark.m_u8buf[p_CombOffset][(l_u8i + 1) % 3];
        l_u16IncludeAng = (DEG_PERSCAN + p_MarkInfo->m_StructMarkInfo[l_u8off1].m_u16Ang
                           - p_MarkInfo->m_StructMarkInfo[l_u8off2].m_u16Ang)
                          % DEG_HALFSCAN;
        l_fIncludeRad = l_u16IncludeAng * g_f32Per10mDeg_Rad;
        p_au16EdgeofDetect[l_u8i] = Cosine_Theorem(p_MarkInfo->m_StructMarkInfo[l_u8off1].m_u16Dist,
                                                   p_MarkInfo->m_StructMarkInfo[l_u8off2].m_u16Dist,
                                                   l_fIncludeRad)
                                    >> 4;
    }
}

/*************************************************
Function		:	Match_Mark_By_SetTri
Description		:	对扫描到的靶标进行三角形匹配
Calls			:	无
Called By		: 	TaskTargetApp
Table Accessed	: 	无
Table Updated	:	无
Input			:	p_NAV_Mark	: 扫描的靶标数据
                    size : 扫描的靶标C(n,3)组合个数
                    offset : 匹配靶标C(n,3)组合偏移量
Output			:	无
Return			:	无
Others			:	无
*************************************************/
// int Match_Mark_By_SetTri(STRUCT_FILTER_TARGET_LITE *p_NAV_Mark, u16 p_size,u16 offset,u16
// *p_SetMark_Edge,MARK_SETS *p_SetMark)
//{
//	u8  l_u8ret,l_u8i;
//	u16 l_u16i ;
//	XY_TO_RelCoor *l_s32xyrobot;
//	XY_TO_RelCoor l_s32xy[3];
//	u16 l_au16EdgeofDetect[3] = {0};
//	u8 l_u8off ;

//	//u8 l_u8IsMark = 0;

//	//u16 l_u16tmp;
//
//	l_s32xyrobot = p_NAV_Mark -> m_sXy2Robot;

//	//生成出来的排列组合表
//	//交换排列组合中的相对坐标等信息
//	//g_sComb中buf最多存64种排列组合，因此最大支持8个靶，10个靶需要120的矩阵，若想支持多个靶标，则需要更大的buf将排列组合顺序都保存下
//	for(l_u16i = offset ;l_u16i < p_size;l_u16i++)
//	{
//		l_u8ret=0;
//
//		for(l_u8i = 0; l_u8i < 3 ; l_u8i ++)
//		{
//			l_u8off = g_sCombofDecMark.m_u8buf[l_u16i][l_u8i] ;
//			l_s32xy[l_u8i] = *(l_s32xyrobot + l_u8off) ;
//		}
//
//		g_sCombofDecMark.m_u8buf[l_u16i][3] = g_sCombofDecMark.m_u8buf[l_u16i][0] ;
//
//		//计算每两靶之间距离
//		l_au16EdgeofDetect[0] = Calculate_Point2Point_Dist_Int((l_s32xy[0].m_s32x -
// l_s32xy[1].m_s32x),l_s32xy[0].m_s32y - l_s32xy[1].m_s32y)>>4;
//
//		l_au16EdgeofDetect[1] = Calculate_Point2Point_Dist_Int((l_s32xy[1].m_s32x -
// l_s32xy[2].m_s32x),l_s32xy[1].m_s32y - l_s32xy[2].m_s32y)>>4;
//
//		l_au16EdgeofDetect[2] = Calculate_Point2Point_Dist_Int((l_s32xy[2].m_s32x -
// l_s32xy[0].m_s32x),l_s32xy[2].m_s32y - l_s32xy[0].m_s32y)>>4;
//		//遍历查找相匹配边长
//		if(g_u8IntinalFlag)
//			l_u8ret = Match_LocalMapEdge_of_Detect(&l_au16EdgeofDetect[0],p_SetMark , p_SetMark_Edge
//,l_u16i ,p_NAV_Mark,&g_sLocalMap_Info_LastCircle); 		else 			l_u8ret =
// Match_Edge_of_Detect(&l_au16EdgeofDetect[0],p_SetMark , p_SetMark_Edge ,l_u16i ,p_NAV_Mark);
//
//		if(l_u8ret >0)
//		{
//			g_u16CombOffset = l_u16i+1;
//			break;
//		}
//	}
//	if(l_u16i == p_size )
//	{
//		g_u16CombOffset = p_size;
//		//l_u8ret=0;
//	}
//
//	return l_u8ret ;
//}
/*************************************************
Function		:	Match_Mark_By_SetTri
Description		:	对扫描到的靶标进行三角形匹配
Calls			:	无
Called By		: 	TaskTargetApp
Table Accessed	: 	无
Table Updated	:	无
Input			:	p_NAV_Mark	: 扫描的靶标数据
                    size : 扫描的靶标C(n,3)组合个数
                    offset : 匹配靶标C(n,3)组合偏移量
Output			:	无
Return			:	无
Others			:	无
*************************************************/
u16 Match_Mark_By_SetTri1(STRUCT_FILTER_TARGET_LITE* p_NAV_Mark,
                          u16 p_size,
                          u16 offset,
                          MARK_SETS* p_SetMark)
{
    u32 l_u32Diff_X = 0, l_u32Diff_Y = 0;
    XY_TO_RelCoor** l_s32xyrobot;
    XY_TO_RelCoor* l_s32xy[3];
    u16 l_au16EdgeofDetect[3] = {0};
    u16 l_u16i;
    u8 l_u8off;
    u8 l_u8NextMarkOffset = 0;
    u8 l_u8i;
    u16 l_u16ret;

    l_s32xyrobot = &p_NAV_Mark->m_StructMarkScanInfoAddr.m_sXy2Robot[0];

    for (l_u16i = offset; l_u16i < p_size; l_u16i++)
    {
        l_u16ret = 0;

        for (l_u8i = 0; l_u8i < 3; l_u8i++)
        {
            l_u8off = g_sCombofDecMark.m_u8buf[l_u16i][l_u8i];
            l_s32xy[l_u8i] = l_s32xyrobot[l_u8off];
        }

        g_sCombofDecMark.m_u8buf[l_u16i][3] = g_sCombofDecMark.m_u8buf[l_u16i][0];
        for (l_u8i = 0; l_u8i < 3; l_u8i++)  //依次计算两靶边长
        {
            l_u8NextMarkOffset = (l_u8i + 1) % 3;
            l_u32Diff_X = Return_Diff(l_s32xy[l_u8i]->m_s32x, l_s32xy[l_u8NextMarkOffset]->m_s32x);
            l_u32Diff_Y = Return_Diff(l_s32xy[l_u8i]->m_s32y, l_s32xy[l_u8NextMarkOffset]->m_s32y);
            l_au16EdgeofDetect[l_u8i] = Cal_Edge_TwoMark(l_u32Diff_X, l_u32Diff_Y);
        }

        //遍历查找相匹配边长
        if (g_u8IntinalFlag)
            l_u16ret = Match_LocalMapEdge_of_Detect1(&l_au16EdgeofDetect[0],
                                                     p_SetMark,
                                                     l_u16i,
                                                     p_NAV_Mark,
                                                     &g_sLocalMap_Info_LastCircle);
        else
            l_u16ret = Match_Edge_of_Detect1(&l_au16EdgeofDetect[0], p_SetMark, l_u16i, p_NAV_Mark);

        if (l_u16ret > 0)
        {
            g_u16CombOffset = l_u16i + 1;
            break;
        }
    }
    if (l_u16i == p_size)
    {
        g_u16CombOffset = p_size;
    }

    return l_u16ret;
}

int Cal_Edge_Offset(u16 p_P1, u16 p_P2, u16 p_SetMarkSize)
{
    if (p_P2 > p_P1)
        return p_P1 * p_SetMarkSize - (((p_P1 + 1) * p_P1) >> 1) + (p_P2 - p_P1 - 1);
    else if (p_P1 > p_P2)
        return p_P2 * p_SetMarkSize - (((p_P2 + 1) * p_P2) >> 1) + (p_P1 - p_P2 - 1);
    else
        return -1;
}

// s8 Match_Tri_ThirdMark_LocalMap( u16 *p_SetEdge ,u16 *p_Point,u16 *p_DetectEdge ,u16
// *p_EqlTriCnt,MARK_SETS *p_SetMark,u16 combno,STRUCT_FILTER_TARGET_LITE *p_NAV_Mark,LOCAL_MAP_INFO
// *p_LoaclMap)
//{
//	u16 l_u16i = 0;
//	u16 l_u16Edge_diff = 0;
//	u16 l_u16PointTmp = 0;
//	int l_s32Edge_Offset = 0;
//	u16 l_u16cnt = 0;
//	l_u16cnt = *p_EqlTriCnt;
//	u16 l_u16SetMarkSize = p_SetMark->m_u16size;
//	u8 l_u8LocalMapMarkSize = p_LoaclMap->m_u32MarkNum;
//	for(l_u16i =0 ;l_u16i < l_u8LocalMapMarkSize; l_u16i++)
//	{
//		if(p_LoaclMap->m_uIsMark[l_u16i] != ISMARK)
//			continue;
//
//		if((p_LoaclMap->m_u16MarkOffset[l_u16i] != p_Point[0])&&
//			(p_LoaclMap->m_u16MarkOffset[l_u16i] != p_Point[1])
//		)
//		{
//			l_s32Edge_Offset =
// Cal_Edge_Offset(p_LoaclMap->m_u16MarkOffset[l_u16i],p_Point[1],l_u16SetMarkSize);//根据两个靶标的下标推算边长的查找下标
//			if(*(p_SetEdge + l_s32Edge_Offset) != 0)
//				l_u16Edge_diff = abs(*(p_DetectEdge + 1) - *(p_SetEdge + l_s32Edge_Offset));
//			else
//				continue;
//
//			if(l_u16Edge_diff < g_u16EdgeDiff)
//			{
//				p_Point[2] = p_LoaclMap->m_u16MarkOffset[l_u16i];
//
//				l_s32Edge_Offset = Cal_Edge_Offset(p_Point[0],p_Point[2],l_u16SetMarkSize);
//
//				if(*(p_SetEdge + l_s32Edge_Offset) != 0)
//					l_u16Edge_diff = abs(*(p_DetectEdge + 2) - *(p_SetEdge + l_s32Edge_Offset));
//				else
//					continue;
//
//				if (l_u16Edge_diff < g_u16EdgeDiff)
//				{
//					//匹配到三角形,将信息保存起来
//					if(l_u16cnt >= EQLTRI_SIZE)
//						return -1;
//					for(u8 i = 0 ;i < 3; i++)
//					{
//						memcpy(&g_sEqlTri.m_sMark[l_u16cnt][i],&
// p_SetMark->m_sMarkSets[p_Point[i]],STRUCT_SIZE_MARK_XY); 						g_sEqlTri.buf[i]
// = g_sCombofDecMark.m_u8buf[combno][i]; //保存的为匹配三角形的扫描下标
// if(l_u16cnt == 0)
//						{
//							p_NAV_Mark->m_StructMarkScanInfo.m_StructMarkInfo[g_sEqlTri.buf[i]].m_u8IsMark
//= ISMARK;
// p_NAV_Mark->m_StructMarkScanInfo.m_StructMarkInfo[g_sEqlTri.buf[i]].m_u8MarkSize =
// g_sEqlTri.m_sMark[l_u16cnt][i].m_u8size ;
//							p_NAV_Mark->m_StructMarkScanInfo.m_StructMarkInfo[g_sEqlTri.buf[i]].m_u8MarkType
//= g_sEqlTri.m_sMark[l_u16cnt][i].m_u8shape ;
//							memcpy(&p_NAV_Mark->m_sMark[g_sEqlTri.buf[i]],&g_sEqlTri.m_sMark[l_u16cnt][i],STRUCT_SIZE_MARK_XY);
//						}
//					}
//					l_u16cnt++;
//				}
//			}
//		}
//	}
//	*p_EqlTriCnt = l_u16cnt ;
//	return 0;
//}

s8 Match_Tri_ThirdMark1(u16 p_SetMarkSize,
                        u16* p_Point,
                        u16* p_DetectEdge,
                        u16* p_EqlTriCnt,
                        MARK_SETS* p_SetMark,
                        u16 combno,
                        STRUCT_FILTER_TARGET_LITE* p_NAV_Mark)
{
    u16 l_u16i = 0;
    u32 l_u32Edge_diff = 0;
    // u16 l_u16PointTmp = 0;
    int l_s32Edge_Offset = 0;
    u16 l_u16cnt = 0;
    u16 l_u16Offset;
    l_u16cnt = *p_EqlTriCnt;
    MARK_XY* l_sMark = p_SetMark->m_sMarkSets;
    STRUCT_MARK_INFO** l_psMarkInfo = &p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[0];
    for (l_u16i = 0; l_u16i < p_SetMarkSize; l_u16i++)
    {
        if ((l_u16i != p_Point[0]) && (l_u16i != p_Point[1]))
        {
            l_u32Edge_diff =
                Return_Diff_Edge(l_sMark + l_u16i, l_sMark + p_Point[1], *(p_DetectEdge + 1));

            if (l_u32Edge_diff < g_u16EdgeDiff)
            {
                p_Point[2] = l_u16i;

                l_u32Edge_diff =
                    Return_Diff_Edge(l_sMark + l_u16i, l_sMark + p_Point[0], *(p_DetectEdge + 2));
                if (l_u32Edge_diff < g_u16EdgeDiff)
                {
                    //匹配到三角形,将信息保存起来
                    if (l_u16cnt >= EQLTRI_SIZE)
                        return -1;
                    for (u8 i = 0; i < 3; i++)
                    {
                        // memcpy(&g_sEqlTri.m_sMark[l_u16cnt][i],&
                        // p_SetMark->m_sMarkSets[p_Point[i]],STRUCT_SIZE_MARK_XY);
                        g_sEqlTri.m_psSetMarkAddr[l_u16cnt][i] = &l_sMark[p_Point[i]];
                        if (l_u16cnt == 0)
                        {
                            l_u16Offset = g_sCombofDecMark.m_u8buf[combno][i];
                            g_sEqlTri.buf[i] = l_u16Offset;  //保存的为匹配三角形的扫描下标
                            l_psMarkInfo[l_u16Offset]->m_u8IsMark = ISMARK;
                            l_psMarkInfo[l_u16Offset]->m_u8MarkSize = l_sMark[p_Point[i]].m_u8size;
                            l_psMarkInfo[l_u16Offset]->m_u8MarkType = l_sMark[p_Point[i]].m_u8shape;
                            // memcpy(&p_NAV_Mark->m_sMark[g_sEqlTri.buf[i]],&g_sEqlTri.m_sMark[l_u16cnt][i],STRUCT_SIZE_MARK_XY);
                        }
                    }
                    l_u16cnt++;
                }
            }
        }
    }

    *p_EqlTriCnt = l_u16cnt;
    return 0;
}
// s8 Match_Tri_ThirdMark(u16 p_SetMarkSize , u16 *p_SetEdge ,u16 *p_Point,u16 *p_DetectEdge ,u16
// *p_EqlTriCnt,MARK_SETS *p_SetMark,u16 combno,STRUCT_FILTER_TARGET_LITE *p_NAV_Mark)
//{
//	u16 l_u16i = 0;
//	u16 l_u16Edge_diff = 0;
//	u16 l_u16PointTmp = 0;
//	int l_s32Edge_Offset = 0;
//	u16 l_u8cnt = 0;
//	l_u8cnt = *p_EqlTriCnt;
//	for(l_u16i =0 ;l_u16i < p_SetMarkSize; l_u16i++)
//	{
//		l_s32Edge_Offset =
// Cal_Edge_Offset(l_u16i,p_Point[1],p_SetMarkSize);//根据两个靶标的下标推算边长的查找下标 		if(
//(l_s32Edge_Offset!= -1) &&
//			(*(p_SetEdge + l_s32Edge_Offset) != 0))
//			l_u16Edge_diff = abs(*(p_DetectEdge + 1) - *(p_SetEdge + l_s32Edge_Offset));
//		else
//			continue;
//
//		if (l_u16Edge_diff < g_u16EdgeDiff)
//		{
//			l_u16PointTmp = l_u16i;
//			if(l_u16PointTmp != p_Point[0])
//			{
//				p_Point[2] = l_u16PointTmp;
//
//				l_s32Edge_Offset = Cal_Edge_Offset(p_Point[0],p_Point[2],p_SetMarkSize);
//
//				if((l_s32Edge_Offset != -1) &&
//					(*(p_SetEdge + l_s32Edge_Offset) != 0))
//					l_u16Edge_diff = abs(*(p_DetectEdge + 2) - *(p_SetEdge + l_s32Edge_Offset));
//				else
//					continue;
//
//				if (l_u16Edge_diff < g_u16EdgeDiff)
//				{
//					//匹配到三角形,将信息保存起来
//					if(l_u8cnt >= EQLTRI_SIZE)
//						return -1;
//					for(u8 i = 0 ;i < 3; i++)
//					{
//						memcpy(&g_sEqlTri.m_sMark[l_u8cnt][i],&
// p_SetMark->m_sMarkSets[p_Point[i]],STRUCT_SIZE_MARK_XY); 						g_sEqlTri.buf[i]
// = g_sCombofDecMark.m_u8buf[combno][i]; //保存的为匹配三角形的扫描下标
// if(l_u8cnt == 0)
//						{
//							p_NAV_Mark->m_StructMarkScanInfo.m_StructMarkInfo[g_sEqlTri.buf[i]].m_u8IsMark
//= ISMARK;
// p_NAV_Mark->m_StructMarkScanInfo.m_StructMarkInfo[g_sEqlTri.buf[i]].m_u8MarkSize =
// g_sEqlTri.m_sMark[l_u8cnt][i].m_u8size ;
//							p_NAV_Mark->m_StructMarkScanInfo.m_StructMarkInfo[g_sEqlTri.buf[i]].m_u8MarkType
//= g_sEqlTri.m_sMark[l_u8cnt][i].m_u8shape ;
//							memcpy(&p_NAV_Mark->m_sMark[g_sEqlTri.buf[i]],&g_sEqlTri.m_sMark[l_u8cnt][i],STRUCT_SIZE_MARK_XY);
//						}
//					}
//					l_u8cnt++;
//				}
//			}
//		}
//	}
//	*p_EqlTriCnt = l_u8cnt ;
//	return 0;
//}
/*************************************************
Function		:	Match_Edge_of_Detect
Description		:	对选取的三个靶组成的三角形边长进行匹配
Calls			:	无
Called By		: 	TaskTargetApp
Table Accessed	: 	无
Table Updated	:	无
Input			:	edge: 选取的三个靶组成的三角形边长
                    SetMark : 设置的靶标
                    TriEdge : 设置的靶标两两间的边长
                    combno : 扫描靶标C(n,3)组合偏移量
                    p_NAV_Mark : 扫描到的靶标信息
Output			:	无
Return			:	无
Others			:	无
*************************************************/
// u32 Match_Edge_of_Detect(u16 *edge, MARK_SETS *SetMark, u16 *TriEdge , u16 combno ,
// STRUCT_FILTER_TARGET_LITE *p_NAV_Mark)
//{
//	u32 l_u32i;
//	u16 l_u16Point[3]={0};
//	u16 l_u16SetMarkSize;
//	u16 l_u16cnt =0;
//	u16 l_u16Edge_diff = 0;
//	u16 l_u16tmp = 0;
//	s8 l_s8ret;

//
//	l_u16SetMarkSize = SetMark->m_u16size;

//	for (l_u32i = 0;l_u32i < g_u32SizeofSetEdge ;l_u32i++)
//	{
//		//反推靶标号码
//		if(l_u32i < Sub(l_u16Point[0] ,l_u16SetMarkSize))
////根据l_u32i反推出组成第l_u32i个边长的两个靶标序号,存入l_u16Point[0],l_u16Point[1]
//		{
//			l_u16Point[1]  ++;
//		}
//		else
//		{
//			l_u16Point[0] ++ ;
//			l_u16Point[1]=l_u16Point[0] +1;
//		}
//
//		if(*(TriEdge+l_u32i) != 0)
//			l_u16Edge_diff = abs(*edge - *(TriEdge + l_u32i));
//		else
//			continue;
//
//		if (l_u16Edge_diff < g_u16EdgeDiff)
//		{
//			//查找(0,m)、…… 、((m-1),m)、(m,m+1)、……、(m,n)
//			l_s8ret = Match_Tri_ThirdMark(l_u16SetMarkSize, TriEdge ,l_u16Point,edge
//,&l_u16cnt,SetMark,combno,p_NAV_Mark); 			if(!l_s8ret)
//			{
//				//将匹配到的point0和point1交换，因不知匹配到的直线是什么方向
//				l_u16tmp = l_u16Point[0];
//				l_u16Point[0] = l_u16Point[1] ;
//				l_u16Point[1] = l_u16tmp;
//
//				l_s8ret = Match_Tri_ThirdMark(l_u16SetMarkSize, TriEdge ,l_u16Point,edge
//,&l_u16cnt,SetMark,combno,p_NAV_Mark); 				if(l_s8ret) 					return 0;
// l_u16tmp
//= l_u16Point[0]; 				l_u16Point[0] = l_u16Point[1] ; 				l_u16Point[1] =
// l_u16tmp;
//			}
//			else
//				return 0;
//		}
//	}
//	g_sEqlTri.m_u16In = l_u16cnt;//全等三角形个数
//	return l_u16cnt;
//}

// u16 Match_Edge_of_Detect1(u16 *edge, MARK_SETS *p_SetMark , u16 combno ,
// STRUCT_FILTER_TARGET_LITE *p_NAV_Mark)
//{
//	u16 l_u16Point[3];
//	u32 l_u32diff_x,l_u32diff_y;
//	u16 l_u16SetMarkSize = p_SetMark->m_u16size;
//	u16 l_u16Edge_diff = 0;
//	u16 l_u16cnt = 0;
//	MARK_XY *l_sMark = p_SetMark->m_sMarkSets;
//
//	for(u16 l_u16i = 0;l_u16i < l_u16SetMarkSize;l_u16i++)
//	{
//		for(u16 l_u16j = l_u16i+1;l_u16j < l_u16SetMarkSize;l_u16j++)
//		{
//			l_u16Edge_diff = Return_Diff_Edge(l_sMark + l_u16i ,l_sMark + l_u16j,*edge);
//			if (l_u16Edge_diff < g_u16EdgeDiff)
//			{
//				l_u16Point[0] = l_u16i;
//				l_u16Point[1] = l_u16j;
//				for(u16 l_u16k = 0;l_u16k < l_u16SetMarkSize;l_u16k++)
//				{
//					if(l_u16k != l_u16j)
//					{
//						l_u16Edge_diff = Return_Diff_Edge(l_sMark + l_u16k ,l_sMark +
// l_u16j,*(edge+1));
//
//						if (l_u16Edge_diff < g_u16EdgeDiff)
//						{
//							l_u16Point[2] = l_u16k;
//							l_u16Edge_diff = Return_Diff_Edge(l_sMark + l_u16k ,l_sMark +
// l_u16i,*(edge+2));
//
//							if (l_u16Edge_diff < g_u16EdgeDiff)
//							{
//								if(l_u16cnt >= EQLTRI_SIZE)
//									return 0;
//								for(u8 i = 0 ;i < 3; i++)
//								{
//									memcpy(&g_sEqlTri.m_sMark[l_u16cnt][i],&
// p_SetMark->m_sMarkSets[l_u16Point[i]],STRUCT_SIZE_MARK_XY);
// g_sEqlTri.buf[i] = g_sCombofDecMark.m_u8buf[combno][i]; //保存的为匹配三角形的扫描下标
// if(l_u16cnt == 0)
//									{
//										p_NAV_Mark->m_StructMarkScanInfo.m_StructMarkInfo[g_sEqlTri.buf[i]].m_u8IsMark
//= ISMARK;
// p_NAV_Mark->m_StructMarkScanInfo.m_StructMarkInfo[g_sEqlTri.buf[i]].m_u8MarkSize =
// g_sEqlTri.m_sMark[l_u16cnt][i].m_u8size ;
//										p_NAV_Mark->m_StructMarkScanInfo.m_StructMarkInfo[g_sEqlTri.buf[i]].m_u8MarkType
//= g_sEqlTri.m_sMark[l_u16cnt][i].m_u8shape ;
//										memcpy(&p_NAV_Mark->m_sMark[g_sEqlTri.buf[i]],&g_sEqlTri.m_sMark[l_u16cnt][i],STRUCT_SIZE_MARK_XY);
//									}
//								}
//								l_u16cnt++;
//							}
//						}
//					}
//				}
//
//				l_u16Point[0] = l_u16j;
//				l_u16Point[1] = l_u16i;
//				for(u16 l_u16k = 0;l_u16k < l_u16SetMarkSize;l_u16k++)
//				{
//					if(l_u16k != l_u16j)
//					{
//						l_u16Edge_diff = Return_Diff_Edge(l_sMark + l_u16k ,l_sMark +
// l_u16i,*(edge+1));
//
//						if (l_u16Edge_diff < g_u16EdgeDiff)
//						{
//							l_u16Point[2] = l_u16k;
//							l_u16Edge_diff = Return_Diff_Edge(l_sMark + l_u16k ,l_sMark +
// l_u16j,*(edge+2));
//
//							if (l_u16Edge_diff < g_u16EdgeDiff)
//							{
//								if(l_u16cnt >= EQLTRI_SIZE)
//									return 0;
//								for(u8 i = 0 ;i < 3; i++)
//								{
//									memcpy(&g_sEqlTri.m_sMark[l_u16cnt][i],&
// p_SetMark->m_sMarkSets[l_u16Point[i]],STRUCT_SIZE_MARK_XY);
// g_sEqlTri.buf[i] = g_sCombofDecMark.m_u8buf[combno][i]; //保存的为匹配三角形的扫描下标
// if(l_u16cnt == 0)
//									{
//										p_NAV_Mark->m_StructMarkScanInfo.m_StructMarkInfo[g_sEqlTri.buf[i]].m_u8IsMark
//= ISMARK;
// p_NAV_Mark->m_StructMarkScanInfo.m_StructMarkInfo[g_sEqlTri.buf[i]].m_u8MarkSize =
// g_sEqlTri.m_sMark[l_u16cnt][i].m_u8size ;
//										p_NAV_Mark->m_StructMarkScanInfo.m_StructMarkInfo[g_sEqlTri.buf[i]].m_u8MarkType
//= g_sEqlTri.m_sMark[l_u16cnt][i].m_u8shape ;
//										memcpy(&p_NAV_Mark->m_sMark[g_sEqlTri.buf[i]],&g_sEqlTri.m_sMark[l_u16cnt][i],STRUCT_SIZE_MARK_XY);
//									}
//								}
//								l_u16cnt++;
//							}
//						}
//					}
//				}
//			}
//		}
//	}

//	g_sEqlTri.m_u16In = l_u16cnt;//全等三角形个数
//	return l_u16cnt;
//}

u16 Match_Edge_of_Detect1(u16* edge,
                          MARK_SETS* p_SetMark,
                          u16 combno,
                          STRUCT_FILTER_TARGET_LITE* p_NAV_Mark)
{
    u16 l_u16Point[3];
    u32 l_u32diff_x, l_u32diff_y;
    u16 l_u16SetMarkSize = p_SetMark->m_u16size;
    u32 l_u32Edge_diff = 0;
    u16 l_u16cnt = 0;
    MARK_XY* l_sMark = p_SetMark->m_sMarkSets;
    s8 l_s8ret;
    u16 l_u16tmp;

    for (u16 l_u16i = 0; l_u16i < l_u16SetMarkSize; l_u16i++)
    {
        for (u16 l_u16j = l_u16i + 1; l_u16j < l_u16SetMarkSize; l_u16j++)
        {
            l_u32Edge_diff = Return_Diff_Edge(l_sMark + l_u16i, l_sMark + l_u16j, *edge);
            if (l_u32Edge_diff < g_u16EdgeDiff)
            {
                l_u16Point[0] = l_u16i;
                l_u16Point[1] = l_u16j;
                //查找(0,m)、…… 、((m-1),m)、(m,m+1)、……、(m,n)
                l_s8ret = Match_Tri_ThirdMark1(
                    l_u16SetMarkSize, l_u16Point, edge, &l_u16cnt, p_SetMark, combno, p_NAV_Mark);
                if (!l_s8ret)
                {
                    //将匹配到的point0和point1交换，因不知匹配到的直线是什么方向
                    l_u16tmp = l_u16Point[0];
                    l_u16Point[0] = l_u16Point[1];
                    l_u16Point[1] = l_u16tmp;

                    l_s8ret = Match_Tri_ThirdMark1(l_u16SetMarkSize,
                                                   l_u16Point,
                                                   edge,
                                                   &l_u16cnt,
                                                   p_SetMark,
                                                   combno,
                                                   p_NAV_Mark);
                    if (l_s8ret)
                        return 0;
                    l_u16tmp = l_u16Point[0];
                    l_u16Point[0] = l_u16Point[1];
                    l_u16Point[1] = l_u16tmp;
                }
                else
                    return 0;
            }
        }
    }

    g_sEqlTri.m_u8In = l_u16cnt;  //全等三角形个数
    return l_u16cnt;
}
///*************************************************
// Function		:	Match_LocalMapEdge_of_Detect
// Description		:	对选取的三个靶组成的三角形边长进行匹配
// Calls			:	无
// Called By		: 	TaskTargetApp
// Table Accessed	: 	无
// Table Updated	:	无
// Input			:	edge: 选取的三个靶组成的三角形边长
//					SetMark : 设置的靶标
//					TriEdge : 设置的靶标两两间的边长
//					combno : 扫描靶标C(n,3)组合偏移量
//					p_NAV_Mark : 扫描到的靶标信息
// Output			:	无
// Return			:	无
// Others			:	无
//*************************************************/
// u32 Match_LocalMapEdge_of_Detect(u16 *edge, MARK_SETS *SetMark, u16 *TriEdge , u16 combno ,
// STRUCT_FILTER_TARGET_LITE *p_NAV_Mark,LOCAL_MAP_INFO *p_LoaclMap)
//{
//	u8 l_u8i,l_u8j;
//	u16 l_u16Point[3]={0};
//	u16 l_u16SetMarkSize;
//	u16 l_u16cnt =0;
//	u16 l_u16Edge_diff = 0;
//	u16 l_u16tmp = 0;
//	s8 l_s8ret;
//	u8 l_u8LocalMarkNum = p_LoaclMap->m_u32MarkNum;
//	int l_s32Edge_Offset;
//
//	l_u16SetMarkSize = SetMark->m_u16size;

//	for (l_u8i = 0;l_u8i < l_u8LocalMarkNum ;l_u8i++)
//	{
//		if(p_LoaclMap->m_uIsMark[l_u8i] != ISMARK)
//			continue;
//
//		for(l_u8j = l_u8i + 1;l_u8j < l_u8LocalMarkNum ;l_u8j++)
//		{
//			if(p_LoaclMap->m_uIsMark[l_u8j] != ISMARK)
//				continue;
//
//			l_s32Edge_Offset =
// Cal_Edge_Offset(p_LoaclMap->m_u16MarkOffset[l_u8i],p_LoaclMap->m_u16MarkOffset[l_u8j],SetMark->m_u16size);//根据两个靶标的下标推算边长的查找下标
//			if(*(TriEdge+l_u8i) != 0)
//				l_u16Edge_diff = abs(*edge - *(TriEdge + l_s32Edge_Offset));
//			else
//				continue;
//			if(l_u16Edge_diff < g_u16EdgeDiff)
//			{
//				l_u16Point[0] = p_LoaclMap->m_u16MarkOffset[l_u8i] ;
//				l_u16Point[1] = p_LoaclMap->m_u16MarkOffset[l_u8j] ;
//				//查找(0,m)、…… 、((m-1),m)、(m,m+1)、……、(m,n)
//				l_s8ret = Match_Tri_ThirdMark_LocalMap( TriEdge ,l_u16Point,edge
//,&l_u16cnt,SetMark,combno,p_NAV_Mark,p_LoaclMap); 				if(!l_s8ret)
//				{
//					//将匹配到的point0和point1交换，因不知匹配到的直线是什么方向
//					l_u16tmp = l_u16Point[0];
//					l_u16Point[0] = l_u16Point[1] ;
//					l_u16Point[1] = l_u16tmp;
//
//					l_s8ret = Match_Tri_ThirdMark_LocalMap( TriEdge ,l_u16Point,edge
//,&l_u16cnt,SetMark,combno,p_NAV_Mark,p_LoaclMap); 					if(l_s8ret)
// return 0; l_u16tmp = l_u16Point[0]; 					l_u16Point[0] = l_u16Point[1] ;
// l_u16Point[1] = l_u16tmp;
//				}
//				else
//					return 0;
//			}
//		}
//	}
//	g_sEqlTri.m_u16In = l_u16cnt;//全等三角形个数
//	return l_u16cnt;
//}

s8 Match_Tri_ThirdMark_LocalMap1(MARK_XY** p_MatchMark,
                                 u16* p_DetectEdge,
                                 u16* p_EqlTriCnt,
                                 MARK_SETS* p_SetMark,
                                 u16 combno,
                                 STRUCT_FILTER_TARGET_LITE* p_NAV_Mark,
                                 LOCAL_MAP_INFO* p_LoaclMap)
{
    u16 l_u16i = 0;
    u32 l_u32Edge_diff = 0;
    u16 l_u16PointTmp = 0;
    int l_s32Edge_Offset = 0;
    u16 l_u16cnt = 0;
    l_u16cnt = *p_EqlTriCnt;
    u16 l_u16SetMarkSize = p_SetMark->m_u16size;
    u16 l_u16LocalMapMarkSize = p_LoaclMap->m_u32MarkTotalNum;
    MARK_XY* l_sMark = &p_SetMark->m_sMarkSets[0];
    STRUCT_MARK_INFO** l_psMarkInfo = &p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[0];
    u16 l_u16Offset;
    for (l_u16i = 0; l_u16i < l_u16LocalMapMarkSize; l_u16i++)
    {
        if (p_LoaclMap->m_uIsMark[l_u16i] != ISMARK)
            continue;

        if ((p_LoaclMap->m_pStructSetMark[l_u16i] != p_MatchMark[0])
            && (p_LoaclMap->m_pStructSetMark[l_u16i] != p_MatchMark[1]))
        {
            l_u32Edge_diff = Return_Diff_Edge(
                p_LoaclMap->m_pStructSetMark[l_u16i], p_MatchMark[1], *(p_DetectEdge + 1));
            if (l_u32Edge_diff < g_u16EdgeDiff)
            {
                p_MatchMark[2] = p_LoaclMap->m_pStructSetMark[l_u16i];
                l_u32Edge_diff =
                    Return_Diff_Edge(p_MatchMark[2], p_MatchMark[0], *(p_DetectEdge + 2));
                if (l_u32Edge_diff < g_u16EdgeDiff)
                {
                    //匹配到三角形,将信息保存起来
                    if (l_u16cnt >= EQLTRI_SIZE)
                        return -1;
                    for (u8 i = 0; i < 3; i++)
                    {
                        // memcpy(&g_sEqlTri.m_sMark[l_u16cnt][i],p_MatchMark[i],STRUCT_SIZE_MARK_XY);
                        g_sEqlTri.m_psSetMarkAddr[l_u16cnt][i] = p_MatchMark[i];
                        if (l_u16cnt == 0)
                        {
                            l_u16Offset = g_sCombofDecMark.m_u8buf[combno][i];
                            g_sEqlTri.buf[i] = l_u16Offset;
                            l_psMarkInfo[l_u16Offset]->m_u8IsMark = ISMARK;
                            l_psMarkInfo[l_u16Offset]->m_u8MarkSize = p_MatchMark[i]->m_u8size;
                            l_psMarkInfo[l_u16Offset]->m_u8MarkType = p_MatchMark[i]->m_u8shape;
                            p_NAV_Mark->m_psSetMarkAddr[l_u16Offset] = p_MatchMark[i];
                            // memcpy(&p_NAV_Mark->m_sMark[g_sEqlTri.buf[i]],&g_sEqlTri.m_sMark[l_u16cnt][i],STRUCT_SIZE_MARK_XY);
                        }
                    }
                    l_u16cnt++;
                }
            }
        }
    }
    *p_EqlTriCnt = l_u16cnt;
    return 0;
}

/*************************************************
Function		:	Match_LocalMapEdge_of_Detect
Description		:	对选取的三个靶组成的三角形边长进行匹配
Calls			:	无
Called By		: 	TaskTargetApp
Table Accessed	: 	无
Table Updated	:	无
Input			:	edge: 选取的三个靶组成的三角形边长
                    SetMark : 设置的靶标
                    TriEdge : 设置的靶标两两间的边长
                    combno : 扫描靶标C(n,3)组合偏移量
                    p_NAV_Mark : 扫描到的靶标信息
Output			:	无
Return			:	无
Others			:	无
*************************************************/
u16 Match_LocalMapEdge_of_Detect1(u16* edge,
                                  MARK_SETS* SetMark,
                                  u16 combno,
                                  STRUCT_FILTER_TARGET_LITE* p_NAV_Mark,
                                  LOCAL_MAP_INFO* p_LoaclMap)
{
    u8 l_u8i, l_u8j;
    MARK_XY *l_psMatchMark[3] = {NULL}, *l_psMatchTmp;
    u16 l_u16SetMarkSize;
    u16 l_u16cnt = 0;
    u32 l_u32Edge_diff = 0;

    s8 l_s8ret;
    u16 l_u16LocalMarkNum = p_LoaclMap->m_u32MarkTotalNum;
    int l_s32Edge_Offset;
    MARK_XY* l_sMark = &SetMark->m_sMarkSets[0];

    l_u16SetMarkSize = SetMark->m_u16size;

    for (l_u8i = 0; l_u8i < l_u16LocalMarkNum; l_u8i++)
    {
        if (p_LoaclMap->m_uIsMark[l_u8i] != ISMARK)
            continue;

        for (l_u8j = l_u8i + 1; l_u8j < l_u16LocalMarkNum; l_u8j++)
        {
            if (p_LoaclMap->m_uIsMark[l_u8j] != ISMARK)
                continue;

            l_u32Edge_diff = Return_Diff_Edge(
                p_LoaclMap->m_pStructSetMark[l_u8i], p_LoaclMap->m_pStructSetMark[l_u8j], *edge);
            if (l_u32Edge_diff < g_u16EdgeDiff)
            {
                l_psMatchMark[0] = p_LoaclMap->m_pStructSetMark[l_u8i];
                l_psMatchMark[1] = p_LoaclMap->m_pStructSetMark[l_u8j];
                //查找(0,m)、…… 、((m-1),m)、(m,m+1)、……、(m,n)
                l_s8ret = Match_Tri_ThirdMark_LocalMap1(
                    l_psMatchMark, edge, &l_u16cnt, SetMark, combno, p_NAV_Mark, p_LoaclMap);
                if (!l_s8ret)
                {
                    //将匹配到的point0和point1交换，因不知匹配到的直线是什么方向
                    l_psMatchTmp = l_psMatchMark[0];
                    l_psMatchMark[0] = l_psMatchMark[1];
                    l_psMatchMark[1] = l_psMatchTmp;

                    l_s8ret = Match_Tri_ThirdMark_LocalMap1(
                        l_psMatchMark, edge, &l_u16cnt, SetMark, combno, p_NAV_Mark, p_LoaclMap);
                    if (l_s8ret)
                        return 0;
                    l_psMatchTmp = l_psMatchMark[0];
                    l_psMatchMark[0] = l_psMatchMark[1];
                    l_psMatchMark[1] = l_psMatchTmp;
                }
                else
                    return 0;
            }
        }
    }
    g_sEqlTri.m_u8In = l_u16cnt;  //全等三角形个数
    return l_u16cnt;
}
u32 Sub(u16 a, u16 n)
{
    u32 l_u32sub = (a + 1) * n - (((a + 2) * (a + 1)) >> 1);
    return l_u32sub;
}
//顺序计算每两个靶间的边长，并存入flash
/*************************************************
Function		: CalSetMarkForEdge
Description		: 顺序计算每两个靶间的边长，并存入flash
Calls			: 无
Called By		: 无
Table Accessed	: 无
Table Updated	: 无
Input			: p_SetMark:靶标首地址, p_TriEdge:存边长buf首地址
Output			: 无
Return			: 无
Others			: 无
*************************************************/
u32 CalSetMarkForEdge(MARK_SETS* p_SetMark, u16* p_TriEdge)
{
    u16 l_u16i, l_u16j;
    u16 l_u16size = p_SetMark->m_u16size;  //该层靶标总个数
    u32 l_u32num = 0;
    int l_s32x[2], l_s32y[2], l_s32X_diff, l_s32Y_diff;
    u64 l_u64sqrtdist;

    memset((void*)p_TriEdge, 0, Mark_Edge_NUM << 1);

    //按照顺序计算每两靶之间的距离
    for (l_u16i = 0; l_u16i < l_u16size; l_u16i++)
    {
        for (l_u16j = l_u16i + 1; l_u16j < l_u16size; l_u16j++)
        {
            l_s32x[0] = p_SetMark->m_sMarkSets[l_u16i].m_s32x;
            l_s32x[1] = p_SetMark->m_sMarkSets[l_u16j].m_s32x;
            l_s32y[0] = p_SetMark->m_sMarkSets[l_u16i].m_s32y;
            l_s32y[1] = p_SetMark->m_sMarkSets[l_u16j].m_s32y;
            l_s32X_diff = l_s32x[0] - l_s32x[1];
            l_s32Y_diff = l_s32y[0] - l_s32y[1];
            l_u64sqrtdist =
                Calculate_Point2Point_Dist_Int(l_s32X_diff, l_s32Y_diff) >> 4;  //计算边长

            if (l_u64sqrtdist >= ((g_sMarkMatch_Set.m_u32MarkScan_Max << 1) >> 4))
            {
                l_u64sqrtdist = 0;
            }
            *(p_TriEdge + l_u32num) = (u16)l_u64sqrtdist;
            l_u32num++;
        }
    }
    g_u32SizeofSetEdge = (l_u16size * (l_u16size - 1)) >> 1;
    return 0;
}
u8 Select_MarkAng_Distribution_Most_Uniform(
    u8 p_SeltMarkNum,
    STRUCT_FILTER_TARGET_LITE* p_Nav_Mark,
    STRUCT_FILTER_TARGET_LITE* p_SeltMark)  //选择分布最均匀的p_SeltMarkNum靶
{
    CalAndSort_Ang_Distribution_Value(p_SeltMarkNum, p_Nav_Mark);  //计算角度与平均角度值,并排序
    Copy_Mark_To_NavMark_Buf(
        p_SeltMarkNum, p_SeltMark, p_Nav_Mark);  //取出分布最均匀的,p_SeltMarkNum个靶
    return 0;
}

void CalAndSort_Ang_Distribution_Value(u8 p_SeltMarkNum, STRUCT_FILTER_TARGET_LITE* p_Nav_Mark)
{
    Get_Ang_Distribution_Value(p_SeltMarkNum, p_Nav_Mark);  //计算角度与平均角度值
    if (g_sComb.m_u16size > 1)
        SortCombine(&g_sComb, p_SeltMarkNum);  //对n组组合值排序
}

void Get_Ang_Distribution_Value(u8 p_SeltMarkNum, STRUCT_FILTER_TARGET_LITE* p_Nav_Mark)
{
    u8 l_u8MarkSize = p_Nav_Mark->m_StructMarkScanInfoAddr.m_u8In;
    Get_Combination(l_u8MarkSize, p_SeltMarkNum);  // p_SeltMarkNum个靶的n组组合
    if (g_sComb.m_u16size > 1)
        Cal_Ang_Distribution_Value(
            &g_sComb, p_SeltMarkNum, p_Nav_Mark);  //计算n组组合的角度与平均角度值
}

void Cal_Ang_Distribution_Value(COMBINE* p_Comb,
                                u8 p_SeltMarkNum,
                                STRUCT_FILTER_TARGET_LITE* p_Nav_Mark)
{
    u8 l_u8i = 0, l_u8j = 0;
    u8 l_u8off[2] = {0};
    int l_s32ang[2] = {0};
    u16 l_u16AngDiff[5] = {0};
    for (l_u8i = 0; l_u8i < p_Comb->m_u16size; l_u8i++)
    {
        for (l_u8j = 0; l_u8j < p_SeltMarkNum; l_u8j++)
        {
            l_u8off[0] = g_sComb.m_u8buf[l_u8i][l_u8j];
            l_u8off[1] = g_sComb.m_u8buf[l_u8i][(l_u8j + 1) % p_SeltMarkNum];
            l_s32ang[0] =
                p_Nav_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8off[0]]->m_u16Ang;
            l_s32ang[1] =
                p_Nav_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8off[1]]->m_u16Ang;
            l_u16AngDiff[l_u8j] = (DEG_PERSCAN + l_s32ang[1] - l_s32ang[0]) % DEG_PERSCAN;
        }
        g_sComb.m_u32val[l_u8i] = Cal_AngDiff_With_PrioAngValue(l_u16AngDiff, p_SeltMarkNum);
    }
}
void Get_Combination(u8 p_Combine_N, u8 p_Combine_X)
{
    u8 l_u8i = 0;
    static u8 l_u8bufa[COM_BUF_MAX] = {0};
    static u8 l_u8bufb[COM_BUF_MAX] = {0};
    for (l_u8i = 0; l_u8i < p_Combine_N; l_u8i++)
    {
        l_u8bufa[l_u8i] = l_u8i;  // i位写i
    }
    memset((void*)&g_sComb, 0, STRUCT_SIZE_COMBINE);

    g_sComb.m_u16size = 0;

    combine(p_Combine_N, p_Combine_X, l_u8bufa, l_u8bufb, p_Combine_X);
}

// int SelectMarkByAngCX5(STRUCT_FILTER_TARGET_LITE *p_Nav_Mark, u8 size,u8 p_CombNum)
//{
//	static u8 l_u8bufa[COM_BUF_MAX] = {0} ;
//	static u8 l_u8bufb[COM_BUF_MAX] = {0} ;
//	static u8 l_u8size ;
//	u8 l_u8i,l_u8j ;
//	u16 l_u16ang[5] ;
//	u16 l_u16ang2[5] ;
//	u16 *l_pu16ang = NULL ;
//	u8 l_u8off ;
//

//	l_u8size = size ;
//	if(l_u8size<5)
//	{
//		return -1;
//	}
//
//
//	for(l_u8i = 0 ;l_u8i < size ;l_u8i++)
//	{
//		l_u8bufa[l_u8i] = l_u8i ;//i位写i
//	}
//	memset((void *)&g_sComb, 0 , STRUCT_SIZE_COMBINE);

//	g_sComb.m_u16size =  0 ;

//	combine(l_u8size,p_CombNum,l_u8bufa,l_u8bufb,p_CombNum) ;
//	//生成出来的排列组合表
//	for(l_u8i = 0 ;l_u8i < g_sComb.m_u16size ;l_u8i++)
//	{
//		//l_pu16ang = pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u8i].m_Struct_MarkBase.m_u16Ang
//; 		for(l_u8j = 0;l_u8j<p_CombNum;l_u8j++)
//		{
//			l_u8off = g_sComb.m_u8buf[l_u8i][l_u8j] ;
//			l_u16ang[l_u8j] =
// p_Nav_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8off]->m_u16Ang
//;
//		}
//		for(l_u8j = 0;l_u8j<p_CombNum;l_u8j++)
//		{
//			l_u16ang2[l_u8j] = (DEG_PERSCAN + l_u16ang[(l_u8j + 1)%p_CombNum] -
// l_u16ang[l_u8j])%DEG_PERSCAN ;
//		}
//		g_sComb.m_u32val[l_u8i] = Cal_AngDiff_With_PrioAngValue(l_u16ang2,p_CombNum) ;
//	}

//	//进行排序
//	SortCombine(&g_sComb,g_sComb.m_u16size) ;

//	return 0 ;
//}

void Copy_Mark_To_NavMark_Buf(u8 p_CombNum,
                              STRUCT_FILTER_TARGET_LITE* p_Selt_Nav_Mark,
                              STRUCT_FILTER_TARGET_LITE* p_Nav_Mark)
{
    u8 l_u8i;
    u8 l_u8off = 0;
    STRUCT_MARK_INFO l_StructMarkInfo[5];
    MARK_XY l_sMark[5];  //这个疑似目标指向的路标的坐标,如果不是则是扫描到的新路标的在环境坐标的位置
                         //	XY_TO_RelCoor l_sXy2Robot[5] ;
                         //	memset((void *)p_Selt_Nav_Mark, 0 , sizeof(STRUCT_FILTER_TARGET_LITE) );
    for (l_u8i = 0; l_u8i < p_CombNum; l_u8i++)  //组合的顺序是从小到大,因此可直接后面覆盖前面.
    {
        l_u8off = g_sComb.m_u8buf[0][l_u8i];
        g_au8NavMarkBuf[l_u8i] = l_u8off;
        // memcpy(&p_Nav_Mark->m_sMark[l_u8i],&p_Nav_Mark->m_sMark[l_u8off],STRUCT_SIZE_MARK_XY);
        p_Selt_Nav_Mark->m_psSetMarkAddr[l_u8i] = p_Nav_Mark->m_psSetMarkAddr[l_u8off];
        p_Selt_Nav_Mark->m_StructMarkScanInfoAddr.m_sXy2Robot[l_u8i] =
            p_Nav_Mark->m_StructMarkScanInfoAddr.m_sXy2Robot[l_u8off];
        //		memcpy(&p_Selt_Nav_Mark->m_sXy2Robot[l_u8i],&p_Nav_Mark->m_sXy2Robot[l_u8off],STRUCT_SIZE_XY_TO_RelCoor);
        p_Selt_Nav_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i] =
            p_Nav_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8off];
        // memcpy(&p_Selt_Nav_Mark->m_StructMarkScanInfo.m_StructMarkInfo[l_u8i],&p_Nav_Mark->m_StructMarkScanInfo.m_StructMarkInfo[l_u8off],STRUCT_SIZE_MARK_INFO);
    }
    //	memset(p_Nav_Mark,0,sizeof(STRUCT_FILTER_TARGET_LITE));
    //	memcpy(&p_Nav_Mark->m_sMark[0],&l_sMark[0],STRUCT_SIZE_MARK_XY * p_CombNum);
    //	memcpy(&p_Nav_Mark->m_sXy2Robot[0],&l_sXy2Robot[0],STRUCT_SIZE_XY_TO_RelCoor * p_CombNum );
    //	memcpy(&p_Nav_Mark->m_StructMarkScanInfo.m_StructMarkInfo[0],
    //&l_StructMarkInfo[0],STRUCT_SIZE_MARK_INFO * p_CombNum );
    p_Nav_Mark->m_StructMarkScanInfoAddr.m_u8In = p_CombNum;
}
// int SelectMarkByAngC53(STRUCT_FILTER_TARGET_LITE *pDist, u8 size, STRUCT_FPGAPOS *FpgaPos)
//{
//
//    static u8 l_u8bufa[COM_BUF_MAX] = {0} ;
//    static u8 l_u8bufb[COM_BUF_MAX] = {0} ;
//	static u8 l_u8size ;
//	u8 l_u8i ;
//	u16 l_u16ang[3] ;
//	u16 l_u16ang2[3] ;
//	//u16 *l_pu16ang = NULL ;
//	u8 l_u8off ;
//	l_u8size = size ;
//
//	for(l_u8i = 0 ;l_u8i < size ;l_u8i++)
//	{
//		l_u8bufa[l_u8i] = l_u8i ;//i位写i
//	}
//	memset((void *)&g_sComb, 0 , STRUCT_SIZE_COMBINE );
//
//	g_sComb.m_u16size =  0 ;
//	combine(l_u8size,3,l_u8bufa,l_u8bufb,3) ;
//	//生成出来的排列组合表
//	for(l_u8i = 0 ;l_u8i < g_sComb.m_u16size ;l_u8i++)
//	{
//		l_u8off = g_sComb.m_u8buf[l_u8i][0] ;
//		l_u16ang[0] = pDist->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8off].m_u16Ang ;
//		l_u8off = g_sComb.m_u8buf[l_u8i][1] ;
//		l_u16ang[1] = pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u8off].m_u16Ang ;
//		l_u8off = g_sComb.m_u8buf[l_u8i][2] ;
//		l_u16ang[2] = pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u8off].m_u16Ang ;

//		//计算夹角的期望误差
//		l_u16ang2[0] = l_u16ang[1] - l_u16ang[0]  ;
//		l_u16ang2[1] = l_u16ang[2] - l_u16ang[1]  ;
//		l_u16ang2[2] = (DEG_PERSCAN + l_u16ang[0] - l_u16ang[2])%DEG_PERSCAN ;
//
//		g_sComb.m_u32val[l_u8i] = Cal_AngDiff_With_PrioAngValue(l_u16ang2,3) ;
//	}
//
//
//
//
//	return 0 ;
//}

//存储靶标到FRAM
u32 StoreMarkSets(MARK_SETS* marks, u16 addr)
{
    // sFRAM_WriteBuffer((void *)marks, addr, sizeof(MARK_SETS));
    return 0;
}

//读取靶标到RAM
u32 LoadMarkSets(MARK_SETS* marks, u16 addr)
{
    // sFRAM_ReadBuffer((void *)marks, addr, sizeof(MARK_SETS));
    if (marks->m_u8flag != 1)
    {
        memset((void*)marks, 0, STRUCT_SIZE_MARK_SETS);
    }
    return 0;
}

//存储靶标到FRAM
u32 StoreRobotSets(ROBOT_XY* robot, u16 addr)
{
    // sFRAM_WriteBuffer((u8 *)robot, addr, STRUCT_SIZE_LASERPOS);
    return 0;
}

//读取靶标到RAM
u32 LoadRobotSets(ROBOT_XY* robot, u16 addr)
{
    // sFRAM_ReadBuffer((u8 *)robot, addr, STRUCT_SIZE_LASERPOS);
    u16 l_u16layertmp = robot->m_u16CurrLayer;
    //加载之后要判断一下,最开始的时候肯定是乱的
    if ((robot->m_u16flag != ACTIVE) || (robot->m_u16flag != INITIAL)
        || (robot->m_Struct_LaserPos.m_u16ang >= DEG_PERSCAN))
    {
        //清空参数
        memset((void*)robot, 0, STRUCT_SIZE_LASERPOS);
        // robot->m_u16CurrLayer = l_u16layertmp;
    }
    return 0;
}

/*****************************************************************************
*************************    计算相关   **************************************
*****************************************************************************/
//角度分辨率0.05°
u32 CalSideLength(u32 a, u32 b, u16 ang)
{
    u16 l_u16ang;
    float l_f32Rad;
    float l_f32Tmp;
    double l_f64Tmp;
    long long l_u64Tmp1, l_u64Tmp2;
    u32 l_u32Ret;
    // 1. 将2个点的夹角转换为弧度
    l_u16ang = ang;
    // 2. 按照7200点,0.05°的分辨率来统一
    l_f32Rad = (3.1415926 * l_u16ang) / 3600.0;
    // 3. 计算cos(flaot x)
    l_f32Tmp = cos(l_f32Rad);
    // 4. 计算 2*a*b*cos(x)
    l_f64Tmp = 2 * a * b * l_f32Tmp;
    l_u64Tmp1 = (long long)l_f64Tmp;
    // 5. 计算 a*a+b*b-2*a*b*cos(x)
    l_u64Tmp2 = a * a + b * b - l_u64Tmp1;
    // 6. 开平方
    l_u32Ret = sqrt(l_u64Tmp2);
    return l_u32Ret;
}

u32 Cal_PerMark_AbsAng(MARK_XY* p_SetMark_Pos, XYANG2ROBOT* p_Laser_Pos)
{
    //根据当前的激光器位置(x,y),计算靶标xy1相对平移到rob的atan2的方位角
    //和激光器扫描到的当前ang进行比对
    //环境坐标是逆时针方向

    //必须搞清楚
    // atan2 计算的角度是x轴的夹角
    //相对坐标的夹角是z轴的夹角, z轴当成x轴或者y轴要搞清楚,因为涉及到坐标变换

    float l_f32rad;
    float l_f32_2PI = 2 * M_PI;
    float l_u16ang;
    l_f32rad = atan2((float)(p_SetMark_Pos->m_s32y - p_Laser_Pos->m_s32y),
                     (float)(p_SetMark_Pos->m_s32x - p_Laser_Pos->m_s32x));
    l_f32rad = fmodf(l_f32rad + l_f32_2PI, l_f32_2PI);
    l_u16ang = l_f32rad * TenmDeg_Per_Rad;  //得到该靶标在世界坐标轴平移后的与x方向的夹角
    // z轴为y轴,激光器顺时针方向
    //激光器的夹角ang是相对y轴的,  环境坐标是相对x轴的, 这就有个90度的固定差
    //方向相反, 顺时针,逆时针

    return (u32)round(l_u16ang);
}

//路标的坐标转换为相对机器人的位置坐标
// x0= (x - rx0)*cos(a) - (y - ry0)*sin(a) + rx0 ;
// y0= (x - rx0)*sin(a) + (y - ry0)*cos(a) + ry0 ;
u32 Xy2Robot(MARK_XY* mark, ROBOT_XY robot, MARK_XY* xy2robot)
{
    // XY2ROBOT_POS *l_psPos = xy2robot ;
    float l_f32rad;
    l_f32rad = M_PI * (float)robot.m_Struct_LaserPos.m_u16ang / 3600.0;
    xy2robot->m_s32x = (mark->m_s32x - robot.m_Struct_LaserPos.m_s32x) * cosf(l_f32rad)
                       - (mark->m_s32y - robot.m_Struct_LaserPos.m_s32y) * sinf(l_f32rad)
                       + robot.m_Struct_LaserPos.m_s32x;
    xy2robot->m_s32y = (mark->m_s32x - robot.m_Struct_LaserPos.m_s32x) * sinf(l_f32rad)
                       + (mark->m_s32y - robot.m_Struct_LaserPos.m_s32y) * cosf(l_f32rad)
                       + robot.m_Struct_LaserPos.m_s32y;
    return 0;
}

//激光器的相对坐标转换为环境坐标
//需要将之前的环境坐标0点先映射到激光器的相对坐标
u32 Robot2XyOri(XY_TO_RelCoor* mark, ROBOT_XY robot, MARK_XY* xy2robot)
{
    // XY2ROBOT_POS *l_psPos = xy2robot ;
    float l_f32rad;
    l_f32rad = M_PI * (float)robot.m_Struct_LaserPos.m_u16ang / 3600.0;
    xy2robot->m_s32x = (mark->m_s32y - robot.m_Struct_LaserPos.m_s32y) * sinf(l_f32rad)
                       + (mark->m_s32x - robot.m_Struct_LaserPos.m_s32x) * cosf(l_f32rad);
    xy2robot->m_s32y = (mark->m_s32y - robot.m_Struct_LaserPos.m_s32y) * cosf(l_f32rad)
                       - (mark->m_s32x - robot.m_Struct_LaserPos.m_s32x) * sinf(l_f32rad);
    return 0;
}

// rob 是指相对激光器的坐标,激光器看到的坐标
// robot 激光器的坐标(参考环境坐标)
// xy 环境坐标
//将环境坐标的原点(0,0)映射到激光器的相对坐标中mark0,避免多次计算
u32 Robot2Xy(XY_TO_RelCoor* rob, ROBOT_XY robot, MARK_XY* xy, MARK_XY mark0)
{
    //	MARK_XY l_sMark0 ,l_sMark;
    //	l_sMark.m_s32x = 0 ;
    //	l_sMark.m_s32y = 0 ;

    //先要知道激光器当前的 位置和方位
    //将环境坐标的原点(0,0)映射到激光器的相对坐标中
    // Xy2Robot(&l_sMark , robot, &l_sMark0); //原坐标点

    //根据激光器相对坐标的rob来反求环境坐标xy
    robot.m_Struct_LaserPos.m_s32x = mark0.m_s32x;
    robot.m_Struct_LaserPos.m_s32y = mark0.m_s32y;
    Robot2XyOri(rob, robot, xy);
    return 0;
}

//特别注意:
//统一定义, 靶标平移到激光器的位置, 计算靶标相对激光器的方位角ang_mark
//激光器z轴探测到的方位角ang_raw
//定义2者的夹角: 初始方位角为激光器探测方位角 ang_raw - ang_mark
u32 Robot2Xy2(u32 dist, u16 ang, ROBOT_XY robot, MARK_XY* xy)
{
    float l_f32rad;
    // l_f32rad = (ang - robot.m_u16ang)*M_PI/3600.0 ;
    l_f32rad = ((ang + robot.m_Struct_LaserPos.m_u16ang + 7200) % 7200) * M_PI / 3600.0;
    xy->m_s32x = robot.m_Struct_LaserPos.m_s32x + dist * cos(l_f32rad);
    xy->m_s32y = robot.m_Struct_LaserPos.m_s32y + dist * sin(l_f32rad);
    return 0;
}

//根据激光器扫描的方位角和距离,来计算相对自己的坐标(z轴为y)
//注意：z轴为x或者y轴, 根据宏定义
// fpga算法要求从x轴顺时针开始给坐标,所以定义z轴为x轴
u32 CalRobotXy(u32 dist, u16 ang, MARK_XY* xy)
{
    float l_f32rad;
    float l_u16ang = (float)ang;
    float l_u32dist = (float)dist;
    MARK_XY* l_psXy = xy;

    // l_f32rad = 2*M_PI - (M_PI*l_u16ang)/3600.0 ;
    l_f32rad = M_PI * l_u16ang / 3600.0;
//这里暂时没有加上靶标的size大小
#ifdef Z_X_DIR
    l_psXy->m_s32x = (int)l_u32dist * cos(l_f32rad);
    l_psXy->m_s32y = (int)l_u32dist * sin(l_f32rad);
#else
    l_psXy->m_s32x = round(l_u32dist * sin(l_f32rad));
    l_psXy->m_s32y = round(l_u32dist * cos(l_f32rad));
#endif
    return 0;
}

//将激光器扫描到到方位和距离转换为相对坐标
int CalRobotXy_Sets(STRUCT_FILTER_TARGET* g_sFilterDist, MARK_SETS* g_sMarkSets)
{
    u8 l_u8size, l_u8i;
    MARK_XY l_sXy;
    u16* l_p32dist = NULL;
    u16* l_p16ang = NULL;
    l_u8size = g_sFilterDist->m_u8In;

    if (l_u8size > TARGET_MAX)
        return -1;

    for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
    {
        *l_p32dist = g_sFilterDist->m_StructMarkInfo[l_u8i].m_u16Dist;
        *l_p16ang = g_sFilterDist->m_StructMarkInfo[l_u8i].m_u16Ang;
        CalRobotXy(*(l_p32dist), *(l_p16ang), &l_sXy);
        g_sMarkSets->m_sMarkSets[l_u8i].m_s32x = l_sXy.m_s32x;
        g_sMarkSets->m_sMarkSets[l_u8i].m_s32y = l_sXy.m_s32y;
        g_sMarkSets->m_sMarkSets[l_u8i].m_u32no = l_u8i;
    }
    g_sMarkSets->m_u16size = l_u8size;
    return 0;
}

/*************************************************
Function		:	TransMark_Polar_to_RelDecare
Description		:	将靶标的扫描数据(极坐标)转换成以激光器坐标系的笛卡尔坐标
Calls			: 	无
Called By		:	TaskTargetApp
Table Accessed	:	无
Table Updated	: 	无
Input			:	p_Nav_Mark:存储用于定位的靶标数据
                    size:靶标的个数
Output			:	p_Nav_Mark:将扫描的极坐标转换成相对笛卡尔坐标系下坐标
Return			:	无
Others			:	无
*************************************************/
void TransMark_Polar_to_RelDecare(STRUCT_FILTER_TARGET_LITE* p_Nav_Mark, u8 size)
{
    u8 l_u8i;
    float l_u16dist = 0;  // pDist->m_u32Dist;
    float l_u16ang = 0;   // pDist->m_u16Ang ;
    XY_TO_RelCoor** p_sMarkXY_to_Rob =
        &p_Nav_Mark->m_StructMarkScanInfoAddr.m_sXy2Robot[0];  //实时扫描靶标的相对坐标
    STRUCT_MARK_INFO** l_psMarkInfoAddr = &p_Nav_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[0];
    float l_f32rad = 0;

    //按照逆时针方向, 识别到靶标的相对坐标值
    for (l_u8i = 0; l_u8i < size; l_u8i++)
    {
        l_u16dist = l_psMarkInfoAddr[l_u8i]->m_u16Dist;
        l_u16ang = l_psMarkInfoAddr[l_u8i]->m_u16Ang;  // 10mdeg为单位
        l_f32rad = Rad_Per_10mDeg * l_u16ang;
        p_sMarkXY_to_Rob[l_u8i]->m_s32x = round(l_u16dist * cos(l_f32rad));
        p_sMarkXY_to_Rob[l_u8i]->m_s32y = round(l_u16dist * sin(l_f32rad));
        //		p_sMarkXY_to_Rob[l_u8i]->m_s32x = l_u16dist * cos(l_f32rad) ;
        //		p_sMarkXY_to_Rob[l_u8i]->m_s32y = l_u16dist * sin(l_f32rad) ;
    }
}

//处理激光器方位角的平均值
u32 CalAvgAng(u16* buf, u8 size)
{
    //主要是处理 7195, 7, 等临界情况
    u8 l_u8i, l_u8size;
    u16* l_pu16 = buf;
    u32 l_u32sum = 0;
    u32 l_u32ret;
    u8 l_u8cnt0 = 0;
    u8 l_u8cnt1 = 0;
    l_u8size = size;

    for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
    {
        if (*(l_pu16 + l_u8i) > 7100)
        {
            l_u8cnt0++;
        }
        else if (*(l_pu16 + l_u8i) < 100)
        {
            l_u8cnt1++;
        }
    }

    //存在临界点
    if ((l_u8cnt0 > 0) && (l_u8cnt1 > 0))
    {
        for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
        {
            if (*(l_pu16 + l_u8i) < 100)
            {
                *(l_pu16 + l_u8i) += 7200;
            }
        }
    }

    //开始计算平均方位角
    for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
    {
        l_u32sum += *(l_pu16 + l_u8i);
    }

    l_u32ret = l_u32sum / l_u8size;
    l_u32ret %= 7200;
    return l_u32ret;
}

// int SeletFirstMark(STRUCT_EqlTri *Eqltri , STRUCT_FILTER_TARGET_LITE
// *p_Scan_Mark,STRUCT_FILTER_TARGET_LITE *p_NavMark, u8 *offset)
//{
//	u8 l_u8i,l_u8j;
//	u8 l_u8size = p_Scan_Mark->m_StructMarkScanInfo.m_u8In;
//	//u8 l_u8tmp = *offset;
//
//	for (l_u8i = *offset;l_u8i< l_u8size;l_u8i++)
//	{
//		if(p_Scan_Mark->m_StructMarkScanInfo.m_StructMarkInfo[l_u8i].m_u8IsMark == NOTMARK)
//		{
//			memcpy((void *)&p_NavMark->m_StructMarkScanInfo.m_StructMarkInfo[3],(void
//*)&p_Scan_Mark->m_StructMarkScanInfo.m_StructMarkInfo[l_u8i],STRUCT_SIZE_MARK_INFO);
// memcpy((void
//*)&p_NavMark->m_sXy2Robot[3],(void *)&p_Scan_Mark->m_sXy2Robot[l_u8i],STRUCT_SIZE_XY_TO_RelCoor);
//			*offset = l_u8i+1;
//			return 0;
//		}
//	}
//	*offset = l_u8size;
//	return -1;//没找到第四个靶
//}

u8 Find_Closest_Pos_to_SetPos(ROBOT_XY* p_SetPos,
                              STRUCT_EqlTri* p_EqlTri,
                              ROBOT_XY* p_Pos_Out,
                              XYANG2ROBOT* p_EqlTri_Pos)
{
    u16 l_u16i = 0;
    u32 l_u32MinDist = 0xFFFFFFFF;
    u32 l_u32TemDist = 0;
    u32 l_u32Diff_x = 0, l_u32Diff_y = 0;
    u8 l_u8FindFlag = 0;
    u8 l_u8iioffset = 0;
    for (l_u16i = 0; l_u16i < p_EqlTri->m_u8In; l_u16i++)
    {
        l_u32Diff_x = abs(p_EqlTri_Pos[l_u16i].m_s32x - p_SetPos->m_Struct_LaserPos.m_s32x);
        l_u32Diff_y = abs(p_EqlTri_Pos[l_u16i].m_s32y - p_SetPos->m_Struct_LaserPos.m_s32y);
        if ((l_u32Diff_x > 3000) || (l_u32Diff_y > 3000))
        {
            continue;
        }
        l_u32TemDist = l_u32Diff_x * l_u32Diff_x + l_u32Diff_y * l_u32Diff_y;
        if (l_u32TemDist < l_u32MinDist)
        {
            l_u8FindFlag = 1;
            l_u8iioffset = l_u16i;
            l_u32MinDist = l_u32TemDist;
        }
    }
    if (l_u8FindFlag)
    {
        memcpy((void*)&p_Pos_Out->m_Struct_LaserPos,
               (void*)(p_EqlTri_Pos + l_u8iioffset),
               STRUCT_SIZE_XYANG2ROBOT);
        return 0;
    }
    else
        return 1;
}

s8 Find_MatchMost_Pos(STRUCT_FILTER_TARGET_LITE* p_Nav_Mark,
                      XY2ROBOT_CNT* p_MappingXY_old,
                      MARK_SETS* p_SETMARK,
                      XYANG2ROBOT* p_PosOut,
                      XYANG2ROBOT* p_PosOld,
                      STRUCT_MarkMatch* p_IdenWindow)
{
    XYANG2ROBOT* p_TmpPos = NULL;
    u8 l_u8MarkNum = p_Nav_Mark->m_StructMarkScanInfoAddr.m_u8In;
    u8 l_u8MarkMatchCnt = 0;
    u8 l_u8MarkMatchMost = 0;
    u8 l_u8MatchLeast = Get_Match_Least_Mark_Num(l_u8MarkNum, 3);
    int64_t l_s64Diff_x = (g_auXY2EqlTri[0].m_s32x - p_PosOld->m_s32x);
    int64_t l_s64Diff_y = (g_auXY2EqlTri[0].m_s32y - p_PosOld->m_s32y);
    u64 l_u64TemDist_Old = l_s64Diff_x * l_s64Diff_x + l_s64Diff_y * l_s64Diff_y;
    u64 l_u64TemDist_New;
    s8 l_s8MatchOkFlag = -1;
    static u32 l_u32mindis[2];
    MARK_XY* l_psMinDistPoint = NULL;
    // u8 l_u8size = p_Nav_Mark->m_StructMarkScanInfoAddr.m_u8In;
    u8 KNN_flag = 0;
    u16 l_u16IdentWindow;
    for (u16 l_u16i = 0; l_u16i < g_sEqlTri.m_u8In; l_u16i++)
    {
        l_u8MarkMatchCnt = 0;
        p_TmpPos = &g_auXY2EqlTri[l_u16i];
        memset((void*)&g_sMarkSets.m_u8hasmatchflag, 0, MAX_SIZE);
        Renew_AbsCoor_XY1(&p_Nav_Mark->m_StructMarkScanInfoAddr.m_sXy2Robot[0],
                          p_TmpPos,
                          p_MappingXY_old,
                          l_u8MarkNum);
        memset(&g_sLocalMap_Info, 0, STRUCT_SIZE_LOCAL_MAP_INFO);

        l_u8MarkMatchCnt = Return_Match_SetMarkNum(p_Nav_Mark, p_MappingXY_old, p_IdenWindow);
        // g_sLocalMap_Info.m_u32MarkNum = l_u8MarkMatchCnt;
        if (l_u8MarkMatchCnt >= l_u8MatchLeast)
        {
            l_s8MatchOkFlag = 0;
            if (l_u8MarkMatchCnt > l_u8MarkMatchMost)
            {
                l_s64Diff_x = (g_auXY2EqlTri[l_u16i].m_s32x - p_PosOld->m_s32x);
                l_s64Diff_y = (g_auXY2EqlTri[l_u16i].m_s32y - p_PosOld->m_s32y);
                l_u64TemDist_Old = l_s64Diff_x * l_s64Diff_x + l_s64Diff_y * l_s64Diff_y;
                l_u8MarkMatchMost = l_u8MarkMatchCnt;
                memcpy(p_PosOut, p_TmpPos, STRUCT_SIZE_XYANG2ROBOT);
            }
            else if (l_u8MarkMatchCnt == l_u8MarkMatchMost)
            {
                l_s64Diff_x = (g_auXY2EqlTri[l_u16i].m_s32x - p_PosOld->m_s32x);
                l_s64Diff_y = (g_auXY2EqlTri[l_u16i].m_s32y - p_PosOld->m_s32y);
                l_u64TemDist_New = l_s64Diff_x * l_s64Diff_x + l_s64Diff_y * l_s64Diff_y;
                if (l_u64TemDist_New < l_u64TemDist_Old)
                {
                    l_u64TemDist_Old = l_u64TemDist_New;
                    // l_u8MarkMatchMost = l_u8MarkMatchCnt;
                    memcpy(p_PosOut, p_TmpPos, STRUCT_SIZE_XYANG2ROBOT);
                }
            }
        }
    }
    return l_s8MatchOkFlag;
}

// int CalAbsCoordinatesofDec(STRUCT_FILTER_TARGET_LITE *pDist , MARK_SETS *pMark , ROBOT_XY *robot)
//{
//	u16 l_u16i,l_u16j;
//	u8 l_u8cnt =0;
//	u16 l_u16maxdiff = 500;//偏差为20cm
//	u16 l_u16SetMarkSize = pMark->m_u16size;//设置的靶标个数
//	u8  l_u8DecMarkSize  = pDist->m_StructMarkScanInfo.m_u8In ;
//	u16 l_u16RobAng = robot->m_Struct_LaserPos.m_u16ang;//激光器的方位角
//	u16 l_u16DecMarkAng;//激光器扫描到靶标的角度
//	u16 l_u16WorldAng;
//	for(l_u16j =0;l_u16j<l_u8DecMarkSize ;l_u16j++)
//	{
//		l_u16DecMarkAng = pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j].m_u16Ang;
//		l_u16WorldAng = l_u16DecMarkAng +
// l_u16RobAng;//得到将绝对坐标系平移到激光器当前位置后，靶标与绝对坐标系的夹角
// if(l_u16WorldAng >7200)
//		{
//			l_u16WorldAng = l_u16WorldAng - 7200;
//		}
//		float l_f16WorldAng = ((float)(l_u16WorldAng /3600.00) *M_PI);//转成弧度值
//		int l_s32x =
// cos(l_f16WorldAng)*pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j].m_u16Dist; 		int
// l_s32y = sin(l_f16WorldAng)*pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j].m_u16Dist;
//		//加上平移坐标值得到扫描靶的绝对坐标 ，平移坐标值 即为激光器的定位坐标
//		l_s32x = l_s32x + robot->m_Struct_LaserPos.m_s32x;
//		l_s32y = l_s32y + robot->m_Struct_LaserPos.m_s32y;
//
//		for(l_u16i=0;l_u16i<l_u16SetMarkSize;l_u16i++)
//		{
//			u16 l_u16tmp = abs(pMark->m_sMarkSets[l_u16i].m_s32x - l_s32x);
//			if(l_u16tmp < l_u16maxdiff)
//			{
//				l_u16tmp = abs(pMark->m_sMarkSets[l_u16i].m_s32y - l_s32y);
//				if(l_u16tmp < l_u16maxdiff)
//				{
//					pDist->m_sMark[l_u16j]->m_s32x = pMark->m_sMarkSets[l_u16i].m_s32x;
//					pDist->m_sMark[l_u16j]->m_s32y = pMark->m_sMarkSets[l_u16i].m_s32y;
//					pDist->m_sMark[l_u16j]->m_u32no = pMark->m_sMarkSets[l_u16i].m_u32no;
//					pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j].m_u8IsMark =1;
//					l_u8cnt ++;
//					break;
//				}
//			}
//		}
//	}
//	if(l_u8cnt >=3)
//		return 0;
//	else
//		return -1;

//
//}
int CalAbsCoordinatesofDec1(STRUCT_MARK_INFO* p_ScanMarkInfo,
                            ROBOT_XY* robot,
                            MARK_XY* p_MatchMark,
                            u8 offset,
                            XY2ROBOT_CNT* absXY)  //将扫描数据转换成绝对坐标系下的坐标值
{
    XY_TO_RelCoor l_xyrob_old, l_xyrob_new;
    u8 l_u8cnt = 0;
    u32 l_u32RobAng = robot->m_Struct_LaserPos.m_u16ang;  //激光器的方位角
    u32 l_u32DecMarkAng;                                  //激光器扫描到靶标的角度
    u32 l_u32WorldAng;
    u16 l_u16MarkDidst = 0;
    u16 l_u16AverCnt = 0;
    l_u32DecMarkAng = p_ScanMarkInfo->m_u16Ang;
    l_u16MarkDidst = p_ScanMarkInfo->m_u16Dist;
    l_u32WorldAng = (l_u32DecMarkAng + l_u32RobAng)
                    % DEG_PERSCAN;  //得到将绝对坐标系平移到激光器当前位置后，靶标与绝对坐标系的夹角

    float l_f16WorldAng = ((float)l_u32WorldAng / 18000.0) * M_PI;  //转成弧度值
    l_xyrob_old.m_s32x = absXY->m_StructXY[offset].m_s32x;
    l_xyrob_old.m_s32y = absXY->m_StructXY[offset].m_s32y;
    int l_s32x = round(cos(l_f16WorldAng) * (float)l_u16MarkDidst);
    int l_s32y = round(sin(l_f16WorldAng) * (float)l_u16MarkDidst);
    absXY->m_StructXY[offset].m_s32x = l_s32x + robot->m_Struct_LaserPos.m_s32x;
    absXY->m_StructXY[offset].m_s32y = l_s32y + robot->m_Struct_LaserPos.m_s32y;
    l_u16AverCnt = g_auMappingList.m_XY2Rob.m_u16cnt[offset];
    // g_u16Mapping_Aver_Done_Cnt=200;
    if ((g_sSysPib.m_u16WorkMode_NavOrMappingOrMark == SYSMODE_NavOrMappingOrMark_MAPPING_P)
        || (g_sSysPib.m_u16WorkMode_NavOrMappingOrMark == SYSMODE_NavOrMappingOrMark_MAPPING_N))
    {
        absXY->m_s32SumX[offset] += absXY->m_StructXY[offset].m_s32x;
        absXY->m_s32SumY[offset] += absXY->m_StructXY[offset].m_s32y;
        //		absXY->m_StructXY[offset].m_s32x = ((long)(l_xyrob_old.m_s32x * (l_u16AverCnt - 1))
        //+ absXY->m_StructXY[offset].m_s32x) / l_u16AverCnt; absXY->m_StructXY[offset].m_s32y =
        //((long)(l_xyrob_old.m_s32y * (l_u16AverCnt - 1)) + absXY->m_StructXY[offset].m_s32y) /
        // l_u16AverCnt;
    }
    else if (g_sSysPib.m_u16WorkMode_NavOrMappingOrMark == SYSMODE_NavOrMappingOrMark_MARK)
    {
        p_MatchMark->m_s32x = absXY->m_StructXY[offset].m_s32x;
        p_MatchMark->m_s32y = absXY->m_StructXY[offset].m_s32y;
        p_MatchMark->m_u8shape = g_sSysPib.m_u16MarkType & 0xff;
        p_MatchMark->m_u8size = g_sSysPib.m_u16MarkRadio & 0xff;
    }
    return 0;
}
u32 FindMax(u32 p_IN1, u32 p_IN2)
{
    return (p_IN1 > p_IN2) ? p_IN1 : p_IN2;
}

u16 Cal_MeanDev(XY2ROBOT_CNT* p_AbsCoor)
{
    u8 l_u8tmp = 0, l_u8i = 0, l_u8j = 0;
    u32 l_u32tmp = 0;
    u8 l_u8Num = g_sFilterDistShortNewLast.m_StructMarkScanInfoAddr.m_u8In;
    u8 l_u8offset = 0;
    u32 l_u32MeanDev = 0;
    XY_TO_RelCoor** l_psRelCoorAddr =
        g_sFilterDistShortNewLast.m_StructMarkScanInfoAddr.m_sXy2Robot;
    MARK_XY** l_psSetMarkAddr = g_sFilterMark_SpeedCorr.m_psSetMarkAddr;
    XY_TO_RelCoor** l_psRelCoorFirstAddr =
        g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_sXy2Robot;
    XY_TO_RelCoor* l_psRelCoor = p_AbsCoor->m_StructXY;

    if (l_u8Num == 0)
        return 0xffff;
    for (l_u8i = 0; l_u8i < l_u8Num; l_u8i++)
    {
        for (l_u8j = l_u8offset; l_u8j < g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In;
             l_u8j++)
        {
            if (l_psRelCoorAddr[l_u8i] == l_psRelCoorFirstAddr[l_u8j])
            {
                l_u8offset = l_u8j;
                break;
            }
        }

        l_u32tmp +=
            sqrt(pow(l_psSetMarkAddr[l_u8offset]->m_s32x - l_psRelCoor[l_u8offset].m_s32x, 2)
                 + pow(l_psSetMarkAddr[l_u8offset]->m_s32y - l_psRelCoor[l_u8offset].m_s32y, 2));
    }
    l_u32MeanDev = l_u32tmp / l_u8Num;
    //靶标数少
    if (l_u8Num < 3)
        l_u8Num *= 2;
    if (l_u32MeanDev > 0xffff)
        l_u32MeanDev = 0xffff;
    return l_u32MeanDev;
}
void Renew_AbsCoor_XY(STRUCT_FILTER_TARGET* p_ScanMark,
                      ROBOT_XY* robot,
                      XY2ROBOT_CNT* absXY,
                      u8 p_MarkSize)  //将扫描数据转换成绝对坐标系下的坐标值
{
    u8 l_u8MarkSize = p_MarkSize;
    u8 l_u8i = 0;
    u32 l_u32RobAng = robot->m_Struct_LaserPos.m_u16ang;  //激光器的方位角
    u32 l_u32ScanMarkAng = 0;                             //激光器扫描到靶标的角度
    u32 l_u32WorldAng = 0;
    float l_f16WorldRad = 0;
    int l_s32x = 0;
    int l_s32y = 0;
    u16 l_u16Dist = 0;

    for (l_u8i = 0; l_u8i < l_u8MarkSize; l_u8i++)
    {
        l_u32ScanMarkAng = p_ScanMark->m_StructMarkInfo[l_u8i].m_u16Ang;
        l_u16Dist = p_ScanMark->m_StructMarkInfo[l_u8i].m_u16Dist;

        l_u32WorldAng =
            (l_u32ScanMarkAng + l_u32RobAng)
            % DEG_PERSCAN;  //得到将绝对坐标系平移到激光器当前位置后，靶标与绝对坐标系的夹角
        l_f16WorldRad = (float)l_u32WorldAng / 18000.0 * M_PI;  //转成弧度值
        l_s32x = round(cos(l_f16WorldRad) * (float)l_u16Dist);
        l_s32y = round(sin(l_f16WorldRad) * (float)l_u16Dist);
        absXY->m_StructXY[l_u8i].m_s32x = l_s32x + robot->m_Struct_LaserPos.m_s32x;
        absXY->m_StructXY[l_u8i].m_s32y = l_s32y + robot->m_Struct_LaserPos.m_s32y;
    }
}

//将扫描数据转换成绝对坐标系下的坐标值
void Renew_AbsCoor_XY1(XY_TO_RelCoor** p_ScanMark,
                       XYANG2ROBOT* robot,
                       XY2ROBOT_CNT* absXY,
                       u8 p_MarkSize)
{
    float l_s32Trans_X = 0;
    float l_s32Trans_Y = 0;
    XY_TO_RelCoor* l_sMarkXY = NULL;
    XY_TO_RelCoor* l_psMarkAbsCoor = &absXY->m_StructXY[0];         //输出：靶标全局位姿
    float l_fRobRad = (float)robot->m_u16ang * g_f32Per10mDeg_Rad;  //单位10mdeg

    float l_fCos = cos(l_fRobRad);
    float l_fSin = sin(l_fRobRad);

    for (u8 l_u8i = 0; l_u8i < p_MarkSize; l_u8i++)
    {
        l_sMarkXY = p_ScanMark[l_u8i];
        l_s32Trans_X = l_fCos * (float)l_sMarkXY->m_s32x - l_fSin * (float)l_sMarkXY->m_s32y;
        l_s32Trans_Y = l_fCos * (float)l_sMarkXY->m_s32y + l_fSin * (float)l_sMarkXY->m_s32x;

        // 屏蔽转换
        // l_psMarkAbsCoor[l_u8i].m_s32x = round(l_s32Trans_X + (float)robot->m_s32x) ;
        // l_psMarkAbsCoor[l_u8i].m_s32y = round(l_s32Trans_Y + (float)robot->m_s32y) ;

        l_psMarkAbsCoor[l_u8i].m_s32x = round(l_s32Trans_X);
        l_psMarkAbsCoor[l_u8i].m_s32y = round(l_s32Trans_Y);
    }
}

int CalAbsCoordinatesofDec_N(STRUCT_FILTER_TARGET_LITE* pDist,
                             ROBOT_XY* robot,
                             u8 offset,
                             XY2ROBOT_CNT* absXY,
                             u8 offset_N)  //将扫描数据转换成绝对坐标系下的坐标值
{
    XY_TO_RelCoor l_xyrob_old, l_xyrob_new;
    u8 l_u8cnt = 0;
    u16 l_u16RobAng = robot->m_Struct_LaserPos.m_u16ang;  //激光器的方位角
    u16 l_u16DecMarkAng;                                  //激光器扫描到靶标的角度
    u32 l_u32WorldAng;
    s16 l_s16RoundUp = 0;
    l_u16DecMarkAng = pDist->m_StructMarkScanInfoAddr.m_StructMarkInfo[offset]->m_u16Ang;
    l_u32WorldAng = l_u16DecMarkAng
                    + l_u16RobAng;  //得到将绝对坐标系平移到激光器当前位置后，靶标与绝对坐标系的夹角
    if (l_u32WorldAng >= DEG_PERSCAN)
    {
        l_u32WorldAng = l_u32WorldAng - DEG_PERSCAN;
    }
    float l_f16WorldAng = ((float)l_u32WorldAng / 18000.0) * M_PI;  //转成弧度值
    l_xyrob_old.m_s32x = absXY->m_StructXY[offset_N].m_s32x;
    l_xyrob_old.m_s32y = absXY->m_StructXY[offset_N].m_s32y;
    int l_s32x =
        round(cos(l_f16WorldAng)
              * (float)pDist->m_StructMarkScanInfoAddr.m_StructMarkInfo[offset]->m_u16Dist);
    int l_s32y =
        round(sin(l_f16WorldAng)
              * (float)pDist->m_StructMarkScanInfoAddr.m_StructMarkInfo[offset]->m_u16Dist);
    absXY->m_s32SumX[offset_N] += (l_s32x + robot->m_Struct_LaserPos.m_s32x);
    // if(absXY->m_s32SumX[offset_N] < 0 )
    // 	l_s16RoundUp = 0 - g_auMappingList.m_XY2Rob.m_u16cnt[offset_N]/2;
    // else
    // 	l_s16RoundUp = g_auMappingList.m_XY2Rob.m_u16cnt[offset_N]/2;
    // absXY->m_StructXY[offset_N].m_s32x = (absXY->m_s32SumX[offset_N] +
    // l_s16RoundUp)/g_auMappingList.m_XY2Rob.m_u16cnt[offset_N];
    absXY->m_StructXY[offset_N].m_s32x = round(
        (float)absXY->m_s32SumX[offset_N] / (float)g_auMappingList.m_XY2Rob.m_u16cnt[offset_N]);
    absXY->m_s32SumY[offset_N] += (l_s32y + robot->m_Struct_LaserPos.m_s32y);
    // if(absXY->m_s32SumY[offset_N] < 0 )
    // 	l_s16RoundUp = 0 - g_auMappingList.m_XY2Rob.m_u16cnt[offset_N]/2;
    // else
    // 	l_s16RoundUp = g_auMappingList.m_XY2Rob.m_u16cnt[offset_N]/2;
    // absXY->m_StructXY[offset_N].m_s32y = (absXY->m_s32SumY[offset_N] +
    // l_s16RoundUp)/g_auMappingList.m_XY2Rob.m_u16cnt[offset_N];
    absXY->m_StructXY[offset_N].m_s32y = round(
        (float)absXY->m_s32SumY[offset_N] / (float)g_auMappingList.m_XY2Rob.m_u16cnt[offset_N]);
    //	absXY->m_StructXY[offset_N].m_s32x =
    //((long)(l_xyrob_old.m_s32x*(g_auMappingList.m_XY2Rob.m_u16cnt[offset_N]-1))+
    // absXY->m_StructXY[offset_N].m_s32x)/g_auMappingList.m_XY2Rob.m_u16cnt[offset_N];
    //	absXY->m_StructXY[offset_N].m_s32y =
    //((long)(l_xyrob_old.m_s32y*(g_auMappingList.m_XY2Rob.m_u16cnt[offset_N]-1))+
    // absXY->m_StructXY[offset_N].m_s32y)/g_auMappingList.m_XY2Rob.m_u16cnt[offset_N];

    return 0;
}

u16 IdentWindow_Formula(u16 p_MarkDist, STRUCT_MarkMatch* p_IdenWindow)
{
    if (p_IdenWindow->m_u16IdentWindow_R_Min == p_IdenWindow->m_u16IdentWindow_R_Max)
        return p_IdenWindow->m_u16IdentWindow_R_Min + (g_sSysPib.m_u16MarkRadio >> 1);

    //根据距离按照比例来计算
    if (p_MarkDist <= 500)
        return XY_ERROR;
    if (p_MarkDist > 500 && p_MarkDist <= 5000)
        return XY_ERROR + p_MarkDist * 0.2 - 10;  // XY_ERROR+ 10*((l_u16dist/500)-1)
    if (p_MarkDist > 5000 && p_MarkDist <= 10000)
        return (XY_ERROR >> 1) + p_MarkDist * 0.2 - 110;  //(XY_ERROR>>1)+ 10*(l_u16dist/500-11)
    if (p_MarkDist > 65000)
        return 2000;
    return ((p_MarkDist * 0.03));  /// 10000*300;
}

u16 Get_IdentWindow(u16 p_MarkDist, STRUCT_MarkMatch* p_IdenWindow)
{
    if (g_sSysPib.m_u16WorkMode_NavOrMappingOrMark == SYSMODE_NavOrMappingOrMark_NAV)
    {
        if (g_sSavePosCur.m_u16flag == NOACTIVE)
        {
            return (XY_ERROR << 1) + (g_sSysPib.m_u16MarkRadio >> 1);
        }
        // std::cout<<"r_max: "<<p_IdenWindow->m_u16IdentWindow_R_Max <<std::endl;
        // std::cout<<"r_min: "<<p_IdenWindow->m_u16IdentWindow_R_Min <<std::endl;
        // std::cout<<"dist_min: "<<p_IdenWindow->m_u32IdentWindow_Dist_Min <<std::endl;
        // std::cout<<"dist_max: "<<p_IdenWindow->m_u32IdentWindow_Dist_Max <<std::endl;

        return IdentWindow_Formula(p_MarkDist, p_IdenWindow);
    }
    if (g_sSysPib.m_u16WorkMode_NavOrMappingOrMark == SYSMODE_NavOrMappingOrMark_MAPPING_N
        || g_sSysPib.m_u16WorkMode_NavOrMappingOrMark == SYSMODE_NavOrMappingOrMark_MAPPING_P)
    {
        return XY_ERROR + (g_sSysPib.m_u16MarkRadio >> 1);
    }

    return IdentWindow_Formula(p_MarkDist, p_IdenWindow);
}

// void MarkCorr_MixLayout(STRUCT_FILTER_TARGET_LITE *pDist, MARK_SETS *pMark , u16 l_u16i, u8
// offset )
//{
//	if(pMark->m_sMarkSets[l_u16i].m_u8shape != g_sSysPib.m_u16MarkType)
//	{
//		if(g_sSysPib.m_u16MarkType == MARK_TYPE_CYC)//说明匹配上的靶是平面靶
//		{
//			pDist->m_StructMarkScanInfo.m_StructMarkInfo[offset].m_u16Dist -=
//(g_sSysPib.m_u16MarkRadio>>1); 			pDist->m_sMark[offset]->m_s32x =
// pMark->m_sMarkSets[l_u16i].m_s32x*(1 -
//(g_sSysPib.m_u16MarkRadio>>1)*(1/sqrt(pMark->m_sMarkSets[l_u16i].m_s32x *
// pMark->m_sMarkSets[l_u16i].m_s32x + pMark->m_sMarkSets[l_u16i].m_s32y *
// pMark->m_sMarkSets[l_u16i].m_s32y))); 			pDist->m_sMark[offset]->m_s32y =
// pMark->m_sMarkSets[l_u16i].m_s32y*(1 -
//(g_sSysPib.m_u16MarkRadio>>1)*(1/sqrt(pMark->m_sMarkSets[l_u16i].m_s32x *
// pMark->m_sMarkSets[l_u16i].m_s32x + pMark->m_sMarkSets[l_u16i].m_s32y *
// pMark->m_sMarkSets[l_u16i].m_s32y)));
//		}
//		else//说明匹配上的靶是圆柱
//		{
//			pDist->m_StructMarkScanInfo.m_StructMarkInfo[offset].m_u16Dist +=
//(g_sSysPib.m_u16MarkRadio>>1); 			pDist->m_sMark[offset].m_s32x =
// pMark->m_sMarkSets[l_u16i].m_s32x*(1 +
//(g_sSysPib.m_u16MarkRadio>>1)*(1/sqrt(pMark->m_sMarkSets[l_u16i].m_s32x *
// pMark->m_sMarkSets[l_u16i].m_s32x + pMark->m_sMarkSets[l_u16i].m_s32y *
// pMark->m_sMarkSets[l_u16i].m_s32y))); 			pDist->m_sMark[offset].m_s32y =
// pMark->m_sMarkSets[l_u16i].m_s32y*(1 +
//(g_sSysPib.m_u16MarkRadio>>1)*(1/sqrt(pMark->m_sMarkSets[l_u16i].m_s32x *
// pMark->m_sMarkSets[l_u16i].m_s32x + pMark->m_sMarkSets[l_u16i].m_s32y *
// pMark->m_sMarkSets[l_u16i].m_s32y)));
//		}
//	}
//	else
//	{
////							if(pMark->m_sMarkSets[l_u16i].m_u8shape ==MARK_TYPE_CYC)
////							{
////								if(pMark->m_sMarkSets[l_u16i].m_u8size !=
/// g_sSysPib.m_u16MarkRadio) /								{
////									l_s16R_diff = (pMark->m_sMarkSets[l_u16i].m_u8size>>1) -
///(g_sSysPib.m_u16MarkRadio>>1) ; /									pDist->m_u32Dist[offset] +=
/// l_s16R_diff; / l_u16markdist =
/// sqrt(pMark->m_sMarkSets[l_u16i].m_s32x*pMark->m_sMarkSets[l_u16i].m_s32x +
/// pMark->m_sMarkSets[l_u16i].m_s32y*pMark->m_sMarkSets[l_u16i].m_s32y); /
/// pDist->m_sMark[offset].m_s32x = (1 +
///(double)(1.0/l_u16markdist)*l_s16R_diff)*pMark->m_sMarkSets[l_u16i].m_s32x; /
/// pDist->m_sMark[offset].m_s32y = (1 + (double)(1.0/l_u16markdist
///)*l_s16R_diff)*pMark->m_sMarkSets[l_u16i].m_s32y; /								} /
/// else /								{
////									pDist->m_sMark[offset].m_s32x =
/// pMark->m_sMarkSets[l_u16i].m_s32x;
////									pDist->m_sMark[offset].m_s32y =
/// pMark->m_sMarkSets[l_u16i].m_s32y; /								} / } / else
//		{
//			pDist->m_sMark[offset].m_s32x = pMark->m_sMarkSets[l_u16i].m_s32x;
//			pDist->m_sMark[offset].m_s32y = pMark->m_sMarkSets[l_u16i].m_s32y;
//		}
//
//	}
//}

int MatchSetMark(MARK_SETS* pMark, u8 offset, XY2ROBOT_CNT* absXY, STRUCT_MarkMatch* p_IdenWindow)
{
    u8 l_u8j;
    u16 l_u16i;
    u16 l_u16IdentWindow = 300;               // 0.3m内认为匹配对
    u16 l_u16SetMarkSize = pMark->m_u16size;  //设置的靶标个数
    u32 l_u32Diff_X = 0, l_u32Diff_Y = 0, l_u32Diff = 0;
    u16 l_u16dist = 0;
    u32 l_u32MatchMarkNo = 0;

    for (l_u16i = 0; l_u16i < l_u16SetMarkSize; l_u16i++)
    {
        l_u32Diff_X = abs(pMark->m_sMarkSets[l_u16i].m_s32x - absXY->m_StructXY[offset].m_s32x);
        l_u32Diff_Y = abs(pMark->m_sMarkSets[l_u16i].m_s32y - absXY->m_StructXY[offset].m_s32y);
        // l_u32Diff = sqrt(pow(l_u32Diff_X,2) + pow(l_u32Diff_Y,2));
        // l_u16IdentWindow = Get_IdentWindow(l_u16dist,p_IdenWindow);

        if (l_u32Diff_X < l_u16IdentWindow && l_u32Diff_Y < l_u16IdentWindow)
        {
            {
                if (g_u16Mapping_Aver_Done_Cnt != 1)
                {
                    return Delet_List(offset, &g_auMappingList);
                }
                return 1;
            }
        }
    }
    return 0;
}

u8 CopyNavMark(STRUCT_FILTER_TARGET_LITE* p_Nav_MarkNew,
               STRUCT_FILTER_TARGET_LITE* p_Nav_MarkOld,
               u8 p_MarkNum,
               u8* p_offset,
               STRUCT_EqlTri* p_EqlTri,
               u8 p_EqlTriOffset)
{
    u8 l_u8i = 0;
    u8 l_u8Cnt = 0;
    u16 l_u16offset;
    OS_CPU_SR cpu_sr;
    MARK_XY* l_psMarkCopy = NULL;
    MARK_XY** l_psMarkNew = &p_Nav_MarkNew->m_psSetMarkAddr[0];
    MARK_XY** l_psMarkOld = &p_Nav_MarkOld->m_psSetMarkAddr[0];
    STRUCT_MARK_INFO** l_psMarkInfoCopy =
        &p_Nav_MarkOld->m_StructMarkScanInfoAddr.m_StructMarkInfo[0];
    STRUCT_MARK_INFO** l_psMarkInfoNew =
        &p_Nav_MarkNew->m_StructMarkScanInfoAddr.m_StructMarkInfo[0];
    XY_TO_RelCoor** l_psXY2RobCopy = &p_Nav_MarkOld->m_StructMarkScanInfoAddr.m_sXy2Robot[0];
    XY_TO_RelCoor** l_psXY2RobNew = &p_Nav_MarkNew->m_StructMarkScanInfoAddr.m_sXy2Robot[0];

    // OS_ENTER_CRITICAL();
    for (l_u8i = 0; l_u8i < p_MarkNum; l_u8i++)
    {
        l_psMarkCopy = p_EqlTri->m_psSetMarkAddr[p_EqlTriOffset][l_u8i];
        l_u16offset = p_offset[l_u8i];
        l_psMarkNew[l_u8Cnt] = l_psMarkCopy;
        l_psMarkOld[l_u16offset] = l_psMarkCopy;
        //		memcpy((void *)(l_psMarkOld + l_u16offset),(void
        //*)l_psMarkCopy,STRUCT_SIZE_MARK_XY); 		memcpy((void *)(l_psMarkNew + l_u8Cnt),(void
        //*)l_psMarkCopy,STRUCT_SIZE_MARK_XY);
        l_psXY2RobNew[l_u8Cnt] = l_psXY2RobCopy[l_u16offset];
        //		memcpy((void *)(l_psXY2RobNew + l_u8Cnt),(void *)(l_psXY2RobCopy +
        // l_u16offset),STRUCT_SIZE_XY_TO_RelCoor);
        l_psMarkInfoNew[l_u8Cnt] = l_psMarkInfoCopy[l_u16offset];
        //		memcpy((void *)(l_psMarkInfoNew + l_u8Cnt),(void *)(l_psMarkInfoCopy +
        // l_u16offset),STRUCT_SIZE_MARK_INFO);
        l_u8Cnt++;
    }
    // OS_EXIT_CRITICAL();
    p_Nav_MarkNew->m_StructMarkScanInfoAddr.m_u8In = l_u8Cnt;
    return 0;
}

void Find_MaxMarkRadio(MARK_SETS* p_Mark, u8* p_MaxMarkRadio)
{
    u16 l_u16i;
    *p_MaxMarkRadio = g_sSysPib.m_u16MarkRadio;
    for (l_u16i = 0; l_u16i < p_Mark->m_u16size; l_u16i++)
    {
        if (p_Mark->m_sMarkSets[l_u16i].m_u8size > *p_MaxMarkRadio)
        {
            *p_MaxMarkRadio = p_Mark->m_sMarkSets[l_u16i].m_u8size;
        }
    }
    g_u16DistDiffMax = (*p_MaxMarkRadio >> 1) * g_sMarkMatch_Set.m_u16FilterMul + 60;
}

u8 Filter_NotMark_CopyAll(STRUCT_FILTER_TARGET_LITE* p_Nav_MarkNew,
                          STRUCT_FILTER_TARGET_LITE* p_Nav_MarkOld)
{
    u8 l_u8MarkSize = p_Nav_MarkOld->m_StructMarkScanInfoAddr.m_u8In;
    u8 l_u8i = 0;
    u8 l_u8Cnt = 0;
    OS_CPU_SR cpu_sr;
    STRUCT_MARK_INFO** l_psMarkInfoAddr_Old =
        p_Nav_MarkOld->m_StructMarkScanInfoAddr.m_StructMarkInfo;
    STRUCT_MARK_INFO** l_psMarkInfoAddr_New =
        p_Nav_MarkNew->m_StructMarkScanInfoAddr.m_StructMarkInfo;
    XY_TO_RelCoor** l_psXYRob_Old = p_Nav_MarkOld->m_StructMarkScanInfoAddr.m_sXy2Robot;
    XY_TO_RelCoor** l_psXYRob_New = p_Nav_MarkNew->m_StructMarkScanInfoAddr.m_sXy2Robot;
    MARK_XY** l_psSetMarkAddr_Old = p_Nav_MarkOld->m_psSetMarkAddr;
    MARK_XY** l_psSetMarkAddr_New = p_Nav_MarkNew->m_psSetMarkAddr;
    //	OS_ENTER_CRITICAL();
    for (l_u8i = 0; l_u8i < l_u8MarkSize; l_u8i++)
    {
        if (l_psMarkInfoAddr_Old[l_u8i]->m_u8IsMark == ISMARK)
        {
            // memcpy((void *)&p_Nav_MarkNew->m_sMark[l_u8Cnt],(void
            // *)&p_Nav_MarkOld->m_sMark[l_u8i],STRUCT_SIZE_MARK_XY);
            l_psSetMarkAddr_New[l_u8Cnt] = l_psSetMarkAddr_Old[l_u8i];
            l_psXYRob_New[l_u8Cnt] = l_psXYRob_Old[l_u8i];
            // memcpy((void *)&p_Nav_MarkNew->m_sXy2Robot[l_u8Cnt],(void
            // *)&p_Nav_MarkOld->m_sXy2Robot[l_u8i],STRUCT_SIZE_XY_TO_RelCoor);
            l_psMarkInfoAddr_New[l_u8Cnt] = l_psMarkInfoAddr_Old[l_u8i];
            //			memcpy((void
            //*)&p_Nav_MarkNew->m_StructMarkScanInfo.m_StructMarkInfo[l_u8Cnt],(void
            //*)&p_Nav_MarkOld->m_StructMarkScanInfo.m_StructMarkInfo[l_u8i],STRUCT_SIZE_MARK_INFO);
            l_u8Cnt++;
        }
    }
    //	OS_EXIT_CRITICAL();
    p_Nav_MarkNew->m_StructMarkScanInfoAddr.m_u8In = l_u8Cnt;
    if (l_u8Cnt < 3)
    {
        l_u8Cnt = l_u8Cnt + 1;
        // ROS_ERROR("");
    }
    return 0;
}

u8 Filter_NotMark_CopyScan(STRUCT_FILTER_TARGET_LITE* p_Nav_MarkNew,
                           STRUCT_FILTER_TARGET_LITE* p_Nav_MarkOld)
{
    u8 l_u8MarkSize = p_Nav_MarkOld->m_StructMarkScanInfoAddr.m_u8In;
    u8 l_u8i = 0;
    u8 l_u8Cnt = 0;
    OS_CPU_SR cpu_sr;

    //	OS_ENTER_CRITICAL();

    for (l_u8i = 0; l_u8i < l_u8MarkSize; l_u8i++)
    {
        if (p_Nav_MarkOld->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i]->m_u8IsMark == ISMARK)
        {
            //			memcpy((void *)&p_Nav_MarkNew->m_sMark[l_u8Cnt],(void
            //*)&p_Nav_MarkOld->m_sMark[l_u8i],STRUCT_SIZE_MARK_XY); 			memcpy((void
            //*)&p_Nav_MarkNew->m_sXy2Robot[l_u8Cnt],(void
            //*)&p_Nav_MarkOld->m_sXy2Robot[l_u8i],STRUCT_SIZE_XY_TO_RelCoor); 			memcpy((void
            //*)&p_Nav_MarkNew->m_StructMarkScanInfo.m_StructMarkInfo[l_u8Cnt],(void
            //*)&p_Nav_MarkOld->m_StructMarkScanInfo.m_StructMarkInfo[l_u8i],STRUCT_SIZE_MARK_INFO);
            p_Nav_MarkNew->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8Cnt] =
                p_Nav_MarkOld->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i];
            p_Nav_MarkNew->m_StructMarkScanInfoAddr.m_sXy2Robot[l_u8Cnt] =
                p_Nav_MarkOld->m_StructMarkScanInfoAddr.m_sXy2Robot[l_u8i];
            l_u8Cnt++;
        }
    }

    //	OS_EXIT_CRITICAL();
    p_Nav_MarkNew->m_StructMarkScanInfoAddr.m_u8In = l_u8Cnt;
    return 0;
}
/*************************************************
Function		:	DeletCloseMark
Description		:	剔除靠的太近的靶（靶标附近正方形）
Calls			:	无
Called By		: 	TaskTargetApp
Table Accessed	: 	无
Table Updated	:	无
Input			:	p_NAV_Mark	:可能用于定位的靶标
Output			:	p_NAV_Mark	:将过于近的靶标的ismark标志标记为tooclose
Return			:	无
Others			:	无
*************************************************/
// void DeletCloseMark(STRUCT_FILTER_TARGET_LITE *p_NAV_Mark)
//{
//	u8 l_u8i;
//	u8 l_u8NextMarkPoint = 0;
//	u8 l_u8size = p_NAV_Mark->m_u8In;
//	u16 l_u16MinGapBetweenMarks = XY_ERROR<<1;
//
//	u32 l_u32diff_x = 0,l_u32diff_y = 0;

//	XY2ROBOT *l_sMarkRelPose = p_NAV_Mark->m_sXy2Robot;

//	for(l_u8i =0;l_u8i < l_u8size ;l_u8i++)
//	{
//		l_u8NextMarkPoint = (l_u8i + 1) % l_u8size;
//
//		l_u32diff_x = abs((l_sMarkRelPose + l_u8i)->m_s32x - (l_sMarkRelPose +
// l_u8NextMarkPoint)->m_s32x); 		l_u32diff_y = abs((l_sMarkRelPose + l_u8i)->m_s32y -
// (l_sMarkRelPose
//+ l_u8NextMarkPoint)->m_s32y);
//
//		if(Points_Diff_Cmp(l_u32diff_x , l_u16MinGapBetweenMarks)&&
//			Points_Diff_Cmp(l_u32diff_y , l_u16MinGapBetweenMarks)
//		)//两靶间最小间距
//		{
//			p_NAV_Mark->m_StructMarkScanInfo.m_StructMarkInfo[l_u8i].m_u8IsMark = ISMARK_TOOCLOSE;
//			p_NAV_Mark->m_StructMarkScanInfo.m_StructMarkInfo[l_u8NextMarkPoint].m_u8IsMark =
// ISMARK_TOOCLOSE;
//
//		}
//	}
//}

/*************************************************
Function		:	DeletCloseMark
Description		:	剔除靠的太近的靶（3°以内）
Calls			:	无
Called By		: 	TaskTargetApp
Table Accessed	: 	无
Table Updated	:	无
Input			:	p_NAV_Mark	:可能用于定位的靶标
Output			:	p_NAV_Mark	:将过于近的靶标的ismark标志标记为tooclose
Return			:	无
Others			:	无
*************************************************/
void DeletCloseMark(STRUCT_FILTER_TARGET_LITE* p_NAV_Mark)
{
    u8 l_u8i;
    u8 l_u8NextMarkPoint = 0;
    u8 l_u8size = p_NAV_Mark->m_StructMarkScanInfoAddr.m_u8In;
    u16 l_u16MinDistBetweenMarks = XY_ERROR << 1;  // 20cm
    u16 l_u16MinAngBetweenMarks = 300;             // 3°
    u32 l_u32diff_Dist = 0, l_u32diff_Ang = 0;

    STRUCT_MARK_INFO** l_sMarkInfo = &p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[0];
    if (l_u8size == 1)
        return;
    for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
    {
        l_u8NextMarkPoint = (l_u8i + 1) % l_u8size;

        // l_u32diff_Dist = abs((l_sMarkInfo + l_u8i)->m_u16Dist - (l_sMarkInfo +
        // l_u8NextMarkPoint)->m_u16Dist);
        l_u32diff_Ang = Find_IncludedAng_Min((*(l_sMarkInfo + l_u8i))->m_u16Ang,
                                             (*(l_sMarkInfo + l_u8NextMarkPoint))->m_u16Ang);

        if (  // Points_Diff_Cmp(l_u32diff_Dist , l_u16MinDistBetweenMarks)&&
            Points_Diff_Cmp(l_u32diff_Ang, l_u16MinAngBetweenMarks))  //两靶间最小间距
        {
            p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i]->m_u8IsMark =
                ISMARK_TOOCLOSE;
            p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8NextMarkPoint]->m_u8IsMark =
                ISMARK_TOOCLOSE;
        }
    }
}

u8 FilterMark_ByMarkMaxMinDist(STRUCT_FILTER_TARGET_LITE* p_ScanMark)
{
    u16 l_u16distdiff = 0;
    u16 l_u16DistDiffMax = g_u8MaxMarkRadio * g_sMarkMatch_Set.m_u16FilterMul;
    if (g_sMarkMatch_Set.m_u16FilterMul == 0)
        return 0;
    for (u8 l_u8i = 0; l_u8i < p_ScanMark->m_StructMarkScanInfoAddr.m_u8In; l_u8i++)
    {
        l_u16distdiff = g_au16max_dist[l_u8i] - g_au16min_dist[l_u8i];
        if ((p_ScanMark->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i]->m_u8IsMark == ISMARK)
            && (l_u16distdiff > l_u16DistDiffMax))
        {
            p_ScanMark->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i]->m_u8IsMark = NOTMARK;
        }
    }
    return 0;
}
s8 Mapping_Match_Dec_Mark(STRUCT_MARK_INFO* p_ScanMark,
                          MappingList* pMappingList,
                          u8* pMappingOffset)
{
    u16 l_u16Diff_Ang = 0, l_u16Diff_Dist = 0;
    u8 l_u8WhileCnt = 0;
    do
    {
        l_u16Diff_Ang =
            (DEG_PERSCAN + p_ScanMark->m_u16Ang - pMappingList->m_u16Ang[*pMappingOffset])
            % DEG_PERSCAN;
        if (l_u16Diff_Ang > 18000)
            l_u16Diff_Ang = DEG_PERSCAN - l_u16Diff_Ang;
        l_u16Diff_Dist = abs(p_ScanMark->m_u16Dist - pMappingList->m_u16Dist[*pMappingOffset]);

        if (Points_Diff_Cmp(l_u16Diff_Ang, 20) &&  //角度跳动 ±5/10(10mDeg)
            Points_Diff_Cmp(l_u16Diff_Dist, 120)   //距离偏差±6cm
        )
        {
            pMappingList->m_XY2Rob.m_u16cnt[*pMappingOffset]++;
            //*pMappingOffset = pMappingList->m_u8Next[*pMappingOffset];
            return (*pMappingOffset + 1);
        }
        else
        {
            *pMappingOffset = pMappingList->m_u8Next[*pMappingOffset];
        }
        l_u8WhileCnt++;
    } while (*pMappingOffset != pMappingList->m_u8First);

    *pMappingOffset = pMappingList->m_XY2Rob.m_u8MarkNum;
    return -1;
}

void Init_List(u8 l_u8i, MappingList* p_MappingList, u8 p_Size)
{
    u8 l_u8First = p_MappingList->m_u8First;
    if (l_u8i == l_u8First)
        g_auMappingList.m_u8Last[l_u8i] = p_Size - 1;
    else
        g_auMappingList.m_u8Last[l_u8i] = l_u8i - 1;

    if (l_u8i == p_Size - 1)
        g_auMappingList.m_u8Next[l_u8i] = l_u8First;
    else
        g_auMappingList.m_u8Next[l_u8i] = l_u8i + 1;

    p_MappingList->m_XY2Rob.m_u16cnt[l_u8i] = 1;
    p_MappingList->m_u16Ang[l_u8i] =
        g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i]->m_u16Ang;
    p_MappingList->m_u16Dist[l_u8i] =
        g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i]->m_u16Dist;
    p_MappingList->m_XY2Rob.m_u8MarkNum = g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In;
}

void Renew_List(s8 l_u8i, MappingList* p_MappingList, STRUCT_MARK_INFO* p_ScanMark)
{
    u8 l_u8First = p_MappingList->m_u8First;
    u8 l_u8End = p_MappingList->m_u8Last[l_u8First];
    u8 l_u8Last = 0;
    u8 l_u8Next = 0;
    u8 l_u8TotalDecMarkSize = p_MappingList->m_XY2Rob.m_u8MarkNum;
    p_MappingList->m_u16Ang[l_u8TotalDecMarkSize] = p_ScanMark->m_u16Ang;
    p_MappingList->m_u16Dist[l_u8TotalDecMarkSize] = p_ScanMark->m_u16Dist;
    if (l_u8i == -1)
    {
        p_MappingList->m_u8First = l_u8TotalDecMarkSize;  //更新First位置

        p_MappingList->m_u8Next[l_u8End] = l_u8TotalDecMarkSize;  //更新最后一个靶标的Next指针
        p_MappingList->m_u8Next[l_u8TotalDecMarkSize] =
            l_u8First;  //更新新添加靶的next指针，应为之前的第一个靶

        p_MappingList->m_u8Last[l_u8TotalDecMarkSize] =
            l_u8End;  //更新新添加靶的last指针，应为最后一个靶，即之前第一个靶的last指针
        p_MappingList->m_u8Last[l_u8First] =
            l_u8TotalDecMarkSize;  //更新之前起始靶的last值，应为新添加的靶

        p_MappingList->m_XY2Rob.m_u16cnt[l_u8TotalDecMarkSize] = 1;
    }
    else if (l_u8i < (l_u8TotalDecMarkSize + 1))
    {
        p_MappingList->m_u8Last[l_u8TotalDecMarkSize] =
            l_u8i;  //更新新添加靶的last指针，应为l_u8i靶
        p_MappingList->m_u8Next[l_u8TotalDecMarkSize] =
            p_MappingList->m_u8Next[l_u8i];  //更新新添加靶的next指针，应为l_u8i靶的next指针
        l_u8Next = p_MappingList->m_u8Next[l_u8i];  //没插入新靶前l_u8i靶的next指针
        p_MappingList->m_u8Next[l_u8i] =
            l_u8TotalDecMarkSize;  //更新l_u8i靶的next指针，应为新添加的靶
        p_MappingList->m_u8Last[l_u8Next] =
            l_u8TotalDecMarkSize;  //更新之前l_u8i靶下一个靶的last指针，应为新添加的靶
        p_MappingList->m_XY2Rob.m_u16cnt[l_u8TotalDecMarkSize] = 1;
    }
    else
    {
        p_MappingList->m_u8Next[l_u8TotalDecMarkSize] =
            p_MappingList->m_u8First;  //更新新添加靶的next指针，应为first指针
        l_u8Last = p_MappingList->m_u8Last[p_MappingList->m_u8First];  //没插入之前最后一个靶的位置
        p_MappingList->m_u8Last[l_u8TotalDecMarkSize] =
            l_u8Last;  // l_u8i;//更新新添加靶的last指针，应为最后一个靶，即没插入之前first指针指的last值，或此时的l_u8i靶
        p_MappingList->m_u8Last[p_MappingList->m_u8First] =
            l_u8TotalDecMarkSize;  //更新第一个靶的last指针，即新添加的靶
        p_MappingList->m_u8Next[l_u8Last] =
            l_u8TotalDecMarkSize;  //更新没添加之前最后一个靶的next指针，即为新添加的靶
        p_MappingList->m_XY2Rob.m_u16cnt[l_u8TotalDecMarkSize] = 1;
    }
}
u8 Delet_List(u8 p_offset, MappingList* p_MappingList)
{
    p_MappingList->m_u8Next[p_MappingList->m_u8Last[p_offset]] =
        p_MappingList->m_u8Next[p_offset];  //将前一个靶的next值变为该靶的next值
    p_MappingList->m_u8Last[p_MappingList->m_u8Next[p_offset]] =
        p_MappingList->m_u8Last[p_offset];  //将后一个靶的last值变为该靶的last值
    //如果改靶是第一个靶，则需要更新First值
    if (p_offset == p_MappingList->m_u8First)
        p_MappingList->m_u8First = p_MappingList->m_u8Next[p_offset];  // first值为下一个靶值
    return 1;
}
s8 Find_List(MappingList* p_MappingList, STRUCT_MARK_INFO* p_ScanMark)
{
    u16 l_u16IdenWindow = Get_IdentWindow(0, &g_sMarkMatch_Set);
    u8 l_u8Next = 0;
    u8 l_u8First = p_MappingList->m_u8First;
    u8 l_u8End = p_MappingList->m_u8Last[l_u8First];
    u8 l_u8i;
    for (l_u8i = l_u8First; l_u8i <= p_MappingList->m_XY2Rob.m_u8MarkNum - 1;)
    {
        l_u8Next = p_MappingList->m_u8Next[l_u8i];
        if (p_ScanMark->m_u16Ang <= p_MappingList->m_u16Ang[l_u8i])
        {
            return -1;
        }
        if ((p_ScanMark->m_u16Ang > p_MappingList->m_u16Ang[l_u8i])
            && (p_ScanMark->m_u16Ang <= p_MappingList->m_u16Ang[l_u8Next]))
        {
            return l_u8i;
        }
        if (p_ScanMark->m_u16Ang > p_MappingList->m_u16Ang[l_u8End])
        {
            return p_MappingList->m_XY2Rob.m_u8MarkNum + 1;
        }
        l_u8i = p_MappingList->m_u8Next[l_u8i];
    }
    return -1;
}

int MatchDecMark_And_RenewList(STRUCT_FILTER_TARGET_LITE* pDistNew,
                               MappingList* pMappingList,
                               u8 newoffset,
                               u8* pMappingOffset)
{
    u8 l_u8tmpoff = *pMappingOffset;
    s8 l_s8ret = 0;
    u8 l_u8TotalDecMarkSize = pMappingList->m_XY2Rob.m_u8MarkNum;

    l_s8ret = Mapping_Match_Dec_Mark(pDistNew->m_StructMarkScanInfoAddr.m_StructMarkInfo[newoffset],
                                     pMappingList,
                                     pMappingOffset);
    if (l_s8ret == -1)
    {
        if (pMappingList->m_XY2Rob.m_u8MarkNum < MAPPING_MAX)
        {
            l_s8ret = Find_List(pMappingList,
                                pDistNew->m_StructMarkScanInfoAddr.m_StructMarkInfo[newoffset]);
            Renew_List(l_s8ret,
                       pMappingList,
                       pDistNew->m_StructMarkScanInfoAddr.m_StructMarkInfo[newoffset]);
            pMappingList->m_XY2Rob.m_u8MarkNum++;
            l_u8tmpoff = l_u8TotalDecMarkSize;
        }
        else
        {
            *pMappingOffset = l_u8tmpoff;
        }
    }
    return l_u8tmpoff;
}

u16 CorrAng_ByLoop(u16 p_Ang, u16 p_MaxAng)
{
    return ((p_MaxAng - p_Ang) > 3000) ? (p_Ang + DEG_PERSCAN) : p_Ang;
}

u16 Round_Ang(u16 p_u16Ang, u16 p_AngResolution)
{
    return (p_u16Ang / p_AngResolution) * p_AngResolution;
}
//根据当前激光器的位置(x,y)计算扫描到的靶标的相对坐标和环境坐标的夹角
//路标的环境坐标和相对坐标,求夹角
u16 Cal_Laser_AbsAng(STRUCT_FILTER_TARGET_LITE* pDist, u8 size, ROBOT_XY* robot)
{
    u8 l_u8i;
    u16 l_u16LaserAng = 0;
    // float l_u16ScanMarkAng = 0;
    u32 l_u32sum = 0;
    // float l_u16SetMarkAbsAng = 0;
    u16 l_u16Ang = 0;
    u16 l_u16Ang_Max = 0;

    for (l_u8i = 0; l_u8i < size; l_u8i++)
    {
        // l_u16ScanMarkAng =
        // (float)pDist->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i]->m_u16Ang;//扫描到靶的方位角
        //根据当前的激光器的位置计算与靶标的绝对坐标,计算靶标以激光器为圆心的绝对坐标系的角度,[0~DEG_PERSCAN]10mdeg单位
        // l_u16SetMarkAbsAng = Cal_PerMark_AbsAng(pDist->m_psSetMarkAddr[l_u8i],
        // &robot->m_Struct_LaserPos ) ;

        //靶标与绝对坐标系夹角(laser为圆心)
        g_u16Ang_SetMark_AbsCoor[l_u8i] =
            (u16)Cal_PerMark_AbsAng(pDist->m_psSetMarkAddr[l_u8i], &robot->m_Struct_LaserPos);
        //靶标与相对坐标系夹角
        g_u16Ang_ScanMark_RelCoor[l_u8i] =
            pDist->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i]->m_u16Ang;  //扫描到靶的方位角
        l_u16Ang =
            (DEG_PERSCAN + g_u16Ang_SetMark_AbsCoor[l_u8i] - g_u16Ang_ScanMark_RelCoor[l_u8i])
            % DEG_PERSCAN;
        if (l_u16Ang > l_u16Ang_Max)
        {
            l_u16Ang_Max = l_u16Ang;
            // ROS_ERROR("l_u16Ang_Max=%d",l_u16Ang_Max);
        }
        g_u16Ang_Laser_AbsCoor[l_u8i] = l_u16Ang;
    }

    //防止出现方位角 1, 7199跨越边界的出现
    for (l_u8i = 0; l_u8i < size; l_u8i++)
    {
        l_u16Ang = g_u16Ang_Laser_AbsCoor[l_u8i];
        // ROS_ERROR("l_u16Ang=%d",l_u16Ang);
        l_u32sum += CorrAng_ByLoop(l_u16Ang, l_u16Ang_Max);
        // ROS_ERROR("l_u16Ang=%d,l_u32sum=%d",l_u16Ang,l_u32sum);
    }

    l_u16LaserAng = (u16)(l_u32sum / size) % DEG_PERSCAN;
    // ROS_ERROR("l_u32sum=%d,l_u16LaserAng=%d",l_u32sum,l_u16LaserAng);
    //	l_u16LaserAng = Round_Ang(l_u16LaserAng,g_u8Ang_Resolution);

    return l_u16LaserAng;
}

/*****************************************************************************
*************************    计算机器人位置相关   **************************************
*****************************************************************************/

bool Points_Diff_Cmp(u32 p_Diff_In, u32 p_Diff_Cmp_In)
{
    return p_Diff_In <= p_Diff_Cmp_In;
}
u16 Return_Max(u16 p_In1, u16 p_In2)
{
    return (p_In1 > p_In2) ? p_In1 : p_In2;
}
//比较前一次和后一次的激光器位置
int CmpRobotPos(ROBOT_XY* old, ROBOT_XY* cur, INPUTSPEED* SPEED, u32 p_ScanOverTS)
{
    u16 l_u16DistDiff_BySpeed = 0;
    u16 speed_x = abs(SPEED->m_StructSpeed_absolute.m_s16speedx);
    u16 speed_y = abs(SPEED->m_StructSpeed_absolute.m_s16speedy);
    u32 l_u32Diff_X = 0, l_u32Diff_Y = 0;
    u16 l_u16CmpDiff = 0;
    u32 l_u32TSDiff = Get_TS_Diff(p_ScanOverTS, old->m_u32timestamp);
    l_u16DistDiff_BySpeed = Return_Max(speed_x, speed_y) * l_u32TSDiff;  //每圈速度

    l_u16CmpDiff = ERR_ROBOT_POS + l_u16DistDiff_BySpeed;
    l_u32Diff_X = abs(old->m_Struct_LaserPos.m_s32x - cur->m_Struct_LaserPos.m_s32x);
    l_u32Diff_Y = abs(old->m_Struct_LaserPos.m_s32y - cur->m_Struct_LaserPos.m_s32y);
    if (!Points_Diff_Cmp(l_u32Diff_X, l_u16CmpDiff))
        return -1;
    if (!Points_Diff_Cmp(l_u32Diff_Y, l_u16CmpDiff))
        return -1;
    if (Find_IncludedAng_Min(cur->m_Struct_LaserPos.m_u16ang, old->m_Struct_LaserPos.m_u16ang)
        > (36 * l_u32TSDiff))
        return -1;
    return 0;
}

int CmpRobotPos1(ROBOT_XY* old, XYANG2ROBOT* cur, u16 errdiff)
{
    if (abs(old->m_Struct_LaserPos.m_s32x - cur->m_s32x) > errdiff)
        return -1;
    if (abs(old->m_Struct_LaserPos.m_s32y - cur->m_s32y) > errdiff)
        return -1;

    //	if(abs(old->m_u16ang - cur->m_u16ang) > ERR_ROBOT_ANG)
    //		return -1 ;
    return 0;
}
//计算
int Get_Laser_Pos_FromFpga(ROBOT_XY* p_LaserPos, u8 i, u8 cmd, u8 decMarkNum)
{
#pragma region XXX
/*
 * 地图上的n个靶标坐标注入[2,n]矩阵V
 * | xA1,xA2,xA3,..|
 * | yA1,yA2,yA3,..|
 */
/*
 * 扫描的n个靶标坐标注入[2,n]矩阵U
 * | xB1,xB2,xB3,..|
 * | yB1,yB2,yB3,..|
 */
// Eigen::MatrixXf MarkSetSca;
// Eigen::MatrixXf MarkSetMap;
// /**重心**/
// Eigen::MatrixXf MarkSetSca_center;
// Eigen::MatrixXf MarkSetMap_center;
// MarkSetSca.resize(2,decMarkNum);
// MarkSetMap.resize(2,decMarkNum);
// MarkSetSca_center.resize(2,decMarkNum);
// MarkSetMap_center.resize(2,decMarkNum);
// /*****重心*****/
// float MarkSet_center[4] = {0,0,0,0};
// for (int i = 0;i< decMarkNum;i++)
// {
// 	MarkSetSca(0,i) =  g_sFpgaPos.m_psSetMarkAddr[i]->m_s32x;
// 	MarkSetSca(1,i) =  g_sFpgaPos.m_psSetMarkAddr[i]->m_s32y;

// 	MarkSetMap(0,i) =  g_sFpgaPos.m_psXy2Robot[i].m_s32x;
// 	MarkSetMap(1,i) =  g_sFpgaPos.m_psXy2Robot[i].m_s32y;

// 	MarkSet_center[0] += MarkSetSca(0,i);//map_X
// 	MarkSet_center[1] += MarkSetSca(1,i);//map_Y

// 	MarkSet_center[2] += MarkSetMap(0,i);//sca_X
// 	MarkSet_center[3] += MarkSetMap(1,i);//sca_Y
// }
// //ROS_INFO("MapSet=[[%.3f,%.3f],
// [%.3f,%.3f]]",MarkSetSca(0,0),MarkSetSca(0,1),MarkSetSca(1,0),MarkSetSca(1,1));
// //ROS_INFO("ScanSet=[[%.3f,%.3f],
// [%.3f,%.3f]]",MarkSetMap(0,0),MarkSetMap(0,1),MarkSetMap(1,0),MarkSetMap(1,1));

// /**重心**/
// for (int i = 0;i< 4;i++)
// {
// 	MarkSet_center[i] /= decMarkNum;
// }
// for (int i = 0;i< decMarkNum;i++)
// {
// 	MarkSetSca_center(0,i) = MarkSet_center[0];
// 	MarkSetSca_center(1,i) = MarkSet_center[1];

// 	MarkSetMap_center(0,i) = MarkSet_center[2];
// 	MarkSetMap_center(1,i) = MarkSet_center[3];
// }

// /*
// * M = MapSetVector*ScanSetVector^T 表示 ScanSetVector->MapSetVector 的变换
// */
// Eigen::Matrix2f M;
// M = (MarkSetMap -MarkSetMap_center ) * ( MarkSetSca - MarkSetSca_center).transpose();
// Eigen::JacobiSVD<Eigen::MatrixXf> svd(M, Eigen::ComputeFullV | Eigen::ComputeFullU);

// Eigen::MatrixXf  V = svd.matrixV(), U = svd.matrixU();

// //旋转矩阵
// Eigen::Matrix2f R;
// /*
// * R = ScanSetVector*MapSetVectorT 表示 MapSetVector->ScanSetVector 的变换
// */
// R  = V * U.transpose();
// if (R.determinant() < 0)
// {
// 	Eigen::JacobiSVD<Eigen::MatrixXf> svd(R, Eigen::ComputeFullV | Eigen::ComputeFullU);
// 	R  = V * U.transpose();
// }

// ROS_INFO("R=[[%.3f,%.3f], [%.3f,%.3f]]",std::acos(R(0,0)),std::asin(R(0,1)),R(1,0),R(1,1));
// Eigen::MatrixXf T;
// //T.resize(2,decMarkNum);
// T = MarkSetMap_center-R*MarkSetSca_center;
// ROS_INFO("T=[[%.3f,%.3f], [%.3f,%.3f]]",T(0,0),T(0,1),T(1,0),T(1,1));
// p_LaserPos->m_Struct_LaserPos.m_u16ang =  (u16) (asin(R(1,0)) / M_PI * 18000);

// p_LaserPos->m_Struct_LaserPos.m_s32x   =  (s32) round(T(0,0));
// p_LaserPos->m_Struct_LaserPos.m_s32y   =  (s32) round(T(1,0));
// ROS_INFO("m_s32x=%d,m_s32y=%d,m_u16ang=%d",p_LaserPos->m_Struct_LaserPos.m_s32x,p_LaserPos->m_Struct_LaserPos.m_s32y,int(asin(R(1,0))
// / M_PI * 18000));
#pragma endregion

    /*
     * 最小二乘法
     * 求解 Ax=B
     *     |x0, -y0, 1, 0|       |cos(t)|	   | x`0|
     *     |y0, x0,  0, 1| 		|sin(t)|	   | y`0|
     * A = |x1, -y1, 1, 0| , x = |deltaX| , B = | x`1|
     * 	  |y1, x1,  0, 1|		|deltaY|	   | y`1|
     * 	  | ... ... ... | 					   | ...|
     */
    // test//
    // decMarkNum =3 ;
    Eigen::MatrixXf MarkSetSca;
    Eigen::MatrixXf MarkSetMap;
    MarkSetSca.resize(2 * decMarkNum, 4);
    MarkSetMap.resize(2 * decMarkNum, 1);

    for (int i = 0; i < decMarkNum; i++)
    {
        MarkSetSca(2 * i, 0) = (float)g_sFpgaPos.m_psXy2Robot[i].m_s32x;
        MarkSetSca(2 * i, 1) = -(float)g_sFpgaPos.m_psXy2Robot[i].m_s32y;
        MarkSetSca(2 * i, 2) = 1.0;
        MarkSetSca(2 * i, 3) = 0.0;
        MarkSetSca(2 * i + 1, 0) = (float)g_sFpgaPos.m_psXy2Robot[i].m_s32y;
        MarkSetSca(2 * i + 1, 1) = (float)g_sFpgaPos.m_psXy2Robot[i].m_s32x;
        MarkSetSca(2 * i + 1, 2) = 0.0;
        MarkSetSca(2 * i + 1, 3) = 1.0;

        MarkSetMap(2 * i, 0) = (float)g_sFpgaPos.m_psSetMarkAddr[i]->m_s32x;
        MarkSetMap(2 * i + 1, 0) = (float)g_sFpgaPos.m_psSetMarkAddr[i]->m_s32y;
    }

    // test//
    // MarkSetSca(0,0) = MarkSetSca(1,1) = 1881.0f;
    // MarkSetSca(2,0) = MarkSetSca(3,1) = 734.0f;
    // MarkSetSca(4,0) = MarkSetSca(5,1) = -1122.0f;
    // MarkSetSca(0,1) = -2730.0f;
    // MarkSetSca(2,1) = -3409.0f;
    // MarkSetSca(4,1) = -1159.0f;
    // MarkSetSca(1,0) = 2730.0f;
    // MarkSetSca(3,0) = 3409.0f;
    // MarkSetSca(5,0) = 1159.0f;
    // MarkSetSca(0,2) = MarkSetSca(2,2) = MarkSetSca(4,2) = 1.0;
    // MarkSetSca(0,3) = MarkSetSca(2,3) = MarkSetSca(4,3) = 0.0;
    // MarkSetSca(1,2) = MarkSetSca(3,2) = MarkSetSca(5,2) =  0.0;
    // MarkSetSca(1,3) = MarkSetSca(3,3) = MarkSetSca(5,3) = 1.0;

    // MarkSetMap(0,0) = 763.0f;
    // MarkSetMap(1,0) = 2837.0f;
    // MarkSetMap(2,0) = -415.0f;
    // MarkSetMap(3,0) = 3463.0f;
    // MarkSetMap(4,0) = -2215.0f;
    // MarkSetMap(5,0) = 1169.0f;

    /**********最小二乘方法********************/
    Eigen::MatrixXf x_lS;
    x_lS = ((MarkSetSca.transpose() * MarkSetSca).inverse()) * MarkSetSca.transpose() * MarkSetMap;
    p_LaserPos->m_Struct_LaserPos.m_s32x = (s32)round(x_lS(2, 0));
    p_LaserPos->m_Struct_LaserPos.m_s32y = (s32)round(x_lS(3, 0));
    float ang = round(asin(x_lS(1, 0)) / M_PI * 18000);
    if (ang < 0)
        ang += 36000;
    else if (ang >= 36000)
        ang -= 36000;
    p_LaserPos->m_Struct_LaserPos.m_u16ang = (u16)ang;
    // ROS_INFO("x_ls=[%.3f,%.3f,%.3f,%.3f]",x_lS(0,0),x_lS(1,0),x_lS(2,0),x_lS(3,0));
    /**********jacobiSvd方法********************/
    // Eigen::MatrixXf x_jacobiSvd;
    // x_jacobiSvd = MarkSetSca.jacobiSvd(Eigen::ComputeThinU |
    // Eigen::ComputeThinV).solve(MarkSetMap);
    // ROS_INFO("x_jac=[%.3f,%.3f,%.3f,%.3f]",x_jacobiSvd(0,0),x_jacobiSvd(1,0),x_jacobiSvd(2,0),x_jacobiSvd(3,0));
    // p_LaserPos->m_Struct_LaserPos.m_s32x = (s32) round(-x_jacobiSvd(2,0));
    // p_LaserPos->m_Struct_LaserPos.m_s32y = (s32) round(-x_jacobiSvd(3,0));
    // float ang = -round(asin(x_jacobiSvd(1,0))/M_PI*18000);
    // if (ang <0) ang += 36000;
    // else if (ang >= 36000) ang -= 36000;
    // p_LaserPos->m_Struct_LaserPos.m_u16ang = (u16) ang ;
    // ROS_INFO("Pos=[%d,%d,%d]",p_LaserPos->m_Struct_LaserPos.m_s32x,p_LaserPos->m_Struct_LaserPos.m_s32y,(s32)ang);
    /**********colPivHouseholderQr方法**********/
    // Eigen::MatrixXf x_colPivHouseholderQr;
    // x_colPivHouseholderQr = MarkSetSca.colPivHouseholderQr().solve(MarkSetMap);
    // ROS_INFO("x_qr=[%.3f,%.3f,%.3f,%.3f]",x_colPivHouseholderQr(0,0),x_colPivHouseholderQr(1,0),x_colPivHouseholderQr(2,0),x_colPivHouseholderQr(3,0));
    // p_LaserPos->m_Struct_LaserPos.m_s32x = (s32) round(-x_colPivHouseholderQr(2,0));
    // p_LaserPos->m_Struct_LaserPos.m_s32y = (s32) round(-x_colPivHouseholderQr(3,0));

    p_LaserPos->m_s32matixx[i] = p_LaserPos->m_Struct_LaserPos.m_s32x;

    p_LaserPos->m_s32matixy[i] = p_LaserPos->m_Struct_LaserPos.m_s32y;
    // /******debug********/
    // if (SaveNavCnt > 0)
    // {
    // 	ROS_INFO("x=[%.3f,%.3f,%.3f,%.3f]",x_jacobiSvd(0,0),x_jacobiSvd(1,0),x_jacobiSvd(2,0),x_jacobiSvd(3,0));
    // 	// d0File.open("/home/<USER>/loam_ws/src/Mark/test/nav.csv",std::ios::app);
    // 	// //d0File<<"s32matixx"<<endl;
    // 	// d0File<<1<<std::endl;
    // 	// d0File.close();
    // }
    // /******debug********/
    return 0;
}

/*****************************************************************************
*************************    算法相关   **************************************
*****************************************************************************/

u32 PollLength(u32 len,
               u8 offsetj,
               MARK_XY* ref_target,
               u16 offset,
               u16 left_size,
               u8* cur_pos,
               MARK_SETS* g_sMarkSets,
               u8* offsetout,
               u8 poly_size,
               u8 side_cnt,
               u8* last)
{
#define CAL_ERROR 150
    MARK_XY* l_spTarget;
    //从偏移位置开始和下一个比较,比较长度为size
    //操作了size,要回到起始位置开始比较
    l_spTarget = ref_target;  //一直指向路标的首地址
    //从这剩余的size找长度len的匹配
    u8 l_u8i, l_u8j;
    u8 l_u8size = g_sMarkSets->m_u16size;  //路标的个数
    long long l_u64tmp;
    u32 l_u32ret = 0;
    int l_s32tmpx1, l_s32tmpx2, l_s32tmpy1, l_s32tmpy2;
    u32 l_u32no1, l_u32no2;
    static u32 l_u32tmp;

    l_s32tmpx1 = (l_spTarget + (offsetj + offset) % l_u8size)->m_s32x;
    l_s32tmpy1 = (l_spTarget + (offsetj + offset) % l_u8size)->m_s32y;
    l_u32no1 = (l_spTarget + (offsetj + offset) % l_u8size)->m_u32no;
    for (l_u8i = 1; l_u8i <= left_size; l_u8i++)
    {
        //这里的l_u8i 是不包含偏移offset的
        l_s32tmpx2 = (l_spTarget + (l_u8i + offset + offsetj) % (l_u8size))->m_s32x;
        l_s32tmpy2 = (l_spTarget + (l_u8i + offset + offsetj) % (l_u8size))->m_s32y;
        l_u32no2 = (l_spTarget + (l_u8i + offset + offsetj) % (l_u8size))->m_u32no;
        l_u64tmp = (l_s32tmpx1 - l_s32tmpx2) * (l_s32tmpx1 - l_s32tmpx2)
                   + (l_s32tmpy1 - l_s32tmpy2) * (l_s32tmpy1 - l_s32tmpy2);
        l_u32tmp = sqrt(l_u64tmp);
        if (fabs(len - l_u32tmp) <= CAL_ERROR)
        {
            //因为l_u8i是从offset开始的，当超过了size的时候，从0开始
            *cur_pos = (l_u8i + offset + offsetj) % l_u8size;  //如果找到了,返回数组的标号
            //剩余的个数
            if (l_u8size >= (l_u8i + offset))
                l_u32ret = l_u8size - l_u8i - offset;
            else
                l_u32ret = 0;
            //下一次的偏移是从offset+offsetj开始
            offset += l_u8i;
            offset %= l_u8size;
            *offsetout = offset;

            //判断最后一个边长,首尾相连接的部分
            if ((poly_size - 1) == side_cnt)
            {
                *last = 1;
            }
            else
            {
                *last = 0;
            }
            return l_u32ret;
        }
    }
    *last = 0;
    //表示没有找到
    return l_u32ret;
}

//找到最小值的位置
u32 FindMinVal(u16* pBuf, u8 size)
{
    u8 l_u8i;
    u32 l_u32ret = 0;
    u16 l_u16tmp;
    l_u16tmp = *pBuf;
    for (l_u8i = 1; l_u8i < size; l_u8i++)
    {
        if (*(pBuf + l_u8i) < l_u16tmp)
        {
            //交换
            l_u16tmp = *(pBuf + l_u8i);
            l_u32ret = l_u8i;
        }
    }
    return l_u32ret;
}

u32 DecMark(u16* pDist, u8 size)
{
    //选路标
    u8 l_u8i;
    u8 l_u8size = size;  // pDist->m_u8In  ;
    u16 l_u16tmp, l_u16pre, l_u16next;

    u8 l_u8idx;    //找到最小值的索引
    u32 l_u32ret;  //返回消灭的点

    //求取每2个点点夹角, 假定不会超过16个路标
    static u16 l_u16ang[16];
    for (l_u8i = 0; l_u8i < l_u8size - 1; l_u8i++)
    {
        l_u16ang[l_u8i] = *(pDist + l_u8i + 1) - *(pDist + l_u8i);
    }
    //计算首尾的夹角
    l_u16ang[l_u8size - 1] = 7200 + *pDist - *(pDist + l_u8size - 1);

    //对两两之间的夹角进行排序
    l_u8idx = FindMinVal(l_u16ang, l_u8size);

    l_u16next = l_u16ang[(l_u8idx + 1) % l_u8size];
    l_u16pre = l_u16ang[(l_u8idx + l_u8size - 1) % l_u8size];
    if (l_u16next <= l_u16pre)
    {
        //这个和后面的合并,去除的点的序号是 l_u8idx + 1;
        l_u32ret = (l_u8idx + 1) % l_u8size;
    }
    else
    {
        //
        l_u32ret = l_u8idx % l_u8size;
    }

    return l_u32ret;
}

//删除数组里面的一个元素, 个数-1
int DelItem(u16* buf, u8 size, u8 idx)
{
    u8 l_u8i;
    //*(buf + idx) = 0xffff ;
    if (size > 16)
        return -1;
    // for(l_u8i = idx ; l_u8i < size ;l_u8i++)
    for (l_u8i = idx; l_u8i < size - 1; l_u8i++)
    {
        //后面一个覆盖前面一个数
        *(buf + l_u8i) = *(buf + l_u8i + 1);
    }
    //如果idx=size最后一个,直接忽略
    return 0;
}

int Cal_Laser_Pose_ByFpga(STRUCT_FILTER_TARGET_LITE* p_NAV_Mark,
                          STRUCT_FPGAPOS* p_Fpga,
                          u8* p_offsetbuf,
                          u8 p_offset,
                          ROBOT_XY* p_LaserPos,
                          u8 p_NavMarkNum)
{
    u8 l_u8i = 0;
    OS_CPU_SR cpu_sr;
    int l_s32ret;
    u16 l_u16Dist = 0;
    float l_f32MarkRad = 0;
    u8 l_u8offset;
    MARK_XY** l_psSetMarkAddr = p_NAV_Mark->m_psSetMarkAddr;
    XY_TO_RelCoor** l_psMarkInfoRelXY = p_NAV_Mark->m_StructMarkScanInfoAddr.m_sXy2Robot;
    MARK_XY** l_psFpgaSetMarkAddr = g_sFpgaPos.m_psSetMarkAddr;
    XY_TO_RelCoor l_sXY_to_RelCoorTmp;

    for (l_u8i = 0; l_u8i < p_NavMarkNum; l_u8i++)
    {
        l_u8offset = p_offsetbuf[l_u8i];
        Corr_Mark_MixLayout(p_NAV_Mark, l_u8offset, &l_sXY_to_RelCoorTmp);

        // memcpy((void *)&g_sFpgaPos.m_sMark[l_u8i],(void
        // *)p_NAV_Mark->m_sMark[p_offsetbuf[l_u8i]],STRUCT_SIZE_MARK_XY);
        l_psFpgaSetMarkAddr[l_u8i] = l_psSetMarkAddr[l_u8offset];
        memcpy((void*)&g_sFpgaPos.m_psXy2Robot[l_u8i],
               (void*)&l_sXY_to_RelCoorTmp,
               STRUCT_SIZE_XY_TO_RelCoor);
    }
    // l_s32ret = Tran_To_Float(p_Fpga , p_NavMarkNum);//将数据转换成浮点数
    l_s32ret = Get_Laser_Pos_FromFpga(p_LaserPos, p_offset, FPGA_CAL_INIT, p_NavMarkNum);
    return 0;
}
void Corr_Mark_MixLayout_Final(STRUCT_FILTER_TARGET_LITE* p_NAV_Mark, u8 p_offset)
{
    int l_s32Dist = 0;
    int l_s32DistOld = 0;
    float l_f32MarkRad = 0;
    XY_TO_RelCoor* l_psRelCoor = p_NAV_Mark->m_StructMarkScanInfoAddr.m_sXy2Robot[p_offset];

    l_s32DistOld = p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[p_offset]->m_u16Dist;
    if (g_sSysPib.m_u16MarkType == MARK_TYPE_CYC)
    {
        if (p_NAV_Mark->m_psSetMarkAddr[p_offset]->m_u8shape != g_sSysPib.m_u16MarkType)
        {
            ROS_ERROR("Corr_Mark_MixLayout_Final1");
            l_s32Dist = p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[p_offset]->m_u16Dist
                        - (g_sSysPib.m_u16MarkRadio >> 1);
            l_psRelCoor->m_s32x = l_s32Dist * l_psRelCoor->m_s32x / l_s32DistOld;
            l_psRelCoor->m_s32y = l_s32Dist * l_psRelCoor->m_s32y / l_s32DistOld;
            //			l_RelCoor.m_s32x =
            // l_s32Dist*p_NAV_Mark->m_sXy2Robot[p_offset].m_s32x/l_s32DistOld;
            // l_RelCoor.m_s32y = l_s32Dist*p_NAV_Mark->m_sXy2Robot[p_offset].m_s32y/l_s32DistOld;
        }
        else if (p_NAV_Mark->m_psSetMarkAddr[p_offset]->m_u8size != g_sSysPib.m_u16MarkRadio)
        {
            ROS_ERROR("Corr_Mark_MixLayout_Final2");
            l_s32Dist = p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[p_offset]->m_u16Dist
                        + (p_NAV_Mark->m_psSetMarkAddr[p_offset]->m_u8size >> 1)
                        - (g_sSysPib.m_u16MarkRadio >> 1);
            l_psRelCoor->m_s32x = l_s32Dist * l_psRelCoor->m_s32x / l_s32DistOld;
            l_psRelCoor->m_s32y = l_s32Dist * l_psRelCoor->m_s32y / l_s32DistOld;
            //			l_RelCoor.m_s32x =
            // l_s32Dist*p_NAV_Mark->m_sXy2Robot[p_offset].m_s32x/l_s32DistOld;
            // l_RelCoor.m_s32y = l_s32Dist*p_NAV_Mark->m_sXy2Robot[p_offset].m_s32y/l_s32DistOld;
        }
    }
    else
    {
        if (p_NAV_Mark->m_psSetMarkAddr[p_offset]->m_u8shape != g_sSysPib.m_u16MarkType)
        {
            ROS_ERROR("Corr_Mark_MixLayout_Final3");
            l_s32Dist = p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[p_offset]->m_u16Dist
                        + (p_NAV_Mark->m_psSetMarkAddr[p_offset]->m_u8size >> 1);
            l_psRelCoor->m_s32x = l_s32Dist * l_psRelCoor->m_s32x / l_s32DistOld;
            l_psRelCoor->m_s32y = l_s32Dist * l_psRelCoor->m_s32y / l_s32DistOld;
            //			l_RelCoor.m_s32x =
            // l_s32Dist*p_NAV_Mark->m_sXy2Robot[p_offset].m_s32x/l_s32DistOld;
            // l_RelCoor.m_s32y = l_s32Dist*p_NAV_Mark->m_sXy2Robot[p_offset].m_s32y/l_s32DistOld;
        }
    }
}

void Corr_Mark_MixLayout(STRUCT_FILTER_TARGET_LITE* p_NAV_Mark,
                         u8 p_offset,
                         XY_TO_RelCoor* p_XYtoRel)
{
    int l_s32Dist = 0;
    int l_s32DistOld = 0;
    float l_f32MarkRad = 0;
    XY_TO_RelCoor* l_psRelCoor = p_NAV_Mark->m_StructMarkScanInfoAddr.m_sXy2Robot[p_offset];

    p_XYtoRel->m_s32x = l_psRelCoor->m_s32x;
    p_XYtoRel->m_s32y = l_psRelCoor->m_s32y;
    l_s32DistOld = p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[p_offset]->m_u16Dist;
    if (g_sSysPib.m_u16MarkType == MARK_TYPE_CYC)
    {
        if (p_NAV_Mark->m_psSetMarkAddr[p_offset]->m_u8shape != g_sSysPib.m_u16MarkType)
        {
            ROS_ERROR("Corr_Mark_MixLayout1");
            l_s32Dist = p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[p_offset]->m_u16Dist
                        - (g_sSysPib.m_u16MarkRadio >> 1);
            p_XYtoRel->m_s32x = l_s32Dist * l_psRelCoor->m_s32x / l_s32DistOld;
            p_XYtoRel->m_s32y = l_s32Dist * l_psRelCoor->m_s32y / l_s32DistOld;
            //			l_RelCoor.m_s32x =
            // l_s32Dist*p_NAV_Mark->m_sXy2Robot[p_offset].m_s32x/l_s32DistOld;
            // l_RelCoor.m_s32y = l_s32Dist*p_NAV_Mark->m_sXy2Robot[p_offset].m_s32y/l_s32DistOld;
        }
        else if (p_NAV_Mark->m_psSetMarkAddr[p_offset]->m_u8size != g_sSysPib.m_u16MarkRadio)
        {
            ROS_ERROR("Corr_Mark_MixLayout2");
            l_s32Dist = p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[p_offset]->m_u16Dist
                        + (p_NAV_Mark->m_psSetMarkAddr[p_offset]->m_u8size >> 1)
                        - (g_sSysPib.m_u16MarkRadio >> 1);
            p_XYtoRel->m_s32x = l_s32Dist * l_psRelCoor->m_s32x / l_s32DistOld;
            p_XYtoRel->m_s32y = l_s32Dist * l_psRelCoor->m_s32y / l_s32DistOld;
            //			l_RelCoor.m_s32x =
            // l_s32Dist*p_NAV_Mark->m_sXy2Robot[p_offset].m_s32x/l_s32DistOld;
            // l_RelCoor.m_s32y = l_s32Dist*p_NAV_Mark->m_sXy2Robot[p_offset].m_s32y/l_s32DistOld;
        }
    }
    else
    {
        if (p_NAV_Mark->m_psSetMarkAddr[p_offset]->m_u8shape != g_sSysPib.m_u16MarkType)
        {
            ROS_ERROR("Corr_Mark_MixLayout3");
            l_s32Dist = p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[p_offset]->m_u16Dist
                        + (p_NAV_Mark->m_psSetMarkAddr[p_offset]->m_u8size >> 1);
            p_XYtoRel->m_s32x = l_s32Dist * l_psRelCoor->m_s32x / l_s32DistOld;
            p_XYtoRel->m_s32y = l_s32Dist * l_psRelCoor->m_s32y / l_s32DistOld;
            //			l_RelCoor.m_s32x =
            // l_s32Dist*p_NAV_Mark->m_sXy2Robot[p_offset].m_s32x/l_s32DistOld;
            // l_RelCoor.m_s32y = l_s32Dist*p_NAV_Mark->m_sXy2Robot[p_offset].m_s32y/l_s32DistOld;
        }
    }
}

void Cal_LaserPos_WeightAver(ROBOT_XY* p_LaserPos, u32 p_WeightSum)
{
    u8 l_u8i = 0;
    double k = 0;
    p_LaserPos->m_64sumx = 0;
    p_LaserPos->m_64sumy = 0;
    float sumAng = 0;
    if (g_sComb.m_u16size != 1)
    {
        for (l_u8i = 0; l_u8i < g_sComb.m_u16size; l_u8i++)
        {
            // k = 1 - ((double)g_sComb.m_u32val[l_u8i] / (double)p_WeightSum);
            k = 1;
            p_LaserPos->m_64sumx += (double)p_LaserPos->m_s32matixx[l_u8i] * k;
            p_LaserPos->m_64sumy += (double)p_LaserPos->m_s32matixy[l_u8i] * k;
            // ROS_INFO("xx=%d,xy=%d,k=%.3f",p_LaserPos->m_s32matixx[l_u8i],p_LaserPos->m_s32matixy[l_u8i],k);
        }
        // p_LaserPos->m_Struct_LaserPos.m_s32x = p_LaserPos->m_64sumx / (g_sComb.m_u16size - 1);
        // p_LaserPos->m_Struct_LaserPos.m_s32y = p_LaserPos->m_64sumy / (g_sComb.m_u16size - 1);
        p_LaserPos->m_Struct_LaserPos.m_s32x = p_LaserPos->m_64sumx / g_sComb.m_u16size;
        p_LaserPos->m_Struct_LaserPos.m_s32y = p_LaserPos->m_64sumy / g_sComb.m_u16size;
    }
    // ROS_INFO("end,x=%d,y=%d",p_LaserPos->m_Struct_LaserPos.m_s32x,p_LaserPos->m_Struct_LaserPos.m_s32y);
}

void Get_LaserPos_WeightAver(STRUCT_FILTER_TARGET_LITE* p_Nav_Mark,
                             STRUCT_FPGAPOS* p_FpgaBuf,
                             ROBOT_XY* p_LaserPos,
                             u8 p_NavMarkNum)
{
    u8 l_u8i = 0;
    u8* l_u8TriBufOffset = NULL;
    u32 l_u32ValSum = 0;
    for (l_u8i = 0; l_u8i < g_sComb.m_u16size; l_u8i++)
    {
        l_u8TriBufOffset = &g_sComb.m_u8buf[l_u8i][0];
        //将数据发给fpga 计算
        Cal_Laser_Pose_ByFpga(
            p_Nav_Mark, p_FpgaBuf, l_u8TriBufOffset, l_u8i, p_LaserPos, p_NavMarkNum);
        l_u32ValSum += g_sComb.m_u32val[l_u8i];
    }

    Cal_LaserPos_WeightAver(p_LaserPos, l_u32ValSum);
    g_sSavePosCur.m_Struct_LaserPos.m_u16ang =
        Cal_Laser_AbsAng(p_Nav_Mark, p_Nav_Mark->m_StructMarkScanInfoAddr.m_u8In, p_LaserPos);
    // ROS_INFO("AbsAng = %d",g_sSavePosCur.m_Struct_LaserPos.m_u16ang);
    // g_sSavePosCur.m_Struct_LaserPos.m_u16ang = (u16) (ang / (float)g_sComb.m_u16size);
    // ROS_INFO("Ang = %d",g_sSavePosCur.m_Struct_LaserPos.m_u16ang);
}
//由fpga计算位置
int Tran_To_Float(STRUCT_FPGAPOS* p_psFpgaPos, u8 num)
{
    u8 l_u8i, l_u8j, l_u8size;
    float l_f32rad[3] = {0.0};
    float l_f32tmp, l_f32tmp1, l_f32tmp2;
    int l_s32x, l_s32y;
    u16 l_u16no, l_u16ang;
    u32 l_u32dist;

    l_f32tmp = (float)p_psFpgaPos->m_psSetMarkAddr[0]->m_s32x;
    memcpy((void*)&p_psFpgaPos->m_s32x1, (void*)&l_f32tmp, 4);
    l_f32tmp = (float)p_psFpgaPos->m_psSetMarkAddr[0]->m_s32y;
    memcpy((void*)&p_psFpgaPos->m_s32y1, (void*)&l_f32tmp, 4);
    l_f32tmp = (float)p_psFpgaPos->m_psSetMarkAddr[1]->m_s32x;
    memcpy((void*)&p_psFpgaPos->m_s32x2, (void*)&l_f32tmp, 4);
    l_f32tmp = (float)p_psFpgaPos->m_psSetMarkAddr[1]->m_s32y;
    memcpy((void*)&p_psFpgaPos->m_s32y2, (void*)&l_f32tmp, 4);

    if (num > 2)
    {
        l_f32tmp = (float)p_psFpgaPos->m_psSetMarkAddr[2]->m_s32x;
        memcpy((void*)&p_psFpgaPos->m_s32x3, (void*)&l_f32tmp, 4);
        l_f32tmp = (float)p_psFpgaPos->m_psSetMarkAddr[2]->m_s32y;
        memcpy((void*)&p_psFpgaPos->m_s32y3, (void*)&l_f32tmp, 4);
    }

    l_f32tmp = (float)p_psFpgaPos->m_psXy2Robot[0].m_s32x;
    memcpy((void*)&p_psFpgaPos->m_s32Rx1, (void*)&l_f32tmp, 4);

    l_f32tmp = (float)p_psFpgaPos->m_psXy2Robot[0].m_s32y;
    memcpy((void*)&p_psFpgaPos->m_s32Ry1, (void*)&l_f32tmp, 4);

    l_f32tmp = (float)p_psFpgaPos->m_psXy2Robot[1].m_s32x;
    memcpy((void*)&p_psFpgaPos->m_s32Rx2, (void*)&l_f32tmp, 4);

    l_f32tmp = (float)p_psFpgaPos->m_psXy2Robot[1].m_s32y;
    memcpy((void*)&p_psFpgaPos->m_s32Ry2, (void*)&l_f32tmp, 4);
    if (num > 2)
    {
        l_f32tmp = (float)p_psFpgaPos->m_psXy2Robot[2].m_s32x;
        memcpy((void*)&p_psFpgaPos->m_s32Rx3, (void*)&l_f32tmp, 4);

        l_f32tmp = (float)p_psFpgaPos->m_psXy2Robot[2].m_s32y;
        memcpy((void*)&p_psFpgaPos->m_s32Ry3, (void*)&l_f32tmp, 4);
    }

    return 0;
}

//返回3个点的坐标

//从指定的距离范围搜索路标个数
u32 SortMarkDist(STRUCT_FILTER_TARGET_LITE* pDist, u32 dist_min, u32 dist_max, u16* max_pos)
{
    u16 l_u16i, l_u16j, l_u16cnt;
    u16 l_u16size;
    u16 l_u16maxpos = 0;
    //	u8 *l_u8valid = pDist->m_u8IsMark ;
    l_u16size = pDist->m_StructMarkScanInfoAddr.m_u8In;
    l_u16cnt = 0;

    // u32 *l_unp = pDist->m_u32Dist ;
    for (l_u16i = 0; l_u16i < l_u16size; l_u16i++)
    {
        if ((pDist->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u16i]->m_u16Dist >= dist_min)
            && (pDist->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u16i]->m_u16Dist <= dist_max)
            && (pDist->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u16i]->m_u8IsMark == ISMARK))
        {
            l_u16cnt++;
            //记录下最后一个满足条件的i值
            l_u16maxpos = l_u16i;
        }
    }
    *max_pos = l_u16maxpos;
    return l_u16cnt;
}

//识别靶标
//对路标按照距离最优,夹角尽可能的均匀来定
u8 SelMarkByDist(STRUCT_FILTER_TARGET_LITE* p_Scan_Mark, STRUCT_FILTER_TARGET_LITE* p_Nav_Mark)
{
    u8 l_u8Prio_Select_MarkNum = 0;
    u8 l_u8CopyNum = 0;
    u8 l_u8ret = 0;
    STRUCT_MARK_INFO** l_psMarkInfoAddr_Scan =
        p_Scan_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo;
    XY_TO_RelCoor** l_psXYRob_Scan = p_Scan_Mark->m_StructMarkScanInfoAddr.m_sXy2Robot;
    MARK_XY** l_psSetMarkAddr_Scan = p_Scan_Mark->m_psSetMarkAddr;
    STRUCT_MARK_INFO** l_psMarkInfoAddr_Nav = p_Nav_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo;
    XY_TO_RelCoor** l_psXYRob_Nav = p_Nav_Mark->m_StructMarkScanInfoAddr.m_sXy2Robot;
    MARK_XY** l_psSetMarkAddr_Nav = p_Nav_Mark->m_psSetMarkAddr;
    // 2. 按照距离排序
    l_u8Prio_Select_MarkNum = Sort_From_Min_TO_Max(p_Scan_Mark, 1);
    if (l_u8Prio_Select_MarkNum >= 5)  // 15米内有大于5个靶
    {
        if (l_u8Prio_Select_MarkNum <= COM_BUF_MAX)
            l_u8CopyNum = l_u8Prio_Select_MarkNum;
        else
            l_u8CopyNum = COM_BUF_MAX;  //最多只取COM_BUF_MAX个靶标
        l_u8ret = 5;
    }
    else if (p_Scan_Mark->m_StructMarkScanInfoAddr.m_u8In >= 5)  //总扫描靶数>5个,但15米内不足5个
    {
        l_u8CopyNum = 5;
        l_u8ret = 5;
    }
    else  //扫描不足5个
    {
        l_u8CopyNum = p_Scan_Mark->m_StructMarkScanInfoAddr.m_u8In;
        if (l_u8CopyNum >= 3)
            l_u8ret = l_u8CopyNum;
        else
            l_u8ret = 2;
    }

    // memcpy(&p_Nav_Mark->m_sMark[0],&p_Scan_Mark->m_sMark[0], STRUCT_SIZE_MARK_XY * l_u8CopyNum);
    for (u8 i = 0; i < l_u8CopyNum; i++)
    {
        l_psSetMarkAddr_Nav[i] = l_psSetMarkAddr_Scan[i];
        l_psMarkInfoAddr_Nav[i] = l_psMarkInfoAddr_Scan[i];
        l_psXYRob_Nav[i] = l_psXYRob_Scan[i];
    }
    // memcpy(&p_Nav_Mark->m_sMark[0],&p_Scan_Mark->m_sMark[0], STRUCT_SIZE_MARK_XY * l_u8CopyNum);
    //	memcpy(&p_Nav_Mark->m_sXy2Robot[0],&p_Scan_Mark->m_sXy2Robot[0], STRUCT_SIZE_MARK_XY *
    // l_u8CopyNum );
    //	memcpy(&p_Nav_Mark->m_StructMarkScanInfo.m_StructMarkInfo[0],&p_Scan_Mark->m_StructMarkScanInfo.m_StructMarkInfo[0],
    // STRUCT_SIZE_MARK_INFO * l_u8CopyNum );

    p_Nav_Mark->m_StructMarkScanInfoAddr.m_u8In = l_u8CopyNum;

    return l_u8ret;
}

int SortMarkByAng(STRUCT_FILTER_TARGET_LITE* p_Scan_Mark)
{
    // 2. 按照距离排序
    Sort_From_Min_TO_Max(p_Scan_Mark, 0);

    return 0;
}

u8 Sort_From_Min_TO_Max(STRUCT_FILTER_TARGET_LITE* p_Scan_Mark,
                        u8 p_Dist_Ang_Flag)  // 1是距离，0是ang
{
    u8 l_u8i, l_u8j;
    u8 l_u8size = p_Scan_Mark->m_StructMarkScanInfoAddr.m_u8In;
    u8 l_u8Cnt = 0;

    STRUCT_MARK_INFO* l_sMarkInfo;
    MARK_XY*
        l_psMarkAddr;  //这个疑似目标指向的路标的坐标,如果不是则是扫描到的新路标的在环境坐标的位置
    XY_TO_RelCoor* l_psXy2Robot;
    STRUCT_MARK_INFO** l_psMarkInfoAddr = p_Scan_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo;
    XY_TO_RelCoor** l_psXYRob = p_Scan_Mark->m_StructMarkScanInfoAddr.m_sXy2Robot;
    MARK_XY** l_psSetMarkAddr = p_Scan_Mark->m_psSetMarkAddr;
    if (p_Dist_Ang_Flag)
    {
        for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
        {
            if (l_psMarkInfoAddr[l_u8i]->m_u16Dist < PRIO_SELECT_DIST)  //看看15mi内有几个靶标
            {
                l_u8Cnt++;
            }
            for (l_u8j = 0; l_u8j < l_u8size - l_u8i - 1; l_u8j++)
            {
                if ((l_psMarkInfoAddr[l_u8j]->m_u16Dist) > (l_psMarkInfoAddr[l_u8j + 1]->m_u16Dist))
                {
                    //				memcpy(&l_sMarkInfo,&p_Scan_Mark->m_StructMarkScanInfo.m_StructMarkInfo[l_u8j],STRUCT_SIZE_MARK_INFO);
                    //				memcpy(&p_Scan_Mark->m_StructMarkScanInfo.m_StructMarkInfo[l_u8j],&p_Scan_Mark->m_StructMarkScanInfo.m_StructMarkInfo[l_u8j
                    //+ 1],STRUCT_SIZE_MARK_INFO);
                    //				memcpy(&p_Scan_Mark->m_StructMarkScanInfo.m_StructMarkInfo[l_u8j
                    //+ 1],&l_sMarkInfo,STRUCT_SIZE_MARK_INFO);
                    l_sMarkInfo = l_psMarkInfoAddr[l_u8j];
                    l_psMarkInfoAddr[l_u8j] = l_psMarkInfoAddr[l_u8j + 1];
                    l_psMarkInfoAddr[l_u8j + 1] = l_sMarkInfo;

                    l_psMarkAddr = l_psSetMarkAddr[l_u8j];
                    l_psSetMarkAddr[l_u8j] = l_psSetMarkAddr[l_u8j + 1];
                    l_psSetMarkAddr[l_u8j + 1] = l_psMarkAddr;
                    //				memcpy(&l_sMark,&p_Scan_Mark->m_sMark[l_u8j],STRUCT_SIZE_MARK_XY);
                    //				memcpy(&p_Scan_Mark->m_sMark[l_u8j], &p_Scan_Mark->m_sMark[l_u8j
                    //+ 1],
                    // STRUCT_SIZE_MARK_XY); 				memcpy(&p_Scan_Mark->m_sMark[l_u8j + 1],
                    // &l_sMark, STRUCT_SIZE_MARK_XY);

                    l_psXy2Robot = l_psXYRob[l_u8j];
                    l_psXYRob[l_u8j] = l_psXYRob[l_u8j + 1];
                    l_psXYRob[l_u8j + 1] = l_psXy2Robot;
                    //					memcpy(&l_sXy2Robot,&p_Scan_Mark->m_sXy2Robot[l_u8j],STRUCT_SIZE_XY_TO_RelCoor);
                    //					memcpy(&p_Scan_Mark->m_sXy2Robot[l_u8j],
                    //&p_Scan_Mark->m_sXy2Robot[l_u8j + 1], STRUCT_SIZE_XY_TO_RelCoor);
                    //					memcpy(&p_Scan_Mark->m_sXy2Robot[l_u8j + 1], &l_sXy2Robot,
                    // STRUCT_SIZE_XY_TO_RelCoor);
                }
            }
        }
    }
    else
    {
        for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
        {
            //			if(p_Scan_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[l_u8i]->m_u16Dist
            //< PRIO_SELECT_DIST)	//看看15mi内有几个靶标
            //			{
            //				l_u8Cnt ++;
            //			}
            for (l_u8j = 0; l_u8j < l_u8size - l_u8i - 1; l_u8j++)
            {
                if ((l_psMarkInfoAddr[l_u8j]->m_u16Ang) > (l_psMarkInfoAddr[l_u8j + 1]->m_u16Ang))
                {
                    //				memcpy(&l_sMarkInfo,&p_Scan_Mark->m_StructMarkScanInfo.m_StructMarkInfo[l_u8j],STRUCT_SIZE_MARK_INFO);
                    //				memcpy(&p_Scan_Mark->m_StructMarkScanInfo.m_StructMarkInfo[l_u8j],&p_Scan_Mark->m_StructMarkScanInfo.m_StructMarkInfo[l_u8j
                    //+ 1],STRUCT_SIZE_MARK_INFO);
                    //				memcpy(&p_Scan_Mark->m_StructMarkScanInfo.m_StructMarkInfo[l_u8j
                    //+ 1],&l_sMarkInfo,STRUCT_SIZE_MARK_INFO);
                    l_sMarkInfo = l_psMarkInfoAddr[l_u8j];
                    l_psMarkInfoAddr[l_u8j] = l_psMarkInfoAddr[l_u8j + 1];
                    l_psMarkInfoAddr[l_u8j + 1] = l_sMarkInfo;

                    l_psMarkAddr = l_psSetMarkAddr[l_u8j];
                    l_psSetMarkAddr[l_u8j] = l_psSetMarkAddr[l_u8j + 1];
                    l_psSetMarkAddr[l_u8j + 1] = l_psMarkAddr;
                    //				memcpy(&l_sMark,&p_Scan_Mark->m_sMark[l_u8j],STRUCT_SIZE_MARK_XY);
                    //				memcpy(&p_Scan_Mark->m_sMark[l_u8j], &p_Scan_Mark->m_sMark[l_u8j
                    //+ 1],
                    // STRUCT_SIZE_MARK_XY); 				memcpy(&p_Scan_Mark->m_sMark[l_u8j + 1],
                    // &l_sMark, STRUCT_SIZE_MARK_XY);

                    l_psXy2Robot = l_psXYRob[l_u8j];
                    l_psXYRob[l_u8j] = l_psXYRob[l_u8j + 1];
                    l_psXYRob[l_u8j + 1] = l_psXy2Robot;
                    //					memcpy(&l_sXy2Robot,&p_Scan_Mark->m_sXy2Robot[l_u8j],STRUCT_SIZE_XY_TO_RelCoor);
                    //					memcpy(&p_Scan_Mark->m_sXy2Robot[l_u8j],
                    //&p_Scan_Mark->m_sXy2Robot[l_u8j + 1], STRUCT_SIZE_XY_TO_RelCoor);
                    //					memcpy(&p_Scan_Mark->m_sXy2Robot[l_u8j + 1], &l_sXy2Robot,
                    // STRUCT_SIZE_XY_TO_RelCoor);
                }
            }
        }
    }

    return l_u8Cnt;
}
// int SelMarkByDistTri(STRUCT_FILTER_TARGET_LITE *pDist, STRUCT_FILTER_TARGET_LITE *pDist2,u32
// MinDist,u8 MarkNum)

//{
//	u16 l_u16i, l_u16j ;
//	u32 l_u32Tmp , l_u32Max, l_u32Min ;
//	u16 l_u16Tmp , l_u16Tmp2 ;
//	u8 l_u8size ,l_u8tmp;
//	u16 l_u16MaxPos ;
//	u16 l_u16off ;
//	int l_s32tmp ;
//	MARK_XY *l_psMark ;
//	XY_TO_RelCoor *l_psRob ;
//	//u8 *l_pu8IsMark ;
//	u8 l_u8cnt = 0 ;
//
//	float l_f32rad , l_f32tmp ;

//	//u32 *l_unpDist = NULL ;
//	//u16 *l_unpPos = NULL ;
//
//	l_u8size = pDist->m_StructMarkScanInfo.m_u8In ;
//
////	l_unpDist = pDist2->m_u32Dist ;
////	l_unpPos = pDist2->m_u16Ang ;
//	l_psMark = pDist2->m_sMark ;
//	l_psRob = pDist2->m_sXy2Robot ;
////	l_pu8IsMark = pDist2->m_u8IsMark ;
//
//
//
//	if((l_u8size < 3) || (l_u8size > TARGET_MAX))
//		return -1;
//
//	//1. 先过滤掉不是有效的靶标
//	for (l_u16i = 0; l_u16i < l_u8size; l_u16i++)
//  {
//		 if(pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u16i].m_u8IsMark == 1) //有效的靶标
//		 {
//			 pDist2->m_sMark[l_u8cnt].m_s32x =  pDist->m_sMark[l_u16i].m_s32x ;
//			 pDist2->m_sMark[l_u8cnt].m_s32y =  pDist->m_sMark[l_u16i].m_s32y ;
//			 pDist2->m_sMark[l_u8cnt].m_u32no =  pDist->m_sMark[l_u16i].m_u32no ;
//			 pDist2->m_sXy2Robot[l_u8cnt].m_s32x = pDist->m_sXy2Robot[l_u16i].m_s32x ;
//			 pDist2->m_sXy2Robot[l_u8cnt].m_s32y = pDist->m_sXy2Robot[l_u16i].m_s32y ;
//			 pDist2->m_StructMarkScanInfo.m_StructMarkInfo[l_u8cnt].m_u16Ang =
// pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u16i].m_u16Ang;
//			 pDist2->m_StructMarkScanInfo.m_StructMarkInfo[l_u8cnt].m_u16Dist =
// pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u16i].m_u16Dist ;
//			 pDist2->m_StructMarkScanInfo.m_StructMarkInfo[l_u8cnt].m_u8IsMark =
// pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u16i].m_u8IsMark ; 			 l_u8cnt++ ;
//		 }
//	}
//	pDist2->m_StructMarkScanInfo.m_u8In = l_u8cnt ;
//	if(l_u8cnt < 3)
//		return -1 ;
//
//
//	l_u8size = l_u8cnt ;
//
//	//2. 按照距离排序
//      for (l_u16i = 0; l_u16i < l_u8size; l_u16i++)
//      {
//            for (l_u16j = 0; l_u16j < l_u8size - l_u16i -1; l_u16j++)
//            if (pDist2->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j].m_u16Dist >
//            pDist2->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j+1].m_u16Dist )
//            {
//				//copy x
//				l_u32Tmp = pDist2->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j].m_u16Dist;
//				pDist2->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j].m_u16Dist =
// pDist2->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j+1].m_u16Dist;
//				pDist2->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j+1].m_u16Dist = l_u32Tmp;
//				//copy y
//				l_u16Tmp = pDist2->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j].m_u16Ang;
//				pDist2->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j].m_u16Ang =
// pDist2->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j+1].m_u16Ang;
//				pDist2->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j+1].m_u16Ang = l_u16Tmp;

//				//不要掉了识别到的靶标信息
//				l_s32tmp = (l_psMark+l_u16j)->m_s32x ;
//				(l_psMark+l_u16j)->m_s32x = (l_psMark+l_u16j+1)->m_s32x ;
//				(l_psMark+l_u16j+1)->m_s32x   = l_s32tmp ;

//				l_s32tmp = (l_psMark+l_u16j)->m_s32y ;
//				(l_psMark+l_u16j)->m_s32y = (l_psMark+l_u16j+1)->m_s32y ;
//				(l_psMark+l_u16j+1)->m_s32y   = l_s32tmp ;

//				l_u32Tmp = (l_psMark+l_u16j)->m_u32no ;
//				(l_psMark+l_u16j)->m_u32no = (l_psMark+l_u16j+1)->m_u32no ;
//				(l_psMark+l_u16j+1)->m_u32no   = l_u32Tmp ;

//				//也交换相对激光器的坐标
//				l_s32tmp = (l_psRob+l_u16j)->m_s32x ;
//				(l_psRob+l_u16j)->m_s32x = (l_psRob+l_u16j+1)->m_s32x ;
//				(l_psRob+l_u16j+1)->m_s32x   = l_s32tmp ;

//				l_s32tmp = (l_psRob+l_u16j)->m_s32y ;
//				(l_psRob+l_u16j)->m_s32y = (l_psRob+l_u16j+1)->m_s32y ;
//				(l_psRob+l_u16j+1)->m_s32y   = l_s32tmp ;

//				//交换靶标有效标志
//				l_u8tmp = pDist2->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j].m_u8IsMark;
//				pDist2->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j].m_u8IsMark =
// pDist2->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j+1].m_u8IsMark ;
//				pDist2->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j+1].m_u8IsMark  = l_u8tmp ;
//
//            }
//
//      }

//   //扫描到的路标的最小,最大值
//	if(l_u8size > 3)
//	{
//		 //设定起始的搜索距离
//		l_u32Min = MinDist/UNITE ;
//		l_u32Max = 15000/UNITE ;
//		for(l_u32Max = 15000/UNITE ; l_u32Max < 65000/UNITE; l_u32Max+=(5000/UNITE))
//		{
//			l_u16Tmp = SortMarkDist(pDist2, l_u32Min, l_u32Max, &l_u16MaxPos) ;
//			if(l_u16Tmp >= MarkNum)
//				break ;
//		//else if(l_u16Tmp < 3)
//		//	return -1 ; //没有找到合适的路标
//		}

//		if(l_u16Tmp < 3)
//			return -1 ;

//		//排序之后, 从l_u16off开始是符合最小起始距离的靶标
//		l_u16off = l_u16MaxPos - (l_u16Tmp - 1) ;

//		if(l_u16off > 0)
//		{
//			//将符合条件的路标提取出来, 放到一个数组里面
//			for(l_u16i = 0; l_u16i < l_u16Tmp ;l_u16i++)
//			{
//				pDist2->m_sMark[l_u16i].m_s32x =  pDist2->m_sMark[l_u16i+l_u16off].m_s32x ;
//				pDist2->m_sMark[l_u16i].m_s32y =  pDist2->m_sMark[l_u16i+l_u16off].m_s32y ;
//				pDist2->m_sMark[l_u16i].m_u32no =  pDist2->m_sMark[l_u16i+l_u16off].m_u32no ;
//				pDist2->m_sXy2Robot[l_u16i].m_s32x = pDist2->m_sXy2Robot[l_u16i+l_u16off].m_s32x ;
//				pDist2->m_sXy2Robot[l_u16i].m_s32y = pDist2->m_sXy2Robot[l_u16i+l_u16off].m_s32y ;
//				pDist2->m_StructMarkScanInfo.m_StructMarkInfo[l_u16i].m_u16Ang =
// pDist2->m_StructMarkScanInfo.m_StructMarkInfo[l_u16i+l_u16off].m_u16Ang ;
//				pDist2->m_StructMarkScanInfo.m_StructMarkInfo[l_u16i].m_u16Dist =
// pDist2->m_StructMarkScanInfo.m_StructMarkInfo[l_u16i+l_u16off].m_u16Dist ;
//				pDist2->m_StructMarkScanInfo.m_StructMarkInfo[l_u16i].m_u8IsMark =
// pDist2->m_StructMarkScanInfo.m_StructMarkInfo[l_u16i+l_u16off].m_u8IsMark ;
//			}
//		}

//		pDist2->m_StructMarkScanInfo.m_u8In = l_u16Tmp ;

//	}

//		return 0 ;
//}

//从指定的距离范围搜索路标个数
// u32 SortMarkAng(STRUCT_FILTER_TARGET_LITE *pDist, u16 offset, u8 size)
//{
//	u16 l_u16i, l_u16j ,l_u16cnt ;
//	u16 l_u16Tmp ;
//	u32 l_u32Tmp ;
//	int l_s32tmp ;
//	u8 l_u8tmp ;
//	MARK_XY *l_psMark ;
//	XY_TO_RelCoor *l_psRob ;
////	u8 *l_pu8Valid ;
//
//	if(size > TARGET_MAX)
//		return -1 ;
//
//	//u32 *l_unpDist = pDist->m_u32Dist + offset ;
//	//u16 *l_unpPos = pDist->m_u16Ang + offset;
//	l_psMark = pDist->m_sMark+ offset ;
//	l_psRob = pDist->m_sXy2Robot + offset ;
//	//l_pu8Valid = pDist->m_u8IsMark+ offset ;
//	for (l_u16i = 0; l_u16i < size; l_u16i++)
//    {
//		for (l_u16j = 0; l_u16j < size - l_u16i -1; l_u16j++)
//		{
//			if (pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j+offset].m_u16Ang>
// pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j+offset+1].m_u16Ang)
//			{
//				//copy pos
//				l_u16Tmp = pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j+offset].m_u16Ang;
//				pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j+offset].m_u16Ang =
// pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j+offset+1].m_u16Ang;
//				pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j+offset+1].m_u16Ang = l_u16Tmp;
//				//copy dist
//				l_u32Tmp = pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j+offset].m_u16Dist;
//				pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j+offset].m_u16Dist =
// pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j+offset+1].m_u16Dist;
//				pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j+offset+1].m_u16Dist = l_u32Tmp;

//				//不要掉了识别到的靶标信息
//				l_s32tmp = (l_psMark+l_u16j)->m_s32x ;
//				(l_psMark+l_u16j)->m_s32x = (l_psMark+l_u16j+1)->m_s32x ;
//				(l_psMark+l_u16j+1)->m_s32x   = l_s32tmp ;

//				l_s32tmp = (l_psMark+l_u16j)->m_s32y ;
//				(l_psMark+l_u16j)->m_s32y = (l_psMark+l_u16j+1)->m_s32y ;
//				(l_psMark+l_u16j+1)->m_s32y   = l_s32tmp ;

//				l_u32Tmp = (l_psMark+l_u16j)->m_u32no ;
//				(l_psMark+l_u16j)->m_u32no = (l_psMark+l_u16j+1)->m_u32no ;
//				(l_psMark+l_u16j+1)->m_u32no   = l_u32Tmp ;

//				//也交换相对激光器的坐标
//				l_s32tmp = (l_psRob+l_u16j)->m_s32x ;
//				(l_psRob+l_u16j)->m_s32x = (l_psRob+l_u16j+1)->m_s32x ;
//				(l_psRob+l_u16j+1)->m_s32x   = l_s32tmp ;

//				l_s32tmp = (l_psRob+l_u16j)->m_s32y ;
//				(l_psRob+l_u16j)->m_s32y = (l_psRob+l_u16j+1)->m_s32y ;
//				(l_psRob+l_u16j+1)->m_s32y   = l_s32tmp ;

//				//交换靶标有效标志
//				l_u8tmp = pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j+offset].m_u8IsMark;
//				pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j+offset].m_u8IsMark =
// pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j+offset+1].m_u8IsMark ;
//				pDist->m_StructMarkScanInfo.m_StructMarkInfo[l_u16j+offset+1].m_u8IsMark  = l_u8tmp
//;

//			}
//		}
//    }
//	return 0 ;
//}

u32 SortXY_X(ROBOT_XY* probot, u16 offset, u8 size)
{
    u16 l_u16i, l_u16j, l_u16cnt;
    int l_s32Tmp;
    u32 l_u32Tmp;
    u8 l_u8tmp;
    MARK_XY* l_psMark;
    XY_TO_RelCoor* l_psRob;
    u8* l_pu8Valid;

    if (size > TARGET_MAX)
        return -1;

    int* l_s32x = probot->m_s32matixx + offset;
    int* l_s32y = probot->m_s32matixy + offset;

    for (l_u16i = 0; l_u16i < size; l_u16i++)
    {
        for (l_u16j = 0; l_u16j < size - l_u16i - 1; l_u16j++)
        {
            if (*(l_s32x + l_u16j) > *(l_s32x + l_u16j + 1))
            {
                // copy x
                l_s32Tmp = *(l_s32x + l_u16j);
                *(l_s32x + l_u16j) = *(l_s32x + l_u16j + 1);
                *(l_s32x + l_u16j + 1) = l_s32Tmp;

                // copy y
                l_s32Tmp = *(l_s32y + l_u16j);
                *(l_s32y + l_u16j) = *(l_s32y + l_u16j + 1);
                *(l_s32y + l_u16j + 1) = l_s32Tmp;
            }
        }
    }

    return 0;
}

u32 SortXY_Y(ROBOT_XY* probot, u16 offset, u8 size)
{
    u16 l_u16i, l_u16j, l_u16cnt;
    int l_s32Tmp;
    u32 l_u32Tmp;
    u8 l_u8tmp;
    MARK_XY* l_psMark;
    XY_TO_RelCoor* l_psRob;
    u8* l_pu8Valid;

    if (size > TARGET_MAX)
        return -1;

    int* l_s32x = probot->m_s32matixx + offset;
    int* l_s32y = probot->m_s32matixy + offset;

    for (l_u16i = 0; l_u16i < size; l_u16i++)
    {
        for (l_u16j = 0; l_u16j < size - l_u16i - 1; l_u16j++)
        {
            if (*(l_s32y + l_u16j) > *(l_s32y + l_u16j + 1))
            {
                // copy x
                l_s32Tmp = *(l_s32x + l_u16j);
                *(l_s32x + l_u16j) = *(l_s32x + l_u16j + 1);
                *(l_s32x + l_u16j + 1) = l_s32Tmp;

                // copy y
                l_s32Tmp = *(l_s32y + l_u16j);
                *(l_s32y + l_u16j) = *(l_s32y + l_u16j + 1);
                *(l_s32y + l_u16j + 1) = l_s32Tmp;
            }
        }
    }

    return 0;
}

/*****************************************************************************
*************************    提取轮廓点相关   **************************************
*****************************************************************************/

int Filter_Mark_By_Ref(u16* PulseWidth, u16* Dist)
{
    int l_s32ret = -1;
    //根据脉宽+距离来查表
    u32 l_u32addr;
    u16 l_u16tmp, l_u16lvl;
    s16 l_s16tmp;
    u32 l_u32pulse;
    //	if((*Dist >( g_sMarkMatch.m_u32MarkScan_Max+(g_sSysPib.m_u16MarkRadio>>1)) )||(*Dist <
    // g_sMarkMatch.m_u32MarkScan_Min))//如果是圆靶,边缘值会测的值稍大 		return l_s32ret ;
    if ((*PulseWidth > (700000 >> 4)) || (*PulseWidth < (1000 >> 4)))
        return l_s32ret;

    l_u16lvl = g_sSysPib.m_u16MarkDistinguish - g_sSysPib.m_u16MarkReflectivity;
    l_u32pulse = ((u32)(*PulseWidth) << 4);  //还原成32位的脉宽, 因为全部改成16位的了

    l_u32addr = ((*Dist + g_sFactorCorr.m_u16DistSub) / 1000 * 700) + l_u32pulse / 1000
                - 1;  //+32330* LIGHTSPEED /
    // 2000//反射率表是0-70m，间隔1000ps脉宽的表，因此中批脉宽修正表+小批反射率表需要加个固定偏移，由于机器内部硬件延时造成的。
    // Flash_Read_HalfWord(ADDR_FLASH_REFLEX_DATA + l_u32addr*2,1,&l_u16tmp);
    l_s16tmp = (s16)l_u16tmp;
    if (l_s16tmp >= l_u16lvl)
        l_s32ret = 0;

    return l_s32ret;
}

/*************************************************
Function		:	Cal_MarkDot_ByDist_Theory
Description		:	根据当前的距离,来获取最低的打在靶标的点数
Calls			:	无
Called By		:	 Filter_FakeMark_ByPointNum
Table Accessed	:	无
Table Updated	:	无
Input			: 	dist:疑似靶标的距离
Output			: 	无
Return			: 	无
Others			:
由于靶的中心点采用斜率的方式获得，所以靶上的点个数与距离有一定关系，但也跟靶与后面背景的反射率有一定的关系
                    所以此函数中通过距离计算靶上点个数的公式不完全按照几何关系来计算。
                    使用公式: 理论点数 = arctan(l_f32MarkRadio/dist)/M_PI*180/0.05*2;
                    简化公式: 理论点数 =  l_f32MarkRadio/dist/M_PI*180/0.05*2 ;
                    防止有些靶标理论点数过大,因此计算公式只取一半点数 : 理论点数 =
l_f32MarkRadio/dist/M_PI*180/0.05, 3600/M_PI = 1145.9 =  靶标直径 /dist * 1145.9
*************************************************/
u16 Cal_MarkDot_ByDist_Theory(u32 p_dist)
{
    u16 l_u16MarkPointNum_Theory = 0;  //靶标理论点数
    u16 l_u16MarkRadio = 0;
    u16 l_u16MarkDotNumPct = g_sSysPib.m_u16MarkDotNumPct;  // 10

    //根据靶标距离和尺寸判断靶上有多少个点
    if (l_u16MarkDotNumPct >= 5)
    {
        l_u16MarkPointNum_Theory =
            g_u32MarkRadio_Coeff / p_dist;  // g_u32MarkRadio_Coeff = l_f32MarkRadio * 1145.9
        if ((p_dist > 20000) && (l_u16MarkPointNum_Theory < 5))
        {
            l_u16MarkPointNum_Theory = 2;
        }
        else
        {
            l_u16MarkPointNum_Theory =
                l_u16MarkPointNum_Theory * l_u16MarkDotNumPct / g_u16MarkDot_Coeff;
        }
    }
    else  //直接粗略的切割
    {
        if (g_sSysPib.m_u16MarkRadio > 70)  //靶标尺寸？ 80mm
        {
            if (p_dist > 7000)
                l_u16MarkPointNum_Theory = 7 >> (g_u8AngMode - 1);
            else
                l_u16MarkPointNum_Theory = 10 >> (g_u8AngMode - 1);
        }
        else
        {
            if (p_dist > 7000)
                l_u16MarkPointNum_Theory = 3 >> (g_u8AngMode - 1);
            else
                l_u16MarkPointNum_Theory = 5 >> (g_u8AngMode - 1);
        }
    }
    return l_u16MarkPointNum_Theory;
}

//将所有符合反光面判决依据的点,全部存下来
void GetOutlineOfMarks(u16* pResults_t,
                       u16* pResults_d,
                       u16 size,
                       u8 no,
                       u8 ang_mode,
                       u16* p_u16pluse_cut)
{
    /**********GetOutlineOfMarks(&g_au16PulseWidth[0],
                                                            &g_as16DistCorr[0],
                                                            DOT_NUM,
                                                            l_u32CntZero-1,
                                                            g_u8AngMode)
       ;***************************************/
    u16 l_u16i;
    u16* l_punTime;
    u16* l_punDist;
    l_punTime = pResults_t;
    l_punDist = pResults_d;
    if (no > 11)
        return;
    for (l_u16i = 0; l_u16i < DOT_NUM; l_u16i++)
    {
        if ((!Filter_Mark_By_Ref(l_punTime + l_u16i, l_punDist + l_u16i))
            && (*(pResults_d + l_u16i) > 10))
        {
            *(p_u16pluse_cut + l_u16i) = *(pResults_t + l_u16i);
            //应该在此处记录对应的码盘刻度值 CodeNum=no*600+l_u16i;
            //符合条件的靶标轮廓点
            //符合条件的脉宽不清空
        }
        else
        {
            //非亮面轮廓点清零, 后面做算法
            *(p_u16pluse_cut + l_u16i) = 0;
        }
    }
}

void GetOutlineOfMarks1(u16* pResults_t, u16* pResults_d, u16 p_Offset, STRUCT_CARVE* p_CarveBuf)
{
    static u16 l_u16MarkStaCnt = 0;
    static u16 l_u16MarkEndCnt = 0;
    static u8 l_u8HasMark = 0;
    u16 l_u16i;
    u16* l_punTime;
    u16* l_punDist;
    l_punTime = pResults_t;
    l_punDist = pResults_d;
    static u16 l_u16ZeroNum;
    if (p_Offset == 0)
    {
        l_u8HasMark = 0;
        l_u16MarkStaCnt = 0;
        l_u16MarkEndCnt = 0;
        l_u16ZeroNum = 0;
        memset(p_CarveBuf, 0, STRUCT_SIZE_CARVE);
    }
    for (l_u16i = p_Offset; l_u16i < DOT_NUM + p_Offset; l_u16i++)
    {
        if (l_u16MarkEndCnt < TARGET_MAX)
        {
            if ((!Filter_Mark_By_Ref(l_punTime + l_u16i, l_punDist + l_u16i))
                && (*(pResults_d + l_u16i) > 10))
            {
                //*(p_u16pluse_cut+l_u16i) = *(pResults_t+l_u16i);
                if (l_u8HasMark == 0)
                {
                    l_u8HasMark = 1;
                    p_CarveBuf->m_u16MarkSta[l_u16MarkEndCnt] = l_u16i;
                    l_u16MarkStaCnt++;
                }
                l_u16ZeroNum = 0;
            }
            else
            {
                //*(p_u16pluse_cut+l_u16i) = 0 ;
                l_u16ZeroNum++;
                if ((l_u8HasMark == 1) && (l_u16ZeroNum >= MARK_COUNTIUEZERO_NUM))
                {
                    l_u8HasMark = 0;
                    p_CarveBuf->m_u16MarkEnd[l_u16MarkEndCnt] = l_u16i - l_u16ZeroNum;
                    l_u16MarkEndCnt++;
                }
            }
        }
        else
        {
            l_u8HasMark = 0;
            break;
        }
    }
    if ((l_u16i == g_u16ScanPointNum) && (l_u8HasMark == 1))
    {
        p_CarveBuf->m_u16MarkEnd[l_u16MarkEndCnt] = g_u16ScanPointNum - l_u16ZeroNum - 1;
        l_u16MarkEndCnt++;
    }
    p_CarveBuf->m_u32MarkNum = l_u16MarkEndCnt;
}

void CalEcho(u16* pResults_t, u16* pResults_d, u16 size, u8 no, u8 ang_mode, u16* pResults_echo)
{
    u16 l_u16i;
    u16* l_punTime;
    u16* l_punDist;
    l_punTime = pResults_t;
    l_punDist = pResults_d;
    if (no > 11)
        return;
    for (l_u16i = 0; l_u16i < DOT_NUM; l_u16i++)
    {
        u16 dist_AG = (u16)(((float)*(pResults_d + l_u16i) / 65536) * 1024);
        u16 Width_AG = (u16)((((float)*(pResults_t + l_u16i) / 43750)) * 1024);

        *(pResults_echo + l_u16i) = (u16)(Width_AG / (float)log(dist_AG));
    }
}

//根据速度,将定位值修正至时间戳位置
u32 Trans_SendPos_To_Ts(ROBOT_XY* PosSend, ROBOT_XY* PosCalNew, INPUTSPEED* speed)
{
    u32 l_u32offsettime = 0;
    u32 l_u32Timestamp = 0;
    u32 l_u32scanoverts = 0;
    u32 l_u32tpdiff;
    u8 l_u8ret;
    SPEED l_psSpeed;
    memcpy(PosSend, PosCalNew, STRUCT_SIZE_LASERPOS);
    l_u32Timestamp = 0;  //= Get_TimeStamp();
    l_u32scanoverts = PosCalNew->m_u32timestamp;
    l_u32offsettime = Get_TS_Diff(l_u32Timestamp, l_u32scanoverts);
    //	if(speed->m_u16speedsetflag == 0)
    //		Corr_Speed(speed,l_u32offsettime,&l_psSpeed,g_f32ScanTime_Per);
    //	else
    if (speed->m_u16speedsetflag == 1)
    {
        memcpy(&l_psSpeed, &speed->m_StructSpeed_absolute, sizeof(SPEED));

        LaserPose_CompensationBySLine(&l_psSpeed,
                                      &PosCalNew->m_Struct_LaserPos,
                                      &PosSend->m_Struct_LaserPos,
                                      l_u32offsettime);
        PosSend->m_Struct_LaserPos.m_u16ang =
            LaserPose_CompensationBySAng(speed, &PosCalNew->m_Struct_LaserPos, l_u32offsettime);
    }

    return l_u32Timestamp;
}

u32 Get_TS_Diff(TIMESTAMP p_TS_New, TIMESTAMP p_TS_Old)
{
    u64 l_u64tpdiff;
    if (g_u16SyncMode == SYNCMODE_CYCLE)  //周期出发IO
        l_u64tpdiff = (1 << g_u8SyncCycleBit) - 1;
    else
        l_u64tpdiff = 0xFFFFFFFF;

    return (l_u64tpdiff + p_TS_New - p_TS_Old) % l_u64tpdiff;
}

u32 Get_SendPos(ROBOT_XY* p_CurPos, ROBOT_XY* p_SendPos)
{
    u32 l_u32sendTS = 0;
    if (p_CurPos->m_u16flag != NOACTIVE)
    {
        l_u32sendTS = Trans_SendPos_To_Ts(p_SendPos, p_CurPos, &g_sInSpeedUsed);
    }
    else
    {
        l_u32sendTS = 0;  // Get_TimeStamp();
        memset(p_SendPos, 0, STRUCT_SIZE_LASERPOS);
    }
    // Renew_AbsCoor_XY1(&g_sFilterMark_SpeedCorr.m_sXy2Robot[0] , &p_SendPos->m_Struct_LaserPos
    // ,&g_sMappingXY_old ,g_sFilterMark_SpeedCorr.m_StructMarkScanInfo.m_u8In);
    return l_u32sendTS;
}

//这里是以转完了一圈最后1个点位参考的, 即归一化到最后一个点
//按照速度2m/s, 1圈就是 0.125s ,走过的距离250mm
//那么一圈计算一个位置, 到底是指给0-250mm中间的哪个位置呢?

/*************************************************
Function		:	Calculate_Point2Point_Dist
Description		:	对输入数据进行平方和开方
Calls			:	无
Called By		: 	无
Table Accessed	: 	无
Table Updated	:	无
Input			:	p_Point_1	:	输入参数1
                    p_Point_2	:	输入参数2
Output			:	无
Return			:	无
Others			:	输入绝对->输出相对,输入相对->输出绝对
*************************************************/
u64 Calculate_Point2Point_Dist_Int(int p_Point_1, int p_Point_2)
{
    u64 l_u64Multiply_Point_1 = 0;
    u64 l_u64Multiply_Point_2 = 0;

    l_u64Multiply_Point_1 = p_Point_1 * p_Point_1;
    l_u64Multiply_Point_2 = p_Point_2 * p_Point_2;

    return sqrt(l_u64Multiply_Point_1 + l_u64Multiply_Point_2);
}

float Calculate_Point2Point_Dist_Float(float p_Point_1, float p_Point_2)
{
    float l_f64Multiply_Point_1 = 0;
    float l_f64Multiply_Point_2 = 0;

    l_f64Multiply_Point_1 = p_Point_1 * p_Point_1;
    l_f64Multiply_Point_2 = p_Point_2 * p_Point_2;

    return sqrt(l_f64Multiply_Point_1 + l_f64Multiply_Point_2);
}

/*************************************************
Function		:	Speed_Transfer_To_Coordinates
Description		:	Speed坐标系转换
Calls			:	无
Called By		: 	CalAngOffsetBySpeed360TOHalf
Table Accessed	: 	无
Table Updated	:	无
Input			:	p_Speed_Abs	:	绝对坐标系下Speed首地址
                    robot		:	Lidar位置,即上一圈定位值
                    p_Speed_Rel	:	相对坐标系下Speed首地址
Output			:	无
Return			:	无
Others			:	输入绝对->输出相对,输入相对->输出绝对
*************************************************/
void Speed_Transfer_To_Coordinates(SPEED* p_Speed_Abs, ROBOT_XY* robot, SPEED* p_Speed_Rel)
{
    s8 l_s8SpeedIn_Type = 0;  //输入速度坐标系种类,绝对坐标系 1,相对坐标系 -1
    int l_s32RobAng = 0;      //激光器方位角
    u16 l_u16SpeedAng_to_InCoorX = 0;  //, l_u16SpeedAng_to_OutCoorX = 0;
    float l_f32SpeedRad_to_OutCoorX = 0;
    float l_f32Rad = 0;  //速度与速度坐标系间的夹角
    float l_f32_2PI = M_PI * 2;
    float l_f64Speed = 0;
    SPEED *l_Speed_In, *l_Speed_Out;

    if (g_sInSpeedUsed.m_u16Corrdinate_Type == COORDINATE_YTPE_ABS)  //设置的为绝对坐标系
    {
        l_Speed_In = p_Speed_Abs;
        l_Speed_Out = p_Speed_Rel;
        l_s8SpeedIn_Type = 1;
    }
    else
    {
        l_Speed_In = p_Speed_Rel;
        l_Speed_Out = p_Speed_Abs;
        l_s8SpeedIn_Type = -1;
    }

    //求出小车的运动方向,相对于设置的速度坐标系(设置的为绝对坐标系,则相对于绝对坐标系速度,设置相对坐标系,则相对于激光器坐标系的夹角)
    l_f32Rad = round(atan2(l_Speed_In->m_s16speedy, l_Speed_In->m_s16speedx));

    l_f32Rad = fmodf(l_f32Rad + l_f32_2PI, l_f32_2PI);  // 取余,atan2结果范围[-M_PI,M_PI]->[0,2pi]
    // l_u16SpeedAng_to_InCoorX = l_f32Rad / g_f32Per10mDeg_Rad;	//转换成10mdeg

    l_f64Speed = Calculate_Point2Point_Dist_Float(l_Speed_In->m_s16speedx,
                                                  l_Speed_In->m_s16speedy);  //求取速度的标量

    l_s32RobAng = robot->m_Struct_LaserPos.m_u16ang * l_s8SpeedIn_Type;  // Lidar角度转换成刻度值
    if (robot->m_u16flag != NOACTIVE)
    {
        l_f32SpeedRad_to_OutCoorX =
            l_s32RobAng * g_f32Per10mDeg_Rad;  //求速度与输出坐标系的夹角刻度
        l_f32Rad = fmodf(l_f32Rad - l_f32SpeedRad_to_OutCoorX + l_f32_2PI,
                         l_f32_2PI);                            //求速度与输出坐标弧度值
        l_Speed_Out->m_s16speedx = l_f64Speed * cos(l_f32Rad);  //求速度在输出坐标系x速度
        l_Speed_Out->m_s16speedy = l_f64Speed * sin(l_f32Rad);  //求速度在输出坐标系y速度
    }
    else
    {
        l_Speed_In->m_s16speedx = 0;
        l_Speed_In->m_s16speedy = 0;
        l_Speed_Out->m_s16speedx = 0;
        l_Speed_Out->m_s16speedy = 0;
    }
}
/*************************************************
Function		:	Mark_Corr_BySpeed
Description		:	根据Speed修正Mark信息
Calls			:	无
Called By		: 	CalAngOffsetBySpeed360TOHalf
Table Accessed	: 	无
Table Updated	:	无
Input			:	p_Speed_Abs	:	绝对坐标系下Speed首地址
                    robot		:	Lidar位置,即上一圈定位值
                    p_Speed_Rel	:	相对坐标系下Speed首地址
Output			:	无
Return			:	无
Others			:	输入绝对->输出相对,输入相对->输出绝对
*************************************************/
void Mark_Corr_BySpeed(INPUTSPEED* p_Speed,
                       u8 p_MarkNum,
                       STRUCT_FILTER_TARGET* p_Filter,
                       float p_CorrTime)
{
    u8 l_u8i, l_u8size;
    u16 l_u16MarkAng;
    u16 l_u16CorrAng = 0;
    float l_f32CorrTime_ms, l_f32CorrGrad_BySRad, l_f32RadCorred_BySLine;
    float l_f32CorrTime__NoSAng_ms = 0;
    int l_s32CorrGrad_BySRad, l_s32CorrGrad_BySLine;
    u16 l_u16TargetPoint = 0, l_u16ResiduePoint;
    float l_f32CorrLine_X = 0, l_f32CorrLine_Y = 0;
    float l_f32MarkRAD = 0;  //靶标弧度值
    // int l_s32CoorLidar_X = 0, l_s32CoorLidar_Y = 0;
    float l_f32CoorLidar_X = 0, l_f32CoorLidar_Y = 0;
    float l_f32_2PI = M_PI * 2;
    u16 l_u16GradCorred_BySLine = 0;
    //	s8 l_s8MarkPose_Type = 0;//靶标在修至目标点方位,大于目标点=1,小于目标点 = -1;

    l_u8size = p_MarkNum;  //靶标个数
    //当目标点不为0或者g_u16ScanPointNum-1时,修正被分为两段
    l_u16TargetPoint =
        18000
        - g_u8Ang_Resolution;  //(g_u16ScanPointNum >> 1) -
                               // 1;//修正目标点,也代表0-目标点,目标点为[0,g_u16ScanPointNum-1]之间值
    l_u16ResiduePoint = 36000 - l_u16TargetPoint - g_u8Ang_Resolution * 2;  //剩余点,扫面点数-目标点
    for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
    {
        if (p_Filter->m_StructMarkInfo[l_u8i].m_u8IsMark == ISMARK)
        {
            l_u16MarkAng = p_Filter->m_StructMarkInfo[l_u8i].m_u16Ang;
            l_f32CorrTime_ms = (g_f32ScanTime_HalfPer - g_f32Time_PerResolution * l_u16MarkAng);
            if (p_Speed->m_s32spdang != 0)  //如果有旋转，先将刻度修至0刻线时的刻度
            {
                l_f32CorrTime__NoSAng_ms = g_f32Time_PerResolution * l_u16MarkAng;
                l_s32CorrGrad_BySRad = l_f32CorrTime__NoSAng_ms * p_Speed->m_s32spdang;

                l_u16MarkAng = (DEG_PERSCAN + l_u16MarkAng + l_s32CorrGrad_BySRad)
                               % DEG_PERSCAN;  //更新用于计算的MarkAng,去除了角速度的影响
            }

            //			if(l_u16MarkAng >= l_u16TargetPoint)
            //			{
            //				l_s8MarkPose_Type = 1;
            //			}
            //			else
            //			{
            //				l_s8MarkPose_Type = -1;
            //			}

            l_f32CorrLine_X =
                p_Speed->m_StructSpeed_relative.m_s16speedx * l_f32CorrTime_ms;  //单位 mm/s  * 毫秒
            //			l_f32CorrLine_X = l_f32CorrLine_X * l_s8MarkPose_Type; //单位mm

            l_f32CorrLine_Y =
                p_Speed->m_StructSpeed_relative.m_s16speedy * l_f32CorrTime_ms;  //单位 mm/s  * 毫秒
            //			l_f32CorrLine_Y = l_f32CorrLine_Y * l_s8MarkPose_Type; //单位mm

            l_f32MarkRAD = g_f32Per10mDeg_Rad * l_u16MarkAng;

            l_f32CoorLidar_X = p_Filter->m_StructMarkInfo[l_u8i].m_u16Dist * cosf(l_f32MarkRAD);
            l_f32CoorLidar_Y = p_Filter->m_StructMarkInfo[l_u8i].m_u16Dist * sinf(l_f32MarkRAD);
            //			Round_Up_To_Int(&l_f32CoorLidar_X,&l_s32CoorLidar_X);
            //			Round_Up_To_Int(&l_f32CoorLidar_Y,&l_s32CoorLidar_Y);

            l_f32RadCorred_BySLine =
                atan2(l_f32CoorLidar_Y - l_f32CorrLine_Y, l_f32CoorLidar_X - l_f32CorrLine_X);
            l_f32RadCorred_BySLine = fmodf(l_f32RadCorred_BySLine + l_f32_2PI, l_f32_2PI);

            l_u16GradCorred_BySLine = (u16)(l_f32RadCorred_BySLine / g_f32Per10mDeg_Rad);

            p_Filter->m_StructMarkInfo[l_u8i].m_u16Ang =
                (DEG_PERSCAN + l_u16GradCorred_BySLine
                 - (int)(g_f32ScanTime_HalfPer * p_Speed->m_s32spdang))
                % DEG_PERSCAN;  //再补偿

            p_Filter->m_StructMarkInfo[l_u8i].m_u16Dist = Calculate_Point2Point_Dist_Int(
                l_f32CoorLidar_Y - l_f32CorrLine_Y, l_f32CoorLidar_X - l_f32CorrLine_X);
        }
        else
            continue;
    }
}

/*************************************************
Function		:	Mark_Corr_BySpeed
Description		:	根据Speed修正Mark信息
Calls			:	无
Called By		: 	CalAngOffsetBySpeed360TOHalf
Table Accessed	: 	无
Table Updated	:	无
Input			:	p_Speed_Abs	:	绝对坐标系下Speed首地址
                    robot		:	Lidar位置,即上一圈定位值
                    p_Speed_Rel	:	相对坐标系下Speed首地址
Output			:	无
Return			:	无
Others			:	输入绝对->输出相对,输入相对->输出绝对
*************************************************/
// void Mark_Corr_BySpeed(INPUTSPEED *p_Speed, u8 p_MarkNum ,STRUCT_FILTER_TARGET *p_Filter )
//{
//	u8 l_u8i,l_u8size;
//	u16 l_u16MarkAng;
//	u16 l_u16CorrAng = 0;
//	float l_f32CorrTime_ms,l_f32CorrGrad_BySRad , l_f32RadCorred_BySLine;
//	int l_s32CorrGrad_BySRad,l_s32CorrGrad_BySLine,l_s32Result_Ang;
//	u16 l_u16TargetPoint  = 0 , l_u16ResiduePoint;
//	int l_s32CorrLine_X = 0 ,l_s32CorrLine_Y = 0;
//	float l_f32MarkRAD = 0;//靶标弧度值
//	int l_s32CoorLidar_X = 0, l_s32CoorLidar_Y = 0;
//	float l_f32CoorLidar_X = 0, l_f32CoorLidar_Y = 0;
//	float l_f32_2PI = M_PI * 2 ;
//	u16 l_u16GradCorred_BySLine = 0;
//	s8 l_s8MarkPose_Type = 0;//靶标在修至目标点方位,大于目标点=1,小于目标点 = -1;

//	l_u8size = p_MarkNum;	//靶标个数
//	//当目标点不为0或者g_u16ScanPointNum-1时,修正被分为两段
//	l_u16TargetPoint = 18000 - g_u8Ang_Resolution;//(g_u16ScanPointNum >> 1) -
// 1;//修正目标点,也代表0-目标点,目标点为[0,g_u16ScanPointNum-1]之间值 	l_u16ResiduePoint = 36000 -
// l_u16TargetPoint - g_u8Ang_Resolution * 2 ;	//剩余点,扫面点数-目标点 	for(l_u8i = 0 ;l_u8i <
// l_u8size ;l_u8i++)
//	{
//		if(p_Filter->m_StructMarkInfo[l_u8i].m_u8IsMark == ISMARK)
//		{
//			l_u16MarkAng = p_Filter->m_StructMarkInfo[l_u8i].m_u16Ang  ; //7200个点
//			if(l_u16MarkAng >= l_u16TargetPoint)
//			{
//				l_s8MarkPose_Type = 1;
//				l_u16CorrAng = l_u16MarkAng - l_u16TargetPoint ; 	//距离目标刻度的刻度个数
//				l_f32CorrTime_ms = l_u16CorrAng * g_f32Time_PerResolution; //单位ms
//			}
//			else
//			{
//				l_s8MarkPose_Type = -1;
//				l_u16CorrAng = l_u16ResiduePoint - l_u16MarkAng;
//				l_f32CorrTime_ms = l_u16CorrAng *g_f32Time_PerResolution;  //单位ms
//			}
//
//			if(p_Speed ->m_s32spdang != 0)
//			{
//				l_s32CorrGrad_BySRad = l_f32CorrTime_ms * p_Speed->m_s32spdang ;
//
//				l_s32CorrGrad_BySRad = l_s32CorrGrad_BySRad * l_s8MarkPose_Type;
////修正的方位角刻度值
//
//			}
//			else
//				l_s32CorrGrad_BySRad = 0;
//
//
//			l_s32CorrLine_X = (p_Speed->m_StructSpeed_relative.m_s16speedx * l_f32CorrTime_ms +
// 0.5);
////单位 mm/s  * 毫秒
//			//Round_Up_To_Int(&l_f32CorrLine_X,&l_s32CorrLine_X);
//			l_s32CorrLine_X = l_s32CorrLine_X * l_s8MarkPose_Type; //单位mm
//			l_s32CorrLine_Y = (p_Speed->m_StructSpeed_relative.m_s16speedy * l_f32CorrTime_ms +
// 0.5);
////单位 mm/s  * 毫秒
//			//Round_Up_To_Int(&l_f32CorrLine_Y,&l_s32CorrLine_Y);
//			l_s32CorrLine_Y = l_s32CorrLine_Y * l_s8MarkPose_Type; //单位mm
//
//			//4. 计算当前点的相对坐标, 还原上一个时刻的方位角
//			// 注意这里要区分 0.05 和 0.1度, 没有统一
//			l_f32MarkRAD = g_f32Per10mDeg_Rad * l_u16MarkAng;
//
//			l_f32CoorLidar_X = p_Filter->m_StructMarkInfo[l_u8i].m_u16Dist * cosf(l_f32MarkRAD);
//			l_f32CoorLidar_Y = p_Filter->m_StructMarkInfo[l_u8i].m_u16Dist * sinf(l_f32MarkRAD);
//			Round_Up_To_Int(&l_f32CoorLidar_X,&l_s32CoorLidar_X);
//			Round_Up_To_Int(&l_f32CoorLidar_Y,&l_s32CoorLidar_Y);
//
//			//6. 求取上一个时刻的方位角
//			//求最后时刻的,也就是新坐标  x' = x - a ;
//			// a就代表速度与时间走过的距离偏移
//			// x` 就是新的位置的相对坐标
//			l_f32RadCorred_BySLine = atan2(l_s32CoorLidar_Y + l_s32CorrLine_Y, l_s32CoorLidar_X +
// l_s32CorrLine_X) ; 			l_f32RadCorred_BySLine = fmodf(l_f32RadCorred_BySLine + l_f32_2PI
// ,l_f32_2PI);
//
//			l_u16GradCorred_BySLine = (u16)(l_f32RadCorred_BySLine / g_f32Per10mDeg_Rad) ;
//		//	Round_Up_To_Int(&l_f32GradCorred_BySLine,(s32 *)&l_u16GradCorred_BySLine);
//
//			l_s32Result_Ang = (l_u16GradCorred_BySLine + l_s32CorrGrad_BySRad + DEG_PERSCAN) %
// DEG_PERSCAN ;
//			//更新当前方位角, 去除了角速度的影响
//			p_Filter->m_StructMarkInfo[l_u8i].m_u16Ang = l_s32Result_Ang ;

//			p_Filter->m_StructMarkInfo[l_u8i].m_u16Dist =
// Calculate_Point2Point_Dist_Int(l_s32CoorLidar_Y + l_s32CorrLine_Y, l_s32CoorLidar_X +
// l_s32CorrLine_X);
//
//		}
//		else
//			continue;
//	}
//
//}

void Round_Up_To_Int(float* p_In, s32* p_Out)
{
    float l_f32tmp;
    if ((*p_In) < -0.000001)
        l_f32tmp = *p_In - 0.5;
    else
        l_f32tmp = *p_In + 0.5;
    *p_Out = (s32)l_f32tmp;
}

/*************************************************
Function		:	CalAngOffsetBySpeed360TOHalf
Description		:	对扫描靶标数据根据速度进行修正,修正至180°
Calls			:	无
Called By		: 	TaskTargetApp
Table Accessed	: 	无
Table Updated	:	无
Input			:	p_Filter	:	存放靶标首地址
                    robot		:	上一圈定位值
                    p_Speed		:	Lidar运动速度
Output			:	无
Return			:	无
Others			:	无
*************************************************/
void CalAngOffsetBySpeed360TOHalf(STRUCT_FILTER_TARGET* p_Filter,
                                  ROBOT_XY* robot,
                                  INPUTSPEED* p_Speed,
                                  float p_CorrTime)
{
    u8 l_u8size = p_Filter->m_u8In;
    //将输入的速度信息转换至另一坐标系
    INPUTSPEED l_sSpeed;
    memcpy(&l_sSpeed, p_Speed, STRUCT_SIZE_INPUTSPEED);
    if (g_sIcpSpeed.m_s32Flag != 0)
    {
        l_sSpeed.m_s32spdang = g_sIcpSpeed.m_f32SpeedAng;
        l_sSpeed.m_StructSpeed_relative.m_s16speedx = g_sIcpSpeed.m_sSpeed_Rel.m_s16speedx;
        l_sSpeed.m_StructSpeed_relative.m_s16speedy = g_sIcpSpeed.m_sSpeed_Rel.m_s16speedy;
    }
    else
    {
        if (p_Speed->m_u16speedsetflag == 0)
            Corr_Speed(p_Speed,
                       g_f32ScanTime_HalfPer,
                       &l_sSpeed.m_StructSpeed_absolute,
                       g_f32ScanTime_HalfPer);
        Speed_Transfer_To_Coordinates(
            &l_sSpeed.m_StructSpeed_absolute, robot, &l_sSpeed.m_StructSpeed_relative);
    }

    //根据运动速度,修正靶标角度与距离
    Mark_Corr_BySpeed(&l_sSpeed, l_u8size, p_Filter, p_CorrTime);
}

/*************************************************
Function		:	LaserPose_CompensationBySAng
Description		:	当有角速度时,将角速度补偿给激光器位置
Calls			:	无
Called By		: 	TaskTargetApp
Table Accessed	: 	无
Table Updated	:	无
Input			:	p_SpeedIn	:输入速度
                    p_LaserPose:激光器上一次定位
Output			:	p_LaserPose:做了半圈补偿后的激光器位置
Return			:	无
Others			:	上一次的定位值为激光器运动到码盘刚好转到360°位置的坐标值,
                    该函数给出的坐标值,需要补偿扫描至180°时的位置,即补偿62.5/g_u8AngMode
ms的速度(因为所有靶标补偿至180°位置)
*************************************************/
u16 LaserPose_CompensationBySAng(INPUTSPEED* p_SpeedIn, XYANG2ROBOT* p_LaserPose, float p_CorrTime)
{
    float l_f32Rad_Per10mdeg = 0;
    s16 l_s16CompensationRad = 0;
    l_f32Rad_Per10mdeg = p_SpeedIn->m_s32spdang;  /// 1000.0 /10;
    u16 l_u16LaserAng = p_LaserPose->m_u16ang;

    if (p_SpeedIn->m_s32spdang != 0)
    {
        l_s16CompensationRad = p_CorrTime * l_f32Rad_Per10mdeg;
        l_u16LaserAng = (DEG_PERSCAN + l_u16LaserAng + l_s16CompensationRad) % DEG_PERSCAN;
        // l_u16LaserAng = Round_Ang(l_u16LaserAng , g_u8Ang_Resolution);
    }
    return l_u16LaserAng;
}

// void Cal_MoveSpeed(INPUTSPEED *p_Speed,ROBOT_XY *p_Cur_Pos,ROBOT_XY *p_Old_Pos,TIMESTAMP
// p_TS_ScanOver,u32 p_TS_Diff)
//{
//	u16 l_u16SLine_Mul = 0;
//	int l_s32CurAng=0;
//	int l_s32OldAng =0;
//	float l_f32Speed_x,l_f32Speed_y;
//	float l_f32SpeedDirectionLast = 0,l_f32SpeedDirectionCurr =0 ;
//	u16 l_u16sprintLen =0;
//	if(p_Speed->m_u16speedsetflag == 0 )
//	{
//		if(g_u32CntOfACTIVE >1)
//		{
//			g_auspeedCnt++;
//			p_Speed->m_u32timestamp = p_TS_ScanOver;
//			p_Speed->m_u16Corrdinate_Type = COORDINATE_YTPE_ABS;
//			p_Speed->m_StructSpeed_absolute.m_s16speedx =
//(float)(p_Cur_Pos->m_Struct_LaserPos.m_s32x
//- p_Old_Pos->m_Struct_LaserPos.m_s32x) / p_TS_Diff;// * g_u8ScanFrequency;
//			p_Speed->m_StructSpeed_absolute.m_s16speedy =
//(float)(p_Cur_Pos->m_Struct_LaserPos.m_s32y
//- p_Old_Pos->m_Struct_LaserPos.m_s32y) / p_TS_Diff;//  * g_u8ScanFrequency; l_u16SLine_Mul =
// abs(p_Speed->m_StructSpeed_absolute.m_s16speedx/NOCORR_SLINE_P); 			if(l_u16SLine_Mul <
// 1) 				p_Speed->m_StructSpeed_absolute.m_s16speedx =0;
//
//			l_u16SLine_Mul = abs(p_Speed->m_StructSpeed_absolute.m_s16speedy/NOCORR_SLINE_P);
//			if(l_u16SLine_Mul < 1)
//				p_Speed->m_StructSpeed_absolute.m_s16speedy =0;
//
//			l_f32Speed_x = p_Speed->m_StructSpeed_absolute.m_s16speedx *
// p_Speed->m_StructSpeed_absolute.m_s16speedx; 			l_f32Speed_y =
// p_Speed->m_StructSpeed_absolute.m_s16speedy * p_Speed->m_StructSpeed_absolute.m_s16speedy;
//
//			p_Speed->m_f32spLine_Abs = sqrt(l_f32Speed_x + l_f32Speed_y)/2 +
// p_Speed->m_f32spLine_Abs/2; 			l_f32SpeedDirectionLast = fmod(p_Speed->m_f32spAngDirection
// + p_Speed->m_s32spdang * p_TS_Diff * g_f32Per10mDeg_Rad , 2*M_PI);
// p_Speed->m_f32spAngDirection =
// fmod(atan2(p_Speed->m_StructSpeed_absolute.m_s16speedy,p_Speed->m_StructSpeed_absolute.m_s16speedx)
//+ 2*M_PI,2*M_PI);
////			if(abs(l_f32SpeedDirectionCurr - l_f32SpeedDirectionLast) > M_PI)
////				p_Speed->m_f32spAngDirection = fmod(l_f32SpeedDirectionLast/2 +
/// l_f32SpeedDirectionCurr/2
///+ M_PI,2*M_PI); /			else /				p_Speed->m_f32spAngDirection =
/// l_f32SpeedDirectionLast/2 + l_f32SpeedDirectionCurr/2; /			p_Speed->m_f32spAngDirection
/// =
/// atan2(p_Speed->m_StructSpeed_absolute.m_s16speedy,p_Speed->m_StructSpeed_absolute.m_s16speedx);
////			if(p_Speed->m_f32spAngDirection < 0.0001)
////				p_Speed->m_f32spAngDirection += 2*M_PI;
////			l_s32CurAng = p_Cur_Pos->m_Struct_LaserPos.m_u16ang;
////			l_s32OldAng = p_Old_Pos->m_Struct_LaserPos.m_u16ang;
////			p_Speed->m_s32spdang = (float)(l_s32CurAng - l_s32OldAng) / p_TS_Diff;
////			if(p_Speed->m_s32spdang <(-144*g_u8AngMode))//(-144*g_u8AngMode)
////				p_Speed->m_s32spdang += (288*g_u8AngMode);//(288*g_u8AngMode)
////			else if(p_Speed->m_s32spdang >(144*g_u8AngMode))//(144*g_u8AngMode)
////				p_Speed->m_s32spdang -= (288*g_u8AngMode);//(288*g_u8AngMode)
////			l_u16SLine_Mul = abs(p_Speed->m_s32spdang) /NOCORR_SANG_P;
////			if(l_u16SLine_Mul < 1)
////				p_Speed->m_s32spdang = 0;
////			else
////			{
////				if(abs(p_Speed->m_s32spdang) < 18)
////					p_Speed->m_s32spdang = p_Speed->m_s32spdang ;
////				else
////					p_Speed->m_s32spdang = 0 ;
////			}
//			u8 l_u8Direction = 0;
//			p_Speed->m_s32spdang =
//(float)Find_IncludedAng_And_Direction(p_Cur_Pos->m_Struct_LaserPos.m_u16ang,p_Old_Pos->m_Struct_LaserPos.m_u16ang,&l_u8Direction)
/// p_TS_Diff;
//
//			if((p_Speed->m_s32spdang > 36))
//			{
//				p_Speed->m_s32spdang = 0;
//			}
//			else
//			{
//				l_u16SLine_Mul = p_Speed->m_s32spdang /NOCORR_SANG_P;
//				if(l_u16SLine_Mul < 1)
//				{
//					p_Speed->m_s32spdang = 0;
//				}
//				else
//				{
//					if(l_u8Direction)	//逆时针
//						p_Speed->m_s32spdang = p_Speed->m_s32spdang ;
//					else
//						p_Speed->m_s32spdang = 0 - p_Speed->m_s32spdang ;
//				}
//			}
//
//		}
//		else
//			memset(p_Speed , 0 ,STRUCT_SIZE_INPUTSPEED);
//	}
//}
void Cal_MoveSpeed(INPUTSPEED* p_Speed,
                   ROBOT_XY* p_Cur_Pos,
                   ROBOT_XY* p_Old_Pos,
                   TIMESTAMP p_TS_ScanOver,
                   u32 p_TS_Diff)
{
    u16 l_u16SLine_Mul = 0;
    int l_s32CurAng = 0;
    int l_s32OldAng = 0;
    float l_f32Speed_x, l_f32Speed_y;
    if (p_Speed->m_u16speedsetflag == 0)
    {
        if (g_u32CntOfACTIVE > 1)
        {
            g_auspeedCnt++;
            p_Speed->m_u32timestamp = p_TS_ScanOver;
            p_Speed->m_u16Corrdinate_Type = COORDINATE_YTPE_ABS;
            p_Speed->m_StructSpeed_absolute.m_s16speedx =
                (float)(p_Cur_Pos->m_Struct_LaserPos.m_s32x - p_Old_Pos->m_Struct_LaserPos.m_s32x)
                / p_TS_Diff;  // * g_u8ScanFrequency;
            p_Speed->m_StructSpeed_absolute.m_s16speedy =
                (float)(p_Cur_Pos->m_Struct_LaserPos.m_s32y - p_Old_Pos->m_Struct_LaserPos.m_s32y)
                / p_TS_Diff;  //  * g_u8ScanFrequency;
            l_u16SLine_Mul = abs(p_Speed->m_StructSpeed_absolute.m_s16speedx / NOCORR_SLINE_P);

            if (l_u16SLine_Mul < 1)
                p_Speed->m_StructSpeed_absolute.m_s16speedx = 0;

            l_u16SLine_Mul = abs(p_Speed->m_StructSpeed_absolute.m_s16speedy / NOCORR_SLINE_P);
            if (l_u16SLine_Mul < 1)
                p_Speed->m_StructSpeed_absolute.m_s16speedy = 0;

            l_f32Speed_x = p_Speed->m_StructSpeed_absolute.m_s16speedx
                           * p_Speed->m_StructSpeed_absolute.m_s16speedx;
            l_f32Speed_y = p_Speed->m_StructSpeed_absolute.m_s16speedy
                           * p_Speed->m_StructSpeed_absolute.m_s16speedy;
            p_Speed->m_f32spLine_Abs = sqrt(l_f32Speed_x + l_f32Speed_y);
            p_Speed->m_f32spAngDirection = atan2(p_Speed->m_StructSpeed_absolute.m_s16speedy,
                                                 p_Speed->m_StructSpeed_absolute.m_s16speedx);
            if (p_Speed->m_f32spAngDirection < 0.0001)
                p_Speed->m_f32spAngDirection += 2 * M_PI;
            //			l_s32CurAng = p_Cur_Pos->m_Struct_LaserPos.m_u16ang;
            //			l_s32OldAng = p_Old_Pos->m_Struct_LaserPos.m_u16ang;
            //			p_Speed->m_s32spdang = (float)(l_s32CurAng - l_s32OldAng) / p_TS_Diff;
            //			if(p_Speed->m_s32spdang <(-144*g_u8AngMode))//(-144*g_u8AngMode)
            //				p_Speed->m_s32spdang += (288*g_u8AngMode);//(288*g_u8AngMode)
            //			else if(p_Speed->m_s32spdang >(144*g_u8AngMode))//(144*g_u8AngMode)
            //				p_Speed->m_s32spdang -= (288*g_u8AngMode);//(288*g_u8AngMode)
            //			l_u16SLine_Mul = abs(p_Speed->m_s32spdang) /NOCORR_SANG_P;
            //			if(l_u16SLine_Mul < 1)
            //				p_Speed->m_s32spdang = 0;
            //			else
            //			{
            //				if(abs(p_Speed->m_s32spdang) < 18)
            //					p_Speed->m_s32spdang = p_Speed->m_s32spdang ;
            //				else
            //					p_Speed->m_s32spdang = 0 ;
            //			}
            u8 l_u8Direction = 0;
            // if(WHO_AM_I != WHO_AM_I_TYPE)
            {
                p_Speed->m_s32spdang =
                    (float)Find_IncludedAng_And_Direction(p_Cur_Pos->m_Struct_LaserPos.m_u16ang,
                                                          p_Old_Pos->m_Struct_LaserPos.m_u16ang,
                                                          &l_u8Direction)
                    / p_TS_Diff;

                if ((p_Speed->m_s32spdang > 36))
                {
                    p_Speed->m_s32spdang = 0;
                }
                else
                {
                    l_u16SLine_Mul = p_Speed->m_s32spdang / NOCORR_SANG_P;
                    if (l_u16SLine_Mul < 1)
                    {
                        p_Speed->m_s32spdang = 0;
                    }
                    else
                    {
                        if (l_u8Direction)  //逆时针
                            p_Speed->m_s32spdang = p_Speed->m_s32spdang;
                        else
                            p_Speed->m_s32spdang = 0 - p_Speed->m_s32spdang;
                    }
                }
            }
        }
        else
            memset(p_Speed, 0, STRUCT_SIZE_INPUTSPEED);
    }
}
u8 Nav_Mark_Num(u8 p_MarkNum)
{
    if (p_MarkNum >= 5)
        return 5;
    if (p_MarkNum == 2)
        return 2;
    return p_MarkNum;
}
void LaserPose_CompensationBySLine(SPEED* p_SpeedIn,
                                   XYANG2ROBOT* p_LaserPose_In,
                                   XYANG2ROBOT* p_LaserPose_Out,
                                   float p_CorrTime)
{
    p_LaserPose_Out->m_s32x = p_LaserPose_In->m_s32x + (int)(p_SpeedIn->m_s16speedx * p_CorrTime);
    p_LaserPose_Out->m_s32y = p_LaserPose_In->m_s32y + (int)(p_SpeedIn->m_s16speedy * p_CorrTime);
}

void Corr_Speed(INPUTSPEED* p_SpeedIn, float p_CorrTime, SPEED* p_SpeedOut, float p_LastMoveTime)
{
    //	float *l_f32Speed_X = &p_SpeedIn->m_StructSpeed_absolute.m_s16speedx ;//  /
    // g_f32ScanTime_Per; 	float *l_f32Speed_Y = &p_SpeedIn->m_StructSpeed_absolute.m_s16speedy;//
    // / g_f32ScanTime_Per;
    float l_f32CorrAng =
        (p_LastMoveTime + p_CorrTime) / 2 * p_SpeedIn->m_s32spdang * g_f32Per10mDeg_Rad
        + p_SpeedIn->m_f32spAngDirection;
    float l_fCos = cos(l_f32CorrAng);
    float l_fSin = sin(l_f32CorrAng);

    if (p_SpeedIn->m_s32spdang != 0)
    {
        p_SpeedOut->m_s16speedx = p_SpeedIn->m_f32spLine_Abs * l_fCos;
        p_SpeedOut->m_s16speedy = p_SpeedIn->m_f32spLine_Abs * l_fSin;
    }
    else
    {
        p_SpeedOut->m_s16speedx = p_SpeedIn->m_StructSpeed_absolute.m_s16speedx;
        p_SpeedOut->m_s16speedy = p_SpeedIn->m_StructSpeed_absolute.m_s16speedy;
    }
}

u8 Get_Match_Least_Mark_Num(u8 p_ScanMarkNum, u8 p_NavLeastMarkNum)
{
    return (p_ScanMarkNum > 6) ? ((p_ScanMarkNum + 1) >> 1) : p_NavLeastMarkNum;
}

void Get_IdenWindow_Corr(STRUCT_MarkMatch p_IdenW_Set, STRUCT_MarkMatch* p_IdenW_Corr)
{
    p_IdenW_Corr->m_u16IdentWindow_R_Min = (p_IdenW_Set.m_u16IdentWindow_R_Min < 150)
                                               ? 300
                                               : (p_IdenW_Set.m_u16IdentWindow_R_Min << 1);
    if (p_IdenW_Corr->m_u16IdentWindow_R_Min > 800)
        p_IdenW_Corr->m_u16IdentWindow_R_Min = 800;
    p_IdenW_Corr->m_u16IdentWindow_R_Max = p_IdenW_Corr->m_u16IdentWindow_R_Min;
}
// u8 Match_SetMark_By_LaserPose(STRUCT_FILTER_TARGET_LITE *p_NAV_Mark, ROBOT_XY *p_LaserPose,
// XY2ROBOT_CNT *p_MappingXY_old , MARK_SETS *p_SETMARK,u8 p_NavLeastMarkNum,STRUCT_MarkMatch
// p_IdenWindow)
//{
//	u8 l_u8i;
//	u8 l_u8size = p_NAV_Mark->m_StructMarkScanInfo.m_u8In;
//	u8 l_u8MarkMatchCnt = 0;
//	u8 l_u8ret = 0;
//	u8 l_u8MatchLeastMarkNum = 0;

//	memset((void *)&p_SETMARK->m_u8hasmatchflag, 0 ,  p_SETMARK->m_u16size);
//	memset((void *)&p_NAV_Mark->m_sMark, 0 ,  l_u8size*STRUCT_SIZE_MARK_XY);
//	g_u8HasCrossFlag = 0;
//	Renew_AbsCoor_XY1(&p_NAV_Mark->m_sXy2Robot[0] , &p_LaserPose->m_Struct_LaserPos ,p_MappingXY_old
//,l_u8size); 	memset(&g_sLocalMap_Info,0, STRUCT_SIZE_LOCAL_MAP_INFO); 	for(l_u8i =0;l_u8i <
// l_u8size ;l_u8i++)
//	{
//		l_u8MarkMatchCnt += MatchSetMark(p_NAV_Mark , p_SETMARK ,  l_u8i ,
// p_MappingXY_old,p_IdenWindow);
//	}
//	g_sLocalMap_Info.m_u32MarkNum = l_u8MarkMatchCnt;
//	l_u8ret = !(l_u8MarkMatchCnt >= Get_Match_Least_Mark_Num(l_u8size , p_NavLeastMarkNum));

//	return l_u8ret;
//}

/*
6个参数分别为：
//	p_NAV_Mark:	当前帧靶标
//	p_LaserPose: 目前为当前帧预估位姿(最大35)，
// 	p_MappingXY_old: MAPPING建图结束地图储存，  输出全局mark坐标?
//	MAP-NAV地图上的靶标，NAV最少扫描靶标数3个，NAV匹配的靶标
//
*/
u8 Match_SetMark_By_LaserPose(STRUCT_FILTER_TARGET_LITE* p_NAV_Mark,
                              ROBOT_XY* p_LaserPose,
                              XY2ROBOT_CNT* p_MappingXY_old,
                              MARK_SETS* p_SETMARK,
                              u8 p_NavLeastMarkNum,
                              STRUCT_MarkMatch* p_IdenWindow)
{
    u8 l_u8size = p_NAV_Mark->m_StructMarkScanInfoAddr.m_u8In;
    u8 l_u8MarkMatchCnt = 0;

    memset((void*)&p_SETMARK->m_u8hasmatchflag, 0, p_SETMARK->m_u16size);
    memset((void*)&p_NAV_Mark->m_psSetMarkAddr, 0, l_u8size * 4);
    g_u8HasCrossFlag = 0;

    // mark转全局坐标系, 参数为：当前帧靶标，当前机器人预估位姿，输出全局mark坐标，当前帧靶标个数
    Renew_AbsCoor_XY1(&p_NAV_Mark->m_StructMarkScanInfoAddr.m_sXy2Robot[0],
                      &p_LaserPose->m_Struct_LaserPos,
                      p_MappingXY_old,
                      l_u8size);
    memset(&g_sLocalMap_Info, 0, STRUCT_SIZE_LOCAL_MAP_INFO);

    //返回匹配到的地图上的靶标数量 p_NAV_Mark 被填充
    l_u8MarkMatchCnt = Return_Match_SetMarkNum(p_NAV_Mark, p_MappingXY_old, p_IdenWindow);

    printf("match mark size : %d\n", l_u8MarkMatchCnt);

    // Get_Match_Least_Mark_Num : l_u8size/2(大于6)，否则返回最低NAV扫描个数3
    return !(l_u8MarkMatchCnt >= Get_Match_Least_Mark_Num(l_u8size, p_NavLeastMarkNum));
}

u8 Return_Match_KNN_Mul(MARK_XY* psMinDistPoint, u32* nMinDis)
{
    u8 l_u8offset;
    if (psMinDistPoint != NULL)
    {
        l_u8offset = (nMinDis[0] <= nMinDis[1]) ? 0 : 1;
        if (nMinDis[l_u8offset] == 0)
            return 1;
        if (nMinDis[(l_u8offset + 1) % 2] / nMinDis[l_u8offset] > 5)
            return 1;
    }
    return 0;
}

// 当前帧靶标，当前帧全局坐标靶标，
u8 Return_Match_SetMarkNum(STRUCT_FILTER_TARGET_LITE* p_NAV_Mark,
                           XY2ROBOT_CNT* p_MappingXY_old,
                           STRUCT_MarkMatch* p_IdenWindow)
{
    u8 l_u8i;
    u8 l_u8MarkMatchCnt = 0;
    u8 l_u8size = p_NAV_Mark->m_StructMarkScanInfoAddr.m_u8In;
    static u32 l_u32mindis[2];
    MARK_XY* l_psMinDistPoint = NULL;
    MARK_XY** l_psSetMarkAddr = &p_NAV_Mark->m_psSetMarkAddr[0];
    STRUCT_MARK_INFO** l_psMarkInfoAddr = &p_NAV_Mark->m_StructMarkScanInfoAddr.m_StructMarkInfo[0];
    u16 l_u16IdentWindow;
    u8 KNN_flag = 0;

    for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
    {
        l_u32mindis[0] = l_u32mindis[1] = 0xFFFFFFFF;
        l_psMinDistPoint = NULL;
        l_psMarkInfoAddr[l_u8i]->m_u8IsMark = NOTMARK;
        l_u16IdentWindow = Get_IdentWindow(l_psMarkInfoAddr[l_u8i]->m_u16Dist, p_IdenWindow);
        // ROS_INFO("l_u16IdentWindow=%d",l_u16IdentWindow);

        // MAP-NAV地图上的靶标的kdtree
        KDTree_GetClosest(g_kd_root,
                          p_MappingXY_old->m_StructXY[l_u8i],
                          &l_psMinDistPoint,
                          &l_u32mindis[0],
                          l_u16IdentWindow);

        KNN_flag = Return_Match_KNN_Mul(l_psMinDistPoint, &l_u32mindis[0]);

        if (KNN_flag)  //匹配成功，但是需要校验是否有重复匹配
        {
            for (u8 j = 0; j < l_u8i; j++)
            {
                // MARK_XY *l_psPoint = l_psSetMarkAddr[j];
                // j靶标之前已经匹配成功，且这次找到的最近靶id = j靶标id
                if (l_psMarkInfoAddr[j]->m_u8IsMark == ISMARK
                    && l_psSetMarkAddr[j]->m_u32no == l_psMinDistPoint->m_u32no)
                {
                    l_psMarkInfoAddr[j]->m_u8IsMark = NOTMARK;
                    g_sLocalMap_Info.m_uIsMark[j] = NOTMARK;
                    g_u8HasCrossFlag = 1;
                    KNN_flag = 0;
                    l_u8MarkMatchCnt -= 1;
                }
            }
        }

        if (KNN_flag)
        {
            l_psSetMarkAddr[l_u8i] = l_psMinDistPoint;
            // memcpy(&p_NAV_Mark->m_sMark[l_u8i],l_psMinDistPoint,STRUCT_SIZE_MARK_XY);
            l_psMarkInfoAddr[l_u8i]->m_u8IsMark = ISMARK;
            l_psMarkInfoAddr[l_u8i]->m_u8MarkSize = l_psSetMarkAddr[l_u8i]->m_u8size;
            l_psMarkInfoAddr[l_u8i]->m_u8MarkType = l_psSetMarkAddr[l_u8i]->m_u8shape;
            g_sLocalMap_Info.m_pStructSetMark[l_u8i] = l_psMinDistPoint;
            g_sLocalMap_Info.m_uIsMark[l_u8i] = ISMARK;
            l_u8MarkMatchCnt += 1;
        }
    }
    g_sLocalMap_Info.m_u32MarkTotalNum = l_u8size;
    g_sLocalMap_Info.m_u32MarkMatchNum = l_u8MarkMatchCnt;
    return l_u8MarkMatchCnt;
}
void Judge_VirtualPos(ROBOT_XY* p_CurPos, ROBOT_XY* p_OldPos)
{
    u32 l_u32Diff_X = abs(p_CurPos->m_Struct_LaserPos.m_s32x - p_OldPos->m_Struct_LaserPos.m_s32x);
    u32 l_u32Diff_Y = abs(p_CurPos->m_Struct_LaserPos.m_s32y - p_OldPos->m_Struct_LaserPos.m_s32y);
    TIMESTAMP l_u32Diff_TS = p_CurPos->m_u32timestamp - p_OldPos->m_u32timestamp;
    if (!Points_Diff_Cmp(l_u32Diff_X, 3000) || (!Points_Diff_Cmp(l_u32Diff_Y, 3000))
        || (!Points_Diff_Cmp(l_u32Diff_TS, 500)))
    {
        g_auspeedCnt = 0;
        // ROS_ERROR("p_CurPos is being memset!!!");
        memset(p_CurPos, 0, STRUCT_SIZE_LASERPOS);
    }
}

void FixScanAng(STRUCT_FILTER_TARGET* pDist)
{
    u8 l_u8size, l_u8i;
    l_u8size = pDist->m_u8In;
    u32 l_u32dist;
    u16 l_u16ang;
    float l_f32tmp;

    for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
    {
        l_u32dist = pDist->m_StructMarkInfo[l_u8i].m_u16Dist;
        if (l_u32dist < 50)
            continue;
        l_f32tmp = atan2(13, l_u32dist);
        if (l_f32tmp < -0.000000001)
            l_f32tmp += 2 * M_PI;
        //统一为7200
        l_u16ang = (u16)(l_f32tmp * g_u16ScanPointNum / 2 / M_PI);

        pDist->m_StructMarkInfo[l_u8i].m_u16Ang += l_u16ang;
        pDist->m_StructMarkInfo[l_u8i].m_u16Ang %= g_u16ScanPointNum;
    }
}

void FixMarkAng_PINGXINGZHOU(STRUCT_MARK_INFO* p_MarkInfo)
{
    u32 l_u32dist;
    u16 l_u16ang;
    float l_f32tmp;
    l_u32dist = p_MarkInfo->m_u16Dist;
    if (l_u32dist < 50)  // 5cm内不修？
        return;

    l_f32tmp = atan2(13, l_u32dist);  // atan2（13/dist）
    if (l_f32tmp < -0.000000001)
        l_f32tmp = 0;
    l_u16ang = (u16)(l_f32tmp / g_f32Per10mDeg_Rad);
    p_MarkInfo->m_u16Ang = (p_MarkInfo->m_u16Ang + l_u16ang) % DEG_PERSCAN;
}

void FixScanAngAfter(STRUCT_FILTER_TARGET* pDist)
{
    u8 l_u8size, l_u8i;
    l_u8size = pDist->m_u8In;
    u32 l_u32dist;
    u16 l_u16ang;
    float l_f32r;
    float l_f32offset = 0.00349;
    float l_f32tmp1, l_f32tmp2, l_f32rad;
    u16 l_u16buf[16];
    if (l_u8size > 16)
        return;
    for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
    {
        l_u32dist = pDist->m_StructMarkInfo[l_u8i].m_u16Dist;

        l_f32tmp1 = pDist->m_StructMarkInfo[l_u8i].m_u16Ang * M_PI / 3600.0;
        l_f32tmp2 = atan2(13, l_u32dist);
        if (l_f32tmp2 < -0.0000001)
            l_f32tmp2 += 2 * M_PI;
        l_f32rad = l_f32tmp1 + l_f32tmp2;

        l_f32r = 0.146;  // 0.1465
        l_f32tmp1 = atan2(l_f32r * sinf(l_f32rad), 24);
        // 先转换为刻度
        //		l_u16ang = l_f32tmp1*3600.0/M_PI ;
        //		l_u16ang  = (l_u16ang/4)*4 ;
        //		l_f32tmp1 = l_u16ang*M_PI/3600.0 ;

        // l_f32tmp1 = l_f32tmp1/l_f32offset * l_f32offset ;
        //		if(l_f32tmp1 < -0.0000001)
        //			l_f32tmp1 += 2*M_PI ;
        l_f32tmp2 = l_f32rad - l_f32tmp1;

        //统一为7200
        l_u16ang = (u16)(l_f32tmp2 * 3600 / M_PI);
        l_u16buf[l_u8i] = l_u16ang;
        pDist->m_StructMarkInfo[l_u8i].m_u16Ang = l_u16ang;
        pDist->m_StructMarkInfo[l_u8i].m_u16Ang %= 7200;
    }
}

//根据距离计算平面靶的有效点数
u32 CalPlanMarkAngle(u32 dist, u16 len)
{
    //取最小边
    u16 l_u16len = len / 2;
    u16 l_u16ang;
    float l_f32rad, l_f32tmp;
    //	l_f32tmp = (float)l_u16len / (float)dist ;
    //	l_f32rad = acosf(l_f32tmp) ;
    //	//转换为角度
    //	l_u16ang = l_f32rad*3600/M_PI ;

    //取近视的平均距离
    l_f32tmp = atan2(l_u16len, dist);
    l_u16ang = 2 * l_f32tmp * 3600 / M_PI;
    return l_u16ang;
}

/*************************************************
Function		:	CopyFilterMark
Description		:	剔除无效靶标，最高强度点距离+靶标半径
Calls			:	无
Called By		: 	TaskTargetApp
Table Accessed	: 	无
Table Updated	:	无
Input			:	pDistOld	:	存放原始靶标首地址
Output			:	pDistNew	:	存放剔除后靶标首地址
Return			:	无
Others			:	无
*************************************************/
u32 CopyFilterMark(STRUCT_FILTER_TARGET* pDistOld, STRUCT_FILTER_TARGET* pDistNew)
{
    u8 l_u8cnt = 0;
    u8 l_u8i = 0, l_u8size = 0;
    u16 l_u16fixsize = 0;
    STRUCT_MARK_INFO *l_psMarkOld = NULL, *l_psMarkNew = NULL;

    l_psMarkOld = &pDistOld->m_StructMarkInfo[0];
    l_psMarkNew = &pDistNew->m_StructMarkInfo[0];
    l_u8size = pDistOld->m_u8In;

    //根据靶标的类型修正距离值
    switch (g_sSysPib.m_u16MarkType)
    {
        case MARK_TYPE_CYC: l_u16fixsize = g_sSysPib.m_u16MarkRadio >> 1; break;
        case MARK_TYPE_FACE: l_u16fixsize = 0; break;
        default: break;
    }

    for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
    {
        if ((l_psMarkOld + l_u8i)->m_u8IsMark == ISMARK)
        {
            //(l_psMarkOld + l_u8i)->m_u16Ang *= g_u8Ang_Resolution;	//统一成7200点
            (l_psMarkOld + l_u8i)->m_u8MarkSize = g_sSysPib.m_u16MarkRadio;
            (l_psMarkOld + l_u8i)->m_u8MarkType = g_sSysPib.m_u16MarkType;
            (l_psMarkOld + l_u8i)->m_u16Dist += l_u16fixsize;
            memcpy((void*)(l_psMarkNew + l_u8cnt),
                   (void*)(l_psMarkOld + l_u8i),
                   STRUCT_SIZE_MARK_INFO);
            l_u8cnt++;
        }
    }
    pDistNew->m_u8In = l_u8cnt;
    return 0;
}

void ByteToHexStr(const unsigned char* source, char* dest, int sourceLen)
{
    short i;
    unsigned char highByte, lowByte;

    for (i = 0; i < sourceLen; i++)
    {
        highByte = source[i] >> 4;
        lowByte = source[i] & 0x0f;

        highByte += 0x30;

        if (highByte > 0x39)
            dest[i * 2] = highByte + 0x07;
        else
            dest[i * 2] = highByte;

        lowByte += 0x30;
        if (lowByte > 0x39)
            dest[i * 2 + 1] = lowByte + 0x07;
        else
            dest[i * 2 + 1] = lowByte;
    }
    return;
}

u32 U16ToHEXStr(u16 x, char* s)
{
    s8 i;
    u8 tmp;
    u32 l_u32ret;
    i = 3;
    while (x != 0)
    {
        tmp = x & 0x0f;
        if (tmp < 10)
            tmp += '0';
        else
            tmp += 'A' - 10;
        s[i] = tmp;
        if ((i--) == 0)
            break;
        x = x >> 4;
    }

    l_u32ret = i + 1;
    //补前导0
    for (; i >= 0; i--)
    {
        s[i] = '0';
    }

    return l_u32ret;
}

u32 U32ToHEXStr(u32 x, char* s)
{
    s8 i;
    u8 tmp;
    u32 l_u32ret;
    i = 7;
    if (x == 0)
    {
        s[7] = 0x30;
        i--;
        l_u32ret = 7;
    }
    else
    {
        while (x != 0)
        {
            tmp = x & 0x0f;
            if (tmp < 10)
                tmp += '0';
            else
                tmp += 'A' - 10;
            s[i] = tmp;
            if ((i--) == 0)
                break;
            x = x >> 4;
        }
        l_u32ret = i + 1;
    }
    //补前导0
    for (; i >= 0; i--)
    {
        s[i] = '0';
    }
    return l_u32ret;
}

u32 HexStr2U32(void* Str)
{
    u32 tmp = 0;
    char* p = (char*)Str;
    while (1)
    {
        if (*p >= '0' && *p <= '9')
        {
            tmp *= 16;
            tmp += *p - '0';
        }
        else if (*p >= 'a' && *p <= 'f')
        {
            tmp *= 16;
            tmp += *p - 'a' + 10;
        }
        else if (*p >= 'A' && *p <= 'F')
        {
            tmp *= 16;
            tmp += *p - 'A' + 10;
        }
        else
        {
            break;
        }
        p++;
    }
    return tmp;
}

u32 U32ToHexNum(u32 num)
{
    u32 l_u32ret;
    if (num < 10)
        l_u32ret = 1;
    else if ((num >= 10) && (num < 100))
        l_u32ret = 2;
    else if ((num >= 100) && (num < 1000))
        l_u32ret = 3;
    else if ((num >= 1000) && (num < 10000))
        l_u32ret = 4;
    else if ((num >= 10000) && (num < 100000))
        l_u32ret = 5;
    else if ((num >= 100000) && (num < 1000000))
        l_u32ret = 6;
    else if ((num >= 1000000) && (num < 10000000))
        l_u32ret = 7;
    else if ((num >= 10000000) && (num < 100000000))
        l_u32ret = 8;
    else if ((num >= 100000000) && (num < 1000000000))
        l_u32ret = 9;
    else if ((num >= 1000000000) && (num < 10000000000))
        l_u32ret = 10;
    else
        l_u32ret = 0;
    return l_u32ret;
}

int U32ToDigitalNum(u32 num)
{
    int l_s32ret;
    if (num < 10)
        l_s32ret = 1;
    else if ((num >= 10) && (num < 100))
        l_s32ret = 2;
    else if ((num >= 100) && (num < 1000))
        l_s32ret = 3;
    else if ((num >= 1000) && (num < 10000))
        l_s32ret = 4;
    else if ((num >= 10000) && (num < 100000))
        l_s32ret = 5;
    else if ((num >= 100000) && (num < 1000000))
        l_s32ret = 6;
    else if ((num >= 1000000) && (num < 10000000))
        l_s32ret = 7;
    else if ((num >= 10000000) && (num < 100000000))
        l_s32ret = 8;
    else if ((num >= 100000000) && (num < 1000000000))
        l_s32ret = 9;
    else
        l_s32ret = -1;
    return l_s32ret;
}

int U32ToStr(u32 x, char* s, u8 len)
{
    int Len1 = U32ToDigitalNum(x);
    int i;
    if (Len1 + 1 > len)
        return 0;  //??
    s += Len1;
    *s = 0;  //???
    for (i = 0; i < Len1; i++)
    {
        s--;
        *s = x % 10 + '0';
        x = x / 10;
    }
    return Len1;
}

/*************************************************
Function		:	CodeZeroCorrect
Description		:	偏心修正查表
Calls			:	无
Called By		: 	TaskTargetApp
Table Accessed	: 	无
Table Updated	:	无
Input			:	pFilter	:	存放靶标首地址
Output			:	pFilter->m_u16Ang:将刻度值转换成10mdeg为单位角度值,进行偏心修正
Return			:	无
Others			:	无
*************************************************/
void CodeZeroCorrect(STRUCT_FILTER_TARGET* pFilter)
{
    u8 l_u8i = 0;
    s8 l_s8PXCorr = 0;
    u8 l_u8size = 0;
    s16 l_s16tmp = 0;
    STRUCT_MARK_INFO* l_psMark = NULL;
    u32 l_u32PXCorr_Offset = 0;

    l_u8size = pFilter->m_u8In;
    if ((g_sSysPib.m_u16PianXinCorrEN == 1) && (g_u16PianXinFlag == 1))
    {
        for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
        {  // 7200
            //没用
            l_u32PXCorr_Offset = (g_u16ScanPointNum + pFilter->m_StructMarkInfo[l_u8i].m_u16Ang
                                  + (g_sFactorCorr.m_s16ArgOfCode >> (g_u8AngMode - 1)))
                                 % g_u16ScanPointNum;  //+ g_sFactorCorr.m_s16BmqOffset;

            l_psMark = &pFilter->m_StructMarkInfo[l_u8i];

            //点Index 转 mdeg
            l_psMark->m_u16Ang *= g_u8Ang_Resolution;

            //偏心修正?
            FixMarkAng_PINGXINGZHOU(l_psMark);
            if (l_psMark->m_u8IsMark == ISMARK)
            {
                // Flash_Read_Byte(ADDR_FLASH_PIANXIN_DATA + l_u32PXCorr_Offset, 1 , (u8
                // *)&l_s8PXCorr);

                l_psMark->m_u16Ang = (DEG_PERSCAN + l_psMark->m_u16Ang + l_s8PXCorr) % DEG_PERSCAN;
            }
        }
    }
    else
    {
        for (l_u8i = 0; l_u8i < l_u8size; l_u8i++)
        {
            l_psMark = &pFilter->m_StructMarkInfo[l_u8i];
            l_psMark->m_u16Ang *= g_u8Ang_Resolution;
            FixMarkAng_PINGXINGZHOU(l_psMark);
            l_psMark->m_u16Ang = (DEG_PERSCAN + l_psMark->m_u16Ang) % DEG_PERSCAN;
        }
    }
}

/*************************************************
Function		:	Moving_Average
Description		:	对脉宽进行多点滑动平均
Calls			:	无
Called By		: 	TaskTargetApp
Table Accessed	: 	无
Table Updated	:	无
Input			:	p_Width_In : 脉宽原始数据首地址
                    p_cnt : 取平均点数(偶数)
                    p_ScanDotNum :
Output			:	p_Width_Out :处理后脉宽数据首地址
Return			:	无
Others			:	无
*************************************************/
u32 Moving_Average(u16* p_Width_In, u16* p_Width_Out, u8 p_cnt, u16 p_ScanDotNum)  //多点滑动平均
{
    u16 l_u16MoveAver_Num = 0;
    u16 l_u16MoveAver_Num_Half = 0;
    u16 l_u16i, l_u16start = 0;
    u32 l_u32SumWidth = 0;
    u16 l_u16DeletePoint = 0, l_u16AddPoint = 0;

    l_u16MoveAver_Num = p_cnt;  //取平均点数
    l_u16MoveAver_Num_Half = l_u16MoveAver_Num >> 1;
    l_u16start = p_ScanDotNum - (l_u16MoveAver_Num >> 1);  //平均起始点

    while (p_cnt > 0)  //求第0个点的脉宽总和,[P(7200-cnt/2) ~ P(cnt/2 - 1)]共Cnt个点
    {
        l_u32SumWidth += p_Width_In[l_u16start % p_ScanDotNum];
        l_u16start += 1;
        p_cnt--;
    }

    p_Width_Out[0] = l_u32SumWidth / l_u16MoveAver_Num;  //第0个点的滑动平均后脉宽值

    for (l_u16i = 1; l_u16i < p_ScanDotNum; l_u16i++)
    {
        l_u16DeletePoint = (l_u16i + p_ScanDotNum - (l_u16MoveAver_Num_Half + 1)) % p_ScanDotNum;

        l_u16AddPoint = l_u16i + (l_u16MoveAver_Num_Half - 1);  //新增加脉宽点的位置

        l_u32SumWidth =
            l_u32SumWidth - p_Width_In[l_u16DeletePoint] + p_Width_In[l_u16AddPoint % p_ScanDotNum];

        p_Width_Out[l_u16i] = (u16)(l_u32SumWidth / l_u16MoveAver_Num);
    }
    return 0;
}
/*************************************************
Function		:	Find_ScanData_StaPoint
Description		:	找到扫描起始点,防止靶标被0点分割
Calls			:	无
Called By		: 	TaskTargetApp
Table Accessed	: 	无
Table Updated	:	无
Input			:	Width_Carve : 切后脉宽首地址
                    p_ScanPointNum : 一圈点数
Output			:	无
Return			:	l_u16i:计算起始点
Others			:	无
*************************************************/
u16 Find_ScanData_StaPoint(u16 p_ScanPointNum, u16* p_Width_Carve, u16 p_ZeroNum)
{
    u16 l_u16i;
    u16 l_u16cnt = 0;
    //	u16 l_u16offset = 0;
    for (l_u16i = 0; l_u16i < p_ScanPointNum; l_u16i++)
    {
        if (p_Width_Carve[l_u16i] == 0)
        {
            l_u16cnt++;
            if (l_u16cnt >= p_ZeroNum)
            {
                return l_u16i;
            }
        }
        else
        {
            l_u16cnt = 0;
        }
    }
    return 0;
}

/*************************************************
Function		:	Find_StaEnd_Point_Rough
Description		:	找到靶标的起始点和结束点
Calls			:	无
Called By		: 	TaskTargetApp
Table Accessed	: 	无
Table Updated	:	无
Input			:	Width_Carve : 切后脉宽首地址
                    p_ScanPointNum : 一圈点数
Output			:	无
Return			:	l_u16offset:计算起始点
Others			:	无
*************************************************/
u8 Find_StaEnd_Point_Rough(u16 p_u16offset,
                           u16* p_Width_Carve,
                           u8 p_u8MoveBit,
                           u16* p_u16start,
                           u16* p_u16end,
                           u8 p_u8Least_DotCnt,
                           u8 varargin)
{
    u8 l_u8cnt = 0;
    u8 l_u8n = 0;
    u8 l_u8m = 0;
    u8 l_u8rem = 0;
    u8 l_u8MaxMarkNum = 0;
    u8 l_u8markflag = 0;
    u16 l_u16i = 0;
    u16 l_u16i_cyc = 0;
    u16 l_u16cnt_nonzero = 0;
    u16 l_u16cnt_zero = 0;

    l_u8MaxMarkNum = 1 << p_u8MoveBit;

    for (l_u16i = p_u16offset; l_u16i < (g_u16ScanPointNum + p_u16offset);
         l_u16i++)  //寻找每个靶的起点
    {
        l_u16i_cyc = l_u16i % g_u16ScanPointNum;
        if (p_Width_Carve[l_u16i_cyc] != 0)
        {
            l_u16cnt_nonzero++;  //脉宽非0点计数
            if ((l_u8markflag == 0) && (l_u16cnt_nonzero >= p_u8Least_DotCnt))
            {
                l_u8markflag = 1;  //靶标标志

                // p_u16start[l_u8cnt]范围[p_u16offset ~g_u16ScanPointNum + p_u16offset]

                Save_MarkBreakPoint(l_u8cnt, p_u8MoveBit, l_u16i, l_u16cnt_nonzero, &p_u16start[0]);

                l_u8cnt++;  //靶标个数
                            //				if(l_u8cnt > 1)
                            //				{
                            //				l_u8cnt = l_u8cnt;
                            //				}
            }
            l_u16cnt_zero = 0;  //脉宽零点计数清零
        }
        else
        {
            l_u16cnt_zero++;       //脉宽零点计数
            l_u16cnt_nonzero = 0;  //脉宽非零点计数清零

            if ((l_u8markflag == 1)
                && (l_u16cnt_zero
                    >= varargin))  //认为连续有10个零点,才认为是两个靶,否则当一个靶处理
            {
                l_u8markflag = 0;

                // p_u16end[l_u8cnt]范围[p_u16offset ~g_u16ScanPointNum + p_u16offset]
                Save_MarkBreakPoint(
                    l_u8cnt - 1, p_u8MoveBit, l_u16i - 1, l_u16cnt_zero, &p_u16end[0]);
            }
        }
    }
    return l_u8cnt;
}

u8 Save_MarkBreakPoint(u8 l_u8cnt, u8 p_u8MoveBit, u16 l_u16i, u16 l_u16cnt, u16* p_u16res)
{
    u8 l_u8MaxMarkNum = 0;
    u8 l_u8n = 0;
    u8 l_u8m = 0;
    u8 l_u8rem = 0;
    u8 l_u8Offset = 0;
    l_u8MaxMarkNum = 1 << p_u8MoveBit;
    if (l_u8cnt < l_u8MaxMarkNum)
    {
        p_u16res[l_u8cnt] = l_u16i - l_u16cnt + 1;  //寻找疑似靶标的脉宽非零起始点或结束点
    }
    else  //一圈多余32个靶标，则后面靶标剔除前面部分靶标
    {
        //踢靶原则: 更新N*((l_u8cnt/32) + 1)的位置靶标
        l_u8n = (l_u8cnt >> p_u8MoveBit) + 1;  //求踢靶位置的公因数
        l_u8m = (l_u8n - 1) << p_u8MoveBit;
        l_u8rem = l_u8cnt % l_u8n;  //当靶标序号为公因数的整数倍时,更新靶标
        if (l_u8rem == 0)
        {
            l_u8Offset = l_u8cnt - l_u8m;
            if ((l_u8Offset == 0) || (l_u8Offset == (l_u8MaxMarkNum - 1)))
            {
                return 0;
            }
            p_u16res[l_u8Offset] = l_u16i - l_u16cnt + 1;  //寻找疑似靶标的脉宽非零起始点或结束点
        }
    }

    return 0;
}
/*************************************************
Function		:	Find_MaxWidthPoint
Description		:	对切后脉宽进行查找,寻找每个靶标的最大脉宽点
Calls			:	无
Called By		: 	TaskTargetApp
Table Accessed	: 	无
Table Updated	:	无
Input			:	Width_Carve : 切后脉宽首地址
Output			:	Max_Width_Point :存入最大脉宽对应的刻度的首地址
Return			:	无
Others			:	无
*************************************************/
u8 Find_MaxWidthPoint(u16* Width_Carve, STRUCT_FILTER_TARGET* p_Filter)
{
    u8 l_u8cnt = 0;
    u16 l_u16start[32] = {0};
    u16 l_u16end[32] = {0};
    u16 l_u16offset = 0;
    u8 l_u8Least_DotCnt = 4 >> (g_u8AngMode - 1);
    u8 l_u8MoveBit = 5;
    u8 l_u8MaxMarkNum = 1 << l_u8MoveBit;

    l_u16offset = Find_ScanData_StaPoint(
        g_u16ScanPointNum, Width_Carve, 30);  //寻找扫描起始点,防止靶标被0点分割
    l_u8cnt = Find_StaEnd_Point_Rough(l_u16offset,
                                      Width_Carve,
                                      l_u8MoveBit,
                                      &l_u16start[0],
                                      &l_u16end[0],
                                      l_u8Least_DotCnt,
                                      20);  //粗查找靶标起始点结束点

    Get_MaxWidth(l_u8MaxMarkNum,
                 l_u8cnt,
                 &l_u16start[0],
                 &l_u16end[0],
                 Width_Carve,
                 p_Filter);  //寻找靶上的最大点
    return l_u8cnt;
}

u8 Find_MaxWidthPoint1(u16* Width_Carve, STRUCT_FILTER_TARGET* p_Filter, STRUCT_CARVE* p_CraveBuf)
{
    u8 l_u8cnt = 0;
    u16* l_u16start = NULL;  //&p_CraveBuf->m_u16MarkSta[0];
    u16* l_u16end = NULL;    //&p_CraveBuf->m_u16MarkEnd[0];
    u16 l_u16offset = 0;
    u8 l_u8Least_DotCnt = 4 >> (g_u8AngMode - 1);
    u8 l_u8MoveBit = 5;
    u8 l_u8MaxMarkNum = 1 << l_u8MoveBit;
    u16 l_u16PointDiff = 0;
    u8 l_u8MarkOffset = 0;
    //	l_u16offset = Find_ScanData_StaPoint(g_u16ScanPointNum, Width_Carve,30);
    ////寻找扫描起始点,防止靶标被0点分割 	l_u8cnt = Find_StaEnd_Point_Rough(l_u16offset
    ///,Width_Carve
    //,l_u8MoveBit , &l_u16start[0], &l_u16end[0] , l_u8Least_DotCnt,20);	//粗查找靶标起始点结束点
    u32 l_u8MarkSize = p_CraveBuf->m_u32MarkNum;
    if ((p_CraveBuf->m_u16MarkSta[0] - p_CraveBuf->m_u16MarkEnd[l_u8MarkSize - 1]
         + g_u16ScanPointNum)
        <= MARK_COUNTIUEZERO_NUM)
    {
        p_CraveBuf->m_u16MarkSta[0] = p_CraveBuf->m_u16MarkSta[l_u8MarkSize - 1];
        p_CraveBuf->m_u16MarkEnd[0] += g_u16ScanPointNum;
        l_u8MarkSize -= 1;
        l_u8MarkOffset = 0;
    }
    else if (l_u8MarkSize == TARGET_MAX)
    {
        l_u8MarkOffset = 1;
        l_u8MarkSize -= 1;
    }
    l_u16start = &p_CraveBuf->m_u16MarkSta[l_u8MarkOffset];
    l_u16end = &p_CraveBuf->m_u16MarkEnd[l_u8MarkOffset];
    Get_MaxWidth(l_u8MaxMarkNum,
                 l_u8MarkSize,
                 l_u16start,
                 l_u16end,
                 Width_Carve,
                 p_Filter);  //寻找靶上的最大点
    return l_u8MarkSize;
}
/*************************************************
Function		:	Get_MaxWidth
Description		:	在起始与结束点间找到脉宽最大点
Calls			:	无
Called By		: 	TaskTargetApp
Table Accessed	: 	无
Table Updated	:	无
Input			:	l_u8MaxMarkNum : 允许存放最多的靶标个数
                    p_MarkNum : 找到的靶标个数
                    l_u16start :存放靶标起始点首地址
                    l_u16end :存放靶标结束点首地址
                    Width_Carve :切后脉宽首地址
Output			:	Max_Width_Point :存入最大脉宽对应的刻度的首地址
Return			:	无
Others			:	无
*************************************************/
void Get_MaxWidth(u8 p_u8MaxMarkNum,
                  u8 p_MarkNum,
                  u16* p_u16start,
                  u16* p_u16end,
                  u16* p_Width_Carve,
                  STRUCT_FILTER_TARGET* p_Filter)
{
    u16 l_u16i = 0, l_u16j = 0;
    u16 l_u16WidthOld = 0;
    u16 l_u16offset = 0;
    u16 l_u16MarkAng = 0;
    if (p_MarkNum >= p_u8MaxMarkNum)
        p_MarkNum = p_u8MaxMarkNum;

    for (l_u16i = 0; l_u16i < p_MarkNum; l_u16i++)
    {
        l_u16WidthOld = p_Width_Carve[p_u16start[l_u16i]];
        l_u16MarkAng = p_u16start[l_u16i];
        for (l_u16j = p_u16start[l_u16i]; l_u16j <= p_u16end[l_u16i]; l_u16j++)
        {
            l_u16offset = l_u16j % g_u16ScanPointNum;

            if (p_Width_Carve[l_u16offset]
                >= l_u16WidthOld)  //若改为没有等号,会有什么影响,待测试(因为光斑不均匀)
            {
                l_u16WidthOld = p_Width_Carve[l_u16offset];
                l_u16MarkAng = l_u16offset;
            }
        }
        p_Filter->m_StructMarkInfo[l_u16i].m_u16Ang = l_u16MarkAng;
    }
}

/*************************************************
Function		:	Find_80Percent_Sta_Point
Description		:	寻找80%脉宽点(起始点)
Calls			:	无
Called By		: 	Find_StaEnd_Point_Slope
Table Accessed	: 	无
Table Updated	:	无
Input			:	Width_Aver : 滑动平均后的脉宽值首地址
                    Max_Width_Point : 靶标的脉宽最大点
Output			:	无
Return			:	l_u16j :80%脉宽点(起始点)
Others			:	无
*************************************************/
u16 Find_80Percent_Sta_Point(u16* Width_Aver, u16 Max_Width_Point)
{
    u16 l_u16j = Max_Width_Point;
    u16 l_u16Cnt = 0;
    u16 l_u16Width_80Percent = 0;
    u16 l_u16MarkMaxPointNum_Half = g_u16MarkMaxPointNum >> 1;

    l_u16Width_80Percent = (Width_Aver[Max_Width_Point] << 2) / 5;  //最大脉宽的80%
    while (1)
    {
        if (Width_Aver[l_u16j] < l_u16Width_80Percent)  //向前查找80%脉宽点
        {
            return l_u16j;
        }
        else
        {
            l_u16Cnt++;
            l_u16j = (l_u16j - 1 + g_u16ScanPointNum) % g_u16ScanPointNum;
        }

        if (l_u16Cnt >= l_u16MarkMaxPointNum_Half)  //靶标太宽不要
        {
            return Max_Width_Point;
        }
    }
}

/*************************************************
Function		:	Find_80Percent_End_Point
Description		:	寻找80%脉宽点(结束点)
Calls			:	无
Called By		: 	Find_StaEnd_Point_Slope
Table Accessed	: 	无
Table Updated	:	无
Input			:	Width_Aver : 滑动平均后的脉宽值首地址
                    Max_Width_Point : 靶标的脉宽最大点
Return			:	l_u16j :80%脉宽点(结束点)
Others			:	无
*************************************************/
u16 Find_80Percent_End_Point(u16* Width_Aver, u16 Max_Width_Point)
{
    u16 l_u16j = Max_Width_Point;
    u16 l_u16Cnt = 0;
    u16 l_u16Width_80Percent = 0;
    u16 l_u16MarkMaxPointNum_Half = g_u16MarkMaxPointNum >> 1;

    l_u16Width_80Percent = (Width_Aver[Max_Width_Point] << 2) / 5;
    while (1)
    {
        if (Width_Aver[l_u16j] < l_u16Width_80Percent)  //向前查找80%脉宽点
        {
            return l_u16j;
        }
        else
        {
            l_u16Cnt++;
            l_u16j = (l_u16j + 1) % g_u16ScanPointNum;
        }

        if (l_u16Cnt >= l_u16MarkMaxPointNum_Half)  //靶标太宽不要
        {
            return Max_Width_Point;
        }
    }
}

/*************************************************
Function		:	Find_Slope_Inflection_Point
Description		:	寻找脉宽拐点
Calls			:	无
Called By		: 	Find_StaEnd_Point_Slope
Table Accessed	: 	无
Table Updated	:	无
Input			:	p_Start_Point : 靶标起始结束点
                    p_Max_Width_Point : 靶标的脉宽最大点
                    Width_Aver :滑动平均后的脉宽
                    p_type :查找斜率的类型(起始=1,结束= -1)
Output			:	无
Return			:	l_f32StartPoint : 起始结束点(80%脉宽与斜率拐点平均值,float)
Others			:	无
*************************************************/
u16 Find_Slope_Inflection_Point(u16 p_StaEnd_Point,
                                u16 p_Max_Width_Point,
                                u16* Width_Aver,
                                s8 p_type)
{
    u16 l_u16StaEndPoint = 0;
    u16 l_u16i = 0, l_u16SearchPoint_Sta = 0, l_u16SearchPoint_End = 0;
    s16 l_s16SlopeOld, l_s16Slope;  //脉宽斜率
    u16 l_u16Slope_Point_1 = 0, l_u16Slope_Point_2 = 0;

    s16 l_s16Slope_Infleaction = 200 * p_type;

    l_u16StaEndPoint = p_StaEnd_Point;
    if (p_type > 0)  //找起始点
    {
        l_u16SearchPoint_Sta = l_u16StaEndPoint;
        l_u16SearchPoint_End = p_Max_Width_Point;
    }
    else  //找结束点
    {
        l_u16SearchPoint_Sta = p_Max_Width_Point;
        l_u16SearchPoint_End = l_u16StaEndPoint;
    }

    l_s16SlopeOld = Width_Aver[(l_u16SearchPoint_Sta + 1) % g_u16ScanPointNum]
                    - Width_Aver[l_u16SearchPoint_Sta];  //计算初始斜??
    if (l_u16SearchPoint_Sta > l_u16SearchPoint_End)     //查找部分被0点分割
    {
        l_u16SearchPoint_End = l_u16SearchPoint_End + g_u16ScanPointNum;
        if (p_type
            < 0)  //查找结束点的时候,如果最大脉宽在结束点后面,即被零点分割,需将结束点平移至最大脉宽后面.起始点不需要增加一圈扫描点数
        {
            l_u16StaEndPoint = l_u16StaEndPoint + g_u16ScanPointNum;
        }
    }

    for (l_u16i = l_u16SearchPoint_Sta; l_u16i <= l_u16SearchPoint_End; l_u16i++)
    {
        l_u16Slope_Point_1 = (l_u16i + 1) % g_u16ScanPointNum;
        l_u16Slope_Point_2 = (l_u16i + 2) % g_u16ScanPointNum;

        l_s16Slope = Width_Aver[l_u16Slope_Point_2] - Width_Aver[l_u16Slope_Point_1];
        if ((l_s16Slope <= l_s16Slope_Infleaction) && (l_s16SlopeOld > l_s16Slope_Infleaction))
        {
            return (l_u16StaEndPoint + l_u16i + 1) >> 1;  //可能>7199的值,80%脉宽与斜率拐点平均值
        }
        else
        {
            l_s16SlopeOld = l_s16Slope;
        }
    }
    return l_u16StaEndPoint;
}
/*************************************************
Function		:	Find_StaEnd_Point_Slope
Description		:	根据斜率寻找靶标的起始点和结束点
Calls			:	无
Called By		: 	TaskTargetApp
Table Accessed	: 	无
Table Updated	:	无
Input			:	Width_Aver : 滑动平均后的脉宽值首地址
                    p_MarkNum : 疑似靶标的总个数
                    Max_Width_Point :疑似靶标最大脉宽处的扫描刻度首地址
                    p_Filter :存放靶标数据各信息的buf首地址
                    dist :扫描数据-距离首地址
                    Width :扫描数据-脉宽首地址
Output			:	p_Filter ->m_u16Ang:存放靶标中心刻度
                    p_Filter ->m_u32Dist:存放靶标中心的扫描距离
Return			:	无
Others			:	无
*************************************************/
u32 Find_StaEnd_Point_Slope(u16* Width_Aver,
                            u8 p_MarkNum,
                            STRUCT_FILTER_TARGET* p_Filter,
                            u16* dist,
                            u16* Width)
{
    s8 l_s8Point_Type = 0;  //起始点 =1 ,结束点=-1;
    u16 l_u16i = 0;
    u16 l_u16Max_Width_Point = 0;
    u16 l_u16Start = 0, l_u16End = 0;
    u16 l_u16Ang = 0;
    //	float l_f32StartPoint = 0,l_f32EndPoint = 0;

    for (l_u16i = 0; l_u16i < p_MarkNum; l_u16i++)
    {
        //起始点
        l_s8Point_Type = 1;
        l_u16Max_Width_Point = p_Filter->m_StructMarkInfo[l_u16i].m_u16Ang;
        p_Filter->m_StructMarkInfo[l_u16i].m_u16ScanSta =
            Find_80Percent_Sta_Point(Width_Aver, l_u16Max_Width_Point);  //寻找80%脉宽起始点
        l_u16Start = Find_Slope_Inflection_Point(p_Filter->m_StructMarkInfo[l_u16i].m_u16ScanSta,
                                                 l_u16Max_Width_Point,
                                                 Width_Aver,
                                                 l_s8Point_Type);  //找到脉宽斜率的拐点
        //结束点
        l_s8Point_Type = -1;
        p_Filter->m_StructMarkInfo[l_u16i].m_u16ScanEnd =
            Find_80Percent_End_Point(Width_Aver, l_u16Max_Width_Point);  //寻找80%脉宽结束点
        l_u16End = Find_Slope_Inflection_Point(p_Filter->m_StructMarkInfo[l_u16i].m_u16ScanEnd,
                                               l_u16Max_Width_Point,
                                               Width_Aver,
                                               l_s8Point_Type);  //找到脉宽斜率的拐点
        if (l_u16Start > l_u16End)
            l_u16End += g_u16ScanPointNum;  //求靶标中间点((sta+end)/2),加1是向上取整

        p_Filter->m_StructMarkInfo[l_u16i].m_u16ScanSta = l_u16Start;
        p_Filter->m_StructMarkInfo[l_u16i].m_u16ScanEnd = l_u16End;
        p_Filter->m_StructMarkInfo[l_u16i].m_u16Ang =
            ((l_u16Start + l_u16End) >> 1) % g_u16ScanPointNum;
        p_Filter->m_StructMarkInfo[l_u16i].m_u16Dist =
            dist[p_Filter->m_StructMarkInfo[l_u16i].m_u16Ang];
    }
    return 0;
}

/*************************************************
Function		: Load_Marks
Description		: 导入AGV所处层的靶标
Calls			: 无
Called By		: 无
Table Accessed	: 无
Table Updated	: 无
Input			: p_LayerAddr:靶标分层信息, p_RobPose:用于获取agv所处层号,p_MarkSet:
靶标读出存放首地址 Output			: 无 Return			: 无 Others			: 无
*************************************************/
u8 Load_Marks(MARKLAYER_ADDRESS* p_LayerAddr, u16 p_Layer, MARK_SETS* p_MarkSet)
{
    u16 l_u16clc, l_u16i, l_u16tmp, l_u16size;
    u8 l_u8flag = 0;
    u32 l_u32Addr;
    // Flash_Read_HalfWord(ADDR_FLASH_MARK_OFFSET, STRUCT_SIZE_MARKLAYER_ADDRESS>>1,
    // (u16*)p_LayerAddr);
    l_u16clc = (p_LayerAddr->m_u16InfoPackNum) ^ (p_LayerAddr->m_u16LayerNum)
               ^ (p_LayerAddr->m_u16MarkTotalNum);
    if ((p_LayerAddr->m_u16clc == l_u16clc) && (p_LayerAddr->m_u16MarkTotalNum != 0)
        && (p_LayerAddr->m_u16MarkTotalNum != 0xffff)  //判断是否校验成功,若不成功则不读靶标
    )
    {
        for (l_u16i = 0; l_u16i < p_LayerAddr->m_u16LayerNum; l_u16i++)
        {
            l_u16tmp = p_LayerAddr->m_u16Layer[l_u16i];
            if (l_u16tmp == p_Layer)  //找到上一次定位所在层号
            {
                g_sMarkSets.m_u16layer = l_u16tmp;
                l_u8flag = 1;
                break;
            }
        }

        if (l_u8flag)
        {
            memset(p_MarkSet, 0, STRUCT_SIZE_MARK_SETS);
            if (p_LayerAddr->m_u16LayerNum > 1)
            {
                if (l_u16i == (p_LayerAddr->m_u16LayerNum - 1))
                    l_u16size = p_LayerAddr->m_u16MarkTotalNum - p_LayerAddr->m_u16Point[l_u16i]
                                + 1;  //该层靶标个数
                else
                    l_u16size =
                        p_LayerAddr->m_u16Point[l_u16i + 1] - p_LayerAddr->m_u16Point[l_u16i];
            }
            else
                l_u16size = p_LayerAddr->m_u16MarkTotalNum;

            if (l_u16size > MAX_SIZE)
                l_u16size = MAX_SIZE;
            //读取靶标信息
            l_u32Addr = ADDR_FLASH_MARK_INFO + 12 * (p_LayerAddr->m_u16Point[l_u16i] - 1);
            // Flash_Read_HalfWord(l_u32Addr, l_u16size * 6, (u16*)&p_MarkSet->m_sMarkSets[0]);
            // //每个靶标占6个半字
            p_MarkSet->m_u16size = l_u16size;                         //该层靶标的个数
            p_MarkSet->m_u16layer = p_LayerAddr->m_u16Layer[l_u16i];  //层号
            p_MarkSet->m_u8flag = 1;                                  //已经读取靶标标志

            return 0;
        }
    }
    return 1;
}

void Find_ScanMark(u16* p_pulse, u16* p_pulse_aver, u16* p_dist, float p_CorrTime)
{
    OS_CPU_SR cpu_sr;

    Empty_UsingBuf();  // 70us

    //滑动平均
    Moving_Average(p_pulse, p_pulse_aver, 20, g_u16ScanPointNum);  // 1700us

    //寻找靶标脉宽最大点
    g_sFilterDist.m_u8In =
        Find_MaxWidthPoint1(p_pulse, &g_sFilterDist, g_sPulseCarveBufCopy);  // 71us
    // g_sFilterDist.m_u8In = Find_MaxWidthPoint(p_pulse01,&g_sFilterDist);//	2428us

    //寻找靶标起始和结束点
    Find_StaEnd_Point_Slope(
        p_pulse_aver, g_sFilterDist.m_u8In, &g_sFilterDist, p_dist, p_pulse);  // 162us

    //筛选真假靶标(靶标上点个数)
    Filter_FakeMark_ByPointNum(g_sFilterDist.m_u8In, &g_sFilterDist);  // 3
    Filter_SameMark_BySpeedRad(&g_sFilterDist);

    Filter_Mark_ByScanRadius(&g_sFilterDist);  // 1

    //		PrintfScanMarks(&g_sFilterDist);//15

    CodeZeroCorrect(&g_sFilterDist);                 // 3
    CopyFilterMark(&g_sFilterDist, &g_sFilterDist);  //修正圆靶距离 2

    if (g_sSysPib.m_u16WorkMode_NavOrMappingOrMark
        == SYSMODE_NavOrMappingOrMark_NAV)  //速度修正 265
    {
        memcpy(&g_sFilterDist.m_StructMarkInfoNoCorr,
               &g_sFilterDist.m_StructMarkInfo,
               STRUCT_SIZE_MARK_INFO * g_sFilterDist.m_u8In);
        CalAngOffsetBySpeed360TOHalf(
            &g_sFilterDist, &g_sSavePosCur, &g_sInSpeedUsed, p_CorrTime - g_f32ScanTime_HalfPer);
    }

    Find_MaxMin_Dist(g_sFilterDist.m_u8In,
                     p_dist,
                     &g_sFilterDist,
                     &g_au16max_dist[0],
                     &g_au16min_dist[0]);  // 60

    //	OS_ENTER_CRITICAL();
    for (u8 i = 0; i < g_sFilterDist.m_u8In; i++)
    {
        g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_StructMarkInfo[i] =
            &g_sFilterDist.m_StructMarkInfo[i];
        g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_sXy2Robot[i] =
            &g_sFilterDist.m_sXy2Robot[i];
    }
    //	memcpy((void *)&g_sFilterMark_SpeedCorr.m_StructMarkScanInfo.m_StructMarkInfo[0],(void
    //*)&g_sFilterDist.m_StructMarkInfo[0],STRUCT_SIZE_MARK_INFO*g_sFilterDist.m_u8In);
    g_sFilterMark_SpeedCorr.m_StructMarkScanInfoAddr.m_u8In = g_sFilterDist.m_u8In;
    //	OS_EXIT_CRITICAL();
}

/**
 * l_u8cnt：靶标个数
 * Dist： 各个点的距离首指针
 * p_Filter： 靶标段信息
 *
 * max_dist： 被操作的值，输出
 * min_dist：
 */
void Find_MaxMin_Dist(u8 l_u8cnt,
                      u16* Dist,
                      STRUCT_FILTER_TARGET* p_Filter,
                      u16* max_dist,
                      u16* min_dist)
{
    u16 l_u16i = 0, l_u16DistOld_max = 0, l_u16DistOld_min = 0, l_u16j = 0;
    u16 l_u16Dist = 0;
    u16 l_u16tmp = 0;
    for (l_u16i = 0; l_u16i < l_u8cnt; l_u16i++)
    {
        l_u16DistOld_max = Dist[p_Filter->m_StructMarkInfo[l_u16i].m_u16ScanSta];
        l_u16DistOld_min = l_u16DistOld_max;

        max_dist[l_u16i] = l_u16DistOld_max;
        min_dist[l_u16i] = l_u16DistOld_min;
        for (l_u16j = p_Filter->m_StructMarkInfo[l_u16i].m_u16ScanSta + 1;
             l_u16j <= p_Filter->m_StructMarkInfo[l_u16i].m_u16ScanEnd;
             l_u16j++)
        {
            l_u16Dist = Dist[l_u16j % g_u16ScanPointNum];
            if (l_u16Dist > l_u16DistOld_max)  //距离
            {
                l_u16DistOld_max = l_u16Dist;
            }
            if ((l_u16Dist < l_u16DistOld_min) && (l_u16Dist != 0))
            {
                l_u16DistOld_min = l_u16Dist;
            }
        }
        max_dist[l_u16i] = l_u16DistOld_max;
        min_dist[l_u16i] = l_u16DistOld_min;
    }
}

u16 Get_Multi(u8 p_data, s8 power)
{
    u16 l_u16ret = p_data;
    if (power < 0)
        return 1;
    for (u8 i = 0; i < power; i++)
    {
        l_u16ret *= p_data;
    }
    return l_u16ret;
}

void UART_TEST(u8 p_Data_Recv, u8* p_u8data, u16 pRecvLen)
{
    static u8 l_u8len;
    static u8 l_firstflag = 0;
    static u16 l_u16LastLen;
    u8 l_u8err = 0;
    if ((p_Data_Recv == 0xff) && (l_firstflag == 0))  //帧头
    {
        l_u8len = 0;
        p_u8data[l_u8len] = p_Data_Recv;
        l_u16LastLen = pRecvLen;
        l_firstflag = 1;
        l_u8len++;
    }
    else if ((p_Data_Recv == 0XAA) && (l_firstflag == 1)
             && ((pRecvLen - 1) == l_u16LastLen))  //帧头
    {
        p_u8data[l_u8len] = p_Data_Recv;
        l_firstflag = 2;
        l_u8len++;
    }
    else if ((p_Data_Recv == 0xee) && (l_firstflag == 2))  //帧尾
    {
        p_u8data[l_u8len] = p_Data_Recv;
        l_u16LastLen = pRecvLen;
        l_firstflag = 3;
        l_u8len++;
    }
    else if ((p_Data_Recv == 0xee) && (l_firstflag == 3)
             && ((pRecvLen - 1) == l_u16LastLen))  //帧尾
    {
        p_u8data[l_u8len] = p_Data_Recv;
        l_firstflag = 0;
        l_u8len++;
        // OSFlagPost(g_flagUser, USR_FLAG_USART, OS_FLAG_SET, &l_u8err) ;
    }
    else if (l_u8len >= 2)  //接收的数据
    {
        p_u8data[l_u8len] = p_Data_Recv;
        l_u8len++;
    }
    else
    {
        l_u8len = 0;
        l_firstflag = 0;
    }
}
void UART_SAVEDATA(u8 p_Cnt, u8 p_DataRecv, u8 p_In)
{
    // g_sQueNetPC.m_u8Buf[p_In][p_Cnt]= p_DataRecv;
}

void UART_SendDATA(u8 p_Len, u8* p_SendData)
{
    // for(u8 i=0;i<p_Len;i++)
    // USART_SendData(USART1, p_SendData[i]) ;
}
