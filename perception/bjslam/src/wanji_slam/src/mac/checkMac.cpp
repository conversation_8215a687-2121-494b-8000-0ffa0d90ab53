/*
 * @Author: your name
 * @Date: 2021-07-20 22:40:04
 * @LastEditTime: 2023-12-05 22:38:19
 * @LastEditors: shuangquan han
 * @Description: In User Settings Edit
 * @FilePath: /zus/src/wanji_slam/include/FileUtils/checkMac.hpp
 */

#include "mac/checkMac.h"
#include "mac/AuthAlgo.h"
#include "mac/definetypes.h"
#include "yaml-cpp/yaml.h"
#include <unistd.h>
std::string readFileMacNo(std::string p_sFilePath);
std::string readMacFromYaml(std::string p_sYamlPath);
bool checkMacKey_(std::string p_sFromWebMacNo);
bool writeMacInYaml_(std::string p_sYamlPath, std::string p_sMacNo);

/**
 * @description: 读取文件并校验Mac是否解密
 * @param {string} p_sMacNo
 * @return {*}
 */
bool checkMacNoFromFile(std::string p_sFilePath)
{
    std::string l_sReadFileMacNo;
    if (access(p_sFilePath.c_str(), F_OK) != -1)
    {
        l_sReadFileMacNo = readFileMacNo(p_sFilePath);
    }
    else
    {
        l_sReadFileMacNo = getSecretNo();
    }
    return checkMacKey_(l_sReadFileMacNo);
}

/**
 * @description: 外部传递并校验Mac是否解密
 * @param {string} p_sMacNo 密钥许可证
 *  @param {string} p_sSecretNo 加密码
 * @return {bool} true 成功
 * @other:
 */
bool checkMacNoFromSet(std::string p_sFilePath, std::string p_sMacNo, std::string& p_sSecretNo)
{
    if (!checkMacKey_(p_sMacNo))
        return false;
    else
        p_sSecretNo = getSecretNo();
    return writeMacInYaml_(p_sFilePath, p_sMacNo);
}

/**
 * @description: 读取本地配置文件，获取mac码
 * @param {string} p_sFilePath
 * @return {*}
 */
std::string readFileMacNo(std::string p_sFilePath)
{
    return readMacFromYaml(p_sFilePath);
}

/**
 * @description: 读取Yaml,获取Mac码
 * @param {string} p_sYamlPath
 * @return {*}
 */
std::string readMacFromYaml(std::string p_sYamlPath)
{
    std::string l_sFileMac = "nomac";
    YAML::Node config = YAML::LoadFile(p_sYamlPath);
    if (config["License"])
    {
        if (config["License"]["SerialLicense"])
        {
            l_sFileMac = config["License"]["SerialLicense"].as<std::string>();
        }
    }
    return l_sFileMac;
}

/**
 * @description: 检验mac码 是否正确
 * @param {string} p_sFromWebMacNo
 * @return {*}
 */
bool checkMacKey_(std::string p_sFromWebMacNo)
{
    return true;
    char* tmp = const_cast<char*>(p_sFromWebMacNo.c_str());
    CAuthAlgo cauthAlgo;
    bool res = cauthAlgo.CheckAuthorityNo(tmp);
    return res;
}

/**
 * @description: 读取当前网卡Mac的地址，基于规则生成加密字符串
 * @param {*}
 * @return {*}
 */
std::string getSecretNo()
{
    CAuthAlgo cauthAlgo;
    return cauthAlgo.MakeSerialNo();
}

/**
 * @description: 写入Mac码至Yaml
 * @param {string} p_sYamlPath
 * @param {string} p_sMacNo
 * @return {*}
 */
bool writeMacInYaml_(std::string p_sYamlPath, std::string p_sMacNo)
{
    std::ofstream l_file(p_sYamlPath);  //有则打开，没有则创建
    if (l_file.is_open())
    {
        YAML::Node l_node;
        l_node["License"]["SerialLicense"] = p_sMacNo;
        l_file << l_node << std::endl;
        l_file.close();
        return true;
    }
    printf("[error] mac file open fail: %s\n", p_sYamlPath.c_str());
    return false;
}