<?xml version="1.0"?>
<launch>
    <rosparam file="$(find wj_slam)/config/user.yaml" command="load"/>
    <arg name="ip" default="************"/>  <!-- AGVIP地址server ip -->
    <arg name="port" default="20000"/> <!-- server port -->
    <arg name="request_delay" default="1"/> <!-- 0:预估是在上一次位姿预估,1:不预估，计算完发送位姿 request type -->
    <arg name="request_interval" default="20000"/> <!-- 请求位姿频率-us request interval us-->
    <arg name="pose_topic" default="/wanji_localization"/> <!-- localization msg topic -->
    <node name="wanji_client_node" pkg="comm_tcp" type="client_node" 
          args="$(arg ip) $(arg port) $(arg request_delay) $(arg request_interval) $(arg pose_topic)" output="screen"/>
</launch>
