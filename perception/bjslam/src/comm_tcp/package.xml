<?xml version="1.0"?>
<package>
  <name>comm_tcp</name>
  <version>0.0.1</version>
  <description>A package for communicating between ROS and other applications using TCP/IP protocol.</description>

  <maintainer email="<EMAIL>">A<PERSON><PERSON><PERSON></maintainer>


  <!-- One license tag required, multiple allowed, one license per tag -->
  <!-- Commonly used license strings: -->
  <license>MIT</license>


  <buildtool_depend>catkin</buildtool_depend>
  <build_depend>roscpp</build_depend>
  <build_depend>rospy</build_depend>
  <build_depend>std_msgs</build_depend>
  <run_depend>roscpp</run_depend>
  <run_depend>rospy</run_depend>
  <run_depend>std_msgs</run_depend>
  <build_depend>pcl_ros</build_depend>
  <run_depend>pcl_ros</run_depend>
  <build_depend>geometry_msgs</build_depend>
  <run_depend>geometry_msgs</run_depend>
  <build_depend>message_generation</build_depend>
  <!-- <build_export_depend>message_generation</build_export_depend> -->
  <run_depend>message_runtime</run_depend>
  <!-- The export tag contains other, unspecified, tags -->
  <export>
    <!-- Other tools can request additional information be placed here -->

  </export>
</package>
