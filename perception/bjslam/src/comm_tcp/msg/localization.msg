int8 STATUS_INVALID=-1
int8 STATUS_UNSTABLE=1
int8 STATUS_VALID=2

std_msgs/Header header
float64 utc_time
float64 x           # 单位 m
float64 y			# 单位 m
float32 heading     # 单位 度 UTM坐标系 y轴正方向为0,顺时针0~180，逆时针0~-180
float32 pitch 
float32 roll
float32 x_acc 
float32 y_acc
float32 z_acc 
float32 gyro_x
float32 gyro_y 
float32 gyro_z
float64 lat 
float64 lon 
float64 alt
float32 speed       # m/s
float32[2] position_stddev # x y坐标的精度单位m
float32 heading_stddev     # 航向角精度单位度
int8 sate_num
int8 status #15为正常 10为不好