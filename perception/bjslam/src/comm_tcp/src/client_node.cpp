/*************************************************************************
 * Author: A<PERSON><PERSON><PERSON>
 * Contact: <EMAIL>, <EMAIL>
 * Date: 28/06/2016
 *
 * This file contains source code to the client node of the ROS package
 * comm_tcp developed at LaRA (Laboratory of Robotics and Automation)
 * as part of my project during an internship from May 2016 - July 2016.
 *
 * (C) All rights reserved. LaRA, HEIG-VD, 2016 (http://lara.populus.ch/)
 ***************************************************************************/
#include <iostream>
#include "std_msgs/Int32.h"
#include "std_msgs/String.h"
#include <Eigen/Geometry>
#include <arpa/inet.h>
#include <errno.h>
#include <fcntl.h>
#include <fstream>
#include <mutex>
#include <nav_msgs/Odometry.h>
#include <nav_msgs/Path.h>
#include <netdb.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <ros/console.h>
#include <ros/ros.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <tf/transform_broadcaster.h>
#include <tf/transform_listener.h>
#include <thread>
#include <unistd.h>
#include <string>
#include <ros/package.h>

#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/common/transforms.h> 
#include "../../../../commonlibrary/src/coordinateTransformation/sensorAxisTransformation/sensorAxisTransformation.h"
#include "../../../../commonlibrary/src/coordinateTransformation/wgs84_utm.h"
#include "comm_tcp/localization.h"
#include "common_msgs/sensorgps.h"
#include "../../../../commonlibrary/src/common.h"

#include "calibLidarRFU2CarbackRFU.h"

std::ofstream g_fileNoKM;
std::ofstream g_fileHasKM;
// 基于当前系统的当前日期/时间
time_t now = time(0);
tm* ltm = localtime(&now);
// std::string g_sFileNoKMPath = "./" + std::to_string(ltm->tm_hour) + "-" +
// std::to_string(ltm->tm_min)
//                            + "-" + std::to_string(ltm->tm_sec) + "-720path_NoKM.csv";

// std::string g_sFileHasKMPath = "./" + std::to_string(ltm->tm_hour) + "-" +
// std::to_string(ltm->tm_min)
//                            + "-" + std::to_string(ltm->tm_sec) + "-720path_HasKM.csv";

void error(const char* msg)
{
    perror(msg);
    exit(0);
}

uint32_t getTimeMsec()
{
    static struct timeval start_Time;
    gettimeofday(&start_Time, NULL);
    static uint32_t start = start_Time.tv_sec * 1000 + start_Time.tv_usec / 1000;
    static struct timeval now_time;
    gettimeofday(&now_time, NULL);
    return uint32_t(now_time.tv_sec * 1000 + now_time.tv_usec / 1000) - start;
}

u_int32_t HexStr2U32(void* Str)
{
    u_int32_t tmp = 0;
    char* p = (char*)Str;
    while (1)
    {
        if (*p >= '0' && *p <= '9')
        {
            tmp *= 16;
            tmp += *p - '0';
        }
        else if (*p >= 'a' && *p <= 'f')
        {
            tmp *= 16;
            tmp += *p - 'a' + 10;
        }
        else if (*p >= 'A' && *p <= 'F')
        {
            tmp *= 16;
            tmp += *p - 'A' + 10;
        }
        else
        {
            break;
        }
        p++;
    }
    return tmp;
}
typedef struct AscProtocol
{
    char m_acCmdType[10];
    char m_acCmd[20];
    char m_acParam[14][20];
} s_AscProtocol;

class Listener {
  private:
    Eigen::Vector3d c_twist = Eigen::Vector3d::Zero();
    Eigen::Quaterniond c_w_quat = Eigen::Quaterniond::Identity();

    Eigen::Vector3d local_plan = Eigen::Vector3d::Zero();
    Eigen::Vector3d c_settingEulorRPYDegree = Eigen::Vector3d::Zero();
    ros::NodeHandle nh;

    ros::Subscriber OdomSub;
    ros::Publisher OdomPub;

    // ros::Subscriber StandardSub;
    ros::Publisher StandardPub;
    // ros::Subscriber UIposeSub;
    ros::Publisher UIposePub;

    ros::Subscriber UIscanSub;
    ros::Publisher UIscanPub;

    // 自己仿写的
    ros::Subscriber NavLocalplanSub;
    ros::Publisher NavLocalplanPub;
    std::mutex data_lock_;

    // pose from tcp transferred to ros type
    ros::Publisher localizationPub;
    ros::Publisher slamGPSPub;
    ros::Publisher slamGPSRandomPub;

    ros::Subscriber sub_imu;

    tf::TransformListener Tflistener;
    tf::TransformBroadcaster Tfbr;

    int c_iPort = 2112;
    char* c_pcServerIp = NULL;
    int c_isDelay = 1;
    int c_interval = 100000;
    std::string c_topicName = "";

    nav_msgs::Odometry c_odom;
    nav_msgs::Path c_local;

    std::ofstream outFile;
    int c_iTimeLoc = 0;

    int c_iPoseX = 0;
    int c_iPoseY = 0;
    int c_iPoseYaw = 0;

    double c_dMapPoseX = 0;
    double c_dMapPoseY = 0;
    double c_dMapPoseYaw = 0;
    std::mutex lock;

  public:
    bool c_bConnected = false;
    bool c_blocalPlan = false;
    int sockfd;
    sockaddr_in c_clientAddr_;
    sockaddr_in c_serverAddr_;
    bool c_bCreat = false;
    bool c_bPass = false;
    double l_dpose_X = 0;
    double l_dpose_Y = 0;
    double l_dpose_A = 0;
    int c_bFlag_ = 0;

    float c_lidarRFU2CarBackRFU_x;
    float c_lidarRFU2CarBackRFU_y;
    float c_lidarRFU2CarBackRFU_z;
    float c_lidarRFU2CarBackRFU_rollDegree;
    float c_lidarRFU2CarBackRFU_pitchDegree;
    float c_lidarRFU2CarBackRFU_yawDegree;

    double c_startingPoint_lonDegree;
    double c_startingPoint_latDegree;
    int c_startingPoint_UTM_Code;
    double c_startingPoint_UTM_x;
    double c_startingPoint_UTM_y;
    float c_startingPoint_altitude;
    float c_startingPoint_rollDegree;
    float c_startingPoint_pitchDegree;
    float c_startingPoint_yawDegree;
    std::vector<double> c_vStartingPoint_UTM;
    Eigen::Vector3d c_startingPoint_EulerDegrees;

    Common m_common;
    std::deque<common_msgs::sensorgps::ConstPtr> c_gpsDeque_; /**< gps */
    std::mutex c_gpsMutex;
    SensorAxisTransformation c_sensorAxisTransformation;
    Eigen::Affine3f m_lidarRFU2CarBackRFUTransform;
    Eigen::Affine3f m_lidarFLU2LidarRFUTransform;
    std::string c_mapSavePath;
    std::string c_mapSaveName = "mappingSLAM.csv";
    fstream c_mapFile;
    std::vector<std::vector<double>> c_vMappingTrajectory;
    bool c_isCalib = false;
    bool c_isSaveSLAMTrakectory = false;
    std::thread c_mapSaveThread;
    std::mutex c_mapSaveMutex;
    double c_aInitialLidarRFU2CarbackRFUTransform[6];
    double c_aLidarRFU2CarbackRFUTransformParamOpt[7];
    fstream c_mapTrajectoryFile;


    Eigen::Affine3f m_optLidarRFU2CarBackRFUTransform;
    ceres::Problem c_ceresProblem;


    boost::shared_ptr<common_msgs::sensorgps> c_pCurGPS;

    Listener(char* p_pcServerIp,
             int p_iPort,
             int p_isDelay,
             int p_interval,
             std::string p_topicName)
        : c_pcServerIp(p_pcServerIp), c_iPort(p_iPort), c_isDelay(p_isDelay),
          c_interval(p_interval), c_topicName(p_topicName)
    {
        Init_();
    }

    ~Listener(){
        saveMapTrajectory();
        if(c_mapFile.is_open()){
            c_mapFile.close();
        }

        if(c_mapTrajectoryFile.is_open())
        {
            c_mapTrajectoryFile.close();
        }

        if(c_isCalib){
            // Configure Ceres Solver options
            ceres::Solver::Options options;
            options.linear_solver_type = ceres::DENSE_NORMAL_CHOLESKY;
            options.num_threads = 100;
            // options.max_num_iterations = 2;
            // Δcost/meanDev <= function_tolerance时停止求解
            options.function_tolerance = 1e-8;  // 1e-6
            // 信任域步长(trust region step)相对减少的最小值。
            options.min_relative_decrease = 1e-6;  // 1e-3
            options.minimizer_progress_to_stdout = true;
            options.update_state_every_iteration = true;
            options.gradient_tolerance = 1e-10;
            options.parameter_tolerance = 1e-8;

            // Run the optimization
            ceres::Solver::Summary summary;
            ceres::Solve(options, &c_ceresProblem,&summary);

            // std::cout << summary.BriefReport() << "\n";
            std::cout << summary.FullReport() << "\n";
            // Output optimized Lidar to CarbackRFU transformation
            Eigen::Quaterniond quaternionOpt{c_aLidarRFU2CarbackRFUTransformParamOpt[3],c_aLidarRFU2CarbackRFUTransformParamOpt[0],
                    c_aLidarRFU2CarbackRFUTransformParamOpt[1],c_aLidarRFU2CarbackRFUTransformParamOpt[2]};
            quaternionOpt.normalize();
            Eigen::Matrix3d rx = quaternionOpt.matrix();
            double rollOpt,pitchOpt,yawOpt;
            toEulerAngle(quaternionOpt, rollOpt, pitchOpt,yawOpt );

            std::cout << "Optimized Lidar to CarbackRFU Transformation:\n";
            std::cout << "X: " << c_aLidarRFU2CarbackRFUTransformParamOpt[4] << ", Y: " << c_aLidarRFU2CarbackRFUTransformParamOpt[5]
                        << ", Z: " << c_aLidarRFU2CarbackRFUTransformParamOpt[6] << ", Roll: " << rollOpt * 180 / M_PI
                        << ", Pitch: " << pitchOpt* 180 / M_PI << ", Yaw: " << yawOpt* 180 / M_PI << "\n";
            
            std::cout << "initial X: " << c_aInitialLidarRFU2CarbackRFUTransform[0] << ", Y: " << c_aInitialLidarRFU2CarbackRFUTransform[1]
                << ", Z: " << c_aInitialLidarRFU2CarbackRFUTransform[2] << ", Roll: " << c_aInitialLidarRFU2CarbackRFUTransform[3]
                << ", Pitch: " << c_aInitialLidarRFU2CarbackRFUTransform[4] << ", Yaw: " << c_aInitialLidarRFU2CarbackRFUTransform[5] << "\n";
        }
        
    }

    void Init_()
    {
        // NavLocalplanSub =
        //     nh.subscribe("/boomove_base/BooCaxPlaner/local_plan", 10,
        //     &Listener::NavlocalplanCallback, this);
        // NavLocalplanPub = nh.advertise<nav_msgs::Path>("/boomove_base/BooCaxPlaner/local_plan",
        // 10);

        // 加载参数
        c_lidarRFU2CarBackRFU_x = 0.0;
        c_lidarRFU2CarBackRFU_y = 0.0;
        c_lidarRFU2CarBackRFU_z = 0.0;
        c_lidarRFU2CarBackRFU_rollDegree = 0.0;
        c_lidarRFU2CarBackRFU_pitchDegree = 0.0;
        c_lidarRFU2CarBackRFU_yawDegree = 0.0;

        c_startingPoint_lonDegree = 0.0;
        c_startingPoint_latDegree = 0.0;
        c_startingPoint_UTM_Code = 50;
        c_startingPoint_UTM_x = 0.0;
        c_startingPoint_UTM_y = 0.0;
        c_startingPoint_altitude = 0.0;
        c_startingPoint_rollDegree = 0.0;
        c_startingPoint_pitchDegree = 0.0;
        c_startingPoint_yawDegree = 0.0;

        c_pCurGPS.reset(new common_msgs::sensorgps());

        nh.param("/LaserConfig/client/isCalib", c_isCalib, c_isCalib);
        nh.param("/LaserConfig/client/isSaveSLAMTrakectory", c_isSaveSLAMTrakectory, c_isSaveSLAMTrakectory);
        nh.param("/LaserConfig/localization/lidarRFU2CarBackRFU_x", c_lidarRFU2CarBackRFU_x, c_lidarRFU2CarBackRFU_x);
        nh.param("/LaserConfig/localization/lidarRFU2CarBackRFU_y", c_lidarRFU2CarBackRFU_y, c_lidarRFU2CarBackRFU_y);
        nh.param("/LaserConfig/localization/lidarRFU2CarBackRFU_z", c_lidarRFU2CarBackRFU_z, c_lidarRFU2CarBackRFU_z);
        nh.param("/LaserConfig/localization/lidarRFU2CarBackRFU_rollDegree", c_lidarRFU2CarBackRFU_rollDegree, c_lidarRFU2CarBackRFU_rollDegree);
        nh.param("/LaserConfig/localization/lidarRFU2CarBackRFU_pitchDegree", c_lidarRFU2CarBackRFU_pitchDegree, c_lidarRFU2CarBackRFU_pitchDegree);
        nh.param("/LaserConfig/localization/lidarRFU2CarBackRFU_yawDegree", c_lidarRFU2CarBackRFU_yawDegree, c_lidarRFU2CarBackRFU_yawDegree);
        nh.param("/LaserConfig/localization/startingPoint_lonDegree", c_startingPoint_lonDegree, c_startingPoint_lonDegree);
        nh.param("/LaserConfig/localization/startingPoint_latDegree", c_startingPoint_latDegree, c_startingPoint_latDegree);
        nh.param("/LaserConfig/localization/startingPoint_altDegree", c_startingPoint_altitude, c_startingPoint_altitude);
        nh.param("/LaserConfig/localization/startingPoint_UTM_Code", c_startingPoint_UTM_Code, c_startingPoint_UTM_Code);
        nh.param("/LaserConfig/localization/startingPoint_UTM_x", c_startingPoint_UTM_x, c_startingPoint_UTM_x);
        nh.param("/LaserConfig/localization/startingPoint_UTM_y", c_startingPoint_UTM_y, c_startingPoint_UTM_y);
        nh.param("/LaserConfig/localization/startingPoint_rollDegree", c_startingPoint_rollDegree, c_startingPoint_rollDegree);
        nh.param("/LaserConfig/localization/startingPoint_pitchDegree", c_startingPoint_pitchDegree, c_startingPoint_pitchDegree);
        nh.param("/LaserConfig/localization/startingPoint_yawDegree", c_startingPoint_yawDegree, c_startingPoint_yawDegree);
        c_startingPoint_yawDegree = c_startingPoint_yawDegree < 0 ? c_startingPoint_yawDegree + 360 : c_startingPoint_yawDegree;
        c_startingPoint_yawDegree = c_startingPoint_yawDegree >= 360 ? c_startingPoint_yawDegree - 360 : c_startingPoint_yawDegree;

        if(c_lidarRFU2CarBackRFU_yawDegree < 0)
            c_lidarRFU2CarBackRFU_yawDegree += 360;
        m_lidarFLU2LidarRFUTransform = c_sensorAxisTransformation.getTransformation(0,0,90.0,0,0,0);

        m_lidarRFU2CarBackRFUTransform = c_sensorAxisTransformation.getTransformation(
                                                c_lidarRFU2CarBackRFU_rollDegree,
                                                c_lidarRFU2CarBackRFU_pitchDegree,
                                                c_lidarRFU2CarBackRFU_yawDegree,
                                                c_lidarRFU2CarBackRFU_x,
                                                c_lidarRFU2CarBackRFU_y,
                                                c_lidarRFU2CarBackRFU_z);


        m_optLidarRFU2CarBackRFUTransform = m_lidarRFU2CarBackRFUTransform;
        // Initial guess for Lidar to CarbackRFU transformation (XYZRPY)
        double l_aLidarRFU2CarbackRFUTransform[6] = {c_lidarRFU2CarBackRFU_x, c_lidarRFU2CarBackRFU_y, c_lidarRFU2CarBackRFU_z,
                                        c_lidarRFU2CarBackRFU_rollDegree, c_lidarRFU2CarBackRFU_pitchDegree, -c_lidarRFU2CarBackRFU_yawDegree};
        for (int i = 0; i < 6; ++i) {
            c_aInitialLidarRFU2CarbackRFUTransform[i] = l_aLidarRFU2CarbackRFUTransform[i];
        }

        Eigen::AngleAxisd rotationVectorx = Eigen::AngleAxisd(c_lidarRFU2CarBackRFU_rollDegree * M_PI / 180.0, Eigen::Vector3d::UnitX());
        Eigen::AngleAxisd rotationVectory = Eigen::AngleAxisd(c_lidarRFU2CarBackRFU_pitchDegree * M_PI / 180.0, Eigen::Vector3d::UnitY());
        Eigen::AngleAxisd rotationVectorz = Eigen::AngleAxisd(-c_lidarRFU2CarBackRFU_yawDegree * M_PI / 180.0, Eigen::Vector3d::UnitZ());
        Eigen::Quaterniond rotationQuat = Eigen::Quaterniond(rotationVectorx * rotationVectory * rotationVectorz);

        c_aLidarRFU2CarbackRFUTransformParamOpt [0] = rotationQuat.x();
        c_aLidarRFU2CarbackRFUTransformParamOpt [1] = rotationQuat.y();
        c_aLidarRFU2CarbackRFUTransformParamOpt [2] = rotationQuat.z();
        c_aLidarRFU2CarbackRFUTransformParamOpt [3] = rotationQuat.w();
        c_aLidarRFU2CarbackRFUTransformParamOpt [4] = c_lidarRFU2CarBackRFU_x;
        c_aLidarRFU2CarbackRFUTransformParamOpt [5] = c_lidarRFU2CarBackRFU_y;
        c_aLidarRFU2CarbackRFUTransformParamOpt [6] = c_lidarRFU2CarBackRFU_z;

        // Create Ceres Problem
        ceres::LocalParameterization *q_parameterization = new ceres::EigenQuaternionParameterization();
        c_ceresProblem.AddParameterBlock(c_aLidarRFU2CarbackRFUTransformParamOpt, 4, q_parameterization);
        c_ceresProblem.AddParameterBlock(c_aLidarRFU2CarbackRFUTransformParamOpt+4, 3);

        c_vStartingPoint_UTM = std::vector<double> {c_startingPoint_UTM_x, c_startingPoint_UTM_y, c_startingPoint_altitude};
        c_startingPoint_EulerDegrees << c_startingPoint_rollDegree,
                                        c_startingPoint_pitchDegree,
                                        c_startingPoint_yawDegree;

        c_sensorAxisTransformation.setSelfCarUTMPosition(c_vStartingPoint_UTM);
        c_sensorAxisTransformation.setSelfCarEulerDegrees(c_startingPoint_EulerDegrees);

        localizationPub = nh.advertise<comm_tcp::localization>(c_topicName, 50000);
        slamGPSPub = nh.advertise<common_msgs::sensorgps>("common_tcp/slamgps", 50000);
        slamGPSRandomPub = nh.advertise<common_msgs::sensorgps>("slam/slamgps", 50000);
        sub_imu = nh.subscribe("sensorgps", 50000, &Listener::SubCallback_gps, this, ros::TransportHints().tcpNoDelay(true));

        // 客户端
        memset(&c_clientAddr_, 0, sizeof(c_clientAddr_));
        c_clientAddr_.sin_family = AF_INET;
        c_clientAddr_.sin_port = htons(c_iPort + 1);
        c_clientAddr_.sin_addr.s_addr = INADDR_ANY;

        // 服务端
        memset(&c_clientAddr_, 0, sizeof(c_clientAddr_));
        c_serverAddr_.sin_family = AF_INET;
        c_serverAddr_.sin_port = htons(c_iPort);
        c_serverAddr_.sin_addr.s_addr = inet_addr(c_pcServerIp);
        printf("serverAddr: %d - %s \n", c_iPort, c_pcServerIp);

        c_bConnected = agvNetInit_(sockfd, c_clientAddr_, c_serverAddr_);

        // 创建文件夹保存
        c_mapSavePath = ros::package::getPath("comm_tcp");
        cout << "mapSavePath path: " << c_mapSavePath << endl;
        c_mapSavePath = c_mapSavePath + "/outputMapping/";

        std::string mkdirSaveDirCommand = "mkdir -p " + c_mapSavePath;
        std::system(mkdirSaveDirCommand.c_str());
        std::string mapSavePathName = c_mapSavePath + c_mapSaveName;
        cout << "mapSavePathName: " << mapSavePathName << endl;

        if(c_isSaveSLAMTrakectory){
            // 如果文件存在，删除,，重新创建文件
            createOrClearFile(mapSavePathName, c_mapFile);
        }

        std::string c_mapTrajectoryPathName = c_mapSavePath + "mapTrajectory.csv";
        createOrClearFile(c_mapTrajectoryPathName, c_mapTrajectoryFile);
    }

    bool fileExists(const std::string& filePathName) {
        std::ifstream file(filePathName);
        return file.good();
    }

    void createOrClearFile(const std::string& filePathName, std::fstream& file) {
        // 如果文件存在，删除文件
        if (fileExists(filePathName)) {
            std::remove(filePathName.c_str());
        }

        // 以 append 模式打开文件
        file.open(filePathName, std::ios::app);

        if (!file.is_open()) {
            std::cerr << "Error: Could not open the file " << filePathName << std::endl;
            return;
        }
    }

    bool agvNetInit_(int& p_iFd, sockaddr_in& p_sClientAddr, sockaddr_in& p_sServerAddr)
    {
        p_iFd = -1;
        // 建立套接字
        p_iFd = socket(PF_INET, SOCK_STREAM, 0);
        if (p_iFd == -1)
        {
            perror("agvNet socket");
            return false;
        }

        // 绑定端口
        if (bind(p_iFd, (sockaddr*)&p_sClientAddr, sizeof(sockaddr)) == -1)
        {
            perror("bind");
            return false;
        }

        int flags = fcntl(p_iFd, F_GETFL, 0);
        // 设置非阻塞方式  &~
        if (fcntl(p_iFd, F_SETFL, flags | O_NONBLOCK) < 0)
        {
            perror("non-block");
            return false;
        }
        ////设置阻塞方式
        // struct timeval timeout = {3,0}; //3s超时
        // setsockopt(p_iFd, SOL_SOCKET, SO_RCVTIMEO, (char *)&timeout, sizeof(struct timeval));

        int l_iRes = connect(p_iFd, (struct sockaddr*)&p_sServerAddr, sizeof(p_sServerAddr));

        // 连接成功(服务器和客户端在同一台机器上时就有可能发生这种情况)
        if (l_iRes == 0)
        {
            printf("agv connect succ! \n");
        }
        else if (l_iRes < 0)
        {
            if (errno == EINPROGRESS)
            {
                printf("agv connecting...\n");

                fd_set writefds;
                FD_ZERO(&writefds);
                FD_SET(p_iFd, &writefds);

                struct timeval timeout;
                timeout.tv_sec = 3;
                timeout.tv_usec = 0;

                // 调用select来等待连接建立成功完成
                l_iRes = select(p_iFd + 1, NULL, &writefds, NULL, &timeout);
                if (l_iRes < 0)
                {
                    perror("agv select\n");
                    close(p_iFd);
                    return false;
                }

                // 返回0,则表示建立连接超时;
                // 我们返回超时错误给用户，同时关闭连接，以防止三路握手操作继续进行
                if (l_iRes == 0)
                {
                    printf("agv connection timeout\n");
                    close(p_iFd);
                    return false;
                }
                else
                {
                    // 返回大于0的值,则需要检查套接口描述符是否可读或可写;
                    if (!FD_ISSET(p_iFd, &writefds))
                    {
                        printf("err, no events found!\n");
                        close(p_iFd);
                        return false;
                    }
                    else
                    {
                        // 套接口描述符可读或可写,通过调用getsockopt得到套接口上待处理的错误(SO_ERROR)
                        // err 0-建立成功
                        int err = 0;
                        socklen_t elen = sizeof(err);
                        l_iRes = getsockopt(p_iFd, SOL_SOCKET, SO_ERROR, (char*)&err, &elen);
                        if (l_iRes < 0)
                        {
                            perror("agv getsockopt\n");
                            close(p_iFd);
                            return false;
                        }
                        if (err != 0)
                        {
                            printf(
                                "agv connect failed with the error: (%d)%s\n", err, strerror(err));
                            close(p_iFd);
                            return false;
                        }
                        else
                        {
                            printf("agv connect succ!\n");
                            return true;
                        }
                    }
                }
            }
            else
            {
                printf("agv connect failed with the error\n");
                return false;
            }
        }
    }

    u_char* getSICKCMD(int& p_iOffset, char* p_pucBuf, int p_iLen)
    {
        int l_iSta = -1, l_iEnd = -1;
        while (p_iOffset != p_iLen)
        {
            if (p_pucBuf[p_iOffset] == 0x02)
            {
                l_iSta = p_iOffset;
                p_iOffset = (p_iOffset + 1) % 4096;
            }
            else if (p_pucBuf[p_iOffset] == 0x03 && l_iSta != -1)
            {
                l_iEnd = p_iOffset;
                p_iOffset = (p_iOffset + 1) % 4096;
            }
            else
            {
                p_iOffset = (p_iOffset + 1) % 4096;
            }

            if (l_iSta != -1 && l_iEnd != -1)
            {
                int dataProcLen = (l_iEnd - l_iSta + 1 + 4096) % 4096;

                u_char* buftmp = new u_char[dataProcLen - 1];
                if (l_iSta > l_iEnd)
                {
                    memcpy(buftmp, &p_pucBuf[l_iSta + 1], 4096 - l_iSta - 1);
                    if (l_iEnd > 0)
                        memcpy(&buftmp[4096 - l_iSta - 1], p_pucBuf, l_iEnd);
                }
                else
                    memcpy(buftmp, &p_pucBuf[l_iSta + 1], dataProcLen - 2);
                buftmp[dataProcLen - 2] = '\0';

                if (0)
                {
                    char l_buf[4096];
                    std::string l_sBuf = "";
                    memcpy(&l_buf, &buftmp[0], dataProcLen);
                    l_sBuf = l_buf;
                    printf("recv %s\n", l_sBuf.c_str());
                }

                return buftmp;
            }
        }
        return NULL;
    }

    s_AscProtocol seprateSICKCmd(u_char* p_pucBuf, int p_iLen)
    {
        if (0)
        {
            u_char l_buf[4096];
            std::string l_sBuf = "";
            memcpy(&l_buf, &p_pucBuf[0], p_iLen);
            l_sBuf = (char*)l_buf;
            printf("recv2 %s\n", l_sBuf.c_str());
        }
        s_AscProtocol l_this;
        int l_iOffset = 0;
        int l_iSta = 0, l_iEnd = 0;
        int l_iCMDCnt = 0;
        memset(&l_this, 0, sizeof(s_AscProtocol));
        while (l_iOffset < p_iLen)
        {
            if (p_pucBuf[l_iOffset] == 0x20)
            {
                l_iEnd = l_iOffset;
                if (l_iCMDCnt == 0)
                {
                    memcpy(l_this.m_acCmdType, &p_pucBuf[l_iSta], l_iEnd - l_iSta);
                    if ((l_iEnd - l_iSta > 10) || (l_iEnd - l_iSta < 0))
                    {
                        std::cout << "wrong 1!!" << std::endl;
                        break;
                    }
                }
                else if (l_iCMDCnt == 1)
                {
                    memcpy(l_this.m_acCmd, &p_pucBuf[l_iSta], l_iEnd - l_iSta);
                    if ((l_iEnd - l_iSta > 20) || (l_iEnd - l_iSta < 0))
                    {
                        std::cout << "wrong 2!!" << std::endl;
                        break;
                    }
                }
                else
                {
                    memcpy(&l_this.m_acParam[l_iCMDCnt - 2][0], &p_pucBuf[l_iSta], l_iEnd - l_iSta);
                }
                l_iSta = l_iOffset + 1;
                if (l_iCMDCnt > 14)
                {
                    printf("buf too long: %d", l_iCMDCnt);
                    break;
                }
                l_iCMDCnt++;

                // l_iSta = l_iOffset + 1;
            }

            l_iOffset++;
        }
        memcpy(&l_this.m_acParam[l_iCMDCnt - 2][0], &p_pucBuf[l_iSta], p_iLen - l_iSta);
        return l_this;
    }

    const double EARTH_RADIUS = 6371e3; // 地球半径，单位：米

    double haversine_distance(double lat1, double lon1, double lat2, double lon2) {
        // 将经纬度转换为弧度
        lat1 = lat1 * M_PI / 180.0;
        lon1 = lon1 * M_PI / 180.0;
        lat2 = lat2 * M_PI / 180.0;
        lon2 = lon2 * M_PI / 180.0;

        // 计算差值
        double dlat = lat2 - lat1;
        double dlon = lon2 - lon1;

        // Haversine公式
        double a = std::pow(std::sin(dlat / 2), 2) +
                std::cos(lat1) * std::cos(lat2) * std::pow(std::sin(dlon / 2), 2);
        double c = 2 * std::atan2(std::sqrt(a), std::sqrt(1 - a));

        // 返回距离，单位：米
        return EARTH_RADIUS * c;
    }

    /***
     * 订阅gps话题
     * @param msg gps话题
     */
    void SubCallback_gps(const common_msgs::sensorgps::ConstPtr& msg)
    {
        std::lock_guard<std::mutex> gpsLock(c_gpsMutex);
        // *c_pCurGPS = *msg;
        c_gpsDeque_.push_back(msg);
        // cout<<std::setprecision(16)<<"\tgps 时间："<<msg->timestamp / 1000.0 << std::endl;

        // 发布
        // common_msgs::sensorgps l_slamGPS = *msg;
        
        //   // 经纬度判断是否在区域内 且GPS状态异常,在区域内设置状态为0
        // Point curPoint = {l_slamGPS.lon, l_slamGPS.lat};
        // if(l_slamGPS.status == 0){
        //     // 生成在[-0.00009, 0.00009]范围内的随机数
        //     double randomRange = 0.000009;
        //     float random_lon = ((float)rand() / RAND_MAX) * 2 * randomRange - randomRange;
        //     float random_lat = ((float)rand() / RAND_MAX) * 2 * randomRange - randomRange;

        //     // std::cout << FRED("drving in area that the gps not useful") << std::endl;
            
        //     l_slamGPS.lon += random_lon;
        //     l_slamGPS.lat += random_lat; //纬度0.0001秒=0.00308m=3.08mm
            
        //     double distanceDiff = haversine_distance(msg->lat, msg->lon, l_slamGPS.lat, l_slamGPS.lon);
        //     std::cout<<"hsq distanceDiff =  " << distanceDiff <<", random_lon = " <<  random_lon <<", random_lat = " << random_lat << std::endl;
        // }


        // // l_slamGPS.timestamp = rosCurrentTime * 1000;

        // slamGPSRandomPub.publish(l_slamGPS);
    }

    bool syncSensorTime(const double& syncFrameStamp){
        // cout << "lidar-gps time sync: \n";
        std::lock_guard<std::mutex> gpsLock(c_gpsMutex);

        if(c_gpsDeque_.empty())
            return false;
        
        //找到容器中数据时间戳小于同步时间的最近索引
        int curMsgIndex = 0;
        int msgDequeSize = c_gpsDeque_.size();
        for (int i = 0; i < msgDequeSize; ++i) {
            common_msgs::sensorgps::ConstPtr l_curGPS = c_gpsDeque_[i];
            if(l_curGPS->timestamp / 1000.0  > syncFrameStamp )
                break;
            curMsgIndex = i;
        }
        
        
        if(curMsgIndex + 1 == c_gpsDeque_.size()){//容器中数据时间戳都小于同步时间,只保留最后一个数据
            if(curMsgIndex > 0){//等于0的情况 说明容器内第一帧radar数据就大于当前帧lidar数据，不处理
                while(curMsgIndex--){
                    c_gpsDeque_.pop_front();
                }
            }
        }
        else{
            if(curMsgIndex > 0){//小于0 说明容器内第一帧radar数据就大于当前帧lidar数据，不处理
                if(abs(syncFrameStamp - c_gpsDeque_[curMsgIndex]->timestamp / 1000.0) >//lidar前一帧
                abs(syncFrameStamp - c_gpsDeque_[curMsgIndex + 1]->timestamp / 1000.0)){//lidar后一帧
                    curMsgIndex += 1;//取最近的一帧数据
                }
                while(curMsgIndex--){
                    c_gpsDeque_.pop_front();
                }
            }
        }

        float timeGap = syncFrameStamp - c_gpsDeque_.front()->timestamp / 1000.0;
        static float minLidarGPSSynchroTime = FLT_MAX, maxLidarGPSSynchroTime = FLT_MIN;
        minLidarGPSSynchroTime = abs(minLidarGPSSynchroTime) < abs(timeGap) ? abs(minLidarGPSSynchroTime) : abs(timeGap);
        maxLidarGPSSynchroTime = abs(maxLidarGPSSynchroTime) > abs(timeGap) ? abs(maxLidarGPSSynchroTime) : abs(timeGap);
        cout<<std::setprecision(16)<<"\tlidar-gps时间差："<<timeGap<<", 当前msg时间 = "<<c_gpsDeque_.front()->timestamp / 1000.0 <<", 当前lidar检测时间 ="
            <<syncFrameStamp<<endl;
        // cout<<std::setprecision(16)<<"\tabs(minLidarGPSSynchroTime)： "<<abs(minLidarGPSSynchroTime)<<", abs(maxLidarGPSSynchroTime) = "<<abs(maxLidarGPSSynchroTime) <<endl;
        //cout<<"\tgps Deque.size = "<<c_gpsDeque_.size() <<endl;
        return true;
    }

    static void toEulerAngle(const Eigen::Quaterniond& q, double& roll, double& pitch, double& yaw)
    {
        // roll (x-axis rotation)
        double sinr_cosp = +2.0 * (q.w() * q.x() + q.y() * q.z());
        double cosr_cosp = +1.0 - 2.0 * (q.x() * q.x() + q.y() * q.y());
        roll = atan2(sinr_cosp, cosr_cosp);

        // pitch (y-axis rotation)
        double sinp = +2.0 * (q.w() * q.y() - q.z() * q.x());
        if (fabs(sinp) >= 1)
        pitch = copysign(M_PI / 2, sinp); // use 90 degrees if out of range
        else
        pitch = asin(sinp);

        // yaw (z-axis rotation)
        double siny_cosp = +2.0 * (q.w() * q.z() + q.x() * q.y());
        double cosy_cosp = +1.0 - 2.0 * (q.y() * q.y() + q.z() * q.z());
        yaw = atan2(siny_cosp, cosy_cosp);
    }

    int selectSICKProtocol(s_AscProtocol l_this)
    {
        int l_iLen = 0;
        int l_time = 0;
        if ((!strcmp("sAN", l_this.m_acCmdType)) && (!strcmp("mNPOSGetPose", l_this.m_acCmd)))
        {
            // std::cout<<"AA " << l_this.m_acCmdType<<", " << l_this.m_acCmd << ", " <<
            // l_this.m_acParam[4]
            // << ", " << l_this.m_acParam[5]<< ", " << l_this.m_acParam[6]<< std::endl;
            if (l_this.m_acParam[6][0] == '\0')
                return 0;

            int l_poseX = (int32_t)HexStr2U32(&l_this.m_acParam[4][0]);
            int l_poseY = (int32_t)HexStr2U32(&l_this.m_acParam[5][0]);
            int l_poseA = (u_int32_t)HexStr2U32(&l_this.m_acParam[6][0]);
            c_bFlag_ = (u_int32_t)HexStr2U32(&l_this.m_acParam[11][0]);
            std::cout << "Got POSE, X Y Yaw and c_bFlag_: " << l_poseX << ", " << l_poseY << ", "
                      << l_poseA << ", " << c_bFlag_ << std::endl;


            double rosCurrentTime = ros::Time::now().toSec();
            comm_tcp::localization curLocate;
            curLocate.header.stamp = ros::Time(rosCurrentTime);
            curLocate.header.frame_id = "map";
            curLocate.status = 10;

            static float minDistanceUTM = FLT_MAX;
            bool isSettingPose = true;
            static bool isSettingFinished = false;

            if (1 == c_bFlag_)
            {
                curLocate.x = ((float)l_poseX) / 1000.0;
                curLocate.y = ((float)l_poseY) / 1000.0;
                l_poseA = l_poseA % 360000;
                float heading = ((float)l_poseA) / 1000.0;  // FLU,x逆时针,0-360相对起点旋转角度
                curLocate.heading = heading;
                curLocate.status = 15;
                cout <<"hsq: curLocate.x = " << curLocate.x << ", curLocate.y = " << curLocate.y << endl;

                // 相对位姿转到ENU坐标
                pcl::PointXYZI pointInLidarFLU;
                pointInLidarFLU.x = curLocate.x;
                pointInLidarFLU.y = curLocate.y;
                pointInLidarFLU.z = 0;
                pcl::PointCloud<pcl::PointXYZI> pointcloudInLidarFLU, pointcloudInLidarRFU;
                pointcloudInLidarFLU.push_back(pointInLidarFLU);
                pcl::transformPointCloud(pointcloudInLidarFLU, pointcloudInLidarRFU, m_lidarFLU2LidarRFUTransform);
                pcl::PointXYZI pointInLidarRFU = pointcloudInLidarRFU.points[0];
                cout <<"hsq: pointInLidarRFU.x = " << pointInLidarRFU.x << ", pointInLidarRFU.y = " << pointInLidarRFU.y << endl;
                pcl::PointCloud<pcl::PointXYZI> pointcloudInCarbackRFU;
                if(c_isCalib){
                    //
                    Eigen::Quaternionf q_calibration{c_aLidarRFU2CarbackRFUTransformParamOpt[3], c_aLidarRFU2CarbackRFUTransformParamOpt[0], c_aLidarRFU2CarbackRFUTransformParamOpt[1], c_aLidarRFU2CarbackRFUTransformParamOpt[2]};
                    q_calibration.normalize();
                    cout <<"hsq: 优化点云 = " << endl;
                    m_optLidarRFU2CarBackRFUTransform = q_calibration.matrix();
                    m_optLidarRFU2CarBackRFUTransform.translation() <<c_aLidarRFU2CarbackRFUTransformParamOpt[4],c_aLidarRFU2CarbackRFUTransformParamOpt[5],c_aLidarRFU2CarbackRFUTransformParamOpt[6];
                    pcl::transformPointCloud(pointcloudInLidarRFU, pointcloudInCarbackRFU, m_optLidarRFU2CarBackRFUTransform);
                }
                else{
                    pcl::transformPointCloud(pointcloudInLidarRFU, pointcloudInCarbackRFU, m_lidarRFU2CarBackRFUTransform);
                }
                
                pcl::PointXYZI pointInCarBackRFU = pointcloudInCarbackRFU.points[0];

                Eigen::Vector3d ENUPosition{pointInCarBackRFU.x,pointInCarBackRFU.y,
                                            pointInCarBackRFU.z};
                cout <<"hsq: pointInCarBackRFU.x = " << pointInCarBackRFU.x << ", pointInCarBackRFU.y = " << pointInCarBackRFU.y << endl;

                Eigen::Vector3d mapTrajectory = ENUPosition;
                static std::vector<double> l_vTrajectoryInCarBackRFU_x, l_vTrajectoryInCarBackRFU_y;
                if(l_vTrajectoryInCarBackRFU_x.size() < 10){
                    l_vTrajectoryInCarBackRFU_x.push_back(pointInCarBackRFU.x);
                    l_vTrajectoryInCarBackRFU_y.push_back(pointInCarBackRFU.y);
                }
                else{
                    if(abs(pointInCarBackRFU.x - l_vTrajectoryInCarBackRFU_x[l_vTrajectoryInCarBackRFU_x.size() - 1]) > 0.1){
                        pointInCarBackRFU.x = 0.4 * pointInCarBackRFU.x 
                                            + 0.3 * l_vTrajectoryInCarBackRFU_x[l_vTrajectoryInCarBackRFU_x.size() - 1]
                                            + 0.2 * l_vTrajectoryInCarBackRFU_x[l_vTrajectoryInCarBackRFU_x.size() - 2]
                                            + 0.1 * l_vTrajectoryInCarBackRFU_x[l_vTrajectoryInCarBackRFU_x.size() - 3];
                    }
                    // if(abs(pointInCarBackRFU.y - l_vTrajectoryInCarBackRFU_y[l_vTrajectoryInCarBackRFU_y.size() - 1]) > 0.1){
                    //     pointInCarBackRFU.y = 0.4 * pointInCarBackRFU.y 
                    //                         + 0.3 * l_vTrajectoryInCarBackRFU_y[l_vTrajectoryInCarBackRFU_y.size() - 1]
                    //                         + 0.2 * l_vTrajectoryInCarBackRFU_y[l_vTrajectoryInCarBackRFU_y.size() - 2]
                    //                         + 0.1 * l_vTrajectoryInCarBackRFU_y[l_vTrajectoryInCarBackRFU_y.size() - 3];
                    // }
                    l_vTrajectoryInCarBackRFU_x.push_back(pointInCarBackRFU.x);
                    l_vTrajectoryInCarBackRFU_y.push_back(pointInCarBackRFU.y);

                    if(l_vTrajectoryInCarBackRFU_x.size() > 10){
                        l_vTrajectoryInCarBackRFU_x.erase(l_vTrajectoryInCarBackRFU_x.begin());
                        l_vTrajectoryInCarBackRFU_y.erase(l_vTrajectoryInCarBackRFU_y.begin());
                    }
                }

                static double maxDistanceX = DBL_MIN;
                static bool l_bIsIniitialized = false;
                static double l_lastPointInCarBackRFUX;
                if(!l_bIsIniitialized){
                    l_lastPointInCarBackRFUX = pointInCarBackRFU.x;
                    maxDistanceX = abs(pointInCarBackRFU.x - l_lastPointInCarBackRFUX) < maxDistanceX ? maxDistanceX : abs(pointInCarBackRFU.x - l_lastPointInCarBackRFUX);
                    l_bIsIniitialized = true;
                }
                else{
                     maxDistanceX = abs(pointInCarBackRFU.x - l_lastPointInCarBackRFUX) < maxDistanceX ? maxDistanceX : abs(pointInCarBackRFU.x - l_lastPointInCarBackRFUX);
                     l_lastPointInCarBackRFUX = pointInCarBackRFU.x;
                }
                cout <<"hsq: maxDistanceX = " << maxDistanceX << endl;

                ENUPosition << pointInCarBackRFU.x,pointInCarBackRFU.y, pointInCarBackRFU.z;
                cout <<"hsq: optimized pointInCarBackRFU.x = " << pointInCarBackRFU.x << ", pointInCarBackRFU.y = " << pointInCarBackRFU.y << endl;
                
                c_mapTrajectoryFile << mapTrajectory[0] << "," <<mapTrajectory[1]<< "," <<
                                    1<< "," <<7<< "," <<0<< "," <<0<< "," <<0<< "," <<0<< "," <<
                                    0<< "," <<3.5<< "," <<3.5<< "," <<3.5<< "," <<1.7<< "," <<
                                    1.7<< "," << ENUPosition[0]<< "," <<mapTrajectory[1]<< "," <<0<< "," <<
                                    0<< "," << 0<< "," << 0<< "," << rosCurrentTime <<std::endl;

                
                    
                


                // ENU坐标转到UTM坐标
                std::vector<double> l_centerPointUTM =
                    c_sensorAxisTransformation.ENU2UTM(ENUPosition);

                // UTM2LLA
                WGS84Corr lla;
                wgs84_utm l_wgs84_utm;
                l_wgs84_utm.UTMXYToLatLon(
                    l_centerPointUTM[0], l_centerPointUTM[1], c_startingPoint_UTM_Code, false, lla);

                // 发布
                common_msgs::sensorgps l_slamGPS;
                l_slamGPS.lon = lla.log / M_PI * 180;
                l_slamGPS.lat = lla.lat / M_PI * 180;
                l_slamGPS.alt = c_startingPoint_altitude;

                float headingDegreeInNorth =
                    360 - curLocate.heading + c_startingPoint_yawDegree;  // 相对+绝对

                if (headingDegreeInNorth < 0)
                    headingDegreeInNorth += 360;

                if (headingDegreeInNorth >= 360)
                    headingDegreeInNorth -= 360;

                l_slamGPS.heading = headingDegreeInNorth;

                // 同步GPS数据
                if(!syncSensorTime(rosCurrentTime)){
                    std::cerr << "[debug]: syncSensorTime data: " << ros::Time::now().toSec() << std::endl;
                }
                if(c_gpsDeque_.empty()){
                    std::cerr << "[debug]: c_gpsDeque_.empty() " << ros::Time::now().toSec() << std::endl;
                }
                else{
                    *c_pCurGPS = *(c_gpsDeque_.front());
                }
        
                if (c_pCurGPS)
                {
                    l_slamGPS.roadtype = c_pCurGPS->roadtype;
                    l_slamGPS.lanetype = c_pCurGPS->lanetype;
                    l_slamGPS.pitch = c_pCurGPS->pitch;
                    l_slamGPS.roll = c_pCurGPS->roll;
                    l_slamGPS.pitchrate = c_pCurGPS->pitchrate;
                    l_slamGPS.rollrate = c_pCurGPS->rollrate;
                    l_slamGPS.yawrate = c_pCurGPS->yawrate;

                    l_slamGPS.accx = c_pCurGPS->accx;
                    l_slamGPS.accy = c_pCurGPS->accy;
                    l_slamGPS.accz = c_pCurGPS->accz;
                    l_slamGPS.mile = c_pCurGPS->mile;
                    l_slamGPS.velocity = c_pCurGPS->velocity;
                    
                    l_slamGPS.satenum = c_pCurGPS->satenum;
                    

                    l_slamGPS.speedN = c_pCurGPS->speedN;
                    l_slamGPS.speedE = c_pCurGPS->speedE;
                    l_slamGPS.speedD = c_pCurGPS->speedD;
                }

                l_slamGPS.status = 4;
                l_slamGPS.rawstatus = 50;
                l_slamGPS.isvalid = 1;
                l_slamGPS.gpstime = rosCurrentTime * 1000;
                l_slamGPS.timestamp = rosCurrentTime * 1000;

                slamGPSPub.publish(l_slamGPS);

                // 保存GPS失效区域的地图
                if(c_isSaveSLAMTrakectory){
                    // if(c_pCurGPS->rawstatus != 50){
                        static std::vector<double> l_centerPointUTMLast;
                        static bool l_isFirstSLAMPoint = true;
                        if(l_isFirstSLAMPoint){
                            l_centerPointUTMLast = l_centerPointUTM;
                            std::vector<double> maptrajectory{l_slamGPS.lon, l_slamGPS.lat, 
                                1, 7, 0, 0, 0, 0, 
                                0, 3.5, 3.5, 3.5, 1.7, 
                                1.7,  l_slamGPS.heading, c_pCurGPS->rawstatus, int(l_slamGPS.satenum), 
                                0,  0,  0,  0};

                            std::lock_guard<std::mutex> savemapLock(c_mapSaveMutex);
                            c_vMappingTrajectory.emplace_back(maptrajectory);
                            l_isFirstSLAMPoint = false;
                        }
                        else{
                            // 计算当前SLAMGPS与上一帧SLAMGPS的位置差值
                            float delta_UTMIncrementX = l_centerPointUTMLast[0] - l_centerPointUTM[0];
                            float delta_UTMIncrementY = l_centerPointUTMLast[1] - l_centerPointUTM[1];
                            float distanceUTMIncrement = sqrt(delta_UTMIncrementX * delta_UTMIncrementX + delta_UTMIncrementY * delta_UTMIncrementY);
                            
                            if(distanceUTMIncrement >= 0.05){
                                std::vector<double> maptrajectory{l_slamGPS.lon, l_slamGPS.lat, 
                                    1, 7, 0, 0, 0, 0, 
                                    0, 3.5, 3.5, 3.5, 1.7, 
                                    1.7,  l_slamGPS.heading, c_pCurGPS->rawstatus, int(l_slamGPS.satenum), 
                                    0,  0,  0,  0};

                                std::lock_guard<std::mutex> savemapLock(c_mapSaveMutex);
                                c_vMappingTrajectory.emplace_back(maptrajectory);
                                l_centerPointUTMLast = l_centerPointUTM;
                            }
                        }
                    // }
                }
                

                // 计算当前GPS与SLAMGPS的位置航向角差值
                tagUTMCorr utm;
                l_wgs84_utm.runLatLonToUTMXY(c_pCurGPS->lon, c_pCurGPS->lat, utm);

                
                if(c_isCalib && c_pCurGPS->rawstatus == 50){
                    // 传入数据：l_lidarRFU坐标、GPSUTM坐标、c_aLidarRFU2CarbackRFUTransform、ENU2UTM的函数指针
                    Eigen::Vector3d l_lidarRFU{pointInLidarRFU.x, pointInLidarRFU.y, 0.0};
                    Eigen::Vector3d l_curGPSUTM{utm.x, utm.y, 0.0};
                    Eigen::Vector3d c_curGPSRPY{c_pCurGPS->roll, c_pCurGPS->pitch, c_pCurGPS->heading};

                    {
                        Eigen::Quaterniond q_calibration{c_aLidarRFU2CarbackRFUTransformParamOpt[3], c_aLidarRFU2CarbackRFUTransformParamOpt[0], c_aLidarRFU2CarbackRFUTransformParamOpt[1], c_aLidarRFU2CarbackRFUTransformParamOpt[2]};
                        q_calibration.normalize();
                        Eigen::Vector3d t_calibration{c_aLidarRFU2CarbackRFUTransformParamOpt[4], c_aLidarRFU2CarbackRFUTransformParamOpt[5], c_aLidarRFU2CarbackRFUTransformParamOpt[6]};
                
                        Eigen::Vector3d ENUPosition;
                        ENUPosition = q_calibration * l_lidarRFU + t_calibration;
                        cout <<"hsq: 传入参数 pointInCarBackRFU.x = " << ENUPosition[0] << ", pointInCarBackRFU.y = " << ENUPosition[1] << endl;
                    }
                    ceres::LossFunction *loss_function = new ceres::HuberLoss(0.1);
                    ceres::CostFunction *cost_function = LidarGpsResidual::Create(l_lidarRFU, l_curGPSUTM, c_curGPSRPY, c_vStartingPoint_UTM, c_startingPoint_EulerDegrees, &c_sensorAxisTransformation);
			        c_ceresProblem.AddResidualBlock(cost_function, loss_function, c_aLidarRFU2CarbackRFUTransformParamOpt, c_aLidarRFU2CarbackRFUTransformParamOpt + 4);

                }
                

                
                float delta_UTMGapX = utm.x - l_centerPointUTM[0];
                float delta_UTMGapY = utm.y - l_centerPointUTM[1];
                float distanceUTMGap = sqrt(delta_UTMGapX * delta_UTMGapX + delta_UTMGapY * delta_UTMGapY);

                static float UTMGapXmax = FLT_MIN;
                static float UTMGapYmax = FLT_MIN;
                UTMGapXmax = delta_UTMGapX > UTMGapXmax ? delta_UTMGapX : UTMGapXmax;
                UTMGapYmax = delta_UTMGapY > UTMGapYmax ? delta_UTMGapY : UTMGapYmax;
                

                
                if(c_pCurGPS->rawstatus == 50){
                    std::cerr << std::setprecision(12)<< "sensorgps 有效" << std::endl;
                }
                else{
                    std::cout << std::setprecision(12)<< "sensorgps 无效"  << std::endl;
                }
                // std::cerr << std::setprecision(12) 
                //     << "\n   timestamp: curgps =" << c_pCurGPS->timestamp / 1000.0 << " rosCurrentTime = " << rosCurrentTime
                //     << ", timegap(ms) =" << (c_pCurGPS->timestamp / 1000.0 - rosCurrentTime) * 1000 << std::endl;
                // std::cerr << std::setprecision(12)<< "cur point log lat =" << lla.log / M_PI * 180 << " " << lla.lat / M_PI * 180
                //           << std::endl;
                // std::cerr << std::setprecision(12)<< "cur gps log lat =" << c_pCurGPS->lon << " " << c_pCurGPS->lat << std::endl;
                // // cout <<"hsq: UTMGapXmax = " << UTMGapXmax << ", UTMGapYmax = " << UTMGapYmax << endl;
                // cout <<"hsq: gps UTM x = " << utm.x << ", y = " << utm.y << endl;
                // cout <<"hsq: cal UTM x = " << l_centerPointUTM[0] << ", y = " << l_centerPointUTM[1] << endl;
                // cout <<"hsq: pointInLidarFLU curLocate.x = " << curLocate.x << ", curLocate.y = " << curLocate.y << endl;
                // cout <<"hsq: pointInLidarRFU.x = " << pointInLidarRFU.x << ", pointInLidarRFU.y = " << pointInLidarRFU.y << endl;
                // cout <<"hsq: pointInCarBackRFU.x = " << pointInCarBackRFU.x << ", pointInCarBackRFU.y = " << pointInCarBackRFU.y << endl;
                cout <<"hsq: curGPSUTM - curSLAMUTM  m: x = " << delta_UTMGapX << ", y = " << delta_UTMGapY
                        << ", distanceUTMGap = " << distanceUTMGap << endl;
                cout <<"hsq: curGPS degree: x = " << c_pCurGPS->heading << ",  curSLAM   = " << headingDegreeInNorth
                    << ", curGPS - curSLAMdeltaHeading = " << c_pCurGPS->heading - headingDegreeInNorth << endl;
                
                // 误差转到建图原点
                // Eigen::Vector3d inputUTMPosition{delta_UTMGapX, delta_UTMGapY, 0};
                // Eigen::Vector3d objectPositionInCarBackFRU;
                // Eigen::Vector3d l_estartingPoint_UTM{0.0, 0.0, 0.0};
                // c_sensorAxisTransformation.utm2CarBackRFU(inputUTMPosition,c_startingPoint_EulerDegrees,
                //                              l_estartingPoint_UTM, objectPositionInCarBackFRU);
                // cout <<"hsq: error m: x = " << objectPositionInCarBackFRU[0] << ", y = " << objectPositionInCarBackFRU[1] << endl;


                // 计算当前GPS与起点GPS的位置航向角差值
                float delta_UTMx = abs(utm.x - c_startingPoint_UTM_x);
                float delta_UTMy = abs(utm.y - c_startingPoint_UTM_y);
                float distanceUTM = sqrt(delta_UTMx * delta_UTMx + delta_UTMy * delta_UTMy);
                float deltaHeadingDegree = cos((c_pCurGPS->heading - c_startingPoint_yawDegree) * M_PI / 180.0);
               
                //  重置定位
                if(minDistanceUTM > distanceUTM){
                    minDistanceUTM = distanceUTM;
                }
                else{
                    isSettingPose = false;
                }

                if(distanceUTM < 5.0 && deltaHeadingDegree > 0.93){
                    // 距离近且航向角小于20度
                    float headingDegreeGap = c_pCurGPS->heading - c_startingPoint_yawDegree;
                    if(headingDegreeGap < 0)
                        headingDegreeGap += 360.0;

                    float setHeadingDegree = 360.0 - headingDegreeGap;
                    if(setHeadingDegree < 0.0) setHeadingDegree += 360;

                    if(isSettingPose){
                        cout <<"\n[hsq: isSettingPose] distanceUTM = " << distanceUTM << ", carBackRFU_x = " << pointInCarBackRFU.x
                        << ", carBackRFU_y = " << pointInCarBackRFU.y << endl;
                        local_plan << pointInCarBackRFU.x, pointInCarBackRFU.y, 1;
                        c_settingEulorRPYDegree << 0.0, 0.0, setHeadingDegree;

                        std::thread sendpos(&Listener::sendSetPose, this);
                        sendpos.detach(); 
                    }
                    else{
                        cout <<"\n[hsq: do not settingPose] distanceUTM = " << distanceUTM << ", carBackRFU_x = " << pointInCarBackRFU.x
                        << ", carBackRFU_y = " << pointInCarBackRFU.y << endl;
                        if(!isSettingFinished){
                            isSettingFinished = true;
                        }
                    }
                }
                else{
                    cout <<"\nhsq: distanceUTM = " << distanceUTM << ", curheading = " << c_pCurGPS->heading
                        << ", c_startingPoint_yawDegree = " << c_startingPoint_yawDegree << endl;
                    if(!isSettingFinished){
                        local_plan << 0.0, 0.0, 0.0;
                        c_settingEulorRPYDegree << 0.0, 0.0, 0.0;

                        std::thread sendpos(&Listener::sendSetPose, this);
                        sendpos.detach();
                    }
                }
                
            }
            localizationPub.publish(curLocate);

            // l_flag 的状态: 1 为正常，非2时为异常（此时遮住时位姿为（0，0，0））

            // 线程的状态位->创建线程->循环判断（flag为全局变量）,需要一个循环判断位->发送一个固定位置->发送位置暂停10s->结束循环控制

            // if(!isSettingFinished){
            //     if (c_bFlag_ != 1 && !c_bCreat)
            //     {
            //         cout <<"isSettingFinished = " << isSettingFinished << endl;
            //         std::thread sendpos(&Listener::sendSetPose, this);
            //         sendpos.detach();
            //     }
            //     else{
            //         cout <<"c_bFlag_ = " << c_bFlag_ << ", c_bCreat = " << c_bCreat << endl;
            //     }
            // }
            // else{
            //     cout <<"Setting not Finished " << endl;
            // }

            // c_bFlag_为1时将值进行保留，下次循环不为1时将该值发出
            // if (c_bFlag_ == 1)
            // {
            //     l_dpose_X = l_poseX;
            //     l_dpose_Y = l_poseY;
            //     l_dpose_A = l_poseA;
            // }

            // l_dpose_X = l_poseX;
            // l_dpose_Y = l_poseY;
            // l_dpose_A = l_poseA;
            // g_file << ros::Time::now() << "," << l_poseX << "," << l_poseY << "," << l_poseA
            //        << std::endl;

            // double yaw = l_poseA * 0.001;
            // if (yaw > 180)
            //     yaw -= 360;

            // c_iPoseX = l_poseX;
            // c_iPoseY = l_poseY;
            // c_iPoseYaw = yaw;

            // Eigen::Matrix3d R;
            // R = (Eigen::AngleAxisd(yaw * M_PI / 180, ::Eigen::Vector3d::UnitZ())
            //      * Eigen::AngleAxisd(0, ::Eigen::Vector3d::UnitY())
            //      * Eigen::AngleAxisd(0, ::Eigen::Vector3d::UnitX()))
            //         .matrix();
            // c_w_quat = R;

            return 1;
        }
        else
        {
            // std::cout<<"protocol not set:AA " << l_this.m_acCmdType<<", " << l_this.m_acCmd << std::endl;// sMA, mNPOSGetPose
            // printf("protocol not set\n");
            return 0;
        }
    }


    

    void saveMapTrajectory(){
        std::lock_guard<std::mutex> savemapLock(c_mapSaveMutex);
        for (const auto& simgleMappingTrajectory : c_vMappingTrajectory) {
            for (size_t i = 0; i < simgleMappingTrajectory.size(); ++i) {
                c_mapFile << std::setprecision(16) << simgleMappingTrajectory[i];
                if (i < simgleMappingTrajectory.size() - 1) {
                    c_mapFile << ','; // 逗号分隔
                }
            }
            c_mapFile << '\n'; // 换行
        }

        c_mapFile.close();
        
    }

    void sendMsg_(int p_sockfd, char* p_pcBuf, int p_iLen)
    {
        if (c_bConnected)
        {
            if (p_sockfd != -1)
            {
                int l_iRes = send(p_sockfd, p_pcBuf, p_iLen, MSG_NOSIGNAL);
                char recvBuf[1024] = {};
                int a = recv(p_sockfd, recvBuf, 1024, 0);
                // if (l_iRes == p_iLen)
                // {
                //     std::cout << "----发送成功----" << p_pcBuf << std::endl;
                // }
                if (l_iRes == -1)
                {
                    std::cout << "----发送失败----" << std::endl;
                }
            }
        }
    }

    // ******* sendsetpos 添加 ************ //
    template <typename T> void fillFloatTo32Bytes(T p_fData, char* p_pcBufCMD, int& p_iLen)
    {
        // multi 1000 to int
        int l_iData = (int)(p_fData * 1000);
        p_pcBufCMD[p_iLen++] = (l_iData & 0xff000000) >> 24;
        p_pcBufCMD[p_iLen++] = (l_iData & 0xff0000) >> 16;
        p_pcBufCMD[p_iLen++] = (l_iData & 0xff00) >> 8;
        p_pcBufCMD[p_iLen++] = l_iData & 0xff;
    }
    // 设定雷达Pose
    u_char c_aucSetLidarPose[26] = {0xFF, 0xAA, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                                    0x00, 0x01, 0x01, 0x01, 0x0B, 0x00, 0x00, 0x00, 0x00,
                                    0x00, 0x00, 0x00, 0x00, 0x0A, 0x0B, 0x00, 0x00};
    char checkXOR(char* p_pcBufResponse, int p_iLen)
    {
        char l_cRes = 0;
        int i = 0;
        for (i = 0; i < p_iLen; i++)
        {
            l_cRes ^= p_pcBufResponse[i];
        }
        return l_cRes;
    }
    bool c_bRun_ = true;

    enum PoseStatus { Default = 0, InitialPose, ContinuePose, VirtualPose, StopPose, SettingPose };

    void sendSetPose()
    {
        c_bCreat = true;

        // while (c_bFlag_ != 1)
        // {
            char l_acBuf[100] = {0};
            int l_iOffset = 26;
            memcpy(&l_acBuf[0], c_aucSetLidarPose, l_iOffset);
            l_acBuf[l_iOffset++] = 1;  // 雷达位姿

            // fillFloatTo32Bytes(l_dpose_X, l_acBuf, l_iOffset);
            // fillFloatTo32Bytes(l_dpose_Y, l_acBuf, l_iOffset);
            // fillFloatTo32Bytes(0, l_acBuf, l_iOffset);
            // fillFloatTo32Bytes(l_dpose_A, l_acBuf, l_iOffset);

            data_lock_.lock();
            // ROS_INFO("send pose: x,y ", local_plan[0],local_plan[1]);
            fillFloatTo32Bytes(local_plan[0], l_acBuf, l_iOffset);
            fillFloatTo32Bytes(local_plan[1], l_acBuf, l_iOffset);
            fillFloatTo32Bytes(c_settingEulorRPYDegree[2], l_acBuf, l_iOffset); //hsq 0值改为建图起点航向角（考虑到运行到建图起点航向角相差不大）int(c_startingPoint_yawDegree * 1000)
            fillFloatTo32Bytes(local_plan[2], l_acBuf, l_iOffset);
            data_lock_.unlock();

            std::cout << "[Send POSE], X Y Yaw and l_flag: " << local_plan[0] << ", " <<
                    local_plan[1] << ", " << c_settingEulorRPYDegree[2] << ", " << local_plan[2] << std::endl;

            // 写入帧长、校验位、帧尾
            l_acBuf[2] = (l_iOffset & 0xff00) >> 8;
            l_acBuf[3] = l_iOffset & 0xff;
            l_acBuf[l_iOffset++] = 0;
            l_acBuf[l_iOffset++] = checkXOR(&l_acBuf[2], l_iOffset - 3);
            l_acBuf[l_iOffset++] = 0xEE;
            l_acBuf[l_iOffset++] = 0xEE;

            if (c_bRun_)
                sendMsg_(sockfd, l_acBuf, l_iOffset);
            usleep(100000);//hsq 1000000
        // }
        c_bCreat = false;
    }

    // ****************************** //

    void makeSpeedInfo(int pi_vx,
                       int pi_vy,
                       int pi_vyaw,
                       int p_time,
                       int p_isglobal,
                       char (&l_acBuf)[100],
                       int& l_iLen)
    {
        char l_acBuf2[16] = {0};
        char l_cOffset;
        char l_cTail_ = 0x03;
        l_iLen = 0;
        l_acBuf[0] = 0x02;
        l_iLen += 1;
        strcpy(l_acBuf + l_iLen, "sMN mNPOSSetSpeed ");
        l_iLen += 18;

        // 速度X
        l_cOffset = sprintf(l_acBuf2, "%X", int(pi_vx));  //
        strncpy(l_acBuf + l_iLen, l_acBuf2, l_cOffset);
        l_iLen += (l_cOffset);
        strcpy(l_acBuf + l_iLen, " ");
        l_iLen += 1;

        // 速度Y
        l_cOffset = sprintf(l_acBuf2, "%X", int(pi_vy));
        strncpy(l_acBuf + l_iLen, l_acBuf2, l_cOffset);
        l_iLen += (l_cOffset);
        strcpy(l_acBuf + l_iLen, " ");
        l_iLen += 1;

        // 速度Yaw
        l_cOffset = sprintf(l_acBuf2, "%X", int(pi_vyaw));
        strncpy(l_acBuf + l_iLen, l_acBuf2, l_cOffset);
        l_iLen += (l_cOffset);
        strcpy(l_acBuf + l_iLen, " ");
        l_iLen += 1;

        // 时间
        l_cOffset = sprintf(l_acBuf2, "%X", int(p_time));
        strncpy(l_acBuf + l_iLen, l_acBuf2, l_cOffset);
        l_iLen += (l_cOffset);
        strcpy(l_acBuf + l_iLen, " ");
        l_iLen += 1;

        // 全局/局部
        l_cOffset = sprintf(l_acBuf2, "%X", int(p_isglobal));
        strncpy(l_acBuf + l_iLen, l_acBuf2, l_cOffset);
        l_iLen += (l_cOffset);
        strcpy(l_acBuf + l_iLen, " ");
        l_iLen += 1;

        strcpy(l_acBuf + l_iLen, &l_cTail_);
        l_iLen += 1;
    }

    void makeGetPoseInfo(char (&l_acBuf)[100], int& l_iLen)
    {
        char l_cTail_ = 0x03;

        l_iLen = 0;
        l_acBuf[0] = 0x02;
        l_iLen += 1;
        if (c_isDelay)
            strcpy(l_acBuf + l_iLen, "sMN mNPOSGetPose 1");
        else
            strcpy(l_acBuf + l_iLen, "sMN mNPOSGetPose 0");
        l_iLen += 18;
        strcpy(l_acBuf + l_iLen, &l_cTail_);
        l_iLen += 1;
    }

    void AskPose()
    {
        int l_iLen = 0;
        // OS_CPU_SR cpu_sr ;
        char l_acBuf[100] = {0};  // 转了
        makeGetPoseInfo(l_acBuf, l_iLen);
        while (ros::ok())
        {
            if (c_bConnected)
            {
                sendMsg_(sockfd, l_acBuf, l_iLen);
            }
            // usleep(125000);
            usleep(c_interval);
        }
    }

    void SendSpeed()
    {
        while (ros::ok())
        {
            if (c_bConnected)
            {
                int l_iLen = 0;
                // OS_CPU_SR cpu_sr ;
                char l_acBuf[100] = {0};  // 转了
                {
                    std::lock_guard<std::mutex> l_lock(lock);
                    makeSpeedInfo(c_twist[0] * 1000,
                                  c_twist[1] * 1000,
                                  c_twist[2] * 100,
                                  getTimeMsec(),
                                  1,
                                  l_acBuf,
                                  l_iLen);
                }
                // makeSpeedInfo(0, 0, 0, 0, 1, l_acBuf, l_iLen);
                sendMsg_(sockfd, l_acBuf, l_iLen);
            }
            usleep(50000);
        }
    }
};

int main(int argc, char* argv[])
{
    ros::init(argc, argv, "wanji_client_node");
    // ros::Time::init();

    if (argc < 3)
    {
        fprintf(stderr, "Usage: $ rosrun comm_tcp client_node <hostname> <port>\n");
        exit(0);
    }

    // g_file.open(g_sFilePath.c_str(), std::ofstream::out | std::ofstream::app);
    // g_file << "Time"
    //        << ","
    //        << "LidarPoseY"
    //        << ","
    //        << "LidarPoseZ"
    //        << ","
    //        << "RtkPoseYaw"
    //        << "\n";

    int addr_len = sizeof(struct sockaddr_in);
    char* l_serverIp = argv[1];
    int l_portno = atoi(argv[2]);
    int l_requestDelay = atoi(argv[3]);
    int l_requestInterval = atoi(argv[4]);
    std::string l_topicName = argv[5];
    Listener listener(l_serverIp, l_portno, l_requestDelay, l_requestInterval, l_topicName);

    std::thread heart(&Listener::AskPose, &listener);
    heart.detach();

    // getTimeMsec() ;
    // std::thread sendSpeed(&Listener::SendSpeed, &listener);
    // sendSpeed.detach();
    ros::Rate loop_rate(100);

    int l_len = 0;
    int l_endIdx = 0;
    char l_acBufList[4096];
    while (ros::ok())
    {
        char l_acBufTmp[4096] = {};
        u_char* l_pucBufTmp = NULL;
        s_AscProtocol l_msg;
        int l_iDataNum = recvfrom(listener.sockfd,
                                  l_acBufTmp,
                                  4096,
                                  0,
                                  (struct sockaddr*)&listener.c_serverAddr_,
                                  (socklen_t*)&addr_len);

        if (0 == l_iDataNum || !listener.c_bConnected)
        {
            listener.c_bConnected = listener.agvNetInit_(
                listener.sockfd, listener.c_clientAddr_, listener.c_serverAddr_);
        }

        if (l_iDataNum > 0 && l_iDataNum <= 4096)
        {
            if ((l_len + l_iDataNum) <= (4096))
            {
                memcpy(&l_acBufList[l_len], l_acBufTmp, l_iDataNum);
                l_len += l_iDataNum;
                l_len %= 4096;
            }
            else
            {
                int offset = 0;
                memcpy(&l_acBufList[l_len], l_acBufTmp, 4096 - l_len);
                offset = 4096 - l_len;
                memcpy(&l_acBufList[0], &l_acBufTmp[offset], l_iDataNum - offset);
                l_len = l_iDataNum - offset;
            }

            while (l_len != l_endIdx)
            {
                l_pucBufTmp = listener.getSICKCMD(l_endIdx, l_acBufList, l_len);
                if (!l_pucBufTmp)
                    continue;

                int l_iDataProcLen = strlen((char*)l_pucBufTmp);
                l_msg = listener.seprateSICKCmd(l_pucBufTmp, l_iDataProcLen);
                listener.selectSICKProtocol(l_msg);

                if (l_pucBufTmp)
                {
                    delete l_pucBufTmp;
                    l_pucBufTmp = NULL;
                }
            }
        }
        ros::spinOnce();
        loop_rate.sleep();
    }

    return 0;
}
