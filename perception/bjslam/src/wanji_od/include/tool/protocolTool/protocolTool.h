#pragma once
#include <string.h>
#include <string>
#include <vector>

/**
 * @description: 协议要求最低长度为34 去除校验位和帧尾后最低30 验证并补0以满足最低长度
 * @param {u_char*} &p_pcBufCMD 完整协议
 * @param {int} p_iOffset ascii读取偏移量
 * @param {std::string&} p_sData 返回提取的string
 * @return {bool} true读取成功 false 协议长度不足strLen
 **/
void fillAskField(char* p_pcBufResponse, int& p_iLen, int p_iDataNum);

/**
 * @description: 异或校验
 * @param {char*} &p_pcBufCMD 完整协议
 * @param {int} p_iOffset ascii读取偏移量
 * @param {std::string&} p_sData 返回提取的string
 * @return {bool} true读取成功 false 协议长度不足strLen
 **/
char checkXOR(char* p_pcBufResponse, int p_iLen);
u_char checkXOR(u_char* p_pcBufResponse, int p_iLen);

/**
 *
 * @description:  获取当前协议第3、4个字节的
 * @param {int} p_iData   数值
 * @param {u_char*} p_pcBufCMD 当前协议
 * @param { int&} p_iLen
 * @return {*}
 * @other:
 */
uint32_t getFrameLen(u_char*& p_pucBuf, int p_iLen);

/**
 * @description: ascii协议提取string
 * @param {u_char*} &p_pcBufCMD 完整协议
 * @param {int} p_iOffset ascii读取偏移量
 * @param {std::string&} p_sData 返回提取的string
 * @return {bool} true读取成功 false 协议长度不足strLen
 **/
bool asciiToString_(u_char*& p_pcBufCMD, int& p_iOffset, std::string& p_sData);

/**
 * @description: 字符串转ascii码 限制字符串长度不可超过255
 * @param {string&} p_sData
 * @param {char*&} p_pcBuf
 * @param {int&} p_iLen 从此字节开始 2字节字符串长度 + 字符串
 * @return {*}
 * @other:
 */
void stringToAscii_(std::string p_sData, char*& p_pcBuf, int& p_iLen);

/**
 * @description: 分割字符串
 * @param {const string&} p_sStr 待分割字符串
 * @param {std::vector<std::string>&} p_vList 分割后字符串List
 * @param {const std::string&} p_sSeparator 分隔符
 * @return {*}
 * @other:
 */
void splitString(const std::string& p_sStr,
                 std::vector<std::string>& p_vList,
                 const std::string& p_sSeparator);

/**
 * @description:IP eg:*********** 转ascii
 * @param {string&} p_sIp
 * @param {u_char*&} p_pucBuf
 * @param {int&} p_iLen
 * @return {*}
 * @other: 4个字节写入ascitt对应位置
 */
void ipToAscii(std::string p_sIp, char*& p_pucBuf, int& p_iLen);

/**
 *
 * @description:ascii 提取IP 内部自动验证IP段是否满足4个字节
 * @param {u_char*&} p_pucBuf
 * @param {int&} p_iLen
 * @param {string&} p_sData
 * @return {bool}  false  偏移量+4 少于 协议长度
 * @other:
 */
bool asciiToIP(u_char*& p_pucBuf, int& p_iLen, std::string& p_sIp);

/**
 *
 * @description: 提取2字节 转 int 自动修改偏移量
 * @param {u_char*&} p_pucBuf   完整协议
 * @param {int&} p_iLen 偏移量
 * @param {T&} p_iData
 * @return {bool} false 字节长度不足
 * @other:
 */
template <typename T> bool bytes16ToInt(u_char*& p_pucBuf, int& p_iLen, T& p_iData)
{
    // 协议长度 = 去帧头帧尾
    int l_iAllLen = (int)getFrameLen(p_pucBuf, p_iLen);
    // 帧头[2] + l_iAllLen + 帧尾[2] >=  p_iLen +Data_Len[2] +校验位[2] +  帧尾[2]
    if (l_iAllLen >= p_iLen + 2)
    {
        p_iData = (((p_pucBuf[p_iLen++] << 8) & 0xFF00 | p_pucBuf[p_iLen++]) & 0xFFFF);
        return true;
    }
    return false;
}

/**
 *
 * @description: 提取2字节 int / 1000.0 = float 自动修改偏移量
 * @param {u_char*&} p_pucBuf   完整协议
 * @param {int&} p_iLen 偏移量
 * @param {T&} p_iData
 * @return {bool} false 字节长度不足
 * @other:
 */
template <typename T> bool bytes16ToFloat(u_char*& p_pucBuf, int& p_iLen, T& p_fData)
{
    // 协议长度 = 去帧头帧尾
    int l_iAllLen = (int)getFrameLen(p_pucBuf, p_iLen);
    // 帧头[2] + l_iAllLen + 帧尾[2] >=  p_iLen +Data_Len[2] +校验位[2] +  帧尾[2]
    if (l_iAllLen >= p_iLen + 2)
    {
        int l_iData = (((p_pucBuf[p_iLen++] << 8) & 0xFF00 | p_pucBuf[p_iLen++]) & 0xFFFF);
        p_fData = l_iData / 1000.0;
        return true;
    }
    return false;
}

/**
 *
 * @description: 提取4字节 转 int 自动修改偏移量
 * @param {u_char*&} p_pucBuf   完整协议
 * @param {int&} p_iLen 偏移量
 * @param {T&} p_iData
 * @return {bool} false 字节长度不足
 * @other:
 */
template <typename T> bool bytes32ToInt(u_char*& p_pucBuf, int& p_iLen, T& p_iData)
{
    // 协议长度 = 去帧头帧尾
    int l_iAllLen = (int)getFrameLen(p_pucBuf, p_iLen);
    // 帧头[2] + l_iAllLen + 帧尾[2] >=  p_iLen +Data_Len[4] +校验位[2] +  帧尾[2]
    if (l_iAllLen >= p_iLen + 4)
    {
        p_iData = (p_pucBuf[p_iLen++] << 24 & 0xff000000 | p_pucBuf[p_iLen++] << 16 & 0xff0000
                   | p_pucBuf[p_iLen++] << 8 & 0xff00 | p_pucBuf[p_iLen++]);
        return true;
    }
    return false;
}

/**
 *
 * @description:  int转2字节 写入协议
 * @param {int} p_iData   数值
 * @param {u_char*} p_pcBufCMD 当前协议
 * @param { int&} p_iLen
 * @return {*}
 * @other:
 */
void fillIntTo16Bytes(int p_iData, char* p_pcBufCMD, int& p_iLen);

/**
 *
 * @description:  int转4字节 写入协议
 * @param {int} p_iData   数值
 * @param {u_char*} p_pcBufCMD 当前协议
 * @param { int&} p_iLen
 * @return {*}
 * @other:
 */
void fillIntTo32Bytes(int p_iData, char* p_pcBufCMD, int& p_iLen);

template <typename T> void fillFloatTo32Bytes(T p_fData, char* p_pcBufCMD, int& p_iLen)
{
    // multi 1000 to int
    int l_iData = (int)(p_fData * 1000);
    p_pcBufCMD[p_iLen++] = (l_iData & 0xff000000) >> 24;
    p_pcBufCMD[p_iLen++] = (l_iData & 0xff0000) >> 16;
    p_pcBufCMD[p_iLen++] = (l_iData & 0xff00) >> 8;
    p_pcBufCMD[p_iLen++] = l_iData & 0xff;
}

/**
 *
 * @description: 提取4字节 int/1000.0 = float 自动修改偏移量
 * @param {u_char*&} p_pucBuf   完整协议
 * @param {int&} p_iLen 偏移量
 * @param {T&} p_iData
 * @return {bool} false 字节长度不足
 * @other:
 */
template <typename T> bool bytes32ToFloat(u_char*& p_pucBuf, int& p_iLen, T& p_fData)
{
    // 协议长度 = 去帧头帧尾
    int l_iAllLen = (int)getFrameLen(p_pucBuf, p_iLen);
    // 帧头[2] + l_iAllLen + 帧尾[2] >=  p_iLen +Data_Len[4] +校验位[2] +  帧尾[2]
    if (l_iAllLen >= p_iLen + 4)
    {
        int p_iData = (p_pucBuf[p_iLen++] << 24 & 0xff000000 | p_pucBuf[p_iLen++] << 16 & 0xff0000
                       | p_pucBuf[p_iLen++] << 8 & 0xff00 | p_pucBuf[p_iLen++]);
        p_fData = p_iData / 1000.0;
        return true;
    }
    return false;
}

int intFrom16Bytes(int& p_iLen, u_char* p_pcBufCMD);

void fillStringTo8Bytes(int p_iLen, std::string p_sData, char* p_pcBufResponse);

void fillStringTo16Bytes(int p_iLen, std::string p_sData, char* p_pcBufResponse);