/*
 * @Description:
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2022-06-08 11:23:46
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-29 09:27:33
 */
#ifndef _LS_C16_DRIVER_H_
#define _LS_C16_DRIVER_H_

#include "input.h"
#include <string>
namespace wanji_driverOD {
class wanjiDriver {
  public:
    /**
     * @brief wanjiDriver
     * @param node          raw packet output topic
     * @param private_nh    通过这个节点传参数
     */
    wanjiDriver(uint32_t p_iLidarID,
                boost::function<void(uint32_t, boost::shared_ptr<s_LIDAR_RAW_DATAS>&, int, bool)>
                    p_packCB_);
    ~wanjiDriver();

    void start();

    void shutDown()
    {
        if (msop_input_)
            msop_input_->shutDown();
    }

  private:
    void scanDataProcessCb(ScanBuffer scanbuffer);

  private:
#pragma region "雷达相关"
    uint32_t c_uiLidarID = 0;        // 雷达id
    uint32_t c_uiCurID = 0;          // ID号 从0开始根据圈号累积
    int32_t c_iLastCycleNum = -1;    // 雷达圈号
    std::string c_sLaserType_ = "";  // 雷达类型
#pragma endregion

#pragma region "其他"
    wj_slam::SYSPARAMOD* c_stSysParam_;
    boost::function<void(uint32_t, boost::shared_ptr<s_LIDAR_RAW_DATAS>&, int, bool)> c_driverCB_;
    boost::shared_ptr<Input> msop_input_;
#pragma endregion
};

}  // namespace wanji_driverOD

#endif