/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2021-12-10 08:43:58
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-29 09:27:35
 */
#ifndef __LSLIDAR_INPUT_H_
#define __LSLIDAR_INPUT_H_

#include "./circleQueue.h"
#include "common/common_ex.h"
#include "tic_toc.h"
#include "tool/fileTool/fileTool.h"
#include <arpa/inet.h>
#include <boost/array.hpp>
#include <boost/function.hpp>
#include <boost/thread.hpp>
#include <errno.h>
#include <fcntl.h>
#include <inttypes.h>
#include <linux/errqueue.h>
#include <linux/net_tstamp.h>
#include <linux/sockios.h>
#include <mutex>
#include <net/if.h>
#include <netinet/in.h>
#include <pcap.h>
#include <poll.h>
#include <signal.h>
#include <sstream>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <sys/file.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>
#include <vector>

// #define DEBUGPCAP
namespace wanji_driverOD {
/***************************************** 720 Protocol
 * ***********************************************/

#define GET_SN 0x0510           // 获取设备SN号   // 0x0401
#define GET_BTN_TEMP 0x0402     // 获取底板温度
#define GET_VERTICAL_AG 0x0514  // 获取雷达垂直角度分辨率
#define GET_LEVEL_AG 0x051A     // 获取雷偏心修正表
#define GET_DEV_PARAM 0x0501    // 获取设备基本参数
#define GET_FIRE_STATUS 0x0507  // 获取发光通道使能情况

#define MIN_PROTOCOL_SIZE 32      // 720协议最小长度
#define RECV_BUFFER_SIZE 4096000  // 接收缓存大小 4000k
#define TEMP_BUFFER_SIZE 40960    // 临时缓存大小 40k
#define SCAN_SIZE_720_FE 1260     // 720扫描数据大小 FFEE格式
#define SCAN_SIZE_720_FD_1 1145  // 720扫描数据大小 FFDD格式 单回波19线（5 + 1188 + 60）
#define SCAN_SIZE_720_FD_2 1001  // 720扫描数据大小 FFDD格式 双回波16线（5 + 936 + 60）

/***************************************** 720 Protocol
 * ***********************************************/
#pragma region 结构体

typedef struct
{
    int fd;
    int port;
    int err_no;
    struct sockaddr_in local;
    struct sockaddr_in remote;
    struct timeval time_kernel;
    struct timeval time_user;
    int64_t prev_serialnum;
} socket_info;

// 扫描数据缓存
struct ScanBuffer
{
    bool m_bIsFull;            // 是否已收集整圈数据
    int32_t m_iCircleNum;      // 本圈圈号
    uint16_t m_frameCnt;       // 已收集帧计数
    uint16_t m_startIdx;       // 本圈起始帧序号
    s_LIDAR_RAW_DATAS m_data;  // 本圈数据

    ScanBuffer() : m_bIsFull(false), m_iCircleNum(-1), m_frameCnt(0), m_startIdx(0) {}
};
#pragma endregion

#pragma region 通信基类
class Input {
  public:
    Input(uint32_t p_uiLidarId, boost::function<void(ScanBuffer)> scandataCb);
    virtual ~Input();

    virtual bool isStart();
    /**
     * @function: sendPacket
     * @description: 发送网络数据
     * @param {uint8_t*} p_cBuf 当前发送数据
     * @param {const int} p_iSize 实际发送长度
     * @return {int} -1 失败 0 成功
     */
    virtual int sendPacket(uint8_t* p_cBuf, const int p_iSize);

    virtual void shutDown();
    virtual void requestBaseParam() = 0;

  protected:
    /**
     * @function: inputInit_
     * @description: 设备初始化 设备可用
     * @param {*}
     * @return true 初始化成功 false 失败
     * @others: null
     */
    virtual bool inputInit_();
    /**
     * @function: onlineInit_
     * @description: 网络初始化 建立套接字 设置端口复用 绑定端口 默认UDP
     * @param {*}
     * @return true 初始化成功 false 失败
     * @others: null
     */
    virtual bool onlineInit_(socket_info& inf, sockaddr_in& p_sMyAddr);
    /**
     * @function: recvSocketData
     * @description: 接收socket数据
     * @param {*}
     * @return {*}
     * @others: null
     */
    virtual void recvSocketData();
    /**
     * @function: parseSocketData
     * @description: 处理socket数据
     * @param {*}
     * @return {*}
     * @others: null
     */
    virtual void parseSocketData();
    /**
     * @function: parseLidarParam
     * @description: 参数查询相关解析
     * @param {*}
     * @return {*}
     * @others: null
     */
    virtual void parseLidarParam(const uint8_t* data, uint16_t len);
    /**
     * @function: parseScanData
     * @description: 扫描数据相关解析
     * @param {*}
     * @return {*}
     * @others: null
     */
    virtual void parseScanData(const uint8_t* data, uint16_t len);
    /**
     * @function: initScanBuffer
     * @description: 初始化扫描数据缓存
     * @param {*}
     * @return {*}
     * @others: null
     */
    void initScanBuffer(uint16_t npackets);
    /**
     * @function: ResetScanBuffer
     * @description: 重置ScanBuffer
     * @param {*}
     * @return {*}
     * @others: null
     */
    void
    ResetScanBuffer(ScanBuffer& buffer, int32_t circleNum, uint16_t startIdx, uint16_t npackets);

    virtual void checkXOR_(uint8_t p_vBuf[], int p_iSize);

  protected:
    wj_slam::SYSPARAMOD* c_stSysParam_;
    wj_slam::s_LidarConfig& c_stLaserCfg_;
    sockaddr_in c_myAddr_;
    sockaddr_in c_remoteAddr_;
    socket_info c_sockinf_;
    uint32_t c_uiLidarId;

    bool c_bRun = false;            // 运行标志
    bool c_bRecvThrOver = true;     // 线程结束标志
    bool c_bParseThrOver = true;    // 线程结束标志
    bool c_bScanStartFlag = false;  // 扫描数据起始标志
    bool c_bInitSuecss_ = false;
    uint16_t m_iLevevAgPkgStatus = 0x0001;  // 偏心表包已获取状态 初值为0x0001
    uint16_t c_uiExpPktIdx = 0;             // 当前期望包号，用以判断包是否乱序
    uint8_t c_iCurBufferFlag = 1;           // 当前使用buffer标志，1: buffer1; 2: buffer2。
    int c_iFirstCycleNum = -1;              // 预处理圈号       初值-1
    int c_iFirstPktIdx = -1;                // 预处理圈首包包号  初值-1

    CircleQueue<uint8_t> m_recvBuffer;  // 雷达UDP数据接收缓存
    ScanBuffer m_scanBuffer1;           // 扫描包接收缓存1（一圈）
    ScanBuffer
        m_scanBuffer2;  // 扫描包接收缓存2（一圈），两个缓存用于解决扫描数据包可能存在的乱序问题
    boost::function<void(ScanBuffer)> c_outScanDataCb;  // 扫描数据回调
};
#pragma endregion

#pragma region 720类
class Input720 : public Input {
  public:
    Input720(uint32_t p_uiLidarId, boost::function<void(ScanBuffer)> scandataCb);
    virtual ~Input720();

    void requestBaseParam();  // 查询参数

  protected:
    using Array = s_LIDAR_RAW_DATA::DArray;
    using Input::c_bInitSuecss_;
    using Input::c_stLaserCfg_;
    using Input::c_stSysParam_;
    using Input::c_uiLidarId;

#pragma region 变量

    std::string c_sVerAnglePath_;     // 垂直分辨率表路径
    std::string c_sCalibrationPath_;  // 水平偏心表路径
    int c_iLevelAngleData[7200];      // 偏心数据

    bool m_isGetSn = false;              // 是否获取到SN号
    bool m_isGetBaseParam = false;       // 是否获取到基本参数
    bool m_isGetVerAngleData = false;    // 是否获取到垂直角度
    bool m_isGetLevelAngleData = false;  // 是否获取到偏心角

#pragma endregion

#pragma region 函数
    void parseLidarParam(const uint8_t* data, uint16_t len);  // 参数查询相关解析
    /**
     * @function: requestSN
     * @description: 查询替换SN
     * @param {*}
     * @return {*}
     * @others: null
     */
    int requestSN_();

    /**
     * @function: analyseSN_
     * @description: 解析SN
     * @param {*}
     * @return {*}
     * @others: null
     */
    bool analyseSN_(const uint8_t* p_buf, const int p_iSize);

    /**
     * @function: compareSN
     * @description: 比较SN 并改写配置SN
     * @param {*}
     * @return {*}
     * @others: null
     */
    void compareSN(std::string& p_sSetSN, std::string p_sReadSN);
    /**
     * @description: 只有1个分割符c, 将s 按照分割符c 切割写入v  同时保留c
     * @param {string}  s 字符串
     * @param {vector<string>} v 输出列表
     * @param {string}  c 分割符
     * @return {*} WLR720F_NP 分割符：_NP  结果将为[0] WLR720F  [1] _NP
     */
    void splitStr_(const std::string& s, std::vector<std::string>& v, const std::string& c);

    /**
     * @function: requestBaseParam_
     * @description: 查询基本运行情况
     * @param {*}
     * @return {*}
     * @others: null
     */
    int requestBaseParam_();

    /**
     * @function: analyseBaseParam_
     * @description: 解析基本运行情况
     * @param {*}
     * @return {*}
     * @others: null
     */
    bool analyseBaseParam_(const uint8_t* p_buf, const int p_iSize);
    /**
     * @function: requestVerAngleData_
     * @description: 查询保存垂直表
     * @param {*}
     * @return -1 读取错误 0 读取成功
     * @others: null
     */
    int requestVerAngleData_();

    /**
     * @function: analyseVerAngleData
     * @description: 解析保存垂直表
     * @param {*}
     * @return {*}
     * @others: null
     */
    bool analyseVerAngleData_(const uint8_t* p_buf, const int p_size);

    /**
     * @function: saveVerAngleData_
     * @description: 保存垂直表
     * @param {*}
     * @return {*}
     * @others: null
     */
    void saveVerAngleData_(float* p_VerAngleData, uint p_uiSize, std::string p_sFilePath);

    /**
     * @function: requestLevelAngleData_
     * @description: 查询保存偏心表
     * @param {*}
     * @return -1 读取错误 0 读取成功
     * @others: null
     */
    int requestLevelAngleData_();

    /**
     * @function: analyseVerAngleData
     * @description: 解析保存偏心表
     * @param {*}
     * @return -1 读取错误 0~14 读取成功
     * @others: null
     */
    int analyseLevelAngleData_(const uint8_t* p_buf, const int p_size);

    /**
     * @function: saveLevelAngleData_
     * @description: 保存偏心表 14400 5个1行
     * @param {*}
     * @return {*}
     * @others: null
     */
    bool saveLevelAngleData_(int* p_VerAngleData, uint p_uiSize, std::string p_sFilePath);
#pragma endregion
};
#pragma endregion

}  // namespace wanji_driverOD

#endif