#pragma once

#include <mutex>
#include <string.h>

/***
 *
 * @class   Class of Cirle Queue
 * @brief   As a fixed size buffer
 * <AUTHOR>
 */

template <class T> class CircleQueue {
  public:
    CircleQueue();
    CircleQueue(unsigned int capacity);
    ~CircleQueue();

    void Reset(unsigned int capacity);

    unsigned int Push(const T* t, unsigned int len);
    unsigned int Pop(unsigned int len);
    unsigned int Data(T* t, unsigned int len);
    unsigned int Size();

  private:
    void AddIn(unsigned int n);
    void AddOut(unsigned int n);

    unsigned int m_capacity;
    unsigned int m_in;
    unsigned int m_out;
    unsigned int m_tSize;

    T* m_data;

    std::mutex m_mutex;
};

template <class T>
CircleQueue<T>::CircleQueue()
    : m_capacity(4096), m_in(0), m_out(0), m_tSize(sizeof(T)), m_data(nullptr)
{
    m_data = new T[m_capacity];
    if (m_data == nullptr)
    {
        fprintf(stderr, "[CircleQueue] : Failed to allocate memory, Capacity : %n", &m_capacity);
    }
}

template <class T>
CircleQueue<T>::CircleQueue(unsigned int size)
    : m_capacity(4096), m_in(0), m_out(0), m_data(nullptr)
{
    m_data = new T[m_capacity];
    if (m_data == nullptr)
    {
        fprintf(stderr, "[CircleQueue] : Failed to allocate memory, Capacity : %n", &m_capacity);
    }
}

template <class T> CircleQueue<T>::~CircleQueue()
{
    if (m_data != nullptr)
    {
        delete[] m_data;
        m_data = nullptr;
    }

    m_capacity = 0;
    m_in = 0;
    m_out = 0;
}

template <class T> void CircleQueue<T>::Reset(unsigned int capacity)
{
    std::lock_guard<std::mutex> locker(m_mutex);
    m_capacity = capacity;
    m_in = 0;
    m_out = 0;

    if (m_data != nullptr)
    {
        delete[] m_data;
        m_data = nullptr;
    }

    m_data = new T[m_capacity];
    if (m_data == nullptr)
    {
        fprintf(stderr, "[CircleQueue] : Failed to allocate memory, Capacity : %n", &m_capacity);
    }
}

template <class T> unsigned int CircleQueue<T>::Push(const T* t, unsigned int len)
{
    std::lock_guard<std::mutex> locker(m_mutex);
    if (t == nullptr)
    {
        fprintf(stderr, "[CircleQueue] : Push Failed");
        return 0;
    }

    unsigned int reallen = std::min(
        len, m_capacity);  // len 大于 Size() 时会覆盖掉已有数据，并且可能会改变m_in与m_out的关系
    if (reallen > m_capacity - Size())
    {
        fprintf(stderr, "[CircleQueue] : len > Size;\n");
        return 0;
    }
    // 跨4096分割保存
    if (m_in + reallen > m_capacity)
    {
        int n = (m_capacity - m_in);
        memcpy(&m_data[m_in], t, m_tSize * n);
        memcpy(m_data, &t[n], m_tSize * (reallen - n));
    }
    else
    {
        memcpy(&m_data[m_in], t, m_tSize * reallen);
    }

    AddIn(reallen);
    return reallen;
}

template <class T> unsigned int CircleQueue<T>::Data(T* t, unsigned int len)
{
    std::lock_guard<std::mutex> locker(m_mutex);
    if (t == nullptr)
    {
        fprintf(stderr, "[CircleQueue] : Get data Failed");
        return 0;
    }

    unsigned int reallen = std::min(len, Size());
    if (reallen == 0)
    {
        return 0;
    }

    if (m_out + reallen > m_capacity)
    {
        int n = m_capacity - m_out;
        memcpy(t, &m_data[m_out], m_tSize * n);
        memcpy(&t[n], m_data, m_tSize * (reallen - n));
    }
    else
    {
        memcpy(t, &m_data[m_out], m_tSize * reallen);
    }

    return reallen;
}

template <class T> unsigned int CircleQueue<T>::Pop(unsigned int len)
{
    std::lock_guard<std::mutex> locker(m_mutex);
    len = std::min(len, Size());
    AddOut(len);
    return len;
}

template <class T> void CircleQueue<T>::AddIn(unsigned int n)
{
    m_in = (m_in + n) % m_capacity;
}

template <class T> void CircleQueue<T>::AddOut(unsigned int n)
{
    m_out = (m_out + n) % m_capacity;
}

template <class T> unsigned int CircleQueue<T>::Size()
{
    return (m_in >= m_out) ? (m_in - m_out) : (m_in + m_capacity - m_out);
}
