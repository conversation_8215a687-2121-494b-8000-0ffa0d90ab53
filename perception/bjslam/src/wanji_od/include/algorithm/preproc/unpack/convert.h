/*
 * @Description: 接收 deta_X * 1600 网络原始数据 判断数据是否异常 解算点云
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2021-12-13 09:11:05
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-29 09:27:11
 */
#ifndef _WANJI_CONVERT_H_
#define _WANJI_CONVERT_H_

#include <boost/format.hpp>
#include <fstream>
#include <iostream>
#include <math.h>
#include <queue>
#include <stdint.h>
#include <sys/time.h>
#include <thread>
// #include <pcl/common/transforms.h>
#include <pcl_ros/point_cloud.h>

#include "../driver/wanji_driver.h"
#include "common_WLR720.h"
#include <tic_toc.h>
using namespace std;

namespace wanji_driverOD {
struct st_TWithTS
{
    boost::shared_ptr<s_LIDAR_RAW_DATAS> m_data;
    int m_iFrameId;
    int m_iTimestamp;
    int m_iEndTimestamp;
    int m_iRecvTimestamp;
    bool m_bIsReConnect;
    st_TWithTS()
    {
        m_data = NULL;
        m_iFrameId = 0;
        m_iTimestamp = 0;
        m_iEndTimestamp = 0;
        m_iRecvTimestamp = 0;
        m_bIsReConnect = false;
    }
    st_TWithTS& operator=(st_TWithTS& in)
    {
        this->m_data = in.m_data;
        this->m_iFrameId = in.m_iFrameId;
        this->m_iTimestamp = in.m_iTimestamp;
        this->m_iEndTimestamp = in.m_iEndTimestamp;
        this->m_iRecvTimestamp = in.m_iRecvTimestamp;
        this->m_bIsReConnect = in.m_bIsReConnect;
        return *this;
    }
};

#pragma region 基类
class Convert {
  public:
    Convert(uint32_t p_uiId);
    ~Convert();

    void setTransToBase()
    {
        std::string l_sStr = "lidar[" + c_stLaserCfg_.m_sLaserName + "] transBase";
        c_transToBase = c_stLaserCfg_.m_transToBase;
        c_transToBase.printf(l_sStr);
        // 蹭这个接口设置雷达盲区
        setBlindZeroAngOffset_();
    }

    virtual void start();

    virtual void reStart();

    virtual void stop();

    virtual bool isStop();

    virtual void shutdown();

    virtual bool isShutdown();

    void run();

    /**
     * @function: getPktDataTime_
     * @description: 根据不同型号雷达 提取包内部时间信息
     * @param {const s_LIDAR_RAW_DATA::DArray&} p_rawData 当前包数据
     * @return {timeval} 返回包内部时间信息
     * @others: {*}
     */
    virtual timeval getPktDataTime_(const s_LIDAR_RAW_DATA::DArray& p_rawData) = 0;

    /**
     * @function: getPktAng_
     * @description: 获取每包的角度
     * @param {const s_LIDAR_RAW_DATA::DArray&} p_rawData 当前包数据
     * @return {uint32_t} 返回包内部角度
     * @others: {*}
     */
    virtual uint32_t getPktAng_(const s_LIDAR_RAW_DATA::DArray& p_rawData) = 0;

    /**
     * @function: processScan
     * @description: 接收不同雷达网络原始数据 转化为该类格式 记录系统时间
     * @param {void*&} p_pScanMsg 原始数据
     * @return {*}
     * @others: {*}
     */
    virtual void processScan(boost::shared_ptr<s_LIDAR_RAW_DATAS> p_pScanMsg,
                             int p_iFrameId,
                             bool isReNewConnect);

  protected:
    st_TWithTS getRawData_();

    void paramInit_();

    bool hasPointCloud_();

    /**
     * @function: saveErrorLogFile_
     * @description: 点云当前帧异常 相关信息保存
     * @param {std::string} p_sPath 存储路径
     * @param {int} p_iLastTime 上一帧点云时间
     * @param {int} p_iCurTime  当前点云时间
     * @param {int} p_iCurID 帧ID
     * @return {bool} true 点云有效
     * @others: {*}
     */
    void saveErrorLogFile_(std::string p_sPath,
                           std::string p_sLaserName,
                           int p_iLastTime,
                           int p_iCurTime,
                           int p_iCurID);

    /**
     * @function: isValidData_
     * @description: 根据距离/角度盲区/最小强度 过滤 点云
     * @param {u_int&} p_uiIntensity 当前点云强度
     * @param {float&} p_fDist 当前点云距离
     * @param {float&} p_fAzimuthAngle  当前点云方位角
     * @return {bool} true 点云有效
     * @others: {*}
     */
    bool isValidData_(u_int& p_uiIntensity, float& p_fDist, float& p_fAzimuthAngle);

    /**
     * @function: setBlindZeroAngOffset_
     * @description: 设置雷达盲区零度偏移 并约束起始/终止角
     * @param {*}
     * @return {*}
     * @others: {*}
     */
    void setBlindZeroAngOffset_();

    /**
     * @function: angAntiRotate
     * @description: 改变雷达角度 适合逆时针旋转方式
     * @param {float&} p_fAzimuthAngle 雷达输出角度
     * @return {*}
     * @others: {*}
     */
    virtual void angAntiRotate(float& p_fAzimuthAngle) = 0;

    /**
     * @function: unpackData_
     * @description: 根据不同型号雷达 点云解算
     * @param {st_TWithTS&} p_pScanMsg 当前点云
     * @param {int} p_iId 当前帧id
     * @return {*}
     * @others: {*}
     */
    virtual void unpackData_(st_TWithTS& p_pScanMsg, int p_iId) = 0;

    /**
     * @function: checkDataTime_
     * @description: 检查原始数据时间是否异常
     * @param {int&} p_iCurTime 当前帧时间
     * @param {int&} p_iLastTime 上一帧时间
     * @param {int&} p_iCurId 帧id
     * @return {*}
     * @others: {*}
     */
    virtual bool checkDataTime_(int& p_iCurTime, int& p_iLastTime, int& p_iCurId);

    /**
     * @function: getFirstPktTime_
     * @description: 获取理论首包的时间
     * @param {boost::shared_ptr<s_LIDAR_RAW_DATAS>} p_pScanMsg 当前帧Pkt
     * @param {timeval&} p_stFirstPktLidarTime 首包雷达内部时间戳
     * @param {timeval&} p_stFirstPktRecvTime 首包雷达接收时间戳
     * @return {bool} 是否找到首包时间 否则丢弃此帧
     * @others: {*}
     */
    virtual bool getFirstPktTime_(boost::shared_ptr<s_LIDAR_RAW_DATAS> p_pScanMsg,
                                  timeval& p_stFirstPktLidarTime,
                                  timeval& p_stFirstPktRecvTime);

    /**
     * @function: checkDataHeader_
     * @description: 检查原始数据头角度范围是否异常
     * @param {st_TWithTS&} p_pScanMsg 原始数据
     * @param {int&} p_iCurId 帧id
     * @return {*}
     * @others: {*}
     */
    virtual bool checkDataHeader_(st_TWithTS& p_pScanMsg, int& p_iCurId) = 0;

  protected:
    typedef pcl::PointXYZI POINTTYPEOUT;
    typedef pcl::PointCloud<POINTTYPEOUT> PCOut;
    typedef pcl::PointCloud<POINTTYPEOUT>::Ptr PCOutPtr;

#pragma region 常量 / 结构体定义
    // 1/180.0*M_PI
    static constexpr double D2R = 0.017453292519943;
    // 1*180.0/M_PI
    static constexpr double R2D = 57.295779513082;

    static constexpr float MIN_DIASTANCE = 0.10f;  //[m]
    static constexpr u_int MIN_INTENSITY = 3;
    static constexpr float GROUND_POINT = 0.10f;  // [m]

    constexpr static const float ROTATION_RESOLUTION = 0.001f;  // [deg]    sin/cos查表最小角度
    constexpr static const float ROTATION_RESOLUTION_INV = 1.0f / ROTATION_RESOLUTION;  // [1/deg]
    constexpr static const u_int ROTATION_MAX_UNITS =
        360 * ROTATION_RESOLUTION_INV;  // [deg/100]长度
    union two_bytes
    {
        uint16_t uint;
        uint8_t bytes[2];
    };  // 联合体-成员共用地址 uint的值 为 byte[1]byte[0]合起来 实现byte转换uint
    union two_bytes_signed
    {
        int16_t sint;
        uint8_t bytes[2];
    };  // 联合体-成员共用地址 int的值 为 byte[1]byte[0]合起来 实现byte转换int
#pragma endregion

    wj_slam::SYSPARAMOD* c_stSysParam_;
    wj_slam::s_LidarConfig& c_stLaserCfg_;
    wj_slam::s_POSE6D c_transToBase;
    uint32_t c_uiLidarID;
    uint32_t c_uiLaserNum;  // 雷达数量 用于判断偏心表/垂直表读取
    float c_afSinRotTable_[ROTATION_MAX_UNITS];          // sin表
    float c_afCosRotTable_[ROTATION_MAX_UNITS];          // cos表
    std::vector<wj_slam::s_BlindConfig> c_vstBlindInfo;  // 角度盲区
    std::queue<st_TWithTS> c_rawDataBuffer_;             // 接收原始数据队列
    s_PCloud c_pcMidOut_;                                // 中间线

    int c_iFrameTime = 100.0;  // 帧耗时
    int c_iLastLidarTime = 0;  // 上一帧雷达自带时间
    int c_iRecvLastTime = 0;   // 上一帧接收雷达时间 (系统时间)
    int c_iCurrID = -1;
    bool c_bInitCheckTime_ = true;  // 初始化耗时检验
    bool c_bUnpcakRun_;
    bool c_bUnpcakRunOver_;
    bool c_bShutDown_;
    bool c_bShutDownOver_ = true;
    std::string c_sPkgPath = "";
    boost::function<void(uint32_t)> c_netErrorCb_;
};
#pragma endregion

#pragma region 720类
class Convert720 : public Convert {
  protected:
#pragma region 常量 / 结构体定义
    constexpr static const int RAW_SCAN_SIZE = 4;  // 占用字节 （距离2 脉宽1 置信度1）
    constexpr static const int SCANS_PER_BLOCK = 19;  // 发光通道19线
    constexpr static const int BLOCK_DATA_SIZE =
        (SCANS_PER_BLOCK * RAW_SCAN_SIZE);  //  19*4 每块数据长度

    constexpr static const int BLOCKS_PER_PACKET = 15;  // 120包每包15块 每块80字节
    constexpr static const int PACKET_STATUS_SIZE = 4;
    constexpr static const int SCANS_PER_PACKET = (SCANS_PER_BLOCK * BLOCKS_PER_PACKET);  //  19*15

    constexpr static const int MIDLINE_POINT_SIZE = 7200;       // 中间线点数
    constexpr static const float DISTANCE_RESOLUTION = 0.004f;  // 720最小精度4mm

    // IMU转换参数
    constexpr static const double c_fAngScalar = 1.0 * 8.75 / 1000.0 / 180.0 * M_PI;  // rad/s
    constexpr static const double c_fAccScalar = 1.0 * 0.061 / 1000.0 * 9.80665;      // m/s^2

    typedef struct RawBlock
    {
        uint16_t header;
        uint16_t rotation;
        uint8_t data[BLOCK_DATA_SIZE];  // data[19*4]
    } s_RawBlock;

    typedef struct RawAddtionMsg
    {
        uint16_t header;
        uint8_t rads[2];
        uint8_t time[6];
        uint8_t nsec[4];
        uint8_t angVel[6];
        uint8_t accel[6];
    } s_RawAddtionMsg;

    typedef struct RawPacket
    {
        s_RawBlock blocks[BLOCKS_PER_PACKET];  // blocks[15]
        s_RawAddtionMsg addmsg;
    } s_RawPacket;
#pragma endregion

  public:
#pragma region 公有函数
    Convert720(
        uint32_t p_uiLidarID,
        boost::function<void(uint32_t, areaPcPtr&, PCOutPtr&, PCOutPtr&, int, int, u_int32_t)>
            p_fECb);

    virtual ~Convert720(){};

#pragma endregion

  protected:
    typedef pcl::PointCloud<pcl::PointXYZI> PointCloud;
    boost::function<void(uint32_t, areaPcPtr&, PCOutPtr&, PCOutPtr&, int, int, u_int32_t)> c_fECb_;
    s_PCloud c_pcRawOut_[WLR720_SCANS_PER_FIRING];
#pragma region 雷达相关
    float c_fAngleResolutionVal_;  // 水平角分辨率
    float c_fLineAngleGap_;        // 4条线发光通道 间隔角度
    float c_fGroupAngleVal_;       // 中间线水平角分辨率
#pragma endregion

  private:
    areaPcPtr c_allAreaPc_;

#pragma region 检测过滤相关
    bool filterGroundPoint(float&);
    bool filterUpAreaPoint(float&, float&, float&);
    bool filterNoisePoint(u_int&, float&);
    bool extractRoiPoint(float& p_fCurDistance, float& p_fMinDistance, float& p_fMaxDistance);
    bool isValidData(scaleTablePtr& p_sacleTablePtr, int p_iScanID, int p_iScaleID);
    void setPointMatrix(int& p_iIdx, pcMatPtr& p_pcMat, int& scanID, int& scaleID);
#pragma endregion

  protected:
#pragma region 修正表相关
    float c_afVertAngle_[SCANS_PER_BLOCK] =  // 默认垂直表
        {0, 14, 12, 10, 8, 0, 6, 4, 2, 0, -2, -4, -6, -8, 0, -10, -12, -14, -16};
    float c_afAzimuthDiff_[SCANS_PER_BLOCK] =  // 方位角修正表?
        {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    float c_afVsinRotTable_[SCANS_PER_BLOCK];   // 垂直表修正sin
    float c_afVcosRotTable_[SCANS_PER_BLOCK];   // 垂直表修正cos
    int c_aiEccentricity_[MIDLINE_POINT_SIZE];  // 偏心修正表 离心
#pragma endregion

#pragma region 保护函数
    /**
     * @function: checkDataHeader_
     * @description: 检查原始数据头角度范围是否异常
     * @param {st_TWithTS&} p_pScanMsg 原始数据
     * @param {int&} p_iCurId 帧id
     * @return {*}
     * @others: {*}
     */
    virtual bool checkDataHeader_(st_TWithTS& p_pScanMsg, int& p_iCurId);

    /**
     * @function: angAntiRotate
     * @description: 改变雷达角度 并适合逆时针旋转方式
     * @param {float&} p_fAzimuthAngle 雷达输出角度
     * @return {*}
     * @others: {*}
     */
    virtual void angAntiRotate(float& p_fAzimuthAngle);

    /**
     * @function: unpackData_
     * @description: 根据不同型号雷达 点云解算
     * @param {st_TWithTS&} p_pScanMsg 当前点云
     * @param {int} p_iId 当前帧id
     * @return {*}
     * @others: {*}
     */
    virtual void unpackData_(st_TWithTS& p_pScanMsg, int p_iId);

    /**
     * @function: checkLines
     * @description: 检查线束的点数是否异常
     * @param {s_PCloud &[WLR720_SCANS_PER_FIRING]} p_rawOut 当前点云
     * @return {*}
     * @others: {*}
     */
    virtual bool checkLines(s_PCloud (&p_rawOut)[WLR720_SCANS_PER_FIRING], int p_iCurId);

    /**
     * @function: getPktDataTime_
     * @description: 根据不同型号雷达 提取包内部时间信息
     * @param {const s_LIDAR_RAW_DATA::DArray&} p_rawData 当前包数据
     * @return {timeval} 返回包内部时间信息
     * @others: {*}
     */
    virtual timeval getPktDataTime_(const s_LIDAR_RAW_DATA::DArray& p_rawData);

    /**
     * @function: getPktAng_
     * @description: 获取每包的角度
     * @param {const s_LIDAR_RAW_DATA::DArray&} p_rawData 当前包数据
     * @return {uint32_t} 返回包内部角度
     * @others: {*}
     */
    virtual uint32_t getPktAng_(const s_LIDAR_RAW_DATA::DArray& p_rawData);

    /**
     * @function: setup_
     * @description: 加载雷达配置
     * @return {*}
     * @others: {*}
     */
    virtual bool setup_();

    /**
     * @function: loadVelAngleData_
     * @description: 读取垂直表
     * @param {float*} p_cVerAngData 待修改垂直表数据 需要有默认值 用于无垂直表时使用
     * @param {float*} p_cDiffData 待填充修正数据?
     * @param {uint32_t} p_uiSize  数组最大长度
     * @param {std::string} p_sFilePath 垂直表路径
     * @return {*}
     * @others: {*}
     */
    virtual bool loadVelAngleData_(float* p_cVerAngData,
                                   float* p_cDiffData,
                                   uint32_t p_uiSize,
                                   std::string p_sFilePath);

    /**
     * @function: loadLevelAngleData_
     * @description: 读取偏心表数据 默认以“，”分割
     * @param {float*} p_cData 代填充数组
     * @param {uint32_t} p_uiSize 数组最大长度
     * @param {std::string} 偏心表文件路径
     * @param {std::string} 分割符
     * @return {*}
     * @others: {*}
     */
    virtual bool loadLevelAngleData_(int* p_cData,
                                     uint32_t p_uiSize,
                                     std::string p_sFilePath,
                                     std::string p_sSplit = ",");

    /**
     * @function: setVelAngleData_
     * @description: 计算用于垂直修正的sin/cos
     * @param {float*} p_aVerAngData 垂直角度表
     * @param {float*} p_aSinData   sin表待填充
     * @param {float*} p_aCosData   cos表待填充
     * @param {uint32_t} p_uiSize   表长度
     * @return {*}
     * @others: {*}
     */
    virtual void
    setVelAngleData_(float* p_aVerAngData, float* p_aSinData, float* p_aCosData, uint32_t p_uiSize);

    /**
     * @function: unpack_
     * @description: 逐包解算雷达数据
     * @param {const s_LIDAR_RAW_DATA::DArray&} p_rawData 雷达原始网络数据
     * @param {double&} p_f64Time 每个点的时间
     * @param {s_PCloud} 原始数据
     * @param {s_PCloud&} pmid 中间线点云
     * @param {PCOutPtr&} pcOut 16*1800点云
     * @param {int&} p_iCnt 点号
     * @return {*}
     * @others: {*}
     */
    virtual void unpack_(int,
                         const s_LIDAR_RAW_DATA::DArray&,
                         double&,
                         AreaTablePtr&,
                         areaPcPtr&,
                         PCOutPtr&,
                         PCOutPtr&,
                         s_PCloud (&pc)[WLR720_SCANS_PER_FIRING]);

    /**
     * @function: unpackAddMsg_
     * @description: 逐包解算雷达数据中的附加数据
     * @param {const s_LIDAR_RAW_DATA::DArray&} p_rawData 雷达原始网络数据
     * @param {AddtionMsgPtr&} padd 附加消息 GNSS+IMU
     * @return {*}
     * @others: {*}
     */
    virtual void unpackAddMsg_(const s_LIDAR_RAW_DATA::DArray& p_rawData, AddtionMsgPtr& padd);
#pragma endregion
};
#pragma endregion

}  // namespace wanji_driverOD
#include "impl/convert.hpp"
#endif