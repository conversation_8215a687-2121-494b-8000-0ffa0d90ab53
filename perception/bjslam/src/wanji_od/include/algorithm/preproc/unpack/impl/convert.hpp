/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2021-12-13 00:11:04
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-29 09:27:16
 */
#include "../convert.h"
namespace wanji_driverOD {

#pragma region 基类
Convert::Convert(uint32_t p_uiId)
    : c_stSysParam_(wj_slam::SYSPARAMOD::getIn()), c_stLaserCfg_(c_stSysParam_->m_lidar[p_uiId]),
      c_transToBase(c_stLaserCfg_.m_transToBase), c_uiLidarID(p_uiId),
      c_iFrameTime(c_stLaserCfg_.m_uiFrameTime)
{
    std::string l_sLaserType = c_stLaserCfg_.m_dev.m_sDevType;
    // 初始化雷达盲区
    setBlindZeroAngOffset_();
    // sin/cos 查表初始化
    if (l_sLaserType == "WLR720FCW" || l_sLaserType == "WLR720FCW_NP")
        for (uint32_t rot_index = 0; rot_index < ROTATION_MAX_UNITS; ++rot_index)
        {
            float rotation = ROTATION_RESOLUTION * rot_index * D2R;
            c_afCosRotTable_[rot_index] = cosf(rotation);
            c_afSinRotTable_[rot_index] = sinf(rotation);
        }
    else
        for (uint32_t rot_index = 0; rot_index < ROTATION_MAX_UNITS; ++rot_index)
        {
            float rotation = ROTATION_RESOLUTION * rot_index * D2R;
            c_afCosRotTable_[rot_index] = -cosf(rotation);
            c_afSinRotTable_[rot_index] = sinf(rotation);
        }
}

Convert::~Convert()
{
    printf("exit conv[%d]\n", c_uiLidarID);
}

void Convert::start()
{
    paramInit_();
}

void Convert::paramInit_()
{
    c_iCurrID = -1;
    c_iLastLidarTime = 0;
    c_bUnpcakRun_ = true;
    c_bUnpcakRunOver_ = false;
    c_bShutDown_ = false;
    c_bShutDownOver_ = false;
}

void Convert::stop()
{
    c_bUnpcakRun_ = false;
    while (1)
    {
        if (isStop())
            break;
        else
            usleep(1000);
    }
}

void Convert::reStart()
{
    c_bUnpcakRunOver_ = false;
    c_bUnpcakRun_ = true;
}

bool Convert::isStop()
{
    if (false == c_bUnpcakRun_ && true == c_bUnpcakRunOver_)
        return true;

    return false;
}

void Convert::shutdown()
{
    c_bShutDown_ = true;
    while (1)
    {
        // printf("convert shutdown\n");
        if (isShutdown())
            break;
        else
            usleep(10000);
    }
}

bool Convert::isShutdown()
{
    if (true == c_bShutDown_ && true == c_bShutDownOver_)
        return true;
    else
        return false;
}

void Convert::run()
{
    uint32_t l_uiPkgNum = 0;
    timeval l_PktFirstTime, l_PktFirstTimeRecv;
    while (1)
    {
        if (hasPointCloud_() && c_bUnpcakRun_ && !c_bShutDown_)
        {
            st_TWithTS l_pRawData = getRawData_();
            c_iCurrID = l_pRawData.m_iFrameId;
            l_uiPkgNum = l_pRawData.m_data->m_vPackets.size();
            // // 提取首包的自带时间戳
            // if (!getFirstPktTime_(l_pRawData.m_data, l_PktFirstTime, l_PktFirstTimeRecv))
            // {
            //     //重新连接不打印
            //     if (!l_pRawData.m_bIsReConnect)
            //     {
            //         c_stSysParam_->m_lidar[c_uiLidarID].m_dev.setStatus(
            //             wj_slam::DevStatus::DATADATAERROR);
            //         LOGA(WWARN,
            //                "{} 雷达 [{}] 数据异常 | 帧[{}] 零度缺失，已丢弃!",
            //                WJODLog::getWholeSysTime(),
            //                c_stLaserCfg_.m_sLaserName,
            //                c_iCurrID);
            //     }
            //     continue;
            // }
            l_PktFirstTime = getPktDataTime_(l_pRawData.m_data->m_vPackets[0].m_data);
            l_PktFirstTimeRecv = l_pRawData.m_data->m_vPackets[0].m_stRecvTimestamp;
            // 首次更新时间戳设置 or 设备内部时间倒退
            if (c_stSysParam_->m_lidar[c_uiLidarID].m_dev.m_stTimeCfg.isResetAlign(l_PktFirstTime))
            {
                c_stSysParam_->m_lidar[c_uiLidarID].m_dev.m_stTimeCfg.setTimeAlign(
                    l_PktFirstTimeRecv, l_PktFirstTime);
                c_stSysParam_->m_lidar[c_uiLidarID].m_dev.m_stTimeCfg.printf(
                    "lidar " + c_stLaserCfg_.m_sLaserName);
                LOGA(WINFO,
                     "{} 雷达 [{}] 帧 {} 授时 procT {} {} syncT {} {} recvT {} {} align {}",
                     WJODLog::getWholeSysTime(),
                     c_stLaserCfg_.m_sLaserName,
                     c_iCurrID,
                     c_stLaserCfg_.m_dev.m_stTimeCfg.m_sLidarTimeCfg.m_sTimeProcStart.tv_sec,
                     c_stLaserCfg_.m_dev.m_stTimeCfg.m_sLidarTimeCfg.m_sTimeProcStart.tv_usec,
                     c_stLaserCfg_.m_dev.m_stTimeCfg.m_sLidarTimeCfg.m_sTimeSyncStart.tv_sec,
                     c_stLaserCfg_.m_dev.m_stTimeCfg.m_sLidarTimeCfg.m_sTimeSyncStart.tv_usec,
                     c_stLaserCfg_.m_dev.m_stTimeCfg.m_sLidarTimeCfg.m_sTimeSetSync.tv_sec,
                     c_stLaserCfg_.m_dev.m_stTimeCfg.m_sLidarTimeCfg.m_sTimeSetSync.tv_usec,
                     c_stLaserCfg_.m_dev.m_stTimeCfg.m_iMsTimeAlign);
            }

            // 获取此帧首包雷达内部时间戳
            l_pRawData.m_iTimestamp =
                c_stSysParam_->m_lidar[c_uiLidarID].m_dev.m_stTimeCfg.applyTimeAlign_(
                    l_PktFirstTime);
            // 获取此帧首包系统接收时间戳
            l_pRawData.m_iRecvTimestamp =
                wj_slam::getTimeDiffMs(l_PktFirstTimeRecv, c_stSysParam_->m_time.m_sTimeSetSync);

            unpackData_(l_pRawData, c_iCurrID);
        }
        else
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }

        if (!c_bUnpcakRun_ && !c_bUnpcakRunOver_)
        {
            while (!c_rawDataBuffer_.empty())
            {
                printf("lidar[%d] pop\n", c_uiLidarID);
                c_rawDataBuffer_.pop();
            }
            c_bUnpcakRunOver_ = true;
        }

        if (c_bShutDown_ && !c_bShutDownOver_)
        {
            while (!c_rawDataBuffer_.empty())
                c_rawDataBuffer_.pop();
            break;
        }
    }
    c_bShutDownOver_ = true;
}

/**
 * @function: checkDataTime_
 * @description: 检查原始数据时间是否异常
 * @param {int&} p_iCurTime 当前帧时间
 * @param {int&} p_iLastTime 上一帧时间
 * @param {int&} p_iCurId 帧id
 * @return {*}
 * @others: {*}
 */
bool Convert::checkDataTime_(int& p_iCurTime, int& p_iLastTime, int& p_iCurId)
{
    return true;
    // //首次默认通过
    // if (c_bInitCheckTime_)
    // {
    //     c_bInitCheckTime_ = false;
    //     //首次存储 程序启动时间 超时阈值
    //     saveErrorLogFile_(c_stSysParam_->m_sPkgPath + "/data/Log/netError.csv",
    //                       c_stLaserCfg_.m_sLaserName,
    //                       0,
    //                       c_stLaserCfg_.m_uiFrameTimeMax,
    //                       p_iCurId + 1);
    //     return true;
    // }

    // if (p_iCurTime - p_iLastTime > (int)c_stLaserCfg_.m_uiFrameTimeMax)
    // {
    //     LOGA(WERROR,
    //            "{} 雷达 [{}] 数据异常 | 帧[{}] 耗时: {} > {}，请参照以下步骤进行检查：",
    //            WJODLog::getWholeSysTime(),
    //            c_stLaserCfg_.m_sLaserName,
    //            p_iCurId + 1,
    //            p_iCurTime - p_iLastTime,
    //            c_stLaserCfg_.m_uiFrameTimeMax);
    //     LOGA(WERROR, " *********************************************** ");
    //     LOGA(WERROR, " * 1. 检查网线是否有松动，如果网线有松动，请重新拔插 ");
    //     LOGA(WERROR, " * 2. 手动Ping雷达IP地址，确认雷达正确连接 ");
    //     LOGA(WERROR, " * 3. 如果上述方法不能解决，请启动wireshark抓包，并联系万集开发人员。 ");
    //     LOGA(WERROR, " * **********************************************");
    //     c_stSysParam_->m_fae.setErrorCode("C16");
    //     return false;
    // }
    // else
    // return true;
}

/**
 * @function: getFirstPktTime_
 * @description: 获取理论首包的时间
 * @param {boost::shared_ptr<s_LIDAR_RAW_DATAS>} p_pScanMsg 当前帧Pkt
 * @param {timeval&} p_stFirstPktLidarTime 首包雷达内部时间戳
 * @param {timeval&} p_stFirstPktRecvTime 首包雷达接收时间戳
 * @return {bool} 是否找到首包时间 否则丢弃此帧
 * @others: {*}
 */
bool Convert::getFirstPktTime_(boost::shared_ptr<s_LIDAR_RAW_DATAS> p_pScanMsg,
                               timeval& p_stFirstPktLidarTime,
                               timeval& p_stFirstPktRecvTime)
{
    uint32_t l_uiPkgNum = p_pScanMsg->m_vPackets.size();
    uint32_t l_uiPktOffset_ = 0, l_uiCurrPktAng = 0;
    uint32_t l_iAngMin_ = 36000, l_iAngMax_ = 0;
    uint32_t l_auiAngId_[2] = {0, 0};  // 记录对应最小角度和最大角度的下标
    while (1)
    {
        // 最多遍历1组==12包，超过则结束 不足12包也结束
        if (l_uiPktOffset_ == l_uiPkgNum || l_uiPktOffset_ >= 12)
            break;
        l_uiCurrPktAng = getPktAng_(p_pScanMsg->m_vPackets[l_uiPktOffset_].m_data) % 36000;
        // 只找第一组内的Pkt,即[0,3300]
        if (l_uiCurrPktAng > 3300)
            break;
        // printf("ang %d T: %ld %ld\n",
        //        l_uiCurrPktAng,
        //        getPktDataTime_(p_pScanMsg->m_vPackets[l_uiPktOffset_].m_data).tv_sec,
        //        getPktDataTime_(p_pScanMsg->m_vPackets[l_uiPktOffset_].m_data).tv_usec);
        // 记录最大最小ang及对应下标
        if (l_uiCurrPktAng < l_iAngMin_)
        {
            l_iAngMin_ = l_uiCurrPktAng;
            l_auiAngId_[0] = l_uiPktOffset_;
        }
        if (l_uiCurrPktAng > l_iAngMax_)
        {
            l_iAngMax_ = l_uiCurrPktAng;
            l_auiAngId_[1] = l_uiPktOffset_;
        }
        // 直接找到了0deg
        if (l_uiCurrPktAng == 0)
        {
            p_stFirstPktLidarTime = getPktDataTime_(p_pScanMsg->m_vPackets[l_uiPktOffset_].m_data);
            p_stFirstPktRecvTime = p_pScanMsg->m_vPackets[l_uiPktOffset_].m_stRecvTimestamp;
            return true;
        }
        l_uiPktOffset_++;
    }
    // 未找到0deg包，丢弃此帧
    return false;
    // // 未找到0deg包，则判断是否找到2包，即最大最小包为不同包
    // if (l_auiAngId_[0] != l_auiAngId_[1])
    // {
    //     printf("min max %d %d\n", l_auiAngId_[0], l_auiAngId_[1]);
    //     l_iAngMax_ /= 300;
    //     l_iAngMin_ /= 300;
    //     timeval l_stTimeMinAng_ = getPktDataTime_(p_pScanMsg->m_vPackets[l_auiAngId_[0]].m_data);
    //     timeval l_stTimeMaxAng_ = getPktDataTime_(p_pScanMsg->m_vPackets[l_auiAngId_[1]].m_data);
    //     uint32_t l_timeDiff = wj_slam::getTimeDiffUs(l_stTimeMaxAng_, l_stTimeMinAng_);
    //     uint32_t l_timeRecvDiff =
    //         wj_slam::getTimeDiffUs(p_pScanMsg->m_vPackets[l_auiAngId_[1]].m_stRecvTimestamp,
    //                                p_pScanMsg->m_vPackets[l_auiAngId_[0]].m_stRecvTimestamp);
    //     int l_iTimeCut = l_timeDiff / (l_iAngMax_ - l_iAngMin_) * l_iAngMin_;
    //     int l_iTimeRecvCut = l_timeRecvDiff / (l_iAngMax_ - l_iAngMin_) * l_iAngMin_;
    //     p_stFirstPktLidarTime = wj_slam::getTimeTransUs(l_stTimeMinAng_, l_iTimeCut * -1);
    //     p_stFirstPktRecvTime = wj_slam::getTimeTransUs(
    //         p_pScanMsg->m_vPackets[l_auiAngId_[0]].m_stRecvTimestamp, l_iTimeRecvCut * -1);
    //     printf("computeT: rawT: min %ld | %ld - max %ld | %ld - diff: %d | %d - align: %ld |
    //     %ld\n",
    //            l_stTimeMinAng_.tv_sec,
    //            l_stTimeMinAng_.tv_usec,
    //            l_stTimeMaxAng_.tv_sec,
    //            l_stTimeMaxAng_.tv_usec,
    //            l_timeDiff,
    //            l_iTimeCut,
    //            p_stFirstPktLidarTime.tv_sec,
    //            p_stFirstPktLidarTime.tv_usec);
    //     printf(
    //         "computeT: recvT: min %ld | %ld - max %ld | %ld - diff: %d | %d - align: %ld |
    //         %ld\n", p_pScanMsg->m_vPackets[l_auiAngId_[0]].m_stRecvTimestamp.tv_sec,
    //         p_pScanMsg->m_vPackets[l_auiAngId_[0]].m_stRecvTimestamp.tv_usec,
    //         p_pScanMsg->m_vPackets[l_auiAngId_[1]].m_stRecvTimestamp.tv_sec,
    //         p_pScanMsg->m_vPackets[l_auiAngId_[1]].m_stRecvTimestamp.tv_usec,
    //         l_timeRecvDiff,
    //         l_iTimeRecvCut,
    //         p_stFirstPktRecvTime.tv_sec,
    //         p_stFirstPktRecvTime.tv_usec);
    //     return true;
    // }
    // return false;
}

void Convert::processScan(boost::shared_ptr<s_LIDAR_RAW_DATAS> p_pScanMsg,
                          int p_iFrameId,
                          bool isReNewConnect)
{
    // 只放入队列，不处理，节省时间
    if (c_bUnpcakRun_ && !c_bShutDown_)
    {
        st_TWithTS l_stTwithTs;
        l_stTwithTs.m_iFrameId = p_iFrameId;
        l_stTwithTs.m_data = p_pScanMsg;
        l_stTwithTs.m_bIsReConnect = isReNewConnect;
        c_rawDataBuffer_.push(l_stTwithTs);
    }
}

bool Convert::hasPointCloud_()
{
    return !c_rawDataBuffer_.empty();
}

st_TWithTS Convert::getRawData_()
{
    st_TWithTS l_pRawData;
    l_pRawData = c_rawDataBuffer_.front();
    c_rawDataBuffer_.pop();
    return l_pRawData;
}

void Convert::saveErrorLogFile_(std::string p_sPath,
                                std::string p_sLaserName,
                                int p_iLastTime,
                                int p_iCurTime,
                                int p_iCurID)
{
    time_t t = time(NULL);
    char ch[64] = {0};
    char result[100] = {0};
    strftime(ch, sizeof(ch) - 1, "%Y_%m_%d_%H:%M_%S", localtime(&t));  //%Y%m%d%H%M%S
    sprintf(result, "%s", ch);
    std::string l_sNow = result;

    std::fstream l_filePoseWR;
    l_filePoseWR.open(p_sPath.c_str(), std::ios::app);
    if (l_filePoseWR.is_open())
    {
        l_filePoseWR << p_sLaserName << "," << c_stLaserCfg_.m_uiFrameTimeMax << "," << l_sNow
                     << "," << p_iCurTime - p_iLastTime << "," << p_iCurID << std::endl;
    }
    l_filePoseWR.close();
}

bool Convert::isValidData_(u_int& p_uiIntensity, float& p_fDist, float& p_fAzimuthAngle)
{
    if (p_fDist < c_stSysParam_->m_lidar[c_uiLidarID].m_fMinDist
        || p_fDist > c_stSysParam_->m_lidar[c_uiLidarID].m_fMaxDist)
        return false;
    if (p_uiIntensity < c_stSysParam_->m_lidar[c_uiLidarID].m_uiMinIntensity)
        return false;
    // 顺时针角度逆时针化
    angAntiRotate(p_fAzimuthAngle);
    for (int i = 0; i < (int)c_vstBlindInfo.size(); i++)
    {
        if (p_fAzimuthAngle < c_vstBlindInfo[i].m_fEndAng
            && p_fAzimuthAngle > c_vstBlindInfo[i].m_fStartAng)
        {
            return false;
        }
    }
    return true;
}

void Convert::setBlindZeroAngOffset_()
{
    /*
    假设雷达线头朝下，盲区角度设定均为从720FCW 0 deg逆时针开始
      720FCW                               720F
        90                                  90
    180     0                           180     0
        270                                 270
        |                                   |
        |                                   |
    但720FCW 雷达角度从Y+顺时针旋转     720F 雷达角度从Y+ 逆时针旋转
      720FCW                               720F
        0                                   180
    270     90                          270     90
        180                                 0
        |                                   |
        |                                   |
    此处先不考虑旋转方向，认为雷达和盲区方向一致，为逆时针
      720FCW                               720F
        0
    90      270
        180
    将盲区设定 旋转 满足 雷达坐标系
      720FCW                               720F
      +270deg                             +270deg
    */
    std::vector<wj_slam::s_BlindConfig> l_vstBlindInfo =
        c_stSysParam_->m_lidar[c_uiLidarID].m_vBlindSector;
    std::vector<wj_slam::s_BlindConfig> l_vstAddBlindInfo;
    for (int i = 0; i < (int)l_vstBlindInfo.size(); i++)
    {
        l_vstBlindInfo[i].m_fStartAng += c_stSysParam_->m_lidar[c_uiLidarID].m_uiBlindZeroDegOffset;
        l_vstBlindInfo[i].m_fEndAng += c_stSysParam_->m_lidar[c_uiLidarID].m_uiBlindZeroDegOffset;
        // 此处为了保证(0,360]
        if (l_vstBlindInfo[i].m_fStartAng >= 360.0)
            l_vstBlindInfo[i].m_fStartAng -= 360.0;
        if (l_vstBlindInfo[i].m_fEndAng > 360.0)
            l_vstBlindInfo[i].m_fEndAng -= 360.0;
        // eg: [set]-[90,300] [align]-[270,120] == [270,0] [120];
        if (l_vstBlindInfo[i].m_fStartAng > l_vstBlindInfo[i].m_fEndAng)
        {
            // 此情况一般发生与engAng>180,此时分割盲区
            wj_slam::s_BlindConfig l_addBlind;
            l_vstBlindInfo[i].m_fEndAng = 360.0;
            l_addBlind.m_fStartAng = 0;
            l_addBlind.m_fEndAng = l_vstBlindInfo[i].m_fEndAng;
            l_vstAddBlindInfo.push_back(l_addBlind);
        }
    }

    // 新增盲区
    if (l_vstAddBlindInfo.size())
        l_vstBlindInfo.insert(
            l_vstBlindInfo.end(), l_vstAddBlindInfo.begin(), l_vstAddBlindInfo.end());

    // for (auto iter : l_vstBlindInfo)
    // {
    //     printf("lidar[%s] blind: %f | %f\n",
    //            c_stSysParam_->m_lidar[c_uiLidarID].m_sLaserName.c_str(),
    //            iter.m_fStartAng,
    //            iter.m_fEndAng);
    // }
    c_vstBlindInfo = l_vstBlindInfo;
}

#pragma endregion

#pragma region 720类
Convert720::Convert720(
    uint32_t p_uiLidarID,
    boost::function<void(uint32_t, areaPcPtr&, PCOutPtr&, PCOutPtr&, int, int, u_int32_t)> p_fECb)
    : Convert(p_uiLidarID), c_fECb_(p_fECb)
{
    c_uiLaserNum = c_stSysParam_->m_lidar.size();
    if (!setup_())
    {
        c_stSysParam_->m_lidar[p_uiLidarID].m_dev.setStatus(wj_slam::DevStatus::LOADPARAMERROR);
        LOGA(WERROR, "SLAM模块驱动初始化失败, 无法运行 | 请排查偏心/垂直表错误后重新启动程序");
        c_stSysParam_->m_fae.setErrorCode("C17");
        return;
    }
    start();
    std::thread procdata(&Convert720::run, this);
    procdata.detach();
}

#pragma region 基类纯虚函数

/**
 * @function: checkDataHeader_
 * @description: 检查原始数据头角度范围是否异常
 * @param {st_TWithTS&} p_pScanMsg 原始数据
 * @param {int&} p_iCurId 帧id
 * @return {*}
 * @others: {*}
 */
bool Convert720::checkDataHeader_(st_TWithTS& p_pScanMsg, int& p_iCurId)
{
    // int l_iFirstPkg = 0;
    // int l_iEndPkg = p_pScanMsg.m_data->m_vPackets.size() - 1;
    // int l_iFirstAng = p_pScanMsg.m_data->m_vPackets[l_iFirstPkg].m_data[3] << 8
    //                   | p_pScanMsg.m_data->m_vPackets[l_iFirstPkg].m_data[2];
    // int l_iEndAng = p_pScanMsg.m_data->m_vPackets[l_iEndPkg].m_data[3] << 8
    //                 | p_pScanMsg.m_data->m_vPackets[l_iEndPkg].m_data[2];
    // //二次判断 -  首包 尾包 角度是否正确
    // if (!(l_iFirstAng == 36000 && l_iEndAng == 35700))
    // {
    //     LOGA(WERROR,
    //            "{} 雷达 [{}] 数据异常 | 帧[{}] 角度: {} -> {}，请检查网络连接是否正常!",
    //            WJODLog::getWholeSysTime(),
    //            c_stLaserCfg_.m_sLaserName,
    //            p_iCurId + 1,
    //            l_iFirstAng,
    //            l_iEndAng);
    //     c_stSysParam_->m_fae.setErrorCode("C16");
    //     return false;
    // }
    return true;
}
/**
 * @function: angAntiRotate
 * @description: 改变雷达角度 适合逆时针旋转
 * @param {float&} p_fAzimuthAngle 雷达输出角度
 * @return {*}
 * @others: {*}
 */
void Convert720::angAntiRotate(float& p_fAzimuthAngle)
{
    if (!c_stLaserCfg_.m_bIsAntiRotate)
    {
        p_fAzimuthAngle = 360.0 - p_fAzimuthAngle;
        // 角度约束[0,360)
        if (p_fAzimuthAngle >= 360.0)
            p_fAzimuthAngle -= 360.0;
        else if (p_fAzimuthAngle < 0.0)
            p_fAzimuthAngle += 360.0;
    }
}

void Convert720::unpackData_(st_TWithTS& p_pScanMsg, int p_iCurId)
{
    for (int i = 0; i < WLR720_SCANS_PER_FIRING; i++)
        c_pcRawOut_[i].reset();
    c_pcMidOut_.reset();

    double l_f64Time = 0.0f;  // 各个点的time 用于偏心修正

    c_allAreaPc_.reset(new allAreaPC());  // 当帧各个区域的障碍点矩阵
    c_allAreaPc_->resize(c_stSysParam_->m_sGroupScaleTable_->size());

    PCOutPtr l_pcAll(new PCOut());
    PCOutPtr l_pcObstacle(new PCOut());
    // std::cout << "\n当前区域个数:  " << c_stSysParam_->m_sGroupScaleTable_->size() << std::endl;

    for (size_t i = 0; i < c_stSysParam_->m_sGroupScaleTable_->size(); i++)
    {
        (*c_allAreaPc_)[i].groupID = (*c_stSysParam_->m_sGroupScaleTable_)[i].groupID;
        (*c_allAreaPc_)[i].areaID = (*c_stSysParam_->m_sGroupScaleTable_)[i].areaID;
        (*c_allAreaPc_)[i].detFrame = (*c_stSysParam_->m_sGroupScaleTable_)[i].resFrame;
        (*c_allAreaPc_)[i].minHeight = (*c_stSysParam_->m_sGroupScaleTable_)[i].minHeight;
        (*c_allAreaPc_)[i].maxHeight = (*c_stSysParam_->m_sGroupScaleTable_)[i].maxHeight;
    }
    if (c_allAreaPc_->size() != 0)
        (*c_allAreaPc_)[0].scanFrame = p_iCurId;  // 记录当前帧号

    for (size_t i = 0; i < p_pScanMsg.m_data->m_vPackets.size(); ++i)
    {
        unpack_(i,
                p_pScanMsg.m_data->m_vPackets[i].m_data,
                l_f64Time,
                c_stSysParam_->m_sGroupScaleTable_,
                c_allAreaPc_,
                l_pcAll,
                l_pcObstacle,
                c_pcRawOut_);
    }
    // 首包取出附加数据
    // unpackAddMsg_(p_pScanMsg.m_data->m_vPackets[0].m_data, c_pcMidOut_.m_pinfo);

    // LOGA(WINFO,
    //         "{} 雷达[{}]时间戳 {} {}",
    //         WJODLog::getSystemTime(),
    //         c_stLaserCfg_.m_sLaserName,
    //         p_pScanMsg.m_iTimestamp,
    //         p_pScanMsg.m_iRecvTimestamp);
    // 确认点数非空
    if (checkLines(c_pcRawOut_, p_iCurId))
        c_fECb_(c_uiLidarID,
                c_allAreaPc_,
                l_pcAll,
                l_pcObstacle,
                p_pScanMsg.m_iTimestamp,
                p_pScanMsg.m_iRecvTimestamp,
                p_iCurId);
}

/**
 * @function: checkLines
 * @description: 检查线束的点数是否异常
 * @param {s_PCloud &[WLR720_SCANS_PER_FIRING]} p_rawOut 当前点云
 * @return {bool} true: 雷达有效点为空
 * @others: {*}
 */
bool Convert720::checkLines(s_PCloud (&p_rawOut)[WLR720_SCANS_PER_FIRING], int p_iCurId)
{
    std::vector<int> l_viZeroLineList;
    int l_iPointSum_ = 0;
    for (int i = 0; i < WLR720_SCANS_PER_FIRING; i++)
    {
        if (p_rawOut[i].m_praw->size() == 0)
            l_viZeroLineList.push_back(i);
        l_iPointSum_ += p_rawOut[i].m_praw->size();
    }
    if (l_iPointSum_ < c_stSysParam_->m_lidar[c_uiLidarID].m_uiPointMinNum
        || l_viZeroLineList.size() == WLR720_SCANS_PER_FIRING)
    {
        c_stSysParam_->m_lidar[c_uiLidarID].m_dev.setStatus(wj_slam::DevStatus::DATADATAERROR);
        // c_stSysParam_->m_fae.setErrorCode("C18");
        LOGA(WERROR,
             "{} 雷达 [{}] 扫描异常 | 帧[{}] 点数过少 {}/{},已丢弃",
             WJODLog::getWholeSysTime(),
             c_stLaserCfg_.m_sLaserName,
             p_iCurId,
             l_iPointSum_,
             c_stSysParam_->m_lidar[c_uiLidarID].m_uiPointMinNum);
        LOGA(WERROR, " *********************************************** ");
        LOGA(WERROR, " * 1. 确认雷达遮光罩没有被障碍物遮挡 ");
        LOGA(WERROR, " * 2. 连接web客户端查看[雷达参数配置]中雷达距离盲区是否在合理值范围");
        LOGA(WERROR, " *       WLR720FCW：MIN:1.5m---->MAX:70m");
        LOGA(WERROR, " *       WLR720F：MIN:0.5m---->MAX:70m ");
        LOGA(WERROR, " * 3. 如果上述操作还未解决，请打开wireshark软件录制数据包，联系万集开发人员");
        LOGA(WERROR, " * **********************************************");
        return false;
    }
    else
    {
        if (c_stSysParam_->m_lidar[c_uiLidarID].m_dev.m_status == wj_slam::DevStatus::DATADATAERROR)
            c_stSysParam_->m_lidar[c_uiLidarID].m_dev.setStatus(wj_slam::DevStatus::DEVCONNECT);
        if (l_viZeroLineList.size() > (int)(WLR720_SCANS_PER_FIRING * 0.2))
        {
            // 16线 超过3线有线点数为0，则提醒
            for (int i = 0; i < (int)l_viZeroLineList.size(); i++)
                LOGA(WWARN,
                     "{} 雷达 [{}] 扫描异常 | 帧[{}] 线[{}] 点数为空",
                     WJODLog::getWholeSysTime(),
                     c_stLaserCfg_.m_sLaserName,
                     p_iCurId,
                     l_viZeroLineList[i]);
            LOGA(WWARN,
                 "{} 雷达 [{}] 扫描异常 | 若存在遮挡雷达,可忽略此问题,否则请检查!",
                 WJODLog::getWholeSysTime(),
                 c_stLaserCfg_.m_sLaserName);
        }
    }
    return true;
}

timeval Convert720::getPktDataTime_(const s_LIDAR_RAW_DATA::DArray& p_rawData)
{
    // const s_LIDAR_RAW_DATA::DArray& p_rawData = p_pScanMsg.m_data->m_vPackets[0].m_data;
    const s_RawPacket* raw = (const s_RawPacket*)&p_rawData[0];
    // gnss/lidar
    // 由雷达设备决定：有GNS则GNS，否则雷达内部计时
    tm l_time = {
        .tm_sec = raw->addmsg.time[0],         // 秒
        .tm_min = raw->addmsg.time[1],         // 分
        .tm_hour = raw->addmsg.time[2],        // 时
        .tm_mday = raw->addmsg.time[3],        // 日
        .tm_mon = raw->addmsg.time[4] - 1,     // 月
        .tm_year = raw->addmsg.time[5] + 100,  // 年
    };
    timeval l_DataTime;
    l_DataTime.tv_sec = mktime(&l_time);
    l_DataTime.tv_usec = (((raw->addmsg.nsec[3] & 0x0F) << 24) + (raw->addmsg.nsec[2] << 16)
                          + (raw->addmsg.nsec[1] << 8) + raw->addmsg.nsec[0])
                         / 100;
    return l_DataTime;
}

/**
 * @function: getPktAng_
 * @description: 获取每包的角度
 * @param {const s_LIDAR_RAW_DATA::DArray&} p_rawData 当前包数据
 * @return {uint32_t} 返回包内部角度
 * @others: {*}
 */
uint32_t Convert720::getPktAng_(const s_LIDAR_RAW_DATA::DArray& p_rawData)
{
    std::string l_sLaserType_ = c_stSysParam_->m_lidar[c_uiLidarID].m_dev.m_sDevType;
    if (l_sLaserType_ == "WLR720A" || l_sLaserType_ == "WLR720F" || l_sLaserType_ == "WLR720FCW")
        return (p_rawData[3] << 8 | p_rawData[2]);
    else if (l_sLaserType_ == "WLR720F_NP" || l_sLaserType_ == "WLR720FCW_NP")
        return (p_rawData[6] << 8 | p_rawData[5]);
    else if (l_sLaserType_ == "WLR720C")
        return (p_rawData[6] << 8 | p_rawData[5]);
    else if (l_sLaserType_ == "WLR719")
    {
        // ToDoList 增加719零度包判断方式
        return 0;
    }
    return 0;
}

#pragma endregion

#pragma region 保护函数
bool Convert720::setup_()
{
    bool l_bRes_ = true;
    // 根据雷达转速更改值
    switch (c_stLaserCfg_.m_uiRPM)
    {
        case 300:  // 5hz
            c_fAngleResolutionVal_ = 0.1;
            c_fLineAngleGap_ = 0.0025;
            break;
        case 600:  // 10hz
            c_fAngleResolutionVal_ = 0.2;
            c_fLineAngleGap_ = 0.005;
            break;
        case 900:  // 15hz
            c_fAngleResolutionVal_ = 0.3;
            c_fLineAngleGap_ = 0.0075;
            break;
        case 1200:  // 20hz
            c_fAngleResolutionVal_ = 0.4;
            c_fLineAngleGap_ = 0.01;
            break;
        default:
            c_fAngleResolutionVal_ = 0;
            c_fLineAngleGap_ = 0;
            break;
    }
    c_fGroupAngleVal_ = c_fAngleResolutionVal_ / 4;

    std::string l_sFilePath = "_" + c_stLaserCfg_.m_sLaserName;

    // 读取垂直表: 垂直角 航向角
    l_bRes_ = loadVelAngleData_(c_afVertAngle_,
                                c_afAzimuthDiff_,
                                SCANS_PER_BLOCK,
                                c_stLaserCfg_.m_sCalibrationFile + l_sFilePath + ".csv")
              && l_bRes_;

    // 计算垂直sin/cos
    setVelAngleData_(c_afVertAngle_, c_afVsinRotTable_, c_afVcosRotTable_, SCANS_PER_BLOCK);

    // 读取偏心表,不存在则设置默认偏心
    l_bRes_ = loadLevelAngleData_(c_aiEccentricity_,
                                  MIDLINE_POINT_SIZE,
                                  c_stLaserCfg_.m_sCalibrationFile + l_sFilePath + ".txt_PX")
              && l_bRes_;
    return l_bRes_;
}

bool Convert720::loadVelAngleData_(float* p_cVerAngData,
                                   float* p_cDiffData,
                                   uint32_t p_uiSize,
                                   std::string p_sFilePath)
{
    ifstream l_file;
    l_file.open(p_sFilePath.c_str());
    uint32_t l_uiRowId = 0;

    if (!l_file)
    {
        LOGA(WERROR,
             "{} 雷达 [{}]  加载修正表2异常 | 文件[{}]损坏/不存在,请按照以下步骤检查：",
             WJODLog::getWholeSysTime(),
             c_stLaserCfg_.m_sLaserName,
             p_sFilePath.c_str());
        LOGA(WERROR, " *********************************************** ");
        LOGA(WERROR, " * 1. 在线模式下为自动获取修正表，如果文件不存在请优先检查雷达是否正确连接 ");
        LOGA(WERROR,
             " * 2. "
             "2022年以前的机器徐手动拷贝对应雷达的修正表1/修正表2,并按照以下命名规则更改名称： ");
        LOGA(WERROR, " *       修正表1：雷达类型_雷达名.csv(eg:WLR720FCW_front.csv)");
        LOGA(WERROR, " *       修正表2：雷达类型_雷达名.txt_PX(eg: WLR720FCW_front.txt_PX) ");
        LOGA(WERROR, " * 3. 如果上述操作还未解决，请打开wireshark软件录制数据包，联系万集开发人员");
        LOGA(WERROR, " * **********************************************");
        c_stSysParam_->m_fae.setErrorCode("C19");
        return false;
    }
    else
    {
        string l_sReadLine, mem, key;
        unsigned int l_uiInd = 0;
        while (true)
        {
            getline(l_file, l_sReadLine);
            if (l_file.fail())
                break;
            // 遍历该行字符串 分割',' key ，mem
            while (l_uiInd < l_sReadLine.length())
            {
                if (l_sReadLine[l_uiInd] == ',')
                {
                    key = mem;
                    mem.clear();
                    l_uiInd++;
                }
                else
                    mem += l_sReadLine[l_uiInd++];
            }
            istringstream isAng(key);
            isAng >> *(p_cVerAngData + l_uiRowId);
            istringstream isAzimuth(mem);
            isAzimuth >> *(p_cDiffData + l_uiRowId);
            l_uiRowId++;
            mem.clear();
            key.clear();
            l_uiInd = 0;
        }
        l_file.close();
    }
    return true;
}

void Convert720::setVelAngleData_(float* p_aVerAngData,
                                  float* p_aSinData,
                                  float* p_aCosData,
                                  uint32_t p_uiSize)
{
    for (uint32_t i = 0; i < p_uiSize; i++)
    {
        *(p_aSinData + i) = sin(*(p_aVerAngData + i) * M_PI / 180.0);
        *(p_aCosData + i) = cos(*(p_aVerAngData + i) * M_PI / 180.0);
    }
}

bool Convert720::loadLevelAngleData_(int* p_cData,
                                     uint32_t p_uiSize,
                                     std::string p_sFilePath,
                                     std::string p_sSplit)
{
    std::ifstream l_file;
    l_file.open(p_sFilePath.c_str());
    if (!l_file)
    {
        LOGA(WERROR,
             "{} 雷达 [{}]  加载修正表1异常 | 文件[{}]损坏/不存在,请按照以下步骤检查：",
             WJODLog::getWholeSysTime(),
             c_stLaserCfg_.m_sLaserName,
             p_sFilePath.c_str());
        c_stSysParam_->m_fae.setErrorCode("C20");
        LOGA(WERROR, " *********************************************** ");
        LOGA(WERROR, " * 1. 在线模式下为自动获取修正表，如果文件不存在请优先检查雷达是否正确连接 ");
        LOGA(WERROR,
             " * 2. "
             "2022年以前的机器徐手动拷贝对应雷达的修正表1/修正表2,并按照以下命名规则更改名称： ");
        LOGA(WERROR, " *       修正表1：雷达类型_雷达名.csv(eg:WLR720FCW_front.csv)");
        LOGA(WERROR, " *       修正表2：雷达类型_雷达名.txt_PX(eg: WLR720FCW_front.txt_PX) ");
        LOGA(WERROR, " * 3. 如果上述操作还未解决，请打开wireshark软件录制数据包，联系万集开发人员");
        LOGA(WERROR, " * **********************************************");
        memset(p_cData, 0, p_uiSize);
        return false;
    }
    else
    {
        std::string str;
        unsigned int ie = 0;
        while (true)
        {
            getline(l_file, str);

            if (l_file.fail())
                break;
            char* p = strtok((char*)str.data(), p_sSplit.c_str());  // 逗号分隔依次取出
            while (p != NULL)
            {
                if (ie >= p_uiSize)
                {
                    LOGA(WERROR,
                         "{} 雷达 [{}] 加载修正表1异常 | 文件[{}]格式异常,请检查修正表1文件！",
                         WJODLog::getWholeSysTime(),
                         c_stLaserCfg_.m_sLaserName,
                         p_sFilePath.c_str());
                    // 用0 替换 p_uiSsze个p_cData
                    memset(p_cData, 0, p_uiSize);
                    l_file.close();
                    return false;
                }
                sscanf(p, "%d", p_cData + ie);  // char ---> int
                ie++;
                p = strtok(NULL, p_sSplit.c_str());
            }
        }
        l_file.close();
    }
    return true;
}

bool Convert720::filterGroundPoint(float& p_fZValue)
{
    // 过滤地面, 10cm以下都算地面
    if (p_fZValue < (GROUND_POINT - c_stSysParam_->m_lidar[c_uiLidarID].m_fFeatureHeight))
        return true;
    return false;
}

bool Convert720::filterUpAreaPoint(float& p_fZVaule,
                                   float& p_fAreaMinHeight,
                                   float& p_fAreaMaxHeight)
{
    if (p_fZVaule > p_fAreaMaxHeight || p_fZVaule < p_fAreaMinHeight)
        return true;
    return false;
}

bool Convert720::filterNoisePoint(u_int& p_iIntensity, float& p_fDistance)
{
    if (p_iIntensity <= MIN_INTENSITY)
        return true;
    if (p_fDistance <= MIN_DIASTANCE)  // 近处的点不要
        return true;
    return false;
}

bool Convert720::isValidData(scaleTablePtr& p_sacleTablePtr, int p_iScanID, int p_iScaleID)
{
    return (*p_sacleTablePtr)[p_iScanID][p_iScaleID].state;
}

bool Convert720::extractRoiPoint(float& p_fCurDistance,
                                 float& p_fMinDistance,
                                 float& p_fMaxDistance)
{
    if (p_fCurDistance < p_fMinDistance || p_fCurDistance > p_fMaxDistance)
        return false;
    return true;
}

void Convert720::setPointMatrix(int& p_iIdx, pcMatPtr& p_pcMat, int& scanID, int& scaleID)
{
    (*p_pcMat)(scanID, scaleID) = (int)1;
}

void Convert720::unpack_(int p_iPckageID,
                         const s_LIDAR_RAW_DATA::DArray& p_rawData,
                         double& p_f64Time,
                         AreaTablePtr& p_allScaleTable,
                         areaPcPtr& p_allAreaPc,
                         PCOutPtr& pcOut,
                         PCOutPtr& pcObstacleOut,
                         s_PCloud (&pc)[WLR720_SCANS_PER_FIRING])
{
    const s_RawPacket* raw = (const s_RawPacket*)&p_rawData[0];
    POINTTYPEOUT l_pointOut;
    PRaw pointRaw;
    PAdd pointAdd;

    float l_fDist_xyz = 0, l_fDist_xy = 0;
    Eigen::Vector3d l_Dist_xyz(0, 0, 0);
    uint32_t l_fIntensity = 0;
    float l_fAzimuthAngle = 0;
    // 乱序包 首点更新时间
    p_f64Time = ((raw->blocks[0].rotation % 36000) / 300) * BLOCKS_PER_PACKET * SCANS_PER_BLOCK
                * WLR720_POINT_TDURATION;
    // 15包
    for (int i = 0; i < BLOCKS_PER_PACKET; i++)
    {
        int scanID = 0;
        // 19块
        for (int j = 0, k = 0; j < SCANS_PER_BLOCK; j++, k += RAW_SCAN_SIZE)
        {
            // 解析获取距离信息 单位4mm -> m
            union two_bytes tmp;
            tmp.bytes[0] = raw->blocks[i].data[k];
            tmp.bytes[1] = raw->blocks[i].data[k + 1];
            l_fDist_xyz = tmp.uint * DISTANCE_RESOLUTION;

            // 解析强度值(脉宽)
            l_fIntensity = raw->blocks[i].data[k + 2];

            float first_Angazimuth = 0, second_Angazimuth = 0, third_Angazimuth = 0,
                  fourth_Angazimuth = 0;

            // 角度 单位deg
            first_Angazimuth = (raw->blocks[i].rotation) / 100.0 - c_fAngleResolutionVal_;
            second_Angazimuth = first_Angazimuth + c_fGroupAngleVal_;
            third_Angazimuth = first_Angazimuth + 2 * c_fGroupAngleVal_;
            fourth_Angazimuth = first_Angazimuth + 3 * c_fGroupAngleVal_;

            switch (j)
            {
                case 0: l_fAzimuthAngle = (first_Angazimuth); break;
                case 1: l_fAzimuthAngle = (first_Angazimuth + c_fLineAngleGap_); break;
                case 2: l_fAzimuthAngle = (first_Angazimuth + 2 * c_fLineAngleGap_); break;
                case 3: l_fAzimuthAngle = (first_Angazimuth + 3 * c_fLineAngleGap_); break;
                case 4: l_fAzimuthAngle = (first_Angazimuth + 4 * c_fLineAngleGap_); break;
                case 5: l_fAzimuthAngle = (second_Angazimuth); break;
                case 6: l_fAzimuthAngle = (second_Angazimuth + c_fLineAngleGap_); break;
                case 7: l_fAzimuthAngle = (second_Angazimuth + 2 * c_fLineAngleGap_); break;
                case 8: l_fAzimuthAngle = (second_Angazimuth + 3 * c_fLineAngleGap_); break;
                case 9: l_fAzimuthAngle = (third_Angazimuth); break;
                case 10: l_fAzimuthAngle = (third_Angazimuth + c_fLineAngleGap_); break;
                case 11: l_fAzimuthAngle = (third_Angazimuth + 2 * c_fLineAngleGap_); break;
                case 12: l_fAzimuthAngle = (third_Angazimuth + 3 * c_fLineAngleGap_); break;
                case 13: l_fAzimuthAngle = (third_Angazimuth + 4 * c_fLineAngleGap_); break;
                case 14: l_fAzimuthAngle = (fourth_Angazimuth); break;
                case 15: l_fAzimuthAngle = (fourth_Angazimuth + c_fLineAngleGap_); break;
                case 16: l_fAzimuthAngle = (fourth_Angazimuth + 2 * c_fLineAngleGap_); break;
                case 17: l_fAzimuthAngle = (fourth_Angazimuth + 3 * c_fLineAngleGap_); break;
                case 18: l_fAzimuthAngle = (fourth_Angazimuth + 4 * c_fLineAngleGap_); break;
                default: break;
            }

            // 真实角度计算
            l_fAzimuthAngle += 0.05 * c_aiEccentricity_[int(l_fAzimuthAngle * 20 + 7200) % 7200];
            // 角度约束[0,360)
            l_fAzimuthAngle = l_fAzimuthAngle < 360.0 ? l_fAzimuthAngle : l_fAzimuthAngle - 360.0;
            l_fAzimuthAngle = l_fAzimuthAngle < 0.0 ? l_fAzimuthAngle + 360.0 : l_fAzimuthAngle;

            // 扫描线束计算
            if (j == 0 || j == 5 || j == 9 || j == 14)
                scanID = 8;
            if (j > 0 && j < 5)
                scanID = 16 - j;
            if (j > 5 && j < 14)
                scanID = 17 - j;
            if (j > 14 && j <= 18)
                scanID = 18 - j;

            l_fDist_xy = l_fDist_xyz * c_afVcosRotTable_[j];
            l_Dist_xyz.z() = l_fDist_xyz * c_afVsinRotTable_[j];
            l_Dist_xyz.x() = l_fDist_xy
                             * c_afSinRotTable_[int(l_fAzimuthAngle * ROTATION_RESOLUTION_INV)
                                                % ROTATION_MAX_UNITS];
            l_Dist_xyz.y() = l_fDist_xy
                             * (c_afCosRotTable_[int(l_fAzimuthAngle * ROTATION_RESOLUTION_INV)
                                                 % ROTATION_MAX_UNITS]);

            // 仿射变换矩阵
            l_Dist_xyz = c_transToBase * l_Dist_xyz;

            p_f64Time += WLR720_POINT_TDURATION;

            if (!isValidData_(l_fIntensity, l_fDist_xyz, l_fAzimuthAngle))
                continue;
            // if (!c_stSysParam_->m_lidar[c_uiLidarID].m_bUseFloor
            //     && l_Dist_xyz.z() < -c_stSysParam_->m_lidar[c_uiLidarID].m_fFeatureHeight)
            //     continue;

            pointRaw.x = l_Dist_xyz.x();
            pointRaw.y = l_Dist_xyz.y();
            pointRaw.z = l_Dist_xyz.z();
            pointAdd.intensity = l_fIntensity;
            pointAdd.xydist = l_fDist_xy;
            pointAdd.depth = l_fDist_xyz;
            pointAdd.ang = l_fAzimuthAngle;
            pointAdd.time = p_f64Time;

            POINTTYPEOUT pointOut;
            pointOut.x = pointRaw.x;
            pointOut.y = pointRaw.y;
            pointOut.z = pointRaw.z;
            pointOut.intensity = pointAdd.intensity;
            pcOut->push_back(pointOut);

            // // 中间线点云
            // if (scanID == 8)
            // {
            //     pmid.m_praw->points.push_back(pointRaw);
            //     pmid.m_padd->push_back(pointAdd);
            // }
            if ((j == 5) || (j == 9) || (j == 14))
            {
                continue;
            }
            pc[scanID].m_praw->points.push_back(pointRaw);
            pc[scanID].m_padd->push_back(pointAdd);

            // 去除地面点
            if (filterGroundPoint(pointRaw.z))
                continue;
            // 去除噪点
            if (filterNoisePoint(l_fIntensity, l_fDist_xyz))
                continue;

            int scaleID = (p_iPckageID * 15) + i;
            for (int idx = 0; idx < (int)p_allScaleTable->size(); idx++)
            {
                scaleTablePtr table = (*p_allScaleTable)[idx].table;
                // 这个刻度下是否在防护区域内
                if (!isValidData(table, scanID, scaleID))
                    continue;

                // 每个区域的最大最小高度可能不一样
                if (filterUpAreaPoint(pointRaw.z,
                                      (*p_allScaleTable)[idx].minHeight,
                                      (*p_allScaleTable)[idx].maxHeight))
                    continue;
                if (extractRoiPoint(l_fDist_xyz,
                                    (*table)[scanID][scaleID].minDis,
                                    (*table)[scanID][scaleID].maxDis))
                {
                    setPointMatrix(idx, (*p_allAreaPc)[idx].pcMat, scanID, scaleID);
                    pcObstacleOut->push_back(pointOut);
                }
            }
        }
    }
}

void Convert720::unpackAddMsg_(const s_LIDAR_RAW_DATA::DArray& p_rawData, AddtionMsgPtr& padd)
{
    const s_RawPacket* raw = (const s_RawPacket*)&p_rawData[0];

    // gnss
    tm l_time = {
        .tm_sec = raw->addmsg.time[0],         // 秒
        .tm_min = raw->addmsg.time[1],         // 分
        .tm_hour = raw->addmsg.time[2],        // 时
        .tm_mday = raw->addmsg.time[3],        // 日
        .tm_mon = raw->addmsg.time[4] - 1,     // 月
        .tm_year = raw->addmsg.time[5] + 100,  // 年
    };
    padd->m_gnssTime.tv_sec = mktime(&l_time);
    padd->m_gnssTime.tv_usec = (((raw->addmsg.nsec[3] & 0x0F) << 24) + (raw->addmsg.nsec[2] << 16)
                                + (raw->addmsg.nsec[1] << 8) + raw->addmsg.nsec[0])
                               / 100;
    padd->m_state[0] = (raw->addmsg.nsec[3] & 0x80) >> 7;
    padd->m_state[1] = (raw->addmsg.nsec[3] & 0x40) >> 6;
    padd->m_state[2] = (raw->addmsg.nsec[3] & 0x30) >> 4;
    /* imu
    角速度数据[rad/s] = 原始数据 * 灵敏度 / 1000 /180 * pi
    加速度数据[m/s^2] = 原始数据 * 灵敏度 / 1000 * gravity 其中gravity = 9.80665
    */
    union two_bytes_signed l_tmpData;
    for (int j = 0; j < 3; ++j)
    {
        l_tmpData.bytes[0] = raw->addmsg.angVel[2 * j];
        l_tmpData.bytes[1] = raw->addmsg.angVel[2 * j + 1];
        padd->m_gyro[j] = l_tmpData.sint * c_fAngScalar;
        l_tmpData.bytes[0] = raw->addmsg.accel[2 * j];
        l_tmpData.bytes[1] = raw->addmsg.accel[2 * j + 1];
        padd->m_accel[j] = l_tmpData.sint * c_fAccScalar;
    }
}

#pragma endregion

#pragma endregion

}  // namespace wanji_driverOD