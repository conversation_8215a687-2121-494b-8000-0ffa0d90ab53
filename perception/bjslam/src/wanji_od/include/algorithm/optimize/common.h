// Copyright 2013, <PERSON>, Carnegie Mellon University
// Further contributions copyright (c) 2016, Southwest Research Institute
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//
// 1. Redistributions of source code must retain the above copyright notice,
//    this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright notice,
//    this list of conditions and the following disclaimer in the documentation
//    and/or other materials provided with the distribution.
// 3. Neither the name of the copyright holder nor the names of its
//    contributors may be used to endorse or promote products derived from this
//    software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
// AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
// LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.
//
// This is an implementation of the algorithm described in the following paper:
//   J. Zhang and S. Singh. LOAM: Lidar Odometry and Mapping in Real-time.
//     Robotics: Science and Systems Conference (RSS). Berkeley, CA, July 2014.
#pragma once
#ifndef COMMON_H_
#    define COMMON_H_

#    include "omp.h"
// #include "tic_toc.h"
// #include "wslam_msgs/wslamScan_msg.h"

#    include <cmath>
#    include <fstream>
#    include <iostream>
#    include <mutex>
#    include <pwd.h>
#    include <queue>
#    include <string>
#    include <thread>
#    include <time.h>
#    include <unistd.h>
#    include <vector>

#    include <Eigen/Dense>
#    include <Eigen/Geometry>

#    include <eigen_conversions/eigen_msg.h>
#    include <geometry_msgs/Point32.h>
#    include <geometry_msgs/PolygonStamped.h>
#    include <geometry_msgs/Quaternion.h>
#    include <nav_msgs/Odometry.h>
#    include <nav_msgs/Path.h>
#    include <pcl_conversions/pcl_conversions.h>
#    include <pcl_ros/point_cloud.h>
#    include <ros/console.h>
#    include <ros/package.h>
#    include <ros/ros.h>
#    include <sensor_msgs/Imu.h>
#    include <sensor_msgs/PointCloud2.h>
#    include <std_msgs/String.h>
#    include <tf/tf.h>
#    include <tf/transform_broadcaster.h>
#    include <tf/transform_datatypes.h>

#    include <pcl/common/centroid.h>
#    include <pcl/common/common.h>
#    include <pcl/common/io.h>
#    include <pcl/console/parse.h>
#    include <pcl/filters/crop_hull.h>
#    include <pcl/filters/filter.h>
#    include <pcl/filters/filter_indices.h>
#    include <pcl/filters/impl/filter.hpp>
#    include <pcl/filters/impl/voxel_grid.hpp>
#    include <pcl/filters/voxel_grid.h>
#    include <pcl/impl/pcl_base.hpp>
#    include <pcl/io/pcd_io.h>
#    include <pcl/kdtree/impl/kdtree_flann.hpp>
#    include <pcl/point_cloud.h>
#    include <pcl/point_types.h>
#    include <pcl/registration/icp.h>
#    include <pcl/search/impl/flann_search.hpp>
#    include <pcl/search/impl/kdtree.hpp>
#    include <pcl/search/organized.h>
#    include <pcl/search/search.h>
#    include <pcl/segmentation/extract_clusters.h>
#    include <pcl/surface/convex_hull.h>

// #define TIME_TEST
#    define MIDSCAN_COMPRESS
#    define IMU_UNDISTORT

#    define MAX_NUM_THREAD 1
#    define MID_NUM_THREAD 3
#    define MIN_NUM_THREAD 2

// #define APP_VESION
#    define RD_VESION

// 清理点云的内存占用
// template <typename T>
// inline void clearPointCloud(pcl::PointCloud<T>& pc)
// {
//     pcl::PointCloud<T>().swap(pc);
// }

template <typename T> inline void clearQueue(std::queue<T>& msgQueue)
{
    std::queue<T>().swap(msgQueue);
}

// 拆分点云
template <typename T>
void segmentCloud(pcl::PointCloud<T>& pc_in,
                  pcl::PointCloud<T>& pc_out1,
                  pcl::PointCloud<T>& pc_out2,
                  int intersect)
{
    std::vector<int> ids_1;
    ids_1.resize(intersect);
    // ROS_INFO("ids_1= %d",ids_1.size());
    for (int i = 0; i < ids_1.size(); ++i)
        ids_1[i] = i;
    clearPointCloud(pc_out1);
    pcl::copyPointCloud(pc_in, ids_1, pc_out1);

    std::vector<int> ids_2;
    ids_2.resize(pc_in.points.size() - intersect);
    // ROS_INFO("ids_2= %d",ids_2.size());
    for (int i = 0; i < ids_2.size(); ++i)
        ids_2[i] = intersect + i;
    clearPointCloud(pc_out2);
    pcl::copyPointCloud(pc_in, ids_2, pc_out2);
}

// 返回弧度值
inline float _AngToRad(float Ang)
{
    return Ang / 180.0 * M_PI;
}
// 返回角度值
inline float _RadToAng(float Rad)
{
    return Rad * 180.0 / M_PI;
}
// 返回360度内的角度
inline float _AngIn360(float Ang)
{
    if (Ang >= 360.0)
    {
        Ang -= 360.0;
        return Ang;
    }
    else if (Ang < 0.0)
    {
        Ang += 360.0;
        return Ang;
    }
    else
        return Ang;
}
// 返回2pi内的弧度
inline float _RadIn2PI(float Rad)
{
    if (Rad >= 2 * M_PI)
    {
        Rad -= 2 * M_PI;
        return Rad;
    }
    else if (Rad < 0.0)
    {
        Rad += 2 * M_PI;
        return Rad;
    }
    else
        return Rad;
}

// 获取当前用户名
inline std::string getUserName()
{
    uid_t userid;
    struct passwd* pwd;
    userid = getuid();
    pwd = getpwuid(userid);
    return pwd->pw_name;
}

// 四元数转欧拉角，先绕Z轴旋转再绕新的y轴旋转，再绕新的x轴旋转。
template <typename T>
inline void toEulerAngle(const Eigen::Quaterniond& q, T& roll, T& pitch, T& yaw)
{
    // roll (x-axis rotation)
    double sinr_cosp = +2.0 * (q.w() * q.x() + q.y() * q.z());
    double cosr_cosp = +1.0 - 2.0 * (q.x() * q.x() + q.y() * q.y());
    roll = atan2(sinr_cosp, cosr_cosp);

    // pitch (y-axis rotation)
    double sinp = +2.0 * (q.w() * q.y() - q.z() * q.x());
    if (fabs(sinp) >= 1)
        pitch = copysign(M_PI / 2, sinp);  // use 90 degrees if out of range
    else
        pitch = asin(sinp);

    // yaw (z-axis rotation)
    double siny_cosp = +2.0 * (q.w() * q.z() + q.x() * q.y());
    double cosy_cosp = +1.0 - 2.0 * (q.y() * q.y() + q.z() * q.z());
    yaw = atan2(siny_cosp, cosy_cosp);
}

// 欧拉角转四元数，先绕Z轴旋转再绕新的y轴旋转，再绕新的x轴旋转。
inline Eigen::Quaterniond RPY2Quat(Eigen::Vector3d RPY)
{
    const Eigen::AngleAxisd roll_angle(RPY(0), Eigen::Vector3d::UnitX());
    const Eigen::AngleAxisd pitch_angle(RPY(1), Eigen::Vector3d::UnitY());
    const Eigen::AngleAxisd yaw_angle(RPY(2), Eigen::Vector3d::UnitZ());
    return yaw_angle * pitch_angle * roll_angle;
}

// 获取键盘的触发
/**
 * @brief GetKeyFromTermino()
 * 非阻塞形式，获取键值
 */
static inline int GetKeyEnterTermino(void)
{
    fd_set rfds;
    struct timeval tv;
    int retval;

    /* Watch stdin (fd 0) to see when it has input. */
    FD_ZERO(&rfds);
    FD_SET(0, &rfds);
    /* Wait up to five seconds. */
    tv.tv_sec = 0;
    tv.tv_usec = 0;

    retval = select(1, &rfds, NULL, NULL, &tv);
    /* Don't rely on the value of tv now! */

    if (retval == -1)
    {
        perror("select()");
        return 0;
    }
    else if (retval)
        return 1;
    /* FD_ISSET(0, &rfds) will be true. */
    else
        return 0;
    return 0;
}

// 获取当前的系统时间
inline std::string getCurrentTimeStr()
{
    time_t t = time(NULL);
    char ch[64] = {0};
    char result[100] = {0};
    strftime(ch, sizeof(ch) - 1, "%Y_%m_%d_%H:%M", localtime(&t));  //%Y%m%d%H%M%S
    sprintf(result, "%s", ch);
    return std::string(result);
}

#endif