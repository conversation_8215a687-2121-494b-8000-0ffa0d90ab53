/*
 * @Author: senlin <EMAIL>
 * @Date: 2022-08-16 16:53:06
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-29 09:27:10
 * @FilePath: /src/wanji_od/include/algorithm/preproc/RoiDetect/RoiDetect.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

#ifndef ROI_DETECT
#define ROI_DETECT

#include "./common/common_ex.h"
#include <mutex>
#include <queue>

namespace wj_od {
class RoiDetect {
  public:
    typedef boost::shared_ptr<RoiDetect> Ptr;

  private:
    typedef pcl::PointXYZI PFeature;
    typedef pcl::PointCloud<PFeature> pcFeature;
    typedef pcFeature::Ptr pcFeaturePtr;

    int c_iMaxDetectNum_; /**< 超过这个点数就不进行大小物体判断 直接报警*/
    int c_iRowNum_;       /**< 一条扫描线连续几个点*/
    int c_iColNum_;       /**< 同一水平刻度连续几个点*/
    int c_iValidNum_; /**< 一帧中的最少点数 默认最少两个点开启大小物体判断*/
    int c_iContinousNum_;
    bool c_bDetectOver_ = true;      // 区域检测完成
    bool c_bStopRoiDetect_ = false;  // 停止区域检测模块
    bool c_bShutDown_ = false;       // 检测模块退出
    bool c_bShutDownOver_ = true;
    bool c_bRoiInit_ = false;  // 初始化完成

    areaPcPtr& c_allAreaLastPc_;         /**< 传入进来的上一帧的障碍点*/
    areaPcPtr c_allAreaCurPc_;           /**< 前帧的障碍点*/
    boolVector& c_areaMultiFrameSwitch_; /**< 传入进来的每个区域对应的多帧检测开关*/

    std::queue<areaPcPtr>& c_allAreaCurPcQue_; /**< 传入进来的当帧障碍点队列*/
    std::mutex& c_mutexDetectOpt_;
    boost::function<void(int, int, s_AreaPC&)> c_roiOutputCb_; /**< 回调函数*/
    boost::function<void()> c_roiInvalidDataCb_;

    /**
     * @brief 从当帧障碍点队列中取出所有区域障碍点
     * @param
     * @return 指针 所有区域当真障碍点
     */
    areaPcPtr getAllAreaPC(void)
    {
        std::lock_guard<std::mutex> l_lock(c_mutexDetectOpt_);
        areaPcPtr l_allAreaCurPc;
        l_allAreaCurPc = c_allAreaCurPcQue_.front();
        while (!c_allAreaCurPcQue_.empty())
            c_allAreaCurPcQue_.pop();
        return l_allAreaCurPc;
    }

  public:
    RoiDetect(std::queue<areaPcPtr>& p_allAreaCurPC,
              areaPcPtr& p_allAreaLastPC,
              boolVector& p_multiFrameSwitch,
              std::mutex& p_roiLock,
              boost::function<void(int, int, s_AreaPC&)> p_callback,
              boost::function<void()> p_invalidCallback);
    ~RoiDetect();
    /**
     * @brief 参数初始化，主要是各种有效点数
     *
     *
     */
    void paramInit();
    /**
     * @brief 判断当帧是否存在障碍点
     * @param p_iPointsNum 有效障碍点数
     * @return 当存在>=p_iPointsNum的障碍点数时返回true
     */
    bool isPointExist(int p_iPointsNum);
    /**
     * @brief 一条扫描线是否有连续n个点存在（横向）
     * @param p_iRowNum
     * @return
     */
    bool horizonDetect(int p_iRowNum);
    /**
     * @brief 同一水平刻度下是否有连续n个点存在（纵向）
     * @param p_iColNum
     * @return [true]
     */
    bool verticalDetect(int p_iColNum);
    /**
     * @brief 2*3的矩阵中是否都存在障碍点
     * @param row
     * @param col
     * @param p_pcMat 当帧障碍点矩阵 16*1800
     * @return 是则返回true，反之false
     */
    bool twoRowThreeColDetect(int row, int col, PcMatrix& p_pcMat);
    /**
     * @brief 3*2的矩阵中是否都存在障碍点
     * @param row
     * @param col
     * @param p_pcMat 当帧障碍点矩阵 16*1800
     * @return 是则返回true，反之false
     */
    bool threeRowTwoColDetect(int row, int col, PcMatrix& p_pcMat);
    /**
     * @brief 是否有大物体存在
     * @param p_pcMat 当帧障碍点矩阵 16*1800
     * @return 是则返回true，反之false
     */
    bool isLargeObstacle(PcMatrix& p_pcMat);
    /**
     * @brief 前后两帧判断是否有重叠点，只有多帧检测开启时才会启用
     * @param p_pcLastMat 上一帧障碍点矩阵
     * @param p_pcCurMat 当帧障碍点矩阵
     * @return 有重叠点则返回true
     */
    bool twoFrameAnalysis(PcMatrix& p_pcLastMat, PcMatrix& p_pcCurMat);
    /**
     * @brief 计算当帧障碍点矩阵中的障碍点数量
     * @param p_pcMat
     * @return
     */
    int getPointsNum(PcMatrix& p_pcMat);
    /**
     * @brief 一个区域的障碍点检测
     * @param p_iAreaID 区域等级ID
     * @param p_pcMat
     */
    void singleAreaDetect(int p_iAreaID, s_AreaPC& p_pcMat);
    /**
     * @brief 所有区域的障碍点检测
     * @param p_allAreaCurPc
     */
    void allAreaDetect(areaPcPtr p_allAreaCurPc);
    /**
     * @brief 进程循环的条件就是必须有障碍点矩阵队列不为空
     * @return 不为空时返回true
     */
    bool checkNewFrame();
    /**
     * @brief 进程循环主函数
     */
    void run();
    /**
     * @brief 停止检测模块，区域切换时开启该功能
     */
    void stopRoiDetect()
    {
        c_bStopRoiDetect_ = true;
        while (1)
        {
            if (c_bDetectOver_)
                break;
            else
                usleep(1000);
        }
    }
    /**
     * @brief 启用检测模块
     */
    void runRoiDetect()
    {
        c_bStopRoiDetect_ = false;
    }
    void shutDown();
};

}  // namespace wj_od

#endif