/*
 * @Description:
 * @Version: 1.0
 * @Autor: zu<PERSON><PERSON>
 * @Date: 2021-10-25 13:45:12
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-10-24 14:42:22
 */
#pragma once

#include <chrono>
#include <cstdlib>
#include <ctime>

class TicToc {
  public:
    TicToc()
    {
        tic();
    }
    void tic()
    {
        start = std::chrono::system_clock::now();
    }

    double toc()
    {
        end = std::chrono::system_clock::now();
        std::chrono::duration<double> elapsed_seconds = end - start;
        return elapsed_seconds.count() * 1000;  // ms
    }

  private:
    std::chrono::time_point<std::chrono::system_clock> start, end;
};