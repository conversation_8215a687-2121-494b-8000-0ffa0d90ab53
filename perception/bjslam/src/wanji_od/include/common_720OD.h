/*
 * @Author: senlin <EMAIL>
 * @Date: 2022-09-15 14:00:42
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-28 14:01:25
 * @FilePath: /src/wanji_od/include/common_720OD.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#pragma once
#ifndef COMMON_720OD
#    define COMMON_720OD

#    include "common_WLR720.h"
#    include <Eigen/Dense>
#    include <boost/function.hpp>
#    include <boost/shared_array.hpp>
#    include <boost/shared_ptr.hpp>
#    include <eigen3/unsupported/Eigen/CXX11/Tensor>
#    include <ros/package.h>
#    include <ros/ros.h>
#    include <unordered_map>
#    include <yaml-cpp/yaml.h>

#    define ZERO_SCAN 8
#    define GROUP_NUM 16
#    define EVERY_GROUP_AREA_NUM 3

static const u_int HORIZON_SCAN_NUM = 1800;  // 大物体特征值计算半窗点

/**<16*1800的障碍点矩阵 */
typedef Eigen::Matrix<int, WLR720_SCANS_PER_FIRING, HORIZON_SCAN_NUM> PcMatrix;
typedef boost::shared_ptr<PcMatrix> pcMatPtr;

/**<点云/顶点相关 */
typedef pcl::PointXYZI PXYZI;
typedef std::vector<PXYZI> pcVer;
typedef boost::shared_ptr<pcVer> pcVerPtr;

/**
 * @brief 一个区域的障碍点矩阵
 * 包含主 副ID, 障碍点矩阵, 响应帧数, 点云帧号, 防护高度
 *
 */
typedef struct AreaPC
{
    int groupID;
    int areaID;
    int detFrame;
    int scanFrame;  // 点云帧号
    float minHeight;
    float maxHeight;
    pcMatPtr pcMat;

    AreaPC()
    {
        groupID = 0;
        areaID = 0;
        detFrame = 2;
        scanFrame = 0;
        minHeight = 0;
        maxHeight = 0;
        pcMat.reset(new PcMatrix(WLR720_SCANS_PER_FIRING, HORIZON_SCAN_NUM));
        pcMat->setZero();  // 全0初始化
    }

    AreaPC& operator=(const AreaPC& p_in)
    {
        *(this->pcMat) = *p_in.pcMat;

        this->groupID = p_in.groupID;
        this->areaID = p_in.areaID;
        this->detFrame = p_in.detFrame;
        this->scanFrame = p_in.scanFrame;
        this->minHeight = p_in.minHeight;
        this->maxHeight = p_in.maxHeight;
        return *this;
    }

    void copy(AreaPC& src)
    {
        this->groupID = src.groupID;
        this->areaID = src.areaID;
        this->pcMat = src.pcMat;
        this->detFrame = src.detFrame;
        this->scanFrame = src.scanFrame;
    }

} s_AreaPC;

typedef std::vector<s_AreaPC> allAreaPC;
typedef boost::shared_ptr<allAreaPC> areaPcPtr; /**<一组三个区域的障碍点矩阵 */

/**< 检测状态: 没有点=0 大物体=1 小物体=2 */
enum DetectState { NoPoint = 0, LargeObstacle = 1, SmallObstacle = 2 };

/**< 指令: 区域新增=0 区域查询=1 区域切换=2 */
enum ActionCMD {
    ADD = 0,     // 区域增加指令
    QUERY = 1,   // 区域查询指令
    CHANGE = 2,  // 区域切换指令
};

/**< 区域形状: 多边形=0 圆形=1 长方形=2  扇形=3*/
enum ShapeType {
    RANDOM = 0,  // 多边形 三个点以上
    CIRCLE = 1,  // 圆形 两个点（圆心+圆周上一点）
    RECT = 2,    // 长方形 两个点（对角线上两个点）
    FAN = 3      // 扇形 三个点（圆心+圆弧上两个点）
};
/**
 * @brief 区域等级
 */
enum AreaDegree {
    DANGER = 0,    // 保护区
    WARNING1 = 1,  // 警告区1
    WARNING2 = 2   // 警告区2
};

typedef std::vector<bool> boolVector;

/**
 * @brief 存储雷达线束最大最小距离
 *  还包括每个刻度下的状态 默认=false 表示该刻度不在防护区域内
 */
typedef struct PointMsg
{
    bool state;
    float minDis;
    float maxDis;
    PointMsg()
    {
        state = false;
        minDis = 0;  // 盲区点在点云解析时自动过滤
        maxDis = 0;
    }
} s_PointMsg;

typedef s_PointMsg PMsg;
typedef std::vector<PMsg> column;
typedef std::vector<column> scaleTable;
typedef boost::shared_ptr<scaleTable> scaleTablePtr;

/**
 * @brief 一个区域16*1800的扫描刻度表,并包含一个状态表，标记16线的有效状态
 */
typedef struct SingleTable
{
    int groupID;
    int areaID;
    int resFrame;
    float minHeight;
    float maxHeight;
    scaleTablePtr table;
    bool scan[WLR720_SCANS_PER_FIRING] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1};

    SingleTable()
    {
        groupID = 0;
        areaID = 0;
        resFrame = 2;  // 默认2帧
        minHeight = 0;
        maxHeight = 0;
        table.reset(new scaleTable(WLR720_SCANS_PER_FIRING, column(HORIZON_SCAN_NUM)));
    }

    void reset()
    {
        groupID = 0;
        areaID = 0;
        resFrame = 2;  // 默认2帧
        minHeight = 0;
        maxHeight = 0;
        table.reset(new scaleTable(WLR720_SCANS_PER_FIRING, column(HORIZON_SCAN_NUM)));
    }

} s_SingleTable;
typedef boost::shared_ptr<s_SingleTable> ScaleTablePtr;

typedef std::vector<s_SingleTable> AreaScaleTable;
typedef boost::shared_ptr<AreaScaleTable> AreaTablePtr;

/**
 * @brief 一个区域组(包含三个区域)的刻度表

 */
typedef struct GroupTable
{
    AreaScaleTable area;
    GroupTable()
    {
        area.resize(EVERY_GROUP_AREA_NUM);
    }

    void reset()
    {
        for (int i = 0; i < EVERY_GROUP_AREA_NUM; i++)
        {
            area[i].reset();
        }
    }
} s_GroupScaleTable;

typedef std::unordered_map<int, ScaleTablePtr> AreaScaleMap; /**< 一个区域组的map,管理三个区域*/
typedef std::unordered_map<int, AreaScaleMap>
    GroupScaleMap; /**< 管理所有区域的map,最大包含16组区域,1组区域中嵌套小map*/

#endif