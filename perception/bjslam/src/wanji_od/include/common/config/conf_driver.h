/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2022-03-04 09:06:49
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-03-04 13:38:57
 */
#pragma once
#include "wj_color.h"
#include <stdint.h>
#include <string>
namespace wj_slam {

typedef struct s_DriverConfig
{
  private:
    std::string Bool[2] = {"false", "true"};

  public:
    // 允许动态调控休眠
    bool m_bDynamicSleep;
    // 雷达自身休眠时间us 雷达频率的0.5 * 播放倍速默认值（10）
    uint32_t m_uiLidarSleepUs;
    // 雷达队列最低Size
    uint32_t m_uiLidarBufMinSize;

    s_DriverConfig()
    {
        this->reset();
    }

    void reset()
    {
        m_bDynamicSleep = false;
        m_uiLidarSleepUs = 50000;
        m_uiLidarBufMinSize = 0;
    }

    void printf()
    {
        wjPrint(WJCOL_GREEN, "[Driver ParamOD] isDynamicSleep", Bool[m_bDynamicSleep]);
        wjPrint(WJCOL_GREEN, "[Driver ParamOD] LidarSleepUs", m_uiLidarSleepUs);
        wjPrint(WJCOL_GREEN, "[Driver ParamOD] LidarBufMinSize", m_uiLidarBufMinSize);
    }

} s_DriverConfig;

}  // namespace wj_slam