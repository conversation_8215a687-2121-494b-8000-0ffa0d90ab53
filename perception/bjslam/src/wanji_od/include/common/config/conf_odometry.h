/*
 * @Description:
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2021-12-01 15:51:16
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-11-03 10:17:41
 */
#pragma once
#include "../type/type_optimiz.h"
#include <wjod_log.h>
namespace wj_slam {

typedef struct s_OdomConfig
{
    // default
    s_OdomConfig()
    {
        setDefaultMatch();
        optimiz_o3D = false;
    }
    // 默认为室外匹配参数
    void setDefaultMatch()
    {
        this->m_match.m_uiPlaneMaxPoints = 10;
        this->m_match.m_fPlaneMaxRadius = 3.5;
        this->m_match.m_fLine2DRadius = 0.3;
        this->m_match.m_uiLineMinPoints = 3;
        this->m_match.m_fLineMaxZDiff = 2.0;
        this->m_match.m_fMaxDist = 0.3;
        this->m_match.m_bSampleMatch = true;
    }
    void setModeDefault(int p_WorkMode, int p_ScenMode)
    {
        switch ((ScenMode)p_ScenMode)
        {
            // 室内匹配参数
            case ScenMode::INDOOR:
                this->m_match.m_uiPlaneMaxPoints = 8;
                this->m_match.m_fPlaneMaxRadius = 2.0;
                this->m_match.m_fLine2DRadius = 0.2;
                this->m_match.m_uiLineMinPoints = 3;
                this->m_match.m_fLineMaxZDiff = 2.0;
                this->m_match.m_fMaxDist = 0.2;
                this->m_match.m_bSampleMatch = false;
                break;
            case ScenMode::OUTDOOR:
                // 默认参数
                setDefaultMatch();
                break;
            case ScenMode::VACROUS:
                // 默认参数
                setDefaultMatch();
                break;
            default: break;
        }
    }
    // 打印
    void log()
    {
        LOGA(WINFO,
             "[param] ODOM-Match General: O{}, S{}, D{:.2f}",
             this->optimiz_o3D,
             this->m_match.m_bSampleMatch,             
             this->m_match.m_fMaxDist);        
        LOGA(WINFO,
             "[param] ODOM-Match Plane: R{:.2f}, P{}",
             this->m_match.m_fPlaneMaxRadius,
             this->m_match.m_uiPlaneMaxPoints);
        LOGA(WINFO,
             "[param] ODOM-Match Line: R{:.2f}, Z{:.2f}, P{}",
             this->m_match.m_fLine2DRadius,
             this->m_match.m_fLineMaxZDiff,
             this->m_match.m_uiLineMinPoints);
    }
    s_MatcherConfig m_match;  // 里程计匹配参数
    bool optimiz_o3D;
} s_OdomConfig;

}  // namespace wj_slam