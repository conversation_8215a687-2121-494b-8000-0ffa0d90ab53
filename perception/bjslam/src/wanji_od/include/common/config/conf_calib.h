/*
 * @Description:
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2022-04-06 08:46:57
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-28 13:58:32
 */
#pragma once
#include "../type/type_pose.h"
#include <mutex>
#include <string>
#include <wjod_log.h>

namespace wj_slam {

typedef struct s_CalibWithDev
{
    int m_iSensorID;
    int m_iScanID;    /*标定数据帧ID*/
    int m_iTimeStamp; /*标定状态*/
    Eigen::VectorXd m_stCalibData;
    Eigen::VectorXd m_stStdDev;
    s_CalibWithDev()
    {
        m_iSensorID = 0;
        m_iScanID = 0;
        m_iTimeStamp = 0;
        m_stCalibData = Eigen::VectorXd::Zero(6);
        m_stStdDev = Eigen::VectorXd::Zero(6);
    }
} s_CalibWithDev;

/** 雷达参数 */
typedef struct s_HorizonAlignConfig
{
    int m_iWorkLidarID;                           // 标定的雷达号
    int m_iMinTimeInit;                           // 初始化的最短时间限制[times]
    bool m_bIsStartCalib;                         // 是否启动校准
    s_CalibWithDev m_data;                        // 标定结果
    std::shared_ptr<std::mutex> m_mtRequestSend;  // 是否需要发送:WEB要数
    bool m_bSaveProcess;                          // 是否保存过程
    std::string m_sProcessFile;                   // 保存过程文件位置
    s_HorizonAlignConfig()
    {
        m_iWorkLidarID = 0;
        m_iMinTimeInit = 20;
        m_bIsStartCalib = false;
        m_mtRequestSend.reset(new std::mutex());
        m_bSaveProcess = false;
        m_sProcessFile = "horizonAlign.csv";
    }
    void resetPoses(int p_iLidarNum)
    {
        m_mtRequestSend->lock();
        m_data = s_CalibWithDev();
        m_data.m_iSensorID = m_iWorkLidarID;
        m_mtRequestSend->unlock();
    }
    // 打印
    void log() {}

} s_HorizAlignConfig;

/** 雷达参数 */
typedef struct s_MultiLidarCalibConfig
{
    int m_iBaseLidarID;                  // 标定的基准雷达号
    int m_iMinTimeInit;                  // 初始化的最短时间限制[ms]
    int m_iMaxTimeReInit;                // 初始化的最大抖动容忍时间限制[ms]
    float m_fInitVeloTHR;                // 判为静止的最大速度[m/s]
    float m_fInitStdDevTHR[2];           // 判为静止的最大位置标准差[m,deg]
    float m_fMoveVeloTHR;                // 用于匀速判定的速度最小值(delta_0.1s)
    float m_fVeloDiffTHR;                // 用于匀速判定的速度差异(delta_0.1s)
    int m_iJumpDuration;                 // 位姿插值时间系数[ms]
    bool m_bIsStartCalib;                // 是否处于标定中
    std::vector<s_CalibWithDev> m_data;  // 标定结果
    std::shared_ptr<std::mutex> m_mtRequestSend;  // 是否需要发送:WEB要数
    bool m_bSaveProcess;                          // 是否保存过程
    std::string m_sProcessFile;                   // 保存过程文件位置
    s_MultiLidarCalibConfig()
    {
        m_iBaseLidarID = 0;  // 此值不可更改
        m_iMinTimeInit = 3000;
        m_iMaxTimeReInit = 1000;
        m_fInitVeloTHR = 0.05;
        m_fInitStdDevTHR[0] = 0.05;
        m_fInitStdDevTHR[1] = 0.5;
        m_fMoveVeloTHR = 0.1;
        m_fVeloDiffTHR = 0.5;
        m_iJumpDuration = 100;
        m_bIsStartCalib = false;
        m_mtRequestSend.reset(new std::mutex());
        m_bSaveProcess = false;
        m_sProcessFile = "calib.csv";
    }
    void resetPoses(int p_iLidarNum)
    {
        m_mtRequestSend->lock();
        m_data.resize(p_iLidarNum);
        for (int i = 0; i < p_iLidarNum; ++i)
        {
            m_data[i] = s_CalibWithDev();
            m_data[i].m_iSensorID = i;
        }
        m_mtRequestSend->unlock();
    }
    // 打印
    void log() {}

} s_MLCalibConfig;

/* 标定集合*/
typedef struct s_CalibConfig
{
    s_CalibConfig() : m_HACalib(), m_MLCalib() {}
    s_HorizAlignConfig m_HACalib;
    s_MLCalibConfig m_MLCalib;
} s_CalibConfig;

}  // namespace wj_slam