/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2021-12-01 15:58:11
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-11-03 10:17:50
 */
#pragma once
#include "../type/type_device.h"
#include <mutex>
#include <string>
#include <wjod_log.h>

namespace wj_slam {

/** Web服务器参数 */
typedef struct s_WebCfg
{
    s_WebCfg()
    {
        m_socket.m_sLocalIP = "**************";
        m_socket.m_uiLocalPort = 10088;
    }
    // 打印
    void log()
    {
        LOGA(WINFO, "[param] WebServer IP: {}", m_socket.m_sLocalIP);
        LOGA(WINFO, "[param] WebServer Port: {}", m_socket.m_uiLocalPort);
    }

    s_DevCfg m_socket;

} s_WebCfg;

}  // namespace wj_slam