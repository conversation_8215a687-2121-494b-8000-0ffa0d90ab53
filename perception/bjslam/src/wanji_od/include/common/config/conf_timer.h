/*
 * @Description:
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2022-02-08 17:47:57
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-29 08:45:47
 */
#pragma once
#include "../type/type_status.h"
#include <iostream>
#include <math.h>
#include <string>
#include <sys/time.h>
#include <unistd.h>
typedef struct timeval sTimeval;

namespace wj_slam {

/**
 * @function: sleepMs
 * @description: 延时函数ms
 * @param {int} p_iDelay:延时时间
 * @input: p_iDelay
 * @output: null
 * @return {*}
 * @others: null
 */
inline void sleepMs(int p_iDelay)
{
    usleep(p_iDelay * 1000);
}

inline sTimeval getTimeNow()
{
    sTimeval l_CurrTime_;
    gettimeofday(&l_CurrTime_, NULL);
    return l_CurrTime_;
}

inline std::string getTimeNowStr()
{
    time_t timep;
    time(&timep);
    char tmp[64];
    strftime(tmp, sizeof(tmp), "%Y_%m_%d_%H_%M_%S", localtime(&timep));
    return tmp;
}

inline int getTimeDiffMs(sTimeval p_time1, sTimeval p_time2)
{
    int l_iTimeMSec =
        (p_time1.tv_sec - p_time2.tv_sec) * 1000 + (p_time1.tv_usec - p_time2.tv_usec) * 0.001;
    return l_iTimeMSec;
}

inline int getTimeDiffUs(sTimeval p_time1, sTimeval p_time2)
{
    int l_iTimeUSec =
        (p_time1.tv_sec - p_time2.tv_sec) * 1e+6 + (p_time1.tv_usec - p_time2.tv_usec);
    return l_iTimeUSec;
}

inline u_int64_t getTimeAddMs(int p_time1, sTimeval p_time2)
{
    u_int64_t l_iTimeMSec =
        (p_time2.tv_sec - 1640966400) * 1000 + p_time1 + p_time2.tv_usec * 0.001;
    return l_iTimeMSec;
}

inline sTimeval getTimeDiff(sTimeval p_time1, sTimeval p_time2)
{
    sTimeval l_iTimeDiff_;

    int l_iSec = p_time1.tv_usec - p_time2.tv_usec;

    if (abs(l_iSec) >= 1e6)
        l_iSec = l_iSec * 1e-6;
    else
        l_iSec = 0;

    l_iTimeDiff_.tv_sec = p_time1.tv_sec - p_time2.tv_sec + l_iSec;
    l_iTimeDiff_.tv_usec = p_time1.tv_usec - p_time2.tv_usec - l_iSec;
    return l_iTimeDiff_;
}

inline bool isTimeNew(sTimeval p_time1, sTimeval p_time2)
{
    return ((p_time1.tv_sec - p_time2.tv_sec) * 1e+6 + p_time1.tv_usec - p_time2.tv_usec) > 0
               ? true
               : false;
}

inline bool isTimeNew(int p_iT1, int p_iT2)
{
    return p_iT1 > p_iT2 ? true : false;
}

inline sTimeval getTimeTransUs(sTimeval& p_time1, int p_TranstimeUs)
{
    sTimeval l_timeTrans = p_time1;
    l_timeTrans.tv_usec += p_TranstimeUs;
    l_timeTrans.tv_sec += l_timeTrans.tv_usec / 1000000;
    l_timeTrans.tv_usec %= 1000000;
    // printf("transT: rawT:  %ld | %ld - tranUs %d align: %ld | %ld\n",
    //            p_time1.tv_sec,
    //            p_time1.tv_usec,
    //            p_TranstimeUs,
    //            l_timeTrans.tv_sec,
    //            l_timeTrans.tv_usec);
    return l_timeTrans;
}

typedef struct s_TimeConfig
{
  private:
    std::string stTimeSource[3] = {"Local", "Lidar", "Other"};

  public:
    sTimeval m_sTimeProcStart;  // 程序启动的系统时间
    sTimeval m_sTimeSyncStart;  // 得到授时的系统时间
    sTimeval m_sTimeSetSync;    // 设置授时时间(导入)
    int m_iTimeSource;          // 设置使用何种时钟源
    s_TimeConfig()
    {
        this->m_sTimeProcStart.tv_sec = 0;
        this->m_sTimeProcStart.tv_usec = 0;
        this->m_sTimeSyncStart.tv_sec = 0;
        this->m_sTimeSyncStart.tv_usec = 0;
        this->m_sTimeSetSync.tv_sec = 0;
        this->m_sTimeSetSync.tv_usec = 0;
        this->m_iTimeSource = int(TimeSource::Local);
    }

    /**
     * @function:
     * @description: 深拷贝
     * @param {*}
     * @return {*}
     * @others: null
     */
    s_TimeConfig& operator=(const s_TimeConfig& p_in)
    {
        this->m_sTimeProcStart = p_in.m_sTimeProcStart;
        this->m_sTimeSyncStart = p_in.m_sTimeSyncStart;
        this->m_sTimeSetSync = p_in.m_sTimeSetSync;
        this->m_iTimeSource = p_in.m_iTimeSource;
        return *this;
    }

} s_TimeConfig;

typedef struct s_DevTimeCfg
{
    s_DevTimeCfg() : m_sLidarTimeCfg()
    {
        m_bIsResetAlign = true;
        m_iMsTimeAlign = 0;
    }

    s_DevTimeCfg(sTimeval p_sProcStart) : m_sLidarTimeCfg()
    {
        m_bIsResetAlign = true;
        m_iMsTimeAlign = 0;
        this->m_sLidarTimeCfg.m_sTimeProcStart = p_sProcStart;
    }

    /**
     * @function: setProcStart
     * @description: 设置程序启动时间
     * @param {sTimeval} p_sProcStart  程序启动时间
     * @return {*}
     * @others: {*}
     */
    void setProcStart(sTimeval p_sProcStart)
    {
        this->m_sLidarTimeCfg.m_sTimeProcStart = p_sProcStart;
    }

    void printf(std::string head = "*")
    {
        std::cout << "*********** TimeCfg " << head << "***********" << std::endl;
        std::cout << "**  procT    : " << this->m_sLidarTimeCfg.m_sTimeProcStart.tv_sec << " "
                  << this->m_sLidarTimeCfg.m_sTimeProcStart.tv_usec << std::endl;
        std::cout << "**  syncT    : " << this->m_sLidarTimeCfg.m_sTimeSyncStart.tv_sec << " "
                  << this->m_sLidarTimeCfg.m_sTimeSyncStart.tv_usec << std::endl;
        std::cout << "**  recvT    : " << this->m_sLidarTimeCfg.m_sTimeSetSync.tv_sec << " "
                  << this->m_sLidarTimeCfg.m_sTimeSetSync.tv_usec << std::endl;
        std::cout << "**  align    : " << this->m_iMsTimeAlign << std::endl;
        std::cout << "********************************" << std::endl;
    }

    void resetAlign()
    {
        this->m_bIsResetAlign = true;
    }

    bool isResetAlign(sTimeval p_sDevOwnTime)
    {
        // 未授时
        if (this->m_bIsResetAlign)
            return true;
        // 若雷达内部时间出现倒退，则意味着雷达重启过
        return !isTimeNew(p_sDevOwnTime, this->m_sLidarTimeCfg.m_sTimeSyncStart);
    }

    bool isResetAlign(uint32_t p_sDevOwnTimeMs)
    {
        sTimeval l_stTime;
        l_stTime.tv_sec = floor(p_sDevOwnTimeMs * 1e-3);
        l_stTime.tv_usec = (p_sDevOwnTimeMs - l_stTime.tv_sec * 1e3) * 1e3;
        // 未授时
        if (this->m_bIsResetAlign)
            return true;
        // 若雷达内部时间出现倒退，则意味着雷达重启过
        return !isTimeNew(l_stTime, this->m_sLidarTimeCfg.m_sTimeSyncStart);
    }

    /**
     * @function: setTimeAlign
     * @description: 授时操作
     * @param {sTimeval} p_sLidarNetTime  收到该lidar包的网卡时间
     * @param {sTimeval} p_sDevOwnTime  该lidar包自带的时间
     * @param {sTimeval} p_sProcStart  程序启动时间
     * @return {*}
     * @others: {*}
     * sysProcStartT   99900
     * lidarT        NetT        NetT-sysProcStartT         alignLidarT
     * 3100        100000      100                                        100
     * 3200                                                                            (3200-3100) +
     * 100
     */
    void setTimeAlign(sTimeval p_sDevNetTime, sTimeval p_sDevOwnTime)
    {
        // 授时开始时间为雷达自带时间，后续新雷达Pkt时间和此时间获取差值
        this->m_sLidarTimeCfg.m_sTimeSyncStart = p_sDevOwnTime;
        this->m_sLidarTimeCfg.m_sTimeSetSync = p_sDevNetTime;

        // m_iMsTimeAlign 即 首Pkt  的网卡时间戳（减去系统启动时间）
        this->m_iMsTimeAlign = wj_slam::getTimeDiffMs(this->m_sLidarTimeCfg.m_sTimeSetSync,
                                                      this->m_sLidarTimeCfg.m_sTimeProcStart);

        this->m_bIsResetAlign = false;
        // this->printf();
    }

    void setTimeAlign(sTimeval p_sDevNetTime, uint32_t p_sDevOwnTimeMs)
    {
        sTimeval l_stTime;
        l_stTime.tv_sec = floor(p_sDevOwnTimeMs * 1e-3);
        l_stTime.tv_usec = (p_sDevOwnTimeMs - l_stTime.tv_sec * 1e3) * 1e3;
        // 授时开始时间为雷达自带时间，后续新雷达Pkt时间和此时间获取差值
        this->m_sLidarTimeCfg.m_sTimeSyncStart = l_stTime;
        this->m_sLidarTimeCfg.m_sTimeSetSync = p_sDevNetTime;

        // m_iMsTimeAlign 即 首Pkt  的网卡时间戳（减去系统启动时间）
        this->m_iMsTimeAlign = wj_slam::getTimeDiffMs(this->m_sLidarTimeCfg.m_sTimeSetSync,
                                                      this->m_sLidarTimeCfg.m_sTimeProcStart);

        this->m_bIsResetAlign = false;
        // this->printf();
    }

    /**
     * @function: applyTimeAlign
     * @description: 应用授时对齐
     * @param {st_TimeCfg&} p_sTimeCfg 当前lidar时间授时配置结构体
     * @param {timeval} p_sDevOwnTime  该lidar包自带的时间
     * @return {*}
     * @others: {*}
     */
    int applyTimeAlign_(timeval p_sDevOwnTime)
    {
        // 获取雷达本体时间的偏移量
        return (this->m_iMsTimeAlign
                + wj_slam::getTimeDiffMs(p_sDevOwnTime, this->m_sLidarTimeCfg.m_sTimeSyncStart));
    }

    uint32_t applyTimeAlign_(uint32_t p_sDevOwnTimeMs)
    {
        sTimeval l_stTime;
        l_stTime.tv_sec = floor(p_sDevOwnTimeMs * 1e-3);
        l_stTime.tv_usec = (p_sDevOwnTimeMs - l_stTime.tv_sec * 1e3) * 1e3;
        // 获取雷达本体时间的偏移量
        return (this->m_iMsTimeAlign
                + wj_slam::getTimeDiffMs(l_stTime, this->m_sLidarTimeCfg.m_sTimeSyncStart));
    }

    bool m_bIsResetAlign;
    s_TimeConfig m_sLidarTimeCfg;  // 雷达时间对齐配置
    int m_iMsTimeAlign;            // 时间补偿值 单位ms
} s_DevTimeCfg;

}  // namespace wj_slam