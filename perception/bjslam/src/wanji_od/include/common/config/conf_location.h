/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON>
 * @Date: 2021-12-01 15:41:18
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-29 08:45:49
 */
#pragma once
#include "../type/type_optimiz.h"
#include "../type/type_status.h"
#include <wjod_log.h>
namespace wj_slam {

/** 建图/定位-地图配置参数模板 */
typedef struct s_MapConfig
{
    s_MapConfig()
    {
        m_sMapName = "map";
        m_fMap_grid = 0.1;
        m_fMapRange = 10.0;
        m_fMapKFPoseGrid = 8.0;
        m_fMapMaxRange = 30.0;
        m_fMapKFSubRange = 30.0;
        m_iMapKFTimeStep = 9;
        m_fMapKFDistStep = 1.0;
        m_fMapKFAnglStep = 180.0;
    }

    // 打印
    void log()
    {
        LOGA(WINFO, "[param] Map Name {}", m_sMapName);
        LOGA(WINFO, "[param] Map Grid {}", m_fMap_grid);
        LOGA(WINFO, "[param] Map Size {}", m_fMapRange);
        LOGA(WINFO, "[param] Map PathGrid {}", m_fMapKFPoseGrid);
        LOGA(WINFO, "[param] Map MaxRange {}", m_fMapMaxRange);
        LOGA(WINFO, "[param] Map PathRange {}", m_fMapKFSubRange);
        LOGA(WINFO, "[param] Map TimeStep {}", m_iMapKFTimeStep);
        LOGA(WINFO, "[param] Map DistStep {}", m_fMapKFDistStep);
        LOGA(WINFO, "[param] Map AnglStep {}", m_fMapKFAnglStep);
    }

    void setModeDefault(int p_WorkMode, int p_ScenMode)
    {
        switch ((WorkMode)p_WorkMode)
        {
            case WorkMode::InitMapMode:
                m_fMapRange = 10.0;
                m_fMapKFPoseGrid = 1.0;
                m_fMapMaxRange = 30.0;
                break;
            case WorkMode::ContMapMode:
                m_fMapRange = 10.0;
                m_fMapKFPoseGrid = 1.0;
                m_fMapMaxRange = 30.0;
                break;
            case WorkMode::UpdateMapMode:
                m_fMapRange = 10.0;
                m_fMapKFPoseGrid = 1.0;
                m_fMapMaxRange = 30.0;
                break;
            case WorkMode::LocatMode:
                m_fMapKFPoseGrid = 8.0;
                m_fMapMaxRange = 30.0;
                m_fMapKFSubRange = 25.0;
                break;
            default: break;
        }
    }
    std::string m_sMapName;  // 地图名 SLAM保存 定位加载
    float m_fMap_grid;       // 地图下采样size
    float m_fMapRange;       // 附近子图半径
    float m_fMapKFPoseGrid;  // 地图KFPose下采样
    float m_fMapMaxRange;    // 地图最大范围-半径
    float m_fMapKFSubRange;  // 地图子图切割半径
    int m_iMapKFTimeStep;    // 建图频率控制（时间步长）
    float m_fMapKFDistStep;  // 建图频率控制（距离步长）
    float m_fMapKFAnglStep;  // 建图频率控制（角度步长）
} s_MapConfig;

/** 建图参数模板 */
typedef struct s_MappingConfig
{
    // default
    s_MappingConfig()
    {
        setDefaultMatch();
        m_bEnableLoop = false;
        optimiz_o3D = false;
    }
    // 默认为室外匹配参数
    void setDefaultMatch()
    {
        for (int i = 0; i < 5; ++i)
        {
            this->m_match[i].m_uiPlaneMaxPoints = 12;
            this->m_match[i].m_fPlaneMaxRadius = 3.5;
            this->m_match[i].m_fLine2DRadius = 0.2;
            this->m_match[i].m_uiLineMinPoints = 4;
            this->m_match[i].m_fLineMaxZDiff = 2.0;
            this->m_match[i].m_fMaxDist = std::max(0.8 - 0.2 * i, 0.2);
            this->m_match[i].m_bSampleMatch = true;
        }
    }
    void setModeDefault(int p_WorkMode, int p_ScenMode)
    {
        switch ((ScenMode)p_ScenMode)
        {
            // 室内匹配参数
            case ScenMode::INDOOR:
                for (int i = 0; i < 5; ++i)
                {
                    this->m_match[i].m_uiPlaneMaxPoints = 8;
                    this->m_match[i].m_fPlaneMaxRadius = 2.5;
                    this->m_match[i].m_fLine2DRadius = 0.2;
                    this->m_match[i].m_uiLineMinPoints = 4;
                    this->m_match[i].m_fLineMaxZDiff = 2.0;
                    this->m_match[i].m_fMaxDist = std::max(0.4 - 0.1 * i, 0.2);
                    this->m_match[i].m_bSampleMatch = true;
                }
                break;
            case ScenMode::OUTDOOR:
                // 默认参数
                setDefaultMatch();
                break;
            case ScenMode::VACROUS:
                // 默认参数
                setDefaultMatch();
                break;
            default: break;
        }
    }
    // 打印
    void log()
    {
        LOGA(WINFO,
             "[param] SLAM-Match General: L{}, O{}, S{}, D{:.2f}",
             this->m_bEnableLoop,
             this->optimiz_o3D,
             this->m_match[2].m_bSampleMatch,
             this->m_match[2].m_fMaxDist);
        LOGA(WINFO,
             "[param] SLAM-Match Plane: R{:.2f}, P{}",
             this->m_match[2].m_fPlaneMaxRadius,
             this->m_match[2].m_uiPlaneMaxPoints);
        LOGA(WINFO,
             "[param] SLAM-Match Line: R{:.2f}, Z{:.2f}, P{}",
             this->m_match[2].m_fLine2DRadius,
             this->m_match[2].m_fLineMaxZDiff,
             this->m_match[2].m_uiLineMinPoints);
    }
    s_MatcherConfig m_match[5];  // 建图匹配参数
    bool m_bEnableLoop;          // 是否启动回环
    bool optimiz_o3D;
} s_MappingConfig;

/** 定位参数模板 */
typedef struct s_LoctConfig
{
    // default
    s_LoctConfig()
    {
        setDefaultMatch();
        this->m_manual.m_uiPlaneMaxPoints = 15;
        this->m_manual.m_fPlaneMaxRadius = 10.0;
        this->m_manual.m_fLine2DRadius = 0.6;
        this->m_manual.m_uiLineMinPoints = 3;
        this->m_manual.m_fLineMaxZDiff = 10.0;
        this->m_manual.m_fMaxDist = 1.0;
        this->m_manual.m_bSampleMatch = true;
        optimiz_o3D = false;
    }
    // 默认为室外匹配参数
    void setDefaultMatch()
    {
        this->m_match.m_uiPlaneMaxPoints = 10;
        this->m_match.m_fPlaneMaxRadius = 3.0;
        this->m_match.m_fLine2DRadius = 0.4;
        this->m_match.m_uiLineMinPoints = 4;
        this->m_match.m_fLineMaxZDiff = 1.0;
        this->m_match.m_fMaxDist = 0.8;
        this->m_match.m_bSampleMatch = true;
    }
    void setModeDefault(int p_WorkMode, int p_ScenMode)
    {
        switch ((ScenMode)p_ScenMode)
        {
            // 室内匹配参数
            case ScenMode::INDOOR:
                this->m_match.m_uiPlaneMaxPoints = 10;
                this->m_match.m_fPlaneMaxRadius = 3.0;
                this->m_match.m_fLine2DRadius = 0.2;
                this->m_match.m_uiLineMinPoints = 4;
                this->m_match.m_fLineMaxZDiff = 2.0;
                this->m_match.m_fMaxDist = 0.4;
                this->m_match.m_bSampleMatch = true;
                break;
            case ScenMode::OUTDOOR:
                // 默认参数
                setDefaultMatch();
                break;
            case ScenMode::VACROUS:
                // 默认参数
                setDefaultMatch();
                break;
            default: break;
        }
    }
    // 打印
    void log()
    {
        LOGA(WINFO,
             "[param] Loct-Match General: O{}, S{}, D{:.2f}",
             this->optimiz_o3D,
             this->m_match.m_bSampleMatch,
             this->m_match.m_fMaxDist);
        LOGA(WINFO,
             "[param] Loct-Match Plane: R{:.2f}, P{}",
             this->m_match.m_fPlaneMaxRadius,
             this->m_match.m_uiPlaneMaxPoints);
        LOGA(WINFO,
             "[param] Loct-Match Line: R{:.2f}, Z{:.2f}, P{}",
             this->m_match.m_fLine2DRadius,
             this->m_match.m_fLineMaxZDiff,
             this->m_match.m_uiLineMinPoints);
        LOGA(WINFO,
             "[param] Manul-Match General: O{}, S{}, D{:.2f}",
             this->optimiz_o3D,
             this->m_manual.m_bSampleMatch,
             this->m_manual.m_fMaxDist);
        LOGA(WINFO,
             "[param] Manul-Match Plane: R{:.2f}, P{}",
             this->m_manual.m_fPlaneMaxRadius,
             this->m_manual.m_uiPlaneMaxPoints);
        LOGA(WINFO,
             "[param] Manul-Match Line: R{:.2f}, Z{:.2f}, P{}",
             this->m_manual.m_fLine2DRadius,
             this->m_manual.m_fLineMaxZDiff,
             this->m_manual.m_uiLineMinPoints);
    }
    s_MatcherConfig m_match;   // 定位匹配参数
    s_MatcherConfig m_manual;  // 手动定位匹配参数
    bool optimiz_o3D;
} s_LoctConfig;

}  // namespace wj_slam