/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2021-12-01 15:58:11
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-29 08:45:48
 */
#pragma once
#include <iostream>
#include <sstream>
#include <string.h>
#include <string>
#include <vector>
namespace wj_slam {
/*网卡配置信息*/
typedef struct s_NetCfg
{
    s_NetCfg()
    {
        this->isSet = false;
        this->isDHCP = true;
        this->isNetwork = false;
        this->m_sNetName = "";
        this->m_sIpSet = "";
        this->m_sMask = "24";  // 掩码默认为*************
        this->m_sGateway = "";
        this->m_vsDNS.clear();  // DHS 默认为空
    }

    s_NetCfg& operator=(const s_NetCfg& p_pCfg)
    {
        this->isSet = p_pCfg.isSet;
        this->isDHCP = p_pCfg.isDHCP;
        this->isNetwork = p_pCfg.isNetwork;
        this->m_sNetName = p_pCfg.m_sNetName;
        this->m_sIpSet = p_pCfg.m_sIpSet;
        this->m_sMask = p_pCfg.m_sMask;
        this->m_sGateway = p_pCfg.m_sGateway;
        this->m_vsDNS = p_pCfg.m_vsDNS;
        return *this;
    }

    void setNetWork(bool res)
    {
        this->isNetwork = res;
        // 联网模式下 设定DNS，网关由外部设定，非联网模式下 清空网关和DNS
        if (this->isNetwork)
        {
            this->m_vsDNS.resize(0);
            this->m_vsDNS.push_back("***************");
            this->m_vsDNS.push_back("*******");
        }
        else
        {
            this->m_vsDNS.clear();
            this->m_sGateway = "";
        }
    }

    void setDHCP(bool res)
    {
        this->isDHCP = res;
        // DHCP模式下 清空设定IP和网关
        if (this->isDHCP)
        {
            this->m_sIpSet = "";
            this->m_sGateway = "";
            this->m_vsDNS.clear();
        }
    }

    /**
     * @description: 设置网关
     * @param {}
     * @param {}
     * @return {*}
     * @other: DHCP模式下已清理网关  非联网模式下 已清理网关   非DHCP模式下若联网则须网关
     * 非DHCP模式若不联网则不须网关
     */
    void setGateway()
    {
        if (this->m_sIpSet.empty())
        {
            printf("[err] setGateway no set IP\n");
            return;
        }
        std::vector<std::string> l_vsIpList = getIpField_(this->m_sIpSet);

        if (l_vsIpList.size() == 4)
            this->m_sGateway = l_vsIpList[0] + "." + l_vsIpList[1] + "." + l_vsIpList[2] + ".1";
        else
            std::cout << "[err] setGateway setIp split fail: [ " << this->m_sIpSet
                      << " ] to split size: " << l_vsIpList.size() << std::endl;
    }

    std::vector<std::string> getIpField_(std::string p_sIP)
    {
        std::string::size_type pos1, pos2;
        std::string l_sSeparator = ".";
        std::vector<std::string> l_vStrList;
        pos2 = p_sIP.find(l_sSeparator);
        pos1 = 0;
        while (std::string::npos != pos2)
        {
            l_vStrList.push_back(p_sIP.substr(pos1, pos2 - pos1));
            pos1 = pos2 + l_sSeparator.size();
            pos2 = p_sIP.find(l_sSeparator, pos1);
        }
        if (pos1 != p_sIP.length())
            l_vStrList.push_back(p_sIP.substr(pos1));
        return l_vStrList;
    }

    bool isSameNetSegment(std::string p_v1, std::string p_v2)
    {
        std::vector<std::string> l_vsIpList1 = getIpField_(p_v1);
        std::vector<std::string> l_vsIpList2 = getIpField_(p_v2);
        if (l_vsIpList1.size() == 4 && l_vsIpList2.size() == 4)
        {
            if (l_vsIpList1[0] == l_vsIpList2[0] && l_vsIpList1[1] == l_vsIpList2[1]
                && l_vsIpList1[2] == l_vsIpList2[2])
                return true;
            else
                return false;
        }
        else
            return false;
    }

    void printf(std::string head = "*")
    {
        std::cout << "*********** NetInfo " << head << "***********" << std::endl;
        std::cout << "**  netName    : " << this->m_sNetName << std::endl;
        if (this->m_sIpRead != "")
            std::cout << "**  ipRead     : " << this->m_sIpRead << std::endl;
        if (this->isSet)
        {
            std::cout << "**  isDHCP     : " << this->isDHCP << std::endl;
            std::cout << "**  ipSet      : " << this->m_sIpSet << std::endl;
            std::cout << "**  mask       : " << this->m_sMask << std::endl;
            std::cout << "**  gatewaymask: " << this->m_sGateway << std::endl;
            for (uint32_t i = 0; i < this->m_vsDNS.size(); i++)
                std::cout << "**  DNS[" << i << "] : " << this->m_vsDNS[i] << std::endl;
        }
        else
            std::cout << "**  yaml not set" << this->isSet << std::endl;
        std::cout << "********************************" << std::endl;
    }

    bool isSet;              // 是否从yaml读取到 存在yaml中为空的情况
    bool isDHCP;             // 是否动态IP
    bool isNetwork;          // 是否联网
    std::string m_sNetName;  // 网卡名
    std::string m_sIpRead;   // 读取IP
    std::string m_sIpSet;    // 设置IP
    std::string m_sMask;     // 掩码
    std::string m_sGateway;  // 网关
    std::vector<std::string> m_vsDNS;
} s_NetCfg;

}  // namespace wj_slam