/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2021-12-01 15:58:11
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-29 09:28:48
 */
#pragma once
#include "./common_ex.h"
#include "./config/conf_net.h"

namespace wj_slam {
typedef struct s_masterOdCfg
{
  private:
    std::string m_LidarMode[2] = {"Online", "Offline"};
    std::string m_Bool[2] = {"false", "true"};

  public:
#pragma region "系统"
    SYSPARAMOD* m_slam;
#pragma endregion

#pragma region "录包"
    bool m_bDefaultSavePcap;          // 是否默认录制PCAP
    int m_iRecordDuration;            // 录制数据包当前时长
    uint32_t m_uiRecordTimeInterval;  // 录制时间间隔
    std::string m_sOffLineDataPath;   // 离线数据存放路径
#pragma endregion

#pragma region "播包"
    bool m_bIsStart;           // 是否启动虚拟数据播放
    uint32_t m_uiPlayBagRate;  // pcap播放速度
#pragma endregion

#pragma region "解密"
    std::string m_sMacNo;     // 密钥
    std::string m_sSecretNo;  // 密电码
#pragma endregion

#pragma region "网络配置"
    std::string m_sNetCfgPath;  // 网络配置文件路径
    std::vector<wj_slam::s_NetCfg> m_net;
    std::string m_sLocalIP;
#pragma endregion

#pragma region
    uint16_t m_uiVirtualAGVPort;  // 离线状态AGV端口
#pragma endregion

    // 时间参数
    s_TimeConfig m_time;

  public:
    s_masterOdCfg()
    {
        m_slam = SYSPARAMOD::getIn();
        m_bDefaultSavePcap = false;  // 默认不录
        m_iRecordDuration = 0;
        m_uiRecordTimeInterval = 600;  // 默认录制10min 可考虑/LidarNum 避免内存爆
        m_bIsStart = false;
        m_uiPlayBagRate = 10;
        m_sNetCfgPath = "/etc/netplan/01-netcfg.yaml";  // 默认以WLC703路径
        m_net.clear();
        m_sLocalIP = "127.0.0.1";
        m_uiVirtualAGVPort = 10000;
    }

    void printf()
    {
        if (m_slam->m_bDebugModel)
        {
            LOGA(WINFO, "[param] netCfgPath", m_sNetCfgPath);
        }
    }

    static s_masterOdCfg* getIn()
    {
        static s_masterOdCfg firstInit;
        return &firstInit;
    }
} s_masterOdCfg;
}  // namespace wj_slam