/*
 * @Description:
 * @Version: 1.0
 * @Autor: <PERSON><PERSON>
 * @Date: 2021-12-01 15:28:36
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-10-25 14:46:59
 */
#pragma once
#include "type_status.h"
#include <Eigen/Eigen>
#include <boost/shared_ptr.hpp>
#include <iostream>
#include <mutex>

namespace wj_slam {

typedef struct s_POSE6D
{
  private:
    Eigen::Vector3d m_rpy_;
    double m_pi2deg_ = 180.0 / M_PI;
    // void calcRPY_()
    // {
    //     this->m_rpy_ = this->m_quat.matrix().eulerAngles(2, 1, 0);  //绕xyz旋转
    //     // std::cout<<"rpy:"<<m_rpy_.z()<<","<<m_rpy_.y()<<","<<m_rpy_.x()<<","<<std::endl;
    //     // std::cout<<"q:"<<m_quat.x()<<","<<m_quat.y()<<","<<m_quat.z()<<","<<m_quat.w()
    //     // <<std::endl;

    //     this->m_rpy_ = this->m_rpy_ / M_PI * 180.0;
    // }
    void calcRPY_()
    {
        double sinr_cosp = +2.0 * (m_quat.w() * m_quat.x() + m_quat.y() * m_quat.z());
        double cosr_cosp = +1.0 - 2.0 * (m_quat.x() * m_quat.x() + m_quat.y() * m_quat.y());
        m_rpy_[2] = atan2(sinr_cosp, cosr_cosp) * m_pi2deg_;

        // pitch (y-axis rotation)
        double sinp = +2.0 * (m_quat.w() * m_quat.y() - m_quat.z() * m_quat.x());
        if (fabs(sinp) >= 1)
            m_rpy_[1] = copysign(M_PI / 2, sinp);  // use 90 degrees if out of range
        else
            m_rpy_[1] = asin(sinp) * m_pi2deg_;

        // yaw (z-axis rotation)
        double siny_cosp = +2.0 * (m_quat.w() * m_quat.z() + m_quat.x() * m_quat.y());
        double cosy_cosp = +1.0 - 2.0 * (m_quat.y() * m_quat.y() + m_quat.z() * m_quat.z());
        m_rpy_[0] = atan2(siny_cosp, cosy_cosp) * m_pi2deg_;
    }

  public:
    Eigen::Quaterniond m_quat;
    Eigen::Vector3d m_trans;
    PoseStatus m_bFlag;
    s_POSE6D()
    {
        m_quat = Eigen::Quaterniond::Identity();
        m_rpy_ = Eigen::Vector3d::Zero();
        m_trans = Eigen::Vector3d::Zero();
        m_bFlag = PoseStatus::Default;
    }
    void reset()
    {
        m_quat = Eigen::Quaterniond::Identity();
        m_rpy_ = Eigen::Vector3d::Zero();
        m_trans = Eigen::Vector3d::Zero();
        m_bFlag = PoseStatus::Default;
        return;
    }
    s_POSE6D& operator=(const s_POSE6D& p_pos)
    {
        this->m_quat = p_pos.m_quat;
        this->m_trans = p_pos.m_trans;
        this->m_bFlag = p_pos.m_bFlag;
        calcRPY_();
        return *this;
    }
    s_POSE6D operator*(const s_POSE6D& p_pos)
    {
        s_POSE6D l_stPose;
        l_stPose.m_trans = this->m_quat * p_pos.m_trans + this->m_trans;
        l_stPose.m_quat = this->m_quat * p_pos.m_quat;
        l_stPose.m_quat.normalize();
        l_stPose.calcRPY_();
        return l_stPose;
    }
    s_POSE6D& operator*=(const s_POSE6D& p_pos)
    {
        this->m_trans = this->m_trans + this->m_quat * p_pos.m_trans;
        this->m_quat = this->m_quat * p_pos.m_quat;
        this->m_quat.normalize();
        calcRPY_();
        return *this;
    }
    s_POSE6D operator*(const float p_fMul)
    {
        s_POSE6D l_stPose;
        // 等于0则直接返回空
        if (p_fMul == 0)
            return l_stPose;

        // 基本位姿变换单元，若变化为负则逆变换
        s_POSE6D l_stCeilPose = (p_fMul > 0) ? *this : this->inverse();
        // 整数位
        int decMul = floor(abs(p_fMul));
        // 小数位
        float dotMul = abs(p_fMul) - decMul;
        // 整数位变换
        while (decMul > 0)
        {
            decMul--;
            l_stPose = l_stPose * l_stCeilPose;
        }
        // 整数位变换
        s_POSE6D l_stDotPose;
        l_stDotPose.m_trans = l_stCeilPose.m_trans * dotMul;
        l_stDotPose.m_quat = Eigen::Quaterniond().Identity().slerp(dotMul, l_stCeilPose.m_quat);
        // // 合并整数位和小数位变换
        l_stPose = l_stDotPose * l_stPose;
        l_stPose.m_quat.normalize();
        l_stPose.calcRPY_();
        return l_stPose;
    }
    s_POSE6D operator/(const float p_fMul)
    {
        // 等于乘以倒数
        if (p_fMul > 0.001 || p_fMul < -0.001)
            return *this * (1.0 / p_fMul);
        else
            return *this;
    }
    s_POSE6D& operator*=(const float p_fMul)
    {
        *this = *this * p_fMul;
        return *this;
    }
    s_POSE6D& operator/=(const float p_fMul)
    {
        *this = *this / p_fMul;
        return *this;
    }
    Eigen::Vector3d operator*(const Eigen::Vector3d& p_pos)
    {
        Eigen::Vector3d l_stPose;
        l_stPose = this->m_quat * p_pos + this->m_trans;
        return l_stPose;
    }
    float* data()
    {
        float* l_pfOut = new float[7];
        l_pfOut[0] = this->m_quat.x();
        l_pfOut[1] = this->m_quat.y();
        l_pfOut[2] = this->m_quat.z();
        l_pfOut[3] = this->m_quat.w();
        l_pfOut[4] = this->m_trans.x();
        l_pfOut[5] = this->m_trans.y();
        l_pfOut[6] = this->m_trans.z();
        return l_pfOut;
    }
    Eigen::VectorXd coeffs_6()
    {
        Eigen::VectorXd l_coeffs = Eigen::VectorXd::Zero(6);
        l_coeffs << this->m_trans, this->roll(), this->pitch(), this->yaw();
        return l_coeffs;
    }
    Eigen::VectorXd coeffs_7()
    {
        Eigen::VectorXd l_coeffs = Eigen::VectorXd::Zero(6);
        l_coeffs << this->m_trans, this->m_quat.coeffs();
        return l_coeffs;
    }
    double normXY()
    {
        double l_dist;
        l_dist = std::sqrt(std::pow(this->m_trans.x(), 2) + std::pow(this->m_trans.y(), 2));
        return l_dist;
    }
    double norm()
    {
        return this->m_trans.norm();
    }
    double x()
    {
        return this->m_trans.x();
    }
    double y()
    {
        return this->m_trans.y();
    }
    double z()
    {
        return this->m_trans.z();
    }
    double yaw()
    {
        return this->m_rpy_.x();
    }
    double pitch()
    {
        return this->m_rpy_.y();
    }
    double roll()
    {
        return this->m_rpy_.z();
    }
    void setX(double x)
    {
        this->m_trans[0] = x;
    }
    void setY(double y)
    {
        this->m_trans[1] = y;
    }
    void setZ(double z)
    {
        this->m_trans[2] = z;
    }
    void setRPY(double roll, double pitch, double yaw)
    {
        roll = std::fmod(roll + 360.0, 360.0);
        pitch = std::fmod(pitch + 360.0, 360.0);
        yaw = std::fmod(yaw + 360.0, 360.0);
        roll = roll > 180 ? roll - 360 : roll;
        pitch = pitch > 180 ? pitch - 360 : pitch;
        yaw = yaw > 180 ? yaw - 360 : yaw;

        m_rpy_ << yaw, pitch, roll;
        roll = (roll * M_PI / 180.0);
        pitch = (pitch * M_PI / 180.0);
        yaw = (yaw * M_PI / 180.0);
        // m_rpy_ << yaw, pitch, roll;
        Eigen::Matrix3d R;
        R = Eigen::AngleAxisd(yaw, ::Eigen::Vector3d::UnitZ())
            * Eigen::AngleAxisd(pitch, ::Eigen::Vector3d::UnitY())
            * Eigen::AngleAxisd(roll, ::Eigen::Vector3d::UnitX());
        this->m_quat = R;
    }
    void setQuat(const Eigen::Quaterniond& p_quat)
    {
        this->m_quat = p_quat.normalized();
        calcRPY_();
    }
    s_POSE6D inverse()
    {
        s_POSE6D l_stPose;
        l_stPose.m_quat = this->m_quat.conjugate();
        l_stPose.m_trans = -(l_stPose.m_quat * this->m_trans);
        l_stPose.m_quat.normalize();
        l_stPose.calcRPY_();
        return l_stPose;
    }
    void printf(std::string head = "*")
    {
        this->m_quat.normalize();
        calcRPY_();
        std::cout << "*********** Pose " << head << "***********" << std::endl;
        std::cout << "**  x : " << this->x() << " y : " << this->y() << " z : " << this->z()
                  << "  **" << std::endl;
        std::cout << "**  r : " << this->roll() << " p : " << this->pitch()
                  << " y : " << this->yaw() << "  **" << std::endl;
        std::cout << "pose status : " << this->m_bFlag << std::endl;
        std::cout << "**************************" << std::endl;
    }
    typedef boost::shared_ptr<s_POSE6D> Ptr;
} s_POSE6D;
typedef s_POSE6D s_TWIST;

typedef struct s_PoseWithTwist
{
    std::mutex m_lock;
    s_POSE6D m_Pose;
    s_TWIST m_Twist;
    int m_iTimetamp;
    int m_iRecvTimestamp;
    PoseStatus m_bFlag;
    std::string slamPosStaus[6] =
        {"初始定位", "初始定位", "连续定位", "虚拟定位", "无效定位", "设置定位"};
    int m_iScanId;
    s_PoseWithTwist()
    {
        m_iTimetamp = 0;
        m_iRecvTimestamp = 0;
        m_bFlag = PoseStatus::Default;
        m_iScanId = 0;
    }
    void reset()
    {
        m_Pose.reset();
        m_Twist.reset();
        m_iTimetamp = 0;
        m_iRecvTimestamp = 0;
        m_bFlag = PoseStatus::Default;
        m_iScanId = 0;
    }
    s_PoseWithTwist(const s_PoseWithTwist& p)
    {
        std::lock_guard<std::mutex> l_mtx(this->m_lock);
        m_iTimetamp = p.m_iTimetamp;
        m_iRecvTimestamp = p.m_iRecvTimestamp;
        m_bFlag = p.m_bFlag;
        m_iScanId = p.m_iScanId;
        m_Pose = p.m_Pose;
        m_Twist = p.m_Twist;
    }
    s_PoseWithTwist& operator=(const s_PoseWithTwist& p_pos)
    {
        std::lock_guard<std::mutex> l_mtx(this->m_lock);
        this->m_Pose = p_pos.m_Pose;
        this->m_Twist = p_pos.m_Twist;
        this->m_bFlag = p_pos.m_bFlag;
        this->m_iScanId = p_pos.m_iScanId;
        this->m_iTimetamp = p_pos.m_iTimetamp;
        this->m_iRecvTimestamp = p_pos.m_iRecvTimestamp;
        return *this;
    }
    void setFlag(PoseStatus p_bFlag)
    {
        std::lock_guard<std::mutex> l_mtx(this->m_lock);
        this->m_bFlag = p_bFlag;
    }
    std::string getPoseStatus()
    {
        return slamPosStaus[this->m_bFlag];
    }
} s_PoseWithTwist;
typedef struct s_fuseTwist
{
    s_TWIST m_Twist;
    int m_iTimetamp;
    TwistStatus m_iStatus;
    int m_iTwistId;
    s_fuseTwist()
    {
        m_iTimetamp = 0;
        m_iStatus = TwistStatus::Unknown;
        m_iTwistId = 0;
    }
    void reset()
    {
        m_Twist.reset();
        m_iTimetamp = 0;
        m_iStatus = TwistStatus::Unknown;
        m_iTwistId = 0;
    }
    s_fuseTwist& operator=(const s_fuseTwist& p_pos)
    {
        this->m_Twist = p_pos.m_Twist;
        this->m_iTimetamp = p_pos.m_iTimetamp;
        this->m_iStatus = p_pos.m_iStatus;
        this->m_iTwistId = p_pos.m_iTwistId;
        return *this;
    }
} s_fuseTwist;

typedef struct RobotPos
{
    std::string RobotSICKPosStaus[5] = {"初始定位", "连续定位", "虚拟定位", "停止定位", "无效定位"};
    std::string RobotWJPosStaus[4] = {"无效定位", "初始定位", "连续定位", "虚拟定位"};
    Eigen::Vector3d t;
    Eigen::Quaterniond q;
    SICKPoseStatus flagSick;  // Initial | Continue | Virtual | Stop | invalid - 0 1 2 3 4
    WJPoseStatus flagWj;      // invalid Initial | Continue | Virtual 0 1 2 3
    int mode;                 // Mark\LOAM 0 1
    int timestamp;
    double meandev;
    RobotPos()
    {
        this->t = Eigen::Vector3d::Zero();
        this->q = Eigen::Quaterniond::Identity();
        this->flagSick = SICKPoseStatus::InvalidSICK;
        this->flagWj = WJPoseStatus::InvalidWJ;
        this->mode = 1;
        this->timestamp = 0;
        this->meandev = 0.0;
    }

    std::string getRobotPoseStatus(bool p_bIsSICK)
    {
        if (p_bIsSICK)
            return this->RobotSICKPosStaus[this->flagSick];
        else
            return this->RobotWJPosStaus[this->flagWj];
    }

    void printf(bool p_bIsSICK, std::string head = "")
    {
        std::cout << "*********** Pose " << head << "***********" << std::endl;
        std::cout << "**  x : " << this->t.x() << " y : " << this->t.y() << " a : " << this->t.z()
                  << "  **" << std::endl;
        std::cout << "pose status : " << this->getRobotPoseStatus(p_bIsSICK) << std::endl;
        std::cout << "time  stamp : " << this->timestamp << std::endl;
        std::cout << "**************************" << std::endl;
    }
} s_RobotPos;
}  // namespace wj_slam