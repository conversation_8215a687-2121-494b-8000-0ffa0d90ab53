/*
 * @Description:
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2021-12-01 15:29:35
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-10-14 14:03:16
 */
#pragma once
#include <sys/types.h>

namespace wj_slam {

/** 匹配器参数模板 */
typedef struct s_MatcherConfig
{
    u_int m_uiPlaneMaxPoints;  //设置面点k近邻搜索
    float m_fPlaneMaxRadius;   //设置面点搜索到的N点的最大距离差
    float m_fLine2DRadius;     //设置角点2D搜索半径
    u_int m_uiLineMinPoints;   //设置角点2D匹配最小点数
    float m_fLineMaxZDiff;     //设置角点2D匹配有效高差范围
    float m_fMaxDist;          //设置点到线、面距离阈值
    bool m_bSampleMatch;       //设置稀疏化
} s_MatcherConfig;

}  // namespace wj_slam