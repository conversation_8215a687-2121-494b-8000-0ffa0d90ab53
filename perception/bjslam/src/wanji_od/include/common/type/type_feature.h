/*
 * @Description:
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2021-12-01 15:28:21
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-10-24 19:02:48
 */
#pragma once
#include <boost/shared_ptr.hpp>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>

namespace wj_slam {

template <typename T> class FEATURE_PAIR {
  private:
    typedef pcl::PointCloud<T> PC_;

  public:
    boost::shared_ptr<PC_> first = NULL;   // 角
    boost::shared_ptr<PC_> second = NULL;  // 面
    boost::shared_ptr<PC_> third = NULL;   // 靶标
    boost::shared_ptr<PC_> fourth = NULL;  // 马路伢子&车道线
    boost::shared_ptr<PC_> allPC = NULL;
    int m_dTimestamp;
    int m_iRecvTimestamp;
    int m_dTimespan;
    u_int32_t m_uiScanFrame;
    int m_iSample2ndSize;
    FEATURE_PAIR()
    {
        first.reset(new PC_());
        second.reset(new PC_());
        third.reset(new PC_());
        fourth.reset(new PC_());
        allPC.reset(new PC_());
        m_iRecvTimestamp = 0;
        m_dTimestamp = 0;
        m_dTimespan = 0;
        m_uiScanFrame = 0;
        m_iSample2ndSize = -1;
    }
    int markSize()
    {
        return third->points.size();
    }
    int cornerSize()
    {
        return first->points.size();
    }
    int surfSize()
    {
        return second->points.size();
    }
    int curbSize()
    {
        return fourth->points.size();
    }
    int pcSize()
    {
        return allPC->points.size();
    }
    int recvTime()
    {
        return m_iRecvTimestamp;
    }
    int time()
    {
        return m_dTimestamp;
    }
    u_int32_t scanFrameID()
    {
        return m_uiScanFrame;
    }
    void free()
    {
        if (this->first)
            PC_().swap(*(this->first));
        if (this->second)
            PC_().swap(*(this->second));
        if (this->third)
            PC_().swap(*(this->third));
        if (this->fourth)
            PC_().swap(*(this->third));
        if (this->allPC)
            PC_().swap(*(this->allPC));
    }
    /**
     * @function:
     * @description: 深拷贝
     * @param {*}
     * @return {*}
     * @others: null
     */
    FEATURE_PAIR& operator=(const FEATURE_PAIR& p_in)
    {
        if (this->first == NULL)
            this->first.reset(new PC_());
        if (this->second == NULL)
            this->second.reset(new PC_());
        if (this->third == NULL)
            this->third.reset(new PC_());
        if (this->fourth == NULL)
            this->fourth.reset(new PC_());
        if (this->allPC == NULL)
            this->allPC.reset(new PC_());
        *(this->allPC) = *p_in.allPC;
        // wjPrint(COLOR_RED, "deep copy FEATURE_PAIR", NULL);
        *(this->first) = *p_in.first;
        *(this->second) = *p_in.second;
        *(this->third) = *p_in.third;
        *(this->fourth) = *p_in.fourth;
        //
        this->m_iRecvTimestamp = p_in.m_iRecvTimestamp;
        this->m_dTimestamp = p_in.m_dTimestamp;
        this->m_dTimespan = p_in.m_dTimespan;
        this->m_uiScanFrame = p_in.m_uiScanFrame;
        this->m_iSample2ndSize = p_in.m_iSample2ndSize;
        return *this;
    }
    FEATURE_PAIR& operator+=(const FEATURE_PAIR& p_in)
    {
        if (this->first == NULL)
            this->first.reset(new PC_());
        if (this->second == NULL)
            this->second.reset(new PC_());
        if (this->third == NULL)
            this->third.reset(new PC_());
        if (this->fourth == NULL)
            this->fourth.reset(new PC_());
        if (this->allPC == NULL)
            this->allPC.reset(new PC_());
        *(this->allPC) += *p_in.allPC;
        // wjPrint(COLOR_RED, "deep copy FEATURE_PAIR", NULL);
        *(this->first) += *p_in.first;
        *(this->second) += *p_in.second;
        *(this->third) += *p_in.third;
        *(this->fourth) += *p_in.fourth;
        //
        this->m_iRecvTimestamp = p_in.m_iRecvTimestamp;
        this->m_dTimestamp = p_in.m_dTimestamp;
        this->m_uiScanFrame = p_in.m_uiScanFrame;
        return *this;
    }

    ~FEATURE_PAIR()
    {
        this->free();
        // std::cerr << "~FEATURE_PAIR" << this << std::endl;
    }
    typedef boost::shared_ptr<FEATURE_PAIR<T>> Ptr;
};

}  // namespace wj_slam