/*
 * @Descripttion:
 * @version:
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2021-04-28 14:37:41
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-29 09:40:24
 */
#pragma once
#include "./common_720OD.h"
#include "./config/conf_agv.h"
#include "./config/conf_calib.h"
#include "./config/conf_driver.h"
#include "./config/conf_fae.h"
#include "./config/conf_lidar.h"
#include "./config/conf_location.h"
#include "./config/conf_odometry.h"
#include "./config/conf_preproc.h"
#include "./config/conf_timer.h"
#include "./config/conf_web.h"
#include "./type/type_frame.h"
#include "./type/type_status.h"
#include "tic_toc.h"
#include "unistd.h"
#include "wjod_log.h"
#define OD_VERSION "V0.0.0.2"
namespace wj_slam {

enum PROCSTATE {
    NOSTART = 0,   // 未启动/未实例化
    STARTING = 1,  // 启动中-初始化中
    RUNNING = 2,   // 运行中-初始化完成
    STOPING = 3,   // 关闭中
    STARTERR = 4,  // 初始化异常
    STOPERR = 5    // 关闭异常
};

struct SYSPARAMOD
{
  private:
    std::string m_WorkMode[5] = {"StandBy", "InitialMap", "ContinusMap", "Location", "UpdateMap"};
    std::string m_ParamMode[4] = {"indoor", "outdoor", "vacrous", "labor"};
    std::string m_Bool[2] = {"false", "true"};
    std::string m_LidarMode[2] = {"Offline", "Online"};
    std::string m_LogLevel[6] = {"DEFAULT",
                                 "ERROR",
                                 "ERROR-WARN",
                                 "ERROR-WARN-INFO",
                                 "ERROR-WARN-INFO-DEBUG",
                                 "ERROR-WARN-INFO-DEBUG-TRACE"};

  public:
    // 模式设置
    int m_iWorkMode;        // 工作模式
    bool m_bIsOnlineMode;   // 是否雷达在线模式
    bool m_bDebugModel;     // 调试模式，默认false 发布点云 区分角面
    int m_nParam_mode;      // 建图参数模式
    int m_iLogLevel;        // 手动设置日志级别
    PROCSTATE m_RunStatus;  // 运行状态

    bool m_bSendCurPC;  // 可视化lidar点云开关
    bool m_bSendMap;    // 可视化map点云开关
    int m_iViewMode;    // 地图可视化模式

    std::string m_sPkgPath;    // 工作区路径
    std::string m_sLogPath;    // 日志路径
    std::string m_sOdVersion;  // 版本号

    /** 参数列表 **/
    // 雷达参数
    int m_iLidarNum;
    std::vector<s_LidarConfig> m_lidar;
    // 设备参数
    std::vector<s_DevCfg*> m_devList;
    // 标定参数
    s_CalibConfig m_calib;
    // 时间参数
    s_TimeConfig m_time;
    // agv通信参数
    s_CommunicateConfig m_agv;
    // web通信参数
    s_WebCfg m_web;
    // pos校验
    s_PoseCheckConfig m_posCheck;
    // 地图参数
    s_MapConfig m_map;
    // 位置参数
    s_PoseConfig m_pos;
    // 预处理参数
    s_PreprocConfig m_prep;
    // 里程计参数
    s_OdomConfig m_odom;
    // 定位参数
    s_LoctConfig m_loct;
    // 建图参数
    s_MappingConfig m_slam;
    // 外置速度参数
    s_VelConfig m_vel;
    // FAE参数
    s_FaeLogCfg m_fae;

    // 弃用字段
    bool m_bIsUseMark;  // 是否使用mark
    float m_fMarkSize;  // 靶标半径

    uint32_t m_iServerPort;            // 服务器端口
    uint32_t m_iWarnPort;              // 报警交互端口
    uint m_iInitGroupID;               //  程序上电自动检测某个区域组
    AreaTablePtr m_sGroupScaleTable_;  // 区域刻度表 以组为单位

    SYSPARAMOD()
        : m_lidar(std::vector<s_LidarConfig>(0)), m_calib(), m_time(), m_agv(), m_web(),
          m_posCheck(), m_map(), m_pos(), m_prep(), m_odom(), m_loct(), m_slam(), m_vel()
    {
        m_iWorkMode = 0;

        m_nParam_mode = 0;
        m_bIsOnlineMode = true;  // 默认在线
        m_bDebugModel = false;
        m_iLogLevel = 0;

        m_RunStatus = PROCSTATE::NOSTART;
        m_bSendCurPC = false;
        m_bSendMap = false;
        m_iViewMode = 0;

        m_sPkgPath = "";
        m_sLogPath = "";
        m_sOdVersion = "";
        m_iLidarNum = 1;

        m_bIsUseMark = false;
        m_fMarkSize = 0.04;

        m_iServerPort = 4112;
        m_iWarnPort = 7070;
        m_iInitGroupID = 1;
        m_sGroupScaleTable_.reset(new AreaScaleTable());
    }
    static SYSPARAMOD* getIn()
    {
        static SYSPARAMOD firstInit;
        return &firstInit;
    }
    void printf()
    {
        // if (this->m_bDebugModel)
        // {
        //     LOGW(WINFO, "[param] WorkMode {}", this->m_WorkMode[m_iWorkMode]);
        //     LOGW(WINFO, "[param] OnlineMode", this->m_bIsOnlineMode);
        //     LOGW(WINFO, "[param] ParamMode {}", this->m_ParamMode[m_nParam_mode]);
        //     LOGW(WINFO, "[param] LogLevel {}", this->m_LogLevel[m_iLogLevel]);
        //     LOGW(WINFO, "[param] DebugModel {}", this->m_bDebugModel);

        //     LOGW(WINFO, "[param] PkgPath {}", this->m_sPkgPath);
        //     LOGW(WINFO, "[param] LogPath {}", this->m_sLogPath);
        //     LOGW(WINFO, "[param] Lidar Number {}", this->m_iLidarNum);

        //     LOGW(WINFO, "[param] SendCurPC {}", this->m_bSendCurPC);
        //     LOGW(WINFO, "[param] SendMap {}", this->m_bSendMap);
        //     LOGW(WINFO, "[param] Map ViewMode {}", m_iViewMode);

        //     this->m_map.log();
        //     this->m_slam.log();
        //     this->m_loct.log();
        //     this->m_odom.log();
        //     this->m_pos.log();
        // }
        // for (u_int l_iLd = 0; l_iLd < m_lidar.size(); ++l_iLd)
        //     this->m_lidar[l_iLd].log();
        // this->m_agv.log();
    }
};

}  // namespace wj_slam
