/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2021-05-16 16:14:43
 * @LastEditTime: 2022-12-28 18:42:18
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @Description: In User Settings Edit
 * @FilePath: /720slam/src/wanji_slam/include/net_app/netApp.h
 */
#pragma once
#include "protocol.h"
#include <arpa/inet.h>
#include <boost/function.hpp>
#include <errno.h>
#include <fcntl.h>
#include <iostream>
#include <netdb.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>

class WJNetAppOD {
  public:
    typedef boost::shared_ptr<WJNetAppOD> Ptr;

  private:
    // boost::function<void(int&, std::vector<int>&)> c_actionCallback_;
    boost::function<void(int, std::vector<int>&)> c_actionCallback_;
    struct tcp_info c_stConnectInfo_;
    int c_iConnectInfoLen_ = sizeof(struct tcp_info);
    int c_iSockFd_ = -1, c_iNewFd_ = -1;
    uint32_t c_uiHasRecvFlag_;
    bool c_bRun_ = true;
    bool c_bNetStatus_;
    bool c_bAcceptStatus = false;
    bool c_bProcThrRun_ = false;
    bool c_bProcThrRunOver_ = true;
    ProtocolOD* c_pProc_ = NULL;
    struct sockaddr_in c_stServerAddr_;
    struct sockaddr_in c_stClientAddr_;
    s_NetMsg* c_pstNetMsg_ = NULL;
    std::string c_sPrintfStr = "";

    u_char c_warnProtocol_[34] = {
        0xFF, 0xAA, 0x00, 0x1E,  // 除去帧头帧尾数据长30
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x02, 0x02, 0x0B, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x01, 0x00, 0x00,
        0x00,        // 26 GroupID
        0x00,        // 27 AreaID
        0x00, 0x00,  // 28-29有无雷达数据标志位
        0x00, 0x00,  // 30-31校验位
        0xEE, 0xEE   // 32-33帧尾
    };

    void procMsg_();
    void sendMsg_(char* buf, int len);
    void fakeConnect(int& p_iFd);
    void sendActionCMD(int p_iActionCMD, std::vector<int>& p_vAreaCfgArr);
    /**
     * @description: 连接TCP服务器 3s未连接则认为失败
     * @param {int&} p_iFd 网络句柄 须已建立socket
     * @param {sockaddr_in&} p_sRemoteAddr 服务器地址
     * @return {bool} TCP服务器连接成功
     * @other:
     */
    bool connectTcpServer_(int& p_iFd, sockaddr_in& p_sMyAddr, sockaddr_in& p_sRemoteAddr);

  public:
    WJNetAppOD(uint32_t p_uiPort, boost::function<void(int, std::vector<int>&)> p_actionCallback);
    void start(int p_iPort);
    void shutDown();
    void fillInWarnCfg(int p_iIdx, std::vector<int>& p_vbWarnFlag);
    void sendInvalidDataFlag(int p_iIdx);
    ~WJNetAppOD();
};
