/*
 * @Description:
 * @Version: 1.0
 * @Autor: Do not edit
 * @Date: 2021-11-15 14:43:25
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-28 17:55:10
 */
#pragma once
#include "algorithm/optimize/laserTransform.h"
#include "common/common_ex.h"
#include <deque>
#include <queue>
#include <vector>

#define SENDID
namespace wj_slam {

class TimeSync {
  private:
    typedef typename pcl::PointXYZHSV P;
    typedef typename FEATURE_PAIR<P>::Ptr FeaturePairPtr;
    typedef pcl::PointCloud<P> Feature;
    typedef boost::shared_ptr<Feature> FeaturePtr;
    typedef std::vector<FeaturePairPtr> F_QUEUE;

  public:
    enum SYNC { UNSYNC = 0, SYNC = 1 };

    //  传感器同步数据
    typedef struct st_SyncDatas
    {
        FeaturePairPtr m_lidarFrame;
    } st_SyncDatas;

  private:
    //  传感器阵列
    typedef struct st_MultiSensorQueue
    {
        std::vector<int> m_SyncStat;
        std::vector<F_QUEUE> m_lidars;
        void reset(const int p_iSize)
        {
            m_SyncStat = std::vector<int>(p_iSize, SYNC::SYNC);
            m_lidars.resize(p_iSize);
        }
        int getSyncStatus()
        {
            for (int i = 0; i < (int)m_SyncStat.size(); ++i)
                if (m_SyncStat[i] == SYNC::UNSYNC)
                    return SYNC::UNSYNC;
            return SYNC::SYNC;
        }
    } st_MultiSensorQueue;

    // 传感器数据集合
    st_MultiSensorQueue c_stQues_;
    // 最大允许落后时间
    int c_iMaxTimeBehind_;
    // 最大允许同步时间
    int c_iMaxTimeSync_;
    // 最大允许发送延时时间
    int c_iMaxTimeDelay_;
    // 上次发送时间
    int c_iLastSendTime_;
    // 累计帧号
    int c_iFrameNo_;
    // 点的时间字段偏移
    int TIME_FIELDS_OFFSET;
    // 点的雷达ID字段偏移
    int ID_FIELDS_OFFSET;
    // 系统参数
    SYSPARAMOD* c_stSysParam_;
    std::mutex syncLock;
    boost::function<void(FeaturePairPtr&)> c_SyncOutputCb_;  // 同步完毕回调

  public:
    TimeSync(boost::function<void(FeaturePairPtr&)> p_SyncOutputCb)
        : c_SyncOutputCb_(p_SyncOutputCb)
    {
        c_stSysParam_ = SYSPARAMOD::getIn();
        c_iMaxTimeBehind_ = c_stSysParam_->m_prep.m_sync.m_iMaxTimeBehind;
        c_iMaxTimeSync_ = c_stSysParam_->m_prep.m_sync.m_iMaxTimeSync;
        c_iMaxTimeDelay_ = c_stSysParam_->m_prep.m_sync.m_iMaxTimeDelay;
        c_iFrameNo_ = 0;
        c_iLastSendTime_ = -1;
        TIME_FIELDS_OFFSET = 5;
        ID_FIELDS_OFFSET = 6;
        c_stQues_.reset(c_stSysParam_->m_iLidarNum);
    }
    ~TimeSync()
    {
        c_iFrameNo_ = 0;
        c_iLastSendTime_ = -1;
        for (int i = 0; i < (int)c_stQues_.m_lidars.size(); ++i)
        {
            c_stQues_.m_lidars[i].resize(0);
        }
        c_stQues_.reset(0);
    }

    /**
     * @description: 设置时间偏移量在点索引中的位置（float*）
     * @param {int} p_iTimeOffset 偏移N个float数
     * @return {*}
     * @other:
     */
    void setTimeOffset(int p_iTimeOffset)
    {
        TIME_FIELDS_OFFSET = p_iTimeOffset;
    }
    /**
     * @description: 设置时间偏移量在点索引中的位置（float*）
     * @param {int} p_iTimeOffset 偏移N个float数
     * @return {*}
     * @other:
     */
    void setIDOffset(int p_iIDOffset)
    {
        ID_FIELDS_OFFSET = p_iIDOffset;
    }

    /**
     * @description: 设置允许的最大同步等待时间，超过后会立即发送最新点云
     * @param {int} p_iTimeOffset 偏移N个float数
     * @return {*}
     * @other:
     */
    void setTimeWaitForSync(int p_iMaxWaitMs)
    {
        c_iMaxTimeDelay_ = p_iMaxWaitMs;
    }

    void syncInputCallBack(int p_iLaserId, FeaturePairPtr& featurePair)
    {
        syncLock.lock();
        setNewLidarFrame(p_iLaserId, featurePair);
        st_SyncDatas l_tempData;
        if (getSyncData(l_tempData))
        {
            c_SyncOutputCb_(l_tempData.m_lidarFrame);
        }
        syncLock.unlock();
    }

    /**
     * @description: 向队列中添加新的雷达帧
     * @param {u_int} p_uiID 雷达序号
     * @param {FeaturePairPtr&} p_pc 雷达帧
     * @return {*}
     * @other:
     */
    void setNewLidarFrame(u_int p_uiID, FeaturePairPtr& p_pc)
    {
        // LOGA(WDEBUG, "get [{}] at {}ms recvT {} ms.", p_uiID, p_pc->m_dTimestamp,
        // p_pc->m_iRecvTimestamp);
        c_stQues_.m_lidars[p_uiID].push_back(p_pc);
        // 当队列中存在过多数据，进行释放
        if (c_stQues_.m_lidars[p_uiID].size() > 4)
        {
            LOGA(WDEBUG,
                 "pop [{}] at {}ms.",
                 p_uiID,
                 c_stQues_.m_lidars[p_uiID].front()->m_dTimestamp);
            c_stQues_.m_lidars[p_uiID].erase(c_stQues_.m_lidars[p_uiID].begin());
        }
    }

    /**
     * @description: 获取已同步的合成帧
     * @param {st_SyncDatas&} p_SyncDatas 合成数据结构
     * @return {bool} 是否完成同步
     * @other:
     */
    bool getSyncData(st_SyncDatas& p_SyncDatas)
    {
        std::vector<FeaturePairPtr> l_lidarDatas(c_stQues_.m_lidars.size(), nullptr);
        if (anyLidarQueueNotEmpty(c_stQues_.m_lidars))
        {
            if (getLidarFrames(l_lidarDatas))
            {
                p_SyncDatas.m_lidarFrame.reset(new FEATURE_PAIR<P>());
                int l_iTimeStamp, l_iRecvTimeStamp, l_iTimeSpan;
                mergLidars(l_lidarDatas,
                           l_iTimeStamp,
                           l_iRecvTimeStamp,
                           l_iTimeSpan,
                           p_SyncDatas.m_lidarFrame);
                // LOGA(WDEBUG,"release one Merg-Frame.");
                // 写入时间戳
                p_SyncDatas.m_lidarFrame->m_iRecvTimestamp = l_iRecvTimeStamp;
                p_SyncDatas.m_lidarFrame->m_dTimestamp = l_iTimeStamp;
                c_iLastSendTime_ = l_iTimeStamp;
                p_SyncDatas.m_lidarFrame->m_dTimespan = l_iTimeSpan;
                // 写入帧号
                p_SyncDatas.m_lidarFrame->m_uiScanFrame = c_iFrameNo_;
                LOGA(WDEBUG,
                     "release [{}]-s{} at {}ms with {}ms | lidarRecvT: {} ms ",
                     p_SyncDatas.m_lidarFrame->m_uiScanFrame,
                     c_stQues_.getSyncStatus(),
                     p_SyncDatas.m_lidarFrame->m_dTimestamp,
                     p_SyncDatas.m_lidarFrame->m_dTimespan,
                     p_SyncDatas.m_lidarFrame->m_iRecvTimestamp);
                c_iFrameNo_++;
                return true;
            }
        }
        return false;
    }

  private:
    /**
     * @description: 判断所有雷达队列中是否存在空队列
     * @param {std::vector<F_QUEUE>&} p_lidarQue 雷达队列的序列
     * @return {bool} 是否存在空队列
     * @other:
     */
    bool anyLidarQueueEmpty(std::vector<F_QUEUE>& p_lidarQue)
    {
        for (int j = 0; j < (int)p_lidarQue.size(); ++j)
        {
            if (p_lidarQue[j].empty())
            {
                return true;
            }
        }
        return false;
    }
    /**
     * @description: 判断所有雷达队列中是否存在非空队列
     * @param {std::vector<F_QUEUE>&} p_lidarQue 雷达队列的序列
     * @return {bool} 是否存在空队列
     * @other:
     */
    bool anyLidarQueueNotEmpty(std::vector<F_QUEUE>& p_lidarQue)
    {
        for (int j = 0; j < (int)p_lidarQue.size(); ++j)
        {
            if (!p_lidarQue[j].empty())
            {
                return true;
            }
        }
        return false;
    }
    /**
     * @description: 获取全部雷达队列最新帧中时间戳最新者
     * @param {std::vector<F_QUEUE>&} p_lidarQue 雷达队列的序列
     * @param {int&} p_iBaseQue 最大时间戳所在雷达队列的序号
     * @return {int} 最大时间戳（最新）
     * @other:
     */
    int getLatestTimeOfLidarQueFront(std::vector<F_QUEUE>& p_lidarQue, int& p_iBaseQue)
    {
        int l_iLatestTime = INT_MIN;
        // 最新帧中时间戳最新者
        for (int i = 0; i < (int)p_lidarQue.size(); ++i)
        {
            if (!p_lidarQue[i].empty())
            {
                if (p_lidarQue[i].back()->m_dTimestamp > l_iLatestTime)
                {
                    l_iLatestTime = p_lidarQue[i].back()->m_dTimestamp;
                    p_iBaseQue = i;
                }
            }
        }
        // LOGA(WDEBUG,"queue-{} is front-latest at {} ms.", p_iBaseQue, l_iLatestTime);
        return l_iLatestTime;
    }

    /**
     * @description: 使用基准时间戳搜索附近时间范围内每个队列中的帧，若帧的时间戳过旧则抛弃
     * @param {std::vector<F_QUEUE>&} p_lidarQue 雷达队列的序列
     * @param {int&} p_iTimeStamp 基准时间戳
     * @param {int&} p_iBaseQue 基准时间戳所在雷达队列序号
     * @param {std::vector<FeaturePairPtr>&} p_lidarQue 输出帧序列
     * @return {bool} 基准时间戳是否远小于某一队列的最旧时间戳
     * @other:
     */
    bool getFramesAtTime(std::vector<F_QUEUE>& p_lidarQue,
                         int p_iTimeStamp,
                         int p_iBaseQue,
                         std::vector<FeaturePairPtr>& p_frames)
    {
        // 其他队列的帧和时间差
        FeaturePairPtr l_fp = nullptr;
        int l_iTimeDiff = 0;
        // 基准自己无需重新判定
        p_frames[p_iBaseQue] = p_lidarQue[p_iBaseQue].back();
        // 对每个雷达队列
        for (int i = 0; i < (int)p_lidarQue.size(); ++i)
        {
            if (i == p_iBaseQue)
                continue;
            // 从较新的开始
            for (int j = p_lidarQue[i].size() - 1; j >= 0; j--)
            {
                l_fp = p_lidarQue[i][j];
                l_iTimeDiff = l_fp->m_dTimestamp - p_iTimeStamp;
                // 如果时间符合区间限制则加入缓存
                if (l_iTimeDiff > c_iMaxTimeBehind_)
                {
                    p_frames[i] = l_fp;
                    // 终止这一队列内循环
                    break;
                }
            }
        }
        return true;
    }

    bool SyncSendAndRecvBuff(std::vector<F_QUEUE>& p_lidarQue,
                             int p_iTimeStamp,
                             int p_iBaseQue,
                             std::vector<FeaturePairPtr>& p_frames)
    {
        // 如果某个为空则表示同步不成功
        std::vector<int> l_iSyncLidar;
        std::vector<int> l_iUnSyncLidar;
        for (int i = 0; i < (int)p_frames.size(); ++i)
        {
            if (p_frames[i] == nullptr)
            {
                l_iUnSyncLidar.push_back(i);
            }
            else
            {
                l_iSyncLidar.push_back(i);
            }
        }
        // 计算时间损失
        int l_iTimeLost = p_iTimeStamp - c_iLastSendTime_;
        // 如果全部同步成功，发送
        if (l_iSyncLidar.size() == p_lidarQue.size())
        {
            // 更新同步状态
            for (int i = 0; i < (int)p_lidarQue.size(); ++i)
                c_stQues_.m_SyncStat[i] = SYNC::SYNC;
            // 清空接收缓存
            for (int i = 0; i < (int)p_lidarQue.size(); ++i)
                p_lidarQue[i].resize(0);
            return true;
        }
        // 如果不是第一帧
        // 如果未发送时间超过允许的跳跃时间限制，直接发送
        // 如果上次丢失同步状态的雷达仍未恢复,且已超过单次同步等待时间，直接发送
        else if (c_iLastSendTime_ > 0
                 && (l_iTimeLost > c_iMaxTimeDelay_
                     || (c_stQues_.getSyncStatus() == SYNC::UNSYNC
                         && l_iTimeLost > 2.1 * SCAN_TIME_MS)))
        {
            LOGA(WWARN, "sync jump {}ms", l_iTimeLost);
            // 更新同步状态
            for (int i = 0; i < (int)l_iUnSyncLidar.size(); ++i)
                c_stQues_.m_SyncStat[l_iUnSyncLidar[i]] = SYNC::UNSYNC;
            // 清空接收缓存
            for (int i = 0; i < (int)p_lidarQue.size(); ++i)
                p_lidarQue[i].resize(0);
            return true;
        }
        return false;
    }

    /**
     * @description: 尝试从雷达序列中捕获一组同步数据
     * @param {std::vector<FeaturePairPtr>&} p_lidarQue 输出帧序列
     * @return {bool} 是否搜索到一组可同步数据
     * @other:
     */
    bool getLidarFrames(std::vector<FeaturePairPtr>& p_frames)
    {
        // 基准时间戳,基准所在雷达队列序号
        int l_iTimeStamp = 0;
        int l_iBaseQueue = 0;
        // 获取雷达队列每队最新帧中最新的时间
        l_iTimeStamp = getLatestTimeOfLidarQueFront(c_stQues_.m_lidars, l_iBaseQueue);
        // 获取这个时间戳范围内的帧，抛弃更旧时间范围的帧
        // 尝试获取时间范围内的帧
        if (getFramesAtTime(c_stQues_.m_lidars, l_iTimeStamp, l_iBaseQueue, p_frames))
        {
            return SyncSendAndRecvBuff(c_stQues_.m_lidars, l_iTimeStamp, l_iBaseQueue, p_frames);
        }
        return false;
    }

    /**
     * @description:已一组可同步数据中最旧时间戳作为同步时间戳
     * @param {std::vector<FeaturePairPtr>&} p_lidarQue 帧序列
     * @param {int&} p_iTimeStamp 待填写的基准时间戳
     * @param {int&} p_iTimeSpan 待填写的合成时间跨度
     * @param {FeaturePairPtr&} p_mergFe 待填写的合成帧
     * @return {*}
     * @other:
     */
    void mergLidars(std::vector<FeaturePairPtr>& p_frames,
                    int& p_iTimeStamp,
                    int& p_iRecvTimeStamp,
                    int& p_iTimeSpan,
                    FeaturePairPtr& p_mergFe)
    {
        // 基准时间戳所在雷达序号
        int l_iBaseQueue = 0;
        // 设置时间基准为序列最旧时间戳
        lidarsTimeReBase(p_frames, p_iTimeStamp, p_iRecvTimeStamp, p_iTimeSpan, l_iBaseQueue);
        // 以时间基准起修改每个点的时间
        pointsTimeReBase(p_frames, p_iTimeStamp, l_iBaseQueue);
        // 转移每个雷达帧到统一坐标系
        // lidarsCoorRebase(p_frames);
        // 合成其他点云
        for (int i = 0; i < (int)p_frames.size(); ++i)
        {
            if (p_frames[i] == nullptr)
                continue;
            *p_mergFe->first += *p_frames[i]->first;
            *p_mergFe->third += *p_frames[i]->third;
            *p_mergFe->fourth += *p_frames[i]->fourth;
            *p_mergFe->allPC += *p_frames[i]->allPC;
        }
        // 重新排列面点和采样offset
        sample2ndArrangeMerg(p_frames, p_mergFe);
    }

    /**
     * @description: 寻找最旧时间戳并计算帧间时间跨度
     * @param {std::vector<FeaturePairPtr>&} p_lidarQue 帧序列
     * @param {int&} p_iTimeStamp 待填写的基准时间戳
     * @param {int&} p_iRecvTimeStamp 待填写的基准时间对应的雷达接收时间
     * @param {int&} p_iTimeSpan 待填写的合成时间跨度
     * @param {int&} p_iBaseQueue 待填写的基准时间戳所在序列号
     * @return {*}
     * @other:
     */
    void lidarsTimeReBase(std::vector<FeaturePairPtr>& p_frames,
                          int& p_iTimeStamp,
                          int& p_iRecvTimeStamp,
                          int& p_iTimeSpan,
                          int& p_iBaseQueue)
    {
        // 初始化时间戳为最大值
        p_iTimeStamp = INT_MAX;
        int l_iTimeEndFrame = INT_MIN;
        // 计算同步帧的最旧时间
        for (int i = 0; i < (int)p_frames.size(); ++i)
        {
            if (p_frames[i] == nullptr)
                continue;
            if (p_frames[i]->m_dTimestamp <= p_iTimeStamp)
            {
                p_iTimeStamp = p_frames[i]->m_dTimestamp;
                p_iRecvTimeStamp = p_frames[i]->m_iRecvTimestamp;
                p_iBaseQueue = i;
            }
            if (p_frames[i]->m_dTimestamp >= l_iTimeEndFrame)
            {
                l_iTimeEndFrame = p_frames[i]->m_dTimestamp;
            }
        }
        // 计算最大时间间隔(首帧起点到最后一帧终点)
        p_iTimeSpan = l_iTimeEndFrame + SCAN_TIME_MS - p_iTimeStamp;
        // LOGA(WDEBUG,"rebase to lidar-{}'s time.", p_iBaseQueue);
    }

    /**
     * @description: 修正全部雷达帧的时间偏移到基准时间戳
     * @param {std::vector<FeaturePairPtr>&} p_lidarQue 帧序列
     * @param {int&} p_iTimeStamp 基准时间戳
     * @param {int&} p_iBaseQueue 基准时间戳所在序列号
     * @return {*}
     * @other:
     */
    void pointsTimeReBase(std::vector<FeaturePairPtr>& p_frames, int p_iTimeStamp, int p_iBaseQueue)
    {
        for (int i = 0; i < (int)p_frames.size(); ++i)
        {
            if (p_frames[i] == nullptr)
                continue;
#ifdef SENDID
            // 转移ID号用于畸变矫正
            resetFPairID(p_frames[i], i);
#endif
            // 时间基准帧不需要计算偏移
            if (i == p_iBaseQueue)
                continue;
            // 计算毫秒单位下的时间偏移量
            float l_fTimeDiff = p_frames[i]->m_dTimestamp - p_iTimeStamp;
            // 刷新时间偏移量
            rebaseFPairTime(p_frames[i], l_fTimeDiff);
        }
    }

    /**
     * @description: 获取点的时间字段指针
     * @param {P&} p_Pnt 点
     * @return {*}
     * @other:
     */
    inline float* ptime(P& p_Pnt)
    {
        return reinterpret_cast<float*>(&p_Pnt) + TIME_FIELDS_OFFSET;
    }

    /**
     * @description: 获取点的雷达ID字段指针
     * @param {P&} p_Pnt 点
     * @return {*}
     * @other:
     */
    inline float* pID(P& p_Pnt)
    {
        return reinterpret_cast<float*>(&p_Pnt) + ID_FIELDS_OFFSET;
    }

    /**
     * @description: 修正帧的时间偏移到基准时间戳
     * @param {FeaturePairPtr} p_pPc 帧
     * @param {float} p_fTimeDiff 时间偏移
     * @return {*}
     * @other:
     */
    void rebaseFPairTime(FeaturePairPtr p_pPc, float p_fTimeDiff)
    {
        rebaseCloudTime(p_pPc->first, p_fTimeDiff);
        rebaseCloudTime(p_pPc->second, p_fTimeDiff);
        rebaseCloudTime(p_pPc->fourth, p_fTimeDiff);
        rebaseCloudTime(p_pPc->allPC, p_fTimeDiff);
    }

    /**
     * @description: 修正点云的时间偏移到基准时间戳
     * @param {FeaturePtr} p_pPc 点云
     * @param {float} p_fTimeDiff 时间偏移
     * @return {*}
     * @other:
     */
    void rebaseCloudTime(FeaturePtr p_pPc, float p_fTimeDiff)
    {
        u_int l_iCldSize = p_pPc->size();
        for (u_int i = 0; i < l_iCldSize; ++i)
            *(ptime(p_pPc->points[i])) += p_fTimeDiff;
    }

    /**
     * @description: 记录点云的雷达ID
     * @param {FeaturePairPtr} p_pPc 帧
     * @param {float} p_fID 雷达ID
     * @return {*}
     * @other:
     */
    void resetFPairID(FeaturePairPtr p_pPc, float p_fID)
    {
        resetCloudID(p_pPc->allPC, p_fID);
    }

    /**
     * @description: 记录点云的雷达ID
     * @param {FeaturePtr} p_pPc 点云
     * @param {float} p_fID 雷达ID
     * @return {*}
     * @other:
     */
    void resetCloudID(FeaturePtr p_pPc, float p_fID)
    {
        u_int l_iCldSize = p_pPc->size();
        for (u_int i = 0; i < l_iCldSize; ++i)
            *(pID(p_pPc->points[i])) = p_fID;
    }

    /**
     * @description: 合成帧中的面点由于采样排序，需要将每个帧的采样点移到非采样点前
     * @param {std::vector<FeaturePairPtr>&} p_lidarQue 待合成帧序列
     * @param {FeaturePairPtr&} p_mergFe 合成帧
     * @return {*}
     * @other:
     */
    void sample2ndArrangeMerg(std::vector<FeaturePairPtr>& p_frames, FeaturePairPtr& p_mergFe)
    {
        FeaturePtr l_pPcS, l_pMergS;
        l_pMergS = p_mergFe->second;
        int l_iSampleSize = 0;
        int l_iMergSampleSize = 0;
        for (int i = 0; i < (int)p_frames.size(); ++i)
        {
            if (p_frames[i] == nullptr)
                continue;
            l_pPcS = p_frames[i]->second;
            l_iSampleSize = p_frames[i]->m_iSample2ndSize;
            // 在上个采样点序列后插入新采样点
            l_pMergS->insert(l_pMergS->begin() + l_iMergSampleSize,
                             l_pPcS->begin(),
                             l_pPcS->begin() + l_iSampleSize);
            // 更新采样点序列长度
            l_iMergSampleSize += l_iSampleSize;
            // 在尾部插入非采样点
            l_pMergS->insert(l_pMergS->end(), l_pPcS->begin() + l_iSampleSize, l_pPcS->end());
            // LOGA(WDEBUG,"merg lidar-{}'s 2ndPC to offset {}.", i, l_iMergSampleSize);
        }
        p_mergFe->m_iSample2ndSize = l_iMergSampleSize;
    }

};  // class TimeSync
}  // namespace wj_slam