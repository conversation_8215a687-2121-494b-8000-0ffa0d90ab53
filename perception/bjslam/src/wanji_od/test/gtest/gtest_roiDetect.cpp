#include "algorithm/roi_detect/roiDetect.h"

int c_iValidNum_ = 2;

bool isPointExist(int p_iPointNum)
{
    return p_iPointNum >= c_iValidNum_;
}

bool twoRowThreeColDetect(int row, int col, PcMatrix& p_pcMat)
{
    int c_iContinousNum_ = 6;
    Eigen::Matrix<int, 2, 3> pcBlock = p_pcMat.block<2, 3>(row - 1, col - 2);
    int l_iPointNum = pcBlock.sum();
    std::cout << l_iPointNum << std::endl;
    return l_iPointNum > c_iContinousNum_;
}

// 测试是否存在有效障碍点数
TEST(StaticAnalysisTest, isPointExist)
{
    int num = 1;
    EXPECT_TRUE(isPointExist(num));
}

// 测试是否存在有效障碍点数
TEST(StaticAnalysisTest, twoRowThreeColDetect)
{
    PcMatrix pc;
    pc.setOnes();
    std::cout << pc(0, 1) << std::endl;
    EXPECT_TRUE(twoRowThreeColDetect(2, 3, pc));
}

int main(int argc, char** argv)
{
#ifdef GTEST
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
#endif
    return 0;
}