/*
 * @Description: 
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2022-12-28 11:17:02
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-28 14:04:15
 */
#include "algorithm/coord_solution/coordSolution.h"

namespace wj_od {
/**
 * @description: 要使用测试fixture，请从testing:: test派生一个类
 * @note: 每个TEST_F使用同一实例的副本，任何改变都不传递到其他测试用例
 */
class StaticAnalysisTest : public testing::Test {
    // 让成员受保护，因此它们可以从子类访问
  protected:
    /**
     * @description: SetUp()将在每个TEST_F测试运行前自动调用。
     * @note: 如果需要初始化变量，重载该函数。
     */
    void SetUp() override
    {
        // 使用系统默认参数初始
    }
    /**
     * @description: TearDown()将在每个TEST_F测试运行后自动调用。
     * @note: 如果需要清理工作，重载该函数。
     */
    void TearDown() override
    {
        // c_pSa_ = nullptr;
    }
    CoordSolution::Ptr c_coordSolution_;
};

// 测试适配雷达水平角分辨率
// 需要雷达扫描起始角向最近的0.2度靠近
TEST_F(StaticAnalysisTest, matchLidarResolution)
{
    PXYZI A;
    A.intensity = 100.35;
    PXYZI* ptrA = &A;
    double lidarHAngle = c_coordSolution_->matchLidarResolution(ptrA);
    EXPECT_EQ(lidarHAngle, 100.4);
    EXPECT_TRUE(c_coordSolution_->floatCompare(lidarHAngle, 100.4));
}

// 测试arccos值返回的角度是否符合预期
TEST_F(StaticAnalysisTest, getCosDegree)
{
    double angle = c_coordSolution_->getCosDegree(0.5);
    // EXPECT_EQ(angle, 60);
    EXPECT_TRUE(c_coordSolution_->floatCompare(angle, 60));
}

// // 测试雷达原点是否在区域内
// TEST_F(StaticAnalysisTest, isOriginInArea)
// {
//     CoordSolution::s_Area area;
//     area.polygon.vNum = 3;
//     PXYZI A;
//     PXYZI B;
//     PXYZI C;

//     A.x = -2;
//     A.y = -2;
//     c_coordSolution_->calculateVertexInfo(A);
//     area.polygon.vertex->push_back(A);

//     B.x = 2;
//     B.y = -2;
//     c_coordSolution_->calculateVertexInfo(B);
//     area.polygon.vertex->push_back(B);

//     C.x = 0;
//     C.y = 2;
//     c_coordSolution_->calculateVertexInfo(C);
//     area.polygon.vertex->push_back(C);

//     EXPECT_TRUE(c_coordSolution_->isOriginInArea(area));
// }

// 测试雷达原点是否在区域内
TEST_F(StaticAnalysisTest, isOriginInArea)
{
    CoordSolution::s_Area area;
    area.polygon.vNum = 4;
    PXYZI A;
    PXYZI B;
    PXYZI C;
    PXYZI D;

    A.x = 0;
    A.y = 2;
    c_coordSolution_->calculateVertexInfo(A);

    B.x = 0;
    B.y = -2;
    c_coordSolution_->calculateVertexInfo(B);

    C.x = -2;
    C.y = -2;
    c_coordSolution_->calculateVertexInfo(C);

    D.x = -2;
    D.y = 2;
    c_coordSolution_->calculateVertexInfo(D);

    area.polygon.vertex->push_back(B);
    area.polygon.vertex->push_back(C);
    area.polygon.vertex->push_back(D);
    area.polygon.vertex->push_back(A);
    EXPECT_TRUE(c_coordSolution_->isOriginInArea(area));
}

// 测试计算雷达有效县束
TEST_F(StaticAnalysisTest, getValidScan)
{
    CoordSolution::s_Area area;
    area.polygon.vNum = 4;
    PXYZI A;
    PXYZI B;
    PXYZI C;
    PXYZI D;

    A.x = 0;
    A.y = 2;
    c_coordSolution_->calculateVertexInfo(A);

    B.x = 0;
    B.y = -2;
    c_coordSolution_->calculateVertexInfo(B);

    C.x = -2;
    C.y = -2;
    c_coordSolution_->calculateVertexInfo(C);

    D.x = -2;
    D.y = 2;
    c_coordSolution_->calculateVertexInfo(D);

    area.polygon.vertex->push_back(B);
    area.polygon.vertex->push_back(C);
    area.polygon.vertex->push_back(D);
    area.polygon.vertex->push_back(A);
    c_coordSolution_->c_fLidarHeight_ = 1.8;
    area.height = 1.8;
    ScaleTablePtr l_scaleTable;
    c_coordSolution_->getValidScan(area, l_scaleTable);
    for (int i = 9; i < WLR720_SCANS_PER_FIRING; i++)
    {
        EXPECT_TRUE(l_scaleTable->scan[8]);
        EXPECT_FALSE(l_scaleTable->scan[i]);
    }
}

}  // namespace wj_od

int main(int argc, char** argv)
{
#ifdef GTEST
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
#endif
    return 0;
}