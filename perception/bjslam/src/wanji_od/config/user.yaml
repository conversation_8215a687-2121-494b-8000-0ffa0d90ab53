LaserConfig:
    laser_lidarIp: ************
    laser_pcIp: ************ # 88
    laser_sn: 33F        #雷达SN码 老9/12号机分别为 9old / 12old
    laser_rpm: 600
    laser_height: 1.4   #雷达安装高度
    use_floor: true     #是否使用地面
    min_distance: 0.5   #雷达最近距离
    max_distance: 70.0  #雷达最远距离
    lidar_data_path: /home/<USER>/main/SLAM/SLAM算法代码/catkin_wanji_od2/src/wanji_data/pcap/
    pcap_name: /home/<USER>/main/SLAM/SLAM算法代码/catkin_wanji_od2/src/wanji_data/pcap/kinglong   #car3_1120_300Auto_slam#pcap包名称 不带后缀
    bag_name: "empty"   #rosbag包名称 不带后缀
    blind0_startAng: 0  #盲区 角度
    blind0_endAng: 0
    blind1_startAng: 0
    blind1_endAng: 0
    blind2_startAng: 0
    blind2_endAng: 0
    blind3_startAng: 0
    blind3_endAng: 0

    work_mode: 0      #工作模式 0:standby 1:initialmap 2:contiunemap 3:location
    use_mark: false     #是否使用靶标
    mark_size: 0.04     #靶标尺寸半径
    param_mode: 2       #建图参数模式 0:indoor 1:outdoor
    enableLoop: false    #是否回环

    map_name: cdv15       #地图名 SLAM保存 / Locate 加载
    map_grid: 0.05      #地图下采样半径
    map_size: 40.0        #地图附近框半径
    
    send_map: true      #可4化地图
    send_point: true    #可视化雷达
    
    playLidarMode: 1   #雷达形式：0:online 1:pcap 2:rosbag

    serverPort: 2113
    warnPort: 7070
    groupID: 1
    
    # laserPose_x: 0.0    #初始位姿X 单位m
    # laserPose_y: 0.0   #初始位姿Y 单位m
    # laserPose_z: 0.0   #初始位姿Z 单位m
    # laserPose_a: 0.0    #初始位姿A 单位deg

    my_map_x: 0         #地图校正 
    my_map_y: 0
    my_map_a: 0

    to_map_x: 0
    to_map_y: 0
    to_map_a: 0

    agv_portnum: 2113         #定位输出 端口  
    agv_ip: *************      #定位输出网络 IP
    agv_mask: *************   #定位输出网络 子网掩码
    agv_gateway: ***********  #定位输出网络 网关
    agv_pcap_name: 10-6-4agv