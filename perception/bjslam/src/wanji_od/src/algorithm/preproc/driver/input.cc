/*
 * @Description: 基于UDP/TCP Socket/pcap获取原始数据
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2021-12-06 08:42:17
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-29 09:30:27
 */
#include "algorithm/preproc/driver/input.h"
#include <fstream>
#include <iomanip>
#include <iostream>
using namespace std;
namespace wanji_driverOD {

#pragma region 基类
Input::Input(uint32_t p_uiLidarId, boost::function<void(ScanBuffer)> p_scandataCb)
    : c_stSysParam_(wj_slam::SYSPARAMOD::getIn()),
      c_stLaserCfg_(c_stSysParam_->m_lidar[p_uiLidarId]), c_uiLidarId(p_uiLidarId),
      c_outScanDataCb(p_scandataCb)
{
    initScanBuffer(c_stLaserCfg_.m_uiFramePkgNum);
    // 本机地址 只用了端口
    memset(&c_myAddr_, 0, sizeof(c_myAddr_));
    c_myAddr_.sin_family = AF_INET;
    c_myAddr_.sin_port = htons(c_stLaserCfg_.m_dev.m_uiLocalPort);
    c_myAddr_.sin_addr.s_addr = INADDR_ANY;

    // 设备地址
    memset(&c_remoteAddr_, 0, sizeof(c_remoteAddr_));
    c_remoteAddr_.sin_family = AF_INET;
    c_remoteAddr_.sin_port = htons(c_stLaserCfg_.m_dev.m_uiDevPort);
    c_remoteAddr_.sin_addr.s_addr = inet_addr(c_stLaserCfg_.m_dev.m_sDevIP.c_str());

    // 套接字
    c_sockinf_.fd = -1;
    c_sockinf_.port = c_stLaserCfg_.m_dev.m_uiLocalPort;
    c_sockinf_.local = c_myAddr_;
    c_sockinf_.prev_serialnum = -1;

    c_bInitSuecss_ = inputInit_();
    if (c_bInitSuecss_)
    {
        c_bRun = true;
        // 创建接收线程
        std::thread recvThr(&Input::recvSocketData, this);
        recvThr.detach();
        // 创建解析线程
        std::thread parseThr(&Input::parseSocketData, this);
        parseThr.detach();
    }
}

Input::~Input()
{
    if (c_sockinf_.fd != -1)
    {
        (void)close(c_sockinf_.fd);
        c_sockinf_.fd = -1;
    }
    shutDown();
}

bool Input::inputInit_()
{
    return onlineInit_(c_sockinf_, c_myAddr_);
}

bool Input::onlineInit_(socket_info& inf, sockaddr_in& p_sMyAddr)
{
    // 建立套接字
    inf.fd = socket(PF_INET, SOCK_DGRAM, IPPROTO_UDP);
    if (inf.fd == -1)
    {
        inf.err_no = errno;
        LOGA(WERROR,
             "{} setup_udp_server: socket failed: {}",
             WJODLog::getWholeSysTime(),
             strerror(inf.err_no));
        return false;
    }

    // 设置UDP SOCKET接收缓冲区大小
    int rcvBufSize = 1024 * 1024 * 2;
    socklen_t optlen = sizeof(rcvBufSize);
    if (setsockopt(inf.fd, SOL_SOCKET, SO_RCVBUF, &rcvBufSize, optlen) < 0)
    {
        LOGA(WERROR, "{} setsockopt error=: {}", WJODLog::getWholeSysTime(), strerror(inf.err_no));
    }

    // 设置端口复用
    int l_iOpt = SOF_TIMESTAMPING_RX_SOFTWARE | SOF_TIMESTAMPING_SOFTWARE | 0;
    if (setsockopt(inf.fd, SOL_SOCKET, SO_TIMESTAMPING, &l_iOpt, sizeof(l_iOpt)))
    {
        inf.err_no = errno;
        LOGA(WERROR,
             "{} setup_udp_server: setsockopt failed: {}",
             WJODLog::getWholeSysTime(),
             strerror(inf.err_no));
        return false;
    }

    int l_iOpt2 = 1;
    if (setsockopt(inf.fd, SOL_SOCKET, SO_REUSEPORT, &l_iOpt2, sizeof(l_iOpt2)))
    {
        inf.err_no = errno;
        LOGA(WERROR,
             "{} setup_udp_server: setsockopt2 failed: {}",
             WJODLog::getWholeSysTime(),
             strerror(inf.err_no));
        return false;
    }

    // 绑定端口
    if (bind(inf.fd, (sockaddr*)&inf.local, sizeof(sockaddr)) == -1)
    {
        inf.err_no = errno;
        LOGA(WERROR,
             "{} setup_udp_server: bind failed: {}",
             WJODLog::getWholeSysTime(),
             strerror(inf.err_no));
        return false;
    }

    // 设置非阻塞方式
    if (fcntl(inf.fd, F_SETFL, O_NONBLOCK | FASYNC) < 0)
    {
        LOGA(WERROR, "{} setup_udp_server: non-block", WJODLog::getWholeSysTime());
        return false;
    }

    return true;
}

bool Input::isStart()
{
    return c_bInitSuecss_;
}

void Input::shutDown()
{
    c_bRun = false;
    while (1)
    {
        if (c_bParseThrOver && c_bRecvThrOver)
            break;
        usleep(1000);
    }
}

void Input::checkXOR_(uint8_t* p_cBuf, int p_iSize)
{
    uint8_t check = 0x00;  // 用于保存异或结果
    for (int i = 2; i < p_iSize - 4; i++)
        check ^= *(p_cBuf + i);

    *(p_cBuf + p_iSize - 4) = 0x00;
    *(p_cBuf + p_iSize - 3) = check;
}

int Input::sendPacket(uint8_t* p_cBuf, const int p_iSize)
{
    checkXOR_(p_cBuf, p_iSize);
    const uint8_t* l_sendBuf = p_cBuf;

    if (sendto(
            c_sockinf_.fd, l_sendBuf, p_iSize, 0, (sockaddr*)&c_remoteAddr_, sizeof(c_remoteAddr_))
        == -1)
    {
        LOGA(WWARN,
             "{} lidar [{}] sendto error: {}",
             WJODLog::getWholeSysTime(),
             c_stLaserCfg_.m_sLaserName,
             strerror(errno));
        return -1;
    }
    return 0;
}

void Input::parseSocketData()
{
    c_bParseThrOver = false;
    uint8_t* buff = new uint8_t[TEMP_BUFFER_SIZE];
    uint16_t l_uiReadBuffLen = 0;
    uint16_t l_uiParsedIdx = 0;
    uint16_t parsedLen = 0;
    while (1)
    {
        if (!c_bRun)
            break;
        l_uiParsedIdx = 0;
        l_uiReadBuffLen = m_recvBuffer.Data(buff, TEMP_BUFFER_SIZE);
        while (l_uiReadBuffLen > MIN_PROTOCOL_SIZE)
        {
            uint16_t header = (buff[l_uiParsedIdx] << 8) | buff[l_uiParsedIdx + 1];
            if (header == 0xFFAA)
            {
                uint16_t frameLen =
                    ((buff[l_uiParsedIdx + 2] << 8) | buff[l_uiParsedIdx + 3]) + 4;  // FFAA 帧长
                if (l_uiReadBuffLen < frameLen)
                    break;
                parseLidarParam(&buff[l_uiParsedIdx], frameLen);
                l_uiParsedIdx += frameLen;
                l_uiReadBuffLen -= frameLen;
            }
            else if (header == 0xFFEE)
            {
                if (l_uiReadBuffLen < SCAN_SIZE_720_FE)  // 扫描数据帧长
                    break;
                parseScanData(&buff[l_uiParsedIdx], SCAN_SIZE_720_FE);
                l_uiParsedIdx += SCAN_SIZE_720_FE;
                l_uiReadBuffLen -= SCAN_SIZE_720_FE;
            }
            else if (header == 0xFFDD)
            {
                uint8_t echoNum = buff[l_uiParsedIdx + 3];  // 回波类型
                uint16_t frameLen =
                    (echoNum == 1) ? SCAN_SIZE_720_FD_1 : SCAN_SIZE_720_FD_2;  // FFDD 帧长
                if (l_uiReadBuffLen < frameLen)
                    break;
                parseScanData(&buff[l_uiParsedIdx], frameLen);
                l_uiParsedIdx += frameLen;
                l_uiReadBuffLen -= frameLen;
            }
            else
            {
                l_uiParsedIdx++;
                l_uiReadBuffLen--;
            }
        }
        parsedLen = l_uiParsedIdx;
        m_recvBuffer.Pop(parsedLen);
        usleep(10);
    }

    delete[] buff;
    c_bParseThrOver = true;
}

void Input::parseLidarParam(const uint8_t* data, uint16_t len)
{
    if (!data)
        return;

    bool gotSn = false;
    bool gotDevParam = false;
    bool gotAngle = false;

    uint16_t cmd = (data[22] << 8) | data[23];
    switch (cmd)
    {
        case GET_SN: break;
        case GET_BTN_TEMP: break;
        case GET_VERTICAL_AG: break;
        case GET_DEV_PARAM: break;
        default: break;
    }
}

void Input::parseScanData(const uint8_t* data, uint16_t len)
{
    if ((!data) || (!c_bInitSuecss_) || (len < MIN_PROTOCOL_SIZE))
        return;

    uint16_t header = (data[0] << 8) | data[1];
    uint16_t frameSize = (header == 0xFFEE)
                             ? SCAN_SIZE_720_FE
                             : ((data[3] == 1) ? SCAN_SIZE_720_FD_1 : SCAN_SIZE_720_FD_2);
    uint16_t angle = (data[3] << 8) | data[2];

    if (len < frameSize)
        return;

    uint16_t l_uiCurPktIdx = (data[frameSize - 4] << 8) | data[frameSize - 3];    // 扫描包序号
    uint16_t l_uiCircleIdx = (data[frameSize - 58] << 8) | data[frameSize - 57];  // 扫描圈号

    // 由于存在包乱序，一圈的首包数据不一定按序到达，故采取以下策略：
    // 以第一个具有完整包数的圈号作为预处理圈，用以确定采集的起始圈号及包号
    // 预处理圈的下一圈号作为采集起始圈号
    if (!c_bScanStartFlag)
    {
        // 到达采集圈号，开始正式采集
        if (m_scanBuffer1.m_iCircleNum == l_uiCircleIdx)
        {
            c_bScanStartFlag = true;
            int startIdx = c_iFirstPktIdx + c_stLaserCfg_.m_uiFramePkgNum;
            c_uiExpPktIdx = startIdx - 1;
            c_iCurBufferFlag = 1;

            m_scanBuffer1.m_startIdx = startIdx;
            m_scanBuffer2.m_iCircleNum = m_scanBuffer1.m_iCircleNum + 1;
            m_scanBuffer2.m_startIdx = m_scanBuffer1.m_startIdx + c_stLaserCfg_.m_uiFramePkgNum;

            LOGA(WINFO,
                 "[Lidar Driver] : Start Circle : {}; Start Packet : {}",
                 l_uiCircleIdx,
                 startIdx);
        }
        // 预处理圈号，确定该圈起始包号
        else if (c_iFirstCycleNum == l_uiCircleIdx)
        {
            // int idx = c_iFirstPktIdx;
            // int diff = abs(idx - l_uiCurPktIdx);
            // c_iFirstPktIdx = ((idx == -1) ? l_uiCurPktIdx : ((diff >=
            // c_stLaserCfg_.m_uiFramePkgNum) ? max<int>(idx, l_uiCurPktIdx) : min<int>(idx,
            // l_uiCurPktIdx)));

            if (c_iFirstPktIdx == -1)
                c_iFirstPktIdx = l_uiCurPktIdx;
            else
            {
                int diff = abs(c_iFirstPktIdx - l_uiCurPktIdx);
                c_iFirstPktIdx = (diff >= (int)c_stLaserCfg_.m_uiFramePkgNum)
                                     ? max<int>(c_iFirstPktIdx, l_uiCurPktIdx)
                                     : min<int>(c_iFirstPktIdx, l_uiCurPktIdx);
            }
            return;
        }
        // 确定预处理圈号及起始采集包号
        else
        {
            c_iFirstCycleNum = l_uiCircleIdx + 1;
            c_iFirstCycleNum %= 65536;
            m_scanBuffer1.m_iCircleNum = l_uiCircleIdx + 2;
            m_scanBuffer1.m_iCircleNum %= 65536;
            return;
        }
    }

    c_uiExpPktIdx++;
    // 乱序
    if (l_uiCurPktIdx != c_uiExpPktIdx)
    {
        // LOGA(WINFO, "[Lidar Driver] : Miss idx in circle , Circle : {}; Exp Packet : {};  Cur
        // Packet : {}", l_uiCircleIdx, c_uiExpPktIdx, l_uiCurPktIdx);
    }

    // 根据当前圈号选择存储Buffer
    ScanBuffer* buffer = nullptr;
    if (l_uiCircleIdx == m_scanBuffer1.m_iCircleNum)
    {
        buffer = &m_scanBuffer1;
    }
    else if (l_uiCircleIdx == m_scanBuffer2.m_iCircleNum)
    {
        buffer = &m_scanBuffer2;
    }
    else
    {
        int32_t tempIdx = (l_uiCircleIdx == 0) ? l_uiCircleIdx + 65536
                                               : l_uiCircleIdx;  // l_uiCircleIdx == 0时需特殊处理
        // 网络问题导致圈号不连续,当前接受圈号circleIdx若大于最新buffer圈号，则重新从circleIdx+1圈开始接受数据
        if (tempIdx
            > (c_iCurBufferFlag == 1 ? m_scanBuffer2.m_iCircleNum : m_scanBuffer1.m_iCircleNum))
        {
            LOGA(WINFO, "[Lidar Driver] : Circle number error : {}", l_uiCircleIdx);
            uint16_t circleDiff = l_uiCircleIdx + 1 - uint16_t(m_scanBuffer1.m_iCircleNum);
            uint16_t startIdx =
                m_scanBuffer1.m_startIdx + (circleDiff * c_stLaserCfg_.m_uiFramePkgNum);
            ResetScanBuffer(
                m_scanBuffer1, l_uiCircleIdx + 1, startIdx, c_stLaserCfg_.m_uiFramePkgNum);
            ResetScanBuffer(m_scanBuffer2,
                            l_uiCircleIdx + 2,
                            startIdx + c_stLaserCfg_.m_uiFramePkgNum,
                            c_stLaserCfg_.m_uiFramePkgNum);
            c_iCurBufferFlag = 1;
        }
        return;
    }

    // 确定当前包号在本圈中的序号，并将本包添加到对应序号的buffer中。
    uint16_t idx = l_uiCurPktIdx - buffer->m_startIdx;  // 本圈序号
    if (idx >= c_stLaserCfg_.m_uiFramePkgNum)
    {
        LOGA(WINFO, "[INPUT] : Packet number error.");
        return;
    }

    memcpy(buffer->m_data.m_vPackets[idx].m_data.c_array(), data, frameSize);  // 拷贝扫描数据
    buffer->m_data.m_vPackets[idx].m_stRecvTimestamp = wj_slam::getTimeNow();  // 获取当前时间戳
    buffer->m_frameCnt++;
    if (buffer->m_frameCnt == c_stLaserCfg_.m_uiFramePkgNum)  // 本圈收集完毕
    {
        buffer->m_bIsFull = true;
    }

    /**
     * 解决扫描数据跨圈乱序的问题:
     * 1. 若buffer1完整，则一圈收集完毕；
     * 2. 若buffer2已经收集了12(?)包，当前使用的仍然是buffer1(buffer1未full)，
     *    则认为丢包，仍然认为buffer1收集完毕。
     * 反之亦然。
     * **/
    if (m_scanBuffer1.m_bIsFull || (c_iCurBufferFlag == 1 && m_scanBuffer2.m_frameCnt > 10))
    {
        c_outScanDataCb(m_scanBuffer1);
        ResetScanBuffer(m_scanBuffer1,
                        (m_scanBuffer2.m_iCircleNum + 1) % 65536,
                        m_scanBuffer2.m_startIdx + c_stLaserCfg_.m_uiFramePkgNum,
                        c_stLaserCfg_.m_uiFramePkgNum);
        c_iCurBufferFlag = 2;
    }
    else if ((m_scanBuffer2.m_bIsFull || (c_iCurBufferFlag == 2 && m_scanBuffer1.m_frameCnt > 10)))
    {
        c_outScanDataCb(m_scanBuffer2);
        ResetScanBuffer(m_scanBuffer2,
                        (m_scanBuffer1.m_iCircleNum + 1) % 65536,
                        m_scanBuffer1.m_startIdx + c_stLaserCfg_.m_uiFramePkgNum,
                        c_stLaserCfg_.m_uiFramePkgNum);
        c_iCurBufferFlag = 1;
    }

    // 圈间乱序 打印
    // if (c_iCurBufferFlag == 1 && m_scanBuffer2.m_frameCnt > 0)
    // {
    //     LOGA(WINFO, "[Lidar Driver] Buffer1 miss Idx, Buffer1 Cnt : {}; Buffer2 Cnt : {}",
    //     m_scanBuffer1.m_frameCnt, m_scanBuffer2.m_frameCnt);
    // }
    // if (c_iCurBufferFlag == 2 && m_scanBuffer1.m_frameCnt > 0)
    // {
    //     LOGA(WINFO, "[Lidar Driver] Buffer2 miss Idx, Buffer2 Cnt : {}; Buffer1 Cnt: {}",
    //     m_scanBuffer2.m_frameCnt, m_scanBuffer1.m_frameCnt);
    // }
}

void Input::recvSocketData()
{
    c_bRecvThrOver = false;
    uint8_t* rbuf = new uint8_t[TEMP_BUFFER_SIZE];
    sockaddr_in sender_address;
    socklen_t sender_address_len = sizeof(sender_address);

    while (1)
    {
        if (!c_bRun)
            break;
        ssize_t nbytes = recvfrom(c_sockinf_.fd,
                                  rbuf,
                                  TEMP_BUFFER_SIZE,
                                  0,
                                  (sockaddr*)&sender_address,
                                  &sender_address_len);
        if (nbytes < 0)
        {
            if (errno != EWOULDBLOCK)
            {
                LOGA(WWARN, "[input] Recvice Failed!");
                usleep(1000);
                continue;
            }
        }
        else if (nbytes > 0)
        {
            m_recvBuffer.Push(rbuf, nbytes);
        }
        usleep(100);
    }
    delete[] rbuf;
    c_bRecvThrOver = true;
}

void Input::initScanBuffer(uint16_t npackets)
{
    // 初始化Buffer
    m_recvBuffer.Reset(RECV_BUFFER_SIZE);
    ResetScanBuffer(m_scanBuffer1, -1, 0, npackets);
    ResetScanBuffer(m_scanBuffer2, -1, 0, npackets);
}

void Input::ResetScanBuffer(ScanBuffer& buffer,
                            int32_t circleNum,
                            uint16_t startIdx,
                            uint16_t npackets)
{
    buffer.m_iCircleNum = circleNum;
    buffer.m_frameCnt = 0;
    buffer.m_bIsFull = false;
    buffer.m_startIdx = startIdx;
    buffer.m_data.m_vPackets.clear();
    buffer.m_data.m_vPackets.resize(npackets);
    buffer.m_data.m_iTimestamp = 0;
}

#pragma endregion

#pragma region 720初始化类

Input720::Input720(uint32_t p_uiLidarId, boost::function<void(ScanBuffer)> scandataCb)
    : Input(p_uiLidarId, scandataCb)
{
    memset(&c_iLevelAngleData, 0, sizeof(c_iLevelAngleData));
}

Input720::~Input720(void) {}

void Input720::requestBaseParam()
{
    std::string l_sFilePath = "_" + c_stLaserCfg_.m_sLaserName;

    c_sVerAnglePath_ = c_stLaserCfg_.m_sCalibrationFile + l_sFilePath + ".csv";

    c_sCalibrationPath_ = c_stLaserCfg_.m_sCalibrationFile + l_sFilePath + ".txt_PX";

    // 在线查询 离线不查询
    if (c_stSysParam_->m_bIsOnlineMode && c_stLaserCfg_.m_bGetCalibrationOnline)
    {
        for (int i = 0; i < 3; i++)
        {
            if (!m_isGetSn)
            {
                requestSN_();
                usleep(10000);
            }
            if (!m_isGetBaseParam)
            {
                requestBaseParam_();
                usleep(10000);
            }
            if (!m_isGetVerAngleData)
            {
                requestVerAngleData_();
                usleep(10000);
            }
            if (!m_isGetLevelAngleData)
            {
                requestLevelAngleData_();
                usleep(10000);
            }
        }

        if (!(m_isGetSn && m_isGetBaseParam && m_isGetVerAngleData && m_isGetLevelAngleData))
            usleep(1000 * 1000);

        if (!m_isGetSn)
        {
            c_stSysParam_->m_fae.setErrorCode("C9");
            LOGA(WERROR, "雷达 [{}] 查询SN失败 | 无回复/校验异常", c_stLaserCfg_.m_sLaserName);
        }
        if (!m_isGetBaseParam)
        {
            c_stSysParam_->m_fae.setErrorCode("C11");
            LOGA(
                WERROR, "雷达 [{}] 查询配置参数失败 | 无回复/校验异常", c_stLaserCfg_.m_sLaserName);
        }
        if (!m_isGetVerAngleData)
        {
            c_stSysParam_->m_fae.setErrorCode("C12");
            LOGA(WERROR,
                 "雷达 [{}] 查询修正表2失败 | 无回复/校验异常，请录制雷达数据包!",
                 c_stLaserCfg_.m_sLaserName);
        }
        if (!m_isGetLevelAngleData)
        {
            LOGA(WERROR, "雷达 [{}] 查询修正表1失败 | 无回复/校验异常", c_stLaserCfg_.m_sLaserName);
        }

        c_bInitSuecss_ =
            m_isGetSn && m_isGetBaseParam && m_isGetVerAngleData && m_isGetLevelAngleData;
        if (!c_bInitSuecss_)
        {
            c_stSysParam_->m_lidar[c_uiLidarId].m_dev.setStatus(wj_slam::DevStatus::GETPARAMERROR);
            LOGA(WERROR, " *********************************************** ");
            LOGA(WERROR, " * 1. 检查网线是否有松动 ");
            LOGA(WERROR,
                 " * 2. 查看端口是否设置正确(默认雷达端口: 3333, PC端口: 2.88[3001]/2.87[3002]) ");
            LOGA(WERROR, " * 3. 如果上述方法不能解决, 请启动wireshark抓包, 并联系万集开发人员。 ");
            LOGA(WERROR, " * **********************************************");
        }
    }
}

void Input720::parseLidarParam(const uint8_t* data, uint16_t len)
{
    if (!data)
        return;
    int l_iLevelAgPkgIdx = 0;
    uint16_t cmd = (data[22] << 8) | data[23];
    switch (cmd)
    {
        case GET_SN:
            if (!m_isGetSn)
            {
                if (analyseSN_(data, len))
                {
                    m_isGetSn = true;
                    LOGA(WINFO, "雷达 [{}] 查询SN成功 | 结束", c_stLaserCfg_.m_sLaserName);
                }
                else
                {
                    LOGA(WWARN, "雷达 [{}] 查询SN失败 | 校验异常", c_stLaserCfg_.m_sLaserName);
                }
            }
            break;
        case GET_BTN_TEMP: break;
        case GET_VERTICAL_AG:
            // 解析和储存垂直分辨率数据
            if (!m_isGetVerAngleData)
            {
                if (analyseVerAngleData_(data, len))
                {
                    m_isGetVerAngleData = true;
                    LOGA(WINFO, "雷达 [{}] 查询修正表2成功 | 结束!", c_stLaserCfg_.m_sLaserName);
                }
                else
                {
                    LOGA(WWARN, "雷达 [{}] 查询修正表2失败 | 校验异常", c_stLaserCfg_.m_sLaserName);
                }
            }
            break;
        case GET_LEVEL_AG:
            if (!m_isGetLevelAngleData)
            {
                l_iLevelAgPkgIdx = analyseLevelAngleData_(data, len);
                if (l_iLevelAgPkgIdx >= 0)
                {
                    m_iLevevAgPkgStatus |= (1 << l_iLevelAgPkgIdx);
                    m_isGetLevelAngleData = (m_iLevevAgPkgStatus == 0xFFFF);
                    if (m_isGetLevelAngleData)
                    {
                        LOGA(WINFO,
                             "雷达 [{}] 查询修正表1第{}包 | 查询成功!",
                             c_stLaserCfg_.m_sLaserName,
                             l_iLevelAgPkgIdx);
                        LOGA(WINFO,
                             "雷达 [{}] 查询修正表1查询成功 | 结束!",
                             c_stLaserCfg_.m_sLaserName);
                        saveLevelAngleData_(c_iLevelAngleData,
                                            sizeof(c_iLevelAngleData)
                                                / sizeof(c_iLevelAngleData[0]),
                                            c_sCalibrationPath_);
                    }
                    else
                    {
                        LOGA(WINFO,
                             "雷达 [{}] 查询修正表1第{}包 | 查询成功!",
                             c_stLaserCfg_.m_sLaserName,
                             l_iLevelAgPkgIdx);
                    }
                }
                else
                {
                    LOGA(WWARN,
                         "雷达 [{}] 查询修正表1第{}包 | 校验异常",
                         c_stLaserCfg_.m_sLaserName,
                         l_iLevelAgPkgIdx);
                }
            }
            break;
        case GET_DEV_PARAM:
            if (!m_isGetBaseParam)
            {
                if (analyseBaseParam_(data, len))
                {
                    m_isGetBaseParam = true;
                    LOGA(WINFO, "雷达 [{}] 查询配置参数成功 | 结束!", c_stLaserCfg_.m_sLaserName);
                }
                else
                {
                    LOGA(
                        WWARN, "雷达 [{}] 查询配置参数失败 | 校验异常", c_stLaserCfg_.m_sLaserName);
                }
            }
            break;
        case GET_FIRE_STATUS: break;
        default: break;
    }
}

int Input720::requestSN_()
{
    // 读取SN
    uint8_t c_getSN[34] = {0xFF, 0xAA, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x01,
                           0x00, 0x0B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x10,
                           0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xEE, 0xEE};

    LOGA(WINFO, "雷达 [{}] 查询SN...", c_stLaserCfg_.m_sLaserName);
    return sendPacket(c_getSN, sizeof(c_getSN));
}

/**
 * @description: 只有1个分割符c, 将s 按照分割符c 切割写入v  同时保留c
 * @param {string}  s 字符串
 * @param {vector<string>} v 输出列表
 * @param {string}  c 分割符
 * @return {*} WLR720F_NP 分割符：_NP  结果将为[0] WLR720F  [1] _NP
 */
void Input720::splitStr_(const std::string& s, std::vector<std::string>& v, const std::string& c)
{
    std::string::size_type pos1, pos2;
    pos2 = s.find(c);
    pos1 = 0;
    while (std::string::npos != pos2)
    {
        if (pos1 != pos2)
            v.push_back(s.substr(pos1, pos2 - pos1));
        v.push_back(s.substr(pos2, c.size()));

        pos1 = pos2 + c.size();
        pos2 = s.find(c, pos1);
    }
    if (pos1 != s.length())
        v.push_back(s.substr(pos1));
}

void Input720::compareSN(std::string& p_sSetSN, std::string p_sReadSN)
{
    if (p_sReadSN != p_sSetSN)
    {
        // SN不符 则删除旧表
        deleteFileOrDir(c_sVerAnglePath_);
        deleteFileOrDir(c_sCalibrationPath_);
        LOGA(WWARN, "雷达SN与配置SN [{}] 不一致, 自动删除雷达修正表", c_stLaserCfg_.m_sLaserSN);
        p_sSetSN = p_sReadSN;
    }
}

bool Input720::analyseSN_(const uint8_t* p_buf, const int p_iSize)
{
    // 720SN-eg: 720A20210506x035
    char l_cSN[17] = {0};
    memcpy(l_cSN, &p_buf[26], 16);
    LOGA(WINFO, "雷达 [{}] 查询SN | SN码: {}", c_stLaserCfg_.m_sLaserName, std::string(l_cSN));

    compareSN(c_stSysParam_->m_lidar[c_uiLidarId].m_sLaserSN, std::string(l_cSN));

    // 最前面是720开头720xxxxxxxxx
    int l_iBasicType =
        (int)(l_cSN[0] - '0') * 100 + (int)(l_cSN[1] - '0') * 10 + (int)(l_cSN[2] - '0');
    char l_cUseType = l_cSN[3];
    // 生产日期
    int l_iManufactureTime = (int)(l_cSN[4] - '0') * 10 + (int)(l_cSN[5] - '0');

    // 如果不是720则异常
    if (l_iBasicType == 720)
    {
        std::string l_sReadDevType;
        // 22年启用
        if (l_iManufactureTime > 21)
        {
            if (l_cUseType == 'C')
                l_sReadDevType = "WLR720C";
            else if (l_cUseType == 'F')
                l_sReadDevType = "WLR720FCW";
            else if (l_cUseType == 'G')
                l_sReadDevType = "WLR720G";
            else
                l_sReadDevType = "WLR" + std::to_string(l_iBasicType) + l_cUseType;
        }
        // 之前的默认为720F
        else
            l_sReadDevType = "WLR720F";

        // 切割并提取主类型号  eg: WLR720F_NP和WLR720F符合
        std::vector<std::string> l_vsOut;
        splitStr_(c_stLaserCfg_.m_dev.m_sDevType, l_vsOut, "_NP");
        if (l_vsOut.size() == 1 || l_vsOut.size() == 2)
        {
            if (l_sReadDevType != l_vsOut[0])
            {
                c_stSysParam_->m_fae.setErrorCode("C10");
                // c_stSysParam_->m_fae.setErrorCode("C14");
                LOGA(WERROR,
                     "雷达 [{}] 类型设定错误：设定 {} 读取 {}  批次 "
                     "{},请根据雷达类型确认雷达型号配置是否正确！",
                     c_stLaserCfg_.m_sLaserName,
                     c_stLaserCfg_.m_dev.m_sDevType,
                     l_sReadDevType,
                     std::to_string(l_iManufactureTime));
                LOGA(WERROR, " *********************************************** ");
                LOGA(WERROR, " * WLR720FCW为顺时针旋转");
                LOGA(WERROR, " * WLR720F为逆时针旋转");
                LOGA(WERROR, " * **********************************************");
                LOGA(WWARN,
                     "雷达 [{}] 类型设定错误：根据读取本次自动修改为 {}",
                     c_stLaserCfg_.m_sLaserName,
                     l_sReadDevType);
                c_stSysParam_->m_lidar[c_uiLidarId].m_dev.m_sDevType = l_sReadDevType;
                return false;
            }
            else
                return true;
        }
        else
        {
            // c_stSysParam_->m_fae.setErrorCode("C15");
            LOGA(WERROR,
                 "雷达 [{}] 类型校验异常： {}，请检查雷达类型！",
                 c_stLaserCfg_.m_sLaserName,
                 c_stLaserCfg_.m_dev.m_sDevType);
        }
    }
    else
    {
        // c_stSysParam_->m_fae.setErrorCode("C16");
        LOGA(WERROR,
             "雷达 [{}] 类型读取异常： {}，不支持此类型，请检查设定的雷达类型是否正确！",
             c_stLaserCfg_.m_sLaserName,
             std::to_string(l_iBasicType) + l_cUseType);
    }
    return false;
}

int Input720::requestBaseParam_()
{
    // 读取设备基本参数
    uint8_t c_getParam[34] = {0xFF, 0xAA, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00,
                              0x00, 0x01, 0x01, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x00,
                              0x00, 0x00, 0x00, 0x00, 0x05, 0x01, 0x00, 0x00, 0x00,
                              0x00, 0x00, 0x00, 0x00, 0x11, 0xEE, 0xEE};

    LOGA(WINFO, "雷达 [{}] 查询配置参数...", c_stLaserCfg_.m_sLaserName);
    return sendPacket(c_getParam, sizeof(c_getParam));
}

bool Input720::analyseBaseParam_(const uint8_t* p_buf, const int p_iSize)
{
    LOGA(WINFO, "");
    LOGA(WINFO, "******  雷达 [{}] 配置参数查询结果校验 ******", c_stLaserCfg_.m_sLaserName);
    bool l_bUseFlag = true;
    char l_cParams[15] = {0};
    memcpy(l_cParams, &p_buf[26], 15);

    // 工作模式 [0]: 1:扫描模式
    int l_iWorkMode = (int)l_cParams[0];
    // 扫描模式 [1]:0~3 5~20Hz
    int l_iRotMode = (int)l_cParams[1];
    // 回波模式 [7]:0X10~0X30 第几重回波 0X40 双回波
    int l_iEchoMode = (int)l_cParams[7];
    // 协议模式 [13]: 0：720F-19线 1：720G 2：720G云台 3：720C-16线
    int l_iProcMode = (int)l_cParams[13];
    // printf("协议模式 = %X, %d\n", l_cParams[13], l_iProcMode);
    // 协议兼容使能 [14]: 0：22新协议 1: 19线单回波旧协议
    int l_iProcCompMode = (int)l_cParams[14];

    LOGA(WINFO,
         "雷达基础参数 工作模式: {:#X} | 扫描模式：{:#X} 回波模式：{:#X} 协议模式： {:#X} "
         "协议新旧： {:#X}",
         (int)l_cParams[0],
         (int)l_cParams[1],
         (int)l_cParams[7],
         (int)l_cParams[13],
         (int)l_cParams[14]);

    switch (l_iProcCompMode)
    {
        case 0: {
            l_bUseFlag = false;
            LOGA(WERROR, "雷达协议类型: 新协议 | 暂不支持此协议, 请修改雷达硬件配置");
            // c_stSysParam_->m_fae.setErrorCode("C30");

            // LOGA(WINFO, "协议类型: {} |  正常，(Ps: 1-旧协议 0-新协议)", l_iProcCompMode);
            // 19通道-单回波新协议
            // if (c_stLaserCfg_.m_dev.m_sDevType == "WLR720F"
            //     || c_stLaserCfg_.m_dev.m_sDevType == "WLR720FCW"
            //     || c_stLaserCfg_.m_dev.m_sDevType == "WLR720G")
            // {
            //     c_stSysParam_->m_fae.setErrorCode("C30");
            //     LOGA(WERROR,
            //            "雷达协议类型: 新协议 | 设备类型 [{}] 为旧协议类型，新协议对应设备类型为
            //            [{}], " "请检查!", c_stLaserCfg_.m_dev.m_sDevType,
            //            c_stLaserCfg_.m_dev.m_sDevType + "_NP");
            //     l_bUseFlag = false;
            // }
            // // 16通道-单回波新协议
            // else if (c_stLaserCfg_.m_dev.m_sDevType == "WLR720C")
            // {
            //     if (c_stLaserCfg_.m_uiFramePkgNum != 100 || c_stLaserCfg_.m_uiFramePktSize !=
            //     1253
            //         || c_stLaserCfg_.m_dev.m_uiDataMinLen != 1253
            //         || c_stLaserCfg_.m_uiFrameTime != 100)
            //     {
            //         c_stSysParam_->m_fae.setErrorCode("C31");
            //         LOGA(WERROR,
            //                "协议类型: 新协议 | 不符合程序参数 {} {} {} {} {} ,请检查!",
            //                c_stLaserCfg_.m_dev.m_sDevType,
            //                c_stLaserCfg_.m_uiFramePkgNum,
            //                c_stLaserCfg_.m_uiFramePktSize,
            //                c_stLaserCfg_.m_dev.m_uiDataMinLen,
            //                c_stLaserCfg_.m_uiFrameTime);

            //         c_stSysParam_->m_lidar[c_uiLidarId].m_uiFramePkgNum = 100;
            //         c_stSysParam_->m_lidar[c_uiLidarId].m_uiFramePktSize = 1253;
            //         c_stSysParam_->m_lidar[c_uiLidarId].m_dev.m_uiDataMinLen = 1253;
            //         c_stSysParam_->m_lidar[c_uiLidarId].m_uiFrameTime = 100;
            //         l_bUseFlag = false;
            //     }
            // }
            // else if (c_stLaserCfg_.m_uiFramePkgNum != 120 || c_stLaserCfg_.m_uiFramePktSize !=
            // 1235
            //          || c_stLaserCfg_.m_dev.m_uiDataMinLen != 1235
            //          || c_stLaserCfg_.m_uiFrameTime != 100)
            // {
            //     c_stSysParam_->m_lidar[c_uiLidarId].m_uiFramePkgNum = 120;
            //     c_stSysParam_->m_lidar[c_uiLidarId].m_uiFramePktSize = 1235;
            //     c_stSysParam_->m_lidar[c_uiLidarId].m_dev.m_uiDataMinLen = 1235;
            //     c_stSysParam_->m_lidar[c_uiLidarId].m_uiFrameTime = 100;
            //     c_stSysParam_->m_fae.setErrorCode("C32");
            //     LOGA(WERROR,
            //            "协议类型: 新协议 | 不符合程序参数 {} {} {} {} {} ,请检查!",
            //            c_stLaserCfg_.m_dev.m_sDevType,
            //            c_stLaserCfg_.m_uiFramePkgNum,
            //            c_stLaserCfg_.m_uiFramePktSize,
            //            c_stLaserCfg_.m_dev.m_uiDataMinLen,
            //            c_stLaserCfg_.m_uiFrameTime);
            //     l_bUseFlag = false;
            // }
            break;
        }
        case 1: {
            if (c_stLaserCfg_.m_dev.m_sDevType != "WLR720F"
                && c_stLaserCfg_.m_dev.m_sDevType != "WLR720FCW")
            {
                // c_stSysParam_->m_fae.setErrorCode("C33");
                LOGA(WERROR,
                     "协议类型: 旧协议 | 对应设备类型 [WLR720F] / [WLR720FCW], "
                     "不符合设备类型[{}]，请检查设备类型与协议是否匹配!",
                     c_stLaserCfg_.m_dev.m_sDevType);
                l_bUseFlag = false;
            }
            // 19通道-单回波旧协议
            if (c_stLaserCfg_.m_uiFramePkgNum != 120 || c_stLaserCfg_.m_uiFramePktSize != 1260
                || c_stLaserCfg_.m_dev.m_uiDataMinLen != 1260 || c_stLaserCfg_.m_uiFrameTime != 100)
            {
                // c_stSysParam_->m_fae.setErrorCode("C34");
                LOGA(WERROR,
                     "协议类型: 旧协议 | 不符合程序参数 {} {} {} {} {} ,请联系万集开发人员!",
                     c_stLaserCfg_.m_dev.m_sDevType,
                     c_stLaserCfg_.m_uiFramePkgNum,
                     c_stLaserCfg_.m_uiFramePktSize,
                     c_stLaserCfg_.m_dev.m_uiDataMinLen,
                     c_stLaserCfg_.m_uiFrameTime);

                c_stSysParam_->m_lidar[c_uiLidarId].m_uiFramePkgNum = 120;
                c_stSysParam_->m_lidar[c_uiLidarId].m_uiFramePktSize = 1260;
                c_stSysParam_->m_lidar[c_uiLidarId].m_dev.m_uiDataMinLen = 1260;
                c_stSysParam_->m_lidar[c_uiLidarId].m_uiFrameTime = 100;
                l_bUseFlag = false;
            }
            break;
        }
        default:
            // c_stSysParam_->m_fae.setErrorCode("C35");
            LOGA(WERROR, "雷达协议类型: {:#X} | 不支持该协议,请联系万集开发人员! ", l_cParams[14]);
            l_bUseFlag = false;
            break;
    }
    LOGA(WINFO, "******  雷达 [{}] 配置参数结果校验完成 ******", c_stLaserCfg_.m_sLaserName);
    LOGA(WINFO, "");
    return l_bUseFlag;
}

int Input720::requestVerAngleData_()
{
    // 读取垂直分比例表
    uint8_t c_getVerAngleData[34] = {0xFF, 0xAA, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00,
                                     0x00, 0x01, 0x01, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x00,
                                     0x00, 0x00, 0x00, 0x00, 0x05, 0x14, 0x00, 0x00, 0x00,
                                     0x00, 0x00, 0x00, 0x00, 0x04, 0xEE, 0xEE};

    LOGA(WINFO, "雷达 [{}] 正在查询修正表2...", c_stLaserCfg_.m_sLaserName);
    return sendPacket(c_getVerAngleData, sizeof(c_getVerAngleData));
}

bool Input720::analyseVerAngleData_(const uint8_t* p_buf, const int p_size)
{
    LOGA(WINFO, "雷达 [{}] 正在校验修正表2...", c_stLaserCfg_.m_sLaserName);
    float verAngle[19] = {0};
    float recvVerAngle[16] = {0};
    for (int j = 0; j < 16; j++)
        recvVerAngle[j] = (p_buf[30 + j * 4] << 24 | p_buf[29 + j * 4] << 16
                           | p_buf[28 + j * 4] << 8 | p_buf[27 + j * 4])
                          * 1.0 / 1000.0;
    // 检查异常数据
    for (int j = 0; j < 16; j++)
    {
        if (recvVerAngle[j] < -20.0 || recvVerAngle[j] > 20.0)
        {
            LOGA(WERROR,
                 "雷达 [{}] 修正表2校验失败 | 线Id: {} data: {} | 数据异常，请录制雷达数据包!",
                 c_stLaserCfg_.m_sLaserName,
                 j,
                 recvVerAngle[j]);
            LOGA(WERROR, " *********************************************** ");
            LOGA(WERROR, " * 请启动wireshark录制雷达数据包，并联系万集开发人员！");
            LOGA(WERROR, " * **********************************************");
            c_stSysParam_->m_fae.setErrorCode("C13");
            // c_stSysParam_->m_fae.setErrorCode("C39");
            return false;
        }
    }
    // 19通道
    if (c_stLaserCfg_.m_dev.m_sDevType == "WLR720A" || c_stLaserCfg_.m_dev.m_sDevType == "WLR720F"
        || c_stLaserCfg_.m_dev.m_sDevType == "WLR720F_NP"
        || c_stLaserCfg_.m_dev.m_sDevType == "WLR720FCW"
        || c_stLaserCfg_.m_dev.m_sDevType == "WLR720FCW_NP"
        || c_stLaserCfg_.m_dev.m_sDevType == "WLR720G")
    {
        verAngle[0] = verAngle[5] = verAngle[9] = verAngle[14] = recvVerAngle[7];
        verAngle[1] = recvVerAngle[0];
        verAngle[2] = recvVerAngle[1];
        verAngle[3] = recvVerAngle[2];
        verAngle[4] = recvVerAngle[3];

        verAngle[6] = recvVerAngle[4];
        verAngle[7] = recvVerAngle[5];
        verAngle[8] = recvVerAngle[6];

        verAngle[10] = recvVerAngle[8];
        verAngle[11] = recvVerAngle[9];
        verAngle[12] = recvVerAngle[10];
        verAngle[13] = recvVerAngle[11];

        verAngle[15] = recvVerAngle[12];
        verAngle[16] = recvVerAngle[13];
        verAngle[17] = recvVerAngle[14];
        verAngle[18] = recvVerAngle[15];
        saveVerAngleData_(verAngle, 19, c_sVerAnglePath_);
    }
    else
    {
        // 16通道
        saveVerAngleData_(recvVerAngle, 16, c_sVerAnglePath_);
    }

    return true;
}

int Input720::requestLevelAngleData_()
{
    // 读取偏心修正表
    uint8_t c_getLevelAngleData[34] = {0xFF, 0xAA, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00,
                                       0x00, 0x01, 0x01, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x00,
                                       0x00, 0x00, 0x00, 0x00, 0x05, 0x1A, 0x00, 0x00, 0x00,
                                       0x01, 0x03, 0xC0, 0x00, 0xC8, 0xEE, 0xEE};

    LOGA(WINFO, "雷达 [{}] 正在查询修正表1...", c_stLaserCfg_.m_sLaserName);

    bool res = true;
    for (uint16_t i = 1; i <= 15; i++)
    {
        if (m_iLevevAgPkgStatus & (1 << i))
        {
            continue;
        }
        *(c_getLevelAngleData + 27) = i;
        res = sendPacket(c_getLevelAngleData, sizeof(c_getLevelAngleData)) && res;
        usleep(50000);
    }
    return res;
}

int Input720::analyseLevelAngleData_(const uint8_t* p_buf, const int p_size)
{
    // 协议内包号1-15 变为数组偏移0~14
    int l_iPkgNum = (p_buf[27] | p_buf[26] << 8) - 1;
    int l_iSize = (p_buf[28] << 8) | p_buf[29];
    // 异常数据
    if (l_iPkgNum > 14 || l_iSize != 960)
    {
        c_stSysParam_->m_fae.setErrorCode("C15");
        // c_stSysParam_->m_fae.setErrorCode("C43");
        LOGA(WERROR,
             "雷达 [{}] 修正表1校验失败 | pkt: {} size: {},请联系万集开发人员！",
             c_stLaserCfg_.m_sLaserName,
             l_iPkgNum,
             l_iSize);
        return -1;
    }
    else
    {
        // 只使用偶数位数值
        l_iSize /= 2;
        LOGA(WDEBUG,
             "{} Horizon angle resolution pack-{} with size {}",
             WJODLog::getWholeSysTime(),
             l_iPkgNum,
             l_iSize);
    }

    // 按照分辨率0.025填写偏心修正表,但只读取0.05分辨率
    for (int j = 0; j < l_iSize; j++)
        c_iLevelAngleData[l_iPkgNum * l_iSize + j] = static_cast<char>(p_buf[30 + 2 * j]);

    // 读取最后一包则保存
    // if (l_iPkgNum == 14)
    //     saveLevelAngleData_(c_iLevelAngleData,
    //                         sizeof(c_iLevelAngleData) / sizeof(c_iLevelAngleData[0]),
    //                         c_sCalibrationPath_);
    return l_iPkgNum + 1;
}

void Input720::saveVerAngleData_(float* p_VerAngleData, uint p_uiSize, std::string p_sFilePath)
{
    int l_nWriteOverSign = 1;
    std::fstream l_filePoseWR;
    l_filePoseWR.open(p_sFilePath.c_str(), std::ios::out | std::ios::trunc);
    if (l_filePoseWR.is_open())
    {
        // 保存格式:19行,两列,第二列为0
        for (uint i = 0; i < p_uiSize; i++)
            l_filePoseWR << *(p_VerAngleData + i) << ","
                         << "0" << std::endl;
        LOGA(WINFO, "雷达 [{}] 保存修正表2成功: 路径: {}", c_stLaserCfg_.m_sLaserName, p_sFilePath);
    }
    else
    {
        c_stSysParam_->m_fae.setErrorCode("C14");
        // c_stSysParam_->m_fae.setErrorCode("C40");
        LOGA(WERROR,
             "雷达 [{}] 保存修正表2失败: 文件打开异常 | 请联系万集开发人员!",
             c_stLaserCfg_.m_sLaserName);
    }
    l_filePoseWR.close();
}

bool Input720::saveLevelAngleData_(int* p_VerAngleData, uint p_uiSize, std::string p_sFilePath)
{
    std::fstream l_filePoseWR;
    l_filePoseWR.open(p_sFilePath.c_str(), std::ios::out | std::ios::trunc);
    if (l_filePoseWR.is_open())
    {
        // 保存格式:1440行,5列
        for (uint i = 0; i < p_uiSize; i += 5)
            l_filePoseWR << *(p_VerAngleData + i) << "," << *(p_VerAngleData + i + 1) << ","
                         << *(p_VerAngleData + i + 2) << "," << *(p_VerAngleData + i + 3) << ","
                         << *(p_VerAngleData + i + 4) << std::endl;
        LOGA(WINFO, "雷达 [{}] 保存修正表1成功: 路径: {}", c_stLaserCfg_.m_sLaserName, p_sFilePath);
    }
    else
    {
        LOGA(WERROR,
             "雷达 [{}] 保存修正表1失败: 文件打开异常 | 请检查!",
             c_stLaserCfg_.m_sLaserName);
        // c_stSysParam_->m_fae.setErrorCode("C44");
        return false;
    }
    l_filePoseWR.close();
    return true;
}
#pragma endregion
}  // namespace wanji_driverOD
