/*
 * @Author: senlin <EMAIL>
 * @Date: 2022-08-19 14:24:22
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-29 09:30:38
 * @FilePath: /src/wanji_od/src/algorithm/coord_soultion/coordSoultion.cpp
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

#include "algorithm/coord_solution/coordSolution.h"
#include <math.h>
#include <pcl/common/common.h>
#include <pcl/io/pcd_io.h>
#include <pcl/point_types.h>
#include <stdio.h>
#include <unistd.h>

namespace wj_od {

CoordSolution::CoordSolution(boost::function<void(GroupScaleMap&)> p_callback)
    : c_preproCallback_(p_callback)

{
    c_stSysParam_ = wj_slam::SYSPARAMOD::getIn();
    paramInit();                                     // 参数初始化
    getLidarParam(c_fLidarHeight_, c_fLidarPitch_);  // get雷达安装高度和俯仰角
    calculateCosTable();                             // 计算cos表
}

CoordSolution::~CoordSolution() {}

void CoordSolution::paramInit()
{
    c_fLidarHeight_ = 1.8;
    c_fLidarPitch_ = 0.0;
    c_fGroudHeight_ = 0.1;
    c_sAreaDir_ = ros::package::getPath("wanji_od");
}

void CoordSolution::calculateCosTable()
{
    for (int i = 0; i < WLR720_SCANS_PER_FIRING; i++)
    {
        c_afVcosRotTable_[i] = cos(c_afVertAngle_[i] * M_PI / 180.0);
    }
}

void CoordSolution::getLidarParam(float p_fLidarHeight, double p_fLidarPitch)
{
    // c_fLidarHeight_ = c_sysParam_.m_lidar.feature_height;
    c_fLidarPitch_ = p_fLidarPitch;
}

bool CoordSolution::isAreaExists(int p_iGroupID, int p_iAreaID)
{
    std::string l_sFile =
        std::to_string(p_iGroupID + 1) + "_" + std::to_string(p_iAreaID + 1) + ".csv";
    std::string l_sFilePath = c_sAreaDir_ + "/area_cfg/" + l_sFile;

    // 该文件是否存在
    if (access(l_sFilePath.c_str(), F_OK) == 0)
    {
        LOGA(WINFO, l_sFile + " exists!");
        return true;
    }
    else
    {
        return false;
    }
}

void CoordSolution::pointDiscretize(s_Area& p_sArea)
{
    double l_dStartAngle = 0;
    double l_dEndAngle = 360;

    if (p_sArea.shape == FAN)
        getFanScanScope(l_dStartAngle, l_dEndAngle, p_sArea.polygon.vertex);
    PXYZI* center = &p_sArea.polygon.vertex->at(0);      // 圆心点
    PXYZI* pointStart = &p_sArea.polygon.vertex->at(1);  // 圆周上的任意一点A起始点

    float centerX = center->x;
    float centerY = center->y;
    float l_fRadius = getDistanceOfTwoPoints(center, pointStart);  // 计算半径

    int i = 0;
    double step = 1;                            // 每隔一度
    p_sArea.polygon.vertex.reset(new pcVer());  //  清空原始顶点
    for (double ang = l_dStartAngle; ang < l_dEndAngle; ang += step)
    {
        PXYZI l_snewVertex;
        double rad = degTransRad(ang);  // 角度转为弧度

        l_snewVertex.x = l_fRadius * cos(rad) + centerX;
        l_snewVertex.y = l_fRadius * sin(rad) + centerY;

        calculateVertexInfo(l_snewVertex);  // 计算水平角度及距离原点的距离
        p_sArea.polygon.vertex->push_back(l_snewVertex);
        i++;
    }
    p_sArea.polygon.vNum = p_sArea.polygon.vertex->size();  // 更新区域顶点数
}

// 计算扇形的起始角及终止角
void CoordSolution::getFanScanScope(double& p_dStartAngle,
                                    double& p_dEndAngle,
                                    pcVerPtr& p_sVerArray)
{
    PXYZI* pointStart = &p_sVerArray->at(1);  // 圆周上的任意一点A起始点
    PXYZI* pointEnd = &p_sVerArray->at(2);    // 圆周上的任意一点B结束点

    p_dStartAngle = getHorAngleDegree(pointStart->x, pointStart->y);  // 计算起始角度
    p_dEndAngle = getHorAngleDegree(pointEnd->x, pointEnd->y);        // 计算终止角度

    // 判断起始和终止方位角的大小，可能存在往回画的情况
    if (p_dStartAngle > p_dEndAngle)
    {
        double temp = p_dStartAngle;
        p_dStartAngle = p_dEndAngle;
        p_dEndAngle = temp;
    }
}

// 输入100.1，输出100
// 输入100.25 输出100.2
// 输入100.39 输出100.4
double CoordSolution::matchLidarResolution(PXYZI* p_ptrCoordA)
{
    // 从A点水平角度开始，遍历到B点水平角
    int l_iHAngle100Times = (int)(p_ptrCoordA->intensity * 100);
    double l_dLidarHAngle = 0;
    // 起始角度向最近的0.2度靠近
    if (l_iHAngle100Times % 20)
    {
        double l_dLidarHAngleLeft = (l_iHAngle100Times / 20 * 20) / 100.0f;  // 向下取角度
        double l_dLidarHAngleRight = (l_iHAngle100Times / 20 * 20 + 20) / 100.0f;  // 向上取角度

        // 比较当前角度距离左右谁更近，取近的那个
        if (p_ptrCoordA->intensity - l_dLidarHAngleLeft
            > l_dLidarHAngleRight - p_ptrCoordA->intensity)
            l_dLidarHAngle = l_dLidarHAngleRight;
        else
            l_dLidarHAngle = l_dLidarHAngleLeft;
    }
    else
        l_dLidarHAngle = l_iHAngle100Times / 100.0f;
    return l_dLidarHAngle;
}

double CoordSolution::degTransRad(double p_fDeg)
{
    double rad = p_fDeg * M_PI / 180;
    return rad;
}

void CoordSolution::updateMaxProtectRange(int p_iScaleId,
                                          float p_dProtectRange,
                                          ScaleTablePtr& p_sNewTable)
{
    for (int i = 0; i < WLR720_SCANS_PER_FIRING; i++)
    {
        if (!p_sNewTable->scan[i])  // 若该线标志无效
            continue;
        (*p_sNewTable->table)[i][p_iScaleId].state = true;
        (*p_sNewTable->table)[i][p_iScaleId].maxDis = p_dProtectRange / c_afVcosRotTable_[i];
    }
}

void CoordSolution::updateMinProtectRange(int p_iScaleId,
                                          float p_dProtectRange,
                                          ScaleTablePtr& p_sNewTable)
{
    for (int i = 0; i < WLR720_SCANS_PER_FIRING; i++)
    {
        if (!p_sNewTable->scan[i])  // 若该线标志无效
            continue;
        (*p_sNewTable->table)[i][p_iScaleId].state = true;
        (*p_sNewTable->table)[i][p_iScaleId].minDis = p_dProtectRange / c_afVcosRotTable_[i];
    }
}

void CoordSolution::initProtectRange(int p_iScaleId,
                                     float p_dProtectRange,
                                     s_Area& p_sNewArea,
                                     ScaleTablePtr& p_sNewTable)
{
    for (int i = 0; i < WLR720_SCANS_PER_FIRING; i++)
    {
        if (!p_sNewTable->scan[i])  // 若该线标志无效
            continue;
        (*p_sNewTable->table)[i][p_iScaleId].state = true;
        // 如果原点不在区域内，则改变最小值为当前最大值
        if (!p_sNewArea.originInRegion)
            (*p_sNewTable->table)[i][p_iScaleId].minDis = p_dProtectRange / c_afVcosRotTable_[i];
        (*p_sNewTable->table)[i][p_iScaleId].maxDis = p_dProtectRange / c_afVcosRotTable_[i];
    }
}

void CoordSolution::setTable(int p_iScaleId,
                             float p_dProtectRange,
                             s_Area& p_sNewArea,
                             ScaleTablePtr& p_sNewTable)
{
    scaleTablePtr l_sTable = p_sNewTable->table;  // 取出该区域的刻度表
    bool originInArea = p_sNewArea.originInRegion;

    // 以下逻辑不要轻易改动 容易出错
    // 如果该刻度已经有值
    if ((*l_sTable)[8][p_iScaleId].state)
    {
        float l_fminHDis = (*l_sTable)[ZERO_SCAN][p_iScaleId].minDis;  // 取出当前水平最小距离
        float l_fmaxHDis = (*l_sTable)[ZERO_SCAN][p_iScaleId].maxDis;  // 取出当前水平最大距离

        if (p_dProtectRange > l_fmaxHDis)
            updateMaxProtectRange(p_iScaleId, p_dProtectRange, p_sNewTable);
        else if (p_dProtectRange < l_fminHDis)
            updateMinProtectRange(p_iScaleId, p_dProtectRange, p_sNewTable);
        else if (!originInArea && (p_dProtectRange > l_fminHDis && p_dProtectRange < l_fmaxHDis))
            updateMinProtectRange(p_iScaleId, p_dProtectRange, p_sNewTable);
    }
    // 如果没有，则将当前值直接写入最大值
    else
        initProtectRange(p_iScaleId, p_dProtectRange, p_sNewArea, p_sNewTable);
}

bool CoordSolution::isHeightBelowGround(float& p_fHeight,
                                        int p_iScanIdx,
                                        ScaleTablePtr& p_scaleTable)
{
    float minH = c_fGroudHeight_ - c_fLidarHeight_;
    if (p_fHeight <= minH)
    {
        p_scaleTable->scan[p_iScanIdx] = false;
        return true;
    }
    return false;
}

bool CoordSolution::isHeightUpArea(float& p_fHeight,
                                   float& p_fAreaHeight,
                                   int p_iScanIdx,
                                   ScaleTablePtr& p_scaleTable)
{
    if (p_fHeight > p_fAreaHeight)
    {
        p_scaleTable->scan[p_iScanIdx] = false;
        return true;
    }
    return false;
}

void CoordSolution::getValidScan(s_Area& p_area, ScaleTablePtr& p_scaleTable)
{
    // 如果防护高度小于雷达安装高度
    // if (p_area.height < c_fLidarHeight_)
    // {
    //     LOGA(WWARN, "Group [{}] area [{}] protect height lower lidar height'",
    //         p_area.groupID+1, p_area.areaID+1, p_area.height, c_fLidarHeight_);
    // }

    // PXYZI min, max;
    // pcVer areaVertex;
    // areaVertex = *p_area.polygon.vertex;    // 当前区域顶点
    // pcl::PointCloud<PXYZI>::Ptr vertexCloud(new pcl::PointCloud<PXYZI>());
    // for (auto v : areaVertex)
    //     vertexCloud->push_back(v);

    // pcl::getMinMax3D(*vertexCloud, min, max);   // 找到坐标极值

    // float height = c_fLidarHeight_;
    // // 防护区域默认高于或等于雷达安装高度
    // for (int i = 0; i < WLR720_SCANS_PER_FIRING; i++)
    // {
    //     double l_fVerAng = degTransRad(c_afVertAngle_[i]);
    //     if (i >= ZERO_SCAN)
    //     {
    //         // 如果防护高度=雷达安装高度 后期要考虑安装俯仰角
    //         if (isHeightUpArea(height, p_area.height, i, p_scaleTable))
    //             continue;
    //         else
    //         {
    //             height = fabs(min.x) * tan(l_fVerAng) + c_fLidarHeight_;
    //             isHeightUpArea(height, p_area.height, i, p_scaleTable);
    //         }
    //     }
    // }
}

void CoordSolution::writeTableCsv(int p_iGroupID, int p_iAreaID, scaleTablePtr& p_scaleTable)
{
    std::ofstream g_outFile;
    std::string l_sOutPath = c_sAreaDir_ + "/data/areaTable/AreaTable_"
                             + std::to_string(p_iGroupID + 1) + "_" + std::to_string(p_iAreaID + 1)
                             + ".csv";
    g_outFile.open(l_sOutPath);
    g_outFile << "ID, verAng, scanID, scaleID, minDis, maxDis, state" << std::endl;
    int idx = 1;
    for (int i = 0; i < WLR720_SCANS_PER_FIRING; i++)
    {
        for (uint32_t j = 0; j < HORIZON_SCAN_NUM; j++)
        {
            g_outFile << idx << "," << c_afVertAngle_[i] << "," << i << "," << j << ","
                      << (*p_scaleTable)[i][j].minDis << "," << (*p_scaleTable)[i][j].maxDis << ","
                      << (int)(*p_scaleTable)[i][j].state << std::endl;
            idx++;
        }
    }
}

double CoordSolution::getHorAngleDegree(float x, float y)
{
    double rad = atan2(y, x);
    double angle = radTransDeg(rad);
    calculateEdgeDeg(angle);
    return angle;
}

void CoordSolution::calculateEdgeDeg(double& p_dAngle)
{
    if (p_dAngle < 0)
        p_dAngle += 360.0;
}

double CoordSolution::radTransDeg(double p_fRad)
{
    double angle = p_fRad * 180.0 / M_PI;
    calculateEdgeDeg(angle);
    return angle;
}

double CoordSolution::getDistanceOfTwoPoints(PXYZI* p_pCoordA, PXYZI* p_pCoordB)
{
    double distance =
        sqrt(pow(p_pCoordA->x - p_pCoordB->x, 2) + pow(p_pCoordA->y - p_pCoordB->y, 2));
    return distance;
}

void CoordSolution::calculateVertexInfo(PXYZI& p_pCoordA)
{
    p_pCoordA.z = sqrt(pow(p_pCoordA.x, 2) + pow(p_pCoordA.y, 2));  // z中存储该顶点距离原点的距离
    p_pCoordA.intensity =
        getHorAngleDegree(p_pCoordA.x, p_pCoordA.y);  // intensity中存储该顶点在水平方向的角度
}

double CoordSolution::getAsinDegree(double p_dSinValue)
{
    // 解决C++ asin的边界问题 超过[-1,1]得到的角度为Nan
    if (floatCompare(p_dSinValue, 1))
        p_dSinValue = 1;
    else if (floatCompare(p_dSinValue, -1))
        p_dSinValue = -1;
    double rad = asin(p_dSinValue);
    double angle = radTransDeg(rad);
    return angle < 0 ? angle + 360 : angle;
}

double CoordSolution::getCosDegree(double p_dCosValue)
{
    if (floatCompare(p_dCosValue, 1))
        p_dCosValue = 1;

    else if (floatCompare(p_dCosValue, -1))
        p_dCosValue = -1;
    double rad = acos(p_dCosValue);
    double angle = radTransDeg(rad);
    return angle < 0 ? angle + 360 : angle;
}

// 余弦定理，根据三边长度，求其中一个角度
double
CoordSolution::CosTheoryGetCosB(PXYZI* p_ptrCoordA, PXYZI* p_ptrCoordB, double p_dAngleOfAngleAOB)
{
    double l_dLenOfEdgeAB =
        getDistanceOfTwoPoints(p_ptrCoordA, p_ptrCoordB);  // 求出AB两点之间距离（角AOB对边）
    double l_dLenOfEdgeOB = p_ptrCoordB->z;  // 取出OB两点之间距离（角OAB对边）
    double l_dLenOfEdgeOA = p_ptrCoordA->z;
    // 分子
    double n = pow(l_dLenOfEdgeOA, 2) + pow(l_dLenOfEdgeAB, 2) - pow(l_dLenOfEdgeOB, 2);
    // 分母
    double d = 2 * l_dLenOfEdgeOA * l_dLenOfEdgeAB;

    // 求出角AOB角度
    double l_dAngleOfAngleOAB = getCosDegree(n / d);
    return l_dAngleOfAngleOAB;
}

bool CoordSolution::floatCompare(double x, double y)
{
    return fabs(x - y) < 1e-4;
}

double
CoordSolution::getProtectRange(double p_dLidarHAngle, double p_dAngleOfAngleOAB, PXYZI* p_ptrCoordA)
{
    // 假设该角度分辨率射线与AB交点为X
    // 则角度偏移量为角AOX
    double l_dAngleOfAngleAOX = p_dLidarHAngle - p_ptrCoordA->intensity;
    // 角AXO = 180° - 角OAB - 角AOX
    double l_dAngleOfAngleAXO = 180.0f - p_dAngleOfAngleOAB - l_dAngleOfAngleAOX;
    l_dAngleOfAngleAXO =
        l_dAngleOfAngleAXO >= 360.0 ? l_dAngleOfAngleAXO - 360 : l_dAngleOfAngleAXO;
    // 根据正弦定理，可求得OX长度
    //  OX = OA * sinOAB / sinAXO
    double l_dLenOfEdgeOX = p_ptrCoordA->z * sin(p_dAngleOfAngleOAB * M_PI / 180)
                            / sin(l_dAngleOfAngleAXO * M_PI / 180);
    return l_dLenOfEdgeOX;
}

void CoordSolution::singleAreaSolution(s_Area& p_sNewArea, ScaleTablePtr& p_sNewTable)
{
    if (isOriginInArea(p_sNewArea))
    {
        p_sNewArea.originInRegion = true;
        // printf("雷达在区域内\n");
    }

    u_int l_iVertexNum = p_sNewArea.polygon.vNum;     // 顶点数量
    pcVer l_areaVertex = *p_sNewArea.polygon.vertex;  // 取出该区域所有顶点

    if (l_iVertexNum != l_areaVertex.size())
        LOGA(WWARN,
             "Group [{}], area [{}], vertex's num: [{}], vNum: [{}]. It's not match!",
             p_sNewArea.groupID + 1,
             p_sNewArea.areaID + 1,
             l_areaVertex.size(),
             l_iVertexNum);

    for (u_int i = 0; i < l_iVertexNum; i++)
    {
        PXYZI l_pCoordA = l_areaVertex[i % l_iVertexNum];
        PXYZI l_pCoordB = l_areaVertex[(i + 1) % l_iVertexNum];
        PXYZI* l_ptrCoordA = &l_pCoordA;
        PXYZI* l_ptrCoordB = &l_pCoordB;

        // 判断两点连线是否过0度，将较小的值加上360度
        handleTwoVertexLine(l_ptrCoordA, l_ptrCoordB, l_pCoordA, l_pCoordB);
        double l_dAngleOfAngleAOB =
            fabs(l_ptrCoordA->intensity - l_ptrCoordB->intensity);  // 求出角AOB角度

        // 如果两个顶点的角度差=0 || =180，则该边在坐标轴上，则原点在区域边界上
        if (diffDegreeOfTwoVetex(l_dAngleOfAngleAOB))
            continue;
        ;

        // 余弦定理求出角OAB的角度
        double l_dAngleOAB = CosTheoryGetCosB(l_ptrCoordA, l_ptrCoordB, l_dAngleOfAngleAOB);
        if (l_dAngleOAB > 180)
        {
            LOGA(WERROR,
                 "Angle error! Group [{}], area [{}], AngleOAB [{}]",
                 p_sNewArea.groupID + 1,
                 p_sNewArea.areaID + 1,
                 l_dAngleOAB);
        }
        double l_dLidarHAngle = matchLidarResolution(l_ptrCoordA);  // 求出雷达起始水平角

        while (l_dLidarHAngle < l_ptrCoordB->intensity)
        {
            double l_dProtectRang = 0;
            if (floatCompare(l_dLidarHAngle, l_ptrCoordA->intensity))  // 直接取A点距离
                l_dProtectRang = l_ptrCoordA->z;
            else
                l_dProtectRang = getProtectRange(l_dLidarHAngle, l_dAngleOAB, l_ptrCoordA);

            double l_dRealHAngle = l_dLidarHAngle >= 360.0 ? l_dLidarHAngle - 360 : l_dLidarHAngle;
            l_dRealHAngle = 360 - l_dRealHAngle;  // 坐标原因，需要把水平角度倒一下,改成顺时针旋转

            // 以防1800越界
            if (floatCompare(l_dRealHAngle, 360))
                l_dRealHAngle = 0;

            int scaleID = (int)((l_dRealHAngle + 0.05) / 0.2);

            // 越界打印
            if (scaleID >= 1800 || scaleID < 0)
                LOGA(WERROR,
                     "Out of array bounds! ScaleID [{}], LidarHAngle [{}]",
                     scaleID,
                     l_dRealHAngle);
            if (l_dProtectRang > 100)
            {
                LOGA(WERROR,
                     "ProntectRange error! Group [{}], area [{}], protectRang [{}], scaleID[{}]",
                     p_sNewArea.groupID + 1,
                     p_sNewArea.areaID + 1,
                     l_dProtectRang,
                     scaleID);
            }

            setTable(scaleID, l_dProtectRang, p_sNewArea, p_sNewTable);
            l_dLidarHAngle += 0.2;
        }
    }
}

bool CoordSolution::lidarHAngleInLine(double p_dLidarHAngle, PXYZI* p_ptrCoordA, PXYZI* p_ptrCoordB)
{
    if ((p_dLidarHAngle < p_ptrCoordA->intensity || p_dLidarHAngle >= p_ptrCoordB->intensity)
        && (p_dLidarHAngle + 360 < p_ptrCoordA->intensity
            || p_dLidarHAngle + 360 >= p_ptrCoordB->intensity))
        return false;
    else
        return true;
}

bool CoordSolution::judgeCrossPointNum(std::vector<int> p_viCrossPointNum)
{
    for (auto num : p_viCrossPointNum)
    {
        if (num % 2 == 0)  // 交点个数为偶数或没有交点时，直接返回false
            return false;
    }
    return true;
}

bool CoordSolution::diffDegreeOfTwoVetex(double p_dAngleOfAngleAOB)
{
    // 如果两个顶点的角度差=0 || =180
    if (floatCompare(p_dAngleOfAngleAOB, 180) || floatCompare(p_dAngleOfAngleAOB, 0))
    {
        // std::cout << "该条边在坐标轴上\n";
        return true;
    }
    return false;
}

bool CoordSolution::isOriginInArea(s_Area& p_sNewArea)
{
    u_int l_iVertexNum = p_sNewArea.polygon.vNum;     // 顶点数量
    pcVer l_areaVertex = *p_sNewArea.polygon.vertex;  // 取出该区域所有顶点

    if (l_iVertexNum != l_areaVertex.size())
        printf("Warn: vertexNum is error!");

    std::vector<double> l_vdLidarHAngle(2);
    l_vdLidarHAngle = {0, 180};  // 两条射线，一条沿x轴正方向，一条沿x轴负方向
    std::vector<int> l_viCrossPointNum(2, 0);  // 两条射线与该区域的交点个数

    for (u_int i = 0; i < l_iVertexNum; i++)
    {
        PXYZI l_pCoordA = l_areaVertex[i % l_iVertexNum];
        PXYZI l_pCoordB;
        if (p_sNewArea.shape == CIRCLE || p_sNewArea.shape == FAN)
            l_pCoordB = l_areaVertex[(i + 1) % l_iVertexNum];
        else
            l_pCoordB = l_areaVertex[(i + 1) % l_iVertexNum];

        PXYZI* l_ptrCoordA = &l_pCoordA;
        PXYZI* l_ptrCoordB = &l_pCoordB;

        // 判断两点连线是否过0度，将较小的值加上360度
        handleTwoVertexLine(l_ptrCoordA, l_ptrCoordB, l_pCoordA, l_pCoordB);
        double l_dAngleOfAngleAOB =
            fabs(l_ptrCoordA->intensity - l_ptrCoordB->intensity);  // 求出角AOB角度

        // 如果两个顶点的角度差=0 || =180，则该边在坐标轴上，则原点在区域边界上
        if (diffDegreeOfTwoVetex(l_dAngleOfAngleAOB))
            return true;

        for (size_t j = 0; j < l_vdLidarHAngle.size(); j++)
        {
            // 不在该线的扫描角度范围内，跳过
            if (!lidarHAngleInLine(l_vdLidarHAngle[j], l_ptrCoordA, l_ptrCoordB))
                continue;
            else
                l_viCrossPointNum[j]++;
        }
    }

    bool l_bOriginInAreaFlag =
        judgeCrossPointNum(l_viCrossPointNum);  // 两条射线的交点个数均为奇数时，原点才在区域内
    return l_bOriginInAreaFlag;
}

void CoordSolution::handleTwoVertexLine(PXYZI* p_ptrCoordA,
                                        PXYZI* p_ptrCoordB,
                                        PXYZI p_pCoordA,
                                        PXYZI p_pCoordB)
{
    double l_dAngleAbsDiff =
        fabs(p_ptrCoordA->intensity - p_ptrCoordB->intensity);  // 两个顶点角度差的绝对值
    bool l_bPassZeroAngle = false;
    if (l_dAngleAbsDiff > 180)
    {
        l_bPassZeroAngle = true;
    }

    // 判断两点水平角大小
    if (p_ptrCoordA->intensity > p_ptrCoordB->intensity)
    {
        if (l_bPassZeroAngle)
        {
            p_pCoordB.intensity += 360.0f;  // 因为过了0°造成的A水平角更大，将点B水平角加上360°
            *p_ptrCoordA = p_pCoordA;
            *p_ptrCoordB = p_pCoordB;
        }

        else  // 未过0°，将两点顺序交换，方便遍历计算
        {
            *p_ptrCoordA = p_pCoordB;
            *p_ptrCoordB = p_pCoordA;
        }
    }
    else  // A点水平角较小
    {
        if (l_bPassZeroAngle)  // 过了0°，将点A水平角加上360°,然后交换顺序
        {
            p_pCoordA.intensity += 360.0f;
            *p_ptrCoordA = p_pCoordB;
            *p_ptrCoordB = p_pCoordA;
        }
    }
}

void CoordSolution::fillInAreaCfg(int p_iLineIdx,
                                  std::vector<std::string> p_vLineArray,
                                  s_Area& p_sNewArea,
                                  ScaleTablePtr& p_sNewTable)
{
    switch (p_iLineIdx)
    {
        case 1:
            p_sNewArea.groupID = atoi(p_vLineArray[1].c_str());
            p_sNewTable->groupID = p_sNewArea.groupID;
            break;
        case 2:
            p_sNewArea.areaID = atoi(p_vLineArray[1].c_str());
            p_sNewTable->areaID = p_sNewArea.areaID;
            break;
        case 3: p_sNewArea.shape = atoi(p_vLineArray[1].c_str()); break;
        case 4:
            p_sNewArea.minHeight = atof(p_vLineArray[1].c_str()) / 1000;  // 最小高度 转化为米
            p_sNewTable->minHeight = p_sNewArea.minHeight;
            break;
        case 5:
            p_sNewArea.maxHeight = atof(p_vLineArray[1].c_str()) / 1000;  // 最大高度 转化为米
            p_sNewTable->maxHeight = p_sNewArea.maxHeight;
            break;
        case 6:
            p_sNewArea.resTime = atoi(p_vLineArray[1].c_str());
            p_sNewTable->resFrame = p_sNewArea.resTime;
            break;
        case 7: p_sNewArea.polygon.vNum = atoi(p_vLineArray[1].c_str()); break;
        default: break;
    }
}

void CoordSolution::readAreaCfgFile(int p_iGroupID,
                                    int p_iAreaID,
                                    s_Area& p_sNewArea,
                                    ScaleTablePtr& p_sNewTable)
{
    std::string l_sFilePath = c_sAreaDir_ + "/area_cfg/" + std::to_string(p_iGroupID + 1) + "_"
                              + std::to_string(p_iAreaID + 1) + ".csv";
    std::ifstream read_file(l_sFilePath);

    if (!read_file.is_open())
    {
        LOGA(WERROR, "Group [{}], area[{}], cannot open area file", p_iGroupID + 1, p_iAreaID + 1);
    }
    p_sNewArea.state = true;

    std::string line;
    int index = 0;
    while (std::getline(read_file, line))
    {
        std::string cell;
        std::stringstream lineStream(line);  // 字符串流
        std::vector<std::string> lineArray;
        while (std::getline(lineStream, cell, ','))
        {
            lineArray.push_back(cell);
        }
        if (index < 7)
            fillInAreaCfg(index + 1, lineArray, p_sNewArea, p_sNewTable);
        else
        {
            PXYZI vertexCoord;
            vertexCoord.x = atof(lineArray[1].c_str()) / 1000;  // 转化为单位m
            vertexCoord.y = atof(lineArray[2].c_str()) / 1000;
            calculateVertexInfo(vertexCoord);  // 计算水平角度及距离原点的距离
            p_sNewArea.polygon.vertex->push_back(vertexCoord);
        }
        index++;
    }
}

// 以组为单位 解算一组区域
void CoordSolution::groupSolution(int p_iGroupID)
{
    int l_iAreaNum = 0;                             // 该组区域文件数量
    std::vector<bool> l_vbAreaExistFlag(3, false);  // 文件是否存在标志位
    for (int j = 0; j < EVERY_GROUP_AREA_NUM; j++)
    {
        // 如果该区域存在
        if (isAreaExists(p_iGroupID, j))
        {
            l_iAreaNum++;
            l_vbAreaExistFlag[j] = true;
        }
    }
    // 如果该组没有任何区域文件存在 退出
    if (l_iAreaNum == 0)
        return;

    // 如果没有该区域组
    if (c_sGroupTableMap_.find(p_iGroupID) == c_sGroupTableMap_.end())
    {
        AreaScaleMap l_sNewGroupTable;
        c_sGroupTableMap_.insert(std::make_pair(p_iGroupID, l_sNewGroupTable));  // 新创建一组区域
        wjPrint(WJCOL_GREEN, "New GroupID", p_iGroupID + 1);
    }
    else
    {
        // 该区域组存在, 清除该区域组
        std::unordered_map<int, ScaleTablePtr>::iterator iter;
        for (iter = c_sGroupTableMap_[p_iGroupID].begin();
             iter != c_sGroupTableMap_[p_iGroupID].end();)
        {
            c_sGroupTableMap_[p_iGroupID].erase(iter++);
        }
        c_sGroupTableMap_[p_iGroupID].clear();
        wjPrint(WJCOL_GREEN, "Clear GroupID", p_iGroupID + 1);
    }

    for (int j = 0; j < EVERY_GROUP_AREA_NUM; j++)
    {
        // 如果该区域不存在
        if (!l_vbAreaExistFlag[j])
            continue;
        // 如果哈希表中不存在该区域 创建一个新的区域
        if (c_sGroupTableMap_[p_iGroupID].find(j) == c_sGroupTableMap_[p_iGroupID].end())
        {
            ScaleTablePtr l_sNewAreaTable(new s_SingleTable());  // 新创建一个区域的刻度表
            c_sGroupTableMap_[p_iGroupID].insert(std::make_pair(j, l_sNewAreaTable));
        }

        s_Area l_sNewArea;  // 创建一个新的区域信息结构体
        readAreaCfgFile(p_iGroupID, j, l_sNewArea, c_sGroupTableMap_[p_iGroupID][j]);
        if (l_sNewArea.shape == FAN
            || l_sNewArea.shape == CIRCLE)  // 若为扇形或圆形，得到离散的圆周/圆弧上的点
            pointDiscretize(l_sNewArea);

        // getValidScan(l_sNewArea, c_sGroupTableMap_[p_iGroupID][j]);  // 计算该区域的有效线束
        singleAreaSolution(l_sNewArea, c_sGroupTableMap_[p_iGroupID][j]);  // 对该区域进行解算

        // writeTableCsv(p_iGroupID, j, c_sGroupTableMap_[p_iGroupID][j]->table);  //
        // 把当前区域的表格存储下来
        LOGA(WINFO, "Group [{}] area [{}] finished coordsolution", p_iGroupID + 1, j + 1);
    }
}

// 初始化 读取文件夹下的所有config文件并解算区域
void CoordSolution::start()
{
    LOGA(WINFO, "Area init coordsoulution!");
    for (int i = 0; i < GROUP_NUM; i++)
    {
        groupSolution(i);
    }
    c_preproCallback_(c_sGroupTableMap_);
}

void CoordSolution::add(std::vector<int>& p_vAreaAddId)
{
    LOGA(WINFO, "Area add coordsoulution!");
    c_vAreaAddId_ = p_vAreaAddId;
    int l_iAddGroupID = c_vAreaAddId_[0];
    groupSolution(l_iAddGroupID);
    c_preproCallback_(c_sGroupTableMap_);
}

}  // namespace wj_od
