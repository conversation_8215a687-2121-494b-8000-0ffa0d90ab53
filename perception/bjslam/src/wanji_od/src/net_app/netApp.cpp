/*
 * @Description:
 * @Version: 1.0
 * @Autor: senlin
 * @Date: 2022-10-31 16:08:03
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-29 09:32:26
 */
#include "net_app/netApp.h"

void WJNetAppOD::procMsg_()
{
    char l_acBufTmp[4096];
    int l_iDataNum;
    c_pProc_->procCmdInThread();
    LOGA(WINFO,
         "[{}] {}服务器: 客户端已连接 | 客户端-IP: {} 客户端-Port: {}",
         WJODLog::getSystemTime(),
         c_sPrintfStr.c_str(),
         inet_ntoa(c_stClientAddr_.sin_addr),
         ntohs(c_stClientAddr_.sin_port));
    c_bProcThrRun_ = true;
    c_bProcThrRunOver_ = false;
    while (c_bProcThrRun_)
    {
        // printf("1\n");
        int l_res = getsockopt(
            c_iNewFd_, IPPROTO_TCP, TCP_INFO, &c_stConnectInfo_, (socklen_t*)&c_iConnectInfoLen_);
        // printf("2\n");
        if (l_res == -1 || c_stConnectInfo_.tcpi_state != TCP_ESTABLISHED)
        {
            LOGA(WWARN,
                 "[{}] {}服务器: 客户端已断开 | 客户端-IP: {} 客户端-Port: {} | 网络状态: {}",
                 WJODLog::getSystemTime(),
                 c_sPrintfStr.c_str(),
                 inet_ntoa(c_stClientAddr_.sin_addr),
                 ntohs(c_stClientAddr_.sin_port),
                 c_stConnectInfo_.tcpi_state);
            break;
        }
        if (c_iNewFd_ != -1)
        {
            // printf("3\n");
            l_iDataNum = recv(c_iNewFd_, l_acBufTmp, 4096, 0);
            // printf("4\n");
            if (l_iDataNum <= 0)
            {
                getsockopt(c_iNewFd_,
                           IPPROTO_TCP,
                           TCP_INFO,
                           &c_stConnectInfo_,
                           (socklen_t*)&c_iConnectInfoLen_);
                LOGA(WERROR,
                     "[{}] Net recvMsg fail: {} | return {} | tcp: {}",
                     c_sPrintfStr.c_str(),
                     strerror(l_iDataNum),
                     c_stConnectInfo_.tcpi_state,
                     l_res);
            }
        }
        else
            l_iDataNum = 0;
        if (l_iDataNum > 0 && l_iDataNum <= NET_LENGTH_MAX)
        {
            if ((c_pstNetMsg_->m_uiDataLen + l_iDataNum) <= (NET_LENGTH_MAX))
            {
                memcpy(&c_pstNetMsg_->m_aucBuf[c_pstNetMsg_->m_uiDataLen], l_acBufTmp, l_iDataNum);
                c_pstNetMsg_->m_uiDataLen += l_iDataNum;
                c_pstNetMsg_->m_uiDataLen %= NET_LENGTH_MAX;
            }
            else
            {
                int offset = 0;
                memcpy(&c_pstNetMsg_->m_aucBuf[c_pstNetMsg_->m_uiDataLen],
                       l_acBufTmp,
                       NET_LENGTH_MAX - c_pstNetMsg_->m_uiDataLen);
                offset = NET_LENGTH_MAX - c_pstNetMsg_->m_uiDataLen;
                memcpy(&c_pstNetMsg_->m_aucBuf[0], &l_acBufTmp[offset], l_iDataNum - offset);
                c_pstNetMsg_->m_uiDataLen = l_iDataNum - offset;
            }
        }
        // printf("5\n");
        usleep(1000);
    }
    // printf("promsg over 1\n");
    c_bNetStatus_ = true;
    close(c_iNewFd_);
    c_iNewFd_ = -1;
    c_pProc_->exitProcThread();
    c_bProcThrRun_ = false;
    c_bProcThrRunOver_ = true;
    // printf("promsg over 2\n");
}

void WJNetAppOD::sendMsg_(char* p_pcBuf, int p_iLen)
{
    if (c_iNewFd_ != -1 && p_iLen > 0)
    {
        int l_res = send(c_iNewFd_, p_pcBuf, p_iLen, MSG_NOSIGNAL);
        if (l_res != p_iLen)
        {
            LOGA(WERROR,
                 "Net sendMsg fail->status: {} | value {} | buf {} | len {}",
                 strerror(l_res),
                 l_res,
                 p_pcBuf,
                 p_iLen);
        }
    }
}

void WJNetAppOD::fillInWarnCfg(int p_iIdx, std::vector<int>& p_vbWarnFlag)
{
    char l_acBuf[100] = {0};
    int l_iOffset = 26;
    memcpy(&l_acBuf[0], c_warnProtocol_, l_iOffset);
    l_acBuf[l_iOffset++] = p_iIdx & 0xFF;  // 区域主ID
    int sum = 0;
    for (size_t i = 0; i < p_vbWarnFlag.size(); i++)
    {
        sum += p_vbWarnFlag[i] * pow(2, i);
    }
    l_acBuf[l_iOffset++] = sum & 0xFF;
    l_acBuf[l_iOffset++] = 0;
    l_acBuf[l_iOffset++] = 1;  // 有雷达数据

    // 写入帧长、校验位、帧尾
    l_acBuf[2] = (l_iOffset & 0xFF00) >> 8;
    l_acBuf[3] = l_iOffset & 0xFF;
    l_acBuf[l_iOffset++] = 0;
    l_acBuf[l_iOffset++] = checkXOR(&l_acBuf[2], l_iOffset - 3);
    l_acBuf[l_iOffset++] = 0xEE;
    l_acBuf[l_iOffset++] = 0xEE;

    if (c_bRun_)
        sendMsg_(l_acBuf, l_iOffset);
}

void WJNetAppOD::sendInvalidDataFlag(int p_iIdx)
{
    char l_acBuf[100] = {0};
    int l_iOffset = 26;
    memcpy(&l_acBuf[0], c_warnProtocol_, l_iOffset);
    l_acBuf[l_iOffset++] = p_iIdx & 0xFF;  // 区域主ID
    l_iOffset = 28;
    l_acBuf[l_iOffset++] = 0;
    l_acBuf[l_iOffset++] = 0;  // 无雷达数据

    // 写入帧长、校验位、帧尾
    l_acBuf[2] = (l_iOffset & 0xFF00) >> 8;
    l_acBuf[3] = l_iOffset & 0xFF;
    l_acBuf[l_iOffset++] = 0;
    l_acBuf[l_iOffset++] = checkXOR(&l_acBuf[2], l_iOffset - 3);
    l_acBuf[l_iOffset++] = 0xEE;
    l_acBuf[l_iOffset++] = 0xEE;

    if (c_bRun_)
        sendMsg_(l_acBuf, l_iOffset);
}

void WJNetAppOD::start(int p_iPort)
{
    int l_iSinSize;
    if ((c_iSockFd_ = socket(AF_INET, SOCK_STREAM, 0)) == -1)
    {
        LOGA(WERROR, "{} server socket error: {}", c_sPrintfStr, strerror(errno));
        return;
    }
    bzero(&c_stServerAddr_, sizeof(struct sockaddr_in));
    c_stServerAddr_.sin_family = AF_INET;
    c_stServerAddr_.sin_addr.s_addr = htonl(INADDR_ANY);
    c_stServerAddr_.sin_port = htons(p_iPort);

    int keepalive = 1;  // 开启TCP KeepAlive功能
    int keepidle = 1;   // tcp_keepalive_time 每隔1s确认1次网络连接
    int keepcnt = 3;    // tcp_keepalive_probes 重复3次，主动断开
    int keepintvl = 5;  // tcp_keepalive_intvl   重复次数之间-间隔5s

    if (setsockopt(c_iSockFd_, SOL_SOCKET, SO_KEEPALIVE, (void*)&keepalive, sizeof(keepalive)))
    {
        LOGA(WERROR, "{} server socket  SO_KEEPALIVE: {}", c_sPrintfStr, strerror(errno));
        return;
    }
    if (setsockopt(c_iSockFd_, SOL_TCP, TCP_KEEPIDLE, (void*)&keepidle, sizeof(keepidle)))
    {
        LOGA(WERROR, "{} server socket  TCP_KEEPIDLE: {}", c_sPrintfStr, strerror(errno));
        return;
    }
    if (setsockopt(c_iSockFd_, SOL_TCP, TCP_KEEPCNT, (void*)&keepcnt, sizeof(keepcnt)))
    {
        LOGA(WERROR, "{} server socket  TCP_KEEPCNT: {}", c_sPrintfStr, strerror(errno));
        return;
    }
    if (setsockopt(c_iSockFd_, SOL_TCP, TCP_KEEPINTVL, (void*)&keepintvl, sizeof(keepintvl)))
    {
        LOGA(WERROR, "{} server socket  TCP_KEEPCNT: {}", c_sPrintfStr, strerror(errno));
        return;
    }

    if (bind(c_iSockFd_, (struct sockaddr*)(&c_stServerAddr_), sizeof(struct sockaddr)) == -1)
    {
        close(c_iSockFd_);
        c_iSockFd_ = -1;
        LOGA(WERROR, "{} 服务器初始化失败 | 端口绑定错误: {}", c_sPrintfStr, strerror(errno));
        return;
    }
    if (listen(c_iSockFd_, 5) == -1)
    {
        LOGA(WERROR, "{} server socket  listen error: {}", c_sPrintfStr, strerror(errno));
        return;
    }

    c_bAcceptStatus = true;
    l_iSinSize = sizeof(struct sockaddr_in);
    LOGA(
        WINFO, "[{}] {} 服务器: 等待客户端连接...", WJODLog::getSystemTime(), c_sPrintfStr.c_str());
    if ((c_iNewFd_ =
             accept(c_iSockFd_, (struct sockaddr*)(&c_stClientAddr_), (socklen_t*)&l_iSinSize))
        == -1)
    {
        LOGA(WERROR, "{} server socket  accept error: {}", c_sPrintfStr, strerror(errno));
        return;
    }
    c_bAcceptStatus = false;
    std::thread prcomsg(&WJNetAppOD::procMsg_, this);
    prcomsg.join();

    while (c_bRun_)
    {
        if (c_bNetStatus_)
        {
            c_bAcceptStatus = true;
            l_iSinSize = sizeof(struct sockaddr_in);
            LOGA(WINFO,
                 "[{}] {} 服务器: 等待客户端连接...",
                 WJODLog::getSystemTime(),
                 c_sPrintfStr.c_str());
            if ((c_iNewFd_ = accept(
                     c_iSockFd_, (struct sockaddr*)(&c_stClientAddr_), (socklen_t*)&l_iSinSize))
                == -1)
            {
                LOGA(WERROR, "{} server socket  accept error: {}", c_sPrintfStr, strerror(errno));
                break;
            }
            c_bAcceptStatus = false;
            c_bNetStatus_ = false;
            prcomsg = std::thread(&WJNetAppOD::procMsg_, this);
            prcomsg.join();
        }
        // else
        //     std::cout << c_bNetStatus_ << std::endl;
        if (!c_bRun_)
            break;
        usleep(1000);
    }
    // std::cout << "thr quit " << c_bNetStatus_ << std::endl;
    c_bAcceptStatus = false;
}

void WJNetAppOD::fakeConnect(int& p_iFd)
{
    int l_len = sizeof(struct tcp_info);
    int ret = getsockname(c_iSockFd_, (sockaddr*)&c_stServerAddr_, (socklen_t*)&l_len);
    if (ret != 0)
        LOGA(WINFO, " {} fakeConnect Error: getsocket fail", c_sPrintfStr);

    struct sockaddr_in l_stClientAddr_;
    bzero(&l_stClientAddr_, sizeof(struct sockaddr_in));
    l_stClientAddr_.sin_family = AF_INET;
    l_stClientAddr_.sin_addr.s_addr = htonl(INADDR_ANY);
    l_stClientAddr_.sin_port = 0;  // 任意端口
    LOGA(WINFO,
         " {} fakeConnect:  client-IP: {} client-Port: {}",
         c_sPrintfStr,
         inet_ntoa(c_stClientAddr_.sin_addr),
         ntohs(l_stClientAddr_.sin_port));
    connectTcpServer_(p_iFd, l_stClientAddr_, c_stServerAddr_);
    usleep(500000);
}

/**
 * @description: 连接TCP服务器 3s未连接则认为失败
 * @param {int&} p_iFd 网络句柄 须已建立socket
 * @param {sockaddr_in&} p_sRemoteAddr 服务器地址
 * @return {bool} TCP服务器连接成功
 * @other:
 */
bool WJNetAppOD::connectTcpServer_(int& p_iFd, sockaddr_in& p_sMyAddr, sockaddr_in& p_sRemoteAddr)
{
    if (p_iFd != -1)
    {
        close(p_iFd);
        p_iFd = -1;
    }
    // 建立套接字
    p_iFd = socket(PF_INET, SOCK_STREAM, 0);
    if (p_iFd == -1)
    {
        LOGA(WERROR, "{} server open socket failed: {}", c_sPrintfStr, strerror(errno));
        return false;
    }
    // 端口复用
    int l_iOpt2 = 1;
    if (setsockopt(p_iFd, SOL_SOCKET, SO_REUSEPORT, &l_iOpt2, sizeof(l_iOpt2)))
    {
        LOGA(WERROR, "{} server reuse port error: {}", c_sPrintfStr, strerror(errno));
        return false;
    }

    // 绑定端口
    if (bind(p_iFd, (sockaddr*)&p_sMyAddr, sizeof(sockaddr)) == -1)
    {
        LOGA(WERROR, "{} server bind error: {}", c_sPrintfStr, strerror(errno));
        return false;
    }

    int flags = fcntl(p_iFd, F_GETFL, 0);
    // 设置非阻塞方式
    if (fcntl(p_iFd, F_SETFL, flags | O_NONBLOCK) < 0)
    {
        LOGA(WERROR, "{} server non-block: {}", c_sPrintfStr, strerror(errno));
        return false;
    }

    int res = connect(p_iFd, (struct sockaddr*)&p_sRemoteAddr, sizeof(p_sRemoteAddr));

    // 连接成功(服务器和客户端在同一台机器上时就有可能发生这种情况)
    if (res == -1)
    {
        // 以非阻塞的方式来进行连接的时候，返回-1,不代表一定连接错误，如果返回EINPROGRESS，代表连接还在进行中
        // 可以通过poll或者select来判断socket是否可写，如果可以写，说明连接完成了
        if (errno == EINPROGRESS)
        {
            fd_set writefds;
            FD_ZERO(&writefds);
            FD_SET(p_iFd, &writefds);

            struct timeval timeout;
            timeout.tv_sec = 3;
            timeout.tv_usec = 0;

            // 调用select来等待连接建立成功完成
            res = select(p_iFd + 1, NULL, &writefds, NULL, &timeout);
            if (res < 0)
            {
                LOGA(WERROR, "{} server TCP select: {}", c_sPrintfStr, strerror(errno));
                close(p_iFd);
                p_iFd = -1;
                return false;
            }

            // 返回0,则表示建立连接超时;
            // 我们返回超时错误给用户，同时关闭连接，以防止三路握手操作继续进行
            if (res == 0)
            {
                LOGA(WERROR, "{} server TCP connect timeout", c_sPrintfStr);
                close(p_iFd);
                p_iFd = -1;
                return false;
            }
            else
            {
                // 返回大于0的值,则需要检查套接口描述符是否可读或可写;
                if (!FD_ISSET(p_iFd, &writefds))
                {
                    LOGA(WERROR, "{} server no events found!", c_sPrintfStr);
                    close(p_iFd);
                    p_iFd = -1;
                    return false;
                }
                else
                {
                    // 套接口描述符可读或可写,通过调用getsockopt得到套接口上待处理的错误(SO_ERROR)
                    // err 0-建立成功
                    int err = 0;
                    socklen_t elen = sizeof(err);
                    res = getsockopt(p_iFd, SOL_SOCKET, SO_ERROR, (char*)&err, &elen);
                    if (res < 0)
                    {
                        LOGA(WERROR, "{} server TCP getsockopt: {}", c_sPrintfStr, strerror(errno));
                        close(p_iFd);
                        p_iFd = -1;
                        return false;
                    }
                    if (err != 0)
                    {
                        // printf("TCP connect failed with the error: (%d)%s\n", err,
                        // strerror(err));
                        close(p_iFd);
                        p_iFd = -1;
                        return false;
                    }
                    else
                        return true;
                }
            }
        }
        else
        {
            LOGA(WERROR, "{} server connectTcp error: {}", c_sPrintfStr, strerror(errno));
            return false;
        }
    }
    else
        return true;
}

void WJNetAppOD::sendActionCMD(int p_iActionCMD, std::vector<int>& p_vAreaCfgArr)
{
    c_actionCallback_(p_iActionCMD, p_vAreaCfgArr);
}

WJNetAppOD::WJNetAppOD(uint32_t p_uiPort,
                       boost::function<void(int, std::vector<int>&)> p_actionCallback)
    : c_actionCallback_(p_actionCallback)
{
    LOGA(WINFO, "server port: {}", p_uiPort);
    c_pstNetMsg_ = new s_NetMsg;
    c_pProc_ = new ProtocolOD(*c_pstNetMsg_,
                              boost::bind(&WJNetAppOD::sendMsg_, this, _1, _2),
                              boost::bind(&WJNetAppOD::sendActionCMD, this, _1, _2));
    std::thread webAppTh = std::thread(&WJNetAppOD::start, this, p_uiPort);
    webAppTh.detach();
}

void WJNetAppOD::shutDown()
{
    LOGA(WINFO, "NetApp shutdown...");
    c_bRun_ = false;
    // 当前未连接 则假连接 要求须每次断开后复位以下2个标志位
    int l_sockfd_ = -1;
    if (c_bAcceptStatus)
        fakeConnect(l_sockfd_);

    // std::cout << "run: "<<c_bRun_<< " isConnect: " << c_bProcThrRun_ <<" | connectQuit: "
    // <<c_bProcThrRunOver_  <<" | acceptStatus: "<<c_bAcceptStatus<< std::endl;
    c_bProcThrRun_ = false;
    // std::cout << "netapp shuting down..1\n";
    // 断开假连接
    if (l_sockfd_ != -1)
    {
        close(l_sockfd_);
        l_sockfd_ = -1;
    }
    // std::cout << "netapp shuting down..2\n";
    while (1)
    {
        if (c_bProcThrRunOver_)
            break;
        else
            usleep(50000);
    }

    // std::cout << " c_bProcThrRunOver_: " << c_bProcThrRunOver_ << std::endl;
    c_pProc_->shutDown();

    if (c_iSockFd_ != -1)
        close(c_iSockFd_);
    c_iSockFd_ = -1;
    if (c_pProc_ != NULL)
        delete c_pProc_;
    if (c_pstNetMsg_ != NULL)
        delete c_pstNetMsg_;
    LOGA(WINFO, "NetApp shutdown succ!");
}

WJNetAppOD::~WJNetAppOD()
{
    if (c_iNewFd_ != -1)
        close(c_iNewFd_);
    c_iNewFd_ = -1;
    if (c_iSockFd_ != -1)
        close(c_iSockFd_);
    c_iSockFd_ = -1;
    close(c_iNewFd_);
    close(c_iSockFd_);
    c_pProc_ = NULL;
    c_pstNetMsg_ = NULL;
}
