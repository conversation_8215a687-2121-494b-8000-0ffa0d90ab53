/*
 * @Author: your name
 * @Date: 2021-05-28 09:19:10
 * @LastEditTime: 2023-04-19 10:00:41
 * @LastEditors: wen <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /wanji_web/resources/func.js
 */

//#region 启动
//菜单栏相关 
//启web前禁用所有按钮和开关 
//window.setInterval(sendMsg,1000);
function defaultWeb() {
    //关闭窗口 
    clearAllWindow();
    //关闭开关
    clearAllBtn();
    //清除模式switch
    clearAllModel();

    //位姿状态 关闭
    viewPoseStatus(-1);

    //禁止所有按钮
    prohibitWeb();

    cViewPoseStatus = false;
}
window.onload = function disableAll() {
    // 重启后删除cookie
    localStorage.removeItem("workmode");
    defaultWeb();

    //初始化rviz
    initRviz();
}

function readAgvParam() {
    readAGVNetParam();

    UpdataPcapList();

    readAGVPcapName();

    readNetListfun();

    readAgvNet();

    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.QUERYCMD_CARCORRECT);
    addTail_(l_msg);
    c_slam.MSG_QUERYCMD_CARCORRECT.start(l_msg);

    l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.QUERYCMD_MAPCORRECT);
    addTail_(l_msg);
    c_slam.MSG_QUERYCMD_MAPCORRECT.start(l_msg);
}

function readSysParam() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.QUERYCMD_SLAMMAPINFO);
    addTail_(l_msg);
    c_slam.MSG_QUERYCMD_SLAMMAPINFO.start(l_msg);

    l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.QUERYCMD_MAPPINGMODE);
    addTail_(l_msg);
    c_slam.MSG_QUERYCMD_MAPPINGMODE.start(l_msg);

    getMotionMode();

    getLogPath();

    getTimeSource();

    getViewModel();
}

function clearStatus() {
    cViewPoseStatus = false;
    cViewMapStatus = false;
    cViewLidarStatus = false;
    cPlayPcapStatus = false;
    cRecordLidarStatus = false;
}

function clearAllBtn() {
    clearBtnValue("#viewerPose");
    clearBtnValue("#viewerPC");
    clearBtnValue("#viewerMap");
    clearBtnValue("#recordLidar");
    clearBtnValue("#playPcap");
    clearBtnValue("#ViewSlamState");

    setSetEnable("#SetSlamStateBtn", false);
}

function clearAllModel() {
    clearSlamModel();
    $("#lidarModel0").radiobutton("clear");
    $("#lidarModel1").radiobutton("clear");

}

function clearSlamModel() {
    $("#model0").radiobutton("clear");
    $("#model1").radiobutton("clear");
    $("#model2").radiobutton("clear");
    $("#model3").radiobutton("clear");
    $("#model4").radiobutton("clear");
}
function clearAllWindow() {
    $('#set0').dialog('close');
    $('#set1').dialog('close');
    $('#set2').dialog('close');
    $('#set3').dialog('close');
    $('#set4').dialog('close');
    $('#set5').dialog('close');
    $('#set6').dialog('close');
    $('#set7').dialog('close');
    $('#set8').dialog('close');
    $('#set9').dialog('close');
    $('#set10').dialog('close');
    $('#mac').dialog('close');
    $('#setUpdateMap').dialog('close');

    $('#saveMapWarn').dialog('close');
    $('#restartWarn').dialog('close');
    $('#recoveryWarn').dialog('close');
    $('#nosaveMapWarn').dialog('close');
    $('#changeModelWarn').dialog('close');
    $('#waitWarn').dialog('close');
    $('#recoverySuccWarn').dialog('close');

    $('#AddLidarTab').dialog('close');
    closeAllTabs();
}

//关闭所有的tab
function closeAllTabs() {
    var tiles = new Array();
    var tabs = $('#tabs4').tabs('tabs');
    var len = tabs.length;
    if (len > 0) {
        for (var j = 0; j < len; j++) {
            var a = tabs[j].panel('options').title;
            tiles.push(a);
        }
        for (var i = 0; i < tiles.length; i++) {
            $('#tabs4').tabs('close', tiles[tiles.length - i - 1]);
        }
    }
    tabOnce = 0;
    g_LaserId = 0;
    sel_laserId = 0;
    LaserUrl = "Laser.html?g_LaserId=" + 0;
}

function sendResetPoseUI() {
    l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_RESETPOSEUI);
    addTail_(l_msg);
    c_master.MSG_SETCMD_RESETPOSEUI.start(l_msg);
}

//查询驱动模式
function QueryDriveMode() {
    //获取驱动模式
    l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_DRIVERMODE);
    addTail_(l_msg);
    c_master.MSG_QUERYCMD_DRIVERMODE.start(l_msg);
}
//查询工作模式
function QuerySlamWorkMode() {
    //获取工作模式
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.QUERYCMD_WORKMODE);
    addTail_(l_msg);
    c_slam.MSG_QUERYCMD_WORKMODE.start(l_msg);
}

function QuerySlamMainInfo() {
    //获取工作模式
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.QUERYCMD_WORKMODE);
    addTail_(l_msg);
    c_slam.MSG_QUERYCMD_WORKMODE.start(l_msg);
    //显示点云按钮
    l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.QUERYCMD_VIEWLIDAR);
    addTail_(l_msg);
    c_slam.MSG_QUERYCMD_VIEWLIDAR.start(l_msg);
    //显示地图按钮
    l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.QUERYCMD_VIEWMAP);
    addTail_(l_msg);
    c_slam.MSG_QUERYCMD_VIEWMAP.start(l_msg);
    //重新获取播放状态
    l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_PLAYBAGSTATUS);
    addTail_(l_msg);
    c_master.MSG_QUERYCMD_PLAYBAGSTATUS.start(l_msg);
}

function QueryMasterMainInfo() {
    //获取驱动模式
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_DRIVERMODE);
    addTail_(l_msg);
    c_master.MSG_QUERYCMD_DRIVERMODE.start(l_msg);
    //获取录制状态
    l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_RECORDDATA);
    addTail_(l_msg);
    c_master.MSG_QUERYCMD_RECORDDATA.start(l_msg);
    //获取播放状态
    QueryPlayBagStatus();

    l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.QUERYCMD_PROCVERSION);
    addTail_(l_msg);
    c_slam.MSG_QUERYCMD_PROCVERSION.start(l_msg);
}

function QueryPlayBagStatus() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_PLAYBAGSTATUS);
    addTail_(l_msg);
    c_master.MSG_QUERYCMD_PLAYBAGSTATUS.start(l_msg);
}

//查询按钮状态
function QueryBntStatusSlam() {
    //显示点云按钮
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.QUERYCMD_VIEWLIDAR);
    addTail_(l_msg);
    c_slam.MSG_QUERYCMD_VIEWLIDAR.start(l_msg);
    //显示地图按钮
    l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.QUERYCMD_VIEWMAP);
    addTail_(l_msg);
    c_slam.MSG_QUERYCMD_VIEWMAP.start(l_msg);
}

function SetSlamState() {
    // 点过1次 未回复前不可再次点
    setSetEnable("#SetSlamStateBtn", false);
    $('#waitWarn').dialog('open');
    let l_SlamState = $("#ViewSlamState").switchbutton("options").checked;
    if (l_SlamState) {
        // 设置关闭slam
        let l_msg = PROTArray.slice();
        setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_SLAMCONTROL);
        l_msg[26] = 0;
        addTail_(l_msg);
        c_master.MSG_SETCMD_SLAMCONTROL.start(l_msg, 60000);
    }
    else {
        // 设置启动slam
        let l_msg = PROTArray.slice();
        setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_SLAMCONTROL);
        l_msg[26] = 1;
        addTail_(l_msg);
        c_master.MSG_SETCMD_SLAMCONTROL.start(l_msg, 60000);
    }
}

function sendResetSLAMCMD() {
    setSetEnable("#SetSlamStateBtn", false);
    let l_SlamState = $("#ViewSlamState").switchbutton("options").checked;
    if (l_SlamState) {
        console.log("重启SLAM");
        //已开启则设置重启
        let l_msg = PROTArray.slice();
        setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_SLAMCONTROL);
        l_msg[26] = 2;
        addTail_(l_msg);
        c_master.MSG_SETCMD_SLAMCONTROL.start(l_msg, 60000);
    }
    else {
        console.log("启动SLAM");
        // 设置启动slam
        let l_msg = PROTArray.slice();
        setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_SLAMCONTROL);
        l_msg[26] = 1;
        addTail_(l_msg);
        c_master.MSG_SETCMD_SLAMCONTROL.start(l_msg, 60000);
    }
}

//#endregion

//#region 顶部栏
function clearWebPose() {
    $("#viewerPoseX").numberbox("clear");
    $("#viewerPoseY").numberbox("clear");
    $("#viewerPoseZ").numberbox("clear");
    $("#viewerPoseA").numberbox("clear");
    $("#viewerPoseAGVX").numberbox("clear");
    $("#viewerPoseAGVY").numberbox("clear");
    $("#viewerPoseAGVZ").numberbox("clear");
    $("#viewerPoseAGVA").numberbox("clear");
}

function viewLaserPose(pPose) {
    $("#viewerPoseX").numberbox("setValue", pPose.PoseX);
    $("#viewerPoseY").numberbox("setValue", pPose.PoseY);
    $("#viewerPoseZ").numberbox("setValue", pPose.PoseZ);
    $("#viewerPoseA").numberbox("setValue", pPose.PoseA);
}
function viewAgvPose(pPose) {
    $("#viewerPoseAGVX").numberbox("setValue", pPose.PoseX);
    $("#viewerPoseAGVY").numberbox("setValue", pPose.PoseY);
    $("#viewerPoseAGVZ").numberbox("setValue", pPose.PoseZ);
    $("#viewerPoseAGVA").numberbox("setValue", pPose.PoseA);
}

function viewCalibPose(pPose) {
    for (let index = 0; index < 4; index++) {
        let l_Is = $("#EnLider" + index).switchbutton("options").checked;
        if (l_Is == 1) {
            $("#LidarX" + index).numberbox("setValue", pPose.PoseX);
            $("#LidarY" + index).numberbox("setValue", pPose.PoseY);
            $("#LidarZ" + index).numberbox("setValue", pPose.PoseZ);
            $("#LidarA" + index).numberbox("setValue", pPose.PoseA);
        }
    }
}
function viewPoseStatus(pValue) {
    // Initial | Continue | Virtual | Curb | Stop | invalid - 0 1 2 3 4
    switch (pValue) {
        //显示关闭
        case 0:
            $('#viewerPoseS').textbox("setValue", '初始位姿');
            break;
        case 1:
            $('#viewerPoseS').textbox("setValue", '正常');
            break;
        case 2:
            $('#viewerPoseS').textbox("setValue", '虚拟位姿');
            break;
        case 3:
            $('#viewerPoseS').textbox("setValue", 'Curb位姿');
            break;
        case 4:
            $('#viewerPoseS').textbox("setValue", '停止位姿');
            break;
        case 5:
            $('#viewerPoseS').textbox("setValue", '无效位姿');
            break;
        default:
            $('#viewerPoseS').textbox("setValue", '关闭显示');
    }
}
//#endregion

//#region 启动台
function writeBagStatus(time) {
    let h = parseInt(time / 60 / 60 % 24)
    h = h < 10 ? '0' + h : h
    let m = parseInt(time / 60 % 60)
    m = m < 10 ? '0' + m : m
    let s = parseInt(time % 60)
    s = s < 10 ? '0' + s : s
    let l_timeStr = "";
    if (h != 0)
        l_timeStr += h.toString() + "H ";
    if (m != 0)
        l_timeStr += m.toString() + "M ";
    // 超过1h后不在显示S
    if (h == 0 && s != 0)
        l_timeStr += s.toString() + "S ";
    console.log("recordT: ", l_timeStr, h, m, s);
    $("#recordDurT").textbox("setValue", l_timeStr);
}
//#endregion

//#region 空闲摸索配置
function readSet0() {
    set0DefaultValue();
    $('#set0').dialog('open');
}
function quitSet0Value() {
    $('#set0').dialog('close');
}

function okSet0Value() {
    if ($("#set0poseX").numberbox("isValid") &&
        $("#set0poseY").numberbox("isValid") &&
        $("#set0poseA").numberbox("isValid") &&
        $("#map0Name").textbox("isValid")) {
        $('#set0').dialog('close');
        read0InitPose();
        read0SlamModel();

        read0MapInfo();

        // 切换模式
        // executeChangeModel(4);
    }
    else {
        alert("无法确认，请检查格式，确认必选项已填");
    }
}
//#endregion


function readSet1() {
    set1DefaultValue();
    $('#set1').dialog('open');
}

//#region 网络配置
function readNetParamTab() {
    $('#netParamTab').dialog('open');
    readNetListfun();
}

function readNetListfun() {
    //查询net list
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_NETLIST);
    addTail_(l_msg);
    c_master.MSG_QUERYCMD_NETLIST.start(l_msg)
}

function quitAllNetParamTab() {
    $('#netParamTab').dialog('close');
}

function updataAllNetParamValue(id) {

    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_NETCFG);
    //读取雷达名称
    let l_NetCardName = $("#NetCardName" + id).textbox("getValue");
    let l_subscript = strToAscii(l_NetCardName, l_msg, 26)

    addTail_(l_msg);
    c_master.MSG_QUERYCMD_NETCFG.start(l_msg);
}
//#endregion
function readCalibTabParamTab() {
    let lModelValue = $("input[name='model']:checked").val();
    if (lModelValue != 3) {
        $('#getLocalbyWarn').dialog('open');
        return;
    }
    $('#CalibTab').dialog('open');


    IsNorth = 1;

    //查询雷达个数
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_PARAM, CMD_PARAM.QUERYCMD_LIDARNUM);
    addTail_(l_msg);
    c_param.MSG_QUERYCMD_LIDARNUM.start(l_msg)
}

function isEmpty(v) {
    switch (typeof v) {
        case 'undefined':
            return true;
        case 'string':
            if (v.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g, '').length == 0) return true;
            break;
        case 'boolean':
            if (!v) return true;
            break;
        case 'number':
            if (0 === v || isNaN(v)) return true;
            break;
        case 'object':
            if (null === v || v.length === 0) return true;
            for (var i in v) {
                return false;
            }
            return true;
    }
    return false;
}

function okSetAllNetParamValue(id) {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_NETCFG);
    //读取雷达名称
    let l_NetCardName = $("#NetCardName" + id).textbox("getValue");
    let l_subscript = strToAscii(l_NetCardName, l_msg, 26)

    let l_IsDHCP = $("#IsDHCP" + id).switchbutton("options").checked;
    if (l_IsDHCP) {
        l_subscript = intTo8Byte(1, l_msg, l_subscript);
    }
    else {
        l_subscript = intTo8Byte(0, l_msg, l_subscript);
    }

    let l_IsNetConnected = $("#IsConnectNet" + id).switchbutton("options").checked;
    if (l_IsNetConnected) {
        l_subscript = intTo8Byte(1, l_msg, l_subscript);
    }
    else {
        l_subscript = intTo8Byte(0, l_msg, l_subscript);
    }

    let l_Ip = $("#IpAddress" + id).textbox("getValue");
    if (!isEmpty(l_Ip)) {
        var Ips = l_Ip.split('.')
        for (let index = 0; index < Ips.length; index++) {
            l_msg[l_subscript++] = parseInt(Ips[index]);
        }
    }
    else
        for (let index = 0; index < 4; index++) {
            l_msg[l_subscript++] = parseInt(0);
        }


    addTail_(l_msg);
    c_master.MSG_SETCMD_NETCFG.start(l_msg);
}

function ResetAllNetParamValue(id) {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_NETRESET);
    //读取雷达名称
    let l_NetCardName = $("#NetCardName" + id).textbox("getValue");
    let l_subscript = strToAscii(l_NetCardName, l_msg, 26);
    addTail_(l_msg);
    c_master.MSG_SETCMD_NETRESET.start(l_msg);
}

function recoverySet1Value() {
}

function resetSet1Value() {
    set1Pose(lastPose);
}

function saveSet1() {
}

function quitSet1Value() {

    recoveryRawModel();
    $('#set1').dialog('close');

}

function set1Pose(pPose) {

    $("#set1poseX").numberbox("setValue", pPose.PoseX);
    $("#set1poseY").numberbox("setValue", pPose.PoseY);
    $("#set1poseZ").numberbox("setValue", pPose.PoseZ);
    $("#set1poseA").numberbox("setValue", pPose.PoseA);
}

function set0Pose(pPose) {

    $("#set0poseX").numberbox("setValue", pPose.PoseX);
    $("#set0poseY").numberbox("setValue", pPose.PoseY);
    $("#set0poseA").numberbox("setValue", pPose.PoseA);
}

function okSet1Value() {
    if ($("#set1poseX").numberbox("isValid") &&
        $("#set1poseY").numberbox("isValid") &&
        $("#set1poseZ").numberbox("isValid") &&
        $("#set1poseA").numberbox("isValid") &&
        $("#map1Name").textbox("isValid")) {
        $('#set1').dialog('close');

        setOffLineLidarData();

        readInitPose();
        readSlamModel("#mapModel");

        read1MapInfo();

        // 切换模式
        executeChangeModel(1);
    }
    else {
        alert("无法确认，请检查格式，确认必选项已填");
    }
}

function set1DefaultValue() {
    //获取radio选中按钮的值
    // var item = $('input[name=isUse1Mark]:checked').val();
    // console.log("item:",item);

    //选中某个按钮
    //$("input[name='isUse1Mark'][value=1]").attr("checked",true);

    let l_defaultPose = new _sPose();
    set1Pose(l_defaultPose);

    // $("#mark1Size").numberbox("setValue","0.04");
    // $("#map1Name").textbox("setValue","map");

}

function set0DefaultValue() {
    //获取radio选中按钮的值
    // var item = $('input[name=isUse1Mark]:checked').val();
    // console.log("item:",item);

    //选中某个按钮
    //$("input[name='isUse1Mark'][value=1]").attr("checked",true);

    let l_defaultPose = new _sPose();
    set0Pose(l_defaultPose);

    $("#map0Name").textbox("setValue", "map");
    $("#bag0NameList").textbox("setValue", "bag");

}

function set2DefaultValue() {
    //获取radio选中按钮的值
    // var item = $('input[name=isUse1Mark]:checked').val();
    // console.log("item:",item);

    //选中某个按钮
    //$("input[name='isUse1Mark'][value=1]").attr("checked",true);

    let l_defaultPose = new _sPose();
    set2Pose(l_defaultPose);

    //$("#map2NameList").textbox("setValue","map");

}

function set3DefaultValue() {
    //获取radio选中按钮的值
    // var item = $('input[name=isUse1Mark]:checked').val();
    // console.log("item:",item);

    //选中某个按钮
    //$("input[name='isUse1Mark'][value=1]").attr("checked",true);

    var l_defaultPose = new _sPose();
    set3Pose(l_defaultPose);

    // $("#map3NameList").textbox("setValue","map");

}

function setDefaultValueModel4() {
    var l_defaultPose = new _sPose();
    setPoseModel4(l_defaultPose);
}

function get1Pose(pPose) {
    pPose.PoseX = $("#set1poseX").numberbox("getValue");
    pPose.PoseY = $("#set1poseY").numberbox("getValue");
    pPose.PoseZ = $("#set1poseZ").numberbox("getValue");
    pPose.PoseA = $("#set1poseA").numberbox("getValue");
}

function getMarkSize(pID) {
    return $(pID).numberbox("getValue");
}

function readSlamModel(pID) {
    let slamModel16Str = $(pID).combobox("getValue").toString(16).toUpperCase().padLeft('0', 2);
    let ll = $(pID).combobox("getValue");
    console.log("readSlamModel: ", ll);

    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.SETCMD_MAPPINGMODE);
    intTo8Byte(ll, l_msg, l_msg.length);
    addTail_(l_msg);
    c_slam.MSG_SETCMD_MAPPINGMODE.start(l_msg);
}

function read1MapInfo() {
    let lMapName = $("#map1Name").textbox("getValue");
    let lMapSize = $("#map1Size").combobox("getValue");

    console.log("read1MapInfo: ", lMapName);
    console.log("read1MapInfo: ", lMapSize);
    addMapInfoArray(lMapName, lMapSize);
}

//#endregion

//#region 连续建图配置

function clearList(pID) {
    $(pID).combobox("clear");
}

function readSet2() {
    clearList("#map2NameList");
    set2DefaultValue();
    $('#set2').dialog('open');
};

function recoverySet2Value() {
};

function saveSet2() {
    get2Pose(lastPose);
};

function quitSet2Value() {
    recoveryRawModel();
    $('#set2').dialog('close');
};

function set2Pose(pPose) {
    $("#set2poseX").numberbox("setValue", pPose.PoseX);
    $("#set2poseY").numberbox("setValue", pPose.PoseY);
    $("#set2poseZ").numberbox("setValue", pPose.PoseZ);
    $("#set2poseA").numberbox("setValue", pPose.PoseA);
};

function get2Pose(pPose) {
    pPose.PoseX = $("#set2poseX").numberbox("getValue");
    pPose.PoseY = $("#set2poseY").numberbox("getValue");
    pPose.PoseZ = $("#set2poseZ").numberbox("getValue");
    pPose.PoseA = $("#set2poseA").numberbox("getValue");
};
function okSet2Value() {
    if ($("#set2poseX").numberbox("isValid") &&
        $("#set2poseY").numberbox("isValid") &&
        $("#set2poseZ").numberbox("isValid") &&
        $("#set2poseA").numberbox("isValid") &&
        $("#map2NameList").combobox("isValid")) {
        $('#set2').dialog('close');

        setOffLineLidarData();

        read2CurrPose();
        read2MapName("#map2NameList");
        readSlamModel("#mapModel2");
        //切换模式
        executeChangeModel(2);
    }
    else {
        alert("无法确认，请检查格式，确认必选项已填");
    }
}

function read2CurrPose() {
    let lPose = new _sPose();
    get2Pose(lPose);

    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.SETCMD_CURRPOSE);
    // 设定Pose为雷达位姿
    intTo8Byte(1, l_msg, l_msg.length);
    poseAdd16Array(lPose, l_msg);
    addTail_(l_msg);
    c_slam.MSG_SETCMD_CURRPOSE.start(l_msg);
}

function read2MapName(pID) {
    //获取设定的value 设置为了mapName
    let lStrMapValue = $(pID).combobox('getValue').toString();
    let lStrMapInfo = lStrMapValue.split(", / ");
    // console.log("lStrMapValue:",lStrMapValue);
    // console.log("lStrMapInfo:",lStrMapInfo[0]);
    addMapInfoArray(lStrMapInfo[0], lStrMapInfo[1]);
}
//#endregion

//#region 连续定位配置

function readSet3() {
    clearList("#map3NameList");
    set3DefaultValue();
    $('#set3').dialog('open');
};

function readSetModel4() {
    clearList("#mapNameListModel4");
    setDefaultValueModel4();
    $('#setUpdateMap').dialog('open');
};

function recoverySet3Value() {
};

function resetSet3Value() {
    set3Pose(lastPose);
}

function saveSet3() {
}

function quitSet3Value() {
    recoveryRawModel();
    $('#set3').dialog('close');
};

function quitSetModel4Value() {
    recoveryRawModel();
    $('#setUpdateMap').dialog('close');
};

function set3Pose(pPose) {
    $("#set3poseX").numberbox("setValue", pPose.PoseX);
    $("#set3poseY").numberbox("setValue", pPose.PoseY);
    $("#set3poseZ").numberbox("setValue", pPose.PoseZ);
    $("#set3poseA").numberbox("setValue", pPose.PoseA);
};

function setPoseModel4(pPose) {
    $("#setMode4poseX").numberbox("setValue", pPose.PoseX);
    $("#setMode4poseY").numberbox("setValue", pPose.PoseY);
    $("#setMode4poseZ").numberbox("setValue", pPose.PoseZ);
    $("#setMode4poseA").numberbox("setValue", pPose.PoseA);
};

function get3Pose(pPose) {
    pPose.PoseX = $("#set3poseX").numberbox("getValue");
    pPose.PoseY = $("#set3poseY").numberbox("getValue");
    pPose.PoseZ = $("#set3poseZ").numberbox("getValue");
    pPose.PoseA = $("#set3poseA").numberbox("getValue");
};

function getPoseModel3(pPose) {
    pPose.PoseX = $("#setMode4poseX").numberbox("getValue");
    pPose.PoseY = $("#setMode4poseY").numberbox("getValue");
    pPose.PoseZ = $("#setMode4poseZ").numberbox("getValue");
    pPose.PoseA = $("#setMode4poseA").numberbox("getValue");
};

function okSet3Value() {
    if ($("#set3poseX").numberbox("isValid") &&
        $("#set3poseY").numberbox("isValid") &&
        $("#set3poseZ").numberbox("isValid") &&
        $("#set3poseA").numberbox("isValid") &&
        $("#map3NameList").combobox("isValid")) {
        $('#set3').dialog('close');

        setOffLineLidarData();

        read3CurrPose();
        read3MapName("#map3NameList");
        readSlamModel("#mapModel3");
        // 切换模式
        executeChangeModel(3);
    }
    else {
        alert("无法确认，请检查格式，确认必选项已填");
    }
}

function okSetModel4Value() {
    if ($("#setMode4poseX").numberbox("isValid") &&
        $("#setMode4poseY").numberbox("isValid") &&
        $("#setMode4poseZ").numberbox("isValid") &&
        $("#setMode4poseA").numberbox("isValid") &&
        $("#mapNameListModel4").combobox("isValid")) {
        $('#setUpdateMap').dialog('close');

        setOffLineLidarData();

        readCurrPoseModel3();
        read3MapName("#mapNameListModel4");
        readSlamModel("#mapModel4");
        // 切换模式
        executeChangeModel(4);
    }
    else {
        alert("无法确认，请检查格式，确认必选项已填");
    }
}

function read3CurrPose() {
    let lPose = new _sPose();
    get3Pose(lPose);
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.SETCMD_CURRPOSE);
    // 设定Pose为雷达位姿
    intTo8Byte(1, l_msg, l_msg.length);
    poseAdd16Array(lPose, l_msg);
    addTail_(l_msg);
    c_slam.MSG_SETCMD_CURRPOSE.start(l_msg);
}

function readCurrPoseModel3() {
    let lPose = new _sPose();
    getPoseModel3(lPose);
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.SETCMD_CURRPOSE);
    // 设定Pose为雷达位姿
    intTo8Byte(1, l_msg, l_msg.length);
    poseAdd16Array(lPose, l_msg);
    addTail_(l_msg);
    c_slam.MSG_SETCMD_CURRPOSE.start(l_msg);
}

function read3MapName(pID) {
    //获取设定的value 设置为了mapName
    let lStrMapValue = $(pID).combobox('getValue').toString();
    let lStrMapInfo = lStrMapValue.split(", / ");
    console.log("lStrMapValue:", lStrMapValue);
    console.log("lStrMapInfo:", lStrMapInfo[0]);
    addMapInfoArray(lStrMapInfo[0], lStrMapInfo[1]);
}

function set3Load() {
    var lPose = new _sPose();
    get3Pose(lPose);
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.SETCMD_CURRPOSE);
    // 设定Pose为雷达位姿
    intTo8Byte(1, l_msg, l_msg.length);
    poseAdd16Array(lPose, l_msg);
    addTail_(l_msg);
    c_slam.MSG_SETCMD_CURRPOSE.start(l_msg);
}
//#endregion

function recoverySet4Value() {

};

function resetSet4Value() {

    set3Pose(lastPose);
}

//#region AGV安装配置
function fillMapCorrect(pArray) {
    let lCorrect = [];
    for (let i = 26; i < 50; i = i + 4) {
        lCorrect.push(((pArray[i] * Math.pow(2, 24) +
            pArray[i + 1] * Math.pow(2, 16) +
            pArray[i + 2] * Math.pow(2, 8) +
            pArray[i + 3]) | 0xFFFFFFFF00000000) * 0.001);
    }
    $("#mymapX").numberbox("setValue", lCorrect[0]);
    $("#mymapY").numberbox("setValue", lCorrect[1]);
    $("#mymapA").numberbox("setValue", lCorrect[2]);
    $("#tomapX").numberbox("setValue", lCorrect[3]);
    $("#tomapY").numberbox("setValue", lCorrect[4]);
    $("#tomapA").numberbox("setValue", lCorrect[5]);
}

function fillAgvCorrect(pArray) {
    let lCorrect = [];
    for (let i = 26; i < 42; i = i + 4) {
        lCorrect.push(((pArray[i] * Math.pow(2, 24) +
            pArray[i + 1] * Math.pow(2, 16) +
            pArray[i + 2] * Math.pow(2, 8) +
            pArray[i + 3]) | 0xFFFFFFFF00000000) * 0.001);
    }
    $("#MLidarX").numberbox("setValue", lCorrect[0]);
    $("#MLidarY").numberbox("setValue", lCorrect[1]);
    $("#MLidarZ").numberbox("setValue", lCorrect[2]);
    $("#MLidarA").numberbox("setValue", lCorrect[3]);
}

function parse(hex) {

    // 0000 03FC –> 1020
    // FFFF FF68 –> -152
    hex = parseInt(hex, 16);
    hex = hex | 0xFFFFFFFF00000000;
    // console.log("hex:=" + hex);

    return hex;
}

function fillAgvPose(pArray) {
    //立即回复
    if (pArray[27] == 00) {
        let lArrayPose = [];
        for (let i = 29; i < 45; i = i + 4) {
            let l_data = (pArray[i] * Math.pow(2, 24) +
                pArray[i + 1] * Math.pow(2, 16) +
                pArray[i + 2] * Math.pow(2, 8) +
                pArray[i + 3]) | 0xFFFFFFFF00000000;
            lArrayPose.push(l_data * 0.001);
        }
        let lPose = new _sPose();
        lPose.PoseX = lArrayPose[0];
        lPose.PoseY = lArrayPose[1];
        lPose.PoseZ = lArrayPose[2];
        lPose.PoseA = lArrayPose[3];

        //如果顶部栏启动显示
        if (cViewPoseStatus) {
            viewAgvPose(lPose);
            changeViewPoseState(pArray[28]);
        }
    }
}

function fillWebPose(pArray) {
    let lArrayPose = [];
    // 26-41 LaserPose
    for (let i = 26; i < 42; i = i + 4) {
        let l_data = (pArray[i] * Math.pow(2, 24) +
            pArray[i + 1] * Math.pow(2, 16) +
            pArray[i + 2] * Math.pow(2, 8) +
            pArray[i + 3]) | 0xFFFFFFFF00000000;
        lArrayPose.push(l_data * 0.001);
    }
    for (let i = 42; i < 58; i = i + 4) {
        let l_data = (pArray[i] * Math.pow(2, 24) +
            pArray[i + 1] * Math.pow(2, 16) +
            pArray[i + 2] * Math.pow(2, 8) +
            pArray[i + 3]) | 0xFFFFFFFF00000000;
        lArrayPose.push(l_data * 0.001);
    }
    let lPoseLaser = new _sPose();
    lPoseLaser.PoseX = lArrayPose[0];
    lPoseLaser.PoseY = lArrayPose[1];
    lPoseLaser.PoseZ = lArrayPose[2];
    lPoseLaser.PoseA = lArrayPose[3];

    let lPoseAgv = new _sPose();
    lPoseAgv.PoseX = lArrayPose[4];
    lPoseAgv.PoseY = lArrayPose[5];
    lPoseAgv.PoseZ = lArrayPose[6];
    lPoseAgv.PoseA = lArrayPose[7];

    if (cViewPoseStatus) {
        viewLaserPose(lPoseLaser);
        viewAgvPose(lPoseAgv);
        viewPoseStatus(pArray[58]);
    }
}

function fillSavePose(pArray) {
    if (pArray[26]) {
        let lArrayPose = [];
        // 27-42
        for (let i = 27; i < 43; i = i + 4) {
            let l_data = (pArray[i] * Math.pow(2, 24) +
                pArray[i + 1] * Math.pow(2, 16) +
                pArray[i + 2] * Math.pow(2, 8) +
                pArray[i + 3]) | 0xFFFFFFFF00000000;
            lArrayPose.push(l_data * 0.001);
        }

        let lPose = new _sPose();
        lPose.PoseX = lArrayPose[0];
        lPose.PoseY = lArrayPose[1];
        lPose.PoseZ = lArrayPose[2];
        lPose.PoseA = lArrayPose[3];

        set2Pose(lPose);
        set3Pose(lPose);
        setPoseModel4(lPose);
    }
    else
        alert("本地存储位姿获取失败！");
}

function fillLaserPose(pArray) {
    //立即回复
    if (pArray[27] == 00) {
        let lArrayPose = [];
        for (let i = 29; i < 45; i = i + 4) {
            let l_data = (pArray[i] * Math.pow(2, 24) +
                pArray[i + 1] * Math.pow(2, 16) +
                pArray[i + 2] * Math.pow(2, 8) +
                pArray[i + 3]) | 0xFFFFFFFF00000000;
            lArrayPose.push(l_data * 0.001);
        }
        let lPose = new _sPose();
        lPose.PoseX = lArrayPose[0];
        lPose.PoseY = lArrayPose[1];
        lPose.PoseZ = lArrayPose[2];
        lPose.PoseA = lArrayPose[3];

        set2Pose(lPose);
        set3Pose(lPose);
        //如果顶部栏启动显示
        if (cViewPoseStatus) {
            viewLaserPose(lPose);
            changeViewPoseState(pArray[28]);
        }
    }
}

function changeViewPoseState(pValue) {

}

function fillMapList(pArray) {
    let lOffset = 26;
    let lMapNum = pArray[lOffset++];
    let mapSingle, mapSingleID;
    let lPairMapInfo = [];
    for (let i = 0; i < lMapNum; i++) {
        let lMapName = asciiToStr(pArray, lOffset);
        lOffset += pArray[lOffset] + 1;
        let lMapSize = ascii16ByteToInt(pArray, lOffset) * 0.001;
        lOffset += 2;

        mapSingle = lMapName.toString() + ", / " + lMapSize.toString();
        mapSingleID = lMapName.toString() + ", / " + lMapSize.toString();
        lPairMapInfo.push({ "text": mapSingle, "id": mapSingleID });
    }

    $("#map3NameList").combobox("loadData", lPairMapInfo);
    $("#map2NameList").combobox("loadData", lPairMapInfo);
    $("#map6NameList").combobox("loadData", lPairMapInfo);
    $("#mapNameListModel4").combobox("loadData", lPairMapInfo);
}

// function fillOffLineDataList(pArray)
// {
//     let lOffset = 26;
//     let lBagNum = pArray[lOffset];
//     let bagSingle,bagSingleID;
//     let lPairDataInfo = [];

//     for(let i=0;i< lBagNum; i++)
//     {
//         let lBagName= asciiToStr(pArray, lOffset);
//         lOffset+= pArray[lOffset] + 1;
//         bagSingle = lBagName.toString();

//         lPairDataInfo.push({ "text": bagSingle, "id": bagSingle });

//     }

//     $("#pcap6NameList").combobox("loadData",lPairDataInfo);
//     $("#pcap7NameList").combobox("loadData",lPairDataInfo);
// }

function fillNetList(pArray) {
    let lOffset = 26;
    let lNetCardSetNum = 4;
    let bagSingle;
    let lNetCardNum = pArray[lOffset++];
    if (lNetCardNum > 4) {
        alert("网卡数量超过限制，仅显示部分");
    }

    l_ListNetName = [];
    let lPairDataInfo = [];
    for (let i = 0; i < lNetCardNum; i++) {
        let lNetCardName = asciiToStr(pArray, lOffset);
        lOffset += pArray[lOffset] + 1;
        bagSingle = lNetCardName.toString();

        let l_NetCardIdTemp = "#NetCardName" + i;
        $(l_NetCardIdTemp).textbox("setValue", lNetCardName);

        if (lNetCardSetNum > 0)
            l_ListNetName.push(lNetCardName);
        lNetCardSetNum--;

        lPairDataInfo.push({ "text": bagSingle, "id": bagSingle });
    }

    $("#AGVNetNameList").combobox("loadData", lPairDataInfo);
}

function fillNetCFG(pArray) {
    let lOffset = 26;
    let lNetCardName = asciiToStr(pArray, lOffset);
    lOffset += pArray[lOffset] + 1;

    let l_id;
    for (let index = 0; index < l_ListNetName.length; index++) {
        const element = l_ListNetName[index];
        if (element == lNetCardName) {
            l_id = index;
        }
    }

    let l_IsSet = pArray[lOffset++];
    if (l_IsSet == 1) {
        let l_IsDHCP = pArray[lOffset++];
        let l_IsNetConnected = pArray[lOffset++];
        let lIP = pArray[lOffset++].toString() + '.' +
            pArray[lOffset++].toString() + '.' +
            pArray[lOffset++].toString() + '.' +
            pArray[lOffset++].toString();

        setBtnValue("#IsDHCP" + l_id, l_IsDHCP);
        setBtnValue("#IsConnectNet" + l_id, l_IsNetConnected);
        $("#IpAddress" + l_id).textbox("setValue", lIP);
    }
    else
        alert("未检测到此网卡配置信息");

}




function fillBagStatus(pArray) {
    if (pArray[26]) {
        let lBagDurTime = pArray[27] * Math.pow(2, 24) +
            pArray[28] * Math.pow(2, 16) +
            pArray[29] * Math.pow(2, 8) +
            pArray[30];

        //确认web启动
        let webStatue = $("#openWeb").switchbutton("options").checked;
        if (webStatue) {
            writeBagStatus(lBagDurTime);
            setBtnValue("#recordLidar", true);
        }
    }
    else {
        writeBagStatus(0);
        setBtnValue("#recordLidar", false);
        c_master.MSG_QUERYCMD_RECORDDATA.stop();
    }
}

var c_nNum = 0;
function fillBagStatusOld(pArray) {
    //确认web启动
    let webStatue = $("#openWeb").switchbutton("options").checked;
    if (webStatue) {
        writeBagStatus(parseInt(pArray));
        let recordSign = $("#recordLidar").switchbutton("options").checked;
        if (!recordSign && c_nNum > 2) {
            setBtnValue("#recordLidar", true);
        }
        else if (!recordSign) {
            c_nNum++;
        }
    }
    else {
        writeBagStatus((0));
        setBtnValue("#recordLidar", false);
    }
}

function fillVERSION(pArray) {
    let lVersionInfo = asciiToStr(pArray, 26);
    let l_version = '  版本号:' + lVersionInfo + '  ';
    document.getElementById("wjversion").innerHTML = l_version;
}

function fillErrorCode(pArray) {
    let lErrorCodeInfo = asciiToStr(pArray, 26);
    if (lErrorCodeInfo == "NOERROR")
        document.getElementById("errcode").innerHTML = "  故障码:无异常 ";
    else {
        let l_errcode = '  故障码:' + lErrorCodeInfo + '  ';
        document.getElementById("errcode").innerHTML = l_errcode;
    }
    console.log("错误码：", lErrorCodeInfo);
}

function fillLidarState(pArray) {
    let l_state = pArray[26];
    if (l_state == 1) {
        $("#LidarState").textbox("setValue", "正常")
    }
    else {
        $("#LidarState").textbox("setValue", "异常")
    }
}
//#endregion

//#region agv参数配置
function readSet5() {
    $('#set5').dialog('open');
};

function recoverySet5Value() {

};

function resetSet5Value() {

}

function quitSet5Value() {
    $('#set5').dialog('close');
};

function readAgvNet() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_AGVNET);
    addTail_(l_msg);
    c_master.MSG_QUERYCMD_AGVNET.start(l_msg);
}

function fillAgvNet(pArray, l_subscript) {
    if (pArray[l_subscript] != 0) {
        let l_agvNetName = asciiToStr(pArray, l_subscript);
        $("#AGVNetNameList").combobox("setValue", l_agvNetName);
    }
}
function readAGVNetParam() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_PARAM, CMD_PARAM.QUERYCMD_AGVNETPARAM);
    addTail_(l_msg);
    c_param.MSG_QUERYCMD_AGVNETPARAM.start(l_msg);
}

function readAGVPcapName() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_AGVPCAPNAME);
    addTail_(l_msg);
    c_master.MSG_QUERYCMD_AGVPCAPNAME.start(l_msg);
}

function okSet5Value() {
    let lModelValue = $("input[name='model']:checked").val();
    if (lModelValue != 0) {
        $('#getStandbyWarn').dialog('open');
        $('#getStandbyWarn').dialog('center');
        return;
    }
    if ($("#agvIP").textbox("isValid") &&
        $("#agvPort").numberbox("isValid") &&
        $("#MLidarX").numberbox("isValid") &&
        $("#MLidarY").numberbox("isValid") &&
        $("#MLidarA").numberbox("isValid") &&
        $("#mymapX").numberbox("isValid") &&
        $("#mymapY").numberbox("isValid") &&
        $("#mymapA").numberbox("isValid") &&
        $("#tomapX").numberbox("isValid") &&
        $("#tomapY").numberbox("isValid") &&
        $("#tomapA").numberbox("isValid") &&
        $("#AGVNetNameList").combobox("isValid")) {
        $('#set5').dialog('close');
        let cLidarModel = $("input[name='lidarModel']:checked").val();
        if (cLidarModel == 1)
            SetAGVNet();
        setNetNameSelect();
        setPcapSelect();

        setMapCorrect();
        setCarCorrect();
        $('#saveParamResetSLAMWarn').dialog('open');
        $('#saveParamResetSLAMWarn').window('center');//使Dialog居中显示
    }
    else {
        alert("无法确认，请检查格式，确认必选项已填");
    }
}


function setPcapSelect() {
    if ($("#Pcap5NameList").combobox("isValid")) {
        let lBag = $("#Pcap5NameList").combobox("getValue").toString();

        let l_msg = PROTArray.slice();
        setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_AGVPCAPNAME);
        strToAscii(lBag, l_msg, l_msg.length);
        addTail_(l_msg);
        c_master.MSG_SETCMD_AGVPCAPNAME.start(l_msg);
    }
    else {
        alert("非法操作，选择文件无效");
    }
}

function setNetNameSelect() {
    if ($("#AGVNetNameList").combobox("isValid")) {
        let lBag = $("#AGVNetNameList").combobox("getValue").toString();

        let l_msg = PROTArray.slice();
        setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_AGVNET);
        strToAscii(lBag, l_msg, l_msg.length);
        addTail_(l_msg);
        c_master.MSG_SETCMD_AGVNET.start(l_msg);
    }
    else {
        alert("非法操作，选择网卡无效");
    }
}

function SetAGVNet() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_PARAM, CMD_PARAM.SETCMD_AGVNETPARAM);
    let l_subscript = 26;
    //设置AGV Ip
    let lIp = $("#agvIP").textbox("getValue").toString();
    let Ips = lIp.split('.')
    for (let index = 0; index < Ips.length; index++) {
        l_msg[l_subscript++] = parseInt(Ips[index])
    }

    let l_lidarPort = $("#agvPort").numberbox("getValue");
    l_subscript = intTo16Byte(l_lidarPort, l_msg, l_subscript);

    addTail_(l_msg);
    c_param.MSG_SETCMD_AGVNETPARAM.start(l_msg);
}

function fillPcapList(pArray) {
    let lOffset = 26;
    let lBagNum = pArray[lOffset++];
    let bagSingle, bagSingleID;
    let lPairDataInfo = [];

    for (let i = 0; i < lBagNum; i++) {
        let lBagName = asciiToStr(pArray, lOffset);
        lOffset += pArray[lOffset] + 1;
        bagSingle = lBagName.toString();

        lPairDataInfo.push({ "text": bagSingle, "id": bagSingle });

    }

    $("#Pcap5NameList").combobox("loadData", lPairDataInfo);
    $("#pcap6NameList").combobox("loadData", lPairDataInfo);
    $("#pcap7NameList").combobox("loadData", lPairDataInfo);
}

function fillAGVPcapName(pArray) {
    let lOffset = 26;
    let l_AGVPcapName = asciiToStr(pArray, lOffset);

    $("#Pcap5NameList").combobox("setValue", l_AGVPcapName);
}

function fillAGVIPinfo(pArray) {
    let lOffset = 26;
    let lIP = pArray[lOffset].toString() + '.' +
        pArray[lOffset + 1].toString() + '.' +
        pArray[lOffset + 2].toString() + '.' +
        pArray[lOffset + 3].toString();
    lOffset += 4;
    let lPort = pArray[lOffset] * Math.pow(2, 8) + pArray[lOffset + 1];

    $("#agvIP").textbox("setValue", lIP);
    $("#agvPort").numberbox("setValue", lPort);
}

function setCarCorrect() {
    let lagvx = $("#MLidarX").numberbox("getValue") * 1000;
    let lagvy = $("#MLidarY").numberbox("getValue") * 1000;
    let lagvz = $("#MLidarZ").numberbox("getValue") * 1000;
    let lagva = $("#MLidarA").numberbox("getValue") * 1000;

    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.SETCMD_CARCORRECT);
    intTo32Byte(lagvx, l_msg, l_msg.length);
    intTo32Byte(lagvy, l_msg, l_msg.length);
    intTo32Byte(lagvz, l_msg, l_msg.length);
    intTo32Byte(lagva, l_msg, l_msg.length);
    addTail_(l_msg);
    c_slam.MSG_SETCMD_CARCORRECT.start(l_msg);
}

function setMapCorrect() {
    let lmyx = $("#mymapX").numberbox("getValue") * 1000;
    let lmyy = $("#mymapY").numberbox("getValue") * 1000;
    let lmya = $("#mymapA").numberbox("getValue") * 1000;

    let ltox = $("#tomapX").numberbox("getValue") * 1000;
    let ltoy = $("#tomapY").numberbox("getValue") * 1000;
    let ltoa = $("#tomapA").numberbox("getValue") * 1000;

    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.SETCMD_MAPCORRECT);
    intTo32Byte(lmyx, l_msg, l_msg.length);
    intTo32Byte(lmyy, l_msg, l_msg.length);
    intTo32Byte(lmya, l_msg, l_msg.length);
    intTo32Byte(ltox, l_msg, l_msg.length);
    intTo32Byte(ltoy, l_msg, l_msg.length);
    intTo32Byte(ltoa, l_msg, l_msg.length);
    addTail_(l_msg);
    c_slam.MSG_SETCMD_MAPCORRECT.start(l_msg);
}

//#endregion

//#region 系统参数配置
function readSet9() {
    $('#set9').dialog('open');
};

function recoverySet9Value() {

};

function resetSet9Value() {

}

function quitSet9Value() {
    $('#set9').dialog('close');
};

function getMotionMode() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.QUERYCMD_MOTIONMODE);
    addTail_(l_msg);
    c_slam.MSG_QUERYCMD_MOTIONMODE.start(l_msg);
}

function getLogPath() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_LOGPATH);
    addTail_(l_msg);
    c_master.MSG_QUERYCMD_LOGPATH.start(l_msg);
}

function getTimeSource() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_PARAM, CMD_PARAM.QUERYCMD_CLOCKSOURCE);
    addTail_(l_msg);
    c_param.MSG_QUERYCMD_CLOCKSOURCE.start(l_msg);
}

function getViewModel() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.QUERYCMD_VIEWMODE);
    addTail_(l_msg);
    c_slam.MSG_QUERYCMD_VIEWMODE.start(l_msg);
}

function fillMovingMode(pArray) {
    let lMovingMode = 0;
    lMovingMode = pArray[26] + pArray[27] + pArray[28];
    if (lMovingMode > 1) lMovingMode = 1;
    // $("input[type='radio'][name='forceXYMoving'][value="+ lMovingMode +"]").prop('checked','true');

    //使用
    if (lMovingMode) {
        $("input[name='isPlaneMove'][value=1]").attr("checked", true);
    }
    else {
        $("input[name='isPlaneMove'][value=0]").attr("checked", true);
    }
}

function fillTimeSource(pArray) {
    let l_iTimeSource = pArray[26];
    let l_TimeType = "系统时钟";
    if (l_iTimeSource == 0) l_TimeType = "系统时钟";
    // if ( l_iTimeSource == 1) l_TimeType ="雷达时钟";
    $("#timeSource").combobox("setValue", l_TimeType);
}

function okSet9Value() {
    let lModelValue = $("input[name='model']:checked").val();
    if (lModelValue != 0) {
        $('#getStandbyWarn').dialog('open');
        $('#getStandbyWarn').dialog('center');
        return;
    }
    if ($("#timeSource").combobox("isValid") &&
        $("#viewModel").combobox("isValid") &&
        $("#logPath").textbox("isValid")) {
        $('#set9').dialog('close');
        setLogPath();
        setMovingMode();
        setTimeSource();
        setViewModel();
        $('#saveParamResetSLAMWarn').dialog('open');
        $('#saveParamResetSLAMWarn').window('center');//使Dialog居中显示
    }
    else {
        alert("无法确认，请检查格式，确认必选项已填");
    }
}

function setMovingMode() {
    let tmp = $('input[name=isPlaneMove]:checked').val();
    console.log("planeMove: ", tmp);
    if (tmp == 1 || tmp == 0) {
        let l_msg = PROTArray.slice();
        setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.SETCMD_MOTIONMODE);
        intTo8Byte(tmp, l_msg, l_msg.length);
        addTail_(l_msg);
        c_slam.MSG_SETCMD_MOTIONMODE.start(l_msg);
    }
    else
        alert("设置平面运动错误");
}
function setTimeSource() {
    let l_iTimeSource = $("#timeSource").combobox("getValue").toString(16).toUpperCase().padLeft('0', 2);
    console.log("setTimeSource: ", l_iTimeSource);
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_PARAM, CMD_PARAM.SETCMD_CLOCKSOURCE);
    intTo8Byte(parseInt(l_iTimeSource.substring(0, 2), 16), l_msg, l_msg.length);
    addTail_(l_msg);
    c_param.MSG_SETCMD_CLOCKSOURCE.start(l_msg);
}
function setViewModel() {
    let viewModel16Str = $("#viewModel").combobox("getValue").toString(16).toUpperCase().padLeft('0', 2);
    let ll = $("#viewModel").combobox("getValue");
    console.log("viewModel: ", ll, viewModel16Str);

    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.SETCMD_VIEWMODE);
    intTo8Byte(parseInt(viewModel16Str.substring(0, 2), 16), l_msg, l_msg.length);
    addTail_(l_msg);
    c_slam.MSG_SETCMD_VIEWMODE.start(l_msg);
}
function setLogPath() {
    let lLogPath = $("#logPath").textbox("getValue");
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_LOGPATH);
    strToAscii(lLogPath, l_msg, l_msg.length);
    addTail_(l_msg);
    c_master.MSG_SETCMD_LOGPATH.start(l_msg);
}
//#endregion



//#region 总览配置
function readSet6() {
    let lModelValue = $("input[name='model']:checked").val();
    if (lModelValue != 0) {
        $('#getStandbyWarn').dialog('open');
        $('#getStandbyWarn').dialog('center');
        return;
    }
    $('#set6').dialog('open');
};

function quitSet6Value() {
    $('#set6').dialog('close');
};

function fillSlamModel(pArray) {
    let lSlamModel = pArray[26];
    let lPairMapInfo = [];
    if (lSlamModel == 0) {
        lPairMapInfo.push({ "text": "室内场景", "id": 0 });
        $("#mapModel").combobox("setValue", "室内场景");
        $("#mapModel2").combobox("setValue", "室内场景");
        $("#mapModel4").combobox("setValue", "室内场景");
        $("#map9Model").combobox("setValue", "室内场景");
    }
    else if (lSlamModel == 1) {
        lPairMapInfo.push({ "text": "室外场景", "id": 1 });
        $("#mapModel").combobox("setValue", "室外场景");
        $("#mapModel2").combobox("setValue", "室外场景");
        $("#mapModel4").combobox("setValue", "室外场景");
        $("#map9Model").combobox("setValue", "室外场景");
    }
    else if (lSlamModel == 2) {
        lPairMapInfo.push({ "text": "复杂场景", "id": 2 });
        $("#mapModel").combobox("setValue", "复杂场景");
        $("#mapModel2").combobox("setValue", "复杂场景");
        $("#mapModel4").combobox("setValue", "复杂场景");
        $("#map9Model").combobox("setValue", "复杂场景");
    }
    $("#map9Model").combobox("loadData", lPairMapInfo);
    $("#mapModel").combobox("loadData", lPairMapInfo);
    $("#mapModel2").combobox("loadData", lPairMapInfo);
    $("#mapMode4").combobox("loadData", lPairMapInfo);
}

function deleteMapFunc() {
    if ($("#map6NameList").combobox("isValid")) {
        let lMapInfo = $("#map6NameList").combobox('getValue').toString();
        let lMapArray = lMapInfo.split(", / ");
        console.log("deleteMapFunc: ", lMapArray[0]);

        let l_msg = PROTArray.slice();
        setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_DELETEMAP);
        strToAscii(lMapArray[0], l_msg, l_msg.length);
        addTail_(l_msg);
        c_master.MSG_SETCMD_DELETEMAP.start(l_msg);

        sleep(1000);
        UpdataMapList();
    }
    else {
        alert("非法操作，请先选择需要删除的文件");
    }
}

function UpdataMapList() {
    //更新Maplist
    l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_MAPLIST);
    addTail_(l_msg);
    c_master.MSG_QUERYCMD_MAPLIST.start(l_msg);
}

function deletePcapfun() {
    if ($("#pcap6NameList").combobox("isValid")) {
        let lBag = $("#pcap6NameList").combobox("getValue").toString();

        let l_msg = PROTArray.slice();
        setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_DELETEPCAP);
        strToAscii(lBag, l_msg, l_msg.length);
        addTail_(l_msg);
        c_master.MSG_SETCMD_DELETEPCAP.start(l_msg);
        sleep(1000);
        UpdataPcapList()
    }
    else {
        alert("非法操作，请先选择需要删除的文件");
    }
}

function UpdataPcapList() {
    //更新pcaplist
    l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_PCAPLIST);
    addTail_(l_msg);
    c_master.MSG_QUERYCMD_PCAPLIST.start(l_msg);
}

//#endregion

//#region 离线PCAP
function setOffLineLidarData() {
    let cLidarModel = $("input[name='lidarModel']:checked").val();
    console.log("setOffLineLidarData: ", cLidarModel);

    if (cLidarModel == 0) {
        // $('#set7').dialog('open');
        alert("提示: 当前为离线驱动模式，须在雷达和AGV界面中正确配置Pcap包,等待工作模式切换后重启SLAM");
    }
    // else if(cLidarModel ==1)
    // {
    //     // $('#set8').dialog('open');
    //     alert("在线模式切换成功，重启SLAM生效");
    // }
}


function okSet7Value() {
    if ($("#pcap7NameList").combobox("isValid")) {
        $('#set7').dialog('close');

        // readOffLineDataName("#pcap7NameList");
        // netSetOffLineLidarData[26] = 1;
        // sendMsg(netSetOffLineLidarData);

        let l_msg = PROTArray.slice();
        setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_SLAVER.SETCMD_LASERPCAPNAME);
        let lStrMapValue = $("#pcap7NameList").combobox('getText');
        strToAscii(lStrMapValue, l_msg, 26);
        addTail_(l_msg);
        c_master.MSG_SETCMD_LASERPCAPNAME.start(l_msg);

        alert("请保存参数，重启SLAM生效");

        // if(cLidarModelChange)
        // {
        //     $('#OffLineSuccWarn').dialog('open');
        // }
        cLidarModelChange = 0;
    }
    else {
        alert("无法确认，请检查格式，确认必选项已填");
    }
}

function readOffLineDataName(pID) {
    let lStrMapValue = $(pID).combobox('getText');
    offLineLidatDataCodeToHex(strTo16Ascii(lStrMapValue), netSetOffLineLidarData);
}

function quitSet7Value() {
    $('#offLineLidarModelWarn').dialog('open');
}
//#endregion

//#region 离线BAG

function okSet8Value() {
    if ($("#bag8NameList").combobox("isValid")) {
        $('#set8').dialog('close');

        readOffLineDataName("#bag8NameList");
        netSetOffLineLidarData[26] = 2;
        sendMsg(netSetOffLineLidarData);

        if (cLidarModelChange) {
            sendMsg(netSaveParam);
            $('#OffLineSuccWarn').dialog('open');
        }
        cLidarModelChange = 0;
    }
    else {
        alert("无法确认，请检查格式，确认必选项已填");
    }
}

function quitSet8Value() {
    $('#offLineLidarModelWarn').dialog('open');
}

//#endregion

//#region 地图Info

// function read1MapInfo(pID)
// {
//     let lMapInfo = $(pID).textbox("getText");
//     let lMapArray =  lMapInfo.split(" / ");
//     console.log("read1MapInfo: ",lMapArray[0]);
//     console.log("read1MapInfo: ",lMapArray[1]);


//     addMapInfoArray(lMapArray[0],lMapArray[1]);
// }

function addMapInfoArray(pName, pSize) {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.SETCMD_SLAMMAPINFO);
    strToAscii(pName, l_msg, 26);
    intTo16Byte(pSize * 1000, l_msg, l_msg.length);
    addTail_(l_msg);
    c_slam.MSG_SETCMD_SLAMMAPINFO.start(l_msg);
}

function strTo16Ascii(pStr) {
    let lCode;
    for (let i = 0; i < pStr.length; i++) {
        if (i == 0) {
            lCode = pStr.charCodeAt(i).toString(16).toUpperCase();
        }
        else {
            lCode = lCode + ' ' + pStr.charCodeAt(i).toString(16).toUpperCase();
        }
    }
    return lCode;
}

function codeToHex(pStr, pArray) {
    //str 隔 空格 提取数组
    let lInd = 0;
    let lArray = pStr.split(' ');
    for (let i = lArray.length - 1; i >= 0; i--) {
        pArray[40 - lInd] = parseInt(lArray[i], 16);
        //Number("0x" + lArray[i]); 
        lInd++;
    }
    for (let j = 26; j <= 40 - lInd; j++) {
        pArray[j] = 0x20;
    }

}

function offLineLidatDataCodeToHex(pStr, pArray) {
    let lInd = 0;
    let lArray = pStr.split(' ');
    for (let i = lArray.length - 1; i >= 0; i--) {
        pArray[41 - lInd] = parseInt(lArray[i], 16);
        lInd++;
    }
    for (let j = 27; j <= 41 - lInd; j++) {
        pArray[j] = 0x20;
    }

}

function codeToHexPush(pStr, pArray, pInd) {
    let lArray = pStr.split(' ');

    for (let i = 0; i < lArray.length; i++) {
        pArray.splice(pInd + i, 0, parseInt(lArray[i], 16));
    }
}

function addMapName(pStr, pID) {

    let lArray = pStr.split(',');
    let lPairMapInfo = [];
    let mapSingle, mapSingleID;
    for (let i = 0; i < lArray.length; i = i + 2) {
        mapSingle = lArray[i].toString() + " 分辨率： " + lArray[i + 1].toString();
        mapSingleID = i;
        lPairMapInfo.push({ "text": mapSingle, "id": lArray[i].toString() });
    }

    // if(pID ==2){
    //     $("#map2NameList").combobox("loadData",lPairMapInfo);
    // }
    // else if(pID ==3)
    // {
    //     $("#map3NameList").combobox("loadData",lPairMapInfo);
    // }
}

function fillCurrMapInfo(pArray) {
    let l_offset = 26;
    // 存在地图
    if (pArray[l_offset++] == 1) {

        let lMapName = asciiToStr(pArray, l_offset);
        l_offset += pArray[l_offset] + 1;
        let lMapSize = ascii16ByteToInt(pArray, l_offset) * 0.001;

        $("#map9Name").textbox("setValue", lMapName.toString());
        $("#map9Size").numberbox("setValue", lMapSize);
    }
    else
        $("#map9Name").textbox("setValue", "地图文件不存在");
}

function fillLogPath(pArray) {
    let l_offset = 26;
    if (pArray[l_offset] != 0) {
        let lPath = asciiToStr(pArray, l_offset);
        $("#logPath").textbox("setValue", lPath.toString());
    }
}

//#endregion

//#region ROS相关

//创建发布数据
function pubRosData(ros_, topicName_, topicMessageType_, topicData_) {

    let l_pubTopic = new ROSLIB.Topic({
        ros: ros_,
        name: topicName_,
        messageType: topicMessageType_
    });

    let pubData = new ROSLIB.Message(topicData_);
    l_pubTopic.publish(pubData);

}

//#region 校验相关

function addArray(pNetArray, pStart, pStr) {
    for (let i = 0; i < pStr.length; i = i + 2) {
        pNetArray[pStart + i / 2] = parseInt(pStr.substring(i, i + 2), 16);
    }
}



function poseAdd16Array(pPose, pNetArray) {
    intTo32Byte(pPose.PoseX * 1000, pNetArray, pNetArray.length);
    intTo32Byte(pPose.PoseY * 1000, pNetArray, pNetArray.length);
    intTo32Byte(pPose.PoseZ * 1000, pNetArray, pNetArray.length);
    intTo32Byte(pPose.PoseA * 1000, pNetArray, pNetArray.length);
}

function ipAdd16Array(pStrIp, pStrPort) {
    let lIpStr = pStrIp.split('.');

    addArray(netIPInfo, 26, decToHex(lIpStr[0], 2));
    addArray(netIPInfo, 27, decToHex(lIpStr[1], 2));
    addArray(netIPInfo, 28, decToHex(lIpStr[2], 2));
    addArray(netIPInfo, 29, decToHex(lIpStr[3], 2));

    addArray(netIPInfo, 30, decToHex(lIpStr[0], 2));
    addArray(netIPInfo, 31, decToHex(lIpStr[1], 2));
    addArray(netIPInfo, 32, decToHex(lIpStr[2], 2));
    addArray(netIPInfo, 33, decToHex('1', 2));

    addArray(netIPInfo, 34, decToHex('255', 2));
    addArray(netIPInfo, 35, decToHex('255', 2));
    addArray(netIPInfo, 36, decToHex('255', 2));
    addArray(netIPInfo, 37, decToHex('0', 2));

    addArray(netIPInfo, 38, decToHex(pStrPort, 4));

    sendMsg(netIPInfo);
}
//#endregion

//#region 禁用操作台点击按钮相关
function disableBtn() {
    setBtnEnable("#recordLidar", false);
    setBtnEnable("#viewerPose", false);
    setBtnEnable("#viewerLidar", false);
    setBtnEnable("#viewerMap", false);
    setBtnEnable("#playPcap", false);
    setBtnEnable("#ViewSlamState", false);
    setSetEnable("#SetSlamStateBtn", false);
};

function enableBtn() {
    // setBtnEnable("#recordLidar", true);

    // setBtnEnable("#ViewSlamState",true);
    // setSetEnable("#SetSlamStateBtn",true);

    setBtnEnable("#playPcap", true);
};

function disableSet() {
    setSetEnable("#readSet", false);
    setSetEnable("#saveMap", false);
    setSetEnable("#restartButton", false);
    setSetEnable("#recoveryButton", false);
    setSetEnable("#resetPoseUI", false);
    setSetEnable("#saveSet", false);

    setSetEnable("#SetSlamStateBtn", false);

    // setSetEnable("#openSet1",false);
    // setSetEnable("#openSet2",false);
    // setSetEnable("#openSet3",false);

    setSetEnable("#openSet4", false);
    setSetEnable("#openSet5", false);
    setSetEnable("#openSet6", false);
    setSetEnable("#openSet7", false);
    setSetEnable("#openSet8", false);
    setSetEnable("#openSet9", false);
    setSetEnable("#openSet10", false);
};

function enableSet() {
    setSetEnable("#readSet", true);
    setSetEnable("#saveMap", true);
    setSetEnable("#restartButton", true);
    setSetEnable("#recoveryButton", true);
    setSetEnable("#resetPoseUI", true);
    setSetEnable("#saveSet", true);

    // setSetEnable("#openSet1",true);
    // setSetEnable("#openSet2",true);
    // setSetEnable("#openSet3",true);
    // setSetEnable("#openSet4",true);
    // setSetEnable("#openSet5",true);
    setSetEnable("#openSet6", true);
    setSetEnable("#openSet7", true);
    setSetEnable("#openSet8", true);
    setSetEnable("#openSet10", true);
    // setSetEnable("#openSet9",true);
};

function setTextReadOnly(id, status) {
    if (status == true)
        $(id).textbox('readonly',true);
    else
        $(id).textbox('readonly',false);
};

function setBtnEnable(id, status) {
    if (status == true)
        $(id).switchbutton("enable");
    else
        $(id).switchbutton("disable");
};

function setRadioBtnEnable(id, status) {
    if (status == true)
        $(id).radiobutton("enable");
    else
        $(id).radiobutton("disable");
};

function setSetEnable(id, status) {
    if (status == true)
        $(id).linkbutton("enable");
    else
        $(id).linkbutton("disable");
};

//启动台相关
function setBtnValue(id, value) {
    if (value)
        $(id).switchbutton("check");
    else
        $(id).switchbutton("uncheck");
};

//启动台相关
function clearBtnValue(id, value) {
    $(id).switchbutton("clear");
};

function sendBtnValue(id) {
    if (id == "openWeb")
        alert("openWeb");
    else if (id == "readSet")
        alert("readSet");
};
//#endregion

//#region 模式切换
function openModelSet(pValue) {
    // "StandBy", "InitialMap", "ContinusMap", "Location", "UpdateMap"
    if (pValue == 1) {
        readSet1();
    }
    else if (pValue == 2) {
        readSet2();
    }
    else if (pValue == 3) {
        readSet3();
    }
    else if (pValue == 0) {
        $('#changeModelWarn').dialog('open');
    }
    else if (pValue == 4) {
        readSetModel4();
    }
}
function disableModelChose() {
    disableSlamModelChose();
    enableDriverModelChose(0);
};

function disableSlamModelChose() {
    setRadioBtnEnable("#model0", "disable");
    setRadioBtnEnable("#model1", "disable");
    setRadioBtnEnable("#model2", "disable");
    setRadioBtnEnable("#model3", "disable");
    setRadioBtnEnable("#model4", "disable");
};

function enableSlamModelChose() {
    setRadioBtnEnable("#model0", "enable");
    setRadioBtnEnable("#model1", "enable");
    setRadioBtnEnable("#model2", "enable");
    setRadioBtnEnable("#model3", "enable");
    setRadioBtnEnable("#model4", "enable");
};

function enableModelChose() {
    enableSlamModelChose();
    enableDriverModelChose(1);
};

function changeModel() {
    //这个是用户设置的新模式
    let lModelValue = $("input[name='model']:checked").val();
    console.log("workmode: ", lModelValue);
    if (lModelValue == 0 ||
        lModelValue == 1 ||
        lModelValue == 2 ||
        lModelValue == 3 ||
        lModelValue == 4) {
        if (lModelValue != cLastModel) {
            //启动对应的设置窗口
            openModelSet(lModelValue);
        }
        //约束模式选择
        constraintModel(lModelValue);
    }
}

function changeLidarModel() {
    var lLidarModelValue = $("input[name='lidarModel']:checked").val();
    if (lLidarModelValue == 0 ||
        lLidarModelValue == 1) {
        if (lLidarModelValue != cLastLidarModel) {
            $('#changeLidarModelWarn').dialog('open');
        }
    }
}

function limitViewPoseBtn() {
    let lModelValue = $("input[name='model']:checked").val();
    // 定位/更新地图模式下可点击
    if (lModelValue == 3 ||
        lModelValue == 4) {
        setBtnEnable("#viewerPose", true);
    }
    else {
        setBtnValue("#viewerPose", false);
        // clearWebPose();
        setBtnEnable("#viewerPose", false);
        // console.log("viewPose: ", cViewPoseStatus);
    }
}

function recoveryRawModel() {
    if (cLastModel == 0) {
        setModelValue("#model0", "check");
    }
    else if (cLastModel == 1) {
        setModelValue("#model1", "check");
    }
    else if (cLastModel == 2) {
        setModelValue("#model2", "check");
    }
    else if (cLastModel == 3) {
        setModelValue("#model3", "check");
    }
    else if (cLastModel == 4) {
        setModelValue("#model4", "check");
    }

    constraintModel(cLastModel);
}

function recoveryRawLidarModel() {
    if (cLastLidarModel == 0) {
        setModelValue("#lidarModel0", "check");
    }
    else if (cLastLidarModel == 1) {
        setModelValue("#lidarModel1", "check");
    }
}

function executeChangeModel(pValue) {
    // 删除cookie文件 保证读到的是新的
    localStorage.removeItem("workmode");
    $('#waitWarn').dialog('open');
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.SETCMD_WORKMODE);
    intTo8Byte(pValue, l_msg, l_msg.length);
    addTail_(l_msg);
    c_slam.MSG_SETCMD_WORKMODE.start(l_msg, 100);
    console.log("切换工作模式: ", pValue);
}

function executeChangeLidarModel() {
    var lLidarModelValue = $("input[name='lidarModel']:checked").val();
    // console.log("lidarModel: ", lLidarModelValue);
    if (lLidarModelValue == 0)
        alert("提示: 切换离线模式须在雷达和AGV界面中完成离线Pcap包设置");

    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_DRIVERMODE);
    intTo8Byte(lLidarModelValue, l_msg, l_msg.length);
    addTail_(l_msg);
    c_master.MSG_SETCMD_DRIVERMODE.start(l_msg);

    cLidarModelChange = 1;
}

/*
用户点击后直接更新为新模式
如果没有收到模式切换回复成功msg，取消弹窗，通知失败，且回到以前的模式
如果收到成功msg,，更新cLastModel会开弹窗供用户更改参数，下述函数就进不去了
如果受到失败msg,
*/
function failSetModel(pRawValue) {
    var lNowValue = $("input[name='model']:checked").val();

    //1s后模式还是用户新设置的模式，1.msg回复成功，2.超时
    if (lNowValue == pRawValue) {
        //msg回复成功后cLastModel会更新为新值
        if (cLastModel == lNowValue) {
            return;
        }
        else if (cLastModel != lNowValue) {
            //超时未反应
        }
    }
    else if (lNowValue == cLastModel) {
        //失败，回到以前的模式
    }

    //取消弹窗
    viewEndWait();

}
function enableDriverModelChose(value) {
    if (value == 1) {
        $("#lidarModel1").radiobutton("enable");
        $("#lidarModel0").radiobutton("enable");
    }
    else {
        $("#lidarModel1").radiobutton("disable");
        $("#lidarModel0").radiobutton("disable");
    }
}

function constraintModel(value) {
    //空闲
    if (value == 0) {
        $("#model1").radiobutton("enable");
        $("#model2").radiobutton("enable");
        $("#model3").radiobutton("enable");
        $("#model4").radiobutton("enable");

    }
    else if (value == 1) {
        $("#model2").radiobutton("disable");
        $("#model3").radiobutton("disable");
        $("#model4").radiobutton("disable");
        $("#model0").radiobutton("enable");
    }
    else if (value == 2) {
        $("#model1").radiobutton("disable");
        $("#model3").radiobutton("disable");
        $("#model4").radiobutton("disable");
        $("#model0").radiobutton("enable");
    }
    else if (value == 3) {
        $("#model1").radiobutton("disable");
        $("#model2").radiobutton("disable");
        $("#model4").radiobutton("disable");
        $("#model0").radiobutton("enable");
    }
    //离线模式
    else if (value == 4) {
        $("#model1").radiobutton("disable");
        $("#model2").radiobutton("disable");
        $("#model3").radiobutton("disable");
        $("#model0").radiobutton("enable");
    }
};




function setModelValue(id_, value_) {
    $(id_).radiobutton(value_);
}


function resetMode(value_) {
    if (value_ == 0) {
        setModelValue("#model0", "check");
    }
    else if (value_ == 1) {
        setModelValue("#model1", "check");
    }
    else if (value_ == 2) {
        setModelValue("#model2", "check");
    }
    else if (value_ == 3) {
        setModelValue("#model3", "check");
    }
    else if (value_ == 4) {
        setModelValue("#model4", "check");
    }
}

function resetLidarMode(value_) {
    if (value_ == 0) {
        setModelValue("#lidarModel0", "check");
    }
    else if (value_ == 1) {
        setModelValue("#lidarModel1", "check");
    }

}
//#endregion

//#region 遮罩
function viewWait(pStr_, pHeight) {
    MaskUtil.mask(pStr_, pHeight);
}
function viewEndWait() {
    MaskUtil.unmask();//后台请求完后移除遮罩
}

function requestLoading(id_, str_) {
    var left = ($(window).outerWidth(true) - 190) / 2;
    var top = ($(window).height()) / 2 - 20;
    var height = $(window).height() * 2;
    $("<div class=\"datagrid-mask\"></div>").css({ display: "block", width: "100%", height: height }).appendTo(id_);
    $("<div class=\"datagrid-mask-msg\"></div>").html(str_).appendTo(id_).css({ display: "block", left: left, top: top });
}

function requestLoadEnd() {
    $(".datagrid-mask").remove();
    $(".datagrid-mask-msg").remove();
}
//#endregion

//#region 数据处理
function executeRecovery() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_PARAM, CMD_PARAM.SETCMD_LOADDEFAULTPARAM);
    addTail_(l_msg);
    c_param.MSG_SETCMD_LOADDEFAULTPARAM.start(l_msg);
    //viewWait("参数恢复出厂...");
}

function executeReStart() {
    // let l_msg = PROTArray.slice();
    // setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_SLAMCONTROL);
    // addTail_(l_msg);
    // c_master.MSG_SETCMD_SLAMCONTROL.start(l_msg);
    // alert("重启中，请勿操作...");

    $('#restartWarn').dialog('close');
    alert("此功能暂未开放，请手动重启");
}

function executeSaveMap() {

    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.SETCMD_SAVEMAP);
    addTail_(l_msg);
    c_slam.MSG_SETCMD_SAVEMAP.start(l_msg);

    //viewWait("保存地图中...");
}

// function executeSaveLidar(pStatus)
// {
//     //启动
//     if(pStatus)
//     {
//         sendMsg(netSaveLidar);                  
//         //viewWait("开启雷达录制中...");

//     }
//     //停止
//     else
//     {
//         writeBagStatus((0));
//         sendMsg(netNoSaveLidar);                  
//         //viewWait("关闭雷达录制中...");
//     } 
// }

function toYaw(x, y, z, w) {
    // yaw (z-axis rotation)
    siny_cosp = +2.0 * (w * z + x * y);
    cosy_cosp = +1.0 - 2.0 * (y * y + z * z);
    return Math.atan2(siny_cosp, cosy_cosp) * 180.0 / Math.PI;
}

function fillViewPoseOld(lMsgArray) {
    let l_viewPose = new _sPose();
    l_viewPose.PoseX = parseInt(lMsgArray[1]) * 0.001;
    l_viewPose.PoseY = parseInt(lMsgArray[2]) * 0.001;
    l_viewPose.PoseZ = parseInt(lMsgArray[3]) * 0.001;
    l_viewPose.PoseA = parseInt(lMsgArray[4]) * 0.001;

    set2Pose(l_viewPose);
    set3Pose(l_viewPose);
    if (cViewPoseStatus) {
        c_GetPose=false;
        viewLaserPose(l_viewPose);
        c_GetPose=true;
        // viewPoseStatus((1));
    }
}

function fillMapList2(pArray) {
    let mapSingle, mapSingleID;
    let lPairMapInfo = [];

    for (let i = 1; i < pArray.length; i++) {
        let lMsgArray = pArray[i].split(".pcd");
        //临时绑定分辨率0.05
        mapSingle = lMsgArray + ", / " + "0.05";
        mapSingleID = lMsgArray + ", / " + "0.05";
        lPairMapInfo.push({ "text": mapSingle, "id": mapSingleID });
    }

    $("#map3NameList").combobox("loadData", lPairMapInfo);
    $("#map2NameList").combobox("loadData", lPairMapInfo);
    $("#map6NameList").combobox("loadData", lPairMapInfo);
}


function fillPcapList2(pArray) {
    let lBagNum = pArray[26];
    let bagSingle, bagSingleID;
    let lPairBagInfo = [];

    for (let i = 1; i < pArray.length; i++) {
        let lMsgArray = pArray[i].split(".pcapng");
        bagSingle = lMsgArray;

        lPairBagInfo.push({ "text": bagSingle, "id": bagSingle });
    }

    $("#pcap6NameList").combobox("loadData", lPairBagInfo);
    $("#pcap7NameList").combobox("loadData", lPairBagInfo);

}

//#endregion

//#region 添加雷达

function readSet4() {
    IsSet4 = 1;
    if (tabOnce != 0) {
        $('#set4').dialog('open');
        return;
    }
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_PARAM, CMD_PARAM.QUERYCMD_LIDARNUM);
    addTail_(l_msg);
    c_param.MSG_QUERYCMD_LIDARNUM.start(l_msg)
};


function fillAllLidarName(pArray) {
    if (IsSet4 == 1) {
        tabOnce = tabOnce + 1;
        let l_subscript = 26;
        let l_NumLidar = pArray[l_subscript++];
        if (l_NumLidar <= 2) {
            if (1 == tabOnce) {

                for (let i = 0; i < l_NumLidar; i++) {
                    let l_NumName = pArray[l_subscript];
                    let LidarName = asciiToStr(pArray, l_subscript);
                    l_subscript = l_subscript + 1;
                    l_subscript = l_subscript + l_NumName;
                    LaserUrl = "Laser.html?g_LaserId=" + LidarName;
                    var content = '<iframe scrolling="auto" frameborder="0"  src="' + LaserUrl + '" style="width:100%;height:99%;"></iframe>'
                    $('#tabs4').tabs('add', {
                        title: LidarName,
                        content: content,
                        closable: true
                    });
                }
            }
            $('#set4').dialog('open');
        }
        else {
            alert("雷达配置错误，请重新配置user.yaml文件中雷达参数")
        }

    }
    if (IsNorth == 1) {
        let l_subscript = 26;
        let l_NumLidar = pArray[l_subscript++];
        if (l_NumLidar > 2) {
            $("#lidarNumWarn").dialog('open');
        }
        c_ListCalibLidarName = [];
        for (let i = 0; i < l_NumLidar; i++) {
            if (i > 2) {
                continue;
            }
            let l_NumName = pArray[l_subscript];
            let LidarName = asciiToStr(pArray, l_subscript);
            l_subscript = l_subscript + 1;
            l_subscript = l_subscript + l_NumName;

            let l_LdarNameIdTemp = "#LidarName" + i;
            $(l_LdarNameIdTemp).textbox("setValue", LidarName);
            c_ListCalibLidarName.push(LidarName);

            //查询雷达使能状态
            l_msg = PROTArray.slice();
            setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.QUERYCMD_LASERENABLE);
            strToAscii(LidarName, l_msg, 26);
            // intTo8Byte(0, l_msg, l_subscript++);
            addTail_(l_msg);
            c_device.MSG_QUERYCMD_LASERENABLE.start(l_msg);
        }
    }
    IsNorth = 0;
    IsSet4 = 0;
}

//region 多雷达标定
function enbleAllLidar() {
    for (let index = 0; index < c_ListCalibLidarName.length; index++) {
        setBtnValue("#EnLider" + index, 1);
    }
}

function PoseSet(index) {
    if (cViewPoseStatus != true) {
        alert("未打开实时位姿");
        return;
    }

    if(c_ListCalibLidarName.length!=2)
    {
        alert("雷达个数不满足要求");
        return;
    }

    if (cViewLidarPose1 == true && cViewLidarPose0 == true) 
    {
        alert("请只开启一个雷达使能进行位姿设定");
        return;
    }

    if(c_GetPose!=true)
    {
        $("#GetPoseWarn").dialog('open');
        return;
    }

    if ($("#viewerPoseX").numberbox("isValid") &&
        $("#viewerPoseY").numberbox("isValid") &&
        $("#viewerPoseZ").numberbox("isValid") &&
        $("#viewerPoseA").numberbox("isValid")) {
        let l_msg = PROTArray.slice();
        setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.SETCMD_MULLASERCALIBRATIONINI);
        //读取雷达名称
        let l_subscript = strToAscii(c_ListCalibLidarName[index], l_msg, 26);
        let l_lidarX = $("#viewerPoseX").numberbox("getValue") * 1000;
        let l_lidarY = $("#viewerPoseY").numberbox("getValue") * 1000;
        let l_lidarZ = $("#viewerPoseZ").numberbox("getValue") * 1000;
        let l_lidarA = $("#viewerPoseA").numberbox("getValue") * 1000;
        l_subscript = intTo32Byte(l_lidarX, l_msg, l_subscript);
        l_subscript = intTo32Byte(l_lidarY, l_msg, l_subscript);
        l_subscript = intTo32Byte(l_lidarZ, l_msg, l_subscript);
        l_subscript = intTo32Byte(l_lidarA, l_msg, l_subscript);
        addTail_(l_msg);
        console.log(c_ListCalibLidarName[index], l_msg);
        c_device.MSG_SETCMD_MULLASERCALIBRATIONINI.start(l_msg);
    }
}

function openLidarCalibIndex() {
    setStartCalibIndex(true);
}

function closeLidarCalibIndex() {
    setStartCalibIndex(false);

    // sleep(100);
    // //安装位置更新
    // readLidarInstallXYZIndex(2);

    // //安装角度更新
    // readLidarInstallANGIndex(2);
}

function applyLidarCalibResultInex(index) {
    if(c_ListCalibLidarName.length!=2)
    {
        alert("雷达个数不满足要求");
        return;
    }
    if (cViewLidarPose1 == true && cViewLidarPose0 == true)
    {
        
        if (index < c_ListCalibLidarName.length) {
            if ($("#LidarX" + index).numberbox("isValid")
                && $("#LidarY" + index).numberbox("isValid")
                && $("#LidarZ" + index).numberbox("isValid")
                && $("#LidarAngleR" + index).numberbox("isValid")
                && $("#LidarAngleP" + index).numberbox("isValid")
                && $("#LidarAngleY" + index).numberbox("isValid"))
                {
                    // setLidarInstallXYZfunIndex(2, index);
                    let seq=2
                    
                    let l_msg = PROTArray.slice();
                    setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.SETCMD_LASERINSTALLXYZ);
                    //读取雷达名称
                    let l_subscript = strToAscii(c_ListCalibLidarName[index], l_msg, 26)
                    // 读取安装组
                    l_subscript = intTo8Byte(seq, l_msg, l_subscript);
                    //设置安装XYZ
                    let l_lidarX = $("#LidarX" + index).numberbox("getValue") * 1000;
                    let l_lidarY = $("#LidarY" + index).numberbox("getValue") * 1000;
                    let l_lidarZ = $("#LidarZ" + index).numberbox("getValue") * 1000;
                    l_subscript = intTo32Byte(l_lidarX, l_msg, l_subscript);
                    l_subscript = intTo32Byte(l_lidarY, l_msg, l_subscript);
                    l_subscript = intTo32Byte(l_lidarZ, l_msg, l_subscript);
                    addTail_(l_msg);
                    
                    // setLidarInstallANGfunIndex(2, index);

                    let l_msg2 = PROTArray.slice();
                    setCMDID(l_msg2, CMDTYPE.CMD_DEVICE, CMD_DEVICE.SETCMD_LASERINSTALLANGLE);
                    //读取雷达名称
                    let l_subscript2 = strToAscii(c_ListCalibLidarName[index], l_msg2, 26);
                    // 读取安装组
                    l_subscript2 = intTo8Byte(seq, l_msg2, l_subscript2);
                    let l_lidarR2 = 0;
                    let l_lidarP2 = 0;
                    let l_lidarY2 = 0;
                    if (seq == 2) {
                        //设置安装RPY
                        l_lidarR2 = $("#LidarAngleR" + index).numberbox("getValue") * 1000;
                        l_lidarP2 = $("#LidarAngleP" + index).numberbox("getValue") * 1000;
                        l_lidarY2 = $("#LidarAngleY" + index).numberbox("getValue") * 1000;
                    }
                    l_subscript2 = intTo32Byte(l_lidarR2, l_msg2, l_subscript2);
                    l_subscript2 = intTo32Byte(l_lidarP2, l_msg2, l_subscript2);
                    l_subscript2 = intTo32Byte(l_lidarY2, l_msg2, l_subscript2);
                    addTail_(l_msg2);

                    //关闭多雷达标定和定时查询标定结果
                    setStartCalibIndex(false);
                    sleep(100);

                    c_device.MSG_SETCMD_LASERINSTALLXYZ.start(l_msg);
                    c_device.MSG_SETCMD_LASERINSTALLANGLE.start(l_msg2);
                }
            else
            {
                alert("标定数据格式不符合");
            }
        }
        else {
            alert("雷达个数不满足要求");
        }
    }
    else{
        alert("请将雷达全部使能");
    }

    // sleep(100);
    // //安装位置更新
    // readLidarInstallXYZIndex(2);
    // //安装角度更新
    // readLidarInstallANGIndex(2);
}

function setLidarInstallXYZfunIndex(seq, index) {
    if ($("#LidarX" + index).numberbox("isValid")
        && $("#LidarY" + index).numberbox("isValid")
        && $("#LidarZ" + index).numberbox("isValid")) {
        let l_msg = PROTArray.slice();
        setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.SETCMD_LASERINSTALLXYZ);
        //读取雷达名称
        let l_subscript = strToAscii(c_ListCalibLidarName[index], l_msg, 26)
        // 读取安装组
        l_subscript = intTo8Byte(seq, l_msg, l_subscript);
        //设置安装XYZ
        let l_lidarX = $("#LidarX" + index).numberbox("getValue") * 1000;
        let l_lidarY = $("#LidarY" + index).numberbox("getValue") * 1000;
        let l_lidarZ = $("#LidarZ" + index).numberbox("getValue") * 1000;
        l_subscript = intTo32Byte(l_lidarX, l_msg, l_subscript);
        l_subscript = intTo32Byte(l_lidarY, l_msg, l_subscript);
        l_subscript = intTo32Byte(l_lidarZ, l_msg, l_subscript);
        addTail_(l_msg);
        c_device.MSG_SETCMD_LASERINSTALLXYZ.start(l_msg);
    }
}

function setLidarInstallANGfunIndex(seq, index) {
    if ($("#LidarAngleR" + index).numberbox("isValid")
        && $("#LidarAngleP" + index).numberbox("isValid")
        && $("#LidarAngleY" + index).numberbox("isValid")) {
        let l_msg = PROTArray.slice();
        setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.SETCMD_LASERINSTALLANGLE);
        //读取雷达名称
        let l_subscript = strToAscii(c_ListCalibLidarName[index], l_msg, 26);
        // 读取安装组
        l_subscript = intTo8Byte(seq, l_msg, l_subscript);
        let l_lidarR = 0;
        let l_lidarP = 0;
        let l_lidarY = 0;
        if (seq == 2) {
            //设置安装RPY
            l_lidarR = $("#LidarAngleR" + index).numberbox("getValue") * 1000;
            l_lidarP = $("#LidarAngleP" + index).numberbox("getValue") * 1000;
            l_lidarY = $("#LidarAngleY" + index).numberbox("getValue") * 1000;
        }
        l_subscript = intTo32Byte(l_lidarR, l_msg, l_subscript);
        l_subscript = intTo32Byte(l_lidarP, l_msg, l_subscript);
        l_subscript = intTo32Byte(l_lidarY, l_msg, l_subscript);
        addTail_(l_msg);
        c_device.MSG_SETCMD_LASERINSTALLANGLE.start(l_msg);
    }
}

function setStartCalibIndex(on) {
    if(c_ListCalibLidarName.length!=2)
    {
        alert("雷达个数不满足要求");
        return;
    }

    if (cViewLidarPose1 == true && cViewLidarPose0 == true) {
        // 设置功能下发
        let l_msg = PROTArray.slice();
        setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.SETCMD_MULLASERCALIBRATION);
        //读取雷达名称
        let l_subscript = strToAscii(c_ListCalibLidarName[1], l_msg, 26);
        if (on == true)
            l_msg[l_subscript++] = 1;
        else
            l_msg[l_subscript++] = 0;
        addTail_(l_msg);
        c_device.MSG_SETCMD_MULLASERCALIBRATION.start(l_msg);
    }
    else {
        alert("请将雷达全部使能");
    }

}

function getCalibStateIndex(on, p_name) {
    if (on == true) {
        let l_msg = PROTArray.slice();
        setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.QUERYCMD_MULLASERCALIBRATION);
        //读取雷达名称
        l_subscript = strToAscii(p_name, l_msg, 26);
        addTail_(l_msg);
        c_device.MSG_QUERYCMD_MULLASERCALIBRATION.start(l_msg);
    }
    else
        c_device.MSG_QUERYCMD_MULLASERCALIBRATION.stop();
}

function fillCalibResultIndex(pArray, l_subscript) {
    let l_temp = 26;
    let l_NameSize = pArray[l_temp];
    let l_LidarName = asciiToStr(pArray, l_temp);
    for (let index = 0; index < c_ListCalibLidarName.length; index++) {
        if (c_ListCalibLidarName[index] == l_LidarName) {
            // 状态
            let l_bIsStartCalib = pArray[l_subscript++];
            // 次数
            let l_CalibTimes = pArray[l_subscript++] * Math.pow(2, 8) + pArray[l_subscript++];
            if (!l_bIsStartCalib) {
                //未标定
                $("#calibrationStateIndex" + index).textbox("setValue", "标定状态：未启动");
                // 重新读取安装参数
                // readLidarInstallXYZfun(2);
                // readLidarInstallANGfun(2);
                return;
            }
            let l_start = l_subscript;
            let lRes = [];
            for (l_subscript; l_subscript < l_start + 12 * 4; l_subscript = l_subscript + 4) {
                lRes.push(((pArray[l_subscript] * Math.pow(2, 24) +
                    pArray[l_subscript + 1] * Math.pow(2, 16) +
                    pArray[l_subscript + 2] * Math.pow(2, 8) +
                    pArray[l_subscript + 3]) | 0xFFFFFFFF00000000) * 0.001);
            }
            console.log(lRes[0], lRes[2], lRes[4]);
            let lState;
            if (lRes[0] == 0 && lRes[2] == 0 && lRes[4] == 0 && l_CalibTimes==0)
            {
                lState = "初始化标定，保持静止10秒";
                setSetEnable("#PoseSet0",false);
                setBtnEnable("#EnLider0",false);
                setSetEnable("#PoseSet1",false);
                setBtnEnable("#EnLider1",false);
                setSetEnable("#applyCalibBtn1",false);
            }
            else {
                let lTRes = Math.sqrt(lRes[1] * lRes[1] + lRes[3] * lRes[3]) * 100;
                let lRRes = Math.sqrt(lRes[11] * lRes[11]);
                // 初始化完成等待运行
                if (l_CalibTimes <= 1) {
                    lState = "已完成初始化,请开始进行匀低速运动!";
                    setSetEnable("#PoseSet0",false);
                    setBtnEnable("#EnLider0",false);
                    setSetEnable("#PoseSet1",false);
                    setBtnEnable("#EnLider1",false);
                    setSetEnable("#applyCalibBtn1",false);
                }
                // 样本数量不足
                else if (l_CalibTimes < 500)
                {
                    lState = "请保持匀低速运动,当前精度:" + lTRes.toFixed(1) + "[cm], " + lRRes.toFixed(1) + "[deg]";
                    setSetEnable("#PoseSet0",false);
                    setBtnEnable("#EnLider0",false);
                    setSetEnable("#PoseSet1",false);
                    setBtnEnable("#EnLider1",false);
                    setSetEnable("#applyCalibBtn1",false);
                }
                // 样本数量足够
                else {
                    setSetEnable("#PoseSet0",true);
                    setBtnEnable("#EnLider0",true);
                    setSetEnable("#PoseSet1",true);
                    setBtnEnable("#EnLider1",true);
                    setSetEnable("#applyCalibBtn1",true);
                    // 精度低
                    if (lTRes > 2 || lRRes > 0.5)
                    {
                        lState = "标定精度低! 精度:" + lTRes.toFixed(1) + "[cm], " + lRRes.toFixed(1) + "[deg]";
                    }
                    // 精度正常
                    else {
                        lState = "标定完成! 请应用结果,精度:" + lTRes.toFixed(1) + "[cm], " + lRRes.toFixed(1) + "[deg]";
                    }
                }
            }

            $("#calibrationStateIndex" + index).textbox("setValue", lState);
            // console.log("l_CalibTimes: ", l_CalibTimes);
            // console.log("lState: ", lState);

            $("#LidarX" + index).numberbox("setValue", lRes[0]);
            $("#LidarY" + index).numberbox("setValue", lRes[2]);
            $("#LidarZ" + index).numberbox("setValue", lRes[4]);
            $("#LidarAngleR" + index).numberbox("setValue", lRes[6]);
            $("#LidarAngleP" + index).numberbox("setValue", lRes[8]);
            $("#LidarAngleY" + index).numberbox("setValue", lRes[10]);
            break;
        }
    }


}
//#endregion 多雷达标定

function AddLidar() {
    let cLidarModel = $("input[name='lidarModel']:checked").val();
    let lModelValue = $("input[name='model']:checked").val();
    if (cLidarModel == 0 || lModelValue != 0) {
        alert("无效操作，新增雷达请切换至雷达模式-[在线模式] 工作模式-[空闲模式] ！");
        return false;
    }
    initAddLidarValue();
    $('#AddLidarTab').dialog('open');
}

function initAddLidarValue() {
    $('#AddLidarIP').textbox("setValue", '************');
    $('#AddPcIP').textbox("setValue", '************');
    $("#AddLidarPort").numberbox("setValue", 3333);
    $("#AddPcPort").numberbox("setValue", 3001);
}

function okSetAddLidarTab() {
    if ($("#AddLidarName").numberbox("isValid") &&
        $("#AddLidarIP").numberbox("isValid") &&
        $("#AddPcIP").numberbox("isValid") &&
        $("#AddLidarPort").numberbox("isValid") &&
        $("#AddPcPort").numberbox("isValid") &&
        $("#AddlidarType").combobox("isValid")) {
        g_LaserId = $("#AddLidarName").textbox("getValue");
        if ($('#tabs4').tabs('exists', g_LaserId)) {
            $('#tabs4').tabs('select', g_LaserId);
            alert("雷达名称已存在，请更换");
            return 0;
        }
        sendAddLaserMsg(g_LaserId);
    }
    else {
        alert("格式错误，请检查！");
    }
}

function quitAddLidarTab() {
    $('#AddLidarTab').dialog('close');
}

function sendAddLaserMsg(title) {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_ADDLASER);
    //设置雷达名称
    let l_subscript = strToAscii(title, l_msg, 26);
    //设置雷达类型
    let l_LidarType = $("#AddlidarType").combobox("getValue");
    l_subscript = strToAscii(l_LidarType, l_msg, l_subscript);
    //设置雷达Ip
    let l_LidarIp = $("#AddLidarIP").textbox("getValue");
    let l_LidarIps = l_LidarIp.split('.')
    for (let index = 0; index < l_LidarIps.length; index++) {
        l_msg[l_subscript++] = parseInt(l_LidarIps[index])
    }
    //设置雷达 port
    let l_LidarPort = $("#AddLidarPort").numberbox("getValue");
    l_subscript = intTo16Byte(l_LidarPort, l_msg, l_subscript);
    //设置PC Ip
    let l_PcIp = $("#AddPcIP").textbox("getValue");
    var l_PcIps = l_PcIp.split('.')
    for (let index = 0; index < l_PcIps.length; index++) {
        l_msg[l_subscript++] = parseInt(l_PcIps[index])
    }
    //设置PC port
    let l_PcPort = $("#AddPcPort").numberbox("getValue");
    l_subscript = intTo16Byte(l_PcPort, l_msg, l_subscript);
    addTail_(l_msg);
    c_master.MSG_SETCMD_ADDLASER.start(l_msg);
}

function addLaserTab(title, url) {
    var content = '<iframe scrolling="auto" frameborder="0"  src="' + url + '" style="width:100%;height:99%;"></iframe>';
    $('#tabs4').tabs('add', {
        title: title,
        content: content,
        closable: true
    });
}
//#endregion

//#region 二维码
function makeMacQRCode2(pArray) {
    qrcode.clear();
    qrcode.makeCode(pArray[1]);

}

function makeMacQRCode(pStr) {
    console.log("QR:");
    console.log(pStr);
    qrcode.clear();
    qrcode.makeCode(pStr);
}
//#endregion

//#region 没用到
function test1(pPose, pNetArray) {
    let lSize = pNetArray.length;

    pNetArray[lSize - 16] = "0x" + ((pPose.PoseX * 1000) & 0xff000000 >> 24);

    pNetArray[lSize - 15] = (pPose.PoseX * 1000) & 0x00ff0000 >> 16;
    pNetArray[lSize - 14] = (pPose.PoseX * 1000) & 0x0000ff00 >> 8;
    pNetArray[lSize - 13] = (pPose.PoseX * 1000) & 0x000000ff;

    pNetArray[lSize - 12] = (pPose.PoseY * 1000) & 0xff000000 >> 24;
    pNetArray[lSize - 11] = (pPose.PoseY * 1000) & 0x00ff0000 >> 16;
    pNetArray[lSize - 10] = (pPose.PoseY * 1000) & 0x0000ff00 >> 8;
    pNetArray[lSize - 9] = (pPose.PoseY * 1000) & 0x000000ff;

    pNetArray[lSize - 8] = (pPose.PoseA * 1000) & 0xff000000 >> 24;
    pNetArray[lSize - 7] = (pPose.PoseA * 1000) & 0x00ff0000 >> 16;
    pNetArray[lSize - 6] = (pPose.PoseA * 1000) & 0x0000ff00 >> 8;
    pNetArray[lSize - 5] = (pPose.PoseA * 1000) & 0x000000ff;
}

function readInitPose() {
    var lPose = new _sPose();
    get1Pose(lPose);

    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_SLAVER, CMD_SLAVER.SETCMD_CURRPOSE);
    // 设定Pose为雷达位姿
    intTo8Byte(1, l_msg, l_msg.length);
    poseAdd16Array(lPose, l_msg);
    addTail_(l_msg);
    c_slam.MSG_SETCMD_CURRPOSE.start(l_msg);
}


function prohibitWeb() {
    //禁止开关
    disableBtn();
    //禁止按钮
    disableSet();
    //禁止模式选择
    disableModelChose();
    //禁止显示pose 
    viewPoseStatus((-1));
    c_bCoreStart = false;
}

function initRosConnect() {
    // //初始化rviz
    // initRviz();

    //停止rviz显示 等待启动
    rvizViewer.stop();
}

function initWeb() {
    enableBtn();

    enableSet();

    enableModelChose();
    //顶部栏位姿状态显示关闭
    viewPoseStatus((-1));
    //bag 容量写0                
    writeBagStatus((0));
    //查询SLam状态
    QuerySlamState();
    // 查询Master关键信息
    QueryMasterMainInfo();
    // 查询错误码
    QueryErrorCode();
}

function readMac() {
    $('#mac').dialog('open');
}

function overMac() {
    $('#mac').dialog('close');
}
//#endregion

//#region bcc校验
function strToHexCharCode(str) {
    if (str === "")
        return "";
    var hexCharCode = [];
    hexCharCode.push("0x");
    for (var i = 0; i < str.length; i++) {
        hexCharCode.push((str.charCodeAt(i)).toString(16));
    }
    return hexCharCode.join("");
}



/*
let 块内作用域 
.substring(start,stop) 提取从 start 处到 stop-1 处的所有字符
.substring(start)      提取从 start 处到字符串末尾处的所有字符 5 6 7 8 9 10
*/

//十进制Str
function bcc10StrCheck(params) {
    let charArr = []
    while (true) {
        if (params.length == 0) {
            break;
        }
        charArr.push(params.substring(0, 2));
        params = params.substring(2)
    }

    let res = 0;
    for (var i = 0; i < charArr.length; i++) {
        res = res ^ parseInt(charArr[i], 16)
    }

    return res.toString(16).toUpperCase()
}



