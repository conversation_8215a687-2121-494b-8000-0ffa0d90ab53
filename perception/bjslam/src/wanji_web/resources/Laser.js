

function ReadLaserParam() {
    //readBaseParam
    readLidarBaseParamfun();
    sleep(50);

    //readLaserNetParam
    readLidarNetParamfun();
    sleep(50);

    readNetListfun();
    sleep(50);

    readLidarNet();
    sleep(50);

    //readPcapList
    readPcapListfun();
    sleep(50);

    //查询雷达Pcap
    readLidarPcap();
    sleep(50);

    // 查询雷达状态
    readLidarStatus()
    sleep(50);

    //readLidarDip
    readLidarInstallANGfun(1);
    readLidarHorizonCalibNum();
    getHorizontalState();
    sleep(50);

    //readLidarCalib
    readLidarInstallXYZfun(2);
    readLidarInstallANGfun(2);
    // 查询雷达是否基准雷达
    readLidarIsBase();
    // getCalibState();
    sleep(50);

    //readBlindDistance
    readDisBlindfun();
    sleep(50);

    //readHight
    readLidarHightfun();
    sleep(50);

    //readBlindAng
    readBlindAnglefun();
    sleep(50);

    //readMark
    readMarkfun();
    sleep(50);

    //readGround
    readGround();
    sleep(50);
}

function SetLaserParam() {
    let lModelValue = localStorage["workmode"];
    console.log("雷达界面获取工作模式：", lModelValue);
    if (lModelValue != 0) {
        $('#getStandbyWarn').dialog('open');
        $('#getStandbyWarn').window('center');
        return;
    }
    if (($("#lidarSN").numberbox("isValid") &&
        $("#lidarName").textbox("isValid") &&
        $("#lidarType").combobox("isValid") &&
        $("#lidarIP").textbox("isValid") &&
        $("#PcPort").numberbox("isValid") &&
        $("#lidarDipAngleR").numberbox("isValid") &&
        $("#lidarDipAngleP").numberbox("isValid") &&
        $("#lidarX").numberbox("isValid") &&
        $("#lidarY").numberbox("isValid") &&
        $("#lidarZ").numberbox("isValid") &&
        $("#lidarAngleR").numberbox("isValid") &&
        $("#lidarAngleP").numberbox("isValid") &&
        $("#lidarAngleY").numberbox("isValid") &&
        $("#blindDistanceMin").numberbox("isValid") &&
        $("#blindDistanceMax").numberbox("isValid") &&
        $("#lidarHeight").numberbox("isValid")) &&
        $("#markSize").numberbox("isValid") &&
        $("#LidarPcapNameList").combobox("isValid") &&
        $("#LidarNetNameList").combobox("isValid")
    ) {
        if (!blindVaild()) {
            alert("请检查盲区角度格式，盲区角度为逆时针0-360，起始角度须小于终止角度");
            return;
        }

        //设置雷达使能状态
        setEnbleLidarfun();

        //设置雷达基础信息
        setLidarBaseParamfun();
        sleep(5);

        //设置雷达网络信息
        setLidarNetParamfun();
        sleep(5);

        //设置雷达网卡
        setNetNameSelect();
        sleep(5);

        //设置选中的pcap包
        setPcapSelect();
        sleep(5);

        //设置雷达倾斜角度
        setLidarInstallANGfun(1);
        setLidarHorizonCalibNum();
        sleep(5);

        //设置雷达安装位置
        setLidarInstallXYZfun(2);
        sleep(5);

        //设置雷达基安装角度
        setLidarInstallANGfun(2);
        sleep(5);

        //设置雷达距离盲区
        setLidarDISBlindfun();
        sleep(5);

        //设置雷达角度盲区
        setLidarANGBlindfun();
        sleep(5);

        //设置雷达高度
        setLidarHight();
        sleep(5);

        // 设置雷达基准
        setLidarBase();
        sleep(5);

        //设置靶标信息
        setMarkInfoFun();
        sleep(5);

        //设置地图信息
        setUseGroundFun();

        // 提示保存并重启SLAM
        $('#saveParamResetSLAMWarn').dialog('open');
        $('#saveParamResetSLAMWarn').dialog('center');
        // $('#saveParamResetSLAMWarn').window('center');//居中显示
    }
    else {
        alert("请检查格式，确认所有必选项已填");
    }
}

function sendSaveParamCMD() {
    //保存系统参数
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_SAVEPARAM);
    addTail_(l_msg);
    c_master.MSG_SETCMD_SAVEPARAM.start(l_msg);
}

//region 水平校准
function setStartHorizontal(on) {
    // 设置功能下发
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.SETCMD_HORIZONTALCALIBRATION);
    //读取雷达名称
    let l_subscript = strToAscii(c_DeviceName, l_msg, 26);
    if (on == true)
        l_msg[l_subscript++] = 1;
    else
        l_msg[l_subscript++] = 0;
    addTail_(l_msg);
    c_device.MSG_SETCMD_HORIZONTALCALIBRATION.start(l_msg);
}

function getHorizontalState(on) {
    if (on == true) {
        let l_msg = PROTArray.slice();
        setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.QUERYCMD_HORIZONTALCALIBRATION);
        //读取雷达名称
        l_subscript = strToAscii(c_DeviceName, l_msg, 26);
        addTail_(l_msg);
        c_device.MSG_QUERYCMD_HORIZONTALCALIBRATION.start(l_msg);
    }
    else
        c_device.MSG_QUERYCMD_HORIZONTALCALIBRATION.stop();
}

function openHorizontalAlign() {
    setLidarHorizonCalibNum();
    setStartHorizontal(true);
}

function closeHorizontalAlign() {
    setStartHorizontal(false);

    sleep(100);
    //查询Dip...角度
    readLidarInstallANGfun(1);
}

function applyHorizontalAlignResult() {
    //关闭多雷达标定和定时查询标定结果
    setStartHorizontal(false);

    sleep(100);
    //设置角度更新
    setLidarInstallANGfun(1);
    //查询Dip...角度
    readLidarInstallANGfun(1);
}

function quitHorizontalAlignResult() {
    //关闭水平校准 和 定时查询标定结果
    if (typeof c_device.MSG_QUERYCMD_HORIZONTALCALIBRATION.c_send != "undefined") {
        setStartHorizontal(false);
    }
    sleep(100);
    //安装角度更新
    //查询Dip...角度
    readLidarInstallANGfun(1);
}

function fillHorizontalAlignResult(pArray, l_subscript) {
    // 状态码+校准当前次数 + R + P
    // console.log("horizonResult: ", pArray, l_subscript);
    // 最大校验次数
    let l_maxNum = parseInt($("#HorizontalAlignTimes").numberbox("getValue"));
    // 状态码
    let state = pArray[l_subscript++];
    // 次数码
    let num = pArray[l_subscript++] * Math.pow(2, 8) + pArray[l_subscript++];
    // 地面高度 & 残余角度    
    let lStd = [];
    let l_temp1 = l_subscript;
    for (l_subscript; l_subscript < l_temp1 + 2 * 4; l_subscript = l_subscript + 4) {
        lStd.push(((pArray[l_subscript] * Math.pow(2, 24) +
            pArray[l_subscript + 1] * Math.pow(2, 16) +
            pArray[l_subscript + 2] * Math.pow(2, 8) +
            pArray[l_subscript + 3]) | 0xFFFFFFFF00000000) * 0.001);
    }
    lStd[0] = -lStd[0];
    // 4舍五入 保留3位小数
    lStd[0] = toDecimal(lStd[0]);
    lStd[1] = toDecimal(lStd[1]);
    switch (state) {
        case 0:
            {
                $("#horizontalState").textbox("setValue", "未启动");
                return;
            }
        case 1:
            {
                $("#horizontalState").textbox("setValue", "等待数据");
                return;
            }
        case 2:
            {
                if (num <= l_maxNum) {
                    let lNum = "校准 " + num + " 次...";
                    $("#horizontalState").textbox("setValue", lNum);
                }
                else
                    $("#horizontalState").textbox("setValue", "正在计算倾角...");
                return;
            }
        case 3:
            {
                let lNum = "校准成功->高度:" + lStd[0] + "[m], 精度:" + lStd[1] + "[deg]";
                $("#horizontalState").textbox("setValue", lNum); break;
            }
        case 4:
            {
                l_subscript += 9;
                $("#horizontalState").textbox("setValue", "数据异常中断"); break;
            }
        case 5:
            {
                let lNum = "校准精度低:" + lStd[1] + "[deg]->请应用后再次校准";
                $("#horizontalState").textbox("setValue", lNum); break;
            }
        case 6:
            {
                l_subscript++;
                let lNum = "地面扫描异常->请更换场地重新开始校准";
                $("#horizontalState").textbox("setValue", lNum); break;
            }
        default:
            console.log("hoziron: ", pArray[l_subscript]);
            break;
    }

    let lRes = [];
    let l_temp = l_subscript;
    for (l_subscript; l_subscript < l_temp + 2 * 4; l_subscript = l_subscript + 4) {
        lRes.push(((pArray[l_subscript] * Math.pow(2, 24) +
            pArray[l_subscript + 1] * Math.pow(2, 16) +
            pArray[l_subscript + 2] * Math.pow(2, 8) +
            pArray[l_subscript + 3]) | 0xFFFFFFFF00000000) * 0.001);
    }

    $("#lidarDipAngleR").numberbox("setValue", lRes[0]);
    $("#lidarDipAngleP").numberbox("setValue", lRes[1]);
}
//end region

//region 多雷达标定
function setStartCalib(on) {
    // 设置功能下发
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.SETCMD_MULLASERCALIBRATION);
    //读取雷达名称
    let l_subscript = strToAscii(c_DeviceName, l_msg, 26);
    if (on == true)
        l_msg[l_subscript++] = 1;
    else
        l_msg[l_subscript++] = 0;
    addTail_(l_msg);
    c_device.MSG_SETCMD_MULLASERCALIBRATION.start(l_msg);
}

function getCalibState(on) {
    if (on == true) {
        let l_msg = PROTArray.slice();
        setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.QUERYCMD_MULLASERCALIBRATION);
        //读取雷达名称
        l_subscript = strToAscii(c_DeviceName, l_msg, 26);
        addTail_(l_msg);
        c_device.MSG_QUERYCMD_MULLASERCALIBRATION.start(l_msg);
    }
    else
        c_device.MSG_QUERYCMD_MULLASERCALIBRATION.stop();
}

function openLidarCalib() {
    setStartCalib(true);
}

function closeLidarCalib() {
    setStartCalib(false);

    sleep(100);
    //安装位置更新
    readLidarInstallXYZfun(2);

    //安装角度更新
    readLidarInstallANGfun(2);
}

function applyLidarCalibResult() {
    //关闭多雷达标定和定时查询标定结果
    setStartCalib(false);

    sleep(100);
    setLidarInstallXYZfun(2);
    setLidarInstallANGfun(2);

    sleep(100);
    //安装位置更新
    readLidarInstallXYZfun(2);
    //安装角度更新
    readLidarInstallANGfun(2);
}

function quitLidarCalibrationResult() {
    //关闭多雷达标定和定时查询标定结果
    if (typeof t_readLidarCalibrationResult != "undefined") {
        setStartCalib(false);
    }
    sleep(100);
    //安装位置更新
    readLidarInstallXYZfun(2);

    //安装角度更新
    readLidarInstallANGfun(2);
}

function fillCalibResult(pArray, l_subscript) {
    // 状态
    let l_bIsStartCalib = pArray[l_subscript++];
    // 次数
    let l_CalibTimes = pArray[l_subscript++] * Math.pow(2, 8) + pArray[l_subscript++];
    if (!l_bIsStartCalib) {
        //未标定
        $("#calibrationState").textbox("setValue", "标定状态：未启动");
        // 重新读取安装参数
        readLidarInstallXYZfun(2);
        readLidarInstallANGfun(2);
        return;
    }
    let l_start = l_subscript;
    let lRes = [];
    for (l_subscript; l_subscript < l_start + 12 * 4; l_subscript = l_subscript + 4) {
        lRes.push(((pArray[l_subscript] * Math.pow(2, 24) +
            pArray[l_subscript + 1] * Math.pow(2, 16) +
            pArray[l_subscript + 2] * Math.pow(2, 8) +
            pArray[l_subscript + 3]) | 0xFFFFFFFF00000000) * 0.001);
    }
    console.log(lRes[0], lRes[2], lRes[4]);
    let lState;
    if (lRes[0] == 0 && lRes[2] == 0 && lRes[4] == 0)
        lState = "初始化标定，保持静止10秒";
    else {
        let lTRes = Math.sqrt(lRes[1] * lRes[1] + lRes[3] * lRes[3]) * 100;
        let lRRes = Math.sqrt(lRes[11] * lRes[11]);
        // 初始化完成等待运行
        if (l_CalibTimes <= 1) {
            lState = "已完成初始化,请开始进行匀低速运动!";
        }
        // 样本数量不足
        else if (l_CalibTimes < 1000)
            lState = "请保持匀低速运动,当前精度:" + lTRes.toFixed(1) + "[cm], " + lRRes.toFixed(1) + "[deg]";
        // 样本数量足够
        else {
            // 精度低
            if (lTRes > 2 || lRRes > 0.5)
                lState = "标定精度低! 精度:" + lTRes.toFixed(1) + "[cm], " + lRRes.toFixed(1) + "[deg]";
            // 精度正常
            else {
                lState = "标定完成! 请应用结果,精度:" + lTRes.toFixed(1) + "[cm], " + lRRes.toFixed(1) + "[deg]";
            }
        }
    }
    // console.log("l_CalibTimes: ", l_CalibTimes);
    // console.log("lState: ", lState);
    $("#calibrationState").textbox("setValue", lState);
    $("#lidarX").numberbox("setValue", lRes[0]);
    $("#lidarY").numberbox("setValue", lRes[2]);
    $("#lidarZ").numberbox("setValue", lRes[4]);
    $("#lidarAngleR").numberbox("setValue", lRes[6]);
    $("#lidarAngleP").numberbox("setValue", lRes[8]);
    $("#lidarAngleY").numberbox("setValue", lRes[10]);
}
function blindVaild() {
    let res = false;
    if ($("#blindAngle11").numberbox("isValid") &&
        $("#blindAngle12").numberbox("isValid") &&
        $("#blindAngle21").numberbox("isValid") &&
        $("#blindAngle22").numberbox("isValid") &&
        $("#blindAngle31").numberbox("isValid") &&
        $("#blindAngle32").numberbox("isValid") &&
        $("#blindAngle41").numberbox("isValid") &&
        $("#blindAngle42").numberbox("isValid")) {
        let l11 = $("#blindAngle11").numberbox("getValue") * 1000;
        let l12 = $("#blindAngle12").numberbox("getValue") * 1000;
        let l21 = $("#blindAngle11").numberbox("getValue") * 1000;
        let l22 = $("#blindAngle12").numberbox("getValue") * 1000;
        let l31 = $("#blindAngle11").numberbox("getValue") * 1000;
        let l32 = $("#blindAngle12").numberbox("getValue") * 1000;
        let l41 = $("#blindAngle11").numberbox("getValue") * 1000;
        let l42 = $("#blindAngle12").numberbox("getValue") * 1000;

        if (l11 <= l12 &&
            l21 <= l22 &&
            l31 <= l32 &&
            l41 <= l42) {
            res = true;
        }
    }
    return res;
}
//endregion


//region 雷达安装位姿
function readLidarInstallXYZfun(seq) {
    //读取雷达安装位置
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.QUERYCMD_LASERINSTALLXYZ);
    let l_subscript = strToAscii(c_DeviceName, l_msg, 26)//设置雷达名称
    l_subscript = intTo8Byte(seq, l_msg, l_subscript);//设置安装组
    addTail_(l_msg);
    c_device.MSG_QUERYCMD_LASERINSTALLXYZ.start(l_msg);
}

function fillLidarXYZ(pArray, l_subscript) {
    let seq = pArray[l_subscript++];
    if (seq == 2) {
        let l_LaserX = ascii32ByteToInt(pArray, l_subscript) * 0.001;
        l_subscript += 4;
        let l_LaserY = ascii32ByteToInt(pArray, l_subscript) * 0.001;
        l_subscript += 4;
        let l_LaserZ = ascii32ByteToInt(pArray, l_subscript) * 0.001;
        l_subscript += 4;

        $("#lidarX").numberbox("setValue", l_LaserX);
        $("#lidarY").numberbox("setValue", l_LaserY);
        $("#lidarZ").numberbox("setValue", l_LaserZ);
    }
}

function readLidarInstallANGfun(seq) {
    //读取雷达安装角度
    l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.QUERYCMD_LASERINSTALLANGLE);
    let l_subscript = strToAscii(c_DeviceName, l_msg, 26)
    l_subscript = intTo8Byte(seq, l_msg, l_subscript);//设置安装组
    addTail_(l_msg);
    c_device.MSG_QUERYCMD_LASERINSTALLANGLE.start(l_msg);
}

function fillLidarRPY(pArray, l_subscript) {
    let seq = pArray[l_subscript++];
    let l_LaserAngleR = hex2dec(pArray[l_subscript++], pArray[l_subscript++], pArray[l_subscript++], pArray[l_subscript++]) * 0.001;
    let l_LaserAngleP = hex2dec(pArray[l_subscript++], pArray[l_subscript++], pArray[l_subscript++], pArray[l_subscript++]) * 0.001;
    let l_LaserAngleY = hex2dec(pArray[l_subscript++], pArray[l_subscript++], pArray[l_subscript++], pArray[l_subscript++]) * 0.001;
    if (seq == 1) {
        $("#lidarDipAngleR").numberbox("setValue", l_LaserAngleR);
        $("#lidarDipAngleP").numberbox("setValue", l_LaserAngleP);
    }
    else if (seq == 2) {
        $("#lidarAngleR").numberbox("setValue", l_LaserAngleR);
        $("#lidarAngleP").numberbox("setValue", l_LaserAngleP);
        $("#lidarAngleY").numberbox("setValue", l_LaserAngleY);
    }
}

function setLidarInstallXYZfun(seq) {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.SETCMD_LASERINSTALLXYZ);
    //读取雷达名称
    let l_subscript = strToAscii(c_DeviceName, l_msg, 26)
    // 读取安装组
    l_subscript = intTo8Byte(seq, l_msg, l_subscript);
    //设置安装XYZ
    let l_lidarX = $("#lidarX").numberbox("getValue") * 1000;
    let l_lidarY = $("#lidarY").numberbox("getValue") * 1000;
    let l_lidarZ = $("#lidarZ").numberbox("getValue") * 1000;
    l_subscript = intTo32Byte(l_lidarX, l_msg, l_subscript);
    l_subscript = intTo32Byte(l_lidarY, l_msg, l_subscript);
    l_subscript = intTo32Byte(l_lidarZ, l_msg, l_subscript);
    addTail_(l_msg);
    c_device.MSG_SETCMD_LASERINSTALLXYZ.start(l_msg);
}

function setLidarInstallANGfun(seq) {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.SETCMD_LASERINSTALLANGLE);
    //读取雷达名称
    let l_subscript = strToAscii(c_DeviceName, l_msg, 26);
    // 读取安装组
    l_subscript = intTo8Byte(seq, l_msg, l_subscript);
    let l_lidarR = 0;
    let l_lidarP = 0;
    let l_lidarY = 0;
    if (seq == 1) {
        //设置倾斜RPY
        l_lidarR = $("#lidarDipAngleR").numberbox("getValue") * 1000;
        l_lidarP = $("#lidarDipAngleP").numberbox("getValue") * 1000;
    }
    else if (seq == 2) {
        //设置安装RPY
        l_lidarR = $("#lidarAngleR").numberbox("getValue") * 1000;
        l_lidarP = $("#lidarAngleP").numberbox("getValue") * 1000;
        l_lidarY = $("#lidarAngleY").numberbox("getValue") * 1000;
    }
    l_subscript = intTo32Byte(l_lidarR, l_msg, l_subscript);
    l_subscript = intTo32Byte(l_lidarP, l_msg, l_subscript);
    l_subscript = intTo32Byte(l_lidarY, l_msg, l_subscript);
    addTail_(l_msg);
    c_device.MSG_SETCMD_LASERINSTALLANGLE.start(l_msg);
}

function setLidarHorizonCalibNum() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.SETCMD_HORIZONTALCALIBNUM);
    //读取雷达名称
    let l_subscript = strToAscii(c_DeviceName, l_msg, 26)
    l_msg[l_subscript++] = parseInt($("#HorizontalAlignTimes").numberbox("getValue"));
    addTail_(l_msg);
    c_device.MSG_SETCMD_HORIZONTALCALIBNUM.start(l_msg);
}

//endregion

//region 雷达基础信息
function readLidarBaseParamfun() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_PARAM, CMD_PARAM.QUERYCMD_LASERBASEPARAM);
    strToAscii(c_DeviceName, l_msg, 26)
    addTail_(l_msg);
    c_param.MSG_QUERYCMD_LASERBASEPARAM.start(l_msg);

    //查询雷达使能状态
    l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.QUERYCMD_LASERENABLE);
    strToAscii(c_DeviceName, l_msg, 26);
    addTail_(l_msg);
    c_device.MSG_QUERYCMD_LASERENABLE.start(l_msg);
}

function fillLidarBaseParam(pArray, l_subscript) {
    $("#lidarName").textbox("setValue", c_DeviceName);

    //雷达SN
    let l_LidarSN = asciiToStr(pArray, l_subscript);
    l_subscript += pArray[l_subscript] + 1;
    //雷达类型
    let l_LidarType = asciiToStr(pArray, l_subscript);
    l_subscript += pArray[l_subscript] + 1;

    $("#lidarSN").textbox("setValue", l_LidarSN);
    $("#lidarType").combobox("setValue", l_LidarType);
    console.log(c_DeviceName, l_LidarSN, l_LidarType);
}

function setLidarBaseParamfun() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_PARAM, CMD_PARAM.SETCMD_LASERBASEPARAM);
    //读取雷达名称
    let l_subscript = strToAscii(c_DeviceName, l_msg, 26)
    //设置雷达SN
    let l_LidarSN = $("#lidarSN").textbox("getValue");
    l_subscript = strToAscii(l_LidarSN, l_msg, l_subscript)
    //设置雷达类型
    let l_LidarType = $("#lidarType").combobox("getValue");
    l_subscript = strToAscii(l_LidarType, l_msg, l_subscript)
    addTail_(l_msg);
    c_param.MSG_SETCMD_LASERBASEPARAM.start(l_msg);
}

function fillEnbleLidar(pArray, l_subscript) {
    let l_EnableLidar = pArray[l_subscript];
    $("input[type='radio'][name='EnableLidar'][value=" + l_EnableLidar + "]").prop('checked', 'true');
}

function setEnbleLidarfun(){
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.SETCMD_LASERENABLE);
    let l_subscript = strToAscii(c_DeviceName, l_msg, 26);
    let tmp = $('input[name=EnableLidar]:checked').val();
    l_subscript = intTo8Byte(tmp, l_msg, l_subscript);
    addTail_(l_msg);
    c_device.MSG_SETCMD_LASERENABLE.start(l_msg);
}
//end region 雷达基础信息

//region 雷达网络信息
function readLidarNetParamfun() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_PARAM, CMD_PARAM.QUERYCMD_LASERNETPARAM);
    strToAscii(c_DeviceName, l_msg, 26)
    addTail_(l_msg);
    c_param.MSG_QUERYCMD_LASERNETPARAM.start(l_msg);
}

/**
 * 填充读取的雷达网络信息 雷达ip PC端口
 * @param {*} pArray 
 * @returns 
 */
function fillLidarNetParam(pArray, l_subscript) {
    let l_lidarIP = pArray[l_subscript++].toString() + '.' +
        pArray[l_subscript++].toString() + '.' +
        pArray[l_subscript++].toString() + '.' +
        pArray[l_subscript++].toString();

    let l_lidarPort = pArray[l_subscript++] * Math.pow(2, 8) + pArray[l_subscript++];

    let l_PcIP = pArray[l_subscript++].toString() + '.' +
        pArray[l_subscript++].toString() + '.' +
        pArray[l_subscript++].toString() + '.' +
        pArray[l_subscript++].toString();

    let l_PcPort = pArray[l_subscript++] * Math.pow(2, 8) + pArray[l_subscript++];

    $("#lidarIP").textbox("setValue", l_lidarIP);
    $("#lidarPort").numberbox("setValue", l_lidarPort);
    $("#PcIP").textbox("setValue", l_PcIP);
    $("#PcPort").numberbox("setValue", l_PcPort);
}

function setLidarNetParamfun() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_PARAM, CMD_PARAM.SETCMD_LASERNETPARAM);
    //读取雷达名称
    let l_subscript = strToAscii(c_DeviceName, l_msg, 26);
    //设置雷达Ip
    let l_LidarIp = $("#lidarIP").textbox("getValue");
    let Ips = l_LidarIp.split('.')
    for (let index = 0; index < Ips.length; index++) {
        l_msg[l_subscript++] = parseInt(Ips[index])
    }

    let l_lidarPort = $("#lidarPort").numberbox("getValue");
    l_subscript = intTo16Byte(l_lidarPort, l_msg, l_subscript);

    //设置Pc Ip
    let l_PcIP = $("#PcIP").textbox("getValue");
    let l_PcIps = l_PcIP.split('.')
    for (let index = 0; index < l_PcIps.length; index++) {
        l_msg[l_subscript++] = parseInt(l_PcIps[index])
    }

    let l_PcPort = $("#PcPort").numberbox("getValue");
    l_subscript = intTo16Byte(l_PcPort, l_msg, l_subscript);

    addTail_(l_msg);
    c_param.MSG_SETCMD_LASERNETPARAM.start(l_msg);
}

function readPcapListfun() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_PCAPLIST);
    addTail_(l_msg);
    c_master.MSG_QUERYCMD_PCAPLIST.start(l_msg);
}

function readNetListfun() {
    //查询net list
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_NETLIST);
    addTail_(l_msg);
    c_master.MSG_QUERYCMD_NETLIST.start(l_msg);
}

function fillMovingMode(pArray) { }

function readLidarPcap() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_LASERPCAPNAME);
    strToAscii(c_DeviceName, l_msg, 26);
    addTail_(l_msg);
    c_master.MSG_QUERYCMD_LASERPCAPNAME.start(l_msg);
}

function readLidarNet() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_LASERNET);
    strToAscii(c_DeviceName, l_msg, 26);
    addTail_(l_msg);
    c_master.MSG_QUERYCMD_LASERNET.start(l_msg);
}

function readLidarStatus() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.QUERYCMD_LIDARSTATUS);
    strToAscii(c_DeviceName, l_msg, 26);
    addTail_(l_msg);
    c_device.MSG_QUERYCMD_LIDARSTATUS.start(l_msg);
}

function readLidarHorizonCalibNum() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.QUERYCMD_HORIZONTALCALIBNUM);
    strToAscii(c_DeviceName, l_msg, 26);
    addTail_(l_msg);
    c_device.MSG_QUERYCMD_HORIZONTALCALIBNUM.start(l_msg);
}

function readLidarIsBase() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_PARAM, CMD_PARAM.QUERYCMD_LASERISBASE);
    strToAscii(c_DeviceName, l_msg, 26);
    addTail_(l_msg);
    c_param.MSG_QUERYCMD_LASERISBASE.start(l_msg);
}

function controlCalibBtn(on) {
    if (on) {
        $("#openCalibBtn").linkbutton("disable");
        $("#closeCalibBtn").linkbutton("disable");
        $("#applyCalibBtn").linkbutton("disable");
    }
    else {
        $("#openCalibBtn").linkbutton("enable");
        $("#closeCalibBtn").linkbutton("enable");
        $("#applyCalibBtn").linkbutton("enable");
    }
}

function fillLidarPcap(pArray, l_subscript) {
    if (pArray[l_subscript] != 0) {
        let l_LidarPcapName = asciiToStr(pArray, l_subscript);
        $("#LidarPcapNameList").combobox("setValue", l_LidarPcapName);
    }
}

function fillLidarNetname(pArray, l_subscript) {
    if (pArray[l_subscript] != 0) {
        let l_LidarNetName = asciiToStr(pArray, l_subscript);
        $("#LidarNetNameList").combobox("setValue", l_LidarNetName);
    }
}

function fillPcapList(pArray) {
    let lOffset = 26;
    let lBagNum = pArray[lOffset++];
    let bagSingle;
    let lPairDataInfo = [];

    for (let i = 0; i < lBagNum; i++) {
        let lBagName = asciiToStr(pArray, lOffset);
        lOffset += pArray[lOffset] + 1;
        bagSingle = lBagName.toString();

        lPairDataInfo.push({ "text": bagSingle, "id": bagSingle });

    }

    $("#LidarPcapNameList").combobox("loadData", lPairDataInfo);
}

function fillNetList(pArray) {
    let lOffset = 26;
    let lNetCardNum = pArray[lOffset++];
    let bagSingle;
    let lPairDataInfo = [];

    for (let i = 0; i < lNetCardNum; i++) {
        let l_NetCardIdTemp = asciiToStr(pArray, lOffset);
        lOffset += pArray[lOffset] + 1;
        bagSingle = l_NetCardIdTemp.toString();

        lPairDataInfo.push({ "text": bagSingle, "id": bagSingle });
    }

    $("#LidarNetNameList").combobox("loadData", lPairDataInfo);
}

function fillAGVIPinfo(pArray) {
}

function setPcapSelect() {
    if ($("#LidarPcapNameList").combobox("isValid")) {
        let lBag = $("#LidarPcapNameList").combobox("getValue").toString();

        let l_msg = PROTArray.slice();
        //读取雷达名称
        let l_subscript = strToAscii(c_DeviceName, l_msg, 26)
        setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_LASERPCAPNAME);
        strToAscii(lBag, l_msg, l_msg.length);
        addTail_(l_msg);
        c_master.MSG_SETCMD_LASERPCAPNAME.start(l_msg);
    }
    else {
        alert("非法操作，选择文件无效");
    }
}

function setNetNameSelect() {
    if ($("#LidarNetNameList").combobox("isValid")) {
        let lBag = $("#LidarNetNameList").combobox("getValue").toString();

        let l_msg = PROTArray.slice();
        //读取雷达名称
        let l_subscript = strToAscii(c_DeviceName, l_msg, 26)
        setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_LASERNET);
        strToAscii(lBag, l_msg, l_msg.length);
        addTail_(l_msg);
        c_master.MSG_SETCMD_LASERNET.start(l_msg);
        console.log("send NetName");
    }
    else {
        alert("非法操作，选择网卡无效");
    }
}
//end region 雷达网络信息


//region 距离盲区
function readDisBlindfun() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.QUERYCMD_LASERDISBLIND);
    strToAscii(c_DeviceName, l_msg, 26);
    addTail_(l_msg);
    c_device.MSG_QUERYCMD_LASERDISBLIND.start(l_msg);
}

function fillLidarDisBlind(pArray, l_subscript) {
    let l_Lasermin = ascii32ByteToInt(pArray, l_subscript) * 0.001;
    l_subscript += 4;
    let l_Lasermax = ascii32ByteToInt(pArray, l_subscript) * 0.001;
    l_subscript += 4;

    $("#blindDistanceMin").numberbox("setValue", l_Lasermin);
    $("#blindDistanceMax").numberbox("setValue", l_Lasermax);
}

function setLidarDISBlindfun() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.SETCMD_LASERDISBLIND);
    //读取雷达名称
    let l_subscript = strToAscii(c_DeviceName, l_msg, 26)
    //设置距离盲区
    let l_blindDistanceMin = $("#blindDistanceMin").numberbox("getValue") * 1000;
    let l_blindDistanceMax = $("#blindDistanceMax").numberbox("getValue") * 1000;
    l_subscript = intTo32Byte(l_blindDistanceMin, l_msg, l_subscript);
    l_subscript = intTo32Byte(l_blindDistanceMax, l_msg, l_subscript);
    addTail_(l_msg);
    c_device.MSG_SETCMD_LASERDISBLIND.start(l_msg);
}

function readLidarHightfun() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.QUERYCMD_LASERHIGHT);
    strToAscii(c_DeviceName, l_msg, 26);
    addTail_(l_msg);
    c_device.MSG_QUERYCMD_LASERHIGHT.start(l_msg);
}

function fillLidarHeight(pArray, l_subscript) {
    let lValue = ascii32ByteToInt(pArray, l_subscript) * 0.001;
    l_subscript += 4;
    $("#lidarHeight").numberbox("setValue", lValue);
}

function fillLidarBase(pArray, l_subscript) {
    let l_isBaseLaser = pArray[l_subscript];
    $("input[type='radio'][name='isBaseLaser'][value=" + l_isBaseLaser + "]").prop('checked', 'true');
    controlCalibBtn(l_isBaseLaser);
}

function fillLidarStatus(pArray, l_subscript) {
    let l_status = pArray[l_subscript];
    switch (l_status) {
        case DevStatus.DEFAULTSTATUS:
            $("#lidarStatus").textbox("setValue", "未更新");
            break;
        case DevStatus.WAITCONNECT:
            $("#lidarStatus").textbox("setValue", "等待连接");
            break;
        case DevStatus.VIRTUALDEV:
            $("#lidarStatus").textbox("setValue", "虚拟设备");
            break;

        case DevStatus.DATAWAIT:
            $("#lidarStatus").textbox("setValue", "等待雷达数据");
            break;
        case DevStatus.DATADATAERROR:
            $("#lidarStatus").textbox("setValue", "雷达数据异常");
            break;
        case DevStatus.PCAPRUN:
            $("#lidarStatus").textbox("setValue", "播放数据");
            break;
        case DevStatus.PCAPSTOP:
            $("#lidarStatus").textbox("setValue", "播放暂停");
            break;
        case DevStatus.DEVCONNECT:
            $("#lidarStatus").textbox("setValue", "雷达正常连接");
            break;

        case DevStatus.LOADPARAMERROR:
            $("#lidarStatus").textbox("setValue", "雷达参数表加载异常");
            break;
        case DevStatus.GETPARAMERROR:
            $("#lidarStatus").textbox("setValue", "雷达参数获取异常");
            break;
        case DevStatus.NOFINDDEV:
            $("#lidarStatus").textbox("setValue", "雷达未识别");
            break;
        case DevStatus.NOFINDDEV:
            $("#lidarStatus").textbox("setValue", "雷达未识别");
            break;
        case DevStatus.NETNOALIVE:
            $("#lidarStatus").textbox("setValue", "网卡未插入");
            break;
        case DevStatus.NETNOIP:
            $("#lidarStatus").textbox("setValue", "网卡未配置IP且未插入");
            break;
        case DevStatus.NETIPERROR:
            $("#lidarStatus").textbox("setValue", "网卡IP不符");
            break;
        case DevStatus.NETSETERROR:
            $("#lidarStatus").textbox("setValue", "未设置网卡");
            break;
        case DevStatus.PCAPERROR:
            $("#lidarStatus").textbox("setValue", "数据包错误");
            break;
        case DevStatus.PCAPOVER:
            $("#lidarStatus").textbox("setValue", "数据包结束");
            break;
        case DevStatus.IPINFOERROR:
            $("#lidarStatus").textbox("setValue", "网络配置错误");
            break;
        default:
            $("#lidarStatus").textbox("setValue", "未定义状态");
            break;
    }
}

function fillLidarHorizonCalibNum(pArray, l_subscript) {
    let lValue = pArray[l_subscript];
    $("#HorizontalAlignTimes").numberbox("setValue", lValue);
}

function setLidarHight() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.SETCMD_LASERHIGHT);
    //读取雷达名称
    let l_subscript = strToAscii(c_DeviceName, l_msg, 26)
    //设置雷达高度
    let l_lidarH = $("#lidarHeight").numberbox("getValue") * 1000;
    l_subscript = intTo32Byte(l_lidarH, l_msg, l_subscript);
    addTail_(l_msg);
    c_device.MSG_SETCMD_LASERHIGHT.start(l_msg);

}

function setLidarBase() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_PARAM, CMD_PARAM.SETCMD_LASERISBASE);
    //读取雷达名称
    let l_subscript = strToAscii(c_DeviceName, l_msg, 26)
    //设置雷达高度
    let tmp = $('input[name=isBaseLaser]:checked').val();
    l_subscript = intTo8Byte(tmp, l_msg, l_subscript);
    addTail_(l_msg);
    c_param.MSG_SETCMD_LASERISBASE.start(l_msg);
}
//end region 

//region 角度盲区
function readBlindAnglefun() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.QUERYCMD_LASERANGBLIND);
    strToAscii(c_DeviceName, l_msg, 26)
    addTail_(l_msg);
    c_device.MSG_QUERYCMD_LASERANGBLIND.start(l_msg);
}

function fillLidarAngBlind(pArray, l_subscript) {
    let l_start = l_subscript;
    let lBlind = [];
    for (l_subscript; l_subscript < l_start + 8 * 4; l_subscript = l_subscript + 4) {
        lBlind.push(((pArray[l_subscript] * Math.pow(2, 24) +
            pArray[l_subscript + 1] * Math.pow(2, 16) +
            pArray[l_subscript + 2] * Math.pow(2, 8) +
            pArray[l_subscript + 3]) | 0xFFFFFFFF00000000) * 0.001);
    }

    $("#blindAngle11").numberbox("setValue", lBlind[0]);
    $("#blindAngle12").numberbox("setValue", lBlind[1]);
    $("#blindAngle21").numberbox("setValue", lBlind[2]);
    $("#blindAngle22").numberbox("setValue", lBlind[3]);
    $("#blindAngle31").numberbox("setValue", lBlind[4]);
    $("#blindAngle32").numberbox("setValue", lBlind[5]);
    $("#blindAngle41").numberbox("setValue", lBlind[6]);
    $("#blindAngle42").numberbox("setValue", lBlind[7]);
}

function setLidarANGBlindfun() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.SETCMD_LASERANGBLIND);
    //读取雷达名称
    let l_subscript = strToAscii(c_DeviceName, l_msg, 26);
    //设置角度盲区
    let l11 = $("#blindAngle11").numberbox("getValue") * 1000;
    let l12 = $("#blindAngle12").numberbox("getValue") * 1000;
    let l21 = $("#blindAngle21").numberbox("getValue") * 1000;
    let l22 = $("#blindAngle22").numberbox("getValue") * 1000;
    let l31 = $("#blindAngle31").numberbox("getValue") * 1000;
    let l32 = $("#blindAngle32").numberbox("getValue") * 1000;
    let l41 = $("#blindAngle41").numberbox("getValue") * 1000;
    let l42 = $("#blindAngle42").numberbox("getValue") * 1000;
    l_subscript = intTo32Byte(l11, l_msg, l_subscript);
    l_subscript = intTo32Byte(l12, l_msg, l_subscript);
    l_subscript = intTo32Byte(l21, l_msg, l_subscript);
    l_subscript = intTo32Byte(l22, l_msg, l_subscript);
    l_subscript = intTo32Byte(l31, l_msg, l_subscript);
    l_subscript = intTo32Byte(l32, l_msg, l_subscript);
    l_subscript = intTo32Byte(l41, l_msg, l_subscript);
    l_subscript = intTo32Byte(l42, l_msg, l_subscript);
    addTail_(l_msg);
    c_device.MSG_SETCMD_LASERANGBLIND.start(l_msg);
}
//end region 

//region 靶标信息
function readMarkfun() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.QUERYCMD_MARKMODE);
    strToAscii(c_DeviceName, l_msg, 26)
    addTail_(l_msg);
    c_device.MSG_QUERYCMD_MARKMODE.start(l_msg);
}

function fillLidarMark(pArray, l_subscript) {
    let isUseMark = pArray[l_subscript++];
    let markClass = pArray[l_subscript++];
    let markSize = (pArray[l_subscript++] * Math.pow(2, 8) + pArray[l_subscript++]) * 0.001;
    //使用
    $("input[type='radio'][name='isUseMark'][value=" + isUseMark + "]").prop('checked', 'true');
    $("#markSize").numberbox("setValue", markSize);
}

function setMarkInfoFun() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.SETCMD_MARKMODE);
    //读取雷达名称
    let l_subscript = strToAscii(c_DeviceName, l_msg, 26);
    //设置是否使用靶标
    let IsUseMark = $('input[name=isUseMark]:checked').val();
    if (1 == IsUseMark || 0 == IsUseMark) {
        l_msg[l_subscript++] = parseInt(IsUseMark);
    }
    else {
        alert("未配置是否使用靶标");
        return;
    }
    //设置靶标半径
    let l_MarkSize = $("#markSize").numberbox("getValue") * 1000;
    l_subscript = intTo32Byte(l_MarkSize, l_msg, l_subscript);
    addTail_(l_msg);
    c_device.MSG_SETCMD_MARKMODE.start(l_msg);
}
//end region 

//region 
function readGround() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.QUERYCMD_USEFLOOR);
    strToAscii(c_DeviceName, l_msg, 26)
    addTail_(l_msg);
    c_device.MSG_QUERYCMD_USEFLOOR.start(l_msg);
}

function fillLidarUseFloor(pArray, l_subscript) {
    $("input[type='radio'][name='isUseGround'][value=" + pArray[l_subscript++] + "]").prop('checked', 'true');
}

function setUseGroundFun() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_DEVICE, CMD_DEVICE.SETCMD_USEFLOOR);
    //读取雷达名称
    let l_subscript = strToAscii(c_DeviceName, l_msg, 26);
    //设置是否使用地面
    let IsUseGround = $('input[name=isUseGround]:checked').val();
    if (1 == IsUseGround || 0 == IsUseGround) {
        l_msg[l_subscript++] = parseInt(IsUseGround);
    }
    else {
        alert("是否使用地图未设置");
    }
    addTail_(l_msg);
    c_device.MSG_SETCMD_USEFLOOR.start(l_msg);
}

function fillWebPose(pArray) {

}

function sendResetSLAMCMD() {
    //已开启则设置重启
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_SLAMCONTROL);
    l_msg[26] = 2;
    addTail_(l_msg);
    c_master.MSG_SETCMD_SLAMCONTROL.start(l_msg, 60000);
}
//end region

//region 

//end region 
