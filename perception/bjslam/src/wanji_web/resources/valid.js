/*
 * @Author: your name
 * @Date: 2021-05-23 22:52:18
 * @LastEditTime: 2022-10-14 14:21:47
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @Description: In User Settings Edit
 * @FilePath: /wanji_web/resources/valid.js
 */


$.extend($.fn.textbox.defaults.rules, {
    number: {
        validator: function (value, param) {
            return /^[0-9]*$/.test(value);
        },
        message: "不合法（只输入数字）"
    },
    checkLength: {
        validator: function (value, param) {
            return param[0] >= get_length(value);
        },
        message: '请输入最大{0}位字符'
    },
    specialCharacter: {
        validator: function (value, param) {
            var reg = new RegExp("[`~!@#$^&*()=|{}':;'\\[\\]<>~！@#￥……&*（）——|{}【】‘；：”“'、？]");
            return !reg.test(value);
        },
        message: '不允许输入特殊字符'
    },
    unnormal: {// 验证是否包含空格和非法字符
        validator: function (value) {
            return /.+/i.test(value);
        },
        message: '输入值不能为空和包含其他非法字符'
    },
    integer: {// 验证整数 可正负数
        validator: function (value) {
            //return /^[+]?[1-9]+\d*$/i.test(value);

            return /^([+]?[0-9])|([-]?[0-9])+\d*$/i.test(value);
        },
        message: '请输入整数'
    },
    ip: {// 验证IP地址
        validator: function (value) {
            // return /d+.d+.d+.d+$/i.test(value);
            return /\d+\.\d+\.\d+\.\d+/.test(value);
        },
        message: '不合法，参照格式***********'
    },
    intOrFloat: {// 验证整数或小数
        validator: function (value) {
            return /^\d+(\.\d+)?$/i.test(value);
        },
        message: '请输入数字，并确保格式正确'
    },
    username: {// 验证用户名 最短3 最长15
        validator: function (value) {
            return /^[a-zA-Z][a-zA-Z0-9_-]{2,14}$/i.test(value);
        },
        message: '不合法（字母开头，允许3-15字节，允许字母数字下划线）'
    },
    strname: {// 验证字符串 最短1 最长255
        validator: function (value) {
            return /^[a-zA-Z0-9_-]{0,255}$/i.test(value);
        },
        message: '不合法（要求1-255字节，允许字母数字下划线）'
    },

});
