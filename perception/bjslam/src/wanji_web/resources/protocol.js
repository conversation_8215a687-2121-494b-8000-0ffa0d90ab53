
//判定要克隆的对象是不是引用类型，如果是引用类型，则继续迭代,如果该项是基本类型，则直接复制。
var ros = new ROSLIB.Ros();
var ipRosServer = 'localhost';
//var ipRosServer = '**************';
var addrRosServer = 'ws://' + ipRosServer + ':9090';
var WEB_VERSION = "V2.0.0.1";
function connectRos() {
    ros.connect(addrRosServer);
};

function closeRos() {
    ros.close();
};


var pubNetTopic = new ROSLIB.Topic({
    ros: ros,
    name: '/web_from',
    messageType: 'std_msgs/Int32MultiArray'
});

// Subscribing to a Topic
var subTopic = new ROSLIB.Topic({
    ros: ros,
    name: '/web_to',
    messageType: 'std_msgs/Int32MultiArray'
});

function deepClone(obj) {
    return JSON.parse(JSON.stringify(obj))
}

function sleep(delay) {
    var start = new Date().getTime();
    while (new Date().getTime() < start + delay);
}

class SendMessage {

    constructor() {
        this.c_send;
        this.c_msg;
    }

    start(p_message, p_time = 1000) {
        //避免多次start 未stop 导致无法stop
        // let l_msg=deepClone(p_message);
        if (typeof this.c_send != "undefined") {
            this.c_send = window.clearInterval(this.c_send);
        }
        sendMsg(p_message);
        this.c_send = window.setInterval(function () {
            sendMsg(p_message);
        }, p_time);

    }
    stop() {
        if (typeof this.c_send != "undefined") {
            this.c_send = window.clearInterval(this.c_send);
        }
    }
}

function sendMsg(pChar) {
    //帧长检测
    lengthCheck(pChar);
    //校验检测
    bcc16ArrayCheck(pChar);
    pubNetData(pChar);
    // console.log("sendMsg: ",pChar);
}

function pubNetData(netData_) {
    //data: 空格得有
    l_netData = new ROSLIB.Message({ data: netData_ });
    pubNetTopic.publish(l_netData);

}

function lengthCheck(pArray) {
    addArray(pArray, 2, decToHex(pArray.length - 4, 4));
}

function decToHex(pNum, pLength) {
    if (pNum < 0) {
        return (parseInt(pNum, 10) >>> 0).toString(16).toUpperCase().padLeft('F', pLength);
    }
    return (parseInt(pNum, 10) >>> 0).toString(16).toUpperCase().padLeft('0', pLength);
}

//功能：将浮点数四舍五入，取小数点后3位 
function toDecimal(x) {
    var f = parseFloat(x);
    if (isNaN(f)) {
        return;
    }
    f = Math.round(x * 1000) / 1000;
    return f;
}

//高位补0，eg hex = hex.padLeft('0', 2);
String.prototype.padLeft = function (padChar, width) {
    var ret = this;
    while (ret.length < width) {
        if (ret.length + padChar.length < width) {
            ret = padChar + ret;
        }
        else {
            ret = padChar.substring(0, width - ret.length) + ret;
        }
    }
    return ret;
};

function addArray(pNetArray, pStart, pStr) {
    for (let i = 0; i < pStr.length; i = i + 2) {
        pNetArray[pStart + i / 2] = parseInt(pStr.substring(i, i + 2), 16);
    }
}

//BCC异或校验 
function bcc16ArrayCheck(charArr) {

    let res = 0;
    let charBcc = [];
    for (var i = 2; i < charArr.length - 4; i++) {
        res = res ^ parseInt(charArr[i])
    }

    let lStr = res.toString(16).toUpperCase().padLeft('0', 4);
    charArr[charArr.length - 4] = Number("0x" + lStr.substring(0, 2));
    charArr[charArr.length - 3] = Number("0x" + lStr.substring(2, 4));
}

function subNetData_() {
    subTopic.subscribe(function (message) { callbackNew(message.data) });
}

function unsubNetData_() {
    subTopic.unsubscribe(function (message) {
        callbackNew(message.data)
    });
}

function charToUchar(recvData) {
    for (let index = 0; index < recvData.length; index++) {
        let l_temp = recvData[index];
        if (l_temp < 0) {
            recvData[index] = 256 + l_temp;
        }
    }
}

function sendSaveParamCMD() {
    console.log("保存参数");
    //保存系统参数
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.SETCMD_SAVEPARAM);
    addTail_(l_msg);
    c_master.MSG_SETCMD_SAVEPARAM.start(l_msg);
}

//查询slam状态
function QuerySlamState() {
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_SLAMSTATE);
    addTail_(l_msg);
    c_master.MSG_QUERYCMD_SLAMSTATE.start(l_msg, 1000000);
}

function QueryErrorCode() {
    document.getElementById("errcode").innerHTML = "  故障码:未刷新 ";
    // 查询故障码
    let l_msg = PROTArray.slice();
    setCMDID(l_msg, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_ERRORCODE);
    addTail_(l_msg);
    c_master.MSG_QUERYCMD_ERRORCODE.start(l_msg);
}

function callbackNew(recvData) {
    // console.log("recvData:" ,recvData);
    if (recvData.length < 34)
        return;
    charToUchar(recvData);
    //帧头帧尾校验
    if (recvData[0] == 0XFF && recvData[1] == 0XAA && recvData[recvData.length - 2] == 0XEE && recvData[recvData.length - 1] == 0XEE) {
        // 应答帧
        if (recvData[11] == 2) {
            switch (recvData[22]) {
                case CMDTYPE.CMD_SLAVER:
                    selectSlaverProtocol(recvData);
                    break;
                case CMDTYPE.CMD_MASTER:
                    selectMasterProtocol(recvData);
                    break;
                case CMDTYPE.CMD_DEVICE:
                    selectDeviceProtocol(recvData);
                    break;
                case CMDTYPE.CMD_PARAM:
                    selectParamProtocol(recvData);
                    break;
                case CMDTYPE.CMD_PRVT:
                    selectPrivateProtocol(recvData);
                    break;
                default:
                    break;
            }
        }
    }
}
class CMDTYPE {
    static get CMD_SLAVER() { return 0x0A; }
    static get CMD_MASTER() { return 0x0B; }
    static get CMD_DEVICE() { return 0x0C; }
    static get CMD_PARAM() { return 0x0D; }
    static get CMD_PRVT() { return 0x0E; }
}

class CMD_SLAVER {
    static get SETCMD_WORKMODE() { return 1; }     //配置工作模式
    static get SETCMD_VIEWLIDAR() { return 3; }   // 设置扫描可视化
    static get SETCMD_VIEWMAP() { return 5; }      // 设置地图可视化
    static get SETCMD_SLAMMAPINFO() { return 7; }  //设置SLAM地图及分辨率
    static get SETCMD_SAVEMAP() { return 9; }      //保存地图
    static get SETCMD_CURRPOSE() { return 11; }    //设置当前位姿
    static get SETCMD_MAPCORRECT() { return 13; }  //设置地图校正
    static get SETCMD_CARCORRECT() { return 15; }  // 设置车体校正
    static get SETCMD_VIEWMODE() { return 17; }  // 设置点云模式 启动/关闭 2D/3D
    static get SETCMD_MAPPINGMODE() { return 19; }     //设置建图模式
    static get SETCMD_MOTIONMODE() { return 21; }       // 设置运动模式

    static get QUERYCMD_WORKMODE() { return 2; }        //查询工作模式
    static get QUERYCMD_VIEWLIDAR() { return 4; }       // 查询扫描可视化
    static get QUERYCMD_VIEWMAP() { return 6; }         // 查询地图可视化
    static get QUERYCMD_SLAMMAPINFO() { return 8; }     //查询当前地图及分辨率
    static get QUERYCMD_CURRLASERPOSE() { return 10; }       //获取当前Laser位姿
    static get QUERYCMD_MAPCORRECT() { return 12; }     //查询地图校正
    static get QUERYCMD_CARCORRECT() { return 14; }     // 设置车体校正
    static get QUERYCMD_CURRTIMESTAMP() { return 16; }  //获取当前时间戳
    static get QUERYCMD_PROCVERSION() { return 18; }    //查询程序版本号
    static get QUERYCMD_CURRAGVPOSE() { return 22; }    // 查询当前AGV位姿
    static get QUERYCMD_VIEWMODE() { return 24; }    // 查询点云模式 2D/3D
    static get QUERYCMD_MAPPINGMODE() { return 26; }     //查询建图模式
    static get QUERYCMD_MOTIONMODE() { return 28; }     // 查询运动模式    
    constructor() {
        this.MSG_SETCMD_WORKMODE = new SendMessage();
        this.MSG_SETCMD_VIEWLIDAR = new SendMessage();
        this.MSG_SETCMD_VIEWMAP = new SendMessage();
        this.MSG_SETCMD_SLAMMAPINFO = new SendMessage();
        this.MSG_SETCMD_SAVEMAP = new SendMessage();
        this.MSG_SETCMD_CURRPOSE = new SendMessage();
        this.MSG_SETCMD_MAPCORRECT = new SendMessage();
        this.MSG_SETCMD_CARCORRECT = new SendMessage();
        this.MSG_SETCMD_VIEWMODE = new SendMessage();
        this.MSG_SETCMD_MAPPINGMODE = new SendMessage();
        this.MSG_SETCMD_MOTIONMODE = new SendMessage();

        this.MSG_QUERYCMD_WORKMODE = new SendMessage();
        this.MSG_QUERYCMD_VIEWLIDAR = new SendMessage();
        this.MSG_QUERYCMD_VIEWMAP = new SendMessage();
        this.MSG_QUERYCMD_SLAMMAPINFO = new SendMessage();
        this.MSG_QUERYCMD_CURRLASERPOSE = new SendMessage();
        this.MSG_QUERYCMD_MAPCORRECT = new SendMessage();
        this.MSG_QUERYCMD_CARCORRECT = new SendMessage();
        this.MSG_QUERYCMD_CURRTIMESTAMP = new SendMessage();
        this.MSG_QUERYCMD_PROCVERSION = new SendMessage();
        this.MSG_QUERYCMD_CURRAGVPOSE = new SendMessage();
        this.MSG_QUERYCMD_VIEWMODE = new SendMessage();
        this.MSG_QUERYCMD_MAPPINGMODE = new SendMessage();
        this.MSG_QUERYCMD_MOTIONMODE = new SendMessage();
    }
    stopAllSend() {
        this.MSG_SETCMD_WORKMODE.stop();
        this.MSG_SETCMD_VIEWLIDAR.stop();
        this.MSG_SETCMD_VIEWMAP.stop();
        this.MSG_SETCMD_SLAMMAPINFO.stop();
        this.MSG_SETCMD_SAVEMAP.stop();
        this.MSG_SETCMD_CURRPOSE.stop();
        this.MSG_SETCMD_MAPCORRECT.stop();
        this.MSG_SETCMD_CARCORRECT.stop();
        this.MSG_SETCMD_VIEWMODE.stop();
        this.MSG_SETCMD_MAPPINGMODE.stop();
        this.MSG_SETCMD_MOTIONMODE.stop();

        this.MSG_QUERYCMD_WORKMODE.stop();
        this.MSG_QUERYCMD_VIEWLIDAR.stop();
        this.MSG_QUERYCMD_VIEWMAP.stop();
        this.MSG_QUERYCMD_SLAMMAPINFO.stop();
        this.MSG_QUERYCMD_CURRLASERPOSE.stop();
        this.MSG_QUERYCMD_MAPCORRECT.stop();
        this.MSG_QUERYCMD_CARCORRECT.stop();
        this.MSG_QUERYCMD_CURRTIMESTAMP.stop();
        this.MSG_QUERYCMD_PROCVERSION.stop();
        this.MSG_QUERYCMD_CURRAGVPOSE.stop();
        this.MSG_QUERYCMD_VIEWMODE.stop();
        this.MSG_QUERYCMD_MAPPINGMODE.stop();
        this.MSG_QUERYCMD_MOTIONMODE.stop();
    }
}
class CMD_MASTER {
    static get SETCMD_DRIVERMODE() { return 1; }        // 配置工作模式
    static get SETCMD_DELETEMAP() { return 3; }         // 设置删除地图
    static get SETCMD_LASERPCAPNAME() { return 5; }     // 设置雷达离线PCAP数据包名
    static get SETCMD_AGVPCAPNAME() { return 7; }       // 设置AGV离线PCAP数据包名
    static get SETCMD_DRIVERCONTROL() { return 9; }     // 设置驱动控制 0关1启
    static get SETCMD_DRIVERPLAYRATE() { return 11; }   // 设置播放速率
    static get SETCMD_DELETEPCAP() { return 13; }       // 设置删除PCAP数据包
    static get SETCMD_RECORDDATA() { return 15; }       // 设置录制数据
    static get SETCMD_TIMEINTERVAL() { return 17; }     // 设置录制间隔
    static get SETCMD_NETCFG() { return 19; }           // 设置网卡配置自动保存
    static get SETCMD_NETRESET() { return 21; }         // 设置还原网卡配置
    static get SETCMD_SLAMCONTROL() { return 23; }        // 设置控制SLAM
    static get SETCMD_SECRETKEY() { return 25; }        // 密钥(if(check) save)
    static get SETCMD_ADDLASER() { return 27; }         // 增加laser
    static get SETCMD_DELETELASER() { return 29; }      // 删除laser
    static get SETCMD_SAVEPARAM() { return 31; }          // 保存参数
    static get SETCMD_LASERNET() { return 33; }          // 设置雷达网卡
    static get SETCMD_AGVNET() { return 35; }          // 设置AGV网卡
    static get SETCMD_LOGPATH() { return 37; }          // 设置日志路径
    static get SETCMD_RESETPOSEUI() { return 39; }          // 重置位姿UI

    static get QUERYCMD_DRIVERMODE() { return 2; }     // 查询雷达工作模式
    static get QUERYCMD_MAPLIST() { return 4; }        // 查询地图List
    static get QUERYCMD_LASERPCAPNAME() { return 6; }       // 查询雷达离线PCAP数据包名
    static get QUERYCMD_AGVPCAPNAME() { return 8; }         // 查询AGV离线PCAP数据包名
    static get QUERYCMD_PLAYBAGSTATUS() { return 10; }  // 查询驱动控制状态
    static get QUERYCMD_DRIVERPLAYRATE() { return 12; } // 查询播放速率
    static get QUERYCMD_PCAPLIST() { return 14; }       // 查询PCAP列表
    static get QUERYCMD_RECORDDATA() { return 16; }     // 查询录制状态
    static get QUERYCMD_RECORDTIME() { return 18; }     // 查询录制间隔
    static get QUERYCMD_NETLIST() { return 20; }        // 查询网卡名List
    static get QUERYCMD_NETCFG() { return 22; }         // 查询网卡配置
    static get QUERYCMD_SAVEPOSE() { return 24; }      // 查询网卡状态
    static get QUERYCMD_SLAMSTATE() { return 26; }     // 查询SLAM启动状态
    static get QUERYCMD_SECRETKEY() { return 28; }     // 查询密钥是否解密
    static get QUERYCMD_MASTERSTATUS() { return 30; }     // 查询Master是否启动 用于启动Web控件
    static get QUERYCMD_LASERNET() { return 32; }     // 查询雷达网卡
    static get QUERYCMD_AGVNET() { return 34; }     // 查询AGV网卡
    static get QUERYCMD_LOGPATH() { return 36; }   //查询日志路径
    static get QUERYCMD_ERRORCODE() { return 38; }   //查询故障码
    static get QUERYCMD_LASERSTATE() { return 40; }    //查询雷达状态

    constructor() {
        this.MSG_SETCMD_DRIVERMODE = new SendMessage();
        this.MSG_SETCMD_DELETEMAP = new SendMessage();
        this.MSG_SETCMD_LASERPCAPNAME = new SendMessage();
        this.MSG_SETCMD_AGVPCAPNAME = new SendMessage();
        this.MSG_SETCMD_DRIVERCONTROL = new SendMessage();
        this.MSG_SETCMD_DRIVERPLAYRATE = new SendMessage();
        this.MSG_SETCMD_DELETEPCAP = new SendMessage();
        this.MSG_SETCMD_RECORDDATA = new SendMessage();
        this.MSG_SETCMD_TIMEINTERVAL = new SendMessage();
        this.MSG_SETCMD_NETCFG = new SendMessage();
        this.MSG_SETCMD_NETRESET = new SendMessage();
        this.MSG_SETCMD_SLAMCONTROL = new SendMessage();
        this.MSG_SETCMD_SECRETKEY = new SendMessage();
        this.MSG_SETCMD_ADDLASER = new SendMessage();
        this.MSG_SETCMD_DELETELASER = new SendMessage();
        this.MSG_SETCMD_SAVEPARAM = new SendMessage();
        this.MSG_SETCMD_LASERNET = new SendMessage();
        this.MSG_SETCMD_AGVNET = new SendMessage();
        this.MSG_SETCMD_LOGPATH = new SendMessage();
        this.MSG_SETCMD_RESETPOSEUI = new SendMessage();

        this.MSG_QUERYCMD_DRIVERMODE = new SendMessage();
        this.MSG_QUERYCMD_MAPLIST = new SendMessage();
        this.MSG_QUERYCMD_LASERPCAPNAME = new SendMessage();
        this.MSG_QUERYCMD_AGVPCAPNAME = new SendMessage();
        this.MSG_QUERYCMD_PLAYBAGSTATUS = new SendMessage();
        this.MSG_QUERYCMD_DRIVERPLAYRATE = new SendMessage();
        this.MSG_QUERYCMD_PCAPLIST = new SendMessage();
        this.MSG_QUERYCMD_RECORDDATA = new SendMessage();
        this.MSG_QUERYCMD_RECORDTIME = new SendMessage();
        this.MSG_QUERYCMD_NETLIST = new SendMessage();
        this.MSG_QUERYCMD_NETCFG = new SendMessage();
        this.MSG_QUERYCMD_SAVEPOSE = new SendMessage();
        this.MSG_QUERYCMD_SLAMSTATE = new SendMessage();
        this.MSG_QUERYCMD_SECRETKEY = new SendMessage();
        this.MSG_QUERYCMD_MASTERSTATUS = new SendMessage();
        this.MSG_QUERYCMD_LASERNET = new SendMessage();
        this.MSG_QUERYCMD_AGVNET = new SendMessage();
        this.MSG_QUERYCMD_LOGPATH = new SendMessage();
        this.MSG_QUERYCMD_ERRORCODE = new SendMessage();
        this.MSG_QUERYCMD_LASERSTATE = new SendMessage();
    }

    stopAllSend() {
        this.MSG_SETCMD_DRIVERMODE.stop();
        this.MSG_SETCMD_DELETEMAP.stop();
        this.MSG_SETCMD_LASERPCAPNAME.stop();
        this.MSG_SETCMD_AGVPCAPNAME.stop();
        this.MSG_SETCMD_DRIVERCONTROL.stop();
        this.MSG_SETCMD_DRIVERPLAYRATE.stop();
        this.MSG_SETCMD_DELETEPCAP.stop();
        this.MSG_SETCMD_RECORDDATA.stop();
        this.MSG_SETCMD_TIMEINTERVAL.stop();
        this.MSG_SETCMD_NETCFG.stop();
        this.MSG_SETCMD_NETRESET.stop();
        this.MSG_SETCMD_SLAMCONTROL.stop();
        this.MSG_SETCMD_SECRETKEY.stop();
        this.MSG_SETCMD_ADDLASER.stop();
        this.MSG_SETCMD_DELETELASER.stop();
        this.MSG_SETCMD_SAVEPARAM.stop();
        this.MSG_SETCMD_LASERNET.stop();
        this.MSG_SETCMD_AGVNET.stop();
        this.MSG_SETCMD_LOGPATH.stop();
        this.MSG_SETCMD_RESETPOSEUI.stop();

        this.MSG_QUERYCMD_DRIVERMODE.stop();
        this.MSG_QUERYCMD_MAPLIST.stop();
        this.MSG_QUERYCMD_LASERPCAPNAME.stop();
        this.MSG_QUERYCMD_AGVPCAPNAME.stop();
        this.MSG_QUERYCMD_PLAYBAGSTATUS.stop();
        this.MSG_QUERYCMD_DRIVERPLAYRATE.stop();
        this.MSG_QUERYCMD_PCAPLIST.stop();
        this.MSG_QUERYCMD_RECORDDATA.stop();
        this.MSG_QUERYCMD_RECORDTIME.stop();
        this.MSG_QUERYCMD_NETLIST.stop();
        this.MSG_QUERYCMD_NETCFG.stop();
        this.MSG_QUERYCMD_SAVEPOSE.stop();
        this.MSG_QUERYCMD_SLAMSTATE.stop();
        this.MSG_QUERYCMD_SECRETKEY.stop();
        this.MSG_QUERYCMD_MASTERSTATUS.stop();
        this.MSG_QUERYCMD_LASERNET.stop();
        this.MSG_QUERYCMD_AGVNET.stop();
        this.MSG_QUERYCMD_LOGPATH.stop();
        this.MSG_QUERYCMD_ERRORCODE.stop();
        this.MSG_QUERYCMD_LASERSTATE.stop();
    }
}

class CMD_DEVICE {
    static get SETCMD_LASERINSTALLXYZ() { return 1; }        //设置雷达安装位置
    static get SETCMD_LASERINSTALLANGLE() { return 3; }      //设置雷达安装角度
    static get SETCMD_LASERDISBLIND() { return 5; }          //设置距离盲区
    static get SETCMD_LASERANGBLIND() { return 7; }         //设置角度盲区
    static get SETCMD_USEFLOOR() { return 9; }               //设置使用地面
    static get SETCMD_LASERHIGHT() { return 11; }  //设置雷达高度
    static get SETCMD_MARKMODE() { return 13; }    //设置靶标功能
    static get SETCMD_HORIZONTALCALIBRATION() { return 15; }  //设置水平校准
    static get SETCMD_MULLASERCALIBRATION() { return 17; }    //设置多雷达标定
    static get SETCMD_HORIZONTALCALIBNUM() { return 19; }    //设置水平校准次数
    static get SETCMD_MULLASERCALIBRATIONINI() { return 21; }    //设置多雷达标定初始化
    static get SETCMD_LASERENABLE() { return 23; }    //设置雷达使能

    static get QUERYCMD_LASERINSTALLXYZ() { return 2; }        //查询雷达安装位置
    static get QUERYCMD_LASERINSTALLANGLE() { return 4; }      //查询雷达安装角度
    static get QUERYCMD_LASERDISBLIND() { return 6; }          //查询距离盲区
    static get QUERYCMD_LASERANGBLIND() { return 8; }          //查询雷达角度盲区
    static get QUERYCMD_USEFLOOR() { return 10; }               //查询使用地面
    static get QUERYCMD_LASERHIGHT() { return 12; }  //查询雷达高度
    static get QUERYCMD_MARKMODE() { return 14; }     //查询靶标功能
    static get QUERYCMD_HORIZONTALCALIBRATION() { return 16; }  //查询水平校准结果
    static get QUERYCMD_MULLASERCALIBRATION() { return 18; }   //查询多雷达标定结果   
    static get QUERYCMD_LIDARSTATUS() { return 20; }    // 查询雷达状态
    static get QUERYCMD_HORIZONTALCALIBNUM() { return 22; }    // 查询水平校准次数
    static get QUERYCMD_LASERENABLE() { return 24; }    // 查询雷达使能

    constructor() {
        this.MSG_SETCMD_LASERINSTALLXYZ = new SendMessage();
        this.MSG_SETCMD_LASERINSTALLANGLE = new SendMessage();
        this.MSG_SETCMD_LASERDISBLIND = new SendMessage();
        this.MSG_SETCMD_LASERANGBLIND = new SendMessage();
        this.MSG_SETCMD_USEFLOOR = new SendMessage();
        this.MSG_SETCMD_LASERHIGHT = new SendMessage();
        this.MSG_SETCMD_MARKMODE = new SendMessage();
        this.MSG_SETCMD_HORIZONTALCALIBRATION = new SendMessage();
        this.MSG_SETCMD_MULLASERCALIBRATION = new SendMessage();
        this.MSG_SETCMD_HORIZONTALCALIBNUM = new SendMessage();
        this.MSG_SETCMD_MULLASERCALIBRATIONINI = new SendMessage();
        this.MSG_SETCMD_LASERENABLE = new SendMessage();

        this.MSG_QUERYCMD_LASERINSTALLXYZ = new SendMessage();
        this.MSG_QUERYCMD_LASERINSTALLANGLE = new SendMessage();
        this.MSG_QUERYCMD_LASERDISBLIND = new SendMessage();
        this.MSG_QUERYCMD_LASERANGBLIND = new SendMessage();
        this.MSG_QUERYCMD_USEFLOOR = new SendMessage();
        this.MSG_QUERYCMD_LASERHIGHT = new SendMessage();
        this.MSG_QUERYCMD_MARKMODE = new SendMessage();
        this.MSG_QUERYCMD_HORIZONTALCALIBRATION = new SendMessage();
        this.MSG_QUERYCMD_MULLASERCALIBRATION = new SendMessage();
        this.MSG_QUERYCMD_LIDARSTATUS = new SendMessage();
        this.MSG_QUERYCMD_HORIZONTALCALIBNUM = new SendMessage();
        this.MSG_QUERYCMD_LASERENABLE = new SendMessage();
    }

    stopAllSend() {
        this.MSG_SETCMD_LASERINSTALLXYZ.stop();
        this.MSG_SETCMD_LASERINSTALLANGLE.stop();
        this.MSG_SETCMD_LASERDISBLIND.stop();
        this.MSG_SETCMD_LASERANGBLIND.stop();
        this.MSG_SETCMD_USEFLOOR.stop();
        this.MSG_SETCMD_LASERHIGHT.stop();
        this.MSG_SETCMD_MARKMODE.stop();
        this.MSG_SETCMD_HORIZONTALCALIBRATION.stop();
        this.MSG_SETCMD_MULLASERCALIBRATION.stop();
        this.MSG_SETCMD_HORIZONTALCALIBNUM.stop();
        this.MSG_SETCMD_MULLASERCALIBRATIONINI.stop();

        this.MSG_QUERYCMD_LASERINSTALLXYZ.stop();
        this.MSG_QUERYCMD_LASERINSTALLANGLE.stop();
        this.MSG_QUERYCMD_LASERDISBLIND.stop();
        this.MSG_QUERYCMD_LASERANGBLIND.stop();
        this.MSG_QUERYCMD_USEFLOOR.stop();
        this.MSG_QUERYCMD_LASERHIGHT.stop();
        this.MSG_QUERYCMD_MARKMODE.stop();
        this.MSG_QUERYCMD_HORIZONTALCALIBRATION.stop();
        this.MSG_QUERYCMD_MULLASERCALIBRATION.stop();
        this.MSG_QUERYCMD_LIDARSTATUS.stop();
        this.MSG_QUERYCMD_HORIZONTALCALIBNUM.stop();
    }
}

class CMD_PARAM {
    static get SETCMD_CLOCKSOURCE() { return 5; }      // 设置时钟源
    static get SETCMD_LASERNETPARAM() { return 7; }   //设置雷达网络参数
    static get SETCMD_LASERBASEPARAM() { return 9; }  //设置雷达基础参数
    static get SETCMD_AGVNETPARAM() { return 11; }               //修改agv网络参数
    static get SETCMD_LOADDEFAULTPARAM() { return 13; }    //恢复出厂配置
    static get SETCMD_LASERISBASE() { return 15; }    //设置基准雷达

    static get QUERYCMD_CLOCKSOURCE() { return 6; }      // 查询时钟源
    static get QUERYCMD_LASERNETPARAM() { return 8; }    //查询雷达网络参数
    static get QUERYCMD_LASERBASEPARAM() { return 10; }  //查询雷达基础参数
    static get QUERYCMD_AGVNETPARAM() { return 12; }     //查询agv网络参数   
    static get QUERYCMD_LIDARNUM() { return 14; }              //查询雷达个数 
    static get QUERYCMD_LASERISBASE() { return 16; }    // 查询是否为基准雷达
    constructor() {
        this.MSG_SETCMD_CLOCKSOURCE = new SendMessage();
        this.MSG_SETCMD_LASERNETPARAM = new SendMessage();
        this.MSG_SETCMD_LASERBASEPARAM = new SendMessage();
        this.MSG_SETCMD_AGVNETPARAM = new SendMessage();
        this.MSG_SETCMD_LOADDEFAULTPARAM = new SendMessage();
        this.MSG_SETCMD_LASERISBASE = new SendMessage();

        this.MSG_QUERYCMD_CLOCKSOURCE = new SendMessage();
        this.MSG_QUERYCMD_LASERNETPARAM = new SendMessage();
        this.MSG_QUERYCMD_LASERBASEPARAM = new SendMessage();
        this.MSG_QUERYCMD_AGVNETPARAM = new SendMessage();
        this.MSG_QUERYCMD_LIDARNUM = new SendMessage();
        this.MSG_QUERYCMD_LASERISBASE = new SendMessage();
    }

    stopAllSend() {
        this.MSG_SETCMD_CLOCKSOURCE.stop();
        this.MSG_SETCMD_LASERNETPARAM.stop();
        this.MSG_SETCMD_LASERBASEPARAM.stop();
        this.MSG_SETCMD_AGVNETPARAM.stop();
        this.MSG_SETCMD_LOADDEFAULTPARAM.stop();
        this.MSG_SETCMD_LASERISBASE.stop();

        this.MSG_QUERYCMD_CLOCKSOURCE.stop();
        this.MSG_QUERYCMD_LASERNETPARAM.stop();
        this.MSG_QUERYCMD_LASERBASEPARAM.stop();
        this.MSG_QUERYCMD_AGVNETPARAM.stop();
        this.MSG_QUERYCMD_LIDARNUM.stop();
        this.MSG_QUERYCMD_LASERISBASE.stop();
    }
}

class CMD_PRVT {
    static get QUERYCMD_WEBPOSE() { return 6; }     //查询Pose
    constructor() {
        this.MSG_QUERYCMD_WEBPOSE = new SendMessage();
    }

    stopAllSend() {
        this.MSG_QUERYCMD_WEBPOSE.stop();
    }
}

class DevStatus {
    static get DEFAULTSTATUS() { return 0; }
    static get WAITCONNECT() { return 1; }
    static get VIRTUALDEV() { return 2; }

    static get DATAWAIT() { return 3; }
    static get DATADATAERROR() { return 4; }
    static get PCAPRUN() { return 5; }
    static get PCAPSTOP() { return 6; }
    static get DEVCONNECT() { return 7; }

    static get LOADPARAMERROR() { return 8; }
    static get GETPARAMERROR() { return 9; }
    static get NOFINDDEV() { return 10; }
    static get NETNOALIVE() { return 11; }
    static get NETNOIP() { return 12; }
    static get NETIPERROR() { return 13; }
    static get NETSETERROR() { return 14; }
    static get PCAPOVER() { return 15; }
    static get PCAPERROR() { return 16; }
    static get IPINFOERROR() { return 17; }
}

var c_slam = new CMD_SLAVER();
var c_master = new CMD_MASTER();
var c_device = new CMD_DEVICE();
var c_param = new CMD_PARAM();
var c_prvt = new CMD_PRVT();

/*通用协议 26长度*/
var PROTArray = [0xFF, 0xAA, 0x00, 0x1E, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00,
    0x01, 0x01, 0x01, 0x0B, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x0B, 0x01, 0x00,
    0x00];

// 修改主命令和子命令
function setCMDID(pArray, pCMDType, pCMDID) {
    // 协议中主命令 子命令的id为 22 23
    if (pArray.length < 24) {
        console.log("setCMDID Error");
        return;
    }
    pArray[22] = pCMDType;
    pArray[23] = pCMDID;
}

function ascii16ByteToInt(pArray, pOffset) {
    return (pArray[pOffset++] * Math.pow(2, 8) + pArray[pOffset++]);
}

function hex2dec(a, b, c, d) {
    let ret = a * Math.pow(2, 24) + b * Math.pow(2, 16) + c * Math.pow(2, 8) + d;
    if (a > 0x80) {
        a = 0xff - a;
        b = 0xff - b;
        c = 0xff - c;
        d = 0xff - d;
        ret = a * Math.pow(2, 24) + b * Math.pow(2, 16) + c * Math.pow(2, 8) + d + 1;
        ret = -ret;
    }

    return ret;
}

function ascii32ByteToInt(pArray, pOffset) {
    return hex2dec(pArray[pOffset++], pArray[pOffset++], pArray[pOffset++], pArray[pOffset++]);
    // return ((pArray[pOffset++]*Math.pow(2,24) +
    // pArray[pOffset++]*Math.pow(2,16) +
    //                     pArray[pOffset++]*Math.pow(2,8) +
    //                     pArray[pOffset++])| 0xFFFFFFFF00000000);
}

// 根据数组和偏移量 提取str 默认规则为 1字节str长度+string  注意：偏移量外部并未改变
function asciiToStr(pArray, pOffset) {
    let l_strLen = pArray[pOffset];
    pOffset++;
    let l_sStr = "";
    for (let j = 0; j < l_strLen; j++) {
        l_sStr += String.fromCharCode(pArray[pOffset++]);
    }
    // console.log("asciiToStr: ", l_sStr);
    return l_sStr;
}

// 将string写入数组,1字节长度+str
function strToAscii(pStr, pArray, pOffset) {
    // 防止数组长度低于pOffset
    if (pArray.length < pOffset) {
        console.log("Error Please Check");
        for (let i = pArray.length; i < pOffset; i++) {
            pArray.splice(i, 0, 0x00);
        }
    }

    // 写入1字节str长度
    pArray.splice(pOffset, 0, parseInt(pStr.length));
    pOffset++;
    for (let i = 0; i < pStr.length; i++) {
        let lCode = pStr.charCodeAt(i).toString(16).toUpperCase();
        // array.splice(index, howmany , item1~itemX)
        // index 下标  howmany 删除的项目数量 0表示不删除 item1～X 添加的项目 
        pArray.splice(pOffset + 1 + i, 0, parseInt(lCode, 16));
        pOffset++;
    }
    return pOffset;
}

function intTo8Byte(pData, pArray, pOffset) {
    addArray(pArray, pOffset, decToHex(pData, 2));
    return pOffset + 1
}

function intTo16Byte(pData, pArray, pOffset) {
    addArray(pArray, pOffset, decToHex(pData, 4));
    return pOffset + 2;
}

function intTo32Byte(pData, pArray, pOffset) {
    addArray(pArray, pOffset, decToHex(pData, 8));
    return pOffset + 4;
}

/*补0满足最小长度，写入协议校验位+帧尾 校验位准确性发送时更正*/
function addTail_(pArray) {
    // 补0 至 校验位结束
    for (let i = pArray.length; i < 30; i++) {
        pArray.splice(i, 0, 0x00);
    }
    // 校验位
    pArray.splice(pArray.length, 0, 0x00);
    pArray.splice(pArray.length, 0, 0x00);
    // 补帧尾
    pArray.splice(pArray.length, 0, 0xEE);
    pArray.splice(pArray.length, 0, 0xEE);
}

function testAPI() {
    // let l_str = "wenZhi";
    // let lnetSecretKey = PROTArray.slice();
    // setCMDID(lnetSecretKey, CMDTYPE.CMD_SLAVER, CMD_MASTER.SETCMD_DRIVERMODE);
    // strToAscii(l_str, lnetSecretKey, 26)
    // addTail_(lnetSecretKey);
    // c_master.MSG_QUERYCMD_DRIVERMODE.start(lnetSecretKey);

    // lnetSecretKey = PROTArray.slice();
    // setCMDID(lnetSecretKey, CMDTYPE.CMD_SLAVER, CMD_MASTER.SETCMD_AGVPCAPNAME);
    // addTail_(lnetSecretKey);
    // c_master.MSG_SETCMD_AGVPCAPNAME.start(lnetSecretKey);

    //查询Master主程序是否启动
    let l_tmp = PROTArray.slice();
    setCMDID(l_tmp, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_MASTERSTATUS);
    addTail_(l_tmp);
    c_master.MSG_QUERYCMD_MASTERSTATUS.start(l_tmp);
    // c_master.MSG_QUERYCMD_MASTERSTATUS.start(l_tmp);

    // console.log("asciiToStr: ",asciiToStr(lnetSecretKey, 26));
}

function selectSlaverProtocol(recvData) {
    // console.log("slamID:", recvData[23]);
    if (c_DeviceName == "index") {

        switch (recvData[23]) {
            case CMD_SLAVER.SETCMD_WORKMODE:
                c_slam.MSG_SETCMD_WORKMODE.stop();
                if (recvData[27] == 2) {
                    // alert("工作模式切换中，点击[确定]后等待切换完成弹窗!");
                    break;
                }
                $('#waitWarn').dialog('close');
                //重新获取播放状态
                QueryPlayBagStatus();

                //模式失败
                if (recvData[27] == 0) {
                    alert("工作模式切换失败!");
                    resetMode(cLastModel);
                }
                //模式成功
                else if (recvData[27] == 1) {
                    if (recvData[26] == 00) {
                        cLastModel = 0;
                        setModelValue("#model0", "check");
                        $('#CalibTab').dialog('close');
                    }
                    else if (recvData[26] == 01) {
                        cLastModel = 1;
                        setModelValue("#model1", "check");
                    }
                    else if (recvData[26] == 02) {
                        cLastModel = 2;
                        setModelValue("#model2", "check");
                    }
                    else if (recvData[26] == 03) {
                        cLastModel = 3;
                        setModelValue("#model3", "check");
                    }
                    //存储最新上次模式状态
                    cLastModel = $("input[name='model']:checked").val();
                    // 更新本地cookie
                    localStorage.workmode = cLastModel;
                    limitViewPoseBtn();
                    console.log("工作模式切换成功：", recvData[26], cLastModel);
                    $('#startWorkMode').dialog('open');
                    // 提示保存参数
                    $('#saveParamWarn').dialog('open');
                }
                //无论失败与否，都得约束其他模式
                constraintModel(cLastModel);
                break;
            case CMD_SLAVER.SETCMD_VIEWLIDAR:
                c_slam.MSG_SETCMD_VIEWLIDAR.stop();
                console.log("SETCMD_VIEWLIDAR");
                // 控制开关 下层发 上层则显示？  目前是此开关控制下层发不发 但上层可不显示（其实是启动订阅  但无法停止显示）
                // setBtnValue("#viewerLidar", recvData[26]);
                break;
            case CMD_SLAVER.SETCMD_VIEWMAP:
                c_slam.MSG_SETCMD_VIEWMAP.stop();
                // 控制开关 下层发 上层则显示？  目前是此开关控制下层发不发 但上层可不显示（其实是启动订阅  但无法停止显示）
                // setBtnValue("#viewerMap", recvData[26]);
                break;
            case CMD_SLAVER.SETCMD_SLAMMAPINFO:
                //失败则不停的发
                if (recvData[26] == 1) {
                    c_slam.MSG_SETCMD_SLAMMAPINFO.stop();
                }
                break;
            case CMD_SLAVER.SETCMD_SAVEMAP:
                c_slam.MSG_SETCMD_SAVEMAP.stop();
                if (recvData[26] == 2) {
                    // alert("地图保存中，请点击[确定]等待系统弹窗：地图保存成功!");
                    return;
                }
                else if (recvData[26] == 1) {
                    $('#saveMapWarn').dialog('close');
                    $('#saveMapSucc').dialog('open');
                }
                break;
            case CMD_SLAVER.SETCMD_CURRPOSE:
                c_slam.MSG_SETCMD_CURRPOSE.stop();
                break;
            case CMD_SLAVER.SETCMD_MAPCORRECT:
                c_slam.MSG_SETCMD_MAPCORRECT.stop();
                break;
            case CMD_SLAVER.SETCMD_CARCORRECT:
                c_slam.MSG_SETCMD_CARCORRECT.stop();
                break;
            case CMD_SLAVER.SETCMD_VIEWMODE:
                c_slam.MSG_SETCMD_VIEWMODE.stop();
                break;
            case CMD_SLAVER.SETCMD_MAPPINGMODE:
                c_slam.MSG_SETCMD_MAPPINGMODE.stop();
                break;
            case CMD_SLAVER.SETCMD_MOTIONMODE:
                c_slam.MSG_SETCMD_MOTIONMODE.stop();
                break;
            /*******************************************/
            case CMD_SLAVER.QUERYCMD_WORKMODE:
                c_slam.MSG_QUERYCMD_WORKMODE.stop();
                // console.log("checkWorkMode: ", recvData[26]);
                if (recvData[26] == 0) {
                    cLastModel = 0;
                    setModelValue("#model0", "check");
                }
                else if (recvData[26] == 1) {
                    cLastModel = 1;
                    setModelValue("#model1", "check");
                }
                else if (recvData[26] == 2) {
                    cLastModel = 2;
                    setModelValue("#model2", "check");
                }
                else if (recvData[26] == 3) {
                    cLastModel = 3;
                    setModelValue("#model3", "check");
                }
                else if (recvData[26] == 4) {
                    cLastModel = 4
                    setModelValue("#model4", "check");
                }
                //存储最新上次模式状态
                cLastModel = $("input[name='model']:checked").val();
                constraintModel(cLastModel);
                limitViewPoseBtn();
                // 存储本地cookie
                localStorage.workmode = cLastModel;
                break;
            case CMD_SLAVER.QUERYCMD_VIEWLIDAR:
                c_slam.MSG_QUERYCMD_VIEWLIDAR.stop();
                cViewLidarStatus = recvData[26];
                console.log("QUERYCMD_VIEWLIDAR");
                setBtnValue("#viewerLidar", recvData[26]);
                break;
            case CMD_SLAVER.QUERYCMD_VIEWMAP:
                c_slam.MSG_QUERYCMD_VIEWMAP.stop();
                cViewMapStatus = recvData[26];
                setBtnValue("#viewerMap", recvData[26]);
                break;
            case CMD_SLAVER.QUERYCMD_SLAMMAPINFO:
                c_slam.MSG_QUERYCMD_SLAMMAPINFO.stop();
                fillCurrMapInfo(recvData);
                break;
            case CMD_SLAVER.QUERYCMD_CURRLASERPOSE:
                break;
            case CMD_SLAVER.QUERYCMD_MAPCORRECT:
                c_slam.MSG_QUERYCMD_MAPCORRECT.stop();
                fillMapCorrect(recvData);
                break;
            case CMD_SLAVER.QUERYCMD_CARCORRECT:
                c_slam.MSG_QUERYCMD_CARCORRECT.stop();
                fillAgvCorrect(recvData);
                break;
            case CMD_SLAVER.QUERYCMD_CURRTIMESTAMP:
                c_slam.MSG_QUERYCMD_CURRTIMESTAMP.stop();
                break;
            case CMD_SLAVER.QUERYCMD_PROCVERSION:
                c_slam.MSG_QUERYCMD_PROCVERSION.stop();
                fillVERSION(recvData);
                break;
            case CMD_SLAVER.QUERYCMD_CURRAGVPOSE:
                break;
            case CMD_SLAVER.QUERYCMD_VIEWMODE:
                c_slam.MSG_QUERYCMD_VIEWMODE.stop();
                // if(recvData[26] == 0 || recvData[26] == 1)
                if (recvData[26] == 0)
                    $("#viewModel").combobox("setValue", recvData[26]);
                break;
            case CMD_SLAVER.QUERYCMD_MAPPINGMODE:
                if (c_DeviceName != "index") {
                    return;
                }
                c_slam.MSG_QUERYCMD_MAPPINGMODE.stop();
                fillSlamModel(recvData);
                break;
            case CMD_SLAVER.QUERYCMD_MOTIONMODE:
                c_slam.MSG_QUERYCMD_MOTIONMODE.stop();
                fillMovingMode(recvData);
                break;
            default:
                break;
        }
    }



}

function selectMasterProtocol(recvData) {
    // console.log("masterID:", recvData[23]);
    if (c_DeviceName == "index") {
        switch (recvData[23]) {
            case CMD_MASTER.SETCMD_DRIVERMODE:

                c_master.MSG_SETCMD_DRIVERMODE.stop();
                //模式失败
                if (recvData[27] == 0) {
                    resetLidarMode(cLastLidarModel);
                    alert("雷达驱动模式切换失败，单次切换驱动模式后须点击 [菜单栏]-[SLAM] 重启SLAM");
                }
                //模式成功
                else if (recvData[27] == 1) {
                    if (recvData[26] == 00) {
                        cLastLidarModel = 0;
                        setModelValue("#lidarModel0", "check");
                        let recordStatue = $("#recordLidar").switchbutton("options").checked;
                        if(recordStatue == 1)
                            setBtnValue("#recordLidar", false);
                        setBtnEnable("#recordLidar", false);
                    }
                    else if (recvData[26] == 01) {
                        cLastLidarModel = 1;
                        setModelValue("#lidarModel1", "check");
                        setBtnEnable("#recordLidar", true);
                    }
                    //存储最新上次模式状态
                    cLastLidarModel = $("input[name='lidarModel']:checked").val();
                    //提示重启
                    $('#forceResetSLAMWarn').dialog('open');
                    $('#forceResetSLAMWarn').window('center');
                }
                break;
            case CMD_MASTER.SETCMD_DELETEMAP:
                c_master.MSG_SETCMD_DELETEMAP.stop();
                break;
            case CMD_MASTER.SETCMD_AGVPCAPNAME:
                c_master.MSG_SETCMD_AGVPCAPNAME.stop();
                break;
            case CMD_MASTER.SETCMD_DRIVERCONTROL:
                c_master.MSG_SETCMD_DRIVERCONTROL.stop();
                break;
            case CMD_MASTER.SETCMD_DRIVERPLAYRATE:

                break;
            case CMD_MASTER.SETCMD_DELETEPCAP:
                c_master.MSG_SETCMD_DELETEPCAP.stop();
                break;
            case CMD_MASTER.SETCMD_RECORDDATA:
                c_master.MSG_SETCMD_RECORDDATA.stop();
                //ToDoList 返回是否录制雷达 存在问题无法将Btn复位 暂时可通过录制时长观看是否录制成功失败
                let recordStatus = $("#recordLidar").switchbutton("options").checked;
                if (recordStatus != recvData[26])
                    $('#saveMapSucc').dialog('open');
                if (recvData[26] == 0)
                    writeBagStatus(0);
                break;
            case CMD_MASTER.SETCMD_TIMEINTERVAL:
                c_master.MSG_SETCMD_TIMEINTERVAL.stop();
                break;
            case CMD_MASTER.SETCMD_NETCFG:
                {
                    c_master.MSG_SETCMD_NETCFG.stop();
                    let l_iOffset = 26 + recvData[26] + 1;
                    if (recvData[l_iOffset] == 0)
                        alert("网卡配置失败，请确认拥有权限 且格式正确");
                    else
                        alert("网卡配置成功，重启工控机生效");
                    break;
                }
            case CMD_MASTER.SETCMD_NETRESET:
                c_master.MSG_SETCMD_NETRESET.stop();
                let l_iOffset = 26 + recvData[26] + 1;
                if (recvData[l_iOffset] == 0)
                    alert("网卡还原配置失败，请确认拥有权限且存在网卡原始配置文件！");
                else
                    alert("网卡还原配置成功，请重启工控机！");
                break;
            case CMD_MASTER.SETCMD_SLAMCONTROL:
                c_master.MSG_SETCMD_SLAMCONTROL.stop();
                if (recvData[27] == 2) {
                    // alert("请点击[确定]，并等待[SLAM]动作执行完毕弹窗");
                    return;
                }
                QuerySlamState();
                break;
            case CMD_MASTER.SETCMD_SECRETKEY:
                c_master.MSG_SETCMD_SECRETKEY.stop();
                if (recvData[26] == 0)
                    alert("密钥错误，请检查!");
                else {
                    // 设置成功后再次查询一次 
                    let l_tmp = PROTArray.slice();
                    setCMDID(l_tmp, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_SECRETKEY);
                    addTail_(l_tmp);
                    c_master.MSG_QUERYCMD_SECRETKEY.start(l_tmp);
                }
                break;
            case CMD_MASTER.SETCMD_ADDLASER: {
                c_master.MSG_SETCMD_ADDLASER.stop();
                let l_subscript = 26;
                let l_NameSize = recvData[l_subscript];
                let l_LidarName = asciiToStr(recvData, l_subscript);
                l_subscript = l_subscript + l_NameSize + 1;
                if (recvData[l_subscript++] == 0)
                    alert("新增雷达失败，请检查");
                else {
                    LaserUrl = "Laser.html?g_LaserId=" + g_LaserId;
                    addLaserTab(g_LaserId, LaserUrl);
                    $('#AddLidarTab').dialog('close');
                    $('#set4').dialog('close');
                    // 重启SLAM
                    $('#resetSLAMWarn').dialog('open');
                    $('#resetSLAMWarn').window('center');
                }
                break;
            }
            case CMD_MASTER.SETCMD_DELETELASER: {
                c_master.MSG_SETCMD_DELETELASER.stop();
                let l_subscript = 26;
                let l_NameSize = recvData[l_subscript];
                let l_LidarName = asciiToStr(recvData, l_subscript);
                l_subscript = l_subscript + l_NameSize + 1;
                if (recvData[l_subscript++] == 0)
                    alert("删除雷达失败，请检查");
                else {
                    g_LaserId = g_LaserId - 1;
                    let opts = $(c_deleteLaserTab).tabs('options');
                    let bc = opts.onBeforeClose;
                    opts.onBeforeClose = function () { };  // allowed to close now
                    $(c_deleteLaserTab).tabs('close', c_deleteLaserIndex);
                    opts.onBeforeClose = bc;  // restore the event function    
                    // 重启SLAM
                    $('#resetSLAMWarn').dialog('open');
                }
                break;
            }
            case CMD_MASTER.SETCMD_SAVEPARAM:
                c_master.MSG_SETCMD_SAVEPARAM.stop();
                if (recvData[26] == 0)
                    alert("保存参数失败，请检查");
                else
                    $('#saveParamSuccWarn').dialog('open');
                break;
            case CMD_MASTER.SETCMD_LOGPATH:
                c_master.MSG_SETCMD_LOGPATH.stop();
                if (recvData[26] == 0)
                    alert("日志路径设置错误，请检查！");
                break;
            case CMD_MASTER.SETCMD_RESETPOSEUI:
                c_master.MSG_SETCMD_RESETPOSEUI.stop();
                break;
            case CMD_MASTER.SETCMD_AGVNET:
                c_master.MSG_SETCMD_AGVNET.stop();
                if (recvData[26] == 0)
                    alert("设置AGV网卡失败，请检查");
                break;
            /*******************************************/
            case CMD_MASTER.QUERYCMD_DRIVERMODE:
                {
                    c_master.MSG_QUERYCMD_DRIVERMODE.stop();
                    if (recvData[26] == 0) {
                        cLastLidarModel = 0;
                        setModelValue("#lidarModel0", "check");
                        let recordStatue = $("#recordLidar").switchbutton("options").checked;
                        if(recordStatue == 1)
                            setBtnValue("#recordLidar", false);
                        setBtnEnable("#recordLidar", false);
                    }
                    else if (recvData[26] == 1) {
                        cLastLidarModel = 1;
                        setModelValue("#lidarModel1", "check");
                        setBtnEnable("#recordLidar", true);
                    }
                    //存储最新上次模式状态
                    cLastLidarModel = $("input[name='lidarModel']:checked").val();
                    let l_SlamState = $("#ViewSlamState").switchbutton("options").checked;
                    if (!l_SlamState)
                        enableDriverModelChose(0);
                    break;
                }
            case CMD_MASTER.QUERYCMD_MAPLIST:
                c_master.MSG_QUERYCMD_MAPLIST.stop();
                fillMapList(recvData);
                break;
            case CMD_MASTER.QUERYCMD_AGVPCAPNAME:
                c_master.MSG_QUERYCMD_AGVPCAPNAME.stop();
                fillAGVPcapName(recvData);
                break;
            case CMD_MASTER.QUERYCMD_PLAYBAGSTATUS:
                c_master.MSG_QUERYCMD_PLAYBAGSTATUS.stop();
                cPlayPcapStatus = recvData[26];
                setBtnValue("#playPcap", recvData[26]);
                break;
            case CMD_MASTER.QUERYCMD_DRIVERPLAYRATE:

                break;

            case CMD_MASTER.QUERYCMD_RECORDDATA:
                cRecordLidarStatus = recvData[26];
                fillBagStatus(recvData);
                break;
            case CMD_MASTER.QUERYCMD_RECORDTIME:
                //录包不由此关闭循环发送

                break;
            case CMD_MASTER.QUERYCMD_NETLIST:
                c_master.MSG_QUERYCMD_NETLIST.stop();
                fillNetList(recvData);
                break;
            case CMD_MASTER.QUERYCMD_NETCFG:
                c_master.MSG_QUERYCMD_NETCFG.stop();
                fillNetCFG(recvData);
                break;
            case CMD_MASTER.QUERYCMD_SAVEPOSE:
                c_master.MSG_QUERYCMD_SAVEPOSE.stop();
                fillSavePose(recvData);
                break;
            case CMD_MASTER.QUERYCMD_SLAMSTATE:
                // 用于控制SLAM关闭和启动时的判定 //启动之后再查询工作模式等
                let l_SlamState = $("#ViewSlamState").switchbutton("options").checked;
                if (recvData[26] == 0) {
                    // 已关闭
                    $('#waitWarn').dialog('close');
                    $('#CalibTab').dialog('close');
                    c_master.MSG_QUERYCMD_SLAMSTATE.stop();
                    setSetEnable("#SetSlamStateBtn", true);
                    if (l_SlamState)
                        setBtnValue("#ViewSlamState", false);
                    setSetEnable("#openSet4", false);
                    setSetEnable("#openSet5", false);
                    setSetEnable("#openSet9", false);
                    setBtnEnable("#playPcap", false);
                    setSetEnable("#saveMap", false);
                    setSetEnable("#saveSet", false);
                    //雷达状态查询关闭
                    c_master.MSG_QUERYCMD_LASERSTATE.stop();
                    // 提示SLAM启动
                    $('#stopSlamSuccWarn').dialog('open');
                    return;
                }
                else if (recvData[26] == 1) {
                    // 启动中
                    return;
                }
                else if (recvData[26] == 2) {
                    // 启动成功
                    $('#waitWarn').dialog('close');
                    c_master.MSG_QUERYCMD_SLAMSTATE.stop();
                    setSetEnable("#SetSlamStateBtn", true);
                    setSetEnable("#openSet4", true);
                    setSetEnable("#openSet5", true);
                    setSetEnable("#openSet9", true);
                    setBtnValue("#ViewSlamState", true);
                    //雷达状态查询开启
                    let l_tmp = PROTArray.slice();
                    setCMDID(l_tmp, CMDTYPE.CMD_MASTER, CMD_MASTER.QUERYCMD_LASERSTATE);
                    addTail_(l_tmp);
                    c_master.MSG_QUERYCMD_LASERSTATE.start(l_tmp,3000);
                    $('#startSlamSuccWarn').dialog('open');
                    $('#set4').dialog('close');
                }
                else if (recvData[26] == 3) {
                    // 关闭中
                    return;
                }
                else if (recvData[26] == 4) {
                    $('#waitWarn').dialog('close');
                    c_master.MSG_QUERYCMD_SLAMSTATE.stop();
                    setSetEnable("#SetSlamStateBtn", true);
                    setBtnValue("#ViewSlamState", false);
                    setSetEnable("#openSet4", false);
                    setSetEnable("#openSet5", false);
                    setSetEnable("#openSet9", false);
                    alert("SLAM启动错误，请重启工控机!");
                }
                else if (recvData[26] == 5) {
                    $('#waitWarn').dialog('close');
                    alert("SLAM关闭错误，请重启工控机!");
                }
                QueryErrorCode();
                break;
            case CMD_MASTER.QUERYCMD_SECRETKEY:
                c_master.MSG_QUERYCMD_SECRETKEY.stop();
                if (recvData[26] == 1) {
                    //mac校验成功
                    overMac();
                }
                else {
                    // 刷新密钥
                    makeMacQRCode(asciiToStr(recvData, 27));
                    //弹出Mac窗
                    readMac();
                }
                break;
            case CMD_MASTER.QUERYCMD_MASTERSTATUS: {
                c_master.MSG_QUERYCMD_MASTERSTATUS.stop();
                //Master已启动 
                if (recvData[26] == 1) {
                    //释放web按钮操作 + 查询必要状态
                    initWeb();
                    //初始化viewer
                    // initRosConnect();
                }
                else
                    alert("Web控制程序未启动，请检查!");
                break;
            }
            case CMD_MASTER.QUERYCMD_AGVNET:
                c_master.MSG_QUERYCMD_AGVNET.stop();
                fillAgvNet(recvData, 26)
                break;
            case CMD_MASTER.QUERYCMD_PCAPLIST:
                c_master.MSG_QUERYCMD_PCAPLIST.stop();
                fillPcapList(recvData)
                break;
            case CMD_MASTER.QUERYCMD_LOGPATH:
                c_master.MSG_QUERYCMD_LOGPATH.stop();
                fillLogPath(recvData)
                break;
            case CMD_MASTER.QUERYCMD_ERRORCODE:
                c_master.MSG_QUERYCMD_ERRORCODE.stop();
                fillErrorCode(recvData);
                break;
            case CMD_MASTER.QUERYCMD_LASERSTATE:
                fillLidarState(recvData);
                break;
            default:
                break;
        }
    }
    else {
        switch (recvData[23]) {
            case CMD_MASTER.SETCMD_DELETELASER: {
                c_master.MSG_SETCMD_DELETELASER.stop();
                let l_subscript = 26;
                let l_NameSize = recvData[l_subscript];
                let l_LidarName = asciiToStr(recvData, l_subscript);
                l_subscript = l_subscript + l_NameSize + 1;
                if (l_LidarName != c_DeviceName) {
                    return;
                }
                if (recvData[l_subscript++] == 0)
                    alert("删除雷达失败，请检查！");
                else {
                    g_LaserId = g_LaserId - 1;
                    let opts = $(c_deleteLaserTab).tabs('options');
                    let bc = opts.onBeforeClose;
                    opts.onBeforeClose = function () { };  // allowed to close now
                    $(c_deleteLaserTab).tabs('close', c_deleteLaserIndex);
                    opts.onBeforeClose = bc;  // restore the event function    
                    // 重启SLAM
                    sleep(500);
                    $('#resetSLAMWarn').dialog('open');
                    $('#resetSLAMWarn').window('center');
                }
                break;
            }
            case CMD_MASTER.SETCMD_LASERPCAPNAME:
                c_master.MSG_SETCMD_LASERPCAPNAME.stop();

                break;
            case CMD_MASTER.SETCMD_SAVEPARAM:
                c_master.MSG_SETCMD_SAVEPARAM.stop();
                if (recvData[26] == 0)
                    alert("保存参数失败，请检查！");
                else
                    $('#saveParamSuccWarn').dialog('open');
                break;
            case CMD_MASTER.SETCMD_LASERNET:
                {
                    c_master.MSG_SETCMD_LASERNET.stop();
                    let l_subscript = 26;
                    let l_NameSize = recvData[l_subscript];
                    let l_LidarName = asciiToStr(recvData, l_subscript);
                    l_subscript = l_subscript + l_NameSize + 1;
                    if (l_LidarName != c_DeviceName) {
                        return;
                    }
                    if (recvData[l_subscript++] == 0)
                        alert("设置雷达网卡失败，请检查！");
                    break;
                }
            case CMD_MASTER.QUERYCMD_NETLIST:
                c_master.MSG_QUERYCMD_NETLIST.stop();
                fillNetList(recvData);
                break;
            case CMD_MASTER.QUERYCMD_PCAPLIST:
                c_master.MSG_QUERYCMD_PCAPLIST.stop();
                fillPcapList(recvData)
                break;
            case CMD_MASTER.QUERYCMD_LASERNET: {
                console.log("QUERYCMD_LASERNET");
                c_master.MSG_QUERYCMD_LASERNET.stop();
                let l_subscript = 26;
                let l_NameSize = recvData[l_subscript]
                let l_LidarName = asciiToStr(recvData, l_subscript)
                if (l_LidarName != c_DeviceName) {
                    return;
                }
                l_subscript = l_subscript + l_NameSize + 1;
                fillLidarNetname(recvData, l_subscript);
                break;
            }
            case CMD_MASTER.QUERYCMD_LASERPCAPNAME:
                console.log("QUERYCMD_LASERPCAPNAME");
                c_master.MSG_QUERYCMD_LASERPCAPNAME.stop();
                let l_subscript = 26;
                let l_NameSize = recvData[l_subscript]
                let l_LidarName = asciiToStr(recvData, l_subscript)
                if (l_LidarName != c_DeviceName) {
                    return;
                }
                l_subscript = l_subscript + l_NameSize + 1;
                fillLidarPcap(recvData, l_subscript);
                break;
            case CMD_MASTER.SETCMD_SLAMCONTROL:
                c_master.MSG_SETCMD_SLAMCONTROL.stop();
                if (recvData[27] != 2) {
                    $('#waitWarnLidar').dialog('close');
                }
                break;
        }
    }
}

function selectDeviceProtocol(recvData) {
    console.log("devID:", recvData[23]);
    console.log("c_DeviceName:", c_DeviceName);
    let l_subscript = 26;
    let l_NameSize = recvData[l_subscript];
    let l_LidarName = asciiToStr(recvData, l_subscript);
    // 主界面运行程序
    if (c_DeviceName == "index") {
        console.log("l_LidarName:", l_LidarName);
        l_subscript = l_subscript + l_NameSize + 1;
        switch (recvData[23]) {
            case CMD_DEVICE.SETCMD_MULLASERCALIBRATIONINI:
                c_device.MSG_SETCMD_MULLASERCALIBRATIONINI.stop();
                if (recvData[l_subscript++] == 0) {
                    if (l_LidarName==c_ListCalibLidarName[0]) {
                        alert("雷达初始位置设定失败，切换基准雷达需保存参数后重启SLAM！");
                    }
                    else
                    {
                        alert("雷达初始位置设定失败，请先设置基准雷达位置！");
                    }
                    
                }
                else {
                    $('#SetPoseSuc').dialog('open');
                }
                break;
            case CMD_DEVICE.SETCMD_LASERENABLE:
                c_device.MSG_SETCMD_LASERENABLE.stop();
                if (recvData[l_subscript++] == 0) {
                    alert("雷达使能设定失败");
                }
                break;
            case CMD_DEVICE.SETCMD_MULLASERCALIBRATION:
                c_device.MSG_SETCMD_MULLASERCALIBRATION.stop();
                if (recvData[l_subscript++] == 0) {
                    // 关闭状态回复
                    if (recvData[l_subscript] == 0)
                        alert("雷达标定关闭失败");
                    if (recvData[l_subscript] == 1)
                    {
                        $("#calibrationStateIndex1").textbox("setValue", "标定状态：未启动");
                        
                        $("#LidarX1").numberbox("clear");
                        $("#LidarY1").numberbox("clear");
                        $("#LidarZ1").numberbox("clear");
                        $("#LidarAngleR1").numberbox("clear");
                        $("#LidarAngleP1").numberbox("clear");
                        $("#LidarAngleY1").numberbox("clear");

                        setSetEnable("#PoseSet0",true);
                        setBtnEnable("#EnLider0",true);
                        setSetEnable("#PoseSet1",true);
                        setBtnEnable("#EnLider1",true);
                        setSetEnable("#applyCalibBtn1",true);
                    }
                }
                else {
                    // 启动状态回复
                    if (recvData[l_subscript] == 0) {
                        alert("雷达标定启动失败，启动标定前确保当前工作模式为定位模式且正常启动，同时正确配置基准雷达");
                    }
                    else
                        getCalibStateIndex(true, l_LidarName);
                }
                break;
            case CMD_DEVICE.QUERYCMD_MULLASERCALIBRATION:
                if (recvData[l_subscript++] == 0)
                    alert("MSG_QUERYCMD_MULLASERCALIBRATION失败");
                else {
                    if (recvData[l_subscript])
                        fillCalibResultIndex(recvData, l_subscript);
                    else
                        getCalibStateIndex(false, l_LidarName);
                }
                break;
            case CMD_DEVICE.QUERYCMD_LASERENABLE:
                c_device.MSG_QUERYCMD_LASERENABLE.stop();
                if (recvData[l_subscript++] == 0) {
                    alert("雷达使能查询失败");
                }
                else {
                    for (let index = 0; index < c_ListCalibLidarName.length; index++)
                    {
                        if (l_LidarName == c_ListCalibLidarName[index]) {
                            switch (index) {
                                case 0:
                                    cViewLidarPose0 = recvData[l_subscript];
                                    break;
                                case 1:
                                    cViewLidarPose1 = recvData[l_subscript];
                                    break;
                                case 2:
                                    cViewLidarPose2 = recvData[l_subscript];
                                    break;
                                case 3:
                                    cViewLidarPose3 = recvData[l_subscript];
                                    break;
                                default:
                                    break;
                            }
                            setBtnValue("#EnLider" + index, recvData[l_subscript]);
                            setSetEnable("#PoseSet"+index,recvData[l_subscript]);
                            break;
                        }
                    }
                }
                break;
            case CMD_DEVICE.SETCMD_LASERINSTALLXYZ:
                c_device.MSG_SETCMD_LASERINSTALLXYZ.stop();
                if (recvData[l_subscript++] == 0)
                    alert("MSG_SETCMD_LASERINSTALLXYZ失败");
                break;
            case CMD_DEVICE.SETCMD_LASERINSTALLANGLE:
                c_device.MSG_SETCMD_LASERINSTALLANGLE.stop();
                if (recvData[l_subscript++] == 0)
                    alert("MSG_SETCMD_LASERINSTALLANGLE失败");
                break;
            default:
                break;
        }
        return;
    }
    if (l_LidarName != c_DeviceName) {
        return;
    }
    l_subscript = l_subscript + l_NameSize + 1;


    switch (recvData[23]) {
        case CMD_DEVICE.SETCMD_LASERINSTALLXYZ:
            c_device.MSG_SETCMD_LASERINSTALLXYZ.stop();
            if (recvData[l_subscript++] == 0)
                alert("MSG_SETCMD_LASERINSTALLXYZ失败");
            break;
        case CMD_DEVICE.SETCMD_LASERINSTALLANGLE:
            c_device.MSG_SETCMD_LASERINSTALLANGLE.stop();
            if (recvData[l_subscript++] == 0)
                alert("MSG_SETCMD_LASERINSTALLANGLE失败");
            break;
        case CMD_DEVICE.SETCMD_LASERDISBLIND:
            c_device.MSG_SETCMD_LASERDISBLIND.stop();
            if (recvData[l_subscript++] == 0)
                alert("MSG_SETCMD_LASERDISBLIND失败");
            break;
        case CMD_DEVICE.SETCMD_LASERANGBLIND:
            c_device.MSG_SETCMD_LASERANGBLIND.stop();
            if (recvData[l_subscript++] == 0)
                alert("MSG_SETCMD_LASERANGBLIND失败");
            break;
        case CMD_DEVICE.SETCMD_USEFLOOR:
            c_device.MSG_SETCMD_USEFLOOR.stop();
            if (recvData[l_subscript++] == 0)
                alert("MSG_SETCMD_USEFLOOR失败");
            break;
        case CMD_DEVICE.SETCMD_LASERHIGHT:
            c_device.MSG_SETCMD_LASERHIGHT.stop();
            if (recvData[l_subscript++] == 0)
                alert("MSG_SETCMD_LASERHIGHT失败");
            break;
        case CMD_DEVICE.SETCMD_MARKMODE:
            c_device.MSG_SETCMD_MARKMODE.stop();
            if (recvData[l_subscript++] == 0)
                alert("MSG_SETCMD_MARKMODE失败");
            break;
        case CMD_DEVICE.SETCMD_HORIZONTALCALIBRATION:
            c_device.MSG_SETCMD_HORIZONTALCALIBRATION.stop();
            if (recvData[l_subscript++] == 0) {
                // 关闭状态回复
                if (recvData[l_subscript] == 0)
                    alert("雷达水平校准关闭失败");
            }
            else {
                // 启动状态回复
                if (recvData[l_subscript] == 0) {
                    alert("雷达水平校准启动失败，启动校准前确保当前工作模式为空闲模式");
                }
                else
                    getHorizontalState(true);
            }
            break;
        case CMD_DEVICE.SETCMD_MULLASERCALIBRATION:
            c_device.MSG_SETCMD_MULLASERCALIBRATION.stop();
            if (recvData[l_subscript++] == 0) {
                // 关闭状态回复
                if (recvData[l_subscript] == 0)
                    alert("雷达标定关闭失败");
            }
            else {
                // 启动状态回复
                if (recvData[l_subscript] == 0) {
                    alert("雷达标定启动失败，启动标定前确保当前工作模式为定位模式且正常启动，同时正确配置基准雷达");
                }
                else
                    getCalibState(true);
            }
            break;
        case CMD_DEVICE.SETCMD_HORIZONTALCALIBNUM:
            {
                c_device.MSG_SETCMD_HORIZONTALCALIBNUM.stop();
                break;
            }
        case CMD_DEVICE.SETCMD_LASERENABLE:
            c_device.MSG_SETCMD_LASERENABLE.stop();
            if (recvData[l_subscript++] == 0) {
                alert("雷达使能设定失败");
            }
            break;
        /*******************************************/
        case CMD_DEVICE.QUERYCMD_LASERINSTALLXYZ:
            c_device.MSG_QUERYCMD_LASERINSTALLXYZ.stop();
            if (recvData[l_subscript++] == 0) {
                alert("MSG_QUERYCMD_LASERINSTALLXYZ失败");
                return;
            }
            fillLidarXYZ(recvData, l_subscript);
            break;
        case CMD_DEVICE.QUERYCMD_LASERINSTALLANGLE:
            c_device.MSG_QUERYCMD_LASERINSTALLANGLE.stop();
            if (recvData[l_subscript++] == 0) {
                alert("QUERYCMD_LASERINSTALLANGLE失败");
                return;
            }
            fillLidarRPY(recvData, l_subscript);
            break;
        case CMD_DEVICE.QUERYCMD_LASERDISBLIND:
            c_device.MSG_QUERYCMD_LASERDISBLIND.stop();
            if (recvData[l_subscript++] == 0) {
                alert("MSG_QUERYCMD_LASERDISBLIND失败");
                return;
            }
            fillLidarDisBlind(recvData, l_subscript);
            break;
        case CMD_DEVICE.QUERYCMD_LASERANGBLIND:
            c_device.MSG_QUERYCMD_LASERANGBLIND.stop();
            if (recvData[l_subscript++] == 0) {
                alert("MSG_QUERYCMD_LASERANGBLIND失败");
                return;
            }
            fillLidarAngBlind(recvData, l_subscript);
            break;
        case CMD_DEVICE.QUERYCMD_USEFLOOR:
            c_device.MSG_QUERYCMD_USEFLOOR.stop();
            if (recvData[l_subscript++] == 0) {
                alert("MSG_QUERYCMD_USEFLOOR失败");
                return;
            }
            fillLidarUseFloor(recvData, l_subscript);
            break;
        case CMD_DEVICE.QUERYCMD_LASERHIGHT:
            c_device.MSG_QUERYCMD_LASERHIGHT.stop();
            if (recvData[l_subscript++] == 0) {
                alert("MSG_QUERYCMD_LASERHIGHT失败");
                return;
            }
            fillLidarHeight(recvData, l_subscript);
            break;
        case CMD_DEVICE.QUERYCMD_MARKMODE:
            c_device.MSG_QUERYCMD_MARKMODE.stop();
            if (recvData[l_subscript++] == 0) {
                alert("MSG_QUERYCMD_MARKMODE失败");
                return;
            }
            fillLidarMark(recvData, l_subscript);
            break;
        case CMD_DEVICE.QUERYCMD_HORIZONTALCALIBRATION:
            if (recvData[l_subscript++] == 0)
                alert("MSG_HORIZONTALCALIBRATION失败");
            else {
                // 状态位非0 则填充
                if (recvData[l_subscript])
                    fillHorizontalAlignResult(recvData, l_subscript);
                else
                    getHorizontalState(false);
            }
            break;
        case CMD_DEVICE.QUERYCMD_MULLASERCALIBRATION:
            if (recvData[l_subscript++] == 0)
                alert("MSG_QUERYCMD_MULLASERCALIBRATION失败");
            else {
                if (recvData[l_subscript])
                    fillCalibResult(recvData, l_subscript);
                else
                    getCalibState(false);
            }
            break;
        case CMD_DEVICE.QUERYCMD_LIDARSTATUS:
            c_device.MSG_QUERYCMD_LIDARSTATUS.stop();
            if (recvData[l_subscript++] == 0) {
                alert("MSG_QUERYCMD_LIDARSTATUS失败");
                return;
            }
            fillLidarStatus(recvData, l_subscript);
            break;
        case CMD_DEVICE.QUERYCMD_HORIZONTALCALIBNUM:
            c_device.MSG_QUERYCMD_HORIZONTALCALIBNUM.stop();
            if (recvData[l_subscript++] == 0) {
                alert("MSG_QUERYCMD_HORIZONTALCALIBNUM失败");
                return;
            }
            fillLidarHorizonCalibNum(recvData, l_subscript);
            break;
        case CMD_DEVICE.QUERYCMD_LASERENABLE:
            c_device.MSG_QUERYCMD_LASERENABLE.stop();
            if (recvData[l_subscript++] == 0) {
                alert("雷达使能查询失败");
            }
            else{
                fillEnbleLidar(recvData,l_subscript);
            }
            break;
        default:
            break;
    }
}

function selectParamProtocol(recvData) {
    // console.log("paramID:", recvData[23]);
    let l_subscript = 26;
    let l_NameSize = 0;
    let l_LidarName = "";
    switch (recvData[23]) {
        case CMD_PARAM.SETCMD_CLOCKSOURCE:
            c_param.MSG_SETCMD_CLOCKSOURCE.stop();
            break;
        case CMD_PARAM.SETCMD_LASERNETPARAM:
            c_param.MSG_SETCMD_LASERNETPARAM.stop();
            l_NameSize = recvData[l_subscript]
            l_LidarName = asciiToStr(recvData, l_subscript)
            if (l_LidarName != c_DeviceName)
                return;
            l_subscript = l_subscript + l_NameSize + 1;
            if (recvData[l_subscript++] == 0) {
                readLidarNetParamfun();
                alert("设置雷达 IP/Port无效,已忽略此设置！请切换至在线模式修改此参数！");
            }
            break;
        case CMD_PARAM.SETCMD_LASERBASEPARAM:
            c_param.MSG_SETCMD_LASERBASEPARAM.stop();
            break;
        case CMD_PARAM.SETCMD_AGVNETPARAM:
            c_param.MSG_SETCMD_AGVNETPARAM.stop();
            if (recvData[l_subscript] == 0) {
                // 重新读取还原显示值
                readAGVNetParam();
                alert("设置AGV IP/Port无效,已忽略此设置！请切换至在线模式修改此参数！");
            }
            break;
        case CMD_PARAM.SETCMD_LOADDEFAULTPARAM:
            c_param.MSG_SETCMD_LOADDEFAULTPARAM.stop();
            if (recvData[l_subscript] == 1)
                alert("恢复出厂成功，请重启工控机");
            else
                alert("恢复出厂异常，文件丢失或损坏");
            break
        case CMD_PARAM.SETCMD_LASERISBASE:
            c_param.MSG_SETCMD_LASERISBASE.stop();
            l_NameSize = recvData[l_subscript]
            l_LidarName = asciiToStr(recvData, l_subscript)
            if (l_LidarName != c_DeviceName) {
                return;
            }
            l_subscript = l_subscript + l_NameSize + 1;
            if (recvData[l_subscript++] == 0) {
                alert("基准雷达配置失败，请先关闭多雷达标定");
                return;
            }
            break;
        /*******************************************/
        case CMD_PARAM.QUERYCMD_CLOCKSOURCE:
            c_param.MSG_QUERYCMD_CLOCKSOURCE.stop();
            // if(recvData[l_subscript] == 0 || recvData[l_subscript] == 1)
            if (recvData[l_subscript] == 0)
                $("#timeSource").combobox("setValue", recvData[l_subscript]);
            break;
        case CMD_PARAM.QUERYCMD_LASERNETPARAM:
            c_param.MSG_QUERYCMD_LASERNETPARAM.stop();

            l_NameSize = recvData[l_subscript]
            l_LidarName = asciiToStr(recvData, l_subscript)
            if (l_LidarName != c_DeviceName) {
                return;
            }
            l_subscript = l_subscript + l_NameSize + 1;
            if (recvData[l_subscript++] == 0) {
                // alert("查询失败");
                return;
            }
            fillLidarNetParam(recvData, l_subscript);
            break;
        case CMD_PARAM.QUERYCMD_LASERBASEPARAM:
            c_param.MSG_QUERYCMD_LASERBASEPARAM.stop();

            l_NameSize = recvData[l_subscript]
            l_LidarName = asciiToStr(recvData, l_subscript)
            if (l_LidarName != c_DeviceName) {
                return;
            }
            l_subscript = l_subscript + l_NameSize + 1;
            if (recvData[l_subscript++] == 0) {
                // alert("查询失败");
                return;
            }

            fillLidarBaseParam(recvData, l_subscript);
            break;
        case CMD_PARAM.QUERYCMD_AGVNETPARAM:
            c_param.MSG_QUERYCMD_AGVNETPARAM.stop();
            fillAGVIPinfo(recvData)
            break;
        case CMD_PARAM.QUERYCMD_LIDARNUM:
            c_param.MSG_QUERYCMD_LIDARNUM.stop();
            if (c_DeviceName != "index") {
                return;
            }
            fillAllLidarName(recvData);
            break;
        case CMD_PARAM.QUERYCMD_LASERISBASE:
            c_param.MSG_QUERYCMD_LASERISBASE.stop();

            l_NameSize = recvData[l_subscript]
            l_LidarName = asciiToStr(recvData, l_subscript)
            if (l_LidarName != c_DeviceName) {
                return;
            }
            l_subscript = l_subscript + l_NameSize + 1;
            if (recvData[l_subscript++] == 0) {
                alert("查询失败");
                return;
            }
            fillLidarBase(recvData, l_subscript);
            break;
        default:
            break;
    }
}

function selectPrivateProtocol(recvData) {
    let l_subscript = 26;
    switch (recvData[23]) {
        case CMD_PRVT.QUERYCMD_WEBPOSE:
            c_prvt.MSG_QUERYCMD_WEBPOSE.stop();
            fillWebPose(recvData);
            break;
        default:
            break;
    }
}

$.extend($.fn.validatebox.defaults.rules, {
    ip:{
        // ip地址校验
        validator: function(value){
            var reg = /^(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}$/
            if(value.length>0){
                return reg.test(value);
            }
        },
        message:'IP地址格式不正确'
    }
});
