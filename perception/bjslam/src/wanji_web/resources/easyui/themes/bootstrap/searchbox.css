.searchbox-button {
  width: 18px;
  height: 20px;
  overflow: hidden;
  display: inline-block;
  vertical-align: top;
  cursor: pointer;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.searchbox-button-hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.searchbox .l-btn-plain {
  border: 0;
  padding: 0;
  vertical-align: top;
  opacity: 0.6;
  filter: alpha(opacity=60);
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.searchbox .l-btn-plain:hover {
  border: 0;
  padding: 0;
  opacity: 1.0;
  filter: alpha(opacity=100);
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.searchbox a.m-btn-plain-active {
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.searchbox .m-btn-active {
  border-width: 0 1px 0 0;
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.searchbox .textbox-button-right {
  border-width: 0 0 0 1px;
}
.searchbox .textbox-button-left {
  border-width: 0 1px 0 0;
}
.searchbox-button {
  background: url('images/searchbox_button.png') no-repeat center center;
}
.searchbox .l-btn-plain {
  background: #F2F2F2;
}
.searchbox .l-btn-plain-disabled,
.searchbox .l-btn-plain-disabled:hover {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
