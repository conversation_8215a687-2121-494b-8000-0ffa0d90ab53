/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2025-07-08 13:26:33
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-07-09 10:00:57
 * @FilePath: /src/perception/commonlibrary/src/msgManager/msgManager.h
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
#ifndef __MSGMANAGER_H__
#define __MSGMANAGER_H__ 
#include <iostream>
#include <string>
#include <vector>

using namespace std;

struct s_Cloudpant {
    string  id;                    // 参与者ID，唯一值；
    int32_t   vehicletype;         // 分车辆类型:0-未知；1-小客车；2-大货车；3-大巴车；4-行人；5-自行车；6-摩托车；7-中巴车；8-危化车；9-遗撒物；10-小货车;11-中货车
    float length;                  // 长度 单位m
    float width;                   // 宽度 单位m
    float height;                  // 高度 单位m
    double longitude;              // 分辨率1e-7°，东经为正，西经为负
    double latitude;               // 分辨率1e-7°，北纬为正，南纬为负
    int   locationconfidence;      // 位置置信度，单位：%
    float speed;                   // 速度，单位：m/s
    float courseangle;             // 航向角，单位：rad，保留1位小数，车头与正北夹角
    int32_t   sportconfidence;     // 运动参数置信度，单位：%
};


struct s_Cloudpants{
    int64_t timestamp;              // 当前时间戳 ms 级
    int32_t frameid;                // 仿真结果帧序号
    int32_t count;                  // 参与者数量
    std::vector<s_Cloudpant> pants; // 参与者集合
};


struct s_PerformanceMonitor {
    float time;              // 程序运行耗时
};


struct s_Oburoadpoint {
    double lon;               // 经度
    double lat;               // 纬度
    double speed;             // 速度 m/s
    double accel;             // 车辆加速度
    double heading;           // 航向角
    int32_t availability;     // 有效性
};

struct s_Oburoadlist{
    uint8_t planpoints;    // 预测轨迹点数
    std::vector<s_Oburoadpoint> oburoadpoint;
};


struct s_Obupant { 
    int32_t   ptc_type;                     // 0:未知 1：行人 2：小客车 3：巴士 4：货车 5：非机动车 100:僵尸车
    int32_t   ptc_id;                       // 交通参与者id
    int32_t   source;                       // 0:未知 1：自车 2：v2x 3:视频传感器 4：毫米波雷达 5：地磁线圈传感器  6:激光雷达 7：2类或以上感知数据的融合结果
    string  source_id;                      // 对象来源ID
    int32_t   sec_mark;                     // 时间戳
    double  pos_lon;                        // 位置经度
    double  pos_lat;                        // 位置纬度
    double  pos_latitude;                   // m
    float speed;                            // m/s
    float heading;                          // 正北是0°，范围0-360
    float accel;                            // m/s
    float accel_angle;                      // 加速度方向
    float acc4way_lon;                      // 四轴纵向加速度
    float acc4way_lat;                      // 四轴横向加速度
    float acc4way_vert;                     // 四轴垂直加速度
    float acc4way_yaw;                      // 四轴角速度
    float width;                            // 单位m
    float length;                           // 单位m
    float height;                           // 单位m
    float lon;                              // 经度
    float lat;                              // 纬度
    int8_t planlist_num;                    // 预测轨迹点list
    std::vector<s_Oburoadlist> roadlist;    // 预测轨迹
};


struct s_Obupants{
    std::vector<s_Obupant> pants;
    uint8_t isvalid;
    int64_t timestamp;
    int32_t msg_cnt;
};


struct s_Sensorgps{
    double lon;          // 纬度
    double lat;          // 经度
    double alt;          // 高度
    uint8_t roadtype;    // 道路属性
    uint8_t lanetype;    // 当前车道|总车道(4|4)
    double heading;      // 航向角 degree
    double pitch;        // 俯仰角
    double roll;         // 横滚角
    double pitchrate;    // deg/s
    double rollrate;     // deg/s
    double yawrate;      // deg/s
    double accx;         // m/s^2
    double accy;         // m/s^2
    double accz;         // m/s^2
    double mile;         // 里程 m
    double velocity;     // 速度
    uint8_t status;      // 导航状态
    uint8_t rawstatus;
    uint8_t satenum;     // 卫星个数
    uint64_t gpstime;    // gps时间
    uint8_t isvalid;     // 有效位
    uint64_t timestamp;  // 时间戳

    double speed_n;      // X轴速度
    double speed_e;      // Y轴速度
    double speed_d;      // Z轴速度
};

struct s_Point3d {
    float x;
    float y;
    float z;
};

struct s_Objecthistory {
    uint64_t timestamp;             // 时间戳-毫秒 发布跟踪预测轨迹信息
    s_Point3d trajectorypoint;      // 轨迹点坐标XYZ
    double lon;                     // 纬度
    double lat;                     // 经度
    double alt;                     // 高度
    float roll;                     // N
    float pitch;                    // E
    float heading;                  // D
    float relavx;		
    float relavy;
    float absvx;                    // m/s
    float absvy;                    // m/s
    float s;                        // m
    float l;                        // m
    float speeds;                   // m/s
    float speedl;                   // m/s
};

struct s_Objectprediction {
    float timestep;                 // 时间步长-0.1s
    float x;                        // x-carBackRFU-m
    float y;                        // y-carBackRFU-m
    float z;                        // z-carBackRFU-m
    double longtitude;              // 纬度    // 20220914
    double latitude;                // 经度
    double altitude;                // 高度
    float rollrad;                  // 横滚角 rad   
    float pitchrad;                 // 俯仰角 rad        
    float azimuth;                  // 航向角 rad
    float relavx;                   // 相对速度-m/s
    float relavy;                   // 相对速度-m/s
    float absvx;                    // 绝对速度-m/s
    float absvy;                    // 绝对速度-m/s
    float heading;                  // 自车RFU坐标Y轴正方向顺时针0-2pi,rad
    float s;                        // m
    float l;                        // m
    float speeds;                   // m/s
    float speedl;                   // m/s

};

struct s_Sensorobject { 
    uint32_t  id;                                           // 跟踪的ID
    float x;                                                // 横坐标
    float y;                                                // 纵坐标
    float z;                                                // Z坐标
    double longtitude;                                      // 经度
    double latitude;                                        // 纬度
    double altitude;                                        // 高度
    float relspeedy;                                        // 纵轴相对速度
    float relspeedx;                                        // 横轴相对速度
    float rollrad;                                          // 横滚角 rad   
    float pitchrad;                                         // 俯仰角 rad        
    float azimuth;                                          // 航向角 rad
    double pitchrate;                                       // deg/s
    double rollrate;                                        // deg/s
    double yawrate;                                         // deg/s
    float width;                                            // 宽度
    float length;                                           // 长度
    float height;                                           // 高度
    uint8_t   classification;                               // 类别
    uint8_t   value;                                        // Cluster 版本用于速度来源-radar 
    float confidence;                                       // 检测置信度
    std::vector<s_Point3d> points;                          // 轮廓点数据 
    uint8_t  driving_intent;                                // 驾驶意图:0-初始,1-切入
    uint8_t  behavior_state;                                // FORWARD_STATE = 0, STOPPING_STATE = 1, BRANCH_LEFT_STATE = 2, BRANCH_RIGHT_STATE = 3, YIELDING_STATE = 4, ACCELERATING_STATE = 5, SLOWDOWN_STATE = 6
    uint8_t  radarindex;                                    // 相对速度来源
    uint8_t  radarobjectid;                                 // radar跟踪目标ID
    float s;                                                // frenet坐标系的s
    float l;                                                // frenet坐标系的l
    float speeds;                                           // frenet坐标系的s方向速度
    float speedl;                                           // frenet坐标系的l方向速度
    uint8_t   object_decision;                              // 障碍物相关决策
    std::vector<s_Objecthistory> object_history;            // 历史轨迹信息
    std::vector<s_Objectprediction> object_prediction;      // 预测信息

};

struct s_Sensorobjects {
    std::vector<s_Sensorobject> obs;
    uint8_t isvalid;
    uint64_t timestamp;
    uint64_t gpstime;
};




#endif __MSGMANAGER_H__ 
