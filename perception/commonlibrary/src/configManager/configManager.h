/*
 * @Description  : 
 * @Version      : V1.0.0
 * <AUTHOR> hanshuangquan <EMAIL>
 * @Date         : 2024-01-26 15:52:45
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2024-10-18 14:23:23
 * @FilePath     : configManager.h
 * Copyright 2024 Marvin, All Rights Reserved. 
 * 2024-01-26 15:52:45
 */
#ifndef __CONFIGREADER_H__
#define __CONFIGREADER_H__

#include <iostream>
#include <string>
#include <vector>
#include <spdlog/spdlog.h>
#include <yaml-cpp/yaml.h>

using namespace std;

namespace ConfigManager{
    static const std::map<int, std::string> carNumberMap{{1,"hongqi1"}, {2,"hongqi2"}};

class ConfigManager
{
public:
    ConfigManager(const std::string yamlPath);
    ~ConfigManager();

    void readParam();
    void printParams(std::shared_ptr<spdlog::logger>& pLogger);

    bool m_isUseHDMap;
	std::string m_hdMapName;
	double m_hdMapLongitude;
	double m_hdMapLatitude;
	bool m_isUseRosBag;
    int m_cityUTMCode;
	bool m_isSimulateMode;
	int m_bdebug;
	float m_radarAsObjectDistance;
	int m_carNumber;
	string m_carName;

    float m_front2BackDistance;
	bool m_isSaveObjectInfoCSV;
	bool m_isEvaluateTracking;
	bool m_isUseRadarObjects;

    int m_debugID;
    std::string m_logFilePath;
    std::string m_logFileName;
    int m_logOutputType;
    int m_logFileMaxSize;
    int m_logFileMaxNum;
    std::vector<std::vector<double>> m_stationRange;

    bool m_isSaveTimeUseFile;
    string m_saveTimeUseFilePath;

private:
    YAML::Node m_configManager;


};

}


#endif /* __CONFIGREADER_H__ */