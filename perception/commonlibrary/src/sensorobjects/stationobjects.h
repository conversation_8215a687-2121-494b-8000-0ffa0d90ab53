/*
 * @Description  : 
 * @Version      : V1.0.0
 * <AUTHOR> hanshuangquan <EMAIL>
 * @Date         : 2024-01-24 15:42:16
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-07-11 17:25:24
 * @FilePath: /src/perception/commonlibrary/src/sensorobjects/stationobjects.h
 * Copyright 2024 Marvin, All Rights Reserved. 
 * 2024-01-24 15:42:16
 */
#ifndef __STATIONOBJECTS_H__
#define __STATIONOBJECTS_H__
#include "objects.h"
#include <cfloat>

#ifdef ROS1_FOUND
#include "common_msgs/obupant.h"
#include "common_msgs/obupants.h"
#include "common_msgs/sensorobject.h"
#include "common_msgs/sensorobjects.h"
#endif
#ifdef ROS2_FOUND
#include "common_msgs_humble/msg/obupant.hpp"
#include "common_msgs_humble/msg/obupants.hpp"
#include "common_msgs_humble/msg/sensorobject.hpp"
#include "common_msgs_humble/msg/sensorobjects.hpp"
#endif

#include "../common.h"
#include "../coordinateTransformation/wgs84_utm.h"
#include "../coordinateTransformation/sensorAxisTransformation/sensorAxisTransformation.h"
namespace SENSOROBJECTS{
template <typename T>
class StationObjects: public SensorObjects<T>
{
private:
    wgs84_utm m_wgs84Utm;
	Common m_common;
	SensorAxisTransformation m_sensorAxisTransformer;
	int m_sensorTimeStampGap = 100; //ms
	#ifdef ROS1_FOUND
		common_msgs::sensorobjects m_zombieObjects;
	#endif
	#ifdef ROS2_FOUND
		common_msgs_humble::msg::Sensorobjects m_zombieObjects;
	#endif
	std::map<int,int> m_obstacleEventMatchingPairs;
public:
	#ifdef ROS1_FOUND
		StationObjects(ros::NodeHandle& nodeName, const std::string& topicName, const int& sensorType);
		~StationObjects();

		void timeSynchro(std::deque<T>& msgDeque,const long& curObjectFrameStamp, const int synchroFlag);
		void transOBUObject2CarBackRFU(common_msgs::sensorobjects& obuObjects, const common_msgs::sensorgps& sensorgps);
		int transOBUObjectType2CameraDetectionType(const int& obuObjectType);
		void mergeZombieObject(common_msgs::sensorobjects& obuObjects);
	#endif
	#ifdef ROS2_FOUND
		StationObjects(rclcpp::Node& nodeName, const std::string& topicName, const int& sensorType);
		~StationObjects();

		void timeSynchro(std::deque<T>& msgDeque,const long& curObjectFrameStamp, const int synchroFlag);
		void transOBUObject2CarBackRFU(common_msgs_humble::msg::Sensorobjects& obuObjects, const common_msgs_humble::msg::Sensorgps& sensorgps);
		int transOBUObjectType2CameraDetectionType(const int& obuObjectType);
		void mergeZombieObject(common_msgs_humble::msg::Sensorobjects& obuObjects);
	#endif
};

#ifdef ROS1_FOUND
template <typename T>
StationObjects<T>::StationObjects(ros::NodeHandle& nodeName, const std::string& topicName, const int& sensorType)
    :SensorObjects<T>(nodeName, topicName, sensorType)
{

}


template <typename T>
void StationObjects<T>::transOBUObject2CarBackRFU(common_msgs::sensorobjects& obuObjects, const common_msgs::sensorgps& sensorgps){
	// std::lock_guard<std::mutex> lock(this->m_dataMutex);
	if(this->m_curSensorData.pants.empty()){
		obuObjects.obs.clear();
		obuObjects = common_msgs::sensorobjects();
		cout<<"\tobu object size = "<<this->m_curSensorData.msg_cnt<<endl;
		cout<<std::setprecision(16)<<"\t当前obu msg时间 = "<<this->m_curSensorData.timestamp / 1000.0 <<endl;
		printf("this->m_curSensorData.pants.empty \n");
		return;
	}
	if(this->m_curSensorData.msg_cnt > 250 ){
		obuObjects.obs.clear();
		obuObjects = common_msgs::sensorobjects();
		cout<<"\t!!!!!!!!!obu 目标数量异常: object size = "<<this->m_curSensorData.msg_cnt<<endl;
		cout<<std::setprecision(16)<<"\t当前obu msg时间 = "<<this->m_curSensorData.timestamp / 1000.0 <<endl;
		return;
	}
	
	tagUTMCorr selfCarUTM;
	m_wgs84Utm.LatLonToUTMXY(sensorgps.lat / 180.0 * M_PI, sensorgps.lon / 180.0 * M_PI, selfCarUTM);
	Eigen::Vector3d selfCarEulerXYZDegree{sensorgps.roll, sensorgps.pitch, sensorgps.heading};
	cout<<"sensorgps.tiemstamp = " << sensorgps.timestamp / 1000.0 <<", sensorgps.heading = " << sensorgps.heading << endl;
	Eigen::Vector3d selfCarUTMAsTranslation{selfCarUTM.x, selfCarUTM.y, 0};

	cout<<"this->m_curSensorData object size = " << this->m_curSensorData.pants.size() << endl;

	std::vector<double> selfCarSpeed = m_sensorAxisTransformer.ENU2CarBackRFUAxis(Eigen::Vector3d{sensorgps.pitch, sensorgps.roll, sensorgps.heading},
																	Eigen::Vector3d{sensorgps.speedE, sensorgps.speedN, -sensorgps.speedD});

	obuObjects.obs.clear();
	obuObjects.timestamp = this->m_curSensorData.timestamp;
	obuObjects.gpstime = this->m_curSensorData.timestamp;
	obuObjects.isvalid = 1;
	for (auto& obupant:this->m_curSensorData.pants) {
		if(obupant.pos_lon <= 0 || obupant.pos_lat <= 0){
			cout<<FYEL("WARN: obu lon lat abnormal:") << ", obu id = " << obupant.ptc_id
			    <<", width = "<<obupant.width<< ", length = " <<obupant.length << endl;
			continue;
		}
		common_msgs::sensorobject obuObject;
		float obuObjectAngleDegree = obupant.heading;
		float obuObjectAngleDegreeInLidarRFU_Clockwise = (obuObjectAngleDegree - sensorgps.heading);
		obuObjectAngleDegreeInLidarRFU_Clockwise = obuObjectAngleDegreeInLidarRFU_Clockwise < 0? obuObjectAngleDegreeInLidarRFU_Clockwise + 360:obuObjectAngleDegreeInLidarRFU_Clockwise;
		obuObjectAngleDegreeInLidarRFU_Clockwise = obuObjectAngleDegreeInLidarRFU_Clockwise >= 360 ? obuObjectAngleDegreeInLidarRFU_Clockwise - 360:obuObjectAngleDegreeInLidarRFU_Clockwise;


		Eigen::Vector3d objectPositionInCarBackFRU;
		double carCenterRFU_x, carCenterRFU_y;
		m_wgs84Utm.LatLonToLocalXY(sensorgps.lon, sensorgps.lat, sensorgps.heading, obupant.pos_lon, obupant.pos_lat, carCenterRFU_x, carCenterRFU_y);
		double temp_angle = obuObjectAngleDegreeInLidarRFU_Clockwise * M_PI / 180.0;
		double gx = 0, gy = 0;//0.3,m_carCenter2CarBackDistance;
		double xgap = gx * cos(temp_angle) - gy * sin(temp_angle);
		double ygap = gx * sin(temp_angle) + gy * cos(temp_angle);

		objectPositionInCarBackFRU[0] = carCenterRFU_x + xgap;
		objectPositionInCarBackFRU[1] = carCenterRFU_y - ygap;//m_carCenter2CarBackDistance;//目标中心点RFU转到车后轴RFU
		objectPositionInCarBackFRU[2] =0;
		//cout<<FGRN("diff1: obuObjectAngleDegreeInLidarRFU_Clockwise = ") << obuObjectAngleDegreeInLidarRFU_Clockwise <<", xgap = " << xgap <<", ygap = " << ygap<< endl;

		//ID移位，避免与车端跟踪ID冲突
		auto tempID = obupant.ptc_id;
		//obupant.ptc_id = static_cast<uint32_t>(obupant.ptc_id) << 16;//超过65535会溢出

		obuObject.id = static_cast<uint32_t>(obupant.ptc_id);
		// cout<<FYEL("WARN: change obu id: ")<<", raw id = "<< tempID << ", changed id = " << obuObject.id << endl;
		obuObject.x = objectPositionInCarBackFRU[0];
		obuObject.y = objectPositionInCarBackFRU[1];
		obuObject.z = 0;//objectPositionInCarBackFRU[2];

		obuObject.longtitude = obupant.pos_lon ;//objectPositionInCarBackFRU[2];
		obuObject.latitude = obupant.pos_lat;//objectPositionInCarBackFRU[2];
		obuObject.altitude = 0;//objectPositionInCarBackFRU[2];

		obuObject.relspeedx = obupant.speed * sin(obuObjectAngleDegreeInLidarRFU_Clockwise * M_PI / 180.0) - selfCarSpeed[0];
		obuObject.relspeedy = obupant.speed * cos(obuObjectAngleDegreeInLidarRFU_Clockwise * M_PI / 180.0) - selfCarSpeed[1];
		obuObject.azimuth = obuObjectAngleDegreeInLidarRFU_Clockwise * M_PI / 180.0;
		
		obuObject.classification = transOBUObjectType2CameraDetectionType(obupant.ptc_type); //static_cast<uint8_t>()

		// 暂不考虑僵尸车
		if(obuObject.classification == COMMON::LidarDetectionClassification::Zombiecar){
			continue;
		}

		obuObject.width = obupant.width;
		obuObject.length = obupant.length; //长宽为实际长宽
		obuObject.height = obupant.height;
		// if(obuObject.classification == COMMON::LidarDetectionClassification::Car
		// 	|| obuObject.classification == COMMON::LidarDetectionClassification::Zombiecar){
		// 	obuObject.length = 4;
		// 	obuObject.width = 1.85;
		// }
		// else if(obuObject.classification == COMMON::LidarDetectionClassification::Pedestrian){
		// 	obuObject.length = 0.6;
		// 	obuObject.width = 0.6;
		// }

		if(obuObject.x == 0 || obuObject.y == 0 || obuObject.width == 0 || obuObject.length == 0){
			cout<<FYEL("WARN: change obu info") << ", raw obu id = " << tempID << ", classification = " << static_cast<int>(obuObject.classification) 
				<<", x = "<<obuObject.x<< ", y = " <<obuObject.y
			    <<", width = "<<obuObject.width<< ", length = " <<obuObject.length << endl;
		}

		obuObject.value = static_cast<uint8_t>(COMMON::SensorType::OBU);

		//生成8角点
		float headingAnticlockwise = 2 * M_PI - obuObject.azimuth; //
		vector<double> boxInfo = {obuObject.x, obuObject.y, obuObject.z,
		                          obuObject.length, obuObject.width, obupant.height, headingAnticlockwise};
		vector<vector<double>> eightCornerPoints = m_common.boxes_to_corners_3d(boxInfo);
		for(const auto& singleCornerPointVector : eightCornerPoints){
			common_msgs::point3d singleCornerPoint;
			singleCornerPoint.x = singleCornerPointVector[0];
			singleCornerPoint.y = singleCornerPointVector[1];
			singleCornerPoint.z = singleCornerPointVector[2];

			obuObject.points.emplace_back(singleCornerPoint);
		}
		
		// 僵尸车事件与僵尸车障碍物分开保存
		if(obuObject.classification == COMMON::LidarDetectionClassification::Zombiecar){
			auto it  = std::find_if(m_zombieObjects.obs.begin(),m_zombieObjects.obs.end(),[&](common_msgs::sensorobject& object){
				return object.id == obuObject.id;
			});
			// 不存在：保存，存在：删除容器中旧有，更新
			if(it == m_zombieObjects.obs.end()){
				cout<<"Zombiecar 不存在，存入容器： id = "<<obuObject.id  << ", classification = " << static_cast<int>(obuObject.classification) 
					<<", x = "<<obuObject.x<< ", y = " <<obuObject.y
					<<", width = "<<obuObject.width<< ", length = " <<obuObject.length 
					<<std::setprecision(13)<<", lon = "<<obuObject.longtitude<< ", lat = " <<obuObject.longtitude<< endl;
				m_zombieObjects.obs.emplace_back(obuObject);
			}
			else{
				int zombieIndex = std::distance(m_zombieObjects.obs.begin(),it);
				cout<<"Zombiecar 删除容器中旧有，旧有 id = "<<m_zombieObjects.obs[zombieIndex].id  << ", 更新 id = "<<obuObject.id  << ", classification = " << static_cast<int>(obuObject.classification) 
					<<", x = "<<obuObject.x<< ", y = " <<obuObject.y
					<<", width = "<<obuObject.width<< ", length = " <<obuObject.length
					<<std::setprecision(13)<<", lon = "<<obuObject.longtitude<< ", lat = " <<obuObject.longtitude << endl;
				m_zombieObjects.obs.erase(m_zombieObjects.obs.begin() + zombieIndex);
				m_zombieObjects.obs.emplace_back(obuObject);
			}
		}
		else{
			obuObjects.obs.emplace_back(obuObject);
		}
	}
	cout<<"obu raw object size = "<<this->m_curSensorData.msg_cnt<<", this->m_curSensorData object size = "<<this->m_curSensorData.pants.size()
		<<", valid obu object size = "<<obuObjects.obs.size() <<", obu zombie object size = "<<m_zombieObjects.obs.size()<<endl;
	
	mergeZombieObject(obuObjects);
	cout<<"debug:finished OBU object trans\n";
}

template <typename T>
void StationObjects<T>::mergeZombieObject(common_msgs::sensorobjects& obuObjects){
	for(auto& zombineObject : m_zombieObjects.obs){
		float minDistance = FLT_MAX;
		int minDistanceIndex;

		for (size_t i = 0; i < obuObjects.obs.size(); i++){
			common_msgs::sensorobject& obuObject = obuObjects.obs[i];
			if(obuObject.classification != COMMON::LidarDetectionClassification::Car)
				continue;
			float distanceX = zombineObject.x - obuObject.x;
			float distanceY = zombineObject.y - obuObject.y;
			float distance = sqrt(distanceX * distanceX + distanceY * distanceY);
			if(distance < minDistance){
				minDistance = distance;
				minDistanceIndex = i;
			}

			// 僵尸车事件数据丢失时，找到之前的匹配对，对匹配到僵尸车事件的障碍物车辆赋值僵尸车类别
			auto it = m_obstacleEventMatchingPairs.find(obuObject.id);
			if(it != m_obstacleEventMatchingPairs.end()){
				obuObject.classification = COMMON::LidarDetectionClassification::Zombiecar;
			}

			
		}
		// 从数据看小于0.1认为是与僵尸车事件匹配的车辆障碍物,僵尸车事件未匹配到车辆障碍物,则将僵尸车事件保存到车辆障碍物中
		if(minDistance < 0.5){
			cout<<"mergeZombieObject: 匹配成功,僵尸车事件ID = "<< zombineObject.id <<", 匹配车辆障碍物ID = "<< obuObjects.obs[minDistanceIndex].id <<endl;
			obuObjects.obs[minDistanceIndex].classification = COMMON::LidarDetectionClassification::Zombiecar;

			// 与僵尸车事假匹配的车辆不在匹配对中，保存僵尸车事件ID与匹配车辆ID对
			auto it = std::find_if(m_obstacleEventMatchingPairs.begin(), m_obstacleEventMatchingPairs.end(),[&](const std::pair<int, int>& pair) {
				return obuObjects.obs[std::get<1>(pair)].id == zombineObject.id;
			});
			if (it == m_obstacleEventMatchingPairs.end()) {
				m_obstacleEventMatchingPairs.insert(std::make_pair(obuObjects.obs[minDistanceIndex].id, zombineObject.id));
			}
		}
		else if(minDistance != FLT_MAX){
			cout<<"mergeZombieObject: 匹配不成功,僵尸车事件ID = "<< zombineObject.id <<", minDistance = "<< minDistance
				<<", 匹配车辆障碍物ID = "<< obuObjects.obs[minDistanceIndex].id  <<endl;
			obuObjects.obs.emplace_back(zombineObject);
		}
		else{
			cout<<"mergeZombieObject: 匹配不成功,僵尸车事件ID = "<< zombineObject.id <<", minDistance = "<< minDistance <<endl;
			obuObjects.obs.emplace_back(zombineObject);
			cout<<"debug： zombie事件匹配OBU目标失败"<<endl;
		}

		
	}
}


#endif
#ifdef ROS2_FOUND
template <typename T>
StationObjects<T>::StationObjects(rclcpp::Node& nodeName, const std::string& topicName, const int& sensorType)
    :SensorObjects<T>(nodeName, topicName, sensorType)
{

}


template <typename T>
void StationObjects<T>::transOBUObject2CarBackRFU(common_msgs_humble::msg::Sensorobjects& obuObjects, const common_msgs_humble::msg::Sensorgps& sensorgps){
	// std::lock_guard<std::mutex> lock(this->m_dataMutex);
	if(this->m_curSensorData.pants.empty()){
		obuObjects.obs.clear();
		obuObjects = common_msgs_humble::msg::Sensorobjects();
		cout<<"\tobu object size = "<<this->m_curSensorData.msg_cnt<<endl;
		cout<<std::setprecision(16)<<"\t当前obu msg时间 = "<<this->m_curSensorData.timestamp / 1000.0 <<endl;
		printf("this->m_curSensorData.pants.empty \n");
		return;
	}
	if(this->m_curSensorData.msg_cnt > 250 ){
		obuObjects.obs.clear();
		obuObjects = common_msgs_humble::msg::Sensorobjects();
		cout<<"\t!!!!!!!!!obu 目标数量异常: object size = "<<this->m_curSensorData.msg_cnt<<endl;
		cout<<std::setprecision(16)<<"\t当前obu msg时间 = "<<this->m_curSensorData.timestamp / 1000.0 <<endl;
		return;
	}
	
	tagUTMCorr selfCarUTM;
	m_wgs84Utm.LatLonToUTMXY(sensorgps.lat / 180.0 * M_PI, sensorgps.lon / 180.0 * M_PI, selfCarUTM);
	Eigen::Vector3d selfCarEulerXYZDegree{sensorgps.roll, sensorgps.pitch, sensorgps.heading};
	cout<<"sensorgps.tiemstamp = " << sensorgps.timestamp / 1000.0 <<", sensorgps.heading = " << sensorgps.heading << endl;
	Eigen::Vector3d selfCarUTMAsTranslation{selfCarUTM.x, selfCarUTM.y, 0};

	cout<<"this->m_curSensorData object size = " << this->m_curSensorData.pants.size() << endl;

	std::vector<double> selfCarSpeed = m_sensorAxisTransformer.ENU2CarBackRFUAxis(Eigen::Vector3d{sensorgps.pitch, sensorgps.roll, sensorgps.heading},
																	Eigen::Vector3d{sensorgps.speed_e, sensorgps.speed_n, -sensorgps.speed_d});

	obuObjects.obs.clear();
	obuObjects.timestamp = this->m_curSensorData.timestamp;
	obuObjects.gpstime = this->m_curSensorData.timestamp;
	obuObjects.isvalid = 1;
	for (auto& obupant:this->m_curSensorData.pants) {
		if(obupant.pos_lon <= 0 || obupant.pos_lat <= 0){
			cout<<FYEL("WARN: obu lon lat abnormal:") << ", obu id = " << obupant.ptc_id
			    <<", width = "<<obupant.width<< ", length = " <<obupant.length << endl;
			continue;
		}
		common_msgs_humble::msg::Sensorobject obuObject;
		float obuObjectAngleDegree = obupant.heading;
		float obuObjectAngleDegreeInLidarRFU_Clockwise = (obuObjectAngleDegree - sensorgps.heading);
		obuObjectAngleDegreeInLidarRFU_Clockwise = obuObjectAngleDegreeInLidarRFU_Clockwise < 0? obuObjectAngleDegreeInLidarRFU_Clockwise + 360:obuObjectAngleDegreeInLidarRFU_Clockwise;
		obuObjectAngleDegreeInLidarRFU_Clockwise = obuObjectAngleDegreeInLidarRFU_Clockwise >= 360 ? obuObjectAngleDegreeInLidarRFU_Clockwise - 360:obuObjectAngleDegreeInLidarRFU_Clockwise;


		Eigen::Vector3d objectPositionInCarBackFRU;
		double carCenterRFU_x, carCenterRFU_y;
		m_wgs84Utm.LatLonToLocalXY(sensorgps.lon, sensorgps.lat, sensorgps.heading, obupant.pos_lon, obupant.pos_lat, carCenterRFU_x, carCenterRFU_y);
		double temp_angle = obuObjectAngleDegreeInLidarRFU_Clockwise * M_PI / 180.0;
		double gx = 0, gy = 0;//0.3,m_carCenter2CarBackDistance;
		double xgap = gx * cos(temp_angle) - gy * sin(temp_angle);
		double ygap = gx * sin(temp_angle) + gy * cos(temp_angle);

		objectPositionInCarBackFRU[0] = carCenterRFU_x + xgap;
		objectPositionInCarBackFRU[1] = carCenterRFU_y - ygap;//m_carCenter2CarBackDistance;//目标中心点RFU转到车后轴RFU
		objectPositionInCarBackFRU[2] =0;
		//cout<<FGRN("diff1: obuObjectAngleDegreeInLidarRFU_Clockwise = ") << obuObjectAngleDegreeInLidarRFU_Clockwise <<", xgap = " << xgap <<", ygap = " << ygap<< endl;

		//ID移位，避免与车端跟踪ID冲突
		auto tempID = obupant.ptc_id;
		//obupant.ptc_id = static_cast<uint32_t>(obupant.ptc_id) << 16;//超过65535会溢出

		obuObject.id = static_cast<uint32_t>(obupant.ptc_id);
		// cout<<FYEL("WARN: change obu id: ")<<", raw id = "<< tempID << ", changed id = " << obuObject.id << endl;
		obuObject.x = objectPositionInCarBackFRU[0];
		obuObject.y = objectPositionInCarBackFRU[1];
		obuObject.z = 0;//objectPositionInCarBackFRU[2];

		obuObject.longtitude = obupant.pos_lon ;//objectPositionInCarBackFRU[2];
		obuObject.latitude = obupant.pos_lat;//objectPositionInCarBackFRU[2];
		obuObject.altitude = 0;//objectPositionInCarBackFRU[2];

		obuObject.relspeedx = obupant.speed * sin(obuObjectAngleDegreeInLidarRFU_Clockwise * M_PI / 180.0) - selfCarSpeed[0];
		obuObject.relspeedy = obupant.speed * cos(obuObjectAngleDegreeInLidarRFU_Clockwise * M_PI / 180.0) - selfCarSpeed[1];
		obuObject.azimuth = obuObjectAngleDegreeInLidarRFU_Clockwise * M_PI / 180.0;
		
		obuObject.classification = transOBUObjectType2CameraDetectionType(obupant.ptc_type); //static_cast<uint8_t>()

		// 暂不考虑僵尸车
		if(obuObject.classification == COMMON::LidarDetectionClassification::Zombiecar){
			continue;
		}

		obuObject.width = obupant.width;
		obuObject.length = obupant.length; //长宽为实际长宽
		obuObject.height = obupant.height;
		// if(obuObject.classification == COMMON::LidarDetectionClassification::Car
		// 	|| obuObject.classification == COMMON::LidarDetectionClassification::Zombiecar){
		// 	obuObject.length = 4;
		// 	obuObject.width = 1.85;
		// }
		// else if(obuObject.classification == COMMON::LidarDetectionClassification::Pedestrian){
		// 	obuObject.length = 0.6;
		// 	obuObject.width = 0.6;
		// }

		if(obuObject.x == 0 || obuObject.y == 0 || obuObject.width == 0 || obuObject.length == 0){
			cout<<FYEL("WARN: change obu info") << ", raw obu id = " << tempID << ", classification = " << static_cast<int>(obuObject.classification) 
				<<", x = "<<obuObject.x<< ", y = " <<obuObject.y
			    <<", width = "<<obuObject.width<< ", length = " <<obuObject.length << endl;
		}

		obuObject.value = static_cast<uint8_t>(COMMON::SensorType::OBU);

		//生成8角点
		float headingAnticlockwise = 2 * M_PI - obuObject.azimuth; //
		vector<double> boxInfo = {obuObject.x, obuObject.y, obuObject.z,
		                          obuObject.length, obuObject.width, obupant.height, headingAnticlockwise};
		vector<vector<double>> eightCornerPoints = m_common.boxes_to_corners_3d(boxInfo);
		for(const auto& singleCornerPointVector : eightCornerPoints){
			common_msgs_humble::msg::Point3d singleCornerPoint;
			singleCornerPoint.x = singleCornerPointVector[0];
			singleCornerPoint.y = singleCornerPointVector[1];
			singleCornerPoint.z = singleCornerPointVector[2];

			obuObject.points.emplace_back(singleCornerPoint);
		}
		
		// 僵尸车事件与僵尸车障碍物分开保存
		if(obuObject.classification == COMMON::LidarDetectionClassification::Zombiecar){
			auto it  = std::find_if(m_zombieObjects.obs.begin(),m_zombieObjects.obs.end(),[&](common_msgs_humble::msg::Sensorobject& object){
				return object.id == obuObject.id;
			});
			// 不存在：保存，存在：删除容器中旧有，更新
			if(it == m_zombieObjects.obs.end()){
				cout<<"Zombiecar 不存在，存入容器： id = "<<obuObject.id  << ", classification = " << static_cast<int>(obuObject.classification) 
					<<", x = "<<obuObject.x<< ", y = " <<obuObject.y
					<<", width = "<<obuObject.width<< ", length = " <<obuObject.length 
					<<std::setprecision(13)<<", lon = "<<obuObject.longtitude<< ", lat = " <<obuObject.longtitude<< endl;
				m_zombieObjects.obs.emplace_back(obuObject);
			}
			else{
				int zombieIndex = std::distance(m_zombieObjects.obs.begin(),it);
				cout<<"Zombiecar 删除容器中旧有，旧有 id = "<<m_zombieObjects.obs[zombieIndex].id  << ", 更新 id = "<<obuObject.id  << ", classification = " << static_cast<int>(obuObject.classification) 
					<<", x = "<<obuObject.x<< ", y = " <<obuObject.y
					<<", width = "<<obuObject.width<< ", length = " <<obuObject.length
					<<std::setprecision(13)<<", lon = "<<obuObject.longtitude<< ", lat = " <<obuObject.longtitude << endl;
				m_zombieObjects.obs.erase(m_zombieObjects.obs.begin() + zombieIndex);
				m_zombieObjects.obs.emplace_back(obuObject);
			}
		}
		else{
			obuObjects.obs.emplace_back(obuObject);
		}
	}
	cout<<"obu raw object size = "<<this->m_curSensorData.msg_cnt<<", this->m_curSensorData object size = "<<this->m_curSensorData.pants.size()
		<<", valid obu object size = "<<obuObjects.obs.size() <<", obu zombie object size = "<<m_zombieObjects.obs.size()<<endl;
	
	mergeZombieObject(obuObjects);
	cout<<"debug:finished OBU object trans\n";
}

template <typename T>
void StationObjects<T>::mergeZombieObject(common_msgs_humble::msg::Sensorobjects& obuObjects){
	for(auto& zombineObject : m_zombieObjects.obs){
		float minDistance = FLT_MAX;
		int minDistanceIndex;

		for (size_t i = 0; i < obuObjects.obs.size(); i++){
			common_msgs_humble::msg::Sensorobject& obuObject = obuObjects.obs[i];
			if(obuObject.classification != COMMON::LidarDetectionClassification::Car)
				continue;
			float distanceX = zombineObject.x - obuObject.x;
			float distanceY = zombineObject.y - obuObject.y;
			float distance = sqrt(distanceX * distanceX + distanceY * distanceY);
			if(distance < minDistance){
				minDistance = distance;
				minDistanceIndex = i;
			}

			// 僵尸车事件数据丢失时，找到之前的匹配对，对匹配到僵尸车事件的障碍物车辆赋值僵尸车类别
			auto it = m_obstacleEventMatchingPairs.find(obuObject.id);
			if(it != m_obstacleEventMatchingPairs.end()){
				obuObject.classification = COMMON::LidarDetectionClassification::Zombiecar;
			}

			
		}
		// 从数据看小于0.1认为是与僵尸车事件匹配的车辆障碍物,僵尸车事件未匹配到车辆障碍物,则将僵尸车事件保存到车辆障碍物中
		if(minDistance < 0.5){
			cout<<"mergeZombieObject: 匹配成功,僵尸车事件ID = "<< zombineObject.id <<", 匹配车辆障碍物ID = "<< obuObjects.obs[minDistanceIndex].id <<endl;
			obuObjects.obs[minDistanceIndex].classification = COMMON::LidarDetectionClassification::Zombiecar;

			// 与僵尸车事假匹配的车辆不在匹配对中，保存僵尸车事件ID与匹配车辆ID对
			auto it = std::find_if(m_obstacleEventMatchingPairs.begin(), m_obstacleEventMatchingPairs.end(),[&](const std::pair<int, int>& pair) {
				return obuObjects.obs[std::get<1>(pair)].id == zombineObject.id;
			});
			if (it == m_obstacleEventMatchingPairs.end()) {
				m_obstacleEventMatchingPairs.insert(std::make_pair(obuObjects.obs[minDistanceIndex].id, zombineObject.id));
			}
		}
		else if(minDistance != FLT_MAX){
			cout<<"mergeZombieObject: 匹配不成功,僵尸车事件ID = "<< zombineObject.id <<", minDistance = "<< minDistance
				<<", 匹配车辆障碍物ID = "<< obuObjects.obs[minDistanceIndex].id  <<endl;
			obuObjects.obs.emplace_back(zombineObject);
		}
		else{
			cout<<"mergeZombieObject: 匹配不成功,僵尸车事件ID = "<< zombineObject.id <<", minDistance = "<< minDistance <<endl;
			obuObjects.obs.emplace_back(zombineObject);
			cout<<"debug： zombie事件匹配OBU目标失败"<<endl;
		}

		
	}
}


#endif

template <typename T>
StationObjects<T>::~StationObjects(){

}





template <typename T>
int StationObjects<T>::transOBUObjectType2CameraDetectionType(const int& obuObjectType){
	switch(obuObjectType){
		case 0:
			return COMMON::LidarDetectionClassification::Unknown;
		case 1:
			return  COMMON::LidarDetectionClassification::Pedestrian;
		case 2:
			return  COMMON::LidarDetectionClassification::Car;
		case 3:
			return  COMMON::LidarDetectionClassification::Bus;
		case 4:
			return  COMMON::LidarDetectionClassification::Truck;
		case 5:
			return  COMMON::LidarDetectionClassification::Bicycle; // 非机动车认为是自行车
		case 100: {
			cout <<FYEL("find Zombiecar object\n") << endl;
			return COMMON::LidarDetectionClassification::Zombiecar;
		}
		default:
			return  COMMON::LidarDetectionClassification::Unknown;
	}
}

// V2X grop 测试版
// template <typename T>
// int StationObjects<T>::transOBUObjectType2CameraDetectionType(const int& obuObjectType){
// 	switch(obuObjectType){
// 		case 0:
// 			return COMMON::LidarDetectionClassification::Unknown;
// 		case 1:
// 			return  COMMON::LidarDetectionClassification::Car;
// 		case 2:
// 			return  COMMON::LidarDetectionClassification::Bicycle; // 非机动车认为是自行车
// 		case 3:
// 			return  COMMON::LidarDetectionClassification::Pedestrian;
			
// 		case 4:
// 			return  COMMON::LidarDetectionClassification::Truck;
// 		case 5:
// 			return  COMMON::LidarDetectionClassification::Bus;
// 		case 100: {
// 			ROS_INFO("find Zombiecar object\n");
// 			return COMMON::LidarDetectionClassification::Zombiecar;
// 		}
// 		default:
// 			return  COMMON::LidarDetectionClassification::Unknown;
// 	}
// }


// // 20240314更改版本 鬼探头 无保护左转 红绿灯(类别协议乱) 违停
// template <typename T>
// int StationObjects<T>::transOBUObjectType2CameraDetectionType(const int& obuObjectType){
// 	switch(obuObjectType){
// 		case 0:
// 		case 1:
// 		case 2:
// 			return COMMON::LidarDetectionClassification::Unknown;
// 		case 10:
// 			return  COMMON::LidarDetectionClassification::Pedestrian;
// 		case 3:
// 			return  COMMON::LidarDetectionClassification::Car;
// 		case 4:
// 		case 6:
// 			return  COMMON::LidarDetectionClassification::Bus;
// 		case 5:
// 			return  COMMON::LidarDetectionClassification::Truck;
// 		case 7:
// 		case 8:
// 			return  COMMON::LidarDetectionClassification::Bicycle; // 非机动车认为是自行车
// 		case 9:
// 			return  COMMON::LidarDetectionClassification::Tricycle; // 非机动车认为是自行车
// 		case 100: {
// 			ROS_INFO("find Zombiecar object\n");
// 			return COMMON::LidarDetectionClassification::Zombiecar;
// 		}
// 		default:
// 			return  COMMON::LidarDetectionClassification::Unknown;
// 	}
// }


/***
 * 20221027 lidar检测与传感器信息的时间同步 20220224 添加同步话题标志位
 * @tparam T 数据类型
 * @param msgDeque 需要同步的传感器（非主传感器）
 * @param curObjectFrameStamp 当前传感器时间时间
 * @param synchroFlag 同步标志位，1：lidar-gps，2：lidar-radar 3:lidar-obu
 */
template <typename T>
void StationObjects<T>::timeSynchro(std::deque<T>& msgDeque,const long& curObjectFrameStamp, const int synchroFlag){
	std::lock_guard<std::mutex> dequeLock(this->m_dataMutex);
	// for(int i = 0; i < msgDeque.size(); i++){
	// 	std::cout<<std::setprecision(13) << "\ni: "<< i<<" " << msgDeque[i].timestamp * 0.001 << std::endl;
	// }
	
	if(msgDeque.empty()) {
		cout<<"\tmsgDeque empty,no need to syncchro\n";
		return;
	}
	cout <<"........station msgDeque.size() = " << msgDeque.size() << endl;
	double curFrameStamp = (double)curObjectFrameStamp / 1000.0;

	int curMsgIndex = 0;
	for (int i = 0; i < msgDeque.size(); ++i) {
		T curMsg = msgDeque[i];
		if((curMsg.timestamp + m_sensorTimeStampGap) / 1000.0  > curFrameStamp )
			break;
		curMsgIndex = i;
	}

	// cout<<"......curMsgIndex = " << curMsgIndex <<"， msgDeque.size() = " << msgDeque.size() << endl;
	if(curMsgIndex + 1 == msgDeque.size()){ //20221014 针对GPS的时间戳都小于lidar时间戳的情况只保留最后一个GPS数据
		if(curMsgIndex > 0){//等于0 说明容器内第一帧radar数据就大于当前帧lidar数据，不处理
			while(curMsgIndex--){
				msgDeque.pop_front();
			}
		}
	}
	else{
		if(curMsgIndex > 0){//小于0 说明容器内第一帧radar数据就大于当前帧lidar数据，不处理
			if(abs(curFrameStamp - (msgDeque[curMsgIndex].timestamp + m_sensorTimeStampGap) / 1000.0) >//lidar前一帧
			   abs(curFrameStamp - (msgDeque[curMsgIndex + 1].timestamp + m_sensorTimeStampGap) / 1000.0)){//lidar后一帧
				curMsgIndex += 1;//取最近的一帧数据 后一帧
			}
			while(curMsgIndex--){
				msgDeque.pop_front();
			}
		}
	}
	float timeGap = curFrameStamp - (msgDeque.front().timestamp + m_sensorTimeStampGap) / 1000.0;
	static float minLidarGPSSynchroTime = FLT_MAX;
	static float maxLidarGPSSynchroTime = FLT_MIN;
	
	minLidarGPSSynchroTime = abs(minLidarGPSSynchroTime) < abs(timeGap) ? abs(minLidarGPSSynchroTime) : abs(timeGap);
	maxLidarGPSSynchroTime = abs(maxLidarGPSSynchroTime) > abs(timeGap) ? abs(maxLidarGPSSynchroTime) : abs(timeGap);
	cout<<std::setprecision(16)<<"\tlidar-obu时间差："<<timeGap<<", 当前obu msg时间 = "<<(msgDeque.front().timestamp +m_sensorTimeStampGap) / 1000.0 <<", lidar时间 ="
		<<curFrameStamp<<endl;
	cout<<std::setprecision(16)<<"\tabs(minLidarOBUSynchroTime)： "<<abs(minLidarGPSSynchroTime)<<", abs(maxLidarOBUSynchroTime) = "<<abs(maxLidarGPSSynchroTime) <<endl;
	cout<<"\tOBU Deque.size = "<<msgDeque.size() <<endl;
	

}

} // namespace SENSOROBJECTS
#endif // __STATIONOBJECTS_H__