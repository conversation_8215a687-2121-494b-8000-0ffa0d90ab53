/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2024-01-15 16:45:47
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-07-11 14:03:15
 * @FilePath: /src/perception/commonlibrary/src/sensorobjects/lidarobjects.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef __LIDAROBJECTS__H__
#define __LIDAROBJECTS__H__

#include <cfloat>
#include "objects.h"

namespace SENSOROBJECTS{

#ifdef ROS1_FOUND
class LidarObjects: public SensorObjects<common_msgs::sensorobjects>
{
    private:
        /* data */
    public:
        LidarObjects(ros::NodeHandle& rosNode, const std::string& topicName, const int& sensorType);
        ~LidarObjects();
        void timeSynchroBeforeCurrent(const long& curObjectFrameStamp, const int& synchroFlag);
};
#endif
#ifdef ROS2_FOUND
class LidarObjects: public SensorObjects<common_msgs_humble::msg::Sensorobjects>
{
    private:
        /* data */
    public:
        LidarObjects(rclcpp::Node& rosNode, const std::string& topicName, const int& sensorType);
        ~LidarObjects();
        void timeSynchroBeforeCurrent(const long& curObjectFrameStamp, const int& synchroFlag);
};
#endif


} // end namespace SENSOROBJECTS

#endif // __LIDAROBJECTS__H__
