/*
 * @Description  : 
 * @Version      : V1.0.0
 * <AUTHOR> hanshuangquan <EMAIL>
 * @Date         : 2024-04-11 17:54:43
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-07-10 08:51:20
 * @FilePath: /src/perception/commonlibrary/src/sensorobjects/lidarobjects.cpp
 * Copyright 2024 vanjee, All Rights Reserved. 
 * 2024-04-11 17:54:43
 */
#include "lidarobjects.h"

namespace SENSOROBJECTS{

#ifdef ROS1_FOUND
LidarObjects::LidarObjects(ros::NodeHandle& rosNode, const std::string& topicName, const int& sensorType)
    :SensorObjects<common_msgs::sensorobjects>(rosNode, topicName, sensorType){

}
#endif
#ifdef ROS2_FOUND
LidarObjects::LidarObjects(rclcpp::Node& rosNode, const std::string& topicName, const int& sensorType)
    :SensorObjects<common_msgs_humble::msg::sensorobjects>(rosNode, topicName, sensorType){

}
#endif
LidarObjects::~LidarObjects()
{}

/***
 * 20221027 lidar检测与传感器信息的时间同步
 * @tparam T 数据类型
 * @param curObjectFrameStamp 当前lidar检测时间
 * @param synchroFlag 同步标志位，1：lidar-gps，2：lidar-radar 3:lidar-obu
 */
void LidarObjects::timeSynchroBeforeCurrent(const long& curObjectFrameStamp, const int& synchroFlag){
    if(curObjectFrameStamp < 0){
        cout <<FYEL("WARN: curObjectFrameStamp < 0, please check") << endl;
    }

    if(synchroFlag < 0 || synchroFlag > 19){
        cout <<FYEL("WARN: undefined sensor type, please check") << endl;
    }


	std::lock_guard<std::mutex> dequeLock(m_dataMutex);
	if(m_SensorDataDeque.empty()) {
		// cout<<"\tmsgDeque empty,no need to syncchro\n";
		return;
	}
	double curFrameStamp = (double)curObjectFrameStamp / 1000.0;

	//找到容器中数据时间戳小于同步时间的最近索引
	int curMsgIndex = 0;
	int msgDequeSize = m_SensorDataDeque.size();
	for (int i = 0; i < msgDequeSize; ++i) {
		common_msgs::sensorobjects curMsg = m_SensorDataDeque[i];
		if(curMsg.timestamp / 1000.0  > curFrameStamp )
			break;
		curMsgIndex = i;
	}

    while(curMsgIndex--){
        m_SensorDataDeque.pop_front();
    }
	
	float timeGap = curFrameStamp - m_SensorDataDeque.front().timestamp / 1000.0;
	static float minSynchroTime = FLT_MAX, maxSynchroTime = FLT_MIN;

    minSynchroTime = abs(minSynchroTime) < abs(timeGap) ? abs(minSynchroTime) : abs(timeGap);
    maxSynchroTime = abs(maxSynchroTime) > abs(timeGap) ? abs(maxSynchroTime) : abs(timeGap);
    cout<<std::setprecision(16)<<"\t时间差："<<timeGap<<", 同步传感器msg时间 = "<<m_SensorDataDeque.front().timestamp / 1000.0 <<", 被同步传感器（主）检测时间 ="
        <<curFrameStamp<<endl;
    cout<<std::setprecision(16)<<"\tabs(minSynchroTime)： "<<abs(minSynchroTime)<<", abs(maxSynchroTime) = "<<abs(maxSynchroTime) <<endl;
    //cout<<"\tgps Deque.size = "<<m_SensorDataDeque.size() <<endl;
	
}

} // SENSOROBJECTS