/*
 * @Description  : 
 * @Version      : V1.0.0
 * <AUTHOR> hanshuangquan <EMAIL>
 * @Date         : 2024-01-25 16:52:40
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-07-09 18:19:41
 * @FilePath: /src/perception/commonlibrary/src/sensorobjects/gps.cpp
 * Copyright 2024 Marvin, All Rights Reserved. 
 * 2024-01-25 16:52:40
 */
#include "gps.h"

namespace SENSOROBJECTS{

#ifdef ROS1_FOUND    
SensorGPS::SensorGPS(ros::NodeHandle& nodeName, const std::string& topicName, const int& sensorType)
:SensorObjects<common_msgs::sensorgps>(nodeName, topicName, sensorType)
{

}

SensorGPS::~SensorGPS(){

}
#endif
#ifdef ROS2_FOUND
SensorGPS::SensorGPS(rclcpp::Node& nodeName, const std::string& topicName, const int& sensorType)
:SensorObjects<common_msgs_humble::msg::Sensorgps>(nodeName, topicName, sensorType)
{

}
SensorGPS::~SensorGPS(){

}
#endif


} // end namespace SENSOROBJEC