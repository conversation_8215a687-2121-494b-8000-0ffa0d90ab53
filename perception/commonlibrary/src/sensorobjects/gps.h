/*
 * @Description  : 
 * @Version      : V1.0.0
 * <AUTHOR> hanshuangquan <EMAIL>
 * @Date         : 2024-01-25 10:46:05
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-07-09 18:12:54
 * @FilePath: /src/perception/commonlibrary/src/sensorobjects/gps.h
 * Copyright 2024 Marvin, All Rights Reserved. 
 * 2024-01-25 10:46:05
 */

#ifndef __GPS__H_
#define __GPS__H_
#include "objects.h"

#ifdef ROS1_FOUND
#include "common_msgs/sensorgps.h"
#endif

#ifdef ROS2_FOUND
#include "common_msgs_humble/msg/sensorgps.h"
#endif


namespace SENSOROBJECTS{

#ifdef ROS1_FOUND    
class SensorGPS: public SensorObjects<common_msgs::sensorgps>
{
private:
public:
    SensorGPS(ros::NodeHandle& nodeName, const std::string& topicName, const int& sensorType);
    ~SensorGPS();
    // void preprocess(const long& curObjectFrameStamp, const int& synchroFlag);
    void run(const long& curObjectFrameStamp, const int& synchroFlag);
};
#endif

#ifdef ROS2_FOUND
class SensorGPS: public SensorObjects<common_msgs_humble::msg::sensorgps>
{
private:
public:
    SensorGPS(rclcpp::Node& nodeName, const std::string& topicName, const int& sensorType);
    ~SensorGPS();
    // void preprocess(const long& curObjectFrameStamp, const int& synchroFlag);
    void run(const long& curObjectFrameStamp, const int& synchroFlag);
};
#endif


} // end namespace SENSOROBJECTS

#endif /* __GPS__H_ */