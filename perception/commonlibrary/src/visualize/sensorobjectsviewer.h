/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2024-10-21 10:26:34
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-07-10 09:19:35
 * @FilePath: /src/perception/commonlibrary/src/visualize/sensorobjectsviewer.h
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
#ifndef SENSOROBJECTSVIEWER_H
#define SENSOROBJECTSVIEWER_H

#include <ros/ros.h>

#include <visualization_msgs/MarkerArray.h>
#include "common_msgs/sensorobjects.h"

#include "viewer.h"
#include "../common.h"

using namespace std;

namespace VISUALIZATION{
#ifdef ROS1_FOUND
class SensorObjectsViewer: public Viewer<common_msgs::sensorobjects>
{
	public:
		Common m_cCommon;
		
		SensorObjectsViewer(ros::NodeHandle& nh, const std::string& pubTopicName, const int& showType);
		~SensorObjectsViewer();
		void visualize(const common_msgs::sensorobjects& objects);
	private:
};

#endif
#ifdef ROS_FOUND
class SensorObjectsViewer: public Viewer<common_msgs_humble::msg::sensorobjects>
{ 
	public:
		Common m_cCommon;
		
		SensorObjectsViewer(rclcpp::Node& nh, const std::string& pubTopicName, const int& showType);
		~SensorObjectsViewer();
		void visualize(const common_msgs_humble::msg::sensorobjects& objects);
	private:
};
#endif

} // namespace VISUALIZATION

#endif