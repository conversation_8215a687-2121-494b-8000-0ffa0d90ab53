#ifndef _VIEWER_H_
#define _VIEWER_H_

#include <map>
#ifdef ROS1_FOUND
#include <ros/ros.h>

#include <visualization_msgs/MarkerArray.h>
#include "common_msgs/sensorobjects.h"
#endif
#ifdef ROS2_FOUND
#include <rclcpp/rclcpp.hpp>
#include <visualization_msgs/msg/marker_array.hpp>
#include "common_msgs_humble/msg/sensorobjects.hpp"
#endif

using namespace std;

const static std::map<int, std::string> showTypeMap = {
		{19, "FusionTrackedObjects"},
		{20, "Radar_MatchedLidar"},
		{21, "Radar_UnMatchedLidar"}
};


namespace VISUALIZATION{
	enum ShowType{
		FusionTrackedObjects = 19,
		Radar_MatchedLidar = 20,
		Radar_UnMatchedLidar = 21
	};

	#ifdef ROS1_FOUND

		template<typename T>
		class Viewer
		{
			public:
				ros::Publisher pub_objectsMarker;
				int m_showType;

				Viewer(ros::NodeHandle& nh, const std::string& pubTopicName, const int& showType);
				~Viewer();
				void visualize(const T& objects);

			private:
				ros::NodeHandle m_nh;
				std::string m_frameName = "car";
				std::string m_markerColor = "white";
				std::string m_topicName;
				
				std::string m_showTypeName;
		};


		template<typename T>
		Viewer<T>::Viewer(ros::NodeHandle& nh, const std::string& pubTopicName, const int& showType):m_nh(nh),m_topicName(pubTopicName),m_showType(showType)
		{
			pub_objectsMarker = m_nh.advertise<visualization_msgs::msg::MarkerArray>(pubTopicName, 10, true);	
			m_showTypeName = showTypeMap.at(m_showType);

			m_nh.param("markercolor", m_markerColor, m_markerColor);
			m_nh.param("myframe", m_frameName, m_frameName);

			// std::cout << "m_markerColor: " << m_markerColor << std::endl;
			// std::cout << "frame tf: " << m_frameName << std::endl;
		}
		template<typename T>
		Viewer<T>::~Viewer() 
		{

		}
	
		#endif
		#ifdef ROS2_FOUND
		template<typename T>
		class Viewer
		{
			public:
				rclcpp::Publisher<visualization_msgs::msg::MarkerArray> pub_objectsMarker;
				int m_showType;

				Viewer(rclcpp::Node& nh, const std::string& pubTopicName, const int& showType);
				~Viewer();
				void visualize(const T& objects);

			private:
				rclcpp::Node::SharedPtr m_nh;
				std::string m_frameName = "car";
				std::string m_markerColor = "white";
				std::string m_topicName;
				
				std::string m_showTypeName;
		};


		template<typename T>
		Viewer<T>::Viewer(rclcpp::Node::SharedPtr& nh, const std::string& pubTopicName, const int& showType):m_nh(nh),m_topicName(pubTopicName),m_showType(showType)
		{
			pub_objectsMarker = m_nh->create_publisher<visualization_msgs::msg::MarkerArray>(pubTopicName, 10, true);	
			m_showTypeName = showTypeMap.at(m_showType);

			m_nh.param("markercolor", m_markerColor, m_markerColor);
			m_nh.param("myframe", m_frameName, m_frameName);

			// std::cout << "m_markerColor: " << m_markerColor << std::endl;
			// std::cout << "frame tf: " << m_frameName << std::endl;
		}

		template<typename T>
		Viewer<T>::~Viewer() 
		{

		}
	#endif


} // namespace VISUALIZATION


#endif //_VIEWER_H_



