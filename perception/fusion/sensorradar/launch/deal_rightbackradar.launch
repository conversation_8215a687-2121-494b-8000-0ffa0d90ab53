<launch>

 

    <!--右后雷达 -->
   <node name="radarrightback_driver"  pkg="radar_driver"   type="radar_driver"   output="screen" respawn="true">
        <!--单位：cm 127-->   
        <param name="radardeltax" value="-95"  type="int"/>
        <!--单位：cm -640-->
        <param name="radardeltay" value="-470"  type="int"/>
        <!--单位：0.01度 -->
        <param name="radardeltayaw" value="2900"   type="int"/>
        <!--canet/pc ip(网络地址) 和 port(端口号)-->
        <param name="radarcanetip" value="*************" type="string"/>
        <param name="radarcanetport" value="3333" type="int"/>
        <param name="radarpcip" value="*************" type="string"/>
        <param name="radarpcport" value="4444" type="int"/>
        <!--是否进行在线标定 0:否 1：是-->
        <param name="radarcali" value="1"   type="int"/> 
        <param name="radardebug" value="0"   type="int"/>
        
   </node>
    <node name="radarrightback_deal"    pkg="radar_deal"   type="radar_deal"   output="screen" respawn="true"/>


   <!--毫米波雷达融合-->
   <node name="sensorradar"   pkg="sensorradar"   type="sensorradar"       output="screen" respawn="true"/> 

</launch>
