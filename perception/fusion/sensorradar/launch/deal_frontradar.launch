<launch>

   <node name="radarfront_driver"  pkg="radar_driver"   type="radar_driver"   output="screen" respawn="true">
         <!--单位：cm -->  
        <param name="radardeltax" value="37"  type="int"/>
        <param name="radardeltay" value="0"  type="int"/>
         <!--单位：0.01度 -->
        <param name="radardeltayaw" value="50"   type="int"/>
        
        <param name="radarcanetip" value="*************" type="string"/>
        <param name="radarcanetport" value="7777" type="int"/>
        <param name="radarpcip" value="*************" type="string"/>
        <param name="radarpcport" value="8888" type="int"/>
       
        <param name="radarcali" value="1"   type="int"/> 
        <param name="radarupdown" value="0"   type="int"/>
        <param name="radardebug" value="1"   type="int"/>

   </node>  
   <node name="radarfront_deal"    pkg="radar_deal"   type="radar_deal"   output="screen" respawn="true"/>

   <node name="sensorradar"   pkg="sensorradar"   type="sensorradar"       output="screen" respawn="true"/> 

</launch>