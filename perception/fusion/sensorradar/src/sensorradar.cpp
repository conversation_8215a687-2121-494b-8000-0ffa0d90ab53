
/**************************************************************************
Copyright: 	2021-2023 VANJEE Technology.
File name: 	sensorradar.cpp
Description: 毫米波雷达融合程序，通过加权平均对数据进行滤波。	
Author: 	zhuxuekui
Version: 	V1.0
Date: 		2019.6
History: 	无
**************************************************************************/
#include "sensorradar.h"

    /*************************************************
	Function:       SensorRadar
	Description:    构造函数，主要用于数据初始化
	Input:          handle：ros句柄
	Output:         无
	Return:         无
	*************************************************/  
SensorRadar::SensorRadar(ros::NodeHandle mh)
{
	//variable initialize
	bdebug = 1;
	loopFrep = 10;
	for(int i = 0; i < RADARNUM; ++i)
	{
		m_radarcnt[i] = 0;
	}
	m_carNumber = 1;    // 20221110
	isRadarPosionSpeedVerify = false; //202221201
	m_isUseRosBag = false; //20221208
	m_isShowRadarNumber = false; //20230522
	m_showRadarNumber = 0;
	m_isSaveObjectInfoCSV = false;
	
	updatelog="null";
	std::string nodeName = ros::this_node::getName(); // "/sensrorradar"
	cout<<"nodeName: "<<nodeName<<endl;
	mh.param("bdebug",bdebug,bdebug);
	mh.param("rsloopfrep",loopFrep,loopFrep);
	mh.param("updatelog",updatelog,updatelog);
	mh.param("/carNumber", m_carNumber, m_carNumber);// 20221110
	mh.param(nodeName + "/isShowRadarNumber",m_isShowRadarNumber,m_isShowRadarNumber);//20230522
	mh.param(nodeName + "/showRadarMarkerNumber",m_showRadarNumber,m_showRadarNumber);//20230522
	mh.param("/isUseRosBag", m_isUseRosBag, m_isUseRosBag);// 20221110
	mh.param("isRadarPosionSpeedVerify",isRadarPosionSpeedVerify,isRadarPosionSpeedVerify);//20221201
	mh.param(nodeName + "/isSaveObjectInfoCSV", m_isSaveObjectInfoCSV, m_isSaveObjectInfoCSV);
	
	//设置保存路径
	int npos = nodeName.find_first_not_of("/");
	std::string nodeNmaeNoChar = nodeName.substr(npos); //"sensrorradar"
	std::string path_txt = ros::package::getPath(nodeNmaeNoChar);
	cout<<"path_txt1: "<<path_txt<<endl;
	int top_n = 2;//需要输出当前路径的前 top_n 级目录
	for(int i=0; i<top_n; i++){
		int path_txt_last = path_txt.find_last_of("/\\");//反向查找, 遇到" "中任一字符均返回
		std::string path_txt_1 = path_txt.substr(0, path_txt_last);
		path_txt = path_txt_1;
	}
	cout<<"path_txt2: "<<path_txt<<endl;
	radarObjectsSavedPath = path_txt + "/outputMapping/radarObjects";          //相对路径
	cout<<"radarObjectsSavedPath: "<<radarObjectsSavedPath<<endl;
	
	if(m_isSaveObjectInfoCSV){
		std::string savePathName =
				radarObjectsSavedPath + "/" + std::to_string(ros::Time::now().toSec()) + ".csv";
		radarObjectInfoFile.open(savePathName, ios::out | ios::app);
		if(!radarObjectInfoFile.is_open()){
			fstream tempFile(savePathName, ios::out | ios::app);
			tempFile.close();
			radarObjectInfoFile.open(savePathName, ios::out | ios::trunc);
			if(!radarObjectInfoFile.is_open())
				cerr<<"file open error: " << savePathName << endl;
		}
		else{
			//cout<<"selfCarInfo save PathName: " << savePathName << endl;
		}
		//添加表头
		//radarObjectInfoFile << "timestamp,radarObjectID,classfication,X-m,Y-m,Z-m,speedX-m/s,speedY-m/s,speed-m/s,"
		//					   "yaw-RFU-YP-clockwise360,length-m,width-m,"
		//                       "height-m,infoSource(radarid)"<<endl;
	}
	
	
	if(m_carNumber == 1){ //红旗1
		SUBTOPIC_RADARLEFTFRONT = "/radarleftfront_deal/radar";
		SUBTOPIC_RADARRIGHTFRONT = "/radarrightfront_deal/radar";
		SUBTOPIC_RADARLEFTBACK = "/radarleftback_deal/radar";
		SUBTOPIC_RADARRIGHTBACK = "/radarrightback_deal/radar";
		SUBTOPIC_RADARFRONT = "/radarfront_deal/radar";
		SUBTOPIC_RADARBACK = "/radarback_deal/radar";
	}
	else if(m_carNumber == 2){
		SUBTOPIC_RADARLEFTFRONT = "/radar/fl";
		SUBTOPIC_RADARRIGHTFRONT = "/radar/fr";
		SUBTOPIC_RADARLEFTBACK = "/radar/rl";
		SUBTOPIC_RADARRIGHTBACK = "/radar/rr";
		SUBTOPIC_RADARFRONT = "/radar/front";
		SUBTOPIC_RADARBACK = "/radar/back"; // 暂无
		
		loadCaliParam(mh); // 20221111 加载radar-ins外参
	}
	else{
		cout<<"m_carNumber == 3\n";
		SUBTOPIC_RADARLEFTFRONT = "/radar/fl";
		SUBTOPIC_RADARRIGHTFRONT = "/radar/fr";
		SUBTOPIC_RADARLEFTBACK = "/radar/rl";
		SUBTOPIC_RADARRIGHTBACK = "/radar/rr";
		SUBTOPIC_RADARFRONT = "/radar/front";
		SUBTOPIC_RADARBACK = "/radar/back"; // 暂无
	}
	
	std::cout<<FRED("Copyright©2021-2023 VANJEE Technology. All rights reserved ")<<std::endl;
	std::cout<<FYEL("*****sensorradar:parameters*******************")<<std::endl;
	std::cout<<FGRN("m_carNumber:")<<m_carNumber<<std::endl;
	std::cout<<FGRN("m_isUseRosBag:")<<m_isUseRosBag<<std::endl;
	std::cout<<FGRN("isRadarPosionSpeedVerify:")<<isRadarPosionSpeedVerify<<std::endl;
	std::cout<<FGRN("bdebug:")<<bdebug<<std::endl;
	std::cout<<FGRN("loopFrep: ")<<loopFrep<<std::endl;
	std::cout<<FGRN("update_log: ")<<updatelog<<std::endl;
	std::cout<<FGRN("m_isShowRadarNumber: ")<<m_isShowRadarNumber<<std::endl;
	std::cout<<FGRN("m_showRadarMarkerNumber: ")<<m_showRadarNumber<<std::endl;
	std::cout<<FGRN("m_isSaveObjectInfoCSV: ")<<m_isSaveObjectInfoCSV<<std::endl;
	std::cout<<FYEL("*****sensorradar:parameters end***************")<<std::endl;

	if(m_carNumber == 2){
		sub_radarfront = mh.subscribe (SUBTOPIC_RADARFRONT,1000,&SensorRadar::SubCallback_radarfront,this);
		sub_radarleftfront = mh.subscribe (SUBTOPIC_RADARLEFTFRONT, 1000,&SensorRadar::SubCallback_radarleftfront,this);  //sub
		sub_radarrightfront = mh.subscribe (SUBTOPIC_RADARRIGHTFRONT, 1000,&SensorRadar::SubCallback_radarrightfront,this);
		sub_radarback = mh.subscribe (SUBTOPIC_RADARBACK,1000,&SensorRadar::SubCallback_radarback,this);
		sub_radarleftback = mh.subscribe (SUBTOPIC_RADARLEFTBACK, 1000,&SensorRadar::SubCallback_radarleftback,this);  //sub
		sub_radarrightback = mh.subscribe (SUBTOPIC_RADARRIGHTBACK, 1000,&SensorRadar::SubCallback_radarrightback,this);
	}
	else if(m_carNumber == 1){
		sub_radarfront = mh.subscribe (SUBTOPIC_RADARFRONT,1000,&SensorRadar::SubCallback_radarfrontCar1,this);
		sub_radarleftfront = mh.subscribe (SUBTOPIC_RADARLEFTFRONT, 1000,&SensorRadar::SubCallback_radarleftfrontCar1,this);  //sub
		sub_radarrightfront = mh.subscribe (SUBTOPIC_RADARRIGHTFRONT, 1000,&SensorRadar::SubCallback_radarrightfrontCar1,this);
		sub_radarback = mh.subscribe (SUBTOPIC_RADARBACK,1000,&SensorRadar::SubCallback_radarbackCar1,this);
		sub_radarleftback = mh.subscribe (SUBTOPIC_RADARLEFTBACK, 1000,&SensorRadar::SubCallback_radarleftbackCar1,this);  //sub
		sub_radarrightback = mh.subscribe (SUBTOPIC_RADARRIGHTBACK, 1000,&SensorRadar::SubCallback_radarrightbackCar1,this);
	}
    
    //pub message
	if(isRadarPosionSpeedVerify){//单独验证原始数据用
		pub_radar = mh.advertise<common_msgs::sensorobjects>("sensorradar2",1000);
		pub_show = mh.advertise<PointCloud>("radar_cloud2",1000);
	}
	//else if(m_isUseRosBag){//用bag验证
	//	pub_radar = mh.advertise<common_msgs::sensorobjects>("sensorradar1",1000);
	//	pub_show = mh.advertise<PointCloud>("radar_cloud1",1000);
	//}
	else{//在线运行
		pub_radar = mh.advertise<common_msgs::sensorobjects>("sensorradar",1000);
		pub_show = mh.advertise<PointCloud>("radar_cloud",1000);
	}


    pub_frontradarstatus = mh.advertise<common_msgs::sensorstatus>("front_radar_status",1000); 
    pub_rightfrontradarstatus = mh.advertise<common_msgs::sensorstatus>("rightfront_radar_status",1000); 
    pub_leftfrontradarstatus = mh.advertise<common_msgs::sensorstatus>("leftfront_radar_status",1000); 
    pub_backradarstatus = mh.advertise<common_msgs::sensorstatus>("back_radar_status",1000); 
    
	pub_rightbackradarstatus = mh.advertise<common_msgs::sensorstatus>("rightback_radar_status",1000); 
    pub_leftbackradarstatus = mh.advertise<common_msgs::sensorstatus>("leftback_radar_status",1000); 
	sensorradarMarkerArrayPub = mh.advertise<visualization_msgs::MarkerArray>("/sensorradar/sensorradarMarker",100);
	pub_sensorradarMarkerArrayInfo = mh.advertise<visualization_msgs::MarkerArray>("/sensorradar/sensorradarMarkerArrayInfo",1);
	pub_sensorradarElapsedtime = mh.advertise<common_msgs::elapsedtime>("/sensorradar/elapsedtime",1);
	
}

/***
 * 20221111 从配置文件读取radar-ins外参
 * @param mh 节点名
 */
void SensorRadar::loadCaliParam(ros::NodeHandle mh){
	std::string node_name = ros::this_node::getName();
	
	//#    i.前ARS-408毫米波 - 0
	frontRotation_x = 0;
	frontRotation_y = 0;
	frontRotation_z = 0;
	frontRotation_w = 1;
	frontTranslation_x = 0;
	frontTranslation_y = 0;
	frontTranslation_z = 0;
	mh.param(node_name + "/frontRotation_x", frontRotation_x, frontRotation_x);
	mh.param(node_name + "/frontRotation_y", frontRotation_y, frontRotation_y);
	mh.param(node_name + "/frontRotation_z", frontRotation_z, frontRotation_z);
	mh.param(node_name + "/frontRotation_w", frontRotation_w, frontRotation_w);
	mh.param(node_name + "/frontTranslation_x", frontTranslation_x, frontTranslation_x);
	mh.param(node_name + "/frontTranslation_y", frontTranslation_y, frontTranslation_y);
	mh.param(node_name + "/frontTranslation_z", frontTranslation_z, frontTranslation_z);
	frontQuaternion.x() = frontRotation_x;
	frontQuaternion.y() = frontRotation_y;
	frontQuaternion.z() = frontRotation_z;
	frontQuaternion.w() = frontRotation_w;
	frontTranslation << frontTranslation_x, frontTranslation_y, frontTranslation_z;
	
	//#    ii.左前SRR-308毫米波-1
	leftFrontRotation_x = 0;
	leftFrontRotation_y = 0;
	leftFrontRotation_z = 0;
	leftFrontRotation_w = 1;
	leftFrontTranslation_x = 0;
	leftFrontTranslation_y = 0;
	leftFrontTranslation_z = 0;
	mh.param(node_name + "/leftFrontRotation_x", leftFrontRotation_x, leftFrontRotation_x);
	mh.param(node_name + "/leftFrontRotation_y", leftFrontRotation_y, leftFrontRotation_y);
	mh.param(node_name + "/leftFrontRotation_z", leftFrontRotation_z, leftFrontRotation_z);
	mh.param(node_name + "/leftFrontRotation_w", leftFrontRotation_w, leftFrontRotation_w);
	mh.param(node_name + "/leftFrontTranslation_x", leftFrontTranslation_x, leftFrontTranslation_x);
	mh.param(node_name + "/leftFrontTranslation_y", leftFrontTranslation_y, leftFrontTranslation_y);
	mh.param(node_name + "/leftFrontTranslation_z", leftFrontTranslation_z, leftFrontTranslation_z);
	leftFrontQuaternion.x() = leftFrontRotation_x;
	leftFrontQuaternion.y() = leftFrontRotation_y;
	leftFrontQuaternion.z() = leftFrontRotation_z;
	leftFrontQuaternion.w() = leftFrontRotation_w;
	leftFrontTranslation << leftFrontTranslation_x, leftFrontTranslation_y, leftFrontTranslation_z;
	
	//	#    iii.右前SRR-308毫米波-2
	rightFrontRotation_x = 0;
	rightFrontRotation_y = 0;
	rightFrontRotation_z = 0;
	rightFrontRotation_w = 1;
	rightFrontTranslation_x = 0;
	rightFrontTranslation_y = 0;
	rightFrontTranslation_z = 0;
	mh.param(node_name + "/rightFrontRotation_x", rightFrontRotation_x, rightFrontRotation_x);
	mh.param(node_name + "/rightFrontRotation_y", rightFrontRotation_y, rightFrontRotation_y);
	mh.param(node_name + "/rightFrontRotation_z", rightFrontRotation_z, rightFrontRotation_z);
	mh.param(node_name + "/rightFrontRotation_w", rightFrontRotation_w, rightFrontRotation_w);
	mh.param(node_name + "/rightFrontTranslation_x", rightFrontTranslation_x, rightFrontTranslation_x);
	mh.param(node_name + "/rightFrontTranslation_y", rightFrontTranslation_y, rightFrontTranslation_y);
	mh.param(node_name + "/rightFrontTranslation_z", rightFrontTranslation_z, rightFrontTranslation_z);
	rightFrontQuaternion.x() = rightFrontRotation_x;
	rightFrontQuaternion.y() = rightFrontRotation_y;
	rightFrontQuaternion.z() = rightFrontRotation_z;
	rightFrontQuaternion.w() = rightFrontRotation_w;
	rightFrontTranslation << rightFrontTranslation_x, rightFrontTranslation_y, rightFrontTranslation_z;
	
	//	#    后ARS-408毫米波-3 暂无
	backRotation_x = 0;
	backRotation_y = 0;
	backRotation_z = 0;
	backRotation_w = 1;
	backTranslation_x = 0;
	backTranslation_y = 0;
	backTranslation_z = 0;
	mh.param(node_name + "/backRotation_x", backRotation_x, backRotation_x);
	mh.param(node_name + "/backRotation_y", backRotation_y, backRotation_y);
	mh.param(node_name + "/backRotation_z", backRotation_z, backRotation_z);
	mh.param(node_name + "/backRotation_w", backRotation_w, backRotation_w);
	mh.param(node_name + "/backTranslation_x", backTranslation_x, backTranslation_x);
	mh.param(node_name + "/backTranslation_y", backTranslation_y, backTranslation_y);
	mh.param(node_name + "/backTranslation_z", backTranslation_z, backTranslation_z);
	backQuaternion.x() = backRotation_x;
	backQuaternion.y() = backRotation_y;
	backQuaternion.z() = backRotation_z;
	backQuaternion.w() = backRotation_w;
	backTranslation << backTranslation_x, backTranslation_y, backTranslation_z;
	
	//	#    iv.左后SRR-308毫米波-4
	leftBackRotation_x = 0;
	leftBackRotation_y = 0;
	leftBackRotation_z = 0;
	leftBackRotation_w = 1;
	leftBackTranslation_x = 0;
	leftBackTranslation_y = 0;
	leftBackTranslation_z = 0;
	mh.param(node_name + "/leftBackRotation_x", leftBackRotation_x, leftBackRotation_x);
	mh.param(node_name + "/leftBackRotation_y", leftBackRotation_y, leftBackRotation_y);
	mh.param(node_name + "/leftBackRotation_z", leftBackRotation_z, leftBackRotation_z);
	mh.param(node_name + "/leftBackRotation_w", leftBackRotation_w, leftBackRotation_w);
	mh.param(node_name + "/leftBackTranslation_x", leftBackTranslation_x, leftBackTranslation_x);
	mh.param(node_name + "/leftBackTranslation_y", leftBackTranslation_y, leftBackTranslation_y);
	mh.param(node_name + "/leftBackTranslation_z", leftBackTranslation_z, leftBackTranslation_z);
	leftBackQuaternion.x() = leftBackRotation_x;
	leftBackQuaternion.y() = leftBackRotation_y;
	leftBackQuaternion.z() = leftBackRotation_z;
	leftBackQuaternion.w() = leftBackRotation_w;
	leftBackTranslation << leftBackTranslation_x, leftBackTranslation_y, leftBackTranslation_z;
	
	//	#    v.右后SRR-308毫米波-5
	rightBackRotation_x = 0;
	rightBackRotation_y = 0;
	rightBackRotation_z = 0;
	rightBackRotation_w = 1;
	rightBackTranslation_x = 0;
	rightBackTranslation_y = 0;
	rightBackTranslation_z = 0;
	mh.param(node_name + "/rightBackRotation_x", rightBackRotation_x, rightBackRotation_x);
	mh.param(node_name + "/rightBackRotation_y", rightBackRotation_y, rightBackRotation_y);
	mh.param(node_name + "/rightBackRotation_z", rightBackRotation_z, rightBackRotation_z);
	mh.param(node_name + "/rightBackRotation_w", rightBackRotation_w, rightBackRotation_w);
	mh.param(node_name + "/rightBackTranslation_x", rightBackTranslation_x, rightBackTranslation_x);
	mh.param(node_name + "/rightBackTranslation_y", rightBackTranslation_y, rightBackTranslation_y);
	mh.param(node_name + "/rightBackTranslation_z", rightBackTranslation_z, rightBackTranslation_z);
	rightBackQuaternion.x() = rightBackRotation_x;
	rightBackQuaternion.y() = rightBackRotation_y;
	rightBackQuaternion.z() = rightBackRotation_z;
	rightBackQuaternion.w() = rightBackRotation_w;
	rightBackTranslation << rightBackTranslation_x, rightBackTranslation_y, rightBackTranslation_z;
	cout << "sensorradar debug: load radar param success." << endl;
	
}

    /*************************************************
	Function:       ~SensorRadar
	Description:    析构函数
	Input:          无
	Output:         无
	Return:         无
	*************************************************/ 	
SensorRadar::~SensorRadar()
{
	if(radarObjectInfoFile.is_open())
		radarObjectInfoFile.close();
}

   /*************************************************
	Function:       Run
	Description:    主程序
	Input:          无
	Output:         无
	Return:         无
	*************************************************/  
void SensorRadar::Run()
{
	// ros::spin();
	unsigned int frenquence = 20;
	ros::Rate rate(frenquence); //20hz
    while(ros::ok())
	{
        ros::spinOnce();
		double startStamp = ros::Time::now().toSec();
		
		for(int i = 0; i < RADARNUM; ++i)
	    {
		  m_radarcnt[i]++;
		  if(m_radarcnt[i] > DELAYTIME)
		  {
			  radarMsg[i].obs.clear();
		  }
	    }

	    DataProcess();
		
		double endStamp = ros::Time::now().toSec();
		common_msgs::elapsedtime sensorradarElapsedtimeMsg;
		sensorradarElapsedtimeMsg.time = endStamp - startStamp;
		pub_sensorradarElapsedtime.publish(sensorradarElapsedtimeMsg);
		
		static int timeCount = 0;
		if(++timeCount % frenquence == 0){
			// cout << "[sensorradar use time] " << (endStamp - startStamp) * 1000 << " ms." << std::endl;
			timeCount = 0;
		}
        rate.sleep();
    } 
}

	/*************************************************
	Function:		// DataProcess
	Description: 	// 数据处理
	Calls: 			// PubMsg
	Called By: 		// 无
	Table Accessed: // 无
	Table Updated: 	// 无
	Input: 			// 多个毫米波雷达数据	
	Output: 		// 融合后毫米波雷达数据
	Return: 		// 无
	Others: 		// 无
	*************************************************/
void SensorRadar::DataProcess() //顺序执行
{
   // abandon dataProcess  
  int TarNum = 0;
  stTarFusion.NumOfValid = 0;
  //融合雷达数据
  for (int k = 0; k < RADARNUM; ++k)
  {
       if(k == RADAR_FRONT || k == RADAR_BACK)
	   {
		   if(true == radarMsg[k].isvalid)
			{
				for(int i=0;i<radarMsg[k].obs.size();i++)
				{
					float x = radarMsg[k].obs[i].x;
					float y = radarMsg[k].obs[i].y;
					
					stTarFusion.TargetData[TarNum].id = radarMsg[k].obs[i].id;
					stTarFusion.TargetData[TarNum].Distance = radarMsg[k].obs[i].y;//cm radarMsg INS原点右前上
					stTarFusion.TargetData[TarNum].LatPos  = radarMsg[k].obs[i].x;
					stTarFusion.TargetData[TarNum].RelSpd_Long = radarMsg[k].obs[i].relspeedy;//INS原点右前上
					stTarFusion.TargetData[TarNum].RelSpd_Lat = radarMsg[k].obs[i].relspeedx;
					stTarFusion.TargetData[TarNum].width = radarMsg[k].obs[i].width;
					stTarFusion.TargetData[TarNum].length = radarMsg[k].obs[i].length;
					stTarFusion.TargetData[TarNum].classification = radarMsg[k].obs[i].classification;
					stTarFusion.TargetData[TarNum].RadarIndex = k;
					stTarFusion.TargetData[TarNum].ValidLevel = 2;
					TarNum ++;
					//cout<<"k = " << k <<", id = " << static_cast<int>(radarMsg[k].obs[i].id) << ", width = " << radarMsg[k].obs[i].width
					//    << ", length = " << radarMsg[k].obs[i].length << ", classification = " << static_cast<int>(radarMsg[k].obs[i].classification)
					//    << endl;
				}
			}
	   }//end if(i == RADAR_FRONT || i == RADAR_BACK)
	   else
	   {
            if(true == radarMsg[k].isvalid)
			{
				for(int i=0;i<radarMsg[k].obs.size();i++)
				{
					Data_Rec Data_Rec_Temp;
					Data_Rec_Temp.id = radarMsg[k].obs[i].id;
					Data_Rec_Temp.Distance = radarMsg[k].obs[i].y;//INS原点右前上
					Data_Rec_Temp.LatPos  = radarMsg[k].obs[i].x;
					Data_Rec_Temp.RelSpd_Long = radarMsg[k].obs[i].relspeedy;// relspeedy前 ，RelSpd_Long纵向
					Data_Rec_Temp.RelSpd_Lat = radarMsg[k].obs[i].relspeedx;//relspeedx右，RelSpd_Lat横向
					Data_Rec_Temp.classification = radarMsg[k].obs[i].classification;
					Data_Rec_Temp.RadarIndex = k;
					Data_Rec_Temp.ValidLevel = 2;
					//cout<<"k = " << k <<", id = " << static_cast<int>(radarMsg[k].obs[i].id) << ", width = " << radarMsg[k].obs[i].width
					//    << ", length = " << radarMsg[k].obs[i].length << ", classification = " << static_cast<int>(radarMsg[k].obs[i].classification)
					//    << endl;
					int k = 0;
					float deltaDis=0,deltaLatPos=0,deltaRelSpd_long=0,deltaRelSpd_lat=0;
					if (TarNum != 0)
					{
						for (k = 0; k < TarNum; k ++)
						{
							deltaDis = Data_Rec_Temp.Distance - stTarFusion.TargetData[k].Distance;
							deltaLatPos = Data_Rec_Temp.LatPos - stTarFusion.TargetData[k].LatPos;
							deltaRelSpd_long = Data_Rec_Temp.RelSpd_Long - stTarFusion.TargetData[k].RelSpd_Long;
							deltaRelSpd_lat = Data_Rec_Temp.RelSpd_Lat - stTarFusion.TargetData[k].RelSpd_Lat;
							
							if ((abs(deltaDis) < DELTA_DIS) && (abs(deltaLatPos) < DELTA_LAT)
								&& (abs(deltaRelSpd_long) < DELTA_REL) &&
								(stTarFusion.TargetData[k].RadarIndex!= RADAR_FRONT && stTarFusion.TargetData[k].RadarIndex!= RADAR_BACK)//侧向radar间的融合：1-4，2-5，1-2，4-5
								)//判断是否有相似目标 TODO:找到相似目标后找出距离最近的目标（存入队列or记录索引）且左后方用左后方radar，右后方用右后方radar（）
							{
								// TODO:相似目标中前向目标优先选择主雷达，侧向去均值或者侧向求得均值后选择与均值差最小的radar数据 问题：左右都有目标的平均有问题 区分前向后向
								stTarFusion.TargetData[k].Distance  += deltaDis/2;
								stTarFusion.TargetData[k].LatPos += deltaLatPos/2;
								stTarFusion.TargetData[k].RelSpd_Long += deltaRelSpd_long/2;
								stTarFusion.TargetData[k].RelSpd_Lat += deltaRelSpd_lat/2;
								stTarFusion.TargetData[k].ValidLevel += Data_Rec_Temp.ValidLevel;//相近目标，数据取平均值，有效程度取和，目标总数不增。
								//stTarFusion.TargetData[k].RadarIndex = Data_Rec_Temp.RadarIndex;
								break;
							}
							//20221202 侧向radar与前/后radar的融合: 位置小于阈值，使用前/后的融合
							if((abs(deltaDis) < DELTA_DIS) && (abs(deltaLatPos) < DELTA_LAT)
								&& (stTarFusion.TargetData[k].RadarIndex == RADAR_FRONT || stTarFusion.TargetData[k].RadarIndex == RADAR_BACK)//侧向radar与前/后radar的融合：0-1，0-2，3-4，3-5
								){
								break;
							}
						}//end for (k = 0; k < TarNum; k ++)
						if (TarNum == k)//没有相近目标
						{
							stTarFusion.TargetData[TarNum].id = Data_Rec_Temp.id;
							stTarFusion.TargetData[TarNum].Distance = Data_Rec_Temp.Distance;
							stTarFusion.TargetData[TarNum].LatPos = Data_Rec_Temp.LatPos;
							stTarFusion.TargetData[TarNum].RelSpd_Long = Data_Rec_Temp.RelSpd_Long;
							stTarFusion.TargetData[TarNum].RelSpd_Lat = Data_Rec_Temp.RelSpd_Lat;
							stTarFusion.TargetData[TarNum].classification = Data_Rec_Temp.classification;
							stTarFusion.TargetData[TarNum].ValidLevel = Data_Rec_Temp.ValidLevel;
							stTarFusion.TargetData[TarNum].RadarIndex = Data_Rec_Temp.RadarIndex;
							TarNum ++;
						}
					} //end if (TarNum != 0)
					else    //初始时刻没有前后radar数据时，得到的数据直接加入当前帧融合数据stTarFusion中
					{
						stTarFusion.TargetData[TarNum].id = Data_Rec_Temp.id;
						stTarFusion.TargetData[TarNum].Distance = Data_Rec_Temp.Distance;
						stTarFusion.TargetData[TarNum].LatPos = Data_Rec_Temp.LatPos;
						stTarFusion.TargetData[TarNum].RelSpd_Long = Data_Rec_Temp.RelSpd_Long;
						stTarFusion.TargetData[TarNum].RelSpd_Lat = Data_Rec_Temp.RelSpd_Lat;
						stTarFusion.TargetData[TarNum].classification = Data_Rec_Temp.classification;
						stTarFusion.TargetData[TarNum].ValidLevel = Data_Rec_Temp.ValidLevel;
						stTarFusion.TargetData[TarNum].RadarIndex = Data_Rec_Temp.RadarIndex;
						TarNum ++;
					}
				}//end for(int i=0;i<radarMsg[k].obs.size();i++)
			}//end if(true == radarMsg[k].isvalid)
	   }//end  else
  }//end for (int k = 0; k < RADARNUM; ++k)
  stTarFusion.NumOfValid = TarNum;
 //publish
  PubMsg();	
}

	/*************************************************
	Function:		// PubMsg
	Description: 	// the Function of Publishing radar Data
	Calls: 			// 无
	Called By: 		// 无
	Table Accessed: // 无
	Table Updated: 	// 无
	Input: 			// 融合后毫米波雷达数据	
	Output: 		// 无
	Return: 		// 无
	Others: 		// 无
	*************************************************/
void SensorRadar::PubMsg()
{
	//cout<<"start PubMsg.......\n";
   //show
   	pcl::PointCloud<pcl::PointXYZI> temp_cloud;
	pcl::PointXYZI  pointsingle;
	PointCloud::Ptr msg2 (new PointCloud);

	common_msgs::sensorobjects msg;
	msg.isvalid = 1;

	if(stTarFusion.NumOfValid <=0)
	{
		return;
	}

	for(int i = 0; i < stTarFusion.NumOfValid; ++i)
	{
		common_msgs::sensorobject ob;
		memset(&ob,0,sizeof(ob));
		float x = stTarFusion.TargetData[i].LatPos;       //m // INS原点右前上
		float y = stTarFusion.TargetData[i].Distance;     //m
        int flag = stTarFusion.TargetData[i].RadarIndex;
		ob.id = stTarFusion.TargetData[i].id;
		ob.x = x;
		ob.y = y; //no
		ob.relspeedy = stTarFusion.TargetData[i].RelSpd_Long;   //m/s // INS原点右前上
		ob.relspeedx = stTarFusion.TargetData[i].RelSpd_Lat;
		ob.width = stTarFusion.TargetData[i].width;
		ob.length = stTarFusion.TargetData[i].length;
		//cout <<"id = " << stTarFusion.TargetData[i].id << ", width = " << stTarFusion.TargetData[i].width
		//    << ", length = " << stTarFusion.TargetData[i].length << ", classification = " << static_cast<int>(stTarFusion.TargetData[i].classification)
		//    << endl;
		
		ob.classification = static_cast<uint8_t>(stTarFusion.TargetData[i].classification);//改成radar的分类stoi(string(1, stTarFusion.TargetData[i].classification))
		ob.radarIndex = stTarFusion.TargetData[i].RadarIndex;//存放radar索引
		ob.radarObjectID = stTarFusion.TargetData[i].id;//存放radar索引
		
		int radarObjectID = static_cast<int>(ob.radarObjectID);
		//cout << "debug sensorradar: radarObjectID = " << radarObjectID <<", raw ID = "<<  stTarFusion.TargetData[i].id
		//		<<", i = "<<  i << ", stTarFusion.NumOfValid =  " << stTarFusion.NumOfValid
		//		<<", x = "<<  x << ", y =  " << y
		//		<<", ob.width = "<<  ob.width << ", ob.length =  " << ob.length
		//		<<", ob.relspeedx = "<<  ob.relspeedx << ", ob.relspeedy =  " << ob.relspeedy
		//		<< endl;

		ob.value = COMMON::SensorType::RADAR; //raw = 1
		msg.obs.push_back(ob);
		if(bdebug == 1)
		{
			if(!isRadarPosionSpeedVerify){
				//不调试radar位置和速度
				pointsingle.x = y;//INS原点前左上 20221202
				pointsingle.y = -x;
			}
			else{//调试：radar点云与驱动坐标系相同
				pointsingle.x = x;//INS原点前左上 TODO 需要验证
				pointsingle.y = y;
			}

			pointsingle.intensity = flag *20;///120 ob.radarIndex

			if(pointsingle.intensity > 255)
			{
				pointsingle.intensity = 255;
			}
			temp_cloud.points.push_back(pointsingle);
		}
	}
	msg.timestamp = ros::Time::now().toSec()*1000;
    pub_radar.publish(msg);//"sensorradar" INS原点右前上
	sensorobjectsBoxShow(msg); // "/sensorradar/sensorradarMarkerArrayInfo" 在rviz显示sensorradar目标（5个radar数据）的信息

    if(bdebug == 1)
	{
		msg2->header.frame_id = "car";
		msg2->height = 1;
		msg2->width = temp_cloud.points.size();
		msg2->points = temp_cloud.points;
		pub_show.publish (msg2); //"radar_cloud" INS原点前左上
	}
	//cout << "debug sensorradar: PubMsg finished." << endl;
}

/***20221130 发布
 * 20221124 显示自车和目标速度信息的marker:前左上坐标
 * @param trackNum 跟踪目标号
 * @param position_x 横坐标前左上
 * @param position_y 纵坐标前左上
 * @param position_z Z
 * @param id 目标ID
 * @param relVelocity_x  目标X轴速度,，非横向速度
 * @param relVelocity_y 目标Y轴速度,，非纵向速度
 * @param classification 目标分类
 * @param frameID marker的frameID
 * @param objectfusionMarkerArray 输出的目标MarkerArray
 */
void SensorRadar::showObjectMarker(const int& trackNum, const radar_msgs::radarobject& singleObject,
                                   const std::string& frameID,
                                   visualization_msgs::MarkerArray& objectfusionMarkerArray
){
	///添加目标信息:ID classification vx vy
	visualization_msgs::Marker* marker = new visualization_msgs::Marker;
	marker->header.frame_id =  frameID;
	//marker->header.stamp = ros::Time(curTime);//20220906
	marker->header.stamp = ros::Time::now();
	marker->ns = "basic_shapes";
	marker->action = visualization_msgs::Marker::ADD;
	//    marker.pose.orientation.w = 1.0;
	marker->id = trackNum;
	marker->type = visualization_msgs::Marker::TEXT_VIEW_FACING;
	marker->scale.z = 0.5;
	marker->color.b = 0;
	marker->color.g = 0;
	marker->color.r = 50;
	marker->color.a = 10;
	geometry_msgs::Pose pose;
	pose.position.x = singleObject.y; // carBackRFU2carBackFLU
	pose.position.y = -singleObject.x;
	pose.position.z = 0;    // radar无高度
	
	// 显示：id
	// 类别-速度来源-运动信息
	// 横向速度-纵向速度
	marker->text = "radarID:" +
			 std::to_string(singleObject.id)
             + "\nX:" + std::to_string(singleObject.x) + "\nY:" + std::to_string(singleObject.y)
			// + std::to_string(classification) + "-"
			 + "\nrelVx:" +	std::to_string(singleObject.relspeedy) + "\nrelVy:" + std::to_string(-singleObject.relspeedx)// carBackRFU2carBackFLU
			+ "\nDynProp:" + std::to_string(singleObject.DynProp) + "\nRCS:" + std::to_string(singleObject.RCS)
			+ "\nMeasState:" + std::to_string(singleObject.MeasState)
            + "\nProbOfExist:" + std::to_string(singleObject.ProbOfExist) + "\nArelLong:" + std::to_string(singleObject.ArelLong)
			+ "\nArelLat:" + std::to_string(singleObject.ArelLat) + "\nOrientationAngel:" + std::to_string(singleObject.OrientationAngel)
			;
	
	marker->pose = pose;
	marker->lifetime = ros::Duration();//20220826去除残留 1 ros::Duration()意味着从不自动删除。
	
	objectfusionMarkerArray.markers.push_back(*marker);
	delete marker;
}


/***
 * 可视化radar结果msg,可视化检测结果，用于调试
 * @param msg_source radar目标
 */
void SensorRadar::sensorobjectsBoxShow(const common_msgs::sensorobjects &msg_source){
	std::string frameIDInfo = "car";
	
	visualization_msgs::MarkerArray markerArray_detect;
	
	visualization_msgs::Marker marker;
	marker.action=visualization_msgs::Marker::DELETEALL;
	markerArray_detect.markers.emplace_back(marker);
	
	
	for (int i=0; i<msg_source.obs.size(); i++){
		vector<float> color = {0.5, 0, 0};
		if(int(msg_source.obs[i].classification) < class_color.size()){
			color = class_color[int(msg_source.obs[i].classification)];
		}
		
		visualization_msgs::Marker text;
		double stamp = msg_source.timestamp / 1000.0;
		text.header.frame_id  = frameIDInfo;
		//text.header.stamp = ros::Time::now();
		text.header.stamp = ros::Time().fromSec(stamp);//TODO  lidar时间戳 //ros::Time::now()
		text.ns = "points_and_lines";
		text.action = visualization_msgs::Marker::ADD;
		text.lifetime =ros::Duration();//0.1
		text.pose.orientation.w = 1.0;
		text.pose.position.x = msg_source.obs[i].y; // carBackRFU2CarBackFLU
		text.pose.position.y = -msg_source.obs[i].x;
		text.pose.position.z = 0;
		text.id = i;
		text.type = visualization_msgs::Marker::TEXT_VIEW_FACING;
		//line width
		//text.scale.x = 0.1;
		//text.scale.y = 0.1;
		text.scale.z = 0.5;
		//color green
		text.color.a = 1;//透明度
		text.color.r = 1;
		text.color.g = 1;//TODO 框为绿色
		text.color.b = 1;
		
		std::string vxString = std::to_string(msg_source.obs[i].relspeedy);// carBackRFU2CarBackFLU
		std::string vyString = std::to_string(-msg_source.obs[i].relspeedx);
		
		// 显示：id
		// 类别-速度来源-运动信息
		// 横向速度-纵向速度
		text.text = "radarID:" +
				std::to_string(msg_source.obs[i].radarIndex) +"\nobjectID:" +
				std::to_string(msg_source.obs[i].radarObjectID) +"\n"
				+ "X:" + std::to_string(msg_source.obs[i].y) + "\nY:" + std::to_string(-msg_source.obs[i].x)// carBackRFU2CarBackFLU
				// + std::to_string(classification) + "-" + speedSource
				+ "\nrelVx:" +	vxString + "\nrelVy:" + vyString;//
				
		markerArray_detect.markers.push_back(text);
		
		/// 保存radar障碍物信息到CSV
		if(m_isSaveObjectInfoCSV){
			// timestamp,radarObjectID,classfication,X-m,Y-m,Z-m,speedX-m/s,speedY-m/s,speed-m/s,"
			//							   "yaw-RFU-YP-clockwise360,length-m,width-m,"
			//		                       "height-m,infoSource(radarid)
			
			//int labelClass = transformDetectionClass2LabelClass(curFrameTracker.label);
			//cout<<"detection class: " << class_name[curFrameTracker.label] <<", label class = " << labelClass << endl;
			radarObjectInfoFile << std::setprecision(12) << msg_source.timestamp / 1000.0 << ","
					<< std::setprecision(6)
					<< std::to_string(static_cast<int>(msg_source.obs[i].radarObjectID)) << ","
			         << std::to_string(static_cast<int>(msg_source.obs[i].classification)) << ","  // 可能不准
			         << msg_source.obs[i].y << "," << -msg_source.obs[i].x << "," // carBackRFU2CarBackFLU
			         << msg_source.obs[i].z << "," // 可能不准
					<< msg_source.obs[i].relspeedy << "," << -msg_source.obs[i].relspeedx << ","// carBackRFU2CarBackFLU
			         << sqrt(pow(msg_source.obs[i].relspeedx, 2) + pow(msg_source.obs[i].relspeedy, 2)) << ","
			         << msg_source.obs[i].azimuth * 180 / M_PI << "," // 顺时针 // 可能不准
			         << msg_source.obs[i].length << "," << msg_source.obs[i].width << ","
			         << msg_source.obs[i].height << ","
			         << std::to_string(static_cast<int>(msg_source.obs[i].radarIndex))
			         << endl;
		}
		
		
	}
	
	pub_sensorradarMarkerArrayInfo.publish(markerArray_detect); // "/sensorradar/sensorradarMarkerArrayInfo" 用于显示radar信息
	markerArray_detect.markers.clear();
}


//20221130 发布
void SensorRadar::showObjectInfo(const radar_msgs::sensorradar& radarObjects, 
								  const std::string& frameID
								  ){
	visualization_msgs::MarkerArray sensorradarMarkerArray; //20221124
	//visualization_msgs::Marker marker;
	//marker.action=visualization_msgs::Marker::DELETEALL;
	//sensorradarMarkerArray.markers.emplace_back(marker);
	
	for (size_t i = 0; i < radarObjects.obs.size(); i++)
	{
		radar_msgs::radarobject singleObject = radarObjects.obs[i];
		///添加目标速度信息 坐标轴速度,前左上速度
		showObjectMarker(i, singleObject, frameID, sensorradarMarkerArray);
	}
	
	//20221130 发布
	sensorradarMarkerArrayPub.publish(sensorradarMarkerArray);   // 发布objectfusionMarkerArray
	
}



/*** object.DistLong 和 object.VelocityLong:radar X 轴
 * object.DistLat 和 object.VelocityLat：radar Y 轴
 * 20221110 ars40x_srr308_RadarObjects类型的radar话题转成sensorradar::sensorradar类型
 * @param ars40x_srr308_RadarObjects radar的ROS驱动发出话题
 * @param radarIndex radar的索引
 * @return radar_msgs::sensorradar类型radar数据
 */
radar_msgs::sensorradar SensorRadar::radarTypeTrans(const ars40x_srr308_msgs::ObjectList& ars40x_srr308_RadarObjects,
										const uint8_t& radarIndex){
	//std::string msgType = abi::__cxa_demangle(typeid(ars40x_srr308_RadarObjects).name(), NULL, NULL, NULL);
	//cout<<"msg type: "<<msgType<<endl;
	//std::string pattern = "_";
	//std::string msgInfo = strtok((char*)msgType.c_str(), pattern.c_str());
	//const char* pointer = msgType.c_str();
	//cout<<msgInfo<<endl;
	//TODO 解析的radar数据还有很多信息，如目标航向角
	radar_msgs::sensorradar outputRadarObjects;
	outputRadarObjects.isvalid = 1;
	for (const auto& object: ars40x_srr308_RadarObjects.data) {
		//cout<<"msg type2: "<<abi::__cxa_demangle(typeid(object).name(), NULL, NULL, NULL)<<endl;
		
		radar_msgs::radarobject radarObject;
		radarObject.id = object.ID;
		radarObject.x = object.DistLong; //纵坐标（X轴坐标），m
		radarObject.y = object.DistLat;//横坐标（Y轴坐标）,m
		radarObject.relspeedx = object.VrelLong;//纵向（X轴方向）相对速度，右值单位m/s 坐标系:前左上
		radarObject.relspeedy = object.VrelLat;//横向（X轴方向）相对速度，右值单位m/s 坐标系:前左上
		radarObject.length = object.Length;
		radarObject.width = object.Width;
		radarObject.classification = object.Class; 
		radarObject.flag = radarIndex;
		radarObject.DynProp = object.DynProp;
		radarObject.RCS = object.RCS;
		radarObject.MeasState = object.MeasState;
		radarObject.ProbOfExist = object.ProbOfExist;
		radarObject.ArelLong = object.ArelLong;
		radarObject.ArelLat = object.ArelLat;
		radarObject.OrientationAngel = object.OrientationAngel;
		outputRadarObjects.obs.emplace_back(std::move(radarObject));
	}
	return std::move(outputRadarObjects);
}

inline double SensorRadar::DegToRad(double deg)//inline
{
	return (deg / 180.0 * M_PI);
}

/***
 * 20221111 点云坐标转换
 * @param inputposition 输入原坐标系下三维点坐标
 * @param quaternion 旋转四元数
 * @param translation 平移向量（米）
 * @param outputPosition 输出坐标系下点坐标
 */
void SensorRadar::transformPoint(const Eigen::Vector3f& inputposition,const Eigen::Quaternion<float>& quaternion,
                                 const Eigen::Vector3f& translation,Eigen::Vector3f& outputPosition){
	Eigen::Affine3f transform = Eigen::Affine3f::Identity();
	transform.translation() << translation[0],translation[1],translation[2];
	transform.rotate(quaternion);
	
	outputPosition(0) = transform(0,0) * inputposition(0) + transform(0,1) * inputposition(1) + transform(0,2) * inputposition(2) +transform(0,3);
	outputPosition(1) = transform(1,0) * inputposition(0) + transform(1,1) * inputposition(1) + transform(1,2) * inputposition(2) + transform(1,3);
	outputPosition(2) = transform(2,0) * inputposition(0) + transform(2,1) * inputposition(1) + transform(2,2) * inputposition(2) + transform(2,3);
}

/***
 * 20221129 速度坐标转换
 * @param inputposition 输入原坐标系下速度
 * @param quaternion 旋转四元数
 * @param translation 平移向量（米）
 * @param outputPosition 输出坐标系下点速度
 */
void SensorRadar::transformSpeed(const Eigen::Vector3f& inputposition,const Eigen::Quaternion<float>& quaternion,
                                 const Eigen::Vector3f& translation,Eigen::Vector3f& outputPosition){
	Eigen::Affine3f transform = Eigen::Affine3f::Identity();
	transform.translation() << translation[0],translation[1],translation[2];
	Eigen::Matrix3f speedMatrix(quaternion);
	// cout<<"speedMatrix:\n"<<speedMatrix.eulerAngles(0,1,2).transpose()<<endl;
	// cout<<"speedMatrix inverse:\n"<<speedMatrix.inverse().eulerAngles(0,1,2).transpose()<<endl;

	transform.rotate(speedMatrix);
	
	outputPosition(0) = transform(0,0) * inputposition(0) + transform(0,1) * inputposition(1) + transform(0,2) * inputposition(2);
	outputPosition(1) = transform(1,0) * inputposition(0) + transform(1,1) * inputposition(1) + transform(1,2) * inputposition(2);
	outputPosition(2) = transform(2,0) * inputposition(0) + transform(2,1) * inputposition(1) + transform(2,2) * inputposition(2);
}


/***
 * 20221111 radar点转换到ins坐标系
 * @param radarObjects
 */
void SensorRadar::transformRadarPoint(radar_msgs::sensorradar& radarObjects, const uint8_t& radarIndex){

	Eigen::Quaternion<float> quaternion{};
	Eigen::Vector3f translation;
	
	if(radarIndex == 0){
		quaternion = frontQuaternion;
		translation = frontTranslation;
	}
	else if(radarIndex == 1){
		quaternion = leftFrontQuaternion;
		translation = leftFrontTranslation;
	}
	else if(radarIndex == 2){
		quaternion = rightFrontQuaternion;
		translation = rightFrontTranslation;
	}
	else if(radarIndex == 3){//未使用
		quaternion = backQuaternion;
		translation = backTranslation;
	}
	else if(radarIndex == 4){
		quaternion = leftBackQuaternion;
		translation = leftBackTranslation;
	}
	else if(radarIndex == 5){
		quaternion = rightBackQuaternion;
		translation = rightBackTranslation;
	}
	else{
		cerr<<"Error:   radar index cannot find radar-INS calibration matrix\n";
	}
	
	for(auto& radarObject : radarObjects.obs){
		Eigen::Vector3f inputPosition{radarObject.x, radarObject.y, 0};//front-radar是前左上
		Eigen::Vector3f outputPosition;
		transformPoint(inputPosition, quaternion, translation, outputPosition);
		radarObject.x = outputPosition[0];//INS右前上
		radarObject.y = outputPosition[1];
		
		Eigen::Vector3f inputSpeed{radarObject.relspeedx, radarObject.relspeedy, 0};//front-radar前左上
		Eigen::Vector3f outputSpeed;
		transformSpeed(inputSpeed, quaternion, translation, outputSpeed);
		radarObject.relspeedx = outputSpeed[0];//INS右前上
		radarObject.relspeedy = outputSpeed[1];

	}
}

/***
 * 20221111 前向lidar回调函数
 * @param msg
 */
 void SensorRadar::SubCallback_radarfront(const ars40x_srr308_msgs::ObjectList &msg)
{
	//  cout << "car 2 \n";
	 m_radarcnt[RADAR_FRONT] = 0;
	 radarMsg[RADAR_FRONT].obs.clear();

	// 数据类型转换
	radar_msgs::sensorradar radarObjects = radarTypeTrans(msg, 0);//位置、速度是radar传感器原点前左上
	if(!isRadarPosionSpeedVerify){
		// 位置、速度转到INS坐标系-右前上
		transformRadarPoint(radarObjects, 0);
	}
	showObjectInfo(radarObjects,"car");//20221130
	radarMsg[RADAR_FRONT] = radarObjects;
	 
    front_radar_status.state = 1;
    front_radar_status.timestamp = ros::Time::now().toSec()*1000;
    pub_frontradarstatus.publish(front_radar_status);
	//DataProcess();
}

void SensorRadar::SubCallback_radarfrontCar1(const radar_msgs::sensorradar::ConstPtr &msg)
{
	// cout << "car 1 front\n";
	m_radarcnt[RADAR_FRONT] = 0;
	radarMsg[RADAR_FRONT].obs.clear();
	radarMsg[RADAR_FRONT]= *msg;
	front_radar_status.state = 1;
	front_radar_status.timestamp = ros::Time::now().toSec()*1000;
	pub_frontradarstatus.publish(front_radar_status);
}


/***
 * 20221111 左前向lidar回调函数
 * @param msg
 */
void SensorRadar::SubCallback_radarleftfront(const ars40x_srr308_msgs::ObjectList &msg)
{
	m_radarcnt[RADAR_LEFTFRONT] = 0;
	radarMsg[RADAR_LEFTFRONT].obs.clear();
	// 数据类型转换
	radar_msgs::sensorradar radarObjects = radarTypeTrans(msg, 1);
	if(!isRadarPosionSpeedVerify){
		// 不做验证，即对radar进行旋转，转到INS坐标系-右前上
		transformRadarPoint(radarObjects, 1);
	}
	showObjectInfo(radarObjects,"car");//20221130
	radarMsg[RADAR_LEFTFRONT] = radarObjects;
	leftfront_radar_status.state = 1;
	leftfront_radar_status.timestamp = ros::Time::now().toSec()*1000;
	pub_leftfrontradarstatus.publish(leftfront_radar_status);
}
void SensorRadar::SubCallback_radarleftfrontCar1(const radar_msgs::sensorradar::ConstPtr &msg)
{
	m_radarcnt[RADAR_LEFTFRONT] = 0;
	radarMsg[RADAR_LEFTFRONT].obs.clear();
	radarMsg[RADAR_LEFTFRONT]= *msg;
	leftfront_radar_status.state = 1;
	leftfront_radar_status.timestamp = ros::Time::now().toSec()*1000;
	pub_leftfrontradarstatus.publish(leftfront_radar_status);
}

/***
 * 20221111 右前向lidar回调函数
 * @param msg
 */
void SensorRadar::SubCallback_radarrightfront(const ars40x_srr308_msgs::ObjectList &msg)
{
	m_radarcnt[RADAR_RIGHTFRONT] = 0;
	radarMsg[RADAR_RIGHTFRONT].obs.clear();
	// 数据类型转换
	radar_msgs::sensorradar radarObjects = radarTypeTrans(msg, 2);
	if(!isRadarPosionSpeedVerify){
		// 不做验证，即对radar进行旋转,转到INS坐标系-右前上
		transformRadarPoint(radarObjects, 2);
	}
	showObjectInfo(radarObjects,"car");//20221130
	radarMsg[RADAR_RIGHTFRONT] = radarObjects;
	rightfront_radar_status.state = 1;
	rightfront_radar_status.timestamp = ros::Time::now().toSec()*1000;
	pub_rightfrontradarstatus.publish(rightfront_radar_status);
}
void SensorRadar::SubCallback_radarrightfrontCar1(const radar_msgs::sensorradar::ConstPtr &msg)
{
	m_radarcnt[RADAR_RIGHTFRONT] = 0;
	radarMsg[RADAR_RIGHTFRONT].obs.clear();
	radarMsg[RADAR_RIGHTFRONT]= *msg;
	rightfront_radar_status.state = 1;
	rightfront_radar_status.timestamp = ros::Time::now().toSec()*1000;
	pub_rightfrontradarstatus.publish(rightfront_radar_status);
}


/***
 * 20221111 后向lidar回调函数
 * @param msg
 */
void SensorRadar::SubCallback_radarback(const ars40x_srr308_msgs::ObjectList &msg)
{
	m_radarcnt[RADAR_BACK] = 0;
	radarMsg[RADAR_BACK].obs.clear();
	// 数据类型转换
	radar_msgs::sensorradar radarObjects = radarTypeTrans(msg, 3);
	if(!isRadarPosionSpeedVerify){
		// 不做验证，即对radar进行旋转,转到INS坐标系-右前上
		transformRadarPoint(radarObjects, 3);
	}
	showObjectInfo(radarObjects,"car");//20221130
	radarMsg[RADAR_BACK] = radarObjects;
	back_radar_status.state = 1;
	back_radar_status.timestamp = ros::Time::now().toSec()*1000;
	pub_backradarstatus.publish(back_radar_status);
}
void SensorRadar::SubCallback_radarbackCar1(const radar_msgs::sensorradar::ConstPtr &msg)
{
	m_radarcnt[RADAR_BACK] = 0;
	radarMsg[RADAR_BACK].obs.clear();
	radarMsg[RADAR_BACK]= *msg;
	back_radar_status.state = 1;
	back_radar_status.timestamp = ros::Time::now().toSec()*1000;
	pub_backradarstatus.publish(back_radar_status);
}

/***
 * 20221111 左后向lidar回调函数
 * @param msg
 */
void SensorRadar::SubCallback_radarleftback(const ars40x_srr308_msgs::ObjectList &msg)
{
	m_radarcnt[RADAR_LEFTBACK] = 0;
	radarMsg[RADAR_LEFTBACK].obs.clear();
	// 数据类型转换
	radar_msgs::sensorradar radarObjects = radarTypeTrans(msg, 4);
	if(!isRadarPosionSpeedVerify){
		// 不做验证，即对radar进行旋转,转到INS坐标系-右前上
		transformRadarPoint(radarObjects, 4);
	}
	showObjectInfo(radarObjects,"car");//20221130
	radarMsg[RADAR_LEFTBACK] = radarObjects;
	leftback_radar_status.state = 1;
	leftback_radar_status.timestamp = ros::Time::now().toSec()*1000;
	pub_leftbackradarstatus.publish(leftback_radar_status);
}
void SensorRadar::SubCallback_radarleftbackCar1(const radar_msgs::sensorradar::ConstPtr &msg)
{
	// cout << "car 1 left back\n";
	m_radarcnt[RADAR_LEFTBACK] = 0;
	radarMsg[RADAR_LEFTBACK].obs.clear();
	radarMsg[RADAR_LEFTBACK]= *msg;
	leftback_radar_status.state = 1;
	leftback_radar_status.timestamp = ros::Time::now().toSec()*1000;
	pub_leftbackradarstatus.publish(leftback_radar_status);
}

/***
 * 20221111 右后向lidar回调函数
 * @param msg
 */
void SensorRadar::SubCallback_radarrightback(const ars40x_srr308_msgs::ObjectList &msg)
{
	m_radarcnt[RADAR_RIGHTBACK] = 0;
	radarMsg[RADAR_RIGHTBACK].obs.clear();
	// 数据类型转换
	radar_msgs::sensorradar radarObjects = radarTypeTrans(msg, 5);
	if(!isRadarPosionSpeedVerify){
		// 不做验证，即对radar进行旋转,转到INS坐标系-右前上
		transformRadarPoint(radarObjects, 5);
	}
	showObjectInfo(radarObjects,"car");//20221130
	radarMsg[RADAR_RIGHTBACK] = radarObjects;
	rightback_radar_status.state = 1;
	rightback_radar_status.timestamp = ros::Time::now().toSec()*1000;
	pub_rightbackradarstatus.publish(rightback_radar_status);
}

void SensorRadar::SubCallback_radarrightbackCar1(const radar_msgs::sensorradar::ConstPtr &msg)
{
	// cout << "car 1 right back\n";
	m_radarcnt[RADAR_RIGHTBACK] = 0;
	radarMsg[RADAR_RIGHTBACK].obs.clear();
	radarMsg[RADAR_RIGHTBACK]= *msg;
	rightback_radar_status.state = 1;
	rightback_radar_status.timestamp = ros::Time::now().toSec()*1000;
	pub_rightbackradarstatus.publish(rightback_radar_status);
}