

/**************************************************************************
Copyright: 	2021-2023 VANJEE Technology.
File name: 	gpsparse.h
Description: 组合导航解析程序。	
Author: 	zhuxuekui
Version: 	V1.0
Date: 		2022.6.3
History: 	无
**************************************************************************/


#ifndef NODE_EXAMPLE_TALKER_H
#define NODE_EXAMPLE_TALKER_H

// ROS includes.
#include "ros/ros.h"
#include "ros/time.h"
#include "std_msgs/Float64MultiArray.h"
#include <geometry_msgs/PoseStamped.h>
#include <tf/transform_broadcaster.h>
#include <std_srvs/Empty.h>
#include <sensor_msgs/Imu.h>

//lib
#include "common_msgs/sensorgps.h"
#include "common_msgs/actuator.h"
#include "common_msgs/collectmap.h"
#include "common_msgs/requestmap.h"
#include "common_msgs/collectpoint.h"
#include "common_msgs/lonlat.h"
#include "common_msgs/lonlatmappoints.h"
#include "common_msgs/sensorstatus.h"


#include "../../../commonlibrary/src/common.h"

#include "std_msgs/String.h"
#include <time.h>

#include "yaml-cpp/yaml.h"
#include <boost/lexical_cast.hpp>

// c  header
#include <stdlib.h>
#include <pwd.h>
#include <stdio.h>
#include"bca.h"
#include"Boost_UDP.h"
#include <mutex>

#include <Eigen/Core>
#include <Eigen/Eigen>

#include <iostream>
#include <fstream>
#include <dirent.h>
#include <sys/stat.h>
#include <sys/types.h>

#include "../src/yamltool/yaml_config_writer.h"
#include "../src/yamltool/yaml_config_reader.h"

//20221110
#include "novatel_oem7_msgs/INSPVA.h"
#include "novatel_oem7_msgs/Oem7Header.h"
#include "novatel_oem7_msgs/InertialSolutionStatus.h"
#include "novatel_oem7_msgs/INSPVAX.h"
#include "novatel_oem7_msgs/PositionOrVelocityType.h"
#include "novatel_oem7_msgs/CORRIMU.h"
#include "sensor_msgs/Imu.h"
/* FOREGROUND */

#define RST  "\x1B[0m"
#define KRED  "\x1B[31m"
#define KGRN  "\x1B[32m"
#define KYEL  "\x1B[33m"
#define KBLU  "\x1B[34m"
#define KMAG  "\x1B[35m"
#define KCYN  "\x1B[36m"
#define KWHT  "\x1B[37m"
#define FRED(x) KRED x RST
#define FGRN(x) KGRN x RST
#define FYEL(x) KYEL x RST
#define FBLU(x) KBLU x RST
#define FMAG(x) KMAG x RST
#define FCYN(x) KCYN x RST
#define FWHT(x) KWHT x RST
#define BOLD(x) "\x1B[1m" x RST
#define UNDL(x) "\x1B[4m" x RST

#define DPCAR 0
#define VREP 1
#define DEBUGHEAD "[sensorgps-->]"
#define M_PI 3.14159265358979323846
using namespace std;


#define MAXSIZE 21
#define SUBTOPIC_COLLECTMAP    "collectmap"
#define SUBTOPIC_REQUESTMAP    "requestmap"
#define SUBTOPIC_COLLECTPOINT  "collectpoint"
#define PUBTOPIC_LONLATMAP     "lonlatmappoints"

typedef struct Point2D
{
	double x;
	double y;
	double lat;
	double lon;
}Point2D;

//动态获取的参数，参加采集地图模块
typedef struct sAddAttr
{
  // int posattr;
  int roadattr;
  int laneattr;
  int speed;
  int mergelanetype;
  int sensorlanetype;
  double sideroadwidth;
  double egolanewidth;
  double leftlanewidth;
  double rightlanewidth;
  double leftsearchdis;
  double rightsearchdis;
  
  unsigned int laneswitch; // 换道标志位
  unsigned int sidepass; // 借道标志位
  unsigned int lanenum; // 车道总数
  unsigned int lanesite; // 所在第几车道
}sAddAttr;

//gps解析得到的数据
typedef struct sGpsPoint
{
  double lon;
  double lat;
  double alt;
  unsigned char roadtype;
  unsigned char lanetype;
  double heading;//差分航向
  double pitch;
  double roll;
  double yaw;    //运动航向
  double pitchrate;
  double rollrate;
  double yawrate;  
  double accx;
  double accy;
  double accz;
  double velocity;//速度
  double mile;//里程
  unsigned int status; //归一化为 0，1，4，5 状态
  unsigned int rawstatus;//真实的状态值
  unsigned int satenum;//卫星数目
  long gpstime; //gps时间
  long gpsweek; //gps week
  double speedN; // X轴速度 20220926
  double speedE; // Y轴速度 20220926
  double speedD; // Z轴速度 20220926
  long timestamp;//20221110
  int isvalid; //20221208
}sGpsPoint;


//以下为联合体，主要用于数据解析
typedef union CHAR2UINT
{
	unsigned int i;
	unsigned char ch[4];
}CHAR2UINT;

 typedef union Char2Uint
 {
   unsigned short us;
   char ch[2];
 }Char2Uint;

 typedef union Char2Uchar
 {
   unsigned char uc;
   char ch;
 }Char2Uchar;

 typedef union Char2Int
 {
   int i;
   char ch[4];
 }Char2Int;

 typedef union Char2Ushort
 {
   unsigned short sh;
   unsigned char ch[2];
 }Char2Ushort;

 typedef union Char2Short
 {
   short sh;
   unsigned char ch[2];
 }Char2Short;


 //从CanetUdp那得到的CAN帧
typedef struct _CANMsg
{
  unsigned char head;//message头文件
	unsigned ID;//ID of message
	unsigned char data[8];//Data of message
} stCANMsg;//CAN报文定义

class gpsparse
{
  public:
  /*************************************************
	Function:       gpsparse
	Description:    构造函数，主要是参数初始化
	Input:          nh: 句柄
	Output:         无
	Return:         无
	*************************************************/ 
  gpsparse(ros::NodeHandle nh);
  /*************************************************
	Function:       ~gpsparse
	Description:    析构函数
	Input:          无
	Output:         无
	Return:         无
	*************************************************/   
  ~gpsparse();

  /*************************************************
	Function:       run
	Description:    主程序。
	Input:          无
	Output:         无
	Return:         无
	*************************************************/    
  void run(); 

  private:
  /*************************************************
	Function:       publishMsg
	Description:    发布组合导航数据
	Input:          data:解析后得到的组合导航数据
	Output:         无
	Return:         无
	*************************************************/ 
  void publishMsg(sGpsPoint &data);

  /*************************************************
    Function:       ParseDataInspva
    Description:    解析串口数据。
    Input:          chr: 单个字符。
    Output:         无
    Return:         无
    *************************************************/
  void ParseDataInspva();

  /*************************************************
	Function:       getDist2
	Description:    得到两个点之间的距离
	Input:          p1: p1点， p2: p2点
	Output:         无
	Return:         距离
	*************************************************/   
  double getDist2(Point2D p1, Point2D p2);

  /*************************************************
	Function:       GetL0InDegree
	Description:    得到时区所在的纬度。
	Input:          dLIn:纬度
	Output:         无
	Return:         纬度
	*************************************************/   
  double GetL0InDegree(double dLIn);

  /*************************************************
	Function:       BLH2XYZ
	Description:    wgs84坐标通过UTM投影转成大地坐标
	Input:          B:经度， L:纬度， H:航向
	Output:         无
	Return:         转换后的大地坐标
	*************************************************/   
  Point2D BLH2XYZ(double B, double L, double H);
  
  
  /*************************************************
	Function:       ConvertDisToLngLat
	Description:    经纬度转换。
	Input:          dis:距离 ，lng:经度， lat:纬度， angle:航向
	Output:         无
	Return:         转换后的经纬度
	*************************************************/ 
  Point2D ConvertDisToLngLat(double dis,double lng,double lat,double angle);

  /*************************************************
	Function:       ParseData
	Description:    解析串口数据。
	Input:          chr: 单个字符。
	Output:         无
	Return:         无
	*************************************************/  
  void ParseData(unsigned char chr);

  /*************************************************
	Function:       recv_data
	Description:    接收串口数据。
	Input:          fd: 句柄，recv_buffer: 接收的字符数组，length: 接收的字符个数。
	Output:         无
	Return:         接收的字符个数。
	*************************************************/   
  int recv_data(int fd, unsigned char* recv_buffer,int length);

  /*************************************************
	Function:       send_data
	Description:    发送串口数据。
	Input:          fd: 句柄，send_buffer: 发送的字符数组，length: 发送的字符个数。
	Output:         无
	Return:         发送的字符个数
	*************************************************/  
  int send_data(int  fd, unsigned char *send_buffer,int length);

  /*************************************************
	Function:       uart_close
	Description:    关闭串口。
	Input:          fd: 句柄。
	Output:         无
	Return:         是否成功
	*************************************************/ 
  int uart_close(int fd);

  /*************************************************
	Function:       uart_set
	Description:    设置串口。
	Input:          fd: 句柄，nspeed: 波特率，nBits: 位数，nEvent: 工作模式，nStop: 停止位
	Output:         无
	Return:         是否成功
	*************************************************/   
  int uart_set(int fd,int nSpeed, int nBits, char nEvent, int nStop);

  /*************************************************
	Function:       uart_open
	Description:    打开串口。
	Input:          fd: 句柄，pathname: 串口名称
	Output:         无
	Return:         是否成功
	*************************************************/ 
  int uart_open(int fd,const char *pathname);

  /*************************************************
	Function:       saveTrajHandler
	Description:    调用服务，保存地图。
	Input:          req: 请求信息，res：回应信息。
	Output:         无
	Return:         是否成功
	*************************************************/    
  bool saveTrajHandler(std_srvs::Empty::Request &req, std_srvs::Empty::Response &res);
  
  /*************************************************
	Function:       ParserData
	Description:    解析字符数组。
	Input:          data[]: 字符数组，num：字符数组个数。
	Output:         无
	Return:         无
	*************************************************/    
  void ParserData(unsigned char data[], int num);

  /*************************************************
	Function:       subCallback_actuator
	Description:    订阅actuator，订阅车辆信息。
	Input:          msg: 车辆信息
	Output:         无
	Return:         无
	*************************************************/     
  void subCallback_actuator(const common_msgs::actuator::ConstPtr &msg);

  /*************************************************
	Function:       SubCallback_collectpoint
	Description:    订阅collectpoint，回调APP数据进行泊车点、红绿灯点的采集
	Input:          msg: 泊车/红绿灯点
	Output:         无
	Return:         无
	*************************************************/   
  void SubCallback_collectpoint(const common_msgs::collectpoint::ConstPtr &msg);

  /*************************************************
	Function:       SubCallback_collectmap
	Description:    订阅collectmap，新建文件并记录地图
	Input:          msg: 
	Output:         无
	Return:         无
	*************************************************/     
  void SubCallback_collectmap(const common_msgs::collectmap::ConstPtr &msg);

  /*************************************************
	Function:       SubCallback_requestmap
	Description:    订阅request map，接收APP数据请求，进行地图数据上传
	Input:          msg: 请求的地图
	Output:         无
	Return:         无
	*************************************************/    
  void SubCallback_requestmap(const common_msgs::requestmap::ConstPtr &msg);

  /*************************************************
    Function:       SubCallback_inspva
	Description:    订阅INSPVA，
	Input:          msg:
	Output:         无
	Return:         无
	*************************************************/
  void SubCallback_inspva(const novatel_oem7_msgs::INSPVA::ConstPtr &msg); //20221110 订阅INSPVA

  /*************************************************
    Function:       SubCallback_inspvax
    Description:    订阅INSPVAX，
    Input:          msg:
    Output:         无
    Return:         无
    *************************************************/
  void SubCallback_inspvax(const novatel_oem7_msgs::INSPVAX::ConstPtr &msg); //20221114 订阅INSPVAX

  /*************************************************
    Function:       SubCallback_corrimu
    Description:    订阅CORRIMU，
    Input:          msg:
    Output:         无
    Return:         无
    *************************************************/
  void SubCallback_corrimu(const novatel_oem7_msgs::CORRIMU::ConstPtr &msg); //20221128 订阅CORRIMU

  void SubCallback_imu(const sensor_msgs::Imu::ConstPtr &msg); //20221207 订阅gps/imu
  /*************************************************
    Function:       Get_imuMsg
    Description:    时间同步，将最近的一组数据作为队列第一帧
    Input:          MsgDeque: 数据队列   GpsTime: GPS当前时间
    Output:         无
    Return:         无
    *************************************************/
  void Get_corrimuMsg(std::deque<novatel_oem7_msgs::CORRIMU> &MsgDeque, const long &GpsTime); //20221128

  void Get_imuMsg(std::deque<sensor_msgs::Imu> &MsgDeque, const long &GpsTime); //20221128
  /*************************************************
	Function:       CopyFile
	Description:    文件重命名
	Input:          sourcefile:源文件名 destfile：目的文件名
	Output:         无
	Return:         无
	*************************************************/ 
  void CopyFile(string sourcefile,string destfile);
  
  /*************************************************
	Function:       publishMap
	Description:    发布所有路径下的点集
	Input:          无
	Output:         无
	Return:         无
	*************************************************/   
  void publishMap();

  /*************************************************
	Function:       publishFileDir
	Description:    发布文件路径下的maping点集
	Input:          filedir:文件路径
	Output:         无
	Return:         无
	*************************************************/  
  void publishFileDir(string filedir);

  /*************************************************
	Function:       split
	Description:    将字符串以某个字符进行分割
	Input:          str:字符串 pattern：子字符串
	Output:         无
	Return:         字符串数组
	*************************************************/ 
  vector<string> split(const string &str,const string &pattern);

  /*************************************************
	Function:       publishMapnames
	Description:    发布文件夹下所有文件名称
	Input:          无
	Output:         无
	Return:         无
	*************************************************/ 
  void publishMapnames();

  /*************************************************
	Function:       safedeletefile
	Description:    安全删除文件
	Input:          name: 文件名称
	Output:         无
	Return:         无
	*************************************************/
  void safedeletefile(const std::string& name);
 
  /*************************************************
	Function:       GenerateNewDir
	Description:    生成新的文件
	Input:          path: 路径名称
	Output:         无
	Return:         无
	*************************************************/
  void GenerateNewDir(string path);

	/*************************************************
	Function:       exists_test3
	Description:    判断文件是否存在
	Input:          name: 文件名称
	Output:         无
	Return:         bool: 是否存在的标志位
	*************************************************/	  
  inline bool exists_test3 (const std::string& name) 
  {
    struct stat buffer;   
    return (stat (name.c_str(), &buffer) == 0); 
  }

  private:

  //订阅和发布定义
  ros::Publisher pub_gps;
  ros::Publisher pub_imu;
  ros::Publisher  pub_gpsstatus;
  ros::Subscriber sub_;
  ros::Subscriber sub_actuator;

  ros::Subscriber sub_collectmap;//接收app采集地图信息
  ros::Subscriber sub_requestmap;//接收app请求信息
  ros::Subscriber sub_collectpoint;//接收app采集定点信息
  ros::Publisher pub_lonlatmap;//发布地图信息

  ros::Subscriber sub_inspvaMsg;//20221110 接收INSPVA数据
  ros::Subscriber sub_inspvaxMsg;//20221114 接收INSPVAX数据
  ros::Subscriber sub_corrimuMsg;//20221128 接收CORRIMU数据
  ros::Subscriber sub_imuMsg;//20221207 接受gps/imu数据

  common_msgs::actuator m_actuator;//车辆信息
  common_msgs::collectmap m_collectmap;//采集地图信息
  common_msgs::requestmap m_requestmap;//请求地图信息
  common_msgs::collectpoint collect_point_;//点信息

  novatel_oem7_msgs::INSPVA m_inspva;//20221110 INSPVA信息
  std::deque<novatel_oem7_msgs::INSPVA> inspvaMsgDeque_;//20221110 INSPVA队列
  novatel_oem7_msgs::INSPVAX m_inspvax;//20221114 INSPVAX信息
  novatel_oem7_msgs::INSPVAX last_inspvax;//20221114 上一时刻INSPVAX信息
  novatel_oem7_msgs::CORRIMU m_corrimu; //20221128 CORRIMU信息
  std::deque<novatel_oem7_msgs::CORRIMU> corrimuMsgDeque;//20221128 CORRIMU队列
  sensor_msgs::Imu m_imu; //20221207 IMU信息
  std::deque<sensor_msgs::Imu> imuMsgDeque_;//20221207 gps/imu队列

  //gps object
  sGpsPoint rp; //解析后的gps信息
  sGpsPoint lastRp;//上一次的gps信息
  sAddAttr addattr;//动态参数
  //for gps data parse

  int baudrate;//波特率
  int loopFrep;//程序运行频率
  int runningmode;//工作模式
  int isSaveFile,collect;//是否保存和采集地图
  int bdebug;//调试模式
  std::string serialport;//串口号
  std::string savePath,saveDir,saveTrajDir;//保存的路径
  std::string updatelog;
  double m_mile;//总里程
  FILE* fp;//文件
  ros::ServiceServer srvSaveTraj; //服务类

  int m_serialcan;//串口 or can解析
  std::string m_canetip;//设备IP
  int m_canetport;//设备端口
  std::string m_pcip;//pc ip
  int m_pcport;//pc 端口
  Boost_UDP *myCanetUdp;//udp类

  int lastcollectmode; //上一次采集模式
  string filedirectoryname;//文件名称

  vector<string> m_ExistFileDir; //文件目录是否存在

  common_msgs::sensorstatus          gps_status;

  int last_property_;

  bool use_inspva;//20221110
  bool use_inspvax;
  bool use_imu;
  bool isUseRosBag;//20230119
  bool isSaveSelfCarInfo; //是否保存自车经纬度速度航向等信息，用于评估自车定位准确性
  int carNumber;
  bool isOnline;
  float m_accuracyLossTime;
	int m_accuracyLossCount = 0;

  Common m_common;
  double m_position1_lon;
  double m_position1_lat;
  double m_position2_lon;
  double m_position2_lat;
  double m_position3_lon;
  double m_position3_lat;
  double m_position4_lon;
  double m_position4_lat;
	
  std::vector<Point> m_slamPolygon;
	
	fstream saveSelfCarInofFile;
};




#endif // NODE_EXAMPLE_TALKER_H
