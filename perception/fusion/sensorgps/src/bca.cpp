
#include "bca.h"
using namespace std;

const static double gpst0[]={1980,1, 6,0,0,0};  //起始时间


bca::bca()
{
}
bca::~bca()
{
}

  /*************************************************
	Function:       int2String
	Description:    Int转字符串
	Input:          obj:整型
	Output:         无
	Return:         字符串
	*************************************************/
string bca::int2String ( const int n )
{
	std::stringstream newstr;
	newstr<<n;
	return newstr.str();
}

  /*************************************************
	Function:       int2String_boost
	Description:    Int转字符串
	Input:          obj:整型
	Output:         无
	Return:         字符串
	*************************************************/
string bca::int2String_boost(int obj)
{
	string ouputValue;
	try   
	{
		ouputValue = boost::lexical_cast<string>( obj );
	}
	catch( boost::bad_lexical_cast & e )
	{
		std::cout << "bca::string2Int_boost: Exception caught : " << e.what() << std::endl;
	}
	return ouputValue;
}

  /*************************************************
	Function:       string2Int_boost
	Description:    字符串转Int
	Input:          obj:字符串
	Output:         无
	Return:         Int 
	*************************************************/	
int bca::string2Int_boost(string obj)
{
	int ouputValue;
	try   
	{
		ouputValue = boost::lexical_cast<int>( obj );
	}
	catch( boost::bad_lexical_cast & e )
	{
		std::cout << "bca::string2Int_boost: Exception caught : " << e.what() << std::endl;
	}
	return ouputValue;
}

  /*************************************************
	Function:       string2Double_boost
	Description:    字符串转Double
	Input:          obj:字符串
	Output:         无
	Return:         Double 
	*************************************************/	
double bca::string2Double_boost(string obj)
{
	double ouputValue;
	try   
	{
		ouputValue = boost::lexical_cast<double>( obj );
	}
	catch( boost::bad_lexical_cast & e )
	{
		std::cout << "bca::string2Double_boost: Exception caught : " << e.what() << std::endl;
	}
	return ouputValue;
}

  /*************************************************
	Function:       string2Float_boost
	Description:    字符串转float
	Input:          obj:字符串
	Output:         无
	Return:         float 
	*************************************************/	
float bca::string2Float_boost(string obj)
{
	float ouputValue;
	try   
	{
		ouputValue = boost::lexical_cast<float>( obj );
	}
	catch( boost::bad_lexical_cast & e )
	{
		std::cout << "bca::string2Double_boost: Exception caught : " << e.what() << std::endl;
	}
	return ouputValue;
}

  /*************************************************
	Function:       charArray2Int
	Description:    字符数组转成Int
	Input:          buffer：字符数组， start：起始位，length：长度
	Output:         无
	Return:         Int 
	*************************************************/	
int bca::charArray2Int(unsigned char buffer[], int start, int length)
{
	BOOST_ASSERT(length > 0);

	unsigned char bufferTarget[4]={0};
	if (2 == length)
	{
		bufferTarget[0] = buffer[start];
		bufferTarget[1] = buffer[start+1];
		bufferTarget[2]=0;
		bufferTarget[3]=0;
	}
	if (3 == length)
	{
		bufferTarget[0] = buffer[start];
		bufferTarget[1] = buffer[start+1];
		bufferTarget[2] = buffer[start+2];
		bufferTarget[3] = 0x00;
		if (1 == bufferTarget[2]>>7)
		{
			bufferTarget[3] = 0xff;
		}
	}
	else if(4 == length)
	{
		bufferTarget[0] = buffer[start];
		bufferTarget[1] = buffer[start+1];
		bufferTarget[2] = buffer[start+2];
		bufferTarget[3] = buffer[start+3];
	}
	int outputValue;
	memcpy(&outputValue, &bufferTarget,4);
	return outputValue;
}

  /*************************************************
	Function:       charArray2uInt
	Description:    字符数组转成uInt
	Input:          buffer：字符数组， start：起始位，length：长度
	Output:         无
	Return:         uInt 
	*************************************************/	
unsigned int bca::charArray2uInt(unsigned char buffer[], int start, int length)
{
	BOOST_ASSERT(length > 0);

	unsigned char bufferTarget[4]={0};
	if (2 == length)
	{
		bufferTarget[0] = buffer[start];
		bufferTarget[1] = buffer[start+1];
		bufferTarget[2]=0;
		bufferTarget[3]=0;
	}
	if (3 == length)
	{
		bufferTarget[0] = buffer[start];
		bufferTarget[1] = buffer[start+1];
		bufferTarget[2] = buffer[start+2];
		bufferTarget[3] = 0x00;
		if (1 == bufferTarget[2]>>7)
		{
			bufferTarget[3] = 0xff;
		}
	}
	else if(4 == length)
	{
		bufferTarget[0] = buffer[start];
		bufferTarget[1] = buffer[start+1];
		bufferTarget[2] = buffer[start+2];
		bufferTarget[3] = buffer[start+3];
	}
	unsigned int outputValue;
	memcpy(&outputValue, &bufferTarget,4);
	return outputValue;
}


  /*************************************************
	Function:       charArray2Double
	Description:    字符数组转成Double
	Input:          buffer：字符数组， start：起始位
	Output:         无
	Return:         Double 
	*************************************************/	
double bca::charArray2Double(unsigned char buffer[], int start)
{
	const int len = 8;
	unsigned char bufferTarget[len];	
	for (int i = 0; i < len; i++)
	{
		bufferTarget[i]=buffer[start+i];
	}
	double outputValue;
	memcpy(&outputValue, &bufferTarget,len);
	return outputValue;
}

  /*************************************************
	Function:       charArray2Float
	Description:    字符数组转成float
	Input:          buffer：字符数组， start：起始位
	Output:         无
	Return:         float 
	*************************************************/	
float bca::charArray2Float(unsigned char buffer[], int start)
{
	const int len = 4;
	unsigned char bufferTarget[len];	
	for (int i=0;i<len;i++)
	{
		bufferTarget[i]=buffer[start+i];
	}
	float outputValue;
	memcpy(&outputValue, &bufferTarget,len);
	return outputValue;
}



  /*************************************************
	Function:       epoch2time
	Description:    将初始时间转成标准时
	Input:          ep: 起始时间
	Output:         无
	Return:         无 
	*************************************************/	
gtime_t bca::epoch2time(const double *ep) 
{ 
    const int doy[]={1,32,60,91,121,152,182,213,244,274,305,335}; 
    gtime_t time={0}; 
    int days,sec,year=(int)ep[0],mon=(int)ep[1],day=(int)ep[2]; 
     
    if (year<1970||2099<year||mon<1||12<mon) return time; 
     
    /* leap year if year%4==0 in 1901-2099 */ 
    days=(year-1970)*365+(year-1969)/4+doy[mon-1]+day-2+(year%4==0&&mon>=3?1:0); 
    sec=(int)floor(ep[5]); 
    time.time=(time_t)days*86400+(int)ep[3]*3600+(int)ep[4]*60+sec; 
    time.sec=ep[5]-sec; 
    return time; 
} 
 
  /*************************************************
	Function:       gpst2time
	Description:    gps时间转成标准时。
	Input:          week:gps周 ， sec: gps秒
	Output:         无
	Return:         无 
	*************************************************/	
gtime_t bca::gpst2time(int week, double sec) 
{ 
    gtime_t t = epoch2time(gpst0); 
    if (sec<-1E9||1E9<sec) sec=0.0; 
	t.time += 86400*7*week + (int)sec; 
	t.sec= sec - (int)sec; 
	return t; 
} 

  /*************************************************
	Function:       timeadd
	Description:    加入间隔秒
	Input:          t: gps时间，sec： 添加的时间
	Output:         无
	Return:         新构造的时间 
	*************************************************/ 
gtime_t bca::timeadd(gtime_t t, double sec) 
{ 
    double tt; 
    t.sec+=sec; 
	tt=floor(t.sec); 
	t.time+=(int)tt; 
	t.sec-=tt; 
    return t; 
} 

  /*************************************************
	Function:       GPSTime2UTCTime
	Description:    gps时间转成utc时间
	Input:          week:gps周 ， sec: 秒， leapsec: 间隔s，一般为固定值。
	Output:         无
	Return:         无 
	*************************************************/	
unsigned long  bca::GPSTime2UTCTime(int week,double sec,double leapsec)
{
  unsigned long timestamp = 0;
  gtime_t gpst = gpst2time(week,sec); 
  
  gtime_t tpst = timeadd(gpst,-leapsec); 

  timestamp = tpst.time*1000 + tpst.sec*1000;
 
  return timestamp;
}