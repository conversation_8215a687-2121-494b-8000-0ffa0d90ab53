日期：2024.10.17
修改人：韩双全
版本号：3.8.5
修改项：
1.地图保存项目gpstime更改为定位状态

日期：2024.08.14
修改人：韩双全
版本号：3.8.4
修改项：
1.细化第二辆车的定位状态

日期：2024.06.04
修改人：韩双全
版本号：3.8.3
修改项：
1.修正两辆车的串口号

日期：2024.06.03
修改人：韩双全
版本号：3.8.2
修改项：
1.GPS串口号由参数读取改为根据carNum读取

日期：2024.05.16
修改人：韩双全
版本号：3.8.1
修改项：
1.更改GPS串口号


日期：2024.03.07
修改人：韩双全
版本号：3.8
修改项：
1.更改GPS失效状态判断：由(进入划定范围&&GPS的rawstatus!=50)改为(进入划定范围)

日期：2024.01.22
修改人：韩双全
版本号：3.7
修改项：
1.第二辆车添加gps状态设置
2.添加地图文件夹自动创建

日期：2023.12.26
修改人：韩双全
版本号：3.6
修改项：
1.添加GPS失效区域及gps状态设置

日期：2023.11.13
修改人：韩双全
版本号：3.5
修改项：
1.gps版本回退3.4

日期：2023.11.13
修改人：韩双全
版本号：3.4
修改项：
1.设置status表示是否可用于自动驾驶，值1为GPS可用，值0为不可用


日期：2023.11.13
修改人：韩双全
版本号：3.3
修改项：
1.novatel发布的sensorgps话题accx，accy输出以前右下坐标输出

日期：2023.09.25
修改人：韩双全
版本号：3.2
修改项：两辆车发布结果统一
1.novatel发布的sensorgps话题rawstatus在定位状态好时设置为50

日期：2023.09.13
修改人：韩双全
版本号：3.1
修改项：两辆车发布结果统一
	1.加速度统一到前右下
	2.角速度统一到顺时针为正，车辆右转时输出角速度为正

日期：2023.
修改人：韩双全
版本号：3.0
修改项：
    1.版本统一，以NPOS版本为基础，添加导远适配（导远通过串口解析，北斗星通通过其封装的ROS驱动解析）；
    2.导远模式地图保存添加4列到TXT文件
    3.使用统一的配置文件（跟踪节点中）
    

日期：2023.04.13
修改人：韩双全
版本号：2.0
修改项：
    1.相比原版代码，适配第二辆车的npos；
    2.添加保存自车信息（经纬度、速度、航向角等）
